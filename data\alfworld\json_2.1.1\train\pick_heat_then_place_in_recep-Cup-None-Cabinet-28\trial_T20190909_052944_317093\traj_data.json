{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000103.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000104.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000186.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000187.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000188.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000189.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000190.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000191.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000192.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000193.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000194.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000245.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000246.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000247.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000248.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000249.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000250.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000251.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000252.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000253.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000254.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000255.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000256.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000257.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000258.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000259.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000260.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000261.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000262.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000263.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000264.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000320.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000321.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000322.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000323.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000324.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000325.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000326.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000327.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000328.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000329.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000330.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000331.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000332.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000333.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000334.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000335.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000336.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000337.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000338.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000339.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000340.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000341.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000342.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000343.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000344.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000345.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000346.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000347.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000348.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000349.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000350.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000351.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000352.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000353.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000354.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000355.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000356.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000357.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000358.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000359.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000360.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000361.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000362.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000363.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000364.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000365.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000366.png", "low_idx": 44}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-9|1|-15"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-0.507182956, -0.507182956, -8.71845816, -8.71845816, 7.75861692, 7.75861692]], "coordinateReceptacleObjectId": ["Cabinet", [-1.349858, -1.349858, -9.99042128, -9.99042128, 8.44060136, 8.44060136]], "forceVisible": true, "objectId": "Cup|-00.13|+01.94|-02.18"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-8|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-9|2|-15"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-0.507182956, -0.507182956, -8.71845816, -8.71845816, 7.75861692, 7.75861692]], "coordinateReceptacleObjectId": ["Cabinet", [-1.3861756, -1.3861756, -13.1733904, -13.1733904, 7.56638432, 7.56638432]], "forceVisible": true, "objectId": "Cup|-00.13|+01.94|-02.18", "receptacleObjectId": "Cabinet|-00.35|+01.89|-03.29"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-00.34|+02.11|-02.50"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [87, 1, 237, 91], "mask": [[93, 137], [393, 137], [693, 137], [993, 137], [1293, 137], [1593, 137], [1893, 137], [2193, 137], [2493, 137], [2793, 138], [3093, 138], [3393, 138], [3693, 138], [3992, 139], [4292, 139], [4592, 139], [4892, 139], [5192, 139], [5492, 139], [5792, 140], [6092, 140], [6392, 140], [6692, 140], [6992, 140], [7292, 140], [7592, 140], [7892, 140], [8192, 140], [8491, 141], [8791, 142], [9091, 142], [9391, 142], [9691, 142], [9991, 142], [10291, 142], [10591, 142], [10891, 142], [11191, 142], [11491, 142], [11791, 142], [12091, 143], [12391, 143], [12690, 144], [12990, 144], [13290, 144], [13590, 144], [13890, 144], [14190, 144], [14490, 144], [14790, 144], [15090, 145], [15390, 145], [15690, 145], [15990, 145], [16290, 145], [16590, 145], [16890, 145], [17189, 146], [17489, 146], [17789, 146], [18089, 147], [18389, 147], [18689, 147], [18989, 147], [19289, 147], [19589, 147], [19889, 147], [20189, 147], [20489, 147], [20789, 147], [21089, 148], [21388, 149], [21688, 149], [21988, 149], [22288, 149], [22588, 149], [22888, 149], [23188, 149], [23488, 149], [23788, 149], [24088, 149], [24388, 150], [24688, 150], [24988, 150], [25288, 150], [25588, 150], [25887, 151], [26187, 151], [26487, 151], [26788, 150], [27088, 149]], "point": [162, 45]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-00.13|+01.94|-02.18"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [114, 77, 146, 91], "mask": [[22925, 10], [23223, 15], [23521, 19], [23820, 21], [24119, 23], [24419, 24], [24718, 26], [25017, 27], [25317, 28], [25616, 29], [25916, 30], [26215, 31], [26515, 31], [26814, 33], [27114, 33]], "point": [130, 83]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-00.34|+02.11|-02.50"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [90, 1, 299, 91], "mask": [[96, 130], [228, 72], [396, 130], [528, 72], [696, 130], [828, 72], [996, 131], [1128, 72], [1296, 131], [1428, 71], [1596, 131], [1728, 71], [1896, 131], [2028, 70], [2196, 131], [2328, 69], [2496, 131], [2628, 69], [2796, 131], [2928, 68], [3095, 132], [3229, 66], [3395, 132], [3529, 66], [3695, 132], [3829, 65], [3995, 133], [4129, 64], [4295, 133], [4429, 63], [4595, 133], [4729, 63], [4895, 133], [5029, 62], [5195, 133], [5329, 61], [5495, 133], [5629, 61], [5795, 133], [5929, 60], [6095, 133], [6229, 59], [6395, 133], [6530, 58], [6695, 133], [6830, 57], [6995, 133], [7130, 56], [7295, 134], [7430, 55], [7594, 135], [7730, 55], [8030, 54], [8330, 53], [8630, 53], [8930, 52], [9230, 51], [9531, 50], [9694, 135], [9831, 49], [9994, 135], [10131, 48], [10294, 135], [10431, 48], [10594, 136], [10731, 47], [10894, 136], [11031, 46], [11194, 136], [11331, 45], [11494, 136], [11631, 45], [11794, 136], [11931, 44], [12093, 137], [12231, 43], [12393, 137], [12531, 43], [12693, 137], [12832, 41], [12993, 137], [13132, 40], [13293, 137], [13432, 40], [13593, 137], [13732, 39], [13893, 138], [14032, 38], [14193, 138], [14332, 38], [14493, 138], [14632, 37], [14793, 138], [14932, 36], [15093, 138], [15232, 35], [15393, 138], [15532, 35], [15693, 138], [15833, 33], [15993, 138], [16133, 32], [16293, 138], [16433, 32], [16593, 138], [16733, 31], [16892, 140], [17033, 30], [17192, 140], [17333, 30], [17492, 140], [17633, 29], [17792, 140], [17933, 28], [18092, 140], [18233, 27], [18392, 140], [18533, 27], [18692, 140], [18833, 26], [18992, 140], [19134, 24], [19292, 140], [19434, 24], [19592, 140], [19734, 23], [19892, 140], [20034, 22], [20192, 141], [20334, 22], [20492, 141], [20634, 21], [20792, 141], [20934, 20], [21092, 141], [21234, 20], [21391, 142], [21534, 19], [21691, 142], [21834, 18], [21991, 142], [22134, 17], [22291, 142], [22435, 16], [22591, 142], [22735, 15], [22891, 142], [23035, 14], [23191, 142], [23335, 14], [23491, 143], [23635, 13], [23791, 143], [23935, 12], [24091, 143], [24235, 12], [24391, 143], [24535, 11], [24691, 143], [24835, 10], [24991, 143], [25135, 10], [25291, 143], [25436, 8], [25591, 143], [25736, 7], [25890, 144], [26036, 6], [26190, 144], [26336, 6], [26490, 145], [26636, 5], [26790, 145], [26937, 3], [27090, 145]], "point": [194, 45]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-00.13|+01.94|-02.18", "placeStationary": true, "receptacleObjectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 10609], [10641, 260], [10950, 250], [11252, 247], [11553, 246], [11854, 244], [12154, 244], [12455, 243], [12755, 244], [13055, 244], [13355, 244], [13655, 244], [13955, 245], [14255, 245], [14554, 246], [14854, 246], [15154, 247], [15454, 247], [15754, 247], [16053, 249], [16353, 249], [16653, 249], [16953, 249], [17252, 250], [17552, 251], [17852, 251], [18152, 251], [18452, 251], [18752, 252], [19051, 253], [19351, 253], [19651, 253], [19951, 254], [20251, 254], [20550, 255], [20850, 255], [21150, 256], [21450, 256], [21750, 256], [22049, 257], [22349, 258], [22649, 258], [22949, 258], [23249, 258], [23548, 260], [23848, 260], [24148, 262], [24446, 8297], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [151, 63]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 7978], [7996, 277], [8300, 271], [8602, 268], [8902, 267], [9203, 265], [9503, 265], [9804, 263], [10104, 263], [10405, 204], [10641, 25], [10705, 196], [10950, 16], [11006, 194], [11252, 13], [11306, 193], [11553, 12], [11606, 193], [11854, 11], [11907, 191], [12154, 10], [12207, 191], [12455, 9], [12507, 191], [12755, 8], [12807, 192], [13055, 8], [13108, 191], [13355, 8], [13408, 191], [13655, 7], [13708, 191], [13955, 7], [14008, 192], [14255, 7], [14308, 192], [14554, 8], [14608, 192], [14854, 8], [14908, 192], [15154, 8], [15208, 193], [15454, 8], [15509, 192], [15754, 7], [15809, 192], [16053, 8], [16109, 193], [16353, 8], [16409, 193], [16653, 8], [16709, 193], [16953, 8], [17009, 193], [17252, 9], [17309, 193], [17552, 9], [17608, 195], [17852, 9], [17908, 195], [18152, 9], [18208, 195], [18452, 9], [18508, 195], [18752, 9], [18808, 196], [19051, 10], [19108, 196], [19351, 10], [19408, 196], [19651, 10], [19708, 196], [19951, 10], [20008, 197], [20251, 10], [20307, 198], [20550, 12], [20607, 198], [20850, 12], [20907, 198], [21150, 12], [21206, 200], [21450, 13], [21506, 200], [21750, 13], [21805, 201], [22049, 14], [22105, 201], [22349, 15], [22404, 203], [22649, 16], [22703, 204], [22949, 17], [23002, 205], [23249, 18], [23301, 206], [23548, 20], [23600, 208], [23848, 23], [23897, 211], [24148, 262], [24446, 8297], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [151, 63]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-00.13|+01.94|-02.18"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [161, 27, 208, 80], "mask": [[7978, 18], [8273, 27], [8571, 31], [8870, 32], [9169, 34], [9468, 35], [9768, 36], [10067, 37], [10367, 38], [10666, 39], [10966, 40], [11265, 41], [11565, 41], [11865, 42], [12164, 43], [12464, 43], [12763, 44], [13063, 45], [13363, 45], [13662, 46], [13962, 46], [14262, 46], [14562, 46], [14862, 46], [15162, 46], [15462, 47], [15761, 48], [16061, 48], [16361, 48], [16661, 48], [16961, 48], [17261, 48], [17561, 47], [17861, 47], [18161, 47], [18461, 47], [18761, 47], [19061, 47], [19361, 47], [19661, 47], [19961, 47], [20261, 46], [20562, 45], [20862, 45], [21162, 44], [21463, 43], [21763, 42], [22063, 42], [22364, 40], [22665, 38], [22966, 36], [23267, 34], [23568, 32], [23871, 26]], "point": [184, 52]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 10609], [10641, 260], [10950, 250], [11252, 247], [11553, 246], [11854, 244], [12154, 244], [12455, 243], [12755, 244], [13055, 244], [13355, 244], [13655, 244], [13955, 245], [14255, 245], [14554, 246], [14854, 246], [15154, 247], [15454, 247], [15754, 247], [16053, 249], [16353, 249], [16653, 249], [16953, 249], [17252, 250], [17552, 251], [17852, 251], [18152, 251], [18452, 251], [18752, 252], [19051, 253], [19351, 253], [19651, 253], [19951, 254], [20251, 254], [20550, 255], [20850, 255], [21150, 256], [21450, 256], [21750, 256], [22049, 257], [22349, 258], [22649, 258], [22949, 258], [23249, 258], [23548, 260], [23848, 260], [24148, 262], [24446, 8297], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [151, 63]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-00.35|+01.89|-03.29"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 59, 99, 242], "mask": [[17408, 3], [17707, 6], [18007, 9], [18307, 11], [18607, 14], [18907, 16], [19207, 19], [19506, 22], [19806, 25], [20106, 27], [20406, 30], [20706, 32], [21006, 35], [21305, 39], [21605, 41], [21905, 44], [22205, 46], [22505, 49], [22805, 51], [23104, 55], [23404, 57], [23704, 60], [24004, 62], [24304, 65], [24604, 67], [24903, 71], [25203, 73], [25503, 76], [25803, 79], [26103, 81], [26403, 84], [26702, 87], [27002, 90], [27302, 92], [27602, 95], [27902, 97], [28202, 98], [28501, 99], [28801, 99], [29101, 99], [29401, 99], [29701, 99], [30001, 99], [30300, 100], [30600, 100], [30900, 100], [31200, 100], [31500, 100], [31800, 100], [32100, 100], [32400, 100], [32700, 100], [33000, 100], [33300, 100], [33600, 99], [33900, 99], [34200, 99], [34500, 99], [34800, 99], [35100, 99], [35400, 99], [35700, 99], [36000, 99], [36300, 99], [36600, 99], [36900, 99], [37200, 99], [37500, 99], [37800, 99], [38100, 99], [38400, 99], [38700, 99], [39000, 99], [39300, 98], [39600, 98], [39900, 98], [40200, 98], [40500, 98], [40800, 98], [41100, 98], [41400, 98], [41700, 98], [42000, 98], [42300, 98], [42600, 98], [42900, 98], [43200, 98], [43500, 98], [43800, 98], [44100, 98], [44400, 98], [44700, 97], [45000, 97], [45300, 97], [45600, 97], [45900, 97], [46200, 97], [46500, 97], [46800, 97], [47100, 97], [47400, 97], [47700, 97], [48000, 97], [48300, 97], [48600, 97], [48900, 97], [49200, 97], [49500, 97], [49800, 97], [50100, 96], [50400, 96], [50700, 96], [51000, 96], [51300, 96], [51600, 96], [51900, 96], [52200, 96], [52500, 96], [52800, 96], [53100, 96], [53400, 96], [53700, 96], [54000, 96], [54300, 96], [54600, 96], [54900, 96], [55200, 96], [55500, 96], [55800, 95], [56100, 95], [56400, 95], [56700, 95], [57000, 95], [57300, 95], [57600, 95], [57900, 95], [58200, 95], [58500, 95], [58800, 95], [59100, 95], [59400, 95], [59700, 95], [60000, 95], [60300, 95], [60600, 95], [60900, 95], [61200, 94], [61500, 94], [61800, 94], [62100, 94], [62400, 94], [62700, 94], [63000, 94], [63300, 94], [63600, 94], [63900, 94], [64200, 94], [64500, 94], [64800, 94], [65100, 94], [65400, 94], [65700, 94], [66000, 94], [66300, 94], [66600, 94], [66900, 93], [67200, 93], [67500, 93], [67800, 93], [68100, 93], [68400, 93], [68700, 93], [69000, 93], [69300, 93], [69600, 93], [69900, 93], [70200, 93], [70500, 93], [70800, 93], [71100, 88], [71400, 69], [71700, 50], [72000, 32], [72300, 13]], "point": [49, 149]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-00.13|+01.94|-02.18", "placeStationary": true, "receptacleObjectId": "Cabinet|-00.35|+01.89|-03.29"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 98, 256], "mask": [[50, 10], [350, 10], [649, 11], [948, 12], [1247, 13], [1547, 13], [1846, 14], [2145, 14], [2444, 15], [2743, 16], [3043, 16], [3342, 17], [3641, 18], [3940, 19], [4240, 19], [4539, 20], [4838, 20], [5137, 21], [5436, 22], [5736, 22], [6035, 23], [6334, 24], [6633, 25], [6933, 25], [7232, 26], [7531, 26], [7830, 27], [8130, 27], [8429, 28], [8728, 29], [9027, 30], [9326, 31], [9626, 31], [9925, 32], [10224, 32], [10523, 33], [10823, 33], [11122, 34], [11421, 35], [11720, 36], [12019, 37], [12319, 37], [12618, 38], [12917, 38], [13216, 39], [13516, 39], [13815, 40], [14114, 41], [14413, 42], [14713, 42], [15012, 43], [15311, 44], [15610, 44], [15909, 45], [16209, 45], [16508, 46], [16807, 47], [17106, 48], [17406, 48], [17706, 48], [18006, 48], [18306, 47], [18605, 48], [18905, 48], [19205, 48], [19505, 48], [19805, 48], [20105, 48], [20405, 48], [20704, 49], [21004, 49], [21304, 48], [21604, 48], [21904, 48], [22204, 48], [22503, 49], [22803, 49], [23103, 51], [23403, 54], [23703, 56], [24003, 59], [24302, 63], [24602, 65], [24902, 68], [25202, 70], [25502, 73], [25802, 75], [26101, 79], [26401, 82], [26701, 84], [27001, 87], [27301, 89], [27601, 92], [27900, 95], [28200, 98], [28500, 99], [28800, 99], [29100, 99], [29400, 99], [29700, 99], [30000, 99], [30300, 99], [30600, 49], [30652, 47], [30900, 49], [30955, 43], [31200, 49], [31258, 40], [31500, 49], [31562, 36], [31800, 52], [31865, 33], [32100, 56], [32168, 30], [32400, 59], [32471, 27], [32700, 62], [32774, 24], [33000, 65], [33077, 21], [33300, 69], [33380, 18], [33600, 72], [33683, 15], [33900, 75], [33987, 11], [34200, 78], [34290, 8], [34500, 82], [34593, 5], [34800, 85], [34896, 2], [35100, 88], [35400, 91], [35700, 94], [36000, 98], [36300, 97], [36600, 97], [36900, 97], [37200, 97], [37500, 97], [37800, 97], [38100, 97], [38400, 97], [38700, 97], [39000, 97], [39300, 97], [39600, 97], [39900, 97], [40200, 97], [40500, 97], [40800, 97], [41100, 97], [41400, 97], [41700, 96], [42000, 96], [42300, 96], [42600, 96], [42900, 96], [43200, 96], [43500, 96], [43800, 96], [44100, 96], [44400, 96], [44700, 96], [45000, 96], [45300, 96], [45600, 96], [45900, 96], [46200, 96], [46500, 96], [46800, 96], [47100, 95], [47400, 95], [47700, 95], [48000, 95], [48300, 95], [48600, 95], [48900, 95], [49200, 95], [49500, 95], [49800, 95], [50100, 95], [50400, 95], [50700, 95], [51000, 95], [51300, 95], [51600, 95], [51900, 95], [52200, 95], [52500, 94], [52800, 94], [53100, 94], [53400, 94], [53700, 94], [54000, 94], [54300, 94], [54600, 94], [54900, 94], [55200, 94], [55500, 94], [55800, 94], [56100, 94], [56400, 94], [56700, 94], [57000, 94], [57300, 94], [57600, 94], [57900, 93], [58200, 93], [58500, 93], [58800, 93], [59100, 93], [59400, 93], [59700, 93], [60000, 93], [60300, 93], [60600, 93], [60900, 93], [61200, 93], [61500, 93], [61800, 93], [62100, 93], [62400, 93], [62700, 93], [63000, 93], [63300, 92], [63600, 92], [63900, 92], [64200, 92], [64500, 92], [64800, 92], [65100, 92], [65400, 92], [65700, 92], [66000, 92], [66300, 92], [66600, 92], [66900, 92], [67200, 92], [67500, 92], [67800, 92], [68100, 92], [68400, 92], [68700, 91], [69000, 91], [69300, 91], [69600, 91], [69900, 91], [70200, 91], [70500, 91], [70800, 84], [71100, 64], [71400, 45], [71700, 34], [72000, 34], [72300, 34], [72600, 34], [72900, 34], [73200, 33], [73500, 33], [73800, 33], [74100, 33], [74400, 33], [74701, 32], [75005, 28], [75309, 24], [75613, 20], [75917, 15], [76221, 11], [76525, 6]], "point": [49, 127]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-00.35|+01.89|-03.29"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 98, 256], "mask": [[50, 10], [350, 10], [649, 11], [948, 12], [1247, 13], [1547, 13], [1846, 14], [2145, 14], [2444, 15], [2743, 16], [3043, 16], [3342, 17], [3641, 18], [3940, 19], [4240, 19], [4539, 20], [4838, 20], [5137, 21], [5436, 22], [5736, 22], [6035, 23], [6334, 24], [6633, 25], [6933, 25], [7232, 26], [7531, 26], [7830, 27], [8130, 27], [8429, 28], [8728, 29], [9027, 30], [9326, 31], [9626, 31], [9925, 32], [10224, 32], [10523, 33], [10823, 33], [11122, 34], [11421, 35], [11720, 36], [12019, 37], [12319, 37], [12618, 38], [12917, 38], [13216, 39], [13516, 39], [13815, 40], [14114, 41], [14413, 42], [14713, 42], [15012, 43], [15311, 44], [15610, 44], [15909, 45], [16209, 45], [16508, 46], [16807, 47], [17106, 48], [17406, 48], [17706, 48], [18006, 48], [18306, 47], [18605, 48], [18905, 48], [19205, 48], [19505, 48], [19805, 48], [20105, 48], [20405, 48], [20704, 49], [21004, 49], [21304, 48], [21604, 48], [21904, 48], [22204, 48], [22503, 49], [22803, 49], [23103, 51], [23403, 54], [23703, 56], [24003, 59], [24302, 63], [24602, 65], [24902, 68], [25202, 70], [25502, 73], [25802, 75], [26101, 79], [26401, 82], [26701, 84], [27001, 87], [27301, 89], [27601, 92], [27900, 95], [28200, 98], [28500, 99], [28800, 99], [29100, 99], [29400, 99], [29700, 99], [30000, 99], [30300, 99], [30600, 49], [30652, 47], [30900, 49], [30955, 43], [31200, 49], [31258, 40], [31500, 49], [31562, 36], [31800, 52], [31865, 33], [32100, 56], [32168, 30], [32400, 59], [32471, 27], [32700, 62], [32774, 24], [33000, 65], [33077, 21], [33300, 69], [33380, 18], [33600, 72], [33683, 15], [33900, 75], [33987, 11], [34200, 78], [34290, 8], [34500, 82], [34593, 5], [34800, 85], [34896, 2], [35100, 88], [35400, 91], [35700, 94], [36000, 98], [36300, 97], [36600, 97], [36900, 97], [37200, 97], [37500, 97], [37800, 97], [38100, 97], [38400, 97], [38700, 97], [39000, 97], [39300, 97], [39600, 97], [39900, 97], [40200, 97], [40500, 97], [40800, 97], [41100, 97], [41400, 97], [41700, 96], [42000, 96], [42300, 96], [42600, 96], [42900, 96], [43200, 96], [43500, 96], [43800, 96], [44100, 96], [44400, 96], [44700, 96], [45000, 96], [45300, 96], [45600, 96], [45900, 96], [46200, 96], [46500, 96], [46800, 96], [47100, 95], [47400, 95], [47700, 95], [48000, 95], [48300, 95], [48600, 95], [48900, 95], [49200, 95], [49500, 95], [49800, 95], [50100, 95], [50400, 95], [50700, 95], [51000, 95], [51300, 95], [51600, 95], [51900, 95], [52200, 95], [52500, 94], [52800, 94], [53100, 94], [53400, 94], [53700, 94], [54000, 94], [54300, 94], [54600, 94], [54900, 94], [55200, 94], [55500, 94], [55800, 94], [56100, 94], [56400, 94], [56700, 94], [57000, 94], [57300, 94], [57600, 94], [57900, 93], [58200, 93], [58500, 93], [58800, 93], [59100, 93], [59400, 93], [59700, 93], [60000, 93], [60300, 93], [60600, 93], [60900, 93], [61200, 93], [61500, 93], [61800, 38], [61849, 44], [62100, 38], [62150, 43], [62400, 37], [62450, 43], [62700, 37], [62751, 42], [63000, 37], [63051, 42], [63300, 37], [63352, 40], [63600, 37], [63652, 40], [63900, 37], [63952, 40], [64200, 37], [64252, 40], [64500, 37], [64553, 39], [64800, 37], [64853, 39], [65100, 36], [65153, 39], [65400, 36], [65453, 39], [65700, 36], [65753, 39], [66000, 36], [66053, 39], [66300, 36], [66353, 39], [66600, 36], [66653, 39], [66900, 36], [66953, 39], [67200, 36], [67252, 40], [67500, 36], [67552, 40], [67800, 35], [67852, 40], [68100, 35], [68152, 40], [68400, 35], [68452, 40], [68700, 35], [68751, 40], [69000, 35], [69051, 40], [69300, 35], [69350, 41], [69600, 35], [69649, 42], [69900, 35], [69948, 43], [70200, 35], [70246, 45], [70500, 34], [70542, 49], [70800, 84], [71100, 64], [71400, 45], [71700, 34], [72000, 34], [72300, 34], [72600, 34], [72900, 34], [73200, 33], [73500, 33], [73800, 33], [74100, 33], [74400, 33], [74701, 32], [75005, 28], [75309, 24], [75613, 20], [75917, 15], [76221, 11], [76525, 6]], "point": [49, 127]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan28", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -2.25, "y": 0.900998235, "z": -1.5}, "object_poses": [{"objectName": "Pan_d1250561", "position": {"x": -0.2221, "y": 0.961999953, "z": -1.8457}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Pan_d1250561", "position": {"x": -0.2221, "y": 0.961999953, "z": -2.243}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5e3edff7", "position": {"x": -4.24255562, "y": 0.7232722, "z": -0.491501868}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_938fe393", "position": {"x": -3.36445379, "y": 0.8951693, "z": -0.3765198}, "rotation": {"x": 0.0, "y": 90.00033, "z": 0.0}}, {"objectName": "Fork_938fe393", "position": {"x": -3.56536245, "y": 0.8951693, "z": -0.5480565}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Spoon_7dcfa4cd", "position": {"x": -1.47139764, "y": 0.9434294, "z": -3.75178385}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_1d69618b", "position": {"x": -3.8821454, "y": 0.7054999, "z": -0.369833976}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_1d69618b", "position": {"x": -0.7401416, "y": 0.8284667, "z": -3.588819}, "rotation": {"x": 9.92312057e-15, "y": 45.0000343, "z": 9.923131e-15}}, {"objectName": "Cup_41873c33", "position": {"x": -0.126795739, "y": 1.93965423, "z": -2.17961454}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ba5a8c1e", "position": {"x": -0.397310972, "y": 0.9564317, "z": -2.78850985}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ba5a8c1e", "position": {"x": -0.321307868, "y": 1.50284219, "z": -0.2970663}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Knife_1b546504", "position": {"x": -2.560824, "y": 0.9202391, "z": -0.2907554}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Tomato_b1760e7a", "position": {"x": -0.359177083, "y": 1.53947651, "z": -0.7623113}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Bowl_1f51c27f", "position": {"x": -0.376194179, "y": 1.28490734, "z": -0.6459999}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Bread_4d72833d", "position": {"x": -2.56082487, "y": 0.9760524, "z": -0.462293148}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Bread_4d72833d", "position": {"x": -2.56082344, "y": 0.9760524, "z": -0.204986483}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Lettuce_ec37c659", "position": {"x": -3.71027613, "y": 0.851072848, "z": -2.850059}, "rotation": {"x": 0.0, "y": 334.125916, "z": 0.0}}, {"objectName": "Lettuce_ec37c659", "position": {"x": -3.39657831, "y": 0.8510728, "z": -3.073093}, "rotation": {"x": 0.0, "y": 244.125916, "z": 0.0}}, {"objectName": "Mug_f3c7368a", "position": {"x": -2.514, "y": 0.9726702, "z": -3.6494}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f3c7368a", "position": {"x": -0.4693821, "y": 0.0754890442, "z": -2.79361367}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_4eadea0d", "position": {"x": -3.94221377, "y": 0.7527082, "z": -0.491501868}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_4eadea0d", "position": {"x": -3.56535983, "y": 0.956032634, "z": -0.119212061}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "SoapBottle_0407be4c", "position": {"x": -3.87898016, "y": 0.758277535, "z": -2.93188286}, "rotation": {"x": 0.0, "y": 334.125916, "z": 0.0}}, {"objectName": "Pot_c7418f98", "position": {"x": -0.606, "y": 0.8029107, "z": -3.419}, "rotation": {"x": 0.0, "y": 315.000031, "z": 0.0}}, {"objectName": "Fork_938fe393", "position": {"x": -3.3693037, "y": 0.7606369, "z": -3.12932754}, "rotation": {"x": 0.0, "y": 244.125916, "z": 0.0}}, {"objectName": "Cup_41873c33", "position": {"x": -0.5525347, "y": 0.9420032, "z": -1.40667677}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_b1760e7a", "position": {"x": -2.35401559, "y": 0.15042609, "z": -3.57128429}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_d1250561", "position": {"x": -2.96263933, "y": 0.89266485, "z": -0.376522064}, "rotation": {"x": 0.0, "y": 0.000327849062, "z": 0.0}}, {"objectName": "Plate_ba5a8c1e", "position": {"x": -0.17396453, "y": 1.51676917, "z": -2.70088148}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_4d72833d", "position": {"x": -3.36445427, "y": 0.9760524, "z": -0.462288678}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Spatula_1d69618b", "position": {"x": -3.93417239, "y": 0.775088251, "z": -3.2783165}, "rotation": {"x": 0.0, "y": 109.125885, "z": 0.0}}, {"objectName": "Knife_1b546504", "position": {"x": -1.69423819, "y": 0.967918336, "z": -3.82994771}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_6f13b4f9", "position": {"x": -0.465918332, "y": 0.7435723, "z": -1.52597415}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_8ae9f292", "position": {"x": -2.962638, "y": 0.890926957, "z": -0.1192154}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "ButterKnife_e676ac70", "position": {"x": -2.35991645, "y": 0.894662857, "z": -0.2907565}, "rotation": {"x": 0.0, "y": 0.000327849062, "z": 0.0}}, {"objectName": "Apple_4eadea0d", "position": {"x": -0.3349219, "y": 1.34801114, "z": -0.413378119}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "DishSponge_7e2c9e01", "position": {"x": -0.394597232, "y": 0.940864265, "z": -1.40667677}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5e3edff7", "position": {"x": -3.56032, "y": 0.79206425, "z": -3.14335728}, "rotation": {"x": 0.0, "y": 109.125885, "z": 0.0}}, {"objectName": "Egg_571582a1", "position": {"x": -4.24255562, "y": 0.7323631, "z": -0.126497984}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_7dcfa4cd", "position": {"x": -3.94221377, "y": 0.6924257, "z": -0.308999956}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_ec37c659", "position": {"x": -4.06235027, "y": 0.782280743, "z": -0.308999956}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f3c7368a", "position": {"x": -0.2523799, "y": 1.28159928, "z": -0.8786222}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Bowl_1f51c27f", "position": {"x": -0.234681278, "y": 1.57376039, "z": -1.937684}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}], "object_toggles": [], "random_seed": 2153654263, "scene_num": 28}, "task_id": "trial_T20190909_052944_317093", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1HKHM4NVAO98H_3DH6GAKTY1GR38GLWFJ5I4EX2IHYZN", "high_descs": ["turn left and walk over to the right side of the kitchen stove, then look up to face the cabinets above the microwave which is above the kitchen stove", "grab a black cup out of the cabinet above the microwave there", "look back down at the stove a bit, then move to the left a bit to face the left side of the stove and look up at the microwave a bit", "place the cup inside of the microwave, microwave the cup, and then take it back out", "literally just turn right so that you have some space between you and the kitchen counter now ahead, and look up to face the cabinets to the left above the kitchen counter space", "place the cup inside the cabinet on the left above the kitchen counters"], "task_desc": "place a microwaved cup inside of the kitchen cabinet", "votes": [1, 1]}, {"assignment_id": "A2A028LRDJB7ZB_3KWTYT0873U9WVPX6CNZZS56S4A5L0", "high_descs": ["Walk forward then turn left head to the right side cabinet above the microwave", "Open and pick up the container in the cabinet", "Look down and make a step on your left ", "Open the microwave put in and out the container in the microwave then close it", "Look down then turn right walk forward facing the cabinet on the left", "Open and put in the container in the cabinet"], "task_desc": "Put the heated container in the cabinet", "votes": [1, 1]}, {"assignment_id": "A1ELPYAFO7MANS_3NG53N1RLYAIAYNXW2N9STOHZ40P88", "high_descs": ["Turn left and walk to the stove.", "Look up above the microwave and open the top right cabinet. Take out the black glass.", "Look down at the microwave.", "Heat the cup in the microwave.", "Turn right and face the sink.", "Place the cup in the large cabinet over the sink."], "task_desc": "Place a heated cup in a cabinet.", "votes": [1, 1]}]}}