{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000248.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000249.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000254.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000255.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000256.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000258.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000259.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000260.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000261.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000262.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000263.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000264.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000265.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000266.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000267.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000268.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000269.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000270.png", "low_idx": 43}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|2|5|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [0.134391904, 0.134391904, 4.87171268, 4.87171268, 3.3330696, 3.3330696]], "coordinateReceptacleObjectId": ["DiningTable", [-0.848358096, -0.848358096, 3.524, 3.524, 0.0886555464, 0.0886555464]], "forceVisible": true, "objectId": "Egg|+00.03|+00.83|+01.22"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|2|8|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-1.244, -1.244, 8.312, 8.312, 3.7060904, 3.7060904]], "forceVisible": true, "objectId": "Microwave|-00.31|+00.93|+02.08"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|5|-1|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [0.134391904, 0.134391904, 4.87171268, 4.87171268, 3.3330696, 3.3330696]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [8.416, 8.416, -1.116, -1.116, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|+00.03|+00.83|+01.22", "receptacleObjectId": "<PERSON><PERSON>|+02.10|+00.00|-00.28"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+00.03|+00.83|+01.22"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [132, 148, 151, 172], "mask": [[44238, 6], [44536, 10], [44835, 12], [45134, 14], [45433, 16], [45733, 17], [46032, 18], [46332, 19], [46632, 19], [46932, 19], [47232, 19], [47532, 20], [47832, 20], [48132, 20], [48432, 20], [48732, 20], [49033, 18], [49333, 18], [49634, 17], [49934, 16], [50235, 15], [50536, 14], [50837, 12], [51138, 10], [51439, 7]], "point": [141, 159]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [60, 1, 299, 149], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14162, 237], [14462, 236], [14763, 235], [15063, 235], [15363, 234], [15663, 234], [15964, 232], [16264, 232], [16565, 231], [16865, 230], [17165, 230], [17465, 230], [17766, 228], [18066, 228], [18366, 227], [18666, 227], [18966, 226], [19267, 225], [19567, 225], [19867, 224], [20167, 224], [20468, 222], [20768, 222], [21068, 222], [21368, 221], [21669, 220], [21969, 219], [22269, 219], [22569, 219], [22870, 217], [23170, 217], [23470, 216], [23770, 216], [24071, 214], [24371, 214], [24671, 214], [24971, 213], [25272, 212], [25572, 211], [25872, 211], [26172, 211], [26473, 209], [26773, 209], [27073, 208], [27373, 208], [27673, 208], [27974, 206], [28274, 206], [28574, 205], [28874, 205], [29175, 204], [29475, 203], [29775, 203], [30075, 202], [30376, 201], [30676, 200], [30976, 200], [31276, 200], [31577, 198], [31877, 198], [32177, 197], [32477, 197], [32778, 196], [33078, 195], [33378, 195], [33678, 194], [33979, 193], [34279, 193], [34579, 192], [34879, 192], [35179, 191], [35480, 190], [35780, 189], [36080, 189], [36380, 189], [36681, 187], [36981, 187], [37281, 186], [37581, 186], [37882, 185], [38182, 184], [38482, 184], [38782, 183], [39083, 182], [39383, 182], [39683, 181], [39983, 181], [40284, 179], [40584, 179], [40884, 178], [41184, 178], [41485, 177], [41785, 176], [42085, 176], [42385, 176], [42685, 175], [42986, 174], [43286, 173], [43586, 173], [43886, 173], [44187, 171], [44487, 171]], "point": [179, 74]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+00.03|+00.83|+01.22", "placeStationary": true, "receptacleObjectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 272], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14156, 243], [14455, 243], [14754, 244], [15054, 244], [15353, 244], [15653, 244], [15952, 244], [16251, 245], [16551, 245], [16850, 245], [17150, 245], [17449, 246], [17748, 246], [18048, 246], [18347, 246], [18647, 246], [18946, 246], [19245, 247], [19545, 247], [19844, 247], [20144, 247], [20443, 247], [20742, 248], [21042, 248], [21341, 248], [21641, 248], [21940, 248], [22239, 79], [22328, 160], [22539, 77], [22630, 158], [22838, 75], [22932, 155], [23138, 74], [23233, 154], [23437, 74], [23534, 152], [23736, 74], [23834, 152], [24036, 73], [24135, 150], [24335, 74], [24436, 149], [24635, 73], [24736, 149], [24934, 74], [25036, 148], [25233, 75], [25337, 147], [25533, 75], [25637, 146], [25832, 76], [25937, 146], [26132, 76], [26237, 146], [26431, 77], [26537, 145], [26730, 78], [26837, 145], [27030, 79], [27137, 144], [27329, 80], [27437, 144], [27629, 80], [27736, 145], [27928, 82], [28036, 144], [28227, 84], [28335, 145], [28527, 85], [28635, 144], [28826, 87], [28934, 145], [29126, 89], [29233, 146], [29425, 92], [29531, 147], [29724, 96], [29828, 150], [30024, 253], [30323, 254], [30623, 253], [30922, 254], [31221, 255], [31521, 254], [31820, 255], [32120, 254], [32419, 255], [32718, 256], [33018, 255], [33317, 256], [33617, 255], [33916, 256], [34215, 257], [34515, 256], [34814, 257], [35114, 256], [35413, 257], [35712, 257], [36012, 257], [36311, 258], [36611, 257], [36910, 258], [37209, 258], [37509, 258], [37808, 259], [38108, 258], [38407, 259], [38706, 259], [39006, 259], [39305, 260], [39605, 259], [39904, 260], [40203, 260], [40503, 260], [40802, 260], [41102, 260], [41401, 261], [41700, 261], [42000, 261], [42300, 261], [42600, 260], [42900, 260], [43200, 259], [43500, 259], [43800, 259], [44100, 86], [44187, 171], [44400, 85], [44487, 171], [44700, 85], [45000, 85], [45300, 85], [45600, 84], [45900, 84], [46200, 84], [46500, 84], [46800, 83], [47100, 83], [47400, 83], [47700, 83], [48000, 82], [48300, 82], [48600, 82], [48900, 82], [49200, 81], [49500, 81], [49800, 81], [50100, 81], [50400, 80], [50700, 80], [51000, 80], [51300, 80], [51600, 79], [51900, 79], [52200, 79], [52500, 79], [52800, 78], [53100, 78], [53400, 78], [53700, 78], [54000, 77], [54300, 77], [54600, 77], [54900, 77], [55200, 76], [55500, 76], [55800, 76], [56100, 76], [56400, 75], [56700, 75], [57000, 75], [57300, 75], [57600, 74], [57900, 74], [58200, 74], [58500, 74], [58800, 73], [59100, 73], [59400, 73], [59700, 73], [60000, 72], [60300, 72], [60600, 72], [60900, 72], [61200, 71], [61500, 71], [61800, 71], [62100, 71], [62400, 70], [62700, 70], [63000, 70], [63300, 70], [63600, 69], [63900, 69], [64200, 69], [64500, 69], [64800, 68], [65100, 68], [65400, 68], [65700, 68], [66000, 67], [66300, 67], [66600, 67], [66900, 67], [67200, 66], [67500, 66], [67800, 66], [68100, 66], [68400, 65], [68700, 65], [69000, 65], [69300, 65], [69600, 64], [69900, 64], [70200, 64], [70500, 64], [70800, 63], [71100, 63], [71400, 63], [71700, 63], [72000, 62], [72300, 62], [72600, 62], [72900, 62], [73200, 61], [73500, 61], [73800, 61], [74101, 60], [74402, 58], [74703, 57], [75004, 56], [75305, 55], [75606, 53], [75907, 52], [76208, 51], [76509, 50], [76809, 49], [77110, 48], [77411, 12], [77425, 33], [77712, 10], [77726, 32], [78013, 8], [78027, 30], [78314, 6], [78327, 30], [78615, 4], [78628, 29], [78929, 31], [79230, 32], [79531, 32], [79832, 24], [79857, 6], [80132, 24], [80158, 4], [80433, 22], [80734, 21], [81035, 20], [81336, 18]], "point": [149, 135]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 272], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14156, 243], [14455, 243], [14754, 244], [15054, 244], [15353, 244], [15653, 244], [15952, 244], [16251, 245], [16551, 245], [16850, 245], [17150, 245], [17449, 246], [17748, 246], [18048, 246], [18347, 246], [18647, 246], [18946, 246], [19245, 247], [19545, 247], [19844, 247], [20144, 247], [20443, 247], [20742, 248], [21042, 248], [21341, 248], [21641, 248], [21940, 248], [22239, 79], [22328, 160], [22539, 77], [22630, 158], [22838, 75], [22932, 155], [23138, 74], [23233, 154], [23437, 74], [23534, 152], [23736, 74], [23834, 152], [24036, 73], [24135, 150], [24335, 74], [24436, 149], [24635, 73], [24736, 149], [24934, 74], [25036, 16], [25057, 127], [25233, 75], [25337, 13], [25359, 125], [25533, 75], [25637, 12], [25660, 123], [25832, 76], [25937, 11], [25961, 122], [26132, 76], [26237, 10], [26261, 122], [26431, 77], [26537, 10], [26562, 120], [26730, 78], [26837, 9], [26862, 120], [27030, 79], [27137, 9], [27163, 118], [27329, 80], [27437, 8], [27463, 118], [27629, 80], [27736, 9], [27764, 117], [27928, 82], [28036, 9], [28064, 116], [28227, 84], [28335, 10], [28364, 116], [28527, 85], [28635, 10], [28664, 115], [28826, 87], [28934, 11], [28964, 115], [29126, 89], [29233, 12], [29264, 115], [29425, 92], [29531, 14], [29564, 114], [29724, 96], [29828, 17], [29864, 114], [30024, 121], [30163, 114], [30323, 123], [30463, 114], [30623, 123], [30762, 114], [30922, 125], [31062, 114], [31221, 126], [31361, 115], [31521, 127], [31660, 115], [31820, 130], [31959, 116], [32120, 132], [32257, 117], [32419, 255], [32718, 256], [33018, 255], [33317, 256], [33617, 255], [33916, 256], [34215, 257], [34515, 256], [34814, 257], [35114, 256], [35413, 257], [35712, 257], [36012, 257], [36311, 258], [36611, 257], [36910, 258], [37209, 258], [37509, 258], [37808, 259], [38108, 258], [38407, 259], [38706, 259], [39006, 259], [39305, 260], [39605, 259], [39904, 260], [40203, 260], [40503, 260], [40802, 260], [41102, 260], [41401, 261], [41700, 261], [42000, 261], [42300, 261], [42600, 260], [42900, 260], [43200, 259], [43500, 259], [43800, 259], [44100, 86], [44187, 171], [44400, 85], [44487, 171], [44700, 85], [45000, 85], [45300, 85], [45600, 84], [45900, 84], [46200, 84], [46500, 84], [46800, 83], [47100, 83], [47400, 83], [47700, 83], [48000, 82], [48300, 82], [48600, 82], [48900, 82], [49200, 81], [49500, 81], [49800, 81], [50100, 81], [50400, 80], [50700, 80], [51000, 80], [51300, 80], [51600, 79], [51900, 79], [52200, 79], [52500, 79], [52800, 78], [53100, 78], [53400, 78], [53700, 78], [54000, 77], [54300, 77], [54600, 77], [54900, 77], [55200, 76], [55500, 76], [55800, 76], [56100, 76], [56400, 75], [56700, 75], [57000, 75], [57300, 75], [57600, 74], [57900, 74], [58200, 74], [58500, 74], [58800, 73], [59100, 73], [59400, 73], [59700, 73], [60000, 72], [60300, 72], [60600, 72], [60900, 72], [61200, 71], [61500, 71], [61800, 71], [62100, 71], [62400, 70], [62700, 70], [63000, 70], [63300, 70], [63600, 69], [63900, 69], [64200, 69], [64500, 69], [64800, 68], [65100, 68], [65400, 68], [65700, 68], [66000, 67], [66300, 67], [66600, 67], [66900, 67], [67200, 66], [67500, 66], [67800, 66], [68100, 66], [68400, 65], [68700, 65], [69000, 65], [69300, 65], [69600, 64], [69900, 64], [70200, 64], [70500, 64], [70800, 63], [71100, 63], [71400, 63], [71700, 63], [72000, 62], [72300, 62], [72600, 62], [72900, 62], [73200, 61], [73500, 61], [73800, 61], [74101, 60], [74402, 58], [74703, 57], [75004, 56], [75305, 55], [75606, 53], [75907, 52], [76208, 51], [76509, 50], [76809, 49], [77110, 48], [77411, 12], [77425, 33], [77712, 10], [77726, 32], [78013, 8], [78027, 30], [78314, 6], [78327, 30], [78615, 4], [78628, 29], [78929, 31], [79230, 32], [79531, 32], [79832, 24], [79857, 6], [80132, 24], [80158, 4], [80433, 22], [80734, 21], [81035, 20], [81336, 18]], "point": [149, 135]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-1.244, -1.244, 8.312, 8.312, 3.7060904, 3.7060904]], "forceVisible": true, "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [60, 1, 299, 149], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14162, 237], [14462, 236], [14763, 235], [15063, 235], [15363, 234], [15663, 234], [15964, 232], [16264, 232], [16565, 231], [16865, 230], [17165, 230], [17465, 230], [17766, 228], [18066, 228], [18366, 227], [18666, 227], [18966, 226], [19267, 225], [19567, 225], [19867, 224], [20167, 224], [20468, 222], [20768, 222], [21068, 222], [21368, 221], [21669, 220], [21969, 219], [22269, 219], [22569, 219], [22870, 217], [23170, 217], [23470, 216], [23770, 216], [24071, 214], [24371, 214], [24671, 214], [24971, 213], [25272, 212], [25572, 211], [25872, 211], [26172, 211], [26473, 209], [26773, 209], [27073, 208], [27373, 208], [27673, 208], [27974, 206], [28274, 206], [28574, 205], [28874, 205], [29175, 204], [29475, 203], [29775, 203], [30075, 202], [30376, 201], [30676, 200], [30976, 200], [31276, 200], [31577, 198], [31877, 198], [32177, 197], [32477, 197], [32778, 196], [33078, 195], [33378, 195], [33678, 194], [33979, 193], [34279, 193], [34579, 192], [34879, 192], [35179, 191], [35480, 190], [35780, 189], [36080, 189], [36380, 189], [36681, 187], [36981, 187], [37281, 186], [37581, 186], [37882, 185], [38182, 184], [38482, 184], [38782, 183], [39083, 182], [39383, 182], [39683, 181], [39983, 181], [40284, 179], [40584, 179], [40884, 178], [41184, 178], [41485, 177], [41785, 176], [42085, 176], [42385, 176], [42685, 175], [42986, 174], [43286, 173], [43586, 173], [43886, 173], [44187, 171], [44487, 171]], "point": [179, 74]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-1.244, -1.244, 8.312, 8.312, 3.7060904, 3.7060904]], "forceVisible": true, "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [60, 1, 299, 149], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14162, 237], [14462, 236], [14763, 235], [15063, 235], [15363, 234], [15663, 234], [15964, 232], [16264, 232], [16565, 231], [16865, 230], [17165, 230], [17465, 230], [17766, 228], [18066, 228], [18366, 227], [18666, 227], [18966, 226], [19267, 225], [19567, 225], [19867, 224], [20167, 224], [20468, 222], [20768, 222], [21068, 222], [21368, 221], [21669, 220], [21969, 219], [22269, 219], [22569, 219], [22870, 217], [23170, 217], [23470, 216], [23770, 216], [24071, 214], [24371, 214], [24671, 214], [24971, 213], [25272, 212], [25572, 211], [25872, 211], [26172, 211], [26473, 209], [26773, 209], [27073, 208], [27373, 208], [27673, 208], [27974, 206], [28274, 206], [28574, 205], [28874, 205], [29175, 204], [29475, 203], [29775, 203], [30075, 202], [30376, 201], [30676, 200], [30976, 200], [31276, 200], [31577, 198], [31877, 198], [32177, 197], [32477, 197], [32778, 196], [33078, 195], [33378, 195], [33678, 194], [33979, 193], [34279, 193], [34579, 192], [34879, 192], [35179, 191], [35480, 190], [35780, 189], [36080, 189], [36380, 189], [36681, 187], [36981, 187], [37281, 186], [37581, 186], [37882, 185], [38182, 184], [38482, 184], [38782, 183], [39083, 182], [39383, 182], [39683, 181], [39983, 181], [40284, 179], [40584, 179], [40884, 178], [41184, 178], [41485, 177], [41785, 176], [42085, 176], [42385, 176], [42685, 175], [42986, 174], [43286, 173], [43586, 173], [43886, 173], [44187, 171], [44487, 171]], "point": [179, 74]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [60, 1, 299, 149], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14162, 237], [14462, 236], [14763, 235], [15063, 235], [15363, 234], [15663, 234], [15964, 232], [16264, 232], [16565, 231], [16865, 230], [17165, 230], [17465, 230], [17766, 228], [18066, 228], [18366, 227], [18666, 227], [18966, 226], [19267, 225], [19567, 225], [19867, 224], [20167, 224], [20468, 222], [20768, 222], [21068, 222], [21368, 221], [21669, 220], [21969, 219], [22269, 219], [22569, 219], [22870, 217], [23170, 217], [23470, 216], [23770, 216], [24071, 214], [24371, 214], [24671, 214], [24971, 213], [25272, 212], [25572, 211], [25872, 211], [26172, 211], [26473, 209], [26773, 209], [27073, 208], [27373, 208], [27673, 208], [27974, 206], [28274, 206], [28574, 205], [28874, 205], [29175, 204], [29475, 203], [29775, 203], [30075, 202], [30376, 201], [30676, 200], [30976, 200], [31276, 200], [31577, 198], [31877, 198], [32177, 197], [32477, 197], [32778, 196], [33078, 195], [33378, 195], [33678, 194], [33979, 193], [34279, 193], [34579, 192], [34879, 192], [35179, 191], [35480, 190], [35780, 189], [36080, 189], [36380, 189], [36681, 187], [36981, 187], [37281, 186], [37581, 186], [37882, 185], [38182, 184], [38482, 184], [38782, 183], [39083, 182], [39383, 182], [39683, 181], [39983, 181], [40284, 179], [40584, 179], [40884, 178], [41184, 178], [41485, 177], [41785, 176], [42085, 176], [42385, 176], [42685, 175], [42986, 174], [43286, 173], [43586, 173], [43886, 173], [44187, 171], [44487, 171]], "point": [179, 74]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+00.03|+00.83|+01.22"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [145, 84, 163, 108], "mask": [[25052, 5], [25350, 9], [25649, 11], [25948, 13], [26247, 14], [26547, 15], [26846, 16], [27146, 17], [27445, 18], [27745, 19], [28045, 19], [28345, 19], [28645, 19], [28945, 19], [29245, 19], [29545, 19], [29845, 19], [30145, 18], [30446, 17], [30746, 16], [31047, 15], [31347, 14], [31648, 12], [31950, 9], [32252, 5]], "point": [154, 95]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 272], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14156, 243], [14455, 243], [14754, 244], [15054, 244], [15353, 244], [15653, 244], [15952, 244], [16251, 245], [16551, 245], [16850, 245], [17150, 245], [17449, 246], [17748, 246], [18048, 246], [18347, 246], [18647, 246], [18946, 246], [19245, 247], [19545, 247], [19844, 247], [20144, 247], [20443, 247], [20742, 248], [21042, 248], [21341, 248], [21641, 248], [21940, 248], [22239, 79], [22328, 160], [22539, 77], [22630, 158], [22838, 75], [22932, 155], [23138, 74], [23233, 154], [23437, 74], [23534, 152], [23736, 74], [23834, 152], [24036, 73], [24135, 150], [24335, 74], [24436, 149], [24635, 73], [24736, 149], [24934, 74], [25036, 148], [25233, 75], [25337, 147], [25533, 75], [25637, 146], [25832, 76], [25937, 146], [26132, 76], [26237, 146], [26431, 77], [26537, 145], [26730, 78], [26837, 145], [27030, 79], [27137, 144], [27329, 80], [27437, 144], [27629, 80], [27736, 145], [27928, 82], [28036, 144], [28227, 84], [28335, 145], [28527, 85], [28635, 144], [28826, 87], [28934, 145], [29126, 89], [29233, 146], [29425, 92], [29531, 147], [29724, 96], [29828, 150], [30024, 253], [30323, 254], [30623, 253], [30922, 254], [31221, 255], [31521, 254], [31820, 255], [32120, 254], [32419, 255], [32718, 256], [33018, 255], [33317, 256], [33617, 255], [33916, 256], [34215, 257], [34515, 256], [34814, 257], [35114, 256], [35413, 257], [35712, 257], [36012, 257], [36311, 258], [36611, 257], [36910, 258], [37209, 258], [37509, 258], [37808, 259], [38108, 258], [38407, 259], [38706, 259], [39006, 259], [39305, 260], [39605, 259], [39904, 260], [40203, 260], [40503, 260], [40802, 260], [41102, 260], [41401, 261], [41700, 261], [42000, 261], [42300, 261], [42600, 260], [42900, 260], [43200, 259], [43500, 259], [43800, 259], [44100, 86], [44187, 171], [44400, 85], [44487, 171], [44700, 85], [45000, 85], [45300, 85], [45600, 84], [45900, 84], [46200, 84], [46500, 84], [46800, 83], [47100, 83], [47400, 83], [47700, 83], [48000, 82], [48300, 82], [48600, 82], [48900, 82], [49200, 81], [49500, 81], [49800, 81], [50100, 81], [50400, 80], [50700, 80], [51000, 80], [51300, 80], [51600, 79], [51900, 79], [52200, 79], [52500, 79], [52800, 78], [53100, 78], [53400, 78], [53700, 78], [54000, 77], [54300, 77], [54600, 77], [54900, 77], [55200, 76], [55500, 76], [55800, 76], [56100, 76], [56400, 75], [56700, 75], [57000, 75], [57300, 75], [57600, 74], [57900, 74], [58200, 74], [58500, 74], [58800, 73], [59100, 73], [59400, 73], [59700, 73], [60000, 72], [60300, 72], [60600, 72], [60900, 72], [61200, 71], [61500, 71], [61800, 71], [62100, 71], [62400, 70], [62700, 70], [63000, 70], [63300, 70], [63600, 69], [63900, 69], [64200, 69], [64500, 69], [64800, 68], [65100, 68], [65400, 68], [65700, 68], [66000, 67], [66300, 67], [66600, 67], [66900, 67], [67200, 66], [67500, 66], [67800, 66], [68100, 66], [68400, 65], [68700, 65], [69000, 65], [69300, 65], [69600, 64], [69900, 64], [70200, 64], [70500, 64], [70800, 63], [71100, 63], [71400, 63], [71700, 63], [72000, 62], [72300, 62], [72600, 62], [72900, 62], [73200, 61], [73500, 61], [73800, 61], [74101, 60], [74402, 58], [74703, 57], [75004, 56], [75305, 55], [75606, 53], [75907, 52], [76208, 51], [76509, 50], [76809, 49], [77110, 48], [77411, 12], [77425, 33], [77712, 10], [77726, 32], [78013, 8], [78027, 30], [78314, 6], [78327, 30], [78615, 4], [78628, 29], [78929, 31], [79230, 32], [79531, 32], [79832, 24], [79857, 6], [80132, 24], [80158, 4], [80433, 22], [80734, 21], [81035, 20], [81336, 18]], "point": [149, 135]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|+02.10|+00.00|-00.28"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 210], "mask": [[0, 36900], [36901, 299], [37202, 298], [37503, 297], [37804, 296], [38105, 295], [38405, 295], [38706, 294], [39007, 293], [39308, 292], [39609, 291], [39910, 290], [40211, 289], [40511, 289], [40812, 288], [41113, 287], [41414, 286], [41715, 284], [42016, 282], [42316, 281], [42617, 279], [42918, 277], [43219, 275], [43520, 274], [43821, 272], [44121, 271], [44422, 269], [44723, 267], [45024, 265], [45325, 263], [45626, 261], [45927, 259], [46227, 258], [46528, 256], [46829, 254], [47130, 252], [47431, 250], [47732, 248], [48032, 248], [48333, 246], [48634, 244], [48935, 242], [49236, 240], [49537, 238], [49838, 236], [50138, 235], [50439, 233], [50740, 231], [51041, 229], [51342, 227], [51643, 225], [51943, 224], [52244, 223], [52545, 221], [52846, 219], [53147, 217], [53448, 215], [53749, 213], [54049, 212], [54350, 210], [54651, 208], [54952, 206], [55253, 204], [55554, 202], [55854, 201], [56155, 199], [56456, 197], [56757, 196], [57058, 194], [57359, 192], [57660, 190], [57960, 189], [58261, 187], [58562, 185], [58863, 183], [59164, 181], [59465, 179], [59765, 178], [60066, 176], [60367, 174], [60668, 172], [60969, 170], [61270, 169], [61570, 168], [61871, 166], [62172, 164], [62473, 162], [62774, 160]], "point": [149, 104]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+00.03|+00.83|+01.22", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|+02.10|+00.00|-00.28"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 21300], [21301, 299], [21602, 298], [21902, 298], [22203, 297], [22504, 296], [22804, 296], [23105, 295], [23406, 294], [23706, 203], [23916, 84], [24007, 201], [24218, 82], [24308, 199], [24518, 82], [24608, 197], [24819, 81], [24909, 195], [25120, 80], [25210, 193], [25421, 79], [25510, 193], [25722, 78], [25811, 191], [26023, 77], [26111, 191], [26324, 76], [26412, 189], [26624, 76], [26713, 188], [26924, 76], [27013, 188], [27224, 76], [27314, 186], [27524, 76], [27615, 185], [27824, 76], [27915, 186], [28124, 76], [28216, 185], [28423, 77], [28517, 184], [28723, 77], [28817, 184], [29023, 77], [29118, 184], [29322, 78], [29419, 184], [29621, 79], [29719, 184], [29920, 80], [30020, 184], [30219, 81], [30321, 184], [30518, 82], [30621, 185], [30817, 83], [30922, 185], [31116, 84], [31223, 186], [31414, 86], [31523, 277], [31824, 276], [32125, 275], [32425, 275], [32726, 274], [33026, 274], [33327, 273], [33628, 272], [33928, 272], [34229, 271], [34530, 270], [34830, 270], [35131, 269], [35432, 268], [35732, 268], [36033, 267], [36334, 266], [36634, 266], [36935, 265], [37236, 264], [37536, 96], [37647, 153], [37837, 95], [37947, 153], [38138, 94], [38247, 153], [38438, 95], [38546, 154], [38739, 95], [38845, 155], [39040, 95], [39143, 157], [39340, 260], [39641, 259], [39941, 259], [40242, 258], [40543, 257], [40843, 257], [41144, 256], [41445, 255], [41745, 255], [42046, 254], [42347, 253], [42647, 253], [42948, 72], [43023, 177], [43249, 69], [43325, 175], [43549, 68], [43626, 174], [43850, 66], [43927, 173], [44151, 65], [44227, 173], [44451, 65], [44528, 172], [44752, 64], [44828, 172], [45053, 63], [45128, 172], [45353, 63], [45428, 172], [45654, 62], [45728, 172], [45955, 62], [46027, 173], [46255, 62], [46327, 173], [46556, 62], [46626, 174], [46856, 64], [46924, 176], [47157, 243], [47458, 242], [47758, 242], [48059, 241], [48360, 240], [48660, 240], [48961, 239], [49262, 238], [49562, 238], [49863, 237], [50164, 236], [50464, 236], [50765, 235], [51066, 234], [51366, 234], [51667, 233], [51968, 232], [52268, 232], [52569, 231], [52869, 231], [53170, 230], [53471, 229], [53771, 229], [54072, 228], [54373, 227], [54673, 227], [54974, 226], [55275, 225], [55575, 225], [55876, 224], [56177, 223], [56479, 221], [56930, 70], [57230, 70], [57531, 69], [57831, 69], [58131, 69], [58431, 69], [58731, 69], [59031, 69], [59332, 68], [59632, 68], [59932, 68], [60232, 68], [60532, 68], [60832, 68], [61133, 67], [61433, 67], [61733, 67], [62033, 67], [62333, 67], [62633, 67], [62934, 66], [63234, 66], [63534, 66], [63834, 66], [64134, 66], [64434, 66], [64735, 65], [65035, 65], [65335, 65], [65635, 65], [65935, 65], [66235, 65], [66536, 64], [66836, 64], [67136, 64], [67436, 64], [67736, 64], [68036, 64], [68336, 64], [68637, 63], [68937, 63], [69237, 63], [69537, 63], [69837, 63], [70137, 63], [70438, 62], [70738, 62], [71038, 62], [71338, 62], [71638, 62], [71938, 62], [72239, 61], [72539, 61], [72839, 61], [73139, 61], [73439, 61], [73739, 61], [74040, 60], [74340, 60], [74640, 60], [74940, 60], [75240, 60], [75540, 60], [75841, 59], [76141, 59], [76441, 59], [76741, 59], [77041, 59], [77341, 59], [77642, 58], [77942, 58], [78242, 58], [78542, 58], [78842, 58], [79142, 58], [79443, 57], [79743, 57], [80043, 57], [80343, 57], [80643, 57], [80943, 57], [81244, 56], [81544, 56], [81844, 56], [82144, 56], [82444, 56], [82744, 56], [83045, 55], [83345, 55], [83645, 55], [83945, 55], [84245, 55], [84545, 55], [84846, 54], [85146, 54], [85446, 54], [85746, 54], [86046, 54], [86346, 54], [86646, 54], [86947, 53], [87247, 53], [87547, 53], [87847, 53], [88147, 53], [88447, 53], [88748, 52], [89048, 52], [89348, 52], [89648, 52], [89948, 52]], "point": [149, 149]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|+02.10|+00.00|-00.28"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 21300], [21301, 299], [21602, 298], [21902, 298], [22203, 297], [22504, 296], [22804, 296], [23105, 295], [23406, 294], [23706, 203], [23916, 84], [24007, 201], [24218, 82], [24308, 199], [24518, 82], [24608, 197], [24819, 81], [24909, 195], [25120, 80], [25210, 193], [25421, 79], [25510, 193], [25722, 78], [25811, 191], [26023, 77], [26111, 191], [26324, 76], [26412, 189], [26624, 76], [26713, 188], [26924, 76], [27013, 188], [27224, 76], [27314, 186], [27524, 76], [27615, 185], [27824, 76], [27915, 186], [28124, 76], [28216, 185], [28423, 77], [28517, 137], [28659, 42], [28723, 77], [28817, 135], [28960, 41], [29023, 77], [29118, 133], [29261, 41], [29322, 78], [29419, 132], [29562, 41], [29621, 79], [29719, 131], [29863, 40], [29920, 80], [30020, 130], [30163, 41], [30219, 81], [30321, 128], [30463, 42], [30518, 82], [30621, 128], [30763, 43], [30817, 83], [30922, 127], [31063, 44], [31116, 84], [31223, 126], [31363, 46], [31414, 86], [31523, 126], [31663, 137], [31824, 126], [31963, 137], [32125, 125], [32263, 137], [32425, 126], [32562, 138], [32726, 125], [32861, 139], [33026, 126], [33160, 140], [33327, 128], [33458, 142], [33628, 272], [33928, 272], [34229, 271], [34530, 270], [34830, 270], [35131, 269], [35432, 268], [35732, 268], [36033, 267], [36334, 266], [36634, 266], [36935, 265], [37236, 264], [37536, 96], [37647, 153], [37837, 95], [37947, 153], [38138, 94], [38247, 153], [38438, 95], [38546, 154], [38739, 95], [38845, 155], [39040, 95], [39143, 157], [39340, 260], [39641, 259], [39941, 259], [40242, 258], [40543, 257], [40843, 257], [41144, 256], [41445, 255], [41745, 255], [42046, 254], [42347, 253], [42647, 253], [42948, 72], [43023, 177], [43249, 69], [43325, 175], [43549, 68], [43626, 174], [43850, 66], [43927, 173], [44151, 65], [44227, 173], [44451, 65], [44528, 172], [44752, 64], [44828, 172], [45053, 63], [45128, 172], [45353, 63], [45428, 172], [45654, 62], [45728, 172], [45955, 62], [46027, 173], [46255, 62], [46327, 173], [46556, 62], [46626, 174], [46856, 64], [46924, 176], [47157, 243], [47458, 242], [47758, 242], [48059, 241], [48360, 240], [48660, 240], [48961, 239], [49262, 238], [49562, 238], [49863, 237], [50164, 236], [50464, 236], [50765, 235], [51066, 234], [51366, 234], [51667, 233], [51968, 232], [52268, 232], [52569, 231], [52869, 231], [53170, 230], [53471, 229], [53771, 229], [54072, 228], [54373, 227], [54673, 227], [54974, 226], [55275, 225], [55575, 225], [55876, 224], [56177, 223], [56479, 221], [56930, 70], [57230, 70], [57531, 69], [57831, 69], [58131, 69], [58431, 69], [58731, 69], [59031, 69], [59332, 68], [59632, 68], [59932, 68], [60232, 68], [60532, 68], [60832, 68], [61133, 67], [61433, 67], [61733, 67], [62033, 67], [62333, 67], [62633, 67], [62934, 66], [63234, 66], [63534, 66], [63834, 66], [64134, 66], [64434, 66], [64735, 65], [65035, 65], [65335, 65], [65635, 65], [65935, 65], [66235, 65], [66536, 64], [66836, 64], [67136, 64], [67436, 64], [67736, 64], [68036, 64], [68336, 64], [68637, 63], [68937, 63], [69237, 63], [69537, 63], [69837, 63], [70137, 63], [70438, 62], [70738, 62], [71038, 62], [71338, 62], [71638, 62], [71938, 62], [72239, 61], [72539, 61], [72839, 61], [73139, 61], [73439, 61], [73739, 61], [74040, 60], [74340, 60], [74640, 60], [74940, 60], [75240, 60], [75540, 60], [75841, 59], [76141, 59], [76441, 59], [76741, 59], [77041, 59], [77341, 59], [77642, 58], [77942, 58], [78242, 58], [78542, 58], [78842, 58], [79142, 58], [79443, 57], [79743, 57], [80043, 57], [80343, 57], [80643, 57], [80943, 57], [81244, 56], [81544, 56], [81844, 56], [82144, 56], [82444, 56], [82744, 56], [83045, 55], [83345, 55], [83645, 55], [83945, 55], [84245, 55], [84545, 55], [84846, 54], [85146, 54], [85446, 54], [85746, 54], [86046, 54], [86346, 54], [86646, 54], [86947, 53], [87247, 53], [87547, 53], [87847, 53], [88147, 53], [88447, 53], [88748, 52], [89048, 52], [89348, 52], [89648, 52], [89948, 52]], "point": [149, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan27", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": 1.0, "y": 0.9010001, "z": 0.25}, "object_poses": [{"objectName": "Pan_f15c1655", "position": {"x": 2.12791038, "y": 1.51882291, "z": -0.46025005}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Potato_d7da2d9d", "position": {"x": 2.00076771, "y": 1.36164212, "z": -0.00712493062}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_9b26f0a9", "position": {"x": -0.349214524, "y": 0.7920962, "z": 1.21792817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_9b26f0a9", "position": {"x": 1.9069, "y": 0.747938633, "z": 0.4460751}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_3133524b", "position": {"x": 1.80233955, "y": 0.7473578, "z": 0.4460751}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_3133524b", "position": {"x": 1.81528163, "y": 0.7473556, "z": 2.129091}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_343518b0", "position": {"x": 0.033597976, "y": 0.8332674, "z": 1.21792817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_540cf8b1", "position": {"x": -0.0429645181, "y": 0.8165821, "z": 1.21792817}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_540cf8b1", "position": {"x": -0.138568774, "y": 0.7724116, "z": 2.19738054}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_e54ab207", "position": {"x": 2.07127213, "y": 0.9390831, "z": 0.3613872}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "SoapBottle_e54ab207", "position": {"x": -0.196089536, "y": 0.7917468, "z": 1.38609231}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_dbaf3456", "position": {"x": -0.196089536, "y": 0.7877516, "z": 1.21792817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_dbaf3456", "position": {"x": 2.16706157, "y": 1.945743, "z": 1.56363583}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_8e528c3b", "position": {"x": 2.04054713, "y": 0.07743579, "z": 0.372066915}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SaltShaker_8e528c3b", "position": {"x": 2.28023243, "y": 1.49853539, "z": 0.448988259}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1fe08306", "position": {"x": -0.0429645181, "y": 0.785966754, "z": 1.049764}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1fe08306", "position": {"x": -0.3996011, "y": 0.9265222, "z": 2.61829162}, "rotation": {"x": 359.992371, "y": 0.370182216, "z": 0.0156093035}}, {"objectName": "Bread_4be1a058", "position": {"x": 2.112262, "y": 1.37139845, "z": -0.369625568}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_5c2a3ea2", "position": {"x": 2.06402683, "y": 0.9892, "z": 2.4621613}, "rotation": {"x": 0.0, "y": 225.000214, "z": 0.0}}, {"objectName": "Egg_343518b0", "position": {"x": 1.95033979, "y": 0.3809166, "z": -0.09774999}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Lettuce_598ee640", "position": {"x": -0.196089536, "y": 0.8527478, "z": 0.545271635}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_3133524b", "position": {"x": -0.119527027, "y": 0.79151535, "z": 1.21792817}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "SoapBottle_e54ab207", "position": {"x": 1.84060025, "y": 0.9390831, "z": 0.8060128}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Ladle_158e5727", "position": {"x": -0.0429645181, "y": 0.835329235, "z": 0.3771075}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_741f0242", "position": {"x": -0.102359548, "y": 0.9492483, "z": 2.541397}, "rotation": {"x": 352.684021, "y": 46.05348, "z": 344.1139}}, {"objectName": "Bread_4be1a058", "position": {"x": -0.199893564, "y": 0.8239382, "z": 0.813959}, "rotation": {"x": 0.0162762068, "y": 0.0151297245, "z": 359.887421}}, {"objectName": "SaltShaker_8e528c3b", "position": {"x": 2.30194378, "y": 0.9333, "z": 0.8060128}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_d7da2d9d", "position": {"x": -0.328524, "y": 1.07598162, "z": 1.907511}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pan_f15c1655", "position": {"x": 2.1402, "y": 0.9351, "z": 1.5045}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "WineBottle_2a35bd39", "position": {"x": -0.425777048, "y": 0.7918924, "z": 0.3771075}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_db580e26", "position": {"x": -0.141177654, "y": 0.9499199, "z": 2.60949326}, "rotation": {"x": 342.18576, "y": 1.53112257, "z": 2.8599658}}, {"objectName": "Knife_540cf8b1", "position": {"x": 1.94175351, "y": 0.7713487, "z": 0.7210319}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_fac53a9c", "position": {"x": 2.12213445, "y": 0.648705661, "z": -0.5508751}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_92427322", "position": {"x": -0.425777048, "y": 0.840991735, "z": 0.713435769}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_a2d86e36", "position": {"x": -0.119527027, "y": 0.7910089, "z": 1.38609231}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_a2dda372", "position": {"x": 1.85448289, "y": 0.9379, "z": 1.497}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1fe08306", "position": {"x": -0.425777048, "y": 0.785966754, "z": 1.21792817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_7054e289", "position": {"x": 2.132856, "y": 0.3368814, "z": -0.188374981}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "DishSponge_6b8b37c4", "position": {"x": 2.12710762, "y": 1.50309968, "z": 0.7250124}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_922e45ca", "position": {"x": 2.20604277, "y": 1.50552022, "z": 2.01008368}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_9b26f0a9", "position": {"x": -0.03310001, "y": 0.7479226, "z": 2.061443}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_5c2a3ea2", "position": {"x": -0.27265203, "y": 0.7859637, "z": 1.38609231}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_dbaf3456", "position": {"x": 1.67308259, "y": 0.934484839, "z": 2.82104349}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 819679272, "scene_num": 27}, "task_id": "trial_T20190908_022348_008779", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2ALWT2BUSXD83_3N2BF7Y2VTBXXH5FBI06NG3J3UJHME", "high_descs": ["Turn around and walk to the table on your left.", "Pick up the egg on the table.", "Turn to your right and approach the microwave.", "Place the egg in the microwave turn the microwave on and wait before removing the egg.", "Turn to your left and walk to the fridge.", "Place the egg in the fridge."], "task_desc": "Place a warmed egg in the fridge.", "votes": [1, 1]}, {"assignment_id": "AJQGWGESKQT4Y_3RU7GD8VPRAS8TG8ES4X8Y4GHAISPD", "high_descs": ["Turn around and go left to stand in front of the egg on the white table.", "Pick the egg up from the table.", "Go to the right and face the microwave.", "Put the egg in the microwave and close the door and then open the door and then open the door.", "Go to the left and then left again to face the fridge.", "Put the egg in the fridge and then shut the door."], "task_desc": "Put a heated egg in the fridge.", "votes": [1, 1]}, {"assignment_id": "A3UF6XXFFRR237_3WOKGM4L74XAMKKVQQ8Y9ST7CD4O06", "high_descs": ["go the right end of the white table where the egg is", "pick up the egg from the table", "turn to your left and take the egg to the microwave", "put the egg inside the microwave to cook then remove when done", "take the cooked egg over to the fridge", "open the refrigerator door, put the egg inside and close the door"], "task_desc": "cook an egg in the microwave then put it in the fridge to cool.", "votes": [1, 1]}]}}