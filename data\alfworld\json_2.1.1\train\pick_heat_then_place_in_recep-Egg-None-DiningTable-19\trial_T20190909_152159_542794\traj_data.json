{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000281.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000282.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000283.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000284.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000285.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000286.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000287.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000288.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000289.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000290.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000291.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000292.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000293.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000294.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000295.png", "low_idx": 53}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "DiningTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-6|-13|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-6.27647688, -6.27647688, -15.1601696, -15.1601696, 3.3064608, 3.3064608]], "coordinateReceptacleObjectId": ["SinkBasin", [-7.1452, -7.1452, -14.9888, -14.9888, 3.12, 3.12]], "forceVisible": true, "objectId": "Egg|-01.57|+00.83|-03.79"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-3|1|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-0.576, -0.576, -3.632, -3.632, 3.615618228, 3.615618228]], "forceVisible": true, "objectId": "Microwave|-00.14|+00.90|-00.91"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-11|-6|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "diningtable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-6.27647688, -6.27647688, -15.1601696, -15.1601696, 3.3064608, 3.3064608]], "coordinateReceptacleObjectId": ["DiningTable", [-11.476, -11.476, -2.024, -2.024, 2.708, 2.708]], "forceVisible": true, "objectId": "Egg|-01.57|+00.83|-03.79", "receptacleObjectId": "DiningTable|-02.87|+00.68|-00.51"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.57|+00.83|-03.79"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [174, 121, 194, 134], "mask": [[36179, 9], [36477, 13], [36775, 17], [37074, 19], [37378, 16], [37680, 15], [37982, 13], [38283, 12], [38584, 11], [38885, 10], [39186, 8], [39486, 7], [39787, 4], [40087, 2]], "point": [184, 126]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.57|+00.83|-03.79", "placeStationary": true, "receptacleObjectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [59, 46, 299, 242], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17795, 205], [18094, 206], [18394, 206], [18694, 206], [18993, 207], [19293, 207], [19593, 207], [19892, 208], [20192, 208], [20492, 208], [20791, 209], [21091, 209], [21391, 209], [21690, 210], [21990, 108], [22105, 95], [22290, 106], [22408, 92], [22589, 105], [22709, 91], [22889, 104], [23009, 91], [23189, 104], [23310, 89], [23488, 104], [23611, 88], [23788, 104], [23911, 87], [24088, 103], [24211, 87], [24387, 104], [24512, 86], [24687, 104], [24811, 86], [24987, 104], [25111, 86], [25286, 105], [25411, 85], [25586, 105], [25711, 85], [25886, 106], [26010, 85], [26185, 107], [26310, 85], [26485, 108], [26609, 85], [26785, 109], [26908, 86], [27084, 111], [27207, 86], [27384, 112], [27506, 87], [27684, 115], [27804, 89], [27984, 208], [28283, 209], [28583, 208], [28883, 208], [29182, 208], [29482, 208], [29782, 207], [30081, 208], [30381, 208], [30681, 207], [30980, 208], [31280, 207], [31580, 207], [31879, 207], [32179, 207], [32479, 206], [32778, 207], [33078, 206], [33378, 206], [33677, 207], [33977, 206], [34277, 206], [34576, 206], [34876, 206], [35176, 205], [35475, 206], [35775, 205], [36075, 205], [36374, 206], [36674, 205], [36974, 205], [37273, 205], [37573, 205], [37873, 204], [38172, 205], [38472, 201], [38772, 201], [39071, 48], [39239, 34], [39371, 48], [39539, 34], [39671, 48], [39840, 33], [39970, 48], [40140, 32], [40270, 48], [40440, 32], [40570, 48], [40741, 30], [40869, 49], [41169, 49], [41469, 49], [41768, 50], [42068, 49], [42368, 49], [42667, 50], [42967, 50], [43267, 50], [43566, 51], [43866, 51], [44166, 51], [44466, 50], [44765, 51], [45065, 51], [45365, 51], [45664, 52], [45964, 52], [46264, 52], [46563, 53], [46863, 52], [47163, 52], [47462, 53], [47762, 53], [48062, 53], [48361, 54], [48661, 54], [48961, 53], [49260, 54], [49560, 54], [49860, 54], [50159, 55], [50460, 54], [50760, 54], [51060, 54], [51361, 52], [51661, 52], [51961, 52], [52262, 51], [52563, 50], [52864, 49], [53166, 47], [53468, 45], [53769, 43], [54071, 41], [54372, 40], [54674, 38], [54974, 38], [55275, 37], [55575, 37], [55875, 37], [56176, 35], [56476, 35], [56776, 35], [57077, 34], [57377, 34], [57677, 34], [57978, 33], [58278, 32], [58578, 32], [58879, 31], [59179, 31], [59479, 31], [59780, 30], [60080, 30], [60380, 30], [60681, 28], [60981, 28], [61281, 28], [61582, 27], [61882, 27], [62182, 27], [62483, 26], [62783, 26], [63083, 25], [63384, 24], [63684, 24], [63984, 24], [64285, 23], [64585, 23], [64885, 23], [65186, 21], [65486, 21], [65786, 21], [66087, 20], [66387, 20], [66687, 20], [66988, 19], [67288, 19], [67587, 19], [67887, 19], [68187, 19], [68487, 19], [68787, 19], [69086, 20], [69386, 20], [69686, 20], [69986, 22], [70286, 25], [70586, 26], [70885, 20], [70908, 4], [71185, 20], [71208, 5], [71486, 19], [71509, 3], [71786, 19], [72087, 17], [72387, 17]], "point": [179, 129]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [59, 46, 299, 242], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17795, 205], [18094, 206], [18394, 206], [18694, 206], [18993, 207], [19293, 207], [19593, 207], [19892, 208], [20192, 208], [20492, 208], [20791, 209], [21091, 209], [21391, 209], [21690, 210], [21990, 108], [22105, 95], [22290, 106], [22408, 92], [22589, 105], [22709, 91], [22889, 104], [23009, 91], [23189, 104], [23310, 89], [23488, 104], [23611, 88], [23788, 104], [23911, 87], [24088, 103], [24211, 87], [24387, 104], [24512, 86], [24687, 104], [24811, 86], [24987, 104], [25111, 86], [25286, 105], [25411, 85], [25586, 105], [25711, 85], [25886, 106], [26010, 85], [26185, 107], [26310, 85], [26485, 108], [26609, 85], [26785, 78], [26867, 27], [26908, 86], [27084, 78], [27168, 27], [27207, 86], [27384, 76], [27469, 27], [27506, 87], [27684, 76], [27770, 29], [27804, 89], [27984, 75], [28071, 121], [28283, 76], [28371, 121], [28583, 75], [28671, 120], [28883, 75], [28972, 119], [29182, 76], [29272, 118], [29482, 75], [29572, 118], [29782, 75], [29872, 117], [30081, 76], [30172, 117], [30381, 76], [30472, 117], [30681, 76], [30772, 116], [30980, 78], [31072, 116], [31280, 78], [31371, 116], [31580, 78], [31671, 116], [31879, 80], [31970, 116], [32179, 80], [32270, 116], [32479, 81], [32569, 116], [32778, 84], [32867, 118], [33078, 206], [33378, 206], [33677, 207], [33977, 206], [34277, 206], [34576, 206], [34876, 206], [35176, 205], [35475, 206], [35775, 205], [36075, 205], [36374, 206], [36674, 205], [36974, 205], [37273, 205], [37573, 205], [37873, 204], [38172, 205], [38472, 201], [38772, 201], [39071, 48], [39239, 34], [39371, 48], [39539, 34], [39671, 48], [39840, 33], [39970, 48], [40140, 32], [40270, 48], [40440, 32], [40570, 48], [40741, 30], [40869, 49], [41169, 49], [41469, 49], [41768, 50], [42068, 49], [42368, 49], [42667, 50], [42967, 50], [43267, 50], [43566, 51], [43866, 51], [44166, 51], [44466, 50], [44765, 51], [45065, 51], [45365, 51], [45664, 52], [45964, 52], [46264, 52], [46563, 53], [46863, 52], [47163, 52], [47462, 53], [47762, 53], [48062, 53], [48361, 54], [48661, 54], [48961, 53], [49260, 54], [49560, 54], [49860, 54], [50159, 55], [50460, 54], [50760, 54], [51060, 54], [51361, 52], [51661, 52], [51961, 52], [52262, 51], [52563, 50], [52864, 49], [53166, 47], [53468, 45], [53769, 43], [54071, 41], [54372, 40], [54674, 38], [54974, 38], [55275, 37], [55575, 37], [55875, 37], [56176, 35], [56476, 35], [56776, 35], [57077, 34], [57377, 34], [57677, 34], [57978, 33], [58278, 32], [58578, 32], [58879, 31], [59179, 31], [59479, 31], [59780, 30], [60080, 30], [60380, 30], [60681, 28], [60981, 28], [61281, 28], [61582, 27], [61882, 27], [62182, 27], [62483, 26], [62783, 26], [63083, 25], [63384, 24], [63684, 24], [63984, 24], [64285, 23], [64585, 23], [64885, 23], [65186, 21], [65486, 21], [65786, 21], [66087, 20], [66387, 20], [66687, 20], [66988, 19], [67288, 19], [67587, 19], [67887, 19], [68187, 19], [68487, 19], [68787, 19], [69086, 20], [69386, 20], [69686, 20], [69986, 22], [70286, 25], [70586, 26], [70885, 20], [70908, 4], [71185, 20], [71208, 5], [71486, 19], [71509, 3], [71786, 19], [72087, 17], [72387, 17]], "point": [179, 129]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-0.576, -0.576, -3.632, -3.632, 3.615618228, 3.615618228]], "forceVisible": true, "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-0.576, -0.576, -3.632, -3.632, 3.615618228, 3.615618228]], "forceVisible": true, "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.57|+00.83|-03.79"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [157, 90, 171, 110], "mask": [[26863, 4], [27162, 6], [27460, 9], [27760, 10], [28059, 12], [28359, 12], [28658, 13], [28958, 14], [29258, 14], [29557, 15], [29857, 15], [30157, 15], [30457, 15], [30757, 15], [31058, 14], [31358, 13], [31658, 13], [31959, 11], [32259, 11], [32560, 9], [32862, 5]], "point": [164, 99]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [59, 46, 299, 242], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17795, 205], [18094, 206], [18394, 206], [18694, 206], [18993, 207], [19293, 207], [19593, 207], [19892, 208], [20192, 208], [20492, 208], [20791, 209], [21091, 209], [21391, 209], [21690, 210], [21990, 108], [22105, 95], [22290, 106], [22408, 92], [22589, 105], [22709, 91], [22889, 104], [23009, 91], [23189, 104], [23310, 89], [23488, 104], [23611, 88], [23788, 104], [23911, 87], [24088, 103], [24211, 87], [24387, 104], [24512, 86], [24687, 104], [24811, 86], [24987, 104], [25111, 86], [25286, 105], [25411, 85], [25586, 105], [25711, 85], [25886, 106], [26010, 85], [26185, 107], [26310, 85], [26485, 108], [26609, 85], [26785, 109], [26908, 86], [27084, 111], [27207, 86], [27384, 112], [27506, 87], [27684, 115], [27804, 89], [27984, 208], [28283, 209], [28583, 208], [28883, 208], [29182, 208], [29482, 208], [29782, 207], [30081, 208], [30381, 208], [30681, 207], [30980, 208], [31280, 207], [31580, 207], [31879, 207], [32179, 207], [32479, 206], [32778, 207], [33078, 206], [33378, 206], [33677, 207], [33977, 206], [34277, 206], [34576, 206], [34876, 206], [35176, 205], [35475, 206], [35775, 205], [36075, 205], [36374, 206], [36674, 205], [36974, 205], [37273, 205], [37573, 205], [37873, 204], [38172, 205], [38472, 201], [38772, 201], [39071, 48], [39239, 34], [39371, 48], [39539, 34], [39671, 48], [39840, 33], [39970, 48], [40140, 32], [40270, 48], [40440, 32], [40570, 48], [40741, 30], [40869, 49], [41169, 49], [41469, 49], [41768, 50], [42068, 49], [42368, 49], [42667, 50], [42967, 50], [43267, 50], [43566, 51], [43866, 51], [44166, 51], [44466, 50], [44765, 51], [45065, 51], [45365, 51], [45664, 52], [45964, 52], [46264, 52], [46563, 53], [46863, 52], [47163, 52], [47462, 53], [47762, 53], [48062, 53], [48361, 54], [48661, 54], [48961, 53], [49260, 54], [49560, 54], [49860, 54], [50159, 55], [50460, 54], [50760, 54], [51060, 54], [51361, 52], [51661, 52], [51961, 52], [52262, 51], [52563, 50], [52864, 49], [53166, 47], [53468, 45], [53769, 43], [54071, 41], [54372, 40], [54674, 38], [54974, 38], [55275, 37], [55575, 37], [55875, 37], [56176, 35], [56476, 35], [56776, 35], [57077, 34], [57377, 34], [57677, 34], [57978, 33], [58278, 32], [58578, 32], [58879, 31], [59179, 31], [59479, 31], [59780, 30], [60080, 30], [60380, 30], [60681, 28], [60981, 28], [61281, 28], [61582, 27], [61882, 27], [62182, 27], [62483, 26], [62783, 26], [63083, 25], [63384, 24], [63684, 24], [63984, 24], [64285, 23], [64585, 23], [64885, 23], [65186, 21], [65486, 21], [65786, 21], [66087, 20], [66387, 20], [66687, 20], [66988, 19], [67288, 19], [67587, 19], [67887, 19], [68187, 19], [68487, 19], [68787, 19], [69086, 20], [69386, 20], [69686, 20], [69986, 22], [70286, 25], [70586, 26], [70885, 20], [70908, 4], [71185, 20], [71208, 5], [71486, 19], [71509, 3], [71786, 19], [72087, 17], [72387, 17]], "point": [179, 129]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.57|+00.83|-03.79", "placeStationary": true, "receptacleObjectId": "DiningTable|-02.87|+00.68|-00.51"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 74, 252, 257], "mask": [[22032, 1], [22318, 30], [22618, 32], [22918, 31], [23218, 30], [23267, 1], [23518, 30], [23567, 6], [23819, 29], [23867, 11], [24119, 29], [24168, 12], [24419, 29], [24468, 15], [24719, 29], [24768, 18], [25020, 28], [25067, 22], [25320, 28], [25367, 24], [25620, 28], [25667, 27], [25923, 25], [25967, 30], [26224, 24], [26267, 32], [26524, 24], [26567, 34], [26824, 24], [26867, 36], [27125, 23], [27167, 38], [27427, 21], [27467, 39], [27654, 3], [27729, 19], [27767, 41], [27953, 4], [28030, 18], [28067, 43], [28251, 6], [28332, 16], [28367, 44], [28549, 9], [28634, 14], [28667, 3], [28679, 34], [28847, 11], [28935, 13], [28967, 1], [28981, 34], [29145, 14], [29237, 11], [29282, 34], [29443, 17], [29539, 9], [29583, 35], [29742, 20], [29763, 1], [29841, 7], [29884, 36], [30040, 25], [30142, 6], [30184, 37], [30339, 27], [30444, 5], [30485, 37], [30638, 30], [30746, 5], [30785, 38], [30936, 33], [31047, 5], [31085, 39], [31235, 36], [31348, 6], [31385, 20], [31406, 19], [31534, 38], [31648, 8], [31662, 1], [31685, 20], [31707, 19], [31833, 40], [31948, 10], [31960, 2], [31985, 20], [32007, 20], [32131, 44], [32248, 15], [32285, 20], [32308, 20], [32430, 46], [32548, 15], [32584, 21], [32608, 22], [32729, 48], [32848, 15], [32884, 22], [32908, 23], [33028, 49], [33148, 16], [33183, 23], [33209, 23], [33327, 51], [33448, 16], [33482, 24], [33509, 24], [33625, 54], [33747, 18], [33782, 25], [33809, 25], [33924, 56], [34046, 20], [34081, 26], [34109, 26], [34223, 59], [34344, 24], [34379, 28], [34410, 26], [34522, 60], [34642, 28], [34677, 30], [34710, 27], [34821, 62], [34941, 67], [35010, 27], [35120, 65], [35239, 70], [35310, 28], [35419, 67], [35536, 73], [35610, 29], [35718, 69], [35834, 75], [35910, 29], [36018, 71], [36132, 77], [36211, 29], [36317, 29], [36348, 42], [36431, 78], [36511, 29], [36616, 30], [36648, 43], [36729, 81], [36811, 30], [36915, 30], [36948, 45], [37027, 36], [37066, 44], [37111, 31], [37215, 30], [37248, 46], [37326, 36], [37368, 42], [37412, 30], [37514, 30], [37548, 47], [37624, 37], [37670, 40], [37712, 31], [37813, 31], [37848, 49], [37922, 37], [37971, 39], [38012, 31], [38112, 31], [38148, 50], [38222, 36], [38273, 38], [38312, 32], [38411, 32], [38447, 52], [38521, 36], [38573, 38], [38613, 32], [38711, 31], [38747, 54], [38819, 37], [38872, 39], [38913, 32], [39010, 32], [39046, 56], [39117, 38], [39171, 37], [39216, 30], [39309, 32], [39346, 57], [39416, 39], [39469, 37], [39519, 27], [39608, 33], [39645, 26], [39673, 32], [39714, 42], [39768, 39], [39819, 28], [39907, 34], [39945, 24], [39974, 32], [40012, 44], [40067, 40], [40120, 28], [40207, 33], [40244, 23], [40276, 31], [40311, 45], [40366, 41], [40420, 28], [40506, 34], [40543, 22], [40577, 77], [40658, 2], [40665, 40], [40720, 28], [40806, 33], [40842, 21], [40878, 75], [40956, 46], [41021, 28], [41106, 33], [41142, 19], [41179, 72], [41254, 46], [41321, 28], [41405, 33], [41441, 19], [41480, 70], [41553, 45], [41621, 28], [41705, 33], [41740, 18], [41781, 67], [41851, 46], [41922, 27], [42004, 33], [42040, 16], [42081, 66], [42149, 46], [42224, 25], [42304, 33], [42339, 15], [42381, 64], [42448, 46], [42526, 24], [42604, 32], [42639, 13], [42680, 63], [42746, 46], [42828, 22], [42903, 33], [42938, 12], [42979, 62], [43044, 47], [43129, 21], [43203, 32], [43238, 11], [43277, 62], [43343, 47], [43431, 19], [43502, 33], [43538, 9], [43575, 63], [43642, 47], [43732, 19], [43802, 32], [43837, 10], [43873, 64], [43941, 47], [44034, 17], [44102, 31], [44137, 11], [44172, 63], [44240, 47], [44335, 16], [44401, 32], [44437, 11], [44470, 64], [44538, 49], [44636, 15], [44701, 31], [44736, 3], [44744, 5], [44768, 64], [44837, 49], [44938, 13], [45001, 31], [45036, 2], [45045, 5], [45066, 65], [45135, 50], [45239, 13], [45300, 31], [45336, 2], [45346, 5], [45365, 65], [45434, 51], [45540, 12], [45600, 31], [45635, 3], [45646, 6], [45663, 65], [45733, 51], [45840, 12], [45900, 30], [45935, 3], [45947, 6], [45961, 66], [46031, 53], [46141, 11], [46200, 30], [46234, 8], [46248, 6], [46260, 66], [46330, 54], [46441, 11], [46500, 29], [46534, 8], [46549, 6], [46558, 66], [46629, 56], [46741, 12], [46800, 29], [46833, 10], [46850, 74], [46927, 59], [47041, 12], [47100, 29], [47132, 12], [47151, 135], [47341, 12], [47400, 44], [47451, 136], [47641, 12], [47700, 45], [47752, 136], [47940, 12], [48000, 46], [48053, 136], [48239, 13], [48300, 47], [48354, 136], [48539, 13], [48600, 49], [48655, 136], [48838, 14], [48900, 50], [48956, 136], [49137, 15], [49200, 51], [49257, 137], [49437, 15], [49500, 51], [49557, 138], [49736, 15], [49800, 50], [49858, 138], [50035, 16], [50100, 48], [50159, 139], [50334, 17], [50400, 47], [50460, 139], [50633, 18], [50700, 47], [50761, 140], [50931, 20], [51000, 48], [51062, 141], [51230, 20], [51300, 49], [51363, 142], [51529, 21], [51600, 50], [51663, 144], [51827, 23], [51900, 47], [51964, 145], [52126, 24], [52200, 46], [52265, 146], [52424, 26], [52500, 45], [52566, 148], [52721, 29], [52800, 44], [52867, 182], [53100, 43], [53168, 181], [53400, 43], [53468, 181], [53700, 43], [53769, 180], [54000, 43], [54070, 179], [54300, 43], [54371, 177], [54600, 43], [54671, 177], [54900, 43], [54972, 176], [55200, 43], [55262, 1], [55273, 174], [55500, 44], [55562, 3], [55574, 173], [55800, 44], [55861, 5], [55874, 172], [56100, 45], [56161, 6], [56175, 171], [56401, 45], [56460, 8], [56475, 170], [56701, 47], [56759, 11], [56775, 170], [57001, 48], [57056, 15], [57076, 168], [57302, 71], [57376, 167], [57602, 73], [57676, 167], [57903, 239], [58203, 238], [58504, 237], [58804, 236], [59105, 124], [59230, 110], [59405, 113], [59527, 112], [59705, 107], [59819, 7], [59829, 109], [60006, 64], [60124, 114], [60306, 63], [60403, 1], [60418, 6], [60428, 109], [60607, 61], [60692, 17], [60723, 4], [60729, 107], [60907, 62], [60982, 28], [61018, 4], [61026, 110], [61208, 104], [61322, 113], [61508, 107], [61616, 119], [61809, 225], [62109, 224], [62410, 223], [62710, 222], [63011, 220], [63312, 219], [63612, 218], [63913, 216], [64215, 213], [64516, 210], [64817, 208], [65118, 206], [65419, 204], [65720, 201], [66021, 199], [66322, 197], [66623, 195], [66924, 192], [67225, 190], [67526, 188], [67827, 186], [68128, 183], [68429, 181], [68730, 179], [69031, 177], [69333, 173], [69634, 171], [69935, 169], [70236, 167], [70537, 164], [70839, 160], [71142, 154], [71444, 150], [71746, 99], [71855, 37], [72048, 95], [72157, 32], [72351, 90], [72459, 28], [72653, 87], [72760, 24], [72955, 84], [73061, 21], [73257, 81], [73362, 17], [73560, 77], [73663, 14], [73862, 74], [73964, 11], [74164, 71], [74265, 7], [74466, 69], [74565, 5], [74768, 66], [74866, 1], [75071, 63], [75373, 61], [75681, 52], [75988, 45], [76296, 37], [76603, 29], [76911, 13]], "point": [126, 164]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan19", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -1.75, "y": 0.9016907, "z": -1.0}, "object_poses": [{"objectName": "DishSponge_78f219bc", "position": {"x": -1.77635264, "y": 0.790120363, "z": -3.66151619}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_a7531534", "position": {"x": -3.29236221, "y": 0.7138123, "z": -0.558499932}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "ButterKnife_a7531534", "position": {"x": -3.22503686, "y": 0.913742, "z": -3.32765055}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_fca8a224", "position": {"x": -0.257330954, "y": 0.9309998, "z": -0.524823248}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_fca8a224", "position": {"x": -2.43923759, "y": 0.7319673, "z": -0.371000051}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Fork_1cd5bb8d", "position": {"x": -1.91450834, "y": 0.7911047, "z": -3.790042}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Fork_1cd5bb8d", "position": {"x": -2.9283, "y": 0.714318752, "z": -0.9225625}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_410beacd", "position": {"x": -0.09370521, "y": 1.29474783, "z": -1.58086491}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_410beacd", "position": {"x": -2.27053785, "y": 0.914025366, "z": -3.87812114}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_bdd88192", "position": {"x": -1.52344751, "y": 0.08108115, "z": -3.63424754}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_bdd88192", "position": {"x": -3.176843, "y": 1.866308, "z": -1.974901}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_46bffc5f", "position": {"x": -0.471112043, "y": 0.778778732, "z": -0.6097589}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_46bffc5f", "position": {"x": -3.14090919, "y": 0.94613713, "z": -2.92600584}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_dd4bbd23", "position": {"x": -0.1792707, "y": 1.86775422, "z": -1.88840556}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_697b561f", "position": {"x": -2.47578621, "y": 0.711368263, "z": -0.709237635}, "rotation": {"x": 0.0, "y": 135.000092, "z": 0.0}}, {"objectName": "SaltShaker_2ce80e75", "position": {"x": -0.5006437, "y": 0.742647648, "z": -0.912980556}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_2ce80e75", "position": {"x": -3.05678177, "y": 0.910006046, "z": -2.82559443}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_5fac94a5", "position": {"x": -0.176295966, "y": 0.91230613, "z": -1.61635566}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_5fac94a5", "position": {"x": -0.41940093, "y": 0.91230613, "z": -1.49366212}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_908150ce", "position": {"x": -3.07920718, "y": 0.729054153, "z": -2.06418562}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Mug_f4b8ca2f", "position": {"x": -2.93075824, "y": 0.763, "z": -0.358240575}, "rotation": {"x": 0.0, "y": 134.999786, "z": 0.0}}, {"objectName": "Mug_f4b8ca2f", "position": {"x": -3.37415123, "y": 1.86315763, "z": -1.86578441}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Apple_1634f71e", "position": {"x": -0.0963172, "y": 1.055421, "z": -0.950173}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Pot_fd5c5c80", "position": {"x": -0.1653, "y": 0.9759, "z": -2.0309}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Egg_b0ce4484", "position": {"x": -1.56911922, "y": 0.8266152, "z": -3.7900424}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_410beacd", "position": {"x": -3.079207, "y": 1.39922929, "z": -2.18732524}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bread_77e17ce3", "position": {"x": -3.30916429, "y": 0.954571664, "z": -3.026417}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_1cd5bb8d", "position": {"x": -0.0763015747, "y": 0.914248466, "z": -2.91177487}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_4966d0cc", "position": {"x": -0.3946991, "y": 0.9731094, "z": -2.41475344}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_2b74ee3a", "position": {"x": -3.014288, "y": 0.9302006, "z": -2.44023085}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "Lettuce_908150ce", "position": {"x": -0.244556189, "y": 1.0015142, "z": -2.91177487}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_f34461c5", "position": {"x": -2.62052226, "y": 0.7633537, "z": -0.339110732}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Apple_1634f71e", "position": {"x": -3.034843, "y": 0.7596065, "z": -0.9302077}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "SoapBottle_dd4bbd23", "position": {"x": -2.70891047, "y": 0.716563761, "z": -0.250722349}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Spatula_fca8a224", "position": {"x": -2.80945253, "y": 0.7319673, "z": -0.6407361}, "rotation": {"x": 0.0, "y": 135.000092, "z": 0.0}}, {"objectName": "SaltShaker_2ce80e75", "position": {"x": -0.500435948, "y": 0.91230613, "z": -1.49366212}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_46bffc5f", "position": {"x": -3.21161938, "y": 0.7494046, "z": -0.753430843}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "PepperShaker_5fac94a5", "position": {"x": -0.5006436, "y": 0.742647648, "z": -2.91359067}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_a7531534", "position": {"x": -3.16700172, "y": 0.913742065, "z": -3.72987866}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_bdd88192", "position": {"x": -0.773222446, "y": 0.08024043, "z": -3.6358695}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_78f219bc", "position": {"x": -3.15527177, "y": 0.716531634, "z": -0.6086949}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Spoon_94d2125c", "position": {"x": -0.176295966, "y": 0.9171294, "z": -1.3709687}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f4b8ca2f", "position": {"x": -1.569119, "y": 0.7857144, "z": -3.704359}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_697b561f", "position": {"x": -3.22617, "y": 1.86575568, "z": -2.39285183}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2364774313, "scene_num": 19}, "task_id": "trial_T20190909_152159_542794", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A20FCMWP43CVIU_308XBLVESLVMO91JC6B8OQEX81IBRC", "high_descs": ["walk to face sink", "pick up egg from sink", "walk to face microwave", "heat and remove egg from microwave", "walk to face round table", "put egg on table"], "task_desc": "put heated egg on kitchen table", "votes": [1, 1]}, {"assignment_id": "AM2KK02JXXW48_32RIADZISVVE4PGNCG1Q6MDNKBK4SN", "high_descs": ["Move to the sink on your left.", "Pick up the egg in the sink. ", "Turn around, bring the egg to the microwave on the right.", "Heat the egg in the microwave. ", "Take the heated egg to the round table behind you.", "Put the egg on the round table, right of the fork. "], "task_desc": "Put a heated egg on the round table. ", "votes": [1, 1]}, {"assignment_id": "A17TKHT8FEVH0R_3PWWM24LHVPMIRMRU4CXWGGOO3K28S", "high_descs": ["Turn left and go to the sink", "Grab the egg out of the sink", "Turn around and go to the microwave", "Heat the egg in the microwave and then take it out", "Turn around and go to the round table", "Put the egg on the table"], "task_desc": "Putting a hot egg on the round table", "votes": [1, 1]}]}}