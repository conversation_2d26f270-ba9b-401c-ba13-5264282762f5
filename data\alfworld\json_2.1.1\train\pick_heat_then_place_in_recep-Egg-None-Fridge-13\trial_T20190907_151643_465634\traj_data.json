{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000281.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000282.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000283.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000284.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000285.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000286.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000287.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000288.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000289.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000290.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000291.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000292.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000293.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000294.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000295.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000296.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000297.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000298.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000299.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000300.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000301.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000302.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000303.png", "low_idx": 46}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-7|10|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-7.44051932, -7.44051932, 8.10603048, 8.10603048, 3.837358, 3.837358]], "coordinateReceptacleObjectId": ["CounterTop", [-7.4884, -7.4884, 7.2, 7.2, 3.836, 3.836]], "forceVisible": true, "objectId": "Egg|-01.86|+00.96|+02.03"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-11|10|2|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-5|15|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-7.44051932, -7.44051932, 8.10603048, 8.10603048, 3.837358, 3.837358]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-1.298918724, -1.298918724, 14.412, 14.412, -0.00599211456, -0.00599211456]], "forceVisible": true, "objectId": "Egg|-01.86|+00.96|+02.03", "receptacleObjectId": "<PERSON><PERSON>|-00.32|00.00|+03.60"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.86|+00.96|+02.03"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [192, 102, 216, 123], "mask": [[30500, 10], [30798, 14], [31097, 17], [31396, 19], [31695, 21], [31994, 22], [32294, 23], [32593, 24], [32893, 24], [33193, 24], [33492, 25], [33792, 25], [34092, 24], [34393, 23], [34693, 22], [34993, 22], [35294, 20], [35594, 19], [35895, 17], [36196, 15], [36498, 11], [36801, 5]], "point": [204, 111]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.86|+00.96|+02.03", "placeStationary": true, "receptacleObjectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 101], [17525, 138], [17700, 99], [17828, 135], [18000, 100], [18129, 134], [18300, 100], [18432, 131], [18600, 100], [18733, 130], [18900, 100], [19028, 2], [19034, 129], [19200, 100], [19328, 3], [19334, 129], [19500, 100], [19628, 4], [19634, 128], [19800, 100], [19928, 4], [19935, 127], [20100, 100], [20228, 4], [20234, 128], [20400, 100], [20528, 4], [20534, 128], [20700, 100], [20828, 4], [20834, 128], [21000, 100], [21128, 4], [21134, 128], [21300, 101], [21428, 4], [21434, 128], [21600, 101], [21728, 4], [21734, 128], [21900, 101], [22028, 3], [22034, 128], [22200, 101], [22328, 3], [22334, 127], [22500, 101], [22628, 3], [22633, 128], [22800, 101], [22928, 3], [22933, 128], [23100, 101], [23228, 2], [23233, 128], [23400, 101], [23528, 2], [23532, 129], [23700, 101], [23827, 3], [23832, 129], [24000, 101], [24127, 2], [24132, 129], [24300, 101], [24427, 2], [24431, 130], [24600, 102], [24727, 1], [24731, 130], [24900, 102], [25030, 131], [25200, 102], [25329, 131], [25500, 102], [25628, 132], [25800, 102], [25927, 133], [26100, 102], [26227, 133], [26400, 102], [26527, 133], [26700, 102], [26827, 133], [27000, 102], [27127, 133], [27300, 102], [27427, 133], [27600, 103], [27727, 133], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 101], [17525, 138], [17700, 99], [17828, 135], [18000, 100], [18129, 134], [18300, 100], [18432, 131], [18600, 100], [18733, 130], [18900, 100], [19034, 129], [19200, 100], [19334, 129], [19500, 100], [19634, 128], [19800, 100], [19935, 127], [20100, 100], [20236, 126], [20400, 100], [20537, 125], [20700, 100], [20837, 125], [21000, 100], [21138, 124], [21300, 101], [21438, 124], [21600, 101], [21739, 123], [21900, 101], [22039, 123], [22200, 101], [22339, 122], [22500, 101], [22640, 121], [22800, 101], [22940, 121], [23100, 101], [23240, 121], [23400, 101], [23540, 121], [23700, 101], [23840, 121], [24000, 101], [24140, 121], [24300, 101], [24440, 121], [24600, 102], [24739, 122], [24900, 102], [25039, 122], [25200, 102], [25339, 121], [25500, 102], [25638, 122], [25800, 102], [25938, 122], [26100, 102], [26237, 123], [26400, 102], [26536, 124], [26700, 102], [26835, 125], [27000, 102], [27133, 127], [27300, 102], [27431, 129], [27600, 103], [27727, 133], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.86|+00.96|+02.03"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [117, 64, 139, 92], "mask": [[19025, 6], [19323, 10], [19622, 12], [19921, 14], [20220, 16], [20519, 18], [20819, 18], [21118, 20], [21418, 20], [21718, 21], [22017, 22], [22317, 22], [22617, 23], [22917, 23], [23217, 23], [23517, 23], [23817, 23], [24117, 23], [24417, 23], [24717, 22], [25018, 21], [25318, 21], [25619, 19], [25919, 19], [26220, 17], [26521, 15], [26822, 13], [27124, 9], [27427, 4]], "point": [128, 77]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 101], [17525, 138], [17700, 99], [17828, 135], [18000, 100], [18129, 134], [18300, 100], [18432, 131], [18600, 100], [18733, 130], [18900, 100], [19028, 2], [19034, 129], [19200, 100], [19328, 3], [19334, 129], [19500, 100], [19628, 4], [19634, 128], [19800, 100], [19928, 4], [19935, 127], [20100, 100], [20228, 4], [20234, 128], [20400, 100], [20528, 4], [20534, 128], [20700, 100], [20828, 4], [20834, 128], [21000, 100], [21128, 4], [21134, 128], [21300, 101], [21428, 4], [21434, 128], [21600, 101], [21728, 4], [21734, 128], [21900, 101], [22028, 3], [22034, 128], [22200, 101], [22328, 3], [22334, 127], [22500, 101], [22628, 3], [22633, 128], [22800, 101], [22928, 3], [22933, 128], [23100, 101], [23228, 2], [23233, 128], [23400, 101], [23528, 2], [23532, 129], [23700, 101], [23827, 3], [23832, 129], [24000, 101], [24127, 2], [24132, 129], [24300, 101], [24427, 2], [24431, 130], [24600, 102], [24727, 1], [24731, 130], [24900, 102], [25030, 131], [25200, 102], [25329, 131], [25500, 102], [25628, 132], [25800, 102], [25927, 133], [26100, 102], [26227, 133], [26400, 102], [26527, 133], [26700, 102], [26827, 133], [27000, 102], [27127, 133], [27300, 102], [27427, 133], [27600, 103], [27727, 133], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-00.32|00.00|+03.60"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 199], "mask": [[0, 18600], [18601, 299], [18901, 299], [19202, 298], [19503, 297], [19803, 297], [20104, 296], [20405, 295], [20705, 295], [21006, 294], [21307, 293], [21607, 293], [21908, 292], [22208, 292], [22509, 291], [22810, 290], [23110, 290], [23411, 289], [23712, 288], [24012, 288], [24313, 287], [24613, 287], [24914, 286], [25215, 285], [25515, 285], [25816, 284], [26117, 283], [26417, 283], [26718, 282], [27018, 282], [27319, 281], [27620, 280], [27920, 280], [28221, 279], [28522, 278], [28822, 278], [29123, 277], [29423, 277], [29724, 276], [30025, 275], [30325, 275], [30626, 274], [30927, 273], [31227, 273], [31528, 272], [31828, 272], [32129, 271], [32430, 270], [32730, 270], [33031, 269], [33332, 268], [33632, 268], [33933, 267], [34234, 266], [34534, 266], [34835, 265], [35135, 265], [35439, 261], [35739, 261], [36040, 260], [36339, 261], [36639, 261], [36939, 261], [37238, 262], [37539, 261], [37839, 261], [38142, 258], [38442, 258], [38743, 257], [39044, 256], [39344, 256], [39645, 255], [39945, 255], [40246, 254], [40547, 253], [40847, 253], [41148, 252], [41449, 251], [41749, 251], [42050, 250], [42350, 250], [42651, 249], [42952, 248], [43252, 248], [43553, 247], [43854, 246], [44154, 246], [44455, 245], [44755, 245], [45056, 244], [45357, 243], [45657, 243], [45958, 242], [46259, 241], [46559, 241], [46860, 240], [47160, 240], [47461, 239], [47762, 237], [48062, 236], [48363, 234], [48664, 232], [48964, 231], [49265, 229], [49565, 228], [49866, 225], [50167, 223], [50467, 222], [50768, 220], [51069, 218], [51369, 217], [51670, 215], [51971, 213], [52271, 212], [52572, 210], [52872, 209], [53173, 207], [53474, 205], [53774, 204], [54075, 202], [54376, 200], [54676, 198], [54977, 196], [55277, 195], [55578, 193], [55879, 191], [56179, 190], [56480, 188], [56781, 186], [57081, 185], [57384, 179], [57684, 11], [57719, 109], [57851, 12], [57984, 11], [58151, 12], [58284, 10], [58451, 12], [58584, 10], [58752, 11], [58885, 9], [59052, 10], [59185, 9], [59352, 10], [59485, 9], [59652, 10]], "point": [149, 99]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.86|+00.96|+02.03", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-00.32|00.00|+03.60"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 266], "mask": [[0, 107], [132, 53], [202, 47], [300, 109], [431, 57], [500, 49], [600, 111], [729, 121], [900, 113], [1027, 123], [1200, 121], [1322, 129], [1500, 252], [1800, 252], [2100, 253], [2400, 253], [2700, 253], [3000, 253], [3300, 253], [3600, 253], [3900, 254], [4200, 254], [4500, 255], [4800, 255], [5100, 256], [5400, 256], [5700, 257], [5999, 258], [6299, 259], [6598, 260], [6897, 262], [7196, 265], [7495, 267], [7794, 270], [8092, 276], [8388, 27651], [36040, 598], [36639, 299], [36939, 599], [37539, 298], [37839, 298], [38142, 295], [38442, 295], [38743, 293], [39044, 292], [39344, 292], [39645, 291], [39945, 290], [40246, 289], [40547, 288], [40847, 287], [41148, 286], [41449, 285], [41749, 285], [42050, 283], [42350, 283], [42651, 282], [42952, 281], [43252, 280], [43553, 279], [43854, 278], [44154, 278], [44455, 276], [44755, 276], [45056, 275], [45357, 274], [45657, 273], [45958, 272], [46259, 271], [46559, 270], [46860, 269], [47160, 269], [47461, 268], [47762, 237], [48000, 28], [48062, 236], [48300, 28], [48363, 234], [48600, 28], [48664, 232], [48900, 28], [48964, 231], [49200, 27], [49265, 229], [49500, 27], [49565, 228], [49800, 27], [49866, 225], [50100, 27], [50167, 223], [50400, 26], [50467, 222], [50700, 26], [50768, 220], [51000, 26], [51069, 218], [51300, 25], [51369, 217], [51600, 25], [51670, 215], [51900, 25], [51971, 213], [52200, 25], [52271, 212], [52500, 24], [52572, 210], [52800, 24], [52872, 209], [53100, 24], [53173, 207], [53400, 24], [53474, 205], [53700, 23], [53774, 204], [54000, 23], [54075, 202], [54300, 23], [54376, 200], [54600, 23], [54676, 198], [54900, 22], [54977, 196], [55200, 22], [55277, 195], [55500, 22], [55578, 193], [55800, 22], [55879, 191], [56100, 21], [56179, 190], [56400, 21], [56480, 188], [56700, 21], [56781, 186], [57000, 20], [57081, 185], [57300, 20], [57384, 179], [57600, 20], [57684, 11], [57719, 109], [57851, 12], [57900, 20], [57984, 11], [58151, 12], [58200, 19], [58284, 10], [58451, 12], [58500, 19], [58584, 10], [58752, 11], [58800, 19], [58885, 9], [59052, 10], [59100, 19], [59185, 9], [59352, 10], [59400, 18], [59485, 9], [59652, 10], [59700, 18], [60000, 18], [60300, 18], [60600, 17], [60900, 17], [61200, 17], [61500, 17], [61800, 16], [62100, 16], [62400, 16], [62700, 15], [63000, 15], [63300, 15], [63600, 15], [63900, 14], [64200, 14], [64500, 14], [64800, 14], [65100, 13], [65400, 13], [65700, 13], [66000, 13], [66300, 12], [66600, 12], [66900, 12], [67200, 11], [67500, 11], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 10], [69300, 10], [69600, 9], [69900, 9], [70200, 9], [70500, 9], [70800, 8], [71100, 8], [71400, 8], [71700, 8], [72000, 7], [72300, 7], [72600, 7], [72900, 6], [73200, 6], [73500, 6], [73800, 6], [74100, 5], [74400, 5], [74700, 5], [75000, 5], [75300, 4], [75600, 4], [75900, 4], [76200, 4], [76500, 3], [76800, 3], [77100, 3], [77400, 3], [77700, 2], [78000, 2], [78300, 2], [78600, 1], [78900, 1], [79200, 1], [79500, 1]], "point": [149, 132]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-00.32|00.00|+03.60"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 266], "mask": [[0, 107], [132, 21], [163, 22], [202, 47], [300, 109], [431, 21], [464, 24], [500, 49], [600, 111], [729, 22], [765, 85], [900, 113], [1027, 23], [1066, 84], [1200, 121], [1322, 27], [1366, 85], [1500, 149], [1667, 85], [1800, 148], [1967, 85], [2100, 148], [2268, 85], [2400, 147], [2568, 85], [2700, 147], [2868, 85], [3000, 147], [3169, 84], [3300, 147], [3469, 84], [3600, 147], [3769, 84], [3900, 147], [4069, 85], [4200, 147], [4369, 85], [4500, 147], [4669, 86], [4800, 147], [4969, 86], [5100, 147], [5268, 88], [5400, 147], [5568, 88], [5700, 148], [5868, 89], [5999, 149], [6167, 90], [6299, 150], [6467, 91], [6598, 151], [6766, 92], [6897, 153], [7065, 94], [7196, 155], [7364, 97], [7495, 157], [7663, 99], [7794, 160], [7961, 103], [8092, 276], [8388, 27651], [36040, 598], [36639, 299], [36939, 599], [37539, 298], [37839, 298], [38142, 295], [38442, 295], [38743, 293], [39044, 292], [39344, 292], [39645, 291], [39945, 290], [40246, 289], [40547, 288], [40847, 287], [41148, 286], [41449, 285], [41749, 285], [42050, 283], [42350, 283], [42651, 282], [42952, 281], [43252, 280], [43553, 279], [43854, 278], [44154, 278], [44455, 276], [44755, 276], [45056, 275], [45357, 274], [45657, 273], [45958, 272], [46259, 271], [46559, 270], [46860, 269], [47160, 269], [47461, 268], [47762, 237], [48000, 28], [48062, 236], [48300, 28], [48363, 234], [48600, 28], [48664, 232], [48900, 28], [48964, 231], [49200, 27], [49265, 229], [49500, 27], [49565, 228], [49800, 27], [49866, 225], [50100, 27], [50167, 223], [50400, 26], [50467, 222], [50700, 26], [50768, 220], [51000, 26], [51069, 218], [51300, 25], [51369, 217], [51600, 25], [51670, 215], [51900, 25], [51971, 213], [52200, 25], [52271, 212], [52500, 24], [52572, 210], [52800, 24], [52872, 209], [53100, 24], [53173, 207], [53400, 24], [53474, 205], [53700, 23], [53774, 204], [54000, 23], [54075, 202], [54300, 23], [54376, 200], [54600, 23], [54676, 198], [54900, 22], [54977, 196], [55200, 22], [55277, 195], [55500, 22], [55578, 193], [55800, 22], [55879, 191], [56100, 21], [56179, 190], [56400, 21], [56480, 188], [56700, 21], [56781, 186], [57000, 20], [57081, 185], [57300, 20], [57384, 179], [57600, 20], [57684, 11], [57719, 109], [57851, 12], [57900, 20], [57984, 11], [58151, 12], [58200, 19], [58284, 10], [58451, 12], [58500, 19], [58584, 10], [58752, 11], [58800, 19], [58885, 9], [59052, 10], [59100, 19], [59185, 9], [59352, 10], [59400, 18], [59485, 9], [59652, 10], [59700, 18], [60000, 18], [60300, 18], [60600, 17], [60900, 17], [61200, 17], [61500, 17], [61800, 16], [62100, 16], [62400, 16], [62700, 15], [63000, 15], [63300, 15], [63600, 15], [63900, 14], [64200, 14], [64500, 14], [64800, 14], [65100, 13], [65400, 13], [65700, 13], [66000, 13], [66300, 12], [66600, 12], [66900, 12], [67200, 11], [67500, 11], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 10], [69300, 10], [69600, 9], [69900, 9], [70200, 9], [70500, 9], [70800, 8], [71100, 8], [71400, 8], [71700, 8], [72000, 7], [72300, 7], [72600, 7], [72900, 6], [73200, 6], [73500, 6], [73800, 6], [74100, 5], [74400, 5], [74700, 5], [75000, 5], [75300, 4], [75600, 4], [75900, 4], [76200, 4], [76500, 3], [76800, 3], [77100, 3], [77400, 3], [77700, 2], [78000, 2], [78300, 2], [78600, 1], [78900, 1], [79200, 1], [79500, 1]], "point": [149, 132]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan13", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -1.75, "y": 0.8995012, "z": 4.0}, "object_poses": [{"objectName": "Pencil_282a316d", "position": {"x": -0.473475426, "y": 0.747098565, "z": 5.111621}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Potato_9933ef2d", "position": {"x": -0.181592077, "y": 0.958568037, "z": 5.932334}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_9933ef2d", "position": {"x": -0.383563221, "y": 1.71932483, "z": 3.915005}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -4.0956, "y": 0.744429, "z": 4.809826}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -0.334957659, "y": 0.924664259, "z": 4.615}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8b572f1d", "position": {"x": -4.031869, "y": 0.9235421, "z": 1.7999959}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8b572f1d", "position": {"x": -3.73307371, "y": 0.923542, "z": 5.41275358}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_04c898c4", "position": {"x": -2.92400026, "y": 0.9303, "z": 1.712}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Fork_1dc44411", "position": {"x": -1.94325483, "y": 0.924048543, "z": 2.02650762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_1dc44411", "position": {"x": -1.69388, "y": 0.924048543, "z": 1.9510051}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_8612b3fc", "position": {"x": -2.01585555, "y": 0.7479368, "z": 1.956092}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_8612b3fc", "position": {"x": -0.5030999, "y": 0.7471313, "z": 5.111621}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_ba2c4270", "position": {"x": -3.85256, "y": 0.922359467, "z": 5.33607531}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_ab4d2337", "position": {"x": -0.218672842, "y": 1.37179637, "z": 3.97424936}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Egg_ab4d2337", "position": {"x": -1.86012983, "y": 0.9593395, "z": 2.02650762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_52085e6b", "position": {"x": -1.777005, "y": 0.9532003, "z": 1.72449732}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_56566bc4", "position": {"x": -1.86012983, "y": 0.9232217, "z": 1.9510051}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_56566bc4", "position": {"x": -3.807036, "y": 0.07924658, "z": 5.27152729}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_9c9ac9d5", "position": {"x": -0.348728865, "y": 1.12759972, "z": 3.85050058}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Tomato_9c9ac9d5", "position": {"x": -0.565006, "y": 0.982143, "z": 4.615}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_f227e8eb", "position": {"x": -3.201604, "y": 0.07647228, "z": 5.23275757}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_c6eeef1e", "position": {"x": -1.86012983, "y": 0.9198061, "z": 1.72449732}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_ddab876f", "position": {"x": -0.5030999, "y": 0.7431136, "z": 6.33393145}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "PepperShaker_ddab876f", "position": {"x": -4.033492, "y": 0.9198062, "z": 4.381836}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_b10200c3", "position": {"x": -3.49410057, "y": 1.00305462, "z": 5.182719}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_b10200c3", "position": {"x": -0.401855379, "y": 1.15011144, "z": 3.35549927}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Mug_19433bed", "position": {"x": -4.167001, "y": 0.07296121, "z": 4.43100929}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_19433bed", "position": {"x": -2.63562632, "y": 1.54121816, "z": 1.63371325}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_d89eec12", "position": {"x": -0.2424807, "y": 1.12402713, "z": 3.23174882}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Egg_ab4d2337", "position": {"x": -3.85256, "y": 0.95933944, "z": 5.48943138}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_d89eec12", "position": {"x": -4.121773, "y": 0.844427466, "z": 3.34761667}, "rotation": {"x": -1.44077641e-20, "y": 269.999939, "z": -1.40334191e-14}}, {"objectName": "Knife_52085e6b", "position": {"x": -0.411640465, "y": 0.9548003, "z": 5.603}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_9933ef2d", "position": {"x": -0.3423415, "y": 1.36942482, "z": 3.23175}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Fork_1dc44411", "position": {"x": -4.19639969, "y": 0.791505635, "z": 3.28501463}, "rotation": {"x": -1.44077641e-20, "y": 269.999939, "z": -1.40334191e-14}}, {"objectName": "Bowl_f227e8eb", "position": {"x": -0.258274853, "y": 0.922047436, "z": 4.944333}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_53fee662", "position": {"x": -3.34089136, "y": 0.924325943, "z": 1.87549877}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_67328aa7", "position": {"x": -0.3011179, "y": 1.4313854, "z": 3.72674966}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Bread_b10200c3", "position": {"x": -0.301118851, "y": 1.66041148, "z": 3.40980458}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Tomato_9c9ac9d5", "position": {"x": -3.74827051, "y": 0.125750929, "z": 6.41615725}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "WineBottle_eafa1419", "position": {"x": -4.38188171, "y": 0.9244286, "z": 2.8829937}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_ebd9a920", "position": {"x": -2.02637982, "y": 0.938499868, "z": 1.87550259}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_aa4cec24", "position": {"x": -4.26, "y": 1.02778542, "z": 4.640952}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_ba2c4270", "position": {"x": -0.34872964, "y": 1.06941628, "z": 3.603}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Pencil_282a316d", "position": {"x": -3.83045149, "y": 0.745155931, "z": 5.1626}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pan_94f6c891", "position": {"x": -2.45436978, "y": 0.9335794, "z": 2.0230875}, "rotation": {"x": 0.0, "y": 240.000244, "z": 0.0}}, {"objectName": "Pot_04c898c4", "position": {"x": -2.469, "y": 0.9303, "z": 1.712}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SaltShaker_c6eeef1e", "position": {"x": -1.905484, "y": 1.44954264, "z": 1.67120421}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_56566bc4", "position": {"x": -4.37735748, "y": 0.9232217, "z": 1.57348776}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_ddab876f", "position": {"x": -3.47575951, "y": 0.0787394643, "z": 1.889157}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8b572f1d", "position": {"x": -1.69388, "y": 0.9235421, "z": 1.8}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -4.18449736, "y": 0.923064232, "z": 3.86360288}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_8612b3fc", "position": {"x": -1.610755, "y": 0.9246294, "z": 1.6489948}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_19433bed", "position": {"x": -4.12177229, "y": 0.7859571, "z": 3.159811}, "rotation": {"x": -1.44077641e-20, "y": 269.999939, "z": -1.40334191e-14}}], "object_toggles": [], "random_seed": 2978341737, "scene_num": 13}, "task_id": "trial_T20190907_151643_465634", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AJQGWGESKQT4Y_3GM6G9ZBKQEN2QWABCHV1Z67E0RMTU", "high_descs": ["Turn and face the counter to the left of the stove.", "Pick up the egg from the counter.", "Go right and stand to face the microwave.", "Put the egg in the microwave, turn it on, take the egg out, and shut the door.", "Turn and walk to stand in front of the fridge.", "Open the fridge, put the egg on the shelf and shut the door."], "task_desc": "Put a cooked egg in the fridge.", "votes": [1, 1, 1]}, {"assignment_id": "A320QA9HJFUOZO_3X73LLYYQ4V3FZQFNJF60VSJ8W1NHG", "high_descs": ["Turn left and walk up to the counter left of the stove", "Pick up the egg from the counter", "Turn and walk a few feet right then turn left to face the microwave", "Cook the egg in the microwave then remove it", "Turn and walk to the fridge on your left", "Open the fridge  and set the egg inside it"], "task_desc": "Put the cooked egg into the fridge", "votes": [1, 1, 1]}, {"assignment_id": "A143XRCI1YXAFE_3ZQIG0FLQHXYKUUDK9NXBFWJ9QWVWR", "high_descs": ["Turn 90 degrees to the left and walk towards the counter.", "Pick up the potato. ", "Turn 90 degrees to the right and turn towards the stove when you reach it. ", "Open the microwave, place the potato inside, close it, cook the potato, and remove it when it is done cooking. ", "Turn around 180 degrees and cross the room. Turn 90 degrees to the right when you reach the fridge and walk towards it.", "Open the freeze drawer and place the potato inside, closing it after. "], "task_desc": "Take a potato from the count, cook it, finally putting it in the freezer.", "votes": [0, 1, 1]}]}}