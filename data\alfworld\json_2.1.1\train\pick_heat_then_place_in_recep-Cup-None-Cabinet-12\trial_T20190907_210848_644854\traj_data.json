{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000034.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000047.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000048.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000049.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000283.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000284.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000285.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000286.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000287.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000288.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000289.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000290.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000291.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000292.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000293.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000294.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000295.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000296.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000297.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000298.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000299.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000300.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000301.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000302.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000303.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000304.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000305.png", "low_idx": 38}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|4|3|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [6.67940904, 6.67940904, 3.2253824, 3.2253824, 3.745912552, 3.745912552]], "coordinateReceptacleObjectId": ["CounterTop", [6.02, 6.02, 9.24, 9.24, 3.8936, 3.8936]], "forceVisible": true, "objectId": "Cup|+01.67|+00.94|+00.81"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|6|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|1|6|1|-15"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [6.67940904, 6.67940904, 3.2253824, 3.2253824, 3.745912552, 3.745912552]], "coordinateReceptacleObjectId": ["Cabinet", [6.00313236, 6.00313236, 8.32905484, 8.32905484, 8.45383264, 8.45383264]], "forceVisible": true, "objectId": "Cup|+01.67|+00.94|+00.81", "receptacleObjectId": "Cabinet|+01.50|+02.11|+02.08"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+01.67|+00.94|+00.81"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [115, 95, 147, 151], "mask": [[28331, 4], [28627, 11], [28924, 17], [29221, 23], [29518, 29], [29817, 31], [30116, 32], [30416, 32], [30716, 32], [31016, 32], [31315, 33], [31615, 33], [31915, 33], [32215, 33], [32515, 33], [32815, 33], [33115, 33], [33415, 33], [33716, 32], [34016, 32], [34316, 31], [34616, 31], [34917, 30], [35217, 30], [35517, 30], [35817, 30], [36118, 29], [36418, 29], [36718, 29], [37018, 28], [37319, 27], [37619, 27], [37919, 27], [38219, 27], [38520, 26], [38820, 26], [39120, 26], [39421, 24], [39721, 24], [40021, 24], [40321, 24], [40622, 23], [40922, 23], [41222, 23], [41522, 23], [41823, 21], [42123, 21], [42423, 21], [42723, 21], [43024, 20], [43324, 20], [43624, 20], [43924, 20], [44225, 18], [44526, 15], [44829, 10], [45131, 5]], "point": [131, 122]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+01.67|+00.94|+00.81", "placeStationary": true, "receptacleObjectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 114], [7915, 143], [8100, 101], [8207, 7], [8215, 143], [8400, 99], [8516, 142], [8700, 97], [8821, 137], [9000, 96], [9125, 133], [9300, 95], [9427, 131], [9600, 94], [9729, 129], [9900, 93], [10030, 127], [10200, 93], [10331, 126], [10500, 92], [10632, 125], [10800, 92], [10932, 125], [11100, 92], [11233, 124], [11400, 91], [11534, 123], [11700, 91], [11834, 123], [12000, 90], [12134, 123], [12300, 90], [12435, 122], [12600, 91], [12735, 122], [12900, 91], [13035, 121], [13200, 91], [13335, 121], [13500, 91], [13635, 121], [13800, 91], [13936, 120], [14100, 91], [14236, 120], [14400, 91], [14536, 120], [14700, 91], [14835, 121], [15000, 91], [15135, 121], [15300, 92], [15435, 121], [15600, 92], [15734, 122], [15900, 92], [16034, 121], [16200, 91], [16334, 121], [16500, 91], [16634, 121], [16800, 91], [16933, 122], [17100, 91], [17233, 122], [17400, 91], [17533, 122], [17700, 91], [17832, 123], [18000, 92], [18132, 123], [18300, 92], [18431, 124], [18600, 92], [18731, 124], [18900, 92], [19030, 124], [19200, 92], [19330, 124], [19500, 93], [19629, 125], [19800, 93], [19928, 126], [20100, 93], [20228, 126], [20400, 94], [20527, 127], [20700, 94], [20827, 127], [21000, 95], [21126, 128], [21300, 96], [21425, 129], [21600, 97], [21723, 131], [21900, 98], [22022, 131], [22200, 99], [22321, 132], [22500, 100], [22609, 144], [22800, 102], [22907, 146], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [131, 62]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[50, 211], [350, 211], [650, 211], [950, 210], [1250, 210], [1550, 210], [1850, 210], [2150, 210], [2450, 210], [2750, 210], [3050, 210], [3350, 210], [3650, 210], [3950, 209], [4250, 209], [4550, 209], [4800, 1], [4850, 209], [5100, 1], [5150, 209], [5400, 1], [5450, 209], [5700, 1], [5750, 209], [6000, 2], [6050, 209], [6300, 2], [6350, 209], [6600, 2], [6650, 209], [6900, 2], [6950, 208], [7200, 3], [7250, 208], [7500, 3], [7550, 208], [7800, 3], [7850, 64], [7915, 143], [8100, 3], [8150, 51], [8207, 7], [8215, 143], [8400, 4], [8450, 49], [8516, 142], [8700, 4], [8750, 47], [8821, 137], [9000, 4], [9050, 46], [9125, 133], [9300, 4], [9350, 45], [9427, 131], [9600, 5], [9650, 44], [9729, 129], [9900, 5], [9950, 43], [10030, 127], [10200, 5], [10250, 43], [10331, 126], [10500, 5], [10550, 42], [10632, 125], [10800, 5], [10850, 42], [10932, 125], [11100, 6], [11150, 42], [11233, 124], [11400, 6], [11450, 41], [11534, 123], [11700, 6], [11750, 41], [11834, 123], [12000, 6], [12050, 40], [12134, 123], [12300, 7], [12350, 40], [12435, 122], [12600, 7], [12650, 41], [12735, 122], [12900, 7], [12950, 41], [13035, 121], [13200, 7], [13250, 41], [13335, 121], [13500, 8], [13550, 41], [13635, 121], [13800, 8], [13850, 41], [13936, 120], [14100, 8], [14150, 41], [14236, 120], [14400, 8], [14450, 41], [14536, 120], [14700, 9], [14750, 41], [14835, 121], [15000, 9], [15050, 41], [15135, 121], [15300, 9], [15350, 42], [15435, 121], [15600, 9], [15650, 42], [15734, 122], [15900, 10], [15950, 42], [16034, 121], [16200, 10], [16250, 41], [16334, 121], [16500, 10], [16549, 42], [16634, 121], [16800, 10], [16849, 42], [16933, 122], [17100, 11], [17149, 42], [17233, 122], [17400, 11], [17449, 42], [17533, 122], [17700, 11], [17749, 42], [17832, 123], [18000, 11], [18049, 43], [18132, 123], [18300, 11], [18349, 43], [18431, 124], [18600, 12], [18649, 43], [18731, 124], [18900, 12], [18949, 43], [19030, 124], [19200, 12], [19249, 43], [19330, 124], [19500, 12], [19549, 44], [19629, 125], [19800, 13], [19849, 44], [19928, 126], [20100, 13], [20149, 44], [20228, 126], [20400, 13], [20449, 45], [20527, 127], [20700, 13], [20749, 45], [20827, 127], [21000, 14], [21049, 46], [21126, 128], [21300, 21], [21348, 48], [21425, 129], [21600, 97], [21723, 131], [21900, 98], [22022, 131], [22200, 99], [22321, 132], [22500, 100], [22609, 144], [22800, 102], [22907, 146], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [131, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+01.67|+00.94|+00.81"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [0, 1, 49, 72], "mask": [[0, 50], [300, 50], [600, 50], [900, 50], [1200, 50], [1500, 50], [1800, 50], [2100, 50], [2400, 50], [2700, 50], [3000, 50], [3300, 50], [3600, 50], [3900, 50], [4200, 50], [4500, 50], [4801, 49], [5101, 49], [5401, 49], [5701, 49], [6002, 48], [6302, 48], [6602, 48], [6902, 48], [7203, 47], [7503, 47], [7803, 47], [8103, 47], [8404, 46], [8704, 46], [9004, 46], [9304, 46], [9605, 45], [9905, 45], [10205, 45], [10505, 45], [10805, 45], [11106, 44], [11406, 44], [11706, 44], [12006, 44], [12307, 43], [12607, 43], [12907, 43], [13207, 43], [13508, 42], [13808, 42], [14108, 42], [14408, 42], [14709, 41], [15009, 41], [15309, 41], [15609, 41], [15910, 40], [16210, 40], [16510, 39], [16810, 39], [17111, 38], [17411, 38], [17711, 38], [18011, 38], [18311, 38], [18612, 37], [18912, 37], [19212, 37], [19512, 37], [19813, 36], [20113, 36], [20413, 36], [20713, 36], [21014, 35], [21321, 27]], "point": [24, 35]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 114], [7915, 143], [8100, 101], [8207, 7], [8215, 143], [8400, 99], [8516, 142], [8700, 97], [8821, 137], [9000, 96], [9125, 133], [9300, 95], [9427, 131], [9600, 94], [9729, 129], [9900, 93], [10030, 127], [10200, 93], [10331, 126], [10500, 92], [10632, 125], [10800, 92], [10932, 125], [11100, 92], [11233, 124], [11400, 91], [11534, 123], [11700, 91], [11834, 123], [12000, 90], [12134, 123], [12300, 90], [12435, 122], [12600, 91], [12735, 122], [12900, 91], [13035, 121], [13200, 91], [13335, 121], [13500, 91], [13635, 121], [13800, 91], [13936, 120], [14100, 91], [14236, 120], [14400, 91], [14536, 120], [14700, 91], [14835, 121], [15000, 91], [15135, 121], [15300, 92], [15435, 121], [15600, 92], [15734, 122], [15900, 92], [16034, 121], [16200, 91], [16334, 121], [16500, 91], [16634, 121], [16800, 91], [16933, 122], [17100, 91], [17233, 122], [17400, 91], [17533, 122], [17700, 91], [17832, 123], [18000, 92], [18132, 123], [18300, 92], [18431, 124], [18600, 92], [18731, 124], [18900, 92], [19030, 124], [19200, 92], [19330, 124], [19500, 93], [19629, 125], [19800, 93], [19928, 126], [20100, 93], [20228, 126], [20400, 94], [20527, 127], [20700, 94], [20827, 127], [21000, 95], [21126, 128], [21300, 96], [21425, 129], [21600, 97], [21723, 131], [21900, 98], [22022, 131], [22200, 99], [22321, 132], [22500, 100], [22609, 144], [22800, 102], [22907, 146], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [131, 62]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|+01.50|+02.11|+02.08"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [33, 81, 119, 147], "mask": [[24041, 79], [24341, 79], [24641, 79], [24941, 79], [25240, 80], [25540, 80], [25840, 80], [26140, 80], [26440, 80], [26740, 80], [27040, 80], [27340, 80], [27639, 81], [27939, 81], [28239, 81], [28539, 81], [28839, 81], [29139, 81], [29439, 81], [29739, 81], [30038, 81], [30338, 81], [30638, 81], [30938, 81], [31238, 81], [31538, 81], [31838, 81], [32138, 81], [32438, 81], [32737, 82], [33037, 82], [33337, 82], [33637, 82], [33937, 82], [34237, 82], [34537, 82], [34837, 82], [35136, 83], [35436, 83], [35736, 83], [36036, 83], [36336, 83], [36636, 83], [36936, 83], [37236, 83], [37535, 84], [37835, 84], [38135, 84], [38435, 84], [38735, 84], [39035, 83], [39335, 83], [39635, 83], [39934, 84], [40234, 84], [40534, 84], [40834, 84], [41134, 84], [41434, 84], [41734, 84], [42034, 84], [42334, 84], [42633, 84], [42933, 81], [43233, 78], [43533, 75], [43834, 73]], "point": [76, 113]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+01.67|+00.94|+00.81", "placeStationary": true, "receptacleObjectId": "Cabinet|+01.50|+02.11|+02.08"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 27, 114, 146], "mask": [[7800, 1], [8100, 1], [8400, 2], [8700, 3], [9000, 4], [9300, 5], [9600, 5], [9900, 6], [10200, 7], [10500, 8], [10800, 8], [11100, 9], [11400, 10], [11700, 11], [12000, 12], [12300, 12], [12600, 13], [12900, 14], [13200, 15], [13500, 15], [13800, 16], [14100, 17], [14400, 18], [14700, 19], [15000, 19], [15300, 20], [15600, 21], [15900, 22], [16200, 22], [16500, 23], [16800, 24], [17100, 25], [17400, 25], [17700, 26], [18000, 27], [18300, 28], [18600, 29], [18900, 29], [19200, 30], [19500, 31], [19800, 32], [20100, 32], [20400, 33], [20700, 34], [21000, 35], [21300, 36], [21600, 36], [21900, 37], [22200, 38], [22500, 39], [22800, 39], [23100, 40], [23400, 41], [23700, 42], [24000, 42], [24300, 43], [24600, 42], [24900, 42], [24945, 70], [25200, 42], [25245, 70], [25500, 42], [25545, 70], [25800, 42], [25845, 70], [26100, 42], [26145, 70], [26400, 42], [26444, 71], [26700, 42], [26744, 71], [27000, 42], [27044, 71], [27300, 41], [27344, 71], [27600, 41], [27644, 71], [27900, 41], [27944, 71], [28200, 41], [28244, 71], [28500, 41], [28544, 71], [28800, 41], [28843, 72], [29100, 41], [29143, 72], [29400, 41], [29443, 72], [29700, 40], [29743, 71], [30000, 40], [30043, 71], [30300, 40], [30343, 71], [30600, 40], [30643, 71], [30900, 40], [30943, 71], [31200, 40], [31243, 71], [31500, 40], [31542, 72], [31800, 40], [31843, 71], [32100, 40], [32400, 39], [32700, 39], [33000, 39], [33042, 72], [33300, 39], [33342, 72], [33600, 39], [33642, 72], [33900, 39], [33941, 73], [34200, 39], [34241, 73], [34500, 39], [34541, 73], [34800, 38], [34841, 73], [35100, 38], [35141, 73], [35400, 38], [35441, 73], [35700, 38], [35741, 73], [36000, 38], [36041, 73], [36300, 38], [36341, 73], [36600, 38], [36640, 74], [36900, 38], [36940, 74], [37200, 37], [37240, 74], [37500, 37], [37540, 73], [37800, 37], [37840, 73], [38101, 36], [38140, 73], [38403, 34], [38440, 73], [38704, 33], [38740, 73], [39006, 31], [39040, 73], [39308, 29], [39339, 74], [39609, 28], [39639, 74], [39911, 25], [39939, 74], [40213, 23], [40239, 74], [40514, 22], [40539, 74], [40816, 20], [40839, 74], [41117, 19], [41139, 74], [41419, 17], [41439, 74], [41721, 15], [41738, 75], [42022, 14], [42038, 75], [42324, 11], [42338, 75], [42625, 10], [42638, 75], [42927, 8], [42938, 75], [43229, 6], [43238, 73], [43530, 5]], "point": [57, 85]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|+01.50|+02.11|+02.08"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 27, 114, 146], "mask": [[7800, 1], [8100, 1], [8400, 2], [8700, 3], [9000, 4], [9300, 5], [9600, 5], [9900, 6], [10200, 7], [10500, 8], [10800, 8], [11100, 9], [11400, 10], [11700, 11], [12000, 12], [12300, 12], [12600, 13], [12900, 14], [13200, 15], [13500, 15], [13800, 16], [14100, 17], [14400, 18], [14700, 19], [15000, 19], [15300, 20], [15600, 21], [15900, 22], [16200, 22], [16500, 23], [16800, 24], [17100, 25], [17400, 25], [17700, 26], [18000, 27], [18300, 28], [18600, 29], [18900, 29], [19200, 30], [19500, 31], [19800, 32], [20100, 32], [20400, 33], [20700, 34], [21000, 35], [21300, 36], [21600, 36], [21900, 37], [22200, 38], [22500, 39], [22800, 39], [23100, 40], [23400, 41], [23700, 42], [24000, 42], [24300, 43], [24600, 42], [24900, 42], [24945, 70], [25200, 42], [25245, 70], [25500, 42], [25545, 70], [25800, 42], [25845, 70], [26100, 42], [26145, 70], [26400, 42], [26444, 71], [26700, 42], [26744, 71], [27000, 42], [27044, 71], [27300, 41], [27344, 71], [27600, 41], [27644, 71], [27900, 41], [27944, 71], [28200, 41], [28244, 71], [28500, 41], [28544, 71], [28800, 41], [28843, 72], [29100, 41], [29143, 72], [29400, 41], [29443, 72], [29700, 40], [29743, 71], [30000, 40], [30043, 71], [30300, 40], [30343, 71], [30600, 40], [30643, 71], [30900, 40], [30943, 71], [31200, 40], [31243, 71], [31500, 40], [31542, 72], [31800, 40], [31843, 71], [32100, 40], [32400, 39], [32700, 39], [33000, 39], [33042, 72], [33300, 39], [33342, 72], [33600, 39], [33642, 72], [33900, 39], [33941, 73], [34200, 39], [34241, 52], [34301, 13], [34500, 39], [34541, 48], [34606, 8], [34800, 38], [34841, 47], [34907, 7], [35100, 38], [35141, 47], [35208, 6], [35400, 38], [35441, 47], [35508, 6], [35700, 38], [35741, 47], [35808, 6], [36000, 38], [36041, 47], [36108, 6], [36300, 38], [36341, 48], [36408, 6], [36600, 38], [36640, 49], [36708, 6], [36900, 38], [36940, 49], [37008, 6], [37200, 37], [37240, 49], [37308, 6], [37500, 37], [37540, 49], [37607, 6], [37800, 37], [37840, 49], [37907, 6], [38101, 36], [38140, 49], [38207, 6], [38403, 34], [38440, 49], [38507, 6], [38704, 33], [38740, 49], [38807, 6], [39006, 31], [39040, 49], [39107, 6], [39308, 29], [39339, 50], [39406, 7], [39609, 28], [39639, 50], [39706, 7], [39911, 25], [39939, 50], [40006, 7], [40213, 23], [40239, 50], [40306, 7], [40514, 22], [40539, 50], [40606, 7], [40816, 20], [40839, 50], [40906, 7], [41117, 19], [41139, 50], [41205, 8], [41419, 17], [41439, 50], [41505, 8], [41721, 15], [41738, 52], [41805, 8], [42022, 14], [42038, 52], [42105, 8], [42324, 11], [42338, 52], [42405, 8], [42625, 10], [42638, 52], [42705, 8], [42927, 8], [42938, 52], [43004, 9], [43229, 6], [43238, 52], [43304, 9], [43530, 5]], "point": [57, 85]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan12", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": 0.75, "y": 0.9009999, "z": -0.25}, "object_poses": [{"objectName": "Potato_22312ae0", "position": {"x": -0.8603946, "y": 0.598476648, "z": -0.1109537}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": -1.043155, "y": 0.9390294, "z": 0.921}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": -0.689003944, "y": 0.7479664, "z": 2.44906855}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -1.109, "y": 0.9307289, "z": 1.4704}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": 1.55062962, "y": 0.9798608, "z": 1.7985332}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": 1.58757079, "y": 1.6316359, "z": 1.59899759}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": 1.66985226, "y": 0.936478138, "z": 0.8063456}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": -1.0011425, "y": 1.499687, "z": 2.58348751}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": 1.346072, "y": 0.0995715857, "z": -0.131285489}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": 1.66628313, "y": 1.50859654, "z": 0.9651258}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": -0.96003, "y": 0.9598927, "z": 1.15406442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": 1.4304558, "y": 0.07861984, "z": 2.532645}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": -1.03728545, "y": 1.94630635, "z": 1.877312}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": -1.043155, "y": 0.9342062, "z": 0.765623748}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -1.209405, "y": 0.9342062, "z": 0.9986881}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -0.712255, "y": 0.9342062, "z": 2.462146}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_f3700d17", "position": {"x": 1.46078944, "y": 1.01439834, "z": 1.677775}, "rotation": {"x": 5.162539, "y": 59.5351868, "z": 356.178833}}, {"objectName": "SoapBottle_09f78d94", "position": {"x": 1.43918228, "y": 0.9372058, "z": 1.07423937}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_20fd7fed", "position": {"x": -0.9230002, "y": 0.990676045, "z": 2.26549983}, "rotation": {"x": 4.161468e-05, "y": 302.470367, "z": 6.4569067e-06}}, {"objectName": "Fork_d40bfead", "position": {"x": -0.689004064, "y": 0.747115731, "z": 0.847031236}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -1.209405, "y": 0.999118, "z": 0.765623748}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": 1.352982, "y": 0.07861984, "z": 0.848087549}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -1.109, "y": 0.9307289, "z": 1.8657}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_733b0828", "position": {"x": 1.34493542, "y": 0.761567056, "z": 2.5278933}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": -0.8965961, "y": 0.7685599, "z": 1.05093741}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": -0.7582013, "y": 0.7431432, "z": 0.9829687}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": 1.68450284, "y": 1.64570308, "z": 1.59899735}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": 1.44873142, "y": 0.7428734, "z": 2.323987}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": -1.12628, "y": 0.9379421, "z": 0.6879356}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_22312ae0", "position": {"x": -1.06534982, "y": 1.35187662, "z": 0.19565101}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": -0.804073453, "y": 0.0866650939, "z": 2.38502026}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": 1.66985226, "y": 0.9374642, "z": 1.00726593}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": 1.45694256, "y": 0.105368823, "z": 2.84730816}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": -1.12788, "y": 0.9364782, "z": 2.3864}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_9f7abbea", "position": {"x": 1.3781, "y": 1.73753989, "z": 0.3929}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": 1.41413271, "y": 0.7479664, "z": 0.9845687}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": 1.52899969, "y": 1.00298607, "z": 2.34560037}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}], "object_toggles": [], "random_seed": 3870115430, "scene_num": 12}, "task_id": "trial_T20190907_210848_644854", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2A028LRDJB7ZB_34YB12FSQ15JXLA4RCIO2GRIOKQGME", "high_descs": ["Move forward to the sink counter on your right", "Pick up the glass on the counter", "Turn to your left move few steps and face the microwave", "Open the microwave put in and out the glass then close the door", "Turn around to your left, facing the cabinet above the microwave", "Open the left side of the cabinet above the microwave then put inside the glass and close the cabinet"], "task_desc": "Put the warm glass in the cabinet above the microwave", "votes": [1, 1]}, {"assignment_id": "A1WJU1IQ3UTRC6_3E1QT0TDFSQI8NEVNZ88IA33JTC8IV", "high_descs": ["Walk to the sink.", "Pick up the glass on the left side of the sink.", "Look up at the microwave.", "Open the microwave, heat the glass and then take the glass out.", "Turn around and go back to the sink.", "Put the glass away in the cupboard above the microwave."], "task_desc": "Heat the glass and put it away in the cupboard.", "votes": [1, 1]}, {"assignment_id": "AJQGWGESKQT4Y_3FIUS151DYJJ5BEIG9KTPB7LSZMGGG", "high_descs": ["Turn to the right and face the counter to the right of the sink.", "Pick the glass up from the counter.", "Move to the left and face the microwave.", "Put the glass in the microwave and turn it on and then open it and take the glass out.", "Back up and look up to the cabinets above the microwave.", "Put the glass in the cabinet on the left above the microwave and then shut the door."], "task_desc": "Put a warm glass in the cabinet.", "votes": [1, 1]}]}}