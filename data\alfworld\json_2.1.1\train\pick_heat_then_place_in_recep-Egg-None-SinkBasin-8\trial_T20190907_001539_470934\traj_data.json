{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000264.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000265.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000266.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000267.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000268.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000269.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000270.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000271.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000272.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000273.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000274.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000275.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000276.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000277.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000278.png", "low_idx": 49}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "SinkBasin", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|2|-4|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [5.04038, 5.04038, -5.32683752, -5.32683752, 3.7625164, 3.7625164]], "coordinateReceptacleObjectId": ["CounterTop", [4.66, 4.66, -2.6, -2.6, 3.7872, 3.7872]], "forceVisible": true, "objectId": "Egg|+01.26|+00.94|-01.33"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|2|0|1|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [5.688, 5.688, 0.092, 0.092, 4.6, 4.6]], "forceVisible": true, "objectId": "Microwave|+01.42|+01.15|+00.02"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-1|-4|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "sinkbasin"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [5.04038, 5.04038, -5.32683752, -5.32683752, 3.7625164, 3.7625164]], "coordinateReceptacleObjectId": ["SinkBasin", [-0.25512844, -0.25512844, -6.85248328, -6.85248328, 3.0634928, 3.0634928]], "forceVisible": true, "objectId": "Egg|+01.26|+00.94|-01.33", "receptacleObjectId": "Sink|+00.16|+00.82|-01.80|SinkBasin"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+01.26|+00.94|-01.33"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [216, 125, 232, 137], "mask": [[37422, 6], [37719, 11], [38018, 13], [38317, 15], [38616, 17], [38916, 17], [39216, 17], [39516, 17], [39816, 17], [40117, 15], [40418, 13], [40720, 10], [41023, 4]], "point": [224, 130]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+01.26|+00.94|-01.33", "placeStationary": true, "receptacleObjectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 24, 252, 209], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9622, 226], [9918, 233], [10214, 239], [10510, 243], [10806, 247], [11102, 251], [11400, 253], [11700, 252], [12000, 252], [12300, 252], [12600, 252], [12900, 252], [13200, 251], [13500, 251], [13800, 251], [14100, 251], [14400, 251], [14700, 251], [15000, 250], [15300, 250], [15600, 250], [15900, 250], [16200, 250], [16500, 249], [16800, 249], [17100, 249], [17400, 249], [17700, 249], [18000, 248], [18300, 248], [18600, 248], [18900, 248], [19200, 248], [19500, 248], [19800, 247], [20100, 247], [20400, 247], [20700, 247], [21000, 247], [21300, 246], [21600, 246], [21900, 93], [21994, 152], [22200, 93], [22294, 152], [22500, 88], [22599, 147], [22800, 85], [22903, 143], [23100, 84], [23204, 141], [23400, 84], [23505, 140], [23700, 83], [23806, 139], [24000, 82], [24107, 138], [24300, 81], [24408, 137], [24600, 81], [24708, 136], [24900, 81], [25009, 135], [25200, 81], [25309, 135], [25500, 80], [25610, 134], [25800, 80], [25910, 134], [26100, 80], [26210, 134], [26400, 80], [26510, 133], [26700, 80], [26810, 133], [27000, 80], [27110, 133], [27300, 81], [27410, 133], [27600, 81], [27710, 133], [27900, 81], [28009, 133], [28200, 81], [28309, 133], [28500, 82], [28609, 133], [28800, 82], [28908, 134], [29100, 83], [29208, 134], [29400, 83], [29508, 134], [29700, 84], [29807, 134], [30000, 85], [30107, 134], [30300, 85], [30406, 135], [30600, 86], [30705, 136], [30900, 89], [31003, 138], [31200, 91], [31301, 139], [31500, 240], [31800, 240], [32100, 240], [32400, 240], [32700, 240], [33000, 239], [33300, 239], [33600, 239], [33900, 239], [34200, 239], [34500, 238], [34800, 238], [35100, 238], [35400, 238], [35700, 238], [36000, 238], [36300, 237], [36600, 237], [36900, 237], [37200, 237], [37500, 237], [37800, 236], [38100, 236], [38400, 236], [38700, 236], [39000, 236], [39300, 236], [39600, 55], [39677, 6], [39798, 37], [39900, 54], [39977, 4], [40098, 37], [40200, 54], [40399, 36], [40500, 53], [40800, 52], [41100, 52], [41400, 51], [41700, 50], [42000, 49], [42300, 49], [42600, 48], [42900, 47], [43200, 47], [43500, 46], [43800, 45], [44100, 44], [44400, 44], [44700, 43], [45000, 42], [45300, 41], [45600, 41], [45900, 40], [46200, 39], [46500, 39], [46800, 38], [47100, 37], [47400, 36], [47700, 36], [48000, 35], [48300, 34], [48600, 34], [48900, 33], [49200, 32], [49500, 31], [49800, 31], [50100, 30], [50400, 29], [50700, 29], [51000, 28], [51300, 27], [51600, 26], [51900, 26], [52200, 25], [52500, 24], [52800, 24], [53100, 23], [53400, 22], [53700, 21], [54000, 21], [54300, 20], [54600, 19], [54900, 19], [55200, 18], [55500, 17], [55800, 16], [56100, 16], [56400, 15], [56700, 14], [57000, 14], [57300, 13], [57600, 12], [57900, 11], [58200, 11], [58500, 10], [58800, 9], [59100, 9], [59400, 8], [59700, 7], [60000, 6], [60300, 6], [60600, 5], [60900, 4], [61200, 4], [61500, 3], [61800, 2], [62100, 1], [62400, 1]], "point": [126, 115]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 24, 252, 209], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9622, 226], [9918, 233], [10214, 239], [10510, 243], [10806, 247], [11102, 251], [11400, 253], [11700, 252], [12000, 252], [12300, 252], [12600, 252], [12900, 252], [13200, 251], [13500, 251], [13800, 251], [14100, 251], [14400, 251], [14700, 251], [15000, 250], [15300, 250], [15600, 250], [15900, 250], [16200, 250], [16500, 249], [16800, 249], [17100, 249], [17400, 249], [17700, 249], [18000, 248], [18300, 248], [18600, 248], [18900, 248], [19200, 248], [19500, 248], [19800, 247], [20100, 247], [20400, 247], [20700, 247], [21000, 247], [21300, 246], [21600, 246], [21900, 93], [21994, 152], [22200, 93], [22294, 152], [22500, 88], [22599, 147], [22800, 85], [22903, 143], [23100, 84], [23204, 141], [23400, 84], [23505, 140], [23700, 83], [23806, 139], [24000, 82], [24107, 138], [24300, 81], [24408, 137], [24600, 81], [24708, 136], [24900, 81], [25009, 135], [25200, 81], [25309, 135], [25500, 80], [25610, 134], [25800, 80], [25910, 134], [26100, 80], [26210, 44], [26259, 85], [26400, 80], [26510, 43], [26560, 83], [26700, 80], [26810, 42], [26861, 82], [27000, 80], [27110, 41], [27162, 81], [27300, 81], [27410, 41], [27462, 81], [27600, 81], [27710, 40], [27763, 80], [27900, 81], [28009, 41], [28063, 79], [28200, 81], [28309, 40], [28363, 79], [28500, 82], [28609, 40], [28663, 79], [28800, 82], [28908, 41], [28963, 79], [29100, 83], [29208, 41], [29264, 78], [29400, 83], [29508, 41], [29563, 79], [29700, 84], [29807, 42], [29863, 78], [30000, 85], [30107, 43], [30163, 78], [30300, 85], [30406, 44], [30463, 78], [30600, 86], [30705, 45], [30762, 79], [30900, 89], [31003, 48], [31062, 79], [31200, 91], [31301, 51], [31361, 79], [31500, 153], [31659, 81], [31800, 240], [32100, 240], [32400, 240], [32700, 240], [33000, 239], [33300, 239], [33600, 239], [33900, 239], [34200, 239], [34500, 238], [34800, 238], [35100, 238], [35400, 238], [35700, 238], [36000, 238], [36300, 237], [36600, 237], [36900, 237], [37200, 237], [37500, 237], [37800, 236], [38100, 236], [38400, 236], [38700, 236], [39000, 236], [39300, 236], [39600, 55], [39677, 6], [39798, 37], [39900, 54], [39977, 4], [40098, 37], [40200, 54], [40399, 36], [40500, 53], [40800, 52], [41100, 52], [41400, 51], [41700, 50], [42000, 49], [42300, 49], [42600, 48], [42900, 47], [43200, 47], [43500, 46], [43800, 45], [44100, 44], [44400, 44], [44700, 43], [45000, 42], [45300, 41], [45600, 41], [45900, 40], [46200, 39], [46500, 39], [46800, 38], [47100, 37], [47400, 36], [47700, 36], [48000, 35], [48300, 34], [48600, 34], [48900, 33], [49200, 32], [49500, 31], [49800, 31], [50100, 30], [50400, 29], [50700, 29], [51000, 28], [51300, 27], [51600, 26], [51900, 26], [52200, 25], [52500, 24], [52800, 24], [53100, 23], [53400, 22], [53700, 21], [54000, 21], [54300, 20], [54600, 19], [54900, 19], [55200, 18], [55500, 17], [55800, 16], [56100, 16], [56400, 15], [56700, 14], [57000, 14], [57300, 13], [57600, 12], [57900, 11], [58200, 11], [58500, 10], [58800, 9], [59100, 9], [59400, 8], [59700, 7], [60000, 6], [60300, 6], [60600, 5], [60900, 4], [61200, 4], [61500, 3], [61800, 2], [62100, 1], [62400, 1]], "point": [126, 115]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [5.688, 5.688, 0.092, 0.092, 4.6, 4.6]], "forceVisible": true, "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [5.688, 5.688, 0.092, 0.092, 4.6, 4.6]], "forceVisible": true, "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+01.26|+00.94|-01.33"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [149, 88, 163, 106], "mask": [[26254, 5], [26553, 7], [26852, 9], [27151, 11], [27451, 11], [27750, 13], [28050, 13], [28349, 14], [28649, 14], [28949, 14], [29249, 15], [29549, 14], [29849, 14], [30150, 13], [30450, 13], [30750, 12], [31051, 11], [31352, 9], [31653, 6]], "point": [156, 96]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 24, 252, 209], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9622, 226], [9918, 233], [10214, 239], [10510, 243], [10806, 247], [11102, 251], [11400, 253], [11700, 252], [12000, 252], [12300, 252], [12600, 252], [12900, 252], [13200, 251], [13500, 251], [13800, 251], [14100, 251], [14400, 251], [14700, 251], [15000, 250], [15300, 250], [15600, 250], [15900, 250], [16200, 250], [16500, 249], [16800, 249], [17100, 249], [17400, 249], [17700, 249], [18000, 248], [18300, 248], [18600, 248], [18900, 248], [19200, 248], [19500, 248], [19800, 247], [20100, 247], [20400, 247], [20700, 247], [21000, 247], [21300, 246], [21600, 246], [21900, 93], [21994, 152], [22200, 93], [22294, 152], [22500, 88], [22599, 147], [22800, 85], [22903, 143], [23100, 84], [23204, 141], [23400, 84], [23505, 140], [23700, 83], [23806, 139], [24000, 82], [24107, 138], [24300, 81], [24408, 137], [24600, 81], [24708, 136], [24900, 81], [25009, 135], [25200, 81], [25309, 135], [25500, 80], [25610, 134], [25800, 80], [25910, 134], [26100, 80], [26210, 134], [26400, 80], [26510, 133], [26700, 80], [26810, 133], [27000, 80], [27110, 133], [27300, 81], [27410, 133], [27600, 81], [27710, 133], [27900, 81], [28009, 133], [28200, 81], [28309, 133], [28500, 82], [28609, 133], [28800, 82], [28908, 134], [29100, 83], [29208, 134], [29400, 83], [29508, 134], [29700, 84], [29807, 134], [30000, 85], [30107, 134], [30300, 85], [30406, 135], [30600, 86], [30705, 136], [30900, 89], [31003, 138], [31200, 91], [31301, 139], [31500, 240], [31800, 240], [32100, 240], [32400, 240], [32700, 240], [33000, 239], [33300, 239], [33600, 239], [33900, 239], [34200, 239], [34500, 238], [34800, 238], [35100, 238], [35400, 238], [35700, 238], [36000, 238], [36300, 237], [36600, 237], [36900, 237], [37200, 237], [37500, 237], [37800, 236], [38100, 236], [38400, 236], [38700, 236], [39000, 236], [39300, 236], [39600, 55], [39677, 6], [39798, 37], [39900, 54], [39977, 4], [40098, 37], [40200, 54], [40399, 36], [40500, 53], [40800, 52], [41100, 52], [41400, 51], [41700, 50], [42000, 49], [42300, 49], [42600, 48], [42900, 47], [43200, 47], [43500, 46], [43800, 45], [44100, 44], [44400, 44], [44700, 43], [45000, 42], [45300, 41], [45600, 41], [45900, 40], [46200, 39], [46500, 39], [46800, 38], [47100, 37], [47400, 36], [47700, 36], [48000, 35], [48300, 34], [48600, 34], [48900, 33], [49200, 32], [49500, 31], [49800, 31], [50100, 30], [50400, 29], [50700, 29], [51000, 28], [51300, 27], [51600, 26], [51900, 26], [52200, 25], [52500, 24], [52800, 24], [53100, 23], [53400, 22], [53700, 21], [54000, 21], [54300, 20], [54600, 19], [54900, 19], [55200, 18], [55500, 17], [55800, 16], [56100, 16], [56400, 15], [56700, 14], [57000, 14], [57300, 13], [57600, 12], [57900, 11], [58200, 11], [58500, 10], [58800, 9], [59100, 9], [59400, 8], [59700, 7], [60000, 6], [60300, 6], [60600, 5], [60900, 4], [61200, 4], [61500, 3], [61800, 2], [62100, 1], [62400, 1]], "point": [126, 115]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+01.26|+00.94|-01.33", "placeStationary": true, "receptacleObjectId": "Sink|+00.16|+00.82|-01.80|SinkBasin"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [74, 140, 132, 185], "mask": [[41791, 37], [42089, 41], [42387, 44], [42686, 45], [42985, 47], [43285, 47], [43584, 49], [43884, 49], [44183, 50], [44483, 50], [44783, 50], [45082, 51], [45382, 51], [45682, 51], [45982, 51], [46281, 52], [46581, 51], [46881, 51], [47181, 51], [47480, 52], [47780, 52], [48080, 52], [48380, 52], [48679, 53], [48979, 53], [49279, 53], [49579, 53], [49878, 54], [50178, 54], [50478, 54], [50777, 55], [51077, 54], [51377, 54], [51677, 54], [51976, 55], [52276, 55], [52576, 55], [52876, 55], [53175, 55], [53475, 54], [53775, 53], [54075, 52], [54374, 52], [54674, 50], [54974, 48], [55274, 44]], "point": [103, 161]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan8", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -0.75, "y": 0.9009992, "z": 2.25}, "object_poses": [{"objectName": "Pan_c0048524", "position": {"x": 1.260095, "y": 0.9109095, "z": -0.65}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_abf5c390", "position": {"x": -2.19359946, "y": 0.909904063, "z": -1.707}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_73e168e3", "position": {"x": 0.585, "y": 0.776087761, "z": -1.5337}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_1cf505ed", "position": {"x": 1.37599969, "y": 1.40926373, "z": 1.97600007}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Potato_1cf505ed", "position": {"x": 1.30206108, "y": 0.256335258, "z": 1.10898018}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_238538e2", "position": {"x": -0.5309839, "y": 1.69993734, "z": -1.839}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_e75d4607", "position": {"x": 0.171171919, "y": 0.115650535, "z": -1.63128853}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Kettle_e75d4607", "position": {"x": 1.20862532, "y": 0.116250515, "z": -1.01392746}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_b7024f32", "position": {"x": 0.946677566, "y": 0.9108642, "z": -1.44882727}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_b308514b", "position": {"x": -2.03981829, "y": 0.9112167, "z": -1.53450024}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_7aaf3771", "position": {"x": 0.5698826, "y": 0.9118485, "z": -1.44882727}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_7aaf3771", "position": {"x": 1.015, "y": 0.7754654, "z": -1.013817}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_6f7c42a7", "position": {"x": 0.9161422, "y": 0.7760463, "z": -1.23358309}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_1d30af71", "position": {"x": 1.42633355, "y": 0.907578, "z": -0.3092672}, "rotation": {"x": 359.509521, "y": 359.992737, "z": 0.05324055}}, {"objectName": "Spatula_1d30af71", "position": {"x": -1.835049, "y": 0.925499856, "z": 0.3772499}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Cup_bfcb0336", "position": {"x": 1.15081286, "y": 0.115688086, "z": -1.30160868}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_bfcb0336", "position": {"x": 1.27506208, "y": 1.36794627, "z": 1.97600031}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Plate_9b9c5535", "position": {"x": 1.32553172, "y": 1.36813617, "z": 2.22600031}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SoapBottle_c69c5340", "position": {"x": -2.1076, "y": 0.111667871, "z": -1.63276732}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_c69c5340", "position": {"x": -1.39602685, "y": 1.85820985, "z": -1.802726}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_441a8bda", "position": {"x": 1.45449877, "y": 1.20231771, "z": -1.34564734}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_a0ece05a", "position": {"x": -2.32376337, "y": 0.911050856, "z": 0.3772499}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_a2768593", "position": {"x": 1.34259534, "y": 1.65106428, "z": -1.34299994}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_49b86ad0", "position": {"x": 1.388463, "y": 1.71522653, "z": 1.77583551}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Mug_49b86ad0", "position": {"x": 1.07070017, "y": 0.9575, "z": -1.64179993}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_441a8bda", "position": {"x": 1.494119, "y": 1.20231771, "z": -1.68895817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_b308514b", "position": {"x": 1.0923003, "y": 0.1185776, "z": 0.489357054}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_af290bda", "position": {"x": 0.758280039, "y": 0.9410002, "z": -1.87489522}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_9b9c5535", "position": {"x": -1.95722759, "y": 0.911168754, "z": 0.291}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_7aaf3771", "position": {"x": -1.8091464, "y": 0.911048532, "z": -1.707}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_238538e2", "position": {"x": 1.46140385, "y": 1.7004267, "z": -0.579093754}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_1cf505ed", "position": {"x": 1.332814, "y": 1.859023, "z": 2.494797}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Lettuce_0f08525d", "position": {"x": 1.4264679, "y": 1.43663275, "z": 1.72599983}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Kettle_e75d4607", "position": {"x": -1.431, "y": 0.949799955, "z": -1.81}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_73e168e3", "position": {"x": 1.06956029, "y": 0.7776274, "z": -0.7328684}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SaltShaker_a2768593", "position": {"x": -0.619845748, "y": 0.106764436, "z": -1.63276732}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_bfcb0336", "position": {"x": 0.970767558, "y": 1.65605378, "z": -1.839}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_e08d0eeb", "position": {"x": 1.260095, "y": 0.9406291, "z": -1.33170938}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_1d30af71", "position": {"x": 0.936764359, "y": 0.7884345, "z": 0.377106577}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_a0ece05a", "position": {"x": 1.09030032, "y": 0.115475655, "z": -0.7362524}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_abf5c390", "position": {"x": -0.7033658, "y": 0.910704, "z": -1.44882691}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_c0048524", "position": {"x": -1.0811, "y": 0.949799955, "z": -1.81}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_c69c5340", "position": {"x": 0.285940379, "y": 0.919453442, "z": -1.75846994}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pot_316d55f0", "position": {"x": 0.9673953, "y": 0.9, "z": 0.249277115}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_7cca838f", "position": {"x": -0.682, "y": 0.9, "z": -1.71}, "rotation": {"x": 0.0, "y": 14.9999714, "z": 0.0}}, {"objectName": "PepperShaker_1de14ecd", "position": {"x": -0.133226439, "y": 0.109609306, "z": -1.44093776}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_b7024f32", "position": {"x": 0.528749943, "y": 0.772999167, "z": -1.50070083}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_9b638b9f", "position": {"x": 1.1642772, "y": 0.9752163, "z": -0.158352479}, "rotation": {"x": 0.238877922, "y": 299.914551, "z": 359.150421}}, {"objectName": "Apple_b66a32b5", "position": {"x": 1.37660575, "y": 1.30013561, "z": 0.188764244}, "rotation": {"x": 0.0, "y": 270.000153, "z": 0.0}}, {"objectName": "Spoon_6f7c42a7", "position": {"x": -2.32376337, "y": 0.9116294, "z": 0.204750121}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_49b86ad0", "position": {"x": 0.14961265, "y": 0.9145499, "z": -1.65757918}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3698076712, "scene_num": 8}, "task_id": "trial_T20190907_001539_470934", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AKW57KYG90X61_35DR22AR5G1TXLXKA4DDU3I7QKE3XN", "high_descs": ["turn around and move towards the gas cooker and then left towards the cupboard.", "pickup the egg on top of the cupboard", "turn left and move towards the microwave oven", "put the egg in the microwave oven and boil it for five minutes", "take out the egg and turn right towards the sink", "drop the egg in the sink"], "task_desc": "drop a cooked egg in the sink", "votes": [1, 1]}, {"assignment_id": "A1MKCTVKE7J0ZP_3A1COHJ8NMCICS3BHHQNIY2OIE7H8G", "high_descs": ["Turn around and go to the counter to the left of the sink.", "Pick up the egg on the counter, behind the sponge.", "Turn to your left and go to the microwave.", "Cook the egg in the microwave.", "Turn around and go left, to the sink.", "Place the egg in the right bottom corner of the sink."], "task_desc": "Put a cooked egg in the sink.", "votes": [1, 1]}, {"assignment_id": "A1GVTH5YS3WOK0_3UNH76FOCVMJJCV4LI4U9GYMSIPMYU", "high_descs": ["Turn around, head towards the stove, turn left towards the end of the counter.", "Pick up the egg from the counter.", "Turn left and walk towards the microwave on your left.", "Open the microwave, place the egg in it, heat it up, and take the egg out of microwave.", "Turn right and head towards the sink.", "Place the egg in the sink."], "task_desc": "Place a warm egg in the sink.", "votes": [1, 1]}]}}