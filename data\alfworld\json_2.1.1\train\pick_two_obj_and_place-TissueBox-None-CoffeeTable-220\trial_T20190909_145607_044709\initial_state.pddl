
(define (problem plan_trial_T20190909_145607_044709)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Boots_bar__minus_00_dot_44_bar__plus_00_dot_01_bar__minus_00_dot_55 - object
        Box_bar__minus_00_dot_25_bar__plus_01_dot_50_bar__plus_03_dot_28 - object
        Candle_bar__minus_00_dot_08_bar__plus_01_dot_31_bar__plus_04_dot_11 - object
        Chair_bar__minus_01_dot_35_bar__plus_00_dot_02_bar__plus_04_dot_24 - object
        CreditCard_bar__minus_00_dot_13_bar__plus_01_dot_31_bar__plus_03_dot_44 - object
        CreditCard_bar__minus_02_dot_66_bar__plus_00_dot_47_bar__plus_01_dot_35 - object
        FloorLamp_bar__minus_04_dot_10_bar__plus_00_dot_01_bar__minus_00_dot_47 - object
        HousePlant_bar__minus_00_dot_29_bar__plus_00_dot_83_bar__plus_00_dot_30 - object
        KeyChain_bar__minus_02_dot_13_bar__plus_00_dot_40_bar__plus_04_dot_12 - object
        KeyChain_bar__minus_02_dot_51_bar__plus_00_dot_47_bar__plus_01_dot_10 - object
        Laptop_bar__minus_02_dot_01_bar__plus_00_dot_45_bar__minus_00_dot_09 - object
        LightSwitch_bar__minus_06_dot_00_bar__plus_01_dot_38_bar__plus_02_dot_88 - object
        Painting_bar__minus_00_dot_05_bar__plus_01_dot_91_bar__plus_03_dot_69 - object
        Painting_bar__minus_05_dot_35_bar__plus_01_dot_99_bar__minus_00_dot_69 - object
        Pillow_bar__minus_03_dot_71_bar__plus_00_dot_64_bar__plus_03_dot_75 - object
        RemoteControl_bar__plus_00_dot_12_bar__plus_00_dot_87_bar__plus_01_dot_85 - object
        RemoteControl_bar__minus_00_dot_41_bar__plus_01_dot_31_bar__plus_03_dot_78 - object
        Statue_bar__minus_02_dot_36_bar__plus_00_dot_48_bar__plus_01_dot_04 - object
        Television_bar__plus_00_dot_23_bar__plus_01_dot_53_bar__plus_02_dot_00 - object
        TissueBox_bar__minus_00_dot_16_bar__plus_00_dot_84_bar__plus_00_dot_32 - object
        TissueBox_bar__minus_00_dot_30_bar__plus_01_dot_31_bar__plus_03_dot_44 - object
        Window_bar__minus_00_dot_97_bar__plus_01_dot_56_bar__minus_00_dot_83 - object
        Window_bar__minus_02_dot_55_bar__plus_01_dot_56_bar__minus_00_dot_83 - object
        Window_bar__minus_04_dot_13_bar__plus_01_dot_56_bar__minus_00_dot_83 - object
        ArmChair_bar__minus_02_dot_06_bar_00_dot_00_bar__plus_04_dot_22 - receptacle
        CoffeeTable_bar__minus_02_dot_65_bar_00_dot_00_bar__plus_01_dot_15 - receptacle
        DiningTable_bar__plus_00_dot_19_bar__plus_00_dot_72_bar__plus_02_dot_00 - receptacle
        DiningTable_bar__plus_00_dot_19_bar__plus_00_dot_91_bar__plus_02_dot_00 - receptacle
        Drawer_bar__minus_00_dot_37_bar__plus_00_dot_21_bar__plus_00_dot_52 - receptacle
        Drawer_bar__minus_00_dot_37_bar__plus_00_dot_57_bar__plus_00_dot_52 - receptacle
        Drawer_bar__minus_00_dot_38_bar__plus_00_dot_18_bar__plus_03_dot_61 - receptacle
        Drawer_bar__minus_00_dot_38_bar__plus_00_dot_48_bar__plus_03_dot_61 - receptacle
        Drawer_bar__minus_00_dot_38_bar__plus_00_dot_79_bar__plus_03_dot_61 - receptacle
        Drawer_bar__minus_00_dot_38_bar__plus_01_dot_07_bar__plus_03_dot_61 - receptacle
        Dresser_bar__minus_00_dot_27_bar__plus_00_dot_00_bar__plus_03_dot_61 - receptacle
        GarbageCan_bar__minus_05_dot_17_bar__plus_00_dot_01_bar__minus_00_dot_41 - receptacle
        SideTable_bar__minus_00_dot_28_bar__plus_00_dot_01_bar__plus_00_dot_52 - receptacle
        Sofa_bar__minus_02_dot_68_bar__minus_00_dot_05_bar__minus_00_dot_12 - receptacle
        Sofa_bar__minus_03_dot_79_bar__plus_00_dot_01_bar__plus_03_dot_89 - receptacle
        TVStand_bar__plus_00_dot_20_bar__plus_00_dot_58_bar__plus_01_dot_98 - receptacle
        loc_bar__minus_11_bar_2_bar_2_bar_60 - location
        loc_bar__minus_21_bar_1_bar_2_bar_60 - location
        loc_bar__minus_21_bar_1_bar_2_bar__minus_15 - location
        loc_bar__minus_4_bar__minus_1_bar_2_bar_15 - location
        loc_bar__minus_6_bar_15_bar_1_bar_60 - location
        loc_bar__minus_10_bar_2_bar_2_bar_0 - location
        loc_bar__minus_2_bar_8_bar_1_bar_60 - location
        loc_bar__minus_22_bar_12_bar_3_bar_30 - location
        loc_bar__minus_4_bar_14_bar_1_bar_60 - location
        loc_bar__minus_5_bar_3_bar_1_bar_45 - location
        loc_bar__minus_11_bar_9_bar_2_bar_60 - location
        loc_bar__minus_3_bar_11_bar_0_bar_30 - location
        loc_bar__minus_8_bar_14_bar_0_bar_60 - location
        loc_bar__minus_3_bar_15_bar_1_bar__minus_15 - location
        loc_bar__minus_6_bar_4_bar_1_bar_45 - location
        loc_bar__minus_2_bar_10_bar_0_bar_60 - location
        loc_bar__minus_5_bar_15_bar_0_bar_60 - location
        loc_bar__minus_17_bar_1_bar_2_bar_60 - location
        loc_bar__minus_17_bar_1_bar_2_bar_0 - location
        loc_bar__minus_3_bar_8_bar_1_bar_45 - location
        loc_bar__minus_3_bar__minus_1_bar_1_bar_60 - location
        loc_bar__minus_4_bar_2_bar_1_bar_60 - location
        loc_bar__minus_5_bar_14_bar_1_bar_60 - location
        loc_bar__minus_15_bar_10_bar_0_bar_45 - location
        loc_bar__minus_23_bar_15_bar_2_bar_30 - location
        )
    

(:init


        (receptacleType SideTable_bar__minus_00_dot_28_bar__plus_00_dot_01_bar__plus_00_dot_52 SideTableType)
        (receptacleType CoffeeTable_bar__minus_02_dot_65_bar_00_dot_00_bar__plus_01_dot_15 CoffeeTableType)
        (receptacleType Sofa_bar__minus_03_dot_79_bar__plus_00_dot_01_bar__plus_03_dot_89 SofaType)
        (receptacleType DiningTable_bar__plus_00_dot_19_bar__plus_00_dot_72_bar__plus_02_dot_00 DiningTableType)
        (receptacleType Dresser_bar__minus_00_dot_27_bar__plus_00_dot_00_bar__plus_03_dot_61 DresserType)
        (receptacleType Sofa_bar__minus_02_dot_68_bar__minus_00_dot_05_bar__minus_00_dot_12 SofaType)
        (receptacleType DiningTable_bar__plus_00_dot_19_bar__plus_00_dot_91_bar__plus_02_dot_00 DiningTableType)
        (receptacleType TVStand_bar__plus_00_dot_20_bar__plus_00_dot_58_bar__plus_01_dot_98 TVStandType)
        (receptacleType GarbageCan_bar__minus_05_dot_17_bar__plus_00_dot_01_bar__minus_00_dot_41 GarbageCanType)
        (receptacleType Drawer_bar__minus_00_dot_37_bar__plus_00_dot_57_bar__plus_00_dot_52 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_38_bar__plus_01_dot_07_bar__plus_03_dot_61 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_37_bar__plus_00_dot_21_bar__plus_00_dot_52 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_38_bar__plus_00_dot_79_bar__plus_03_dot_61 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_38_bar__plus_00_dot_18_bar__plus_03_dot_61 DrawerType)
        (receptacleType ArmChair_bar__minus_02_dot_06_bar_00_dot_00_bar__plus_04_dot_22 ArmChairType)
        (receptacleType Drawer_bar__minus_00_dot_38_bar__plus_00_dot_48_bar__plus_03_dot_61 DrawerType)
        (objectType Window_bar__minus_04_dot_13_bar__plus_01_dot_56_bar__minus_00_dot_83 WindowType)
        (objectType Painting_bar__minus_00_dot_05_bar__plus_01_dot_91_bar__plus_03_dot_69 PaintingType)
        (objectType TissueBox_bar__minus_00_dot_30_bar__plus_01_dot_31_bar__plus_03_dot_44 TissueBoxType)
        (objectType HousePlant_bar__minus_00_dot_29_bar__plus_00_dot_83_bar__plus_00_dot_30 HousePlantType)
        (objectType CreditCard_bar__minus_02_dot_66_bar__plus_00_dot_47_bar__plus_01_dot_35 CreditCardType)
        (objectType KeyChain_bar__minus_02_dot_13_bar__plus_00_dot_40_bar__plus_04_dot_12 KeyChainType)
        (objectType RemoteControl_bar__plus_00_dot_12_bar__plus_00_dot_87_bar__plus_01_dot_85 RemoteControlType)
        (objectType KeyChain_bar__minus_02_dot_51_bar__plus_00_dot_47_bar__plus_01_dot_10 KeyChainType)
        (objectType Chair_bar__minus_01_dot_35_bar__plus_00_dot_02_bar__plus_04_dot_24 ChairType)
        (objectType Boots_bar__minus_00_dot_44_bar__plus_00_dot_01_bar__minus_00_dot_55 BootsType)
        (objectType LightSwitch_bar__minus_06_dot_00_bar__plus_01_dot_38_bar__plus_02_dot_88 LightSwitchType)
        (objectType RemoteControl_bar__minus_00_dot_41_bar__plus_01_dot_31_bar__plus_03_dot_78 RemoteControlType)
        (objectType Television_bar__plus_00_dot_23_bar__plus_01_dot_53_bar__plus_02_dot_00 TelevisionType)
        (objectType TissueBox_bar__minus_00_dot_16_bar__plus_00_dot_84_bar__plus_00_dot_32 TissueBoxType)
        (objectType Statue_bar__minus_02_dot_36_bar__plus_00_dot_48_bar__plus_01_dot_04 StatueType)
        (objectType CreditCard_bar__minus_00_dot_13_bar__plus_01_dot_31_bar__plus_03_dot_44 CreditCardType)
        (objectType Painting_bar__minus_05_dot_35_bar__plus_01_dot_99_bar__minus_00_dot_69 PaintingType)
        (objectType Window_bar__minus_02_dot_55_bar__plus_01_dot_56_bar__minus_00_dot_83 WindowType)
        (objectType FloorLamp_bar__minus_04_dot_10_bar__plus_00_dot_01_bar__minus_00_dot_47 FloorLampType)
        (objectType Box_bar__minus_00_dot_25_bar__plus_01_dot_50_bar__plus_03_dot_28 BoxType)
        (objectType Laptop_bar__minus_02_dot_01_bar__plus_00_dot_45_bar__minus_00_dot_09 LaptopType)
        (objectType Pillow_bar__minus_03_dot_71_bar__plus_00_dot_64_bar__plus_03_dot_75 PillowType)
        (objectType Window_bar__minus_00_dot_97_bar__plus_01_dot_56_bar__minus_00_dot_83 WindowType)
        (objectType Candle_bar__minus_00_dot_08_bar__plus_01_dot_31_bar__plus_04_dot_11 CandleType)
        (canContain SideTableType CandleType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType TissueBoxType)
        (canContain SideTableType StatueType)
        (canContain CoffeeTableType CandleType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType TissueBoxType)
        (canContain CoffeeTableType StatueType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType CreditCardType)
        (canContain DiningTableType CandleType)
        (canContain DiningTableType BoxType)
        (canContain DiningTableType KeyChainType)
        (canContain DiningTableType CreditCardType)
        (canContain DiningTableType LaptopType)
        (canContain DiningTableType RemoteControlType)
        (canContain DiningTableType TissueBoxType)
        (canContain DiningTableType StatueType)
        (canContain DresserType CandleType)
        (canContain DresserType BoxType)
        (canContain DresserType KeyChainType)
        (canContain DresserType CreditCardType)
        (canContain DresserType LaptopType)
        (canContain DresserType RemoteControlType)
        (canContain DresserType TissueBoxType)
        (canContain DresserType StatueType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType CreditCardType)
        (canContain DiningTableType CandleType)
        (canContain DiningTableType BoxType)
        (canContain DiningTableType KeyChainType)
        (canContain DiningTableType CreditCardType)
        (canContain DiningTableType LaptopType)
        (canContain DiningTableType RemoteControlType)
        (canContain DiningTableType TissueBoxType)
        (canContain DiningTableType StatueType)
        (canContain TVStandType TissueBoxType)
        (canContain GarbageCanType TissueBoxType)
        (canContain DrawerType CandleType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType TissueBoxType)
        (canContain DrawerType CandleType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType TissueBoxType)
        (canContain DrawerType CandleType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType TissueBoxType)
        (canContain DrawerType CandleType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType TissueBoxType)
        (canContain DrawerType CandleType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType TissueBoxType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (canContain DrawerType CandleType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType TissueBoxType)
        (pickupable TissueBox_bar__minus_00_dot_30_bar__plus_01_dot_31_bar__plus_03_dot_44)
        (pickupable CreditCard_bar__minus_02_dot_66_bar__plus_00_dot_47_bar__plus_01_dot_35)
        (pickupable KeyChain_bar__minus_02_dot_13_bar__plus_00_dot_40_bar__plus_04_dot_12)
        (pickupable RemoteControl_bar__plus_00_dot_12_bar__plus_00_dot_87_bar__plus_01_dot_85)
        (pickupable KeyChain_bar__minus_02_dot_51_bar__plus_00_dot_47_bar__plus_01_dot_10)
        (pickupable Boots_bar__minus_00_dot_44_bar__plus_00_dot_01_bar__minus_00_dot_55)
        (pickupable RemoteControl_bar__minus_00_dot_41_bar__plus_01_dot_31_bar__plus_03_dot_78)
        (pickupable TissueBox_bar__minus_00_dot_16_bar__plus_00_dot_84_bar__plus_00_dot_32)
        (pickupable Statue_bar__minus_02_dot_36_bar__plus_00_dot_48_bar__plus_01_dot_04)
        (pickupable CreditCard_bar__minus_00_dot_13_bar__plus_01_dot_31_bar__plus_03_dot_44)
        (pickupable Box_bar__minus_00_dot_25_bar__plus_01_dot_50_bar__plus_03_dot_28)
        (pickupable Laptop_bar__minus_02_dot_01_bar__plus_00_dot_45_bar__minus_00_dot_09)
        (pickupable Pillow_bar__minus_03_dot_71_bar__plus_00_dot_64_bar__plus_03_dot_75)
        (pickupable Candle_bar__minus_00_dot_08_bar__plus_01_dot_31_bar__plus_04_dot_11)
        (isReceptacleObject Box_bar__minus_00_dot_25_bar__plus_01_dot_50_bar__plus_03_dot_28)
        (openable Drawer_bar__minus_00_dot_37_bar__plus_00_dot_57_bar__plus_00_dot_52)
        (openable Drawer_bar__minus_00_dot_38_bar__plus_01_dot_07_bar__plus_03_dot_61)
        (openable Drawer_bar__minus_00_dot_37_bar__plus_00_dot_21_bar__plus_00_dot_52)
        (openable Drawer_bar__minus_00_dot_38_bar__plus_00_dot_79_bar__plus_03_dot_61)
        (openable Drawer_bar__minus_00_dot_38_bar__plus_00_dot_18_bar__plus_03_dot_61)
        (openable Drawer_bar__minus_00_dot_38_bar__plus_00_dot_48_bar__plus_03_dot_61)
        
        (atLocation agent1 loc_bar__minus_23_bar_15_bar_2_bar_30)
        
        
        
        
        
        
        
        (toggleable FloorLamp_bar__minus_04_dot_10_bar__plus_00_dot_01_bar__minus_00_dot_47)
        
        
        
        
        (inReceptacle Pillow_bar__minus_03_dot_71_bar__plus_00_dot_64_bar__plus_03_dot_75 Sofa_bar__minus_03_dot_79_bar__plus_00_dot_01_bar__plus_03_dot_89)
        (inReceptacle RemoteControl_bar__minus_00_dot_41_bar__plus_01_dot_31_bar__plus_03_dot_78 Dresser_bar__minus_00_dot_27_bar__plus_00_dot_00_bar__plus_03_dot_61)
        (inReceptacle TissueBox_bar__minus_00_dot_30_bar__plus_01_dot_31_bar__plus_03_dot_44 Dresser_bar__minus_00_dot_27_bar__plus_00_dot_00_bar__plus_03_dot_61)
        (inReceptacle Box_bar__minus_00_dot_25_bar__plus_01_dot_50_bar__plus_03_dot_28 Dresser_bar__minus_00_dot_27_bar__plus_00_dot_00_bar__plus_03_dot_61)
        (inReceptacle CreditCard_bar__minus_00_dot_13_bar__plus_01_dot_31_bar__plus_03_dot_44 Dresser_bar__minus_00_dot_27_bar__plus_00_dot_00_bar__plus_03_dot_61)
        (inReceptacle Candle_bar__minus_00_dot_08_bar__plus_01_dot_31_bar__plus_04_dot_11 Dresser_bar__minus_00_dot_27_bar__plus_00_dot_00_bar__plus_03_dot_61)
        (inReceptacle Laptop_bar__minus_02_dot_01_bar__plus_00_dot_45_bar__minus_00_dot_09 Sofa_bar__minus_02_dot_68_bar__minus_00_dot_05_bar__minus_00_dot_12)
        (inReceptacle TissueBox_bar__minus_00_dot_16_bar__plus_00_dot_84_bar__plus_00_dot_32 SideTable_bar__minus_00_dot_28_bar__plus_00_dot_01_bar__plus_00_dot_52)
        (inReceptacle HousePlant_bar__minus_00_dot_29_bar__plus_00_dot_83_bar__plus_00_dot_30 SideTable_bar__minus_00_dot_28_bar__plus_00_dot_01_bar__plus_00_dot_52)
        (inReceptacle Television_bar__plus_00_dot_23_bar__plus_01_dot_53_bar__plus_02_dot_00 TVStand_bar__plus_00_dot_20_bar__plus_00_dot_58_bar__plus_01_dot_98)
        (inReceptacle KeyChain_bar__minus_02_dot_13_bar__plus_00_dot_40_bar__plus_04_dot_12 ArmChair_bar__minus_02_dot_06_bar_00_dot_00_bar__plus_04_dot_22)
        (inReceptacle CreditCard_bar__minus_02_dot_66_bar__plus_00_dot_47_bar__plus_01_dot_35 CoffeeTable_bar__minus_02_dot_65_bar_00_dot_00_bar__plus_01_dot_15)
        (inReceptacle KeyChain_bar__minus_02_dot_51_bar__plus_00_dot_47_bar__plus_01_dot_10 CoffeeTable_bar__minus_02_dot_65_bar_00_dot_00_bar__plus_01_dot_15)
        (inReceptacle Statue_bar__minus_02_dot_36_bar__plus_00_dot_48_bar__plus_01_dot_04 CoffeeTable_bar__minus_02_dot_65_bar_00_dot_00_bar__plus_01_dot_15)
        (inReceptacle RemoteControl_bar__plus_00_dot_12_bar__plus_00_dot_87_bar__plus_01_dot_85 DiningTable_bar__plus_00_dot_19_bar__plus_00_dot_91_bar__plus_02_dot_00)
        
        
        (receptacleAtLocation ArmChair_bar__minus_02_dot_06_bar_00_dot_00_bar__plus_04_dot_22 loc_bar__minus_8_bar_14_bar_0_bar_60)
        (receptacleAtLocation CoffeeTable_bar__minus_02_dot_65_bar_00_dot_00_bar__plus_01_dot_15 loc_bar__minus_11_bar_9_bar_2_bar_60)
        (receptacleAtLocation DiningTable_bar__plus_00_dot_19_bar__plus_00_dot_72_bar__plus_02_dot_00 loc_bar__minus_3_bar_8_bar_1_bar_45)
        (receptacleAtLocation DiningTable_bar__plus_00_dot_19_bar__plus_00_dot_91_bar__plus_02_dot_00 loc_bar__minus_3_bar_8_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_37_bar__plus_00_dot_21_bar__plus_00_dot_52 loc_bar__minus_6_bar_4_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_37_bar__plus_00_dot_57_bar__plus_00_dot_52 loc_bar__minus_5_bar_3_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_38_bar__plus_00_dot_18_bar__plus_03_dot_61 loc_bar__minus_6_bar_15_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__minus_00_dot_38_bar__plus_00_dot_48_bar__plus_03_dot_61 loc_bar__minus_5_bar_14_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__minus_00_dot_38_bar__plus_00_dot_79_bar__plus_03_dot_61 loc_bar__minus_4_bar_14_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__minus_00_dot_38_bar__plus_01_dot_07_bar__plus_03_dot_61 loc_bar__minus_3_bar_11_bar_0_bar_30)
        (receptacleAtLocation Dresser_bar__minus_00_dot_27_bar__plus_00_dot_00_bar__plus_03_dot_61 loc_bar__minus_2_bar_10_bar_0_bar_60)
        (receptacleAtLocation GarbageCan_bar__minus_05_dot_17_bar__plus_00_dot_01_bar__minus_00_dot_41 loc_bar__minus_21_bar_1_bar_2_bar_60)
        (receptacleAtLocation SideTable_bar__minus_00_dot_28_bar__plus_00_dot_01_bar__plus_00_dot_52 loc_bar__minus_4_bar_2_bar_1_bar_60)
        (receptacleAtLocation Sofa_bar__minus_02_dot_68_bar__minus_00_dot_05_bar__minus_00_dot_12 loc_bar__minus_11_bar_2_bar_2_bar_60)
        (receptacleAtLocation Sofa_bar__minus_03_dot_79_bar__plus_00_dot_01_bar__plus_03_dot_89 loc_bar__minus_15_bar_10_bar_0_bar_45)
        (receptacleAtLocation TVStand_bar__plus_00_dot_20_bar__plus_00_dot_58_bar__plus_01_dot_98 loc_bar__minus_2_bar_8_bar_1_bar_60)
        (objectAtLocation CreditCard_bar__minus_02_dot_66_bar__plus_00_dot_47_bar__plus_01_dot_35 loc_bar__minus_11_bar_9_bar_2_bar_60)
        (objectAtLocation RemoteControl_bar__minus_00_dot_41_bar__plus_01_dot_31_bar__plus_03_dot_78 loc_bar__minus_2_bar_10_bar_0_bar_60)
        (objectAtLocation KeyChain_bar__minus_02_dot_51_bar__plus_00_dot_47_bar__plus_01_dot_10 loc_bar__minus_11_bar_9_bar_2_bar_60)
        (objectAtLocation TissueBox_bar__minus_00_dot_16_bar__plus_00_dot_84_bar__plus_00_dot_32 loc_bar__minus_4_bar_2_bar_1_bar_60)
        (objectAtLocation Box_bar__minus_00_dot_25_bar__plus_01_dot_50_bar__plus_03_dot_28 loc_bar__minus_2_bar_10_bar_0_bar_60)
        (objectAtLocation Chair_bar__minus_01_dot_35_bar__plus_00_dot_02_bar__plus_04_dot_24 loc_bar__minus_5_bar_15_bar_0_bar_60)
        (objectAtLocation Television_bar__plus_00_dot_23_bar__plus_01_dot_53_bar__plus_02_dot_00 loc_bar__minus_2_bar_8_bar_1_bar_60)
        (objectAtLocation LightSwitch_bar__minus_06_dot_00_bar__plus_01_dot_38_bar__plus_02_dot_88 loc_bar__minus_22_bar_12_bar_3_bar_30)
        (objectAtLocation TissueBox_bar__minus_00_dot_30_bar__plus_01_dot_31_bar__plus_03_dot_44 loc_bar__minus_2_bar_10_bar_0_bar_60)
        (objectAtLocation KeyChain_bar__minus_02_dot_13_bar__plus_00_dot_40_bar__plus_04_dot_12 loc_bar__minus_8_bar_14_bar_0_bar_60)
        (objectAtLocation Boots_bar__minus_00_dot_44_bar__plus_00_dot_01_bar__minus_00_dot_55 loc_bar__minus_3_bar__minus_1_bar_1_bar_60)
        (objectAtLocation Painting_bar__minus_00_dot_05_bar__plus_01_dot_91_bar__plus_03_dot_69 loc_bar__minus_3_bar_15_bar_1_bar__minus_15)
        (objectAtLocation Painting_bar__minus_05_dot_35_bar__plus_01_dot_99_bar__minus_00_dot_69 loc_bar__minus_21_bar_1_bar_2_bar__minus_15)
        (objectAtLocation Candle_bar__minus_00_dot_08_bar__plus_01_dot_31_bar__plus_04_dot_11 loc_bar__minus_2_bar_10_bar_0_bar_60)
        (objectAtLocation RemoteControl_bar__plus_00_dot_12_bar__plus_00_dot_87_bar__plus_01_dot_85 loc_bar__minus_3_bar_8_bar_1_bar_45)
        (objectAtLocation HousePlant_bar__minus_00_dot_29_bar__plus_00_dot_83_bar__plus_00_dot_30 loc_bar__minus_4_bar_2_bar_1_bar_60)
        (objectAtLocation CreditCard_bar__minus_00_dot_13_bar__plus_01_dot_31_bar__plus_03_dot_44 loc_bar__minus_2_bar_10_bar_0_bar_60)
        (objectAtLocation Laptop_bar__minus_02_dot_01_bar__plus_00_dot_45_bar__minus_00_dot_09 loc_bar__minus_11_bar_2_bar_2_bar_60)
        (objectAtLocation Pillow_bar__minus_03_dot_71_bar__plus_00_dot_64_bar__plus_03_dot_75 loc_bar__minus_15_bar_10_bar_0_bar_45)
        (objectAtLocation FloorLamp_bar__minus_04_dot_10_bar__plus_00_dot_01_bar__minus_00_dot_47 loc_bar__minus_17_bar_1_bar_2_bar_60)
        (objectAtLocation Window_bar__minus_04_dot_13_bar__plus_01_dot_56_bar__minus_00_dot_83 loc_bar__minus_17_bar_1_bar_2_bar_0)
        (objectAtLocation Window_bar__minus_00_dot_97_bar__plus_01_dot_56_bar__minus_00_dot_83 loc_bar__minus_4_bar__minus_1_bar_2_bar_15)
        (objectAtLocation Window_bar__minus_02_dot_55_bar__plus_01_dot_56_bar__minus_00_dot_83 loc_bar__minus_10_bar_2_bar_2_bar_0)
        (objectAtLocation Statue_bar__minus_02_dot_36_bar__plus_00_dot_48_bar__plus_01_dot_04 loc_bar__minus_11_bar_9_bar_2_bar_60)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 TissueBoxType)
                                    (receptacleType ?r CoffeeTableType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 TissueBoxType)
                                            (receptacleType ?r CoffeeTableType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            