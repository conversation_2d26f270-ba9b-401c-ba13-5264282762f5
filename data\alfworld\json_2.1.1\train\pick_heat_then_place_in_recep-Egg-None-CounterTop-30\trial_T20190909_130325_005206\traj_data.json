{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 41}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "CounterTop", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-1|2|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-4.03268432, -4.03268432, 2.492002248, 2.492002248, 3.8113404, 3.8113404]], "coordinateReceptacleObjectId": ["CounterTop", [-4.0316868, -4.0316868, -0.1576, -0.1576, 3.7432, 3.7432]], "forceVisible": true, "objectId": "Egg|-01.01|+00.95|+00.62"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-1|5|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-4.136, -4.136, 5.716, 5.716, 3.5567708, 3.5567708]], "forceVisible": true, "objectId": "Microwave|-01.03|+00.89|+01.43"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-1|0|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "countertop"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-4.03268432, -4.03268432, 2.492002248, 2.492002248, 3.8113404, 3.8113404]], "coordinateReceptacleObjectId": ["CounterTop", [-4.0316868, -4.0316868, -0.1576, -0.1576, 3.7432, 3.7432]], "forceVisible": true, "objectId": "Egg|-01.01|+00.95|+00.62", "receptacleObjectId": "CounterTop|-01.01|+00.94|-00.04"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.01|+00.95|+00.62"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [171, 123, 191, 148], "mask": [[36782, 1], [37078, 8], [37377, 11], [37675, 14], [37974, 15], [38274, 16], [38573, 18], [38872, 19], [39172, 19], [39472, 20], [39771, 21], [40071, 21], [40371, 21], [40671, 21], [40971, 21], [41271, 21], [41571, 21], [41871, 21], [42171, 20], [42472, 19], [42772, 18], [43073, 17], [43374, 15], [43675, 13], [43976, 10], [44278, 6]], "point": [181, 134]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [103, 21, 299, 168], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22304, 196], [22604, 196], [22904, 196], [23204, 196], [23504, 196], [23804, 196], [24104, 196], [24405, 195], [24705, 195], [25005, 195], [25305, 195], [25605, 195], [25905, 195], [26205, 195], [26505, 195], [26805, 195], [27106, 194], [27406, 194], [27706, 194], [28006, 194], [28306, 194], [28606, 194], [28906, 194], [29206, 194], [29507, 193], [29807, 193], [30107, 193], [30407, 193], [30707, 193], [31007, 193], [31307, 193], [31608, 192], [31908, 192], [32208, 192], [32508, 192], [32808, 192], [33108, 192], [33408, 192], [33709, 191], [34009, 191], [34309, 191], [34609, 191], [34909, 191], [35209, 191], [35509, 191], [35810, 190], [36110, 190], [36410, 190], [36710, 190], [37010, 190], [37310, 190], [37610, 190], [37911, 189], [38211, 189], [38511, 189], [38811, 189], [39111, 189], [39411, 189], [39711, 189], [40012, 188], [40312, 188], [40612, 187], [40912, 187], [41212, 186], [41512, 186], [41812, 185], [42112, 184], [42413, 183], [42713, 182], [43013, 182], [43313, 181], [43613, 181], [43913, 180], [44213, 180], [44514, 178], [44814, 178], [45114, 177], [45414, 176], [45714, 176], [46014, 175], [46314, 175], [46615, 173], [46915, 173], [47215, 172], [47515, 172], [47815, 171], [48115, 171], [48416, 169], [48716, 169], [49016, 168], [49316, 168], [49616, 167], [49916, 167], [50217, 165]], "point": [201, 93]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.01|+00.95|+00.62", "placeStationary": true, "receptacleObjectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [37, 21, 299, 294], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22294, 206], [22593, 207], [22893, 207], [23193, 207], [23492, 208], [23792, 208], [24092, 208], [24391, 209], [24691, 209], [24991, 209], [25291, 209], [25590, 210], [25890, 120], [26034, 66], [26190, 120], [26334, 66], [26489, 121], [26634, 66], [26789, 121], [26933, 67], [27089, 121], [27233, 67], [27388, 122], [27533, 67], [27688, 122], [27832, 68], [27988, 121], [28132, 68], [28287, 122], [28432, 68], [28587, 122], [28731, 69], [28887, 122], [29031, 69], [29187, 122], [29331, 69], [29486, 123], [29630, 70], [29786, 123], [29930, 70], [30086, 122], [30230, 70], [30385, 123], [30530, 70], [30685, 123], [30829, 71], [30985, 123], [31129, 71], [31284, 124], [31429, 71], [31584, 124], [31728, 72], [31884, 124], [32028, 72], [32183, 125], [32328, 72], [32483, 126], [32628, 72], [32783, 127], [32927, 73], [33083, 129], [33226, 74], [33382, 131], [33525, 75], [33682, 134], [33822, 78], [33982, 218], [34281, 219], [34581, 219], [34881, 219], [35180, 220], [35480, 220], [35780, 220], [36080, 220], [36379, 221], [36679, 221], [36979, 221], [37278, 222], [37578, 222], [37878, 222], [38177, 223], [38477, 223], [38777, 223], [39076, 224], [39376, 224], [39676, 224], [39976, 224], [40275, 225], [40575, 224], [40875, 224], [41174, 224], [41474, 224], [41774, 223], [42073, 223], [42373, 223], [42673, 222], [42972, 223], [43272, 222], [43572, 222], [43872, 221], [44171, 222], [44471, 221], [44771, 221], [45070, 221], [45370, 220], [45670, 220], [45969, 220], [46269, 220], [46569, 219], [46868, 220], [47168, 219], [47468, 219], [47768, 218], [48067, 219], [48367, 218], [48667, 218], [48966, 218], [49266, 218], [49566, 217], [49865, 218], [50165, 217], [50465, 52], [50765, 52], [51064, 53], [51364, 53], [51664, 53], [51963, 54], [52263, 53], [52563, 53], [52862, 54], [53162, 54], [53462, 54], [53761, 55], [54061, 55], [54361, 55], [54661, 55], [54960, 55], [55260, 55], [55560, 55], [55859, 56], [56159, 56], [56459, 56], [56758, 57], [57058, 57], [57358, 56], [57657, 57], [57957, 57], [58257, 57], [58557, 57], [58856, 58], [59156, 58], [59456, 58], [59755, 59], [60055, 58], [60355, 58], [60654, 59], [60954, 59], [61254, 59], [61553, 60], [61853, 60], [62153, 60], [62453, 59], [62752, 60], [63052, 60], [63352, 60], [63651, 61], [63951, 61], [64251, 61], [64550, 62], [64841, 71], [65141, 70], [65440, 71], [65740, 71], [66040, 71], [66339, 72], [66639, 72], [66939, 72], [67238, 73], [67538, 72], [67838, 72], [68138, 72], [68438, 72], [68738, 72], [69037, 73], [69337, 73], [69637, 73], [69938, 72], [70238, 71], [70538, 71], [70838, 71], [71138, 71], [71438, 71], [71738, 71], [72038, 71], [72338, 71], [72639, 69], [72939, 69], [73239, 69], [73539, 69], [73839, 69], [74139, 69], [74440, 68], [74740, 68], [75040, 68], [75341, 66], [75641, 66], [75941, 66], [76241, 66], [76542, 65], [76842, 65], [77142, 65], [77443, 64], [77743, 63], [78044, 62], [78344, 62], [78644, 62], [78945, 61], [79245, 61], [79546, 60], [79847, 59], [80148, 57], [80448, 57], [80749, 56], [81050, 55], [81351, 54], [81653, 52], [81954, 51], [82255, 50], [82556, 49], [82858, 46], [83159, 45], [83460, 44], [83762, 42], [84064, 40], [84365, 39], [84667, 37], [84969, 35], [85271, 32], [85573, 30], [85878, 28], [86179, 29], [86479, 31], [86780, 23], [86804, 6], [87080, 23], [87105, 5], [87381, 22], [87682, 21], [87982, 20]], "point": [168, 156]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [37, 21, 299, 294], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22294, 206], [22593, 207], [22893, 207], [23193, 207], [23492, 208], [23792, 208], [24092, 208], [24391, 209], [24691, 209], [24991, 209], [25291, 209], [25590, 210], [25890, 120], [26034, 66], [26190, 120], [26334, 66], [26489, 121], [26634, 66], [26789, 121], [26933, 67], [27089, 121], [27233, 67], [27388, 122], [27533, 67], [27688, 122], [27832, 68], [27988, 121], [28132, 68], [28287, 122], [28432, 68], [28587, 122], [28731, 69], [28887, 122], [29031, 69], [29187, 122], [29331, 69], [29486, 123], [29630, 70], [29786, 63], [29856, 53], [29930, 70], [30086, 61], [30158, 50], [30230, 70], [30385, 61], [30459, 49], [30530, 70], [30685, 60], [30760, 48], [30829, 71], [30985, 59], [31061, 47], [31129, 71], [31284, 60], [31362, 46], [31429, 71], [31584, 59], [31662, 46], [31728, 72], [31884, 59], [31962, 46], [32028, 72], [32183, 59], [32263, 45], [32328, 72], [32483, 59], [32563, 46], [32628, 72], [32783, 58], [32864, 46], [32927, 73], [33083, 58], [33164, 48], [33226, 74], [33382, 59], [33464, 49], [33525, 75], [33682, 59], [33764, 52], [33822, 78], [33982, 59], [34064, 136], [34281, 60], [34364, 136], [34581, 60], [34664, 136], [34881, 60], [34964, 136], [35180, 61], [35264, 136], [35480, 61], [35564, 136], [35780, 62], [35863, 137], [36080, 62], [36163, 137], [36379, 63], [36463, 137], [36679, 64], [36762, 138], [36979, 65], [37061, 139], [37278, 67], [37360, 140], [37578, 68], [37659, 141], [37878, 69], [37958, 142], [38177, 72], [38256, 144], [38477, 223], [38777, 223], [39076, 224], [39376, 224], [39676, 224], [39976, 224], [40275, 225], [40575, 224], [40875, 224], [41174, 224], [41474, 224], [41774, 223], [42073, 223], [42373, 223], [42673, 222], [42972, 223], [43272, 222], [43572, 222], [43872, 221], [44171, 222], [44471, 221], [44771, 221], [45070, 221], [45370, 220], [45670, 220], [45969, 220], [46269, 220], [46569, 219], [46868, 220], [47168, 219], [47468, 219], [47768, 218], [48067, 219], [48367, 218], [48667, 218], [48966, 218], [49266, 218], [49566, 217], [49865, 218], [50165, 217], [50465, 52], [50765, 52], [51064, 53], [51364, 53], [51664, 53], [51963, 54], [52263, 53], [52563, 53], [52862, 54], [53162, 54], [53462, 54], [53761, 55], [54061, 55], [54361, 55], [54661, 55], [54960, 55], [55260, 55], [55560, 55], [55859, 56], [56159, 56], [56459, 56], [56758, 57], [57058, 57], [57358, 56], [57657, 57], [57957, 57], [58257, 57], [58557, 57], [58856, 58], [59156, 58], [59456, 58], [59755, 59], [60055, 58], [60355, 58], [60654, 59], [60954, 59], [61254, 59], [61553, 60], [61853, 60], [62153, 60], [62453, 59], [62752, 60], [63052, 60], [63352, 60], [63651, 61], [63951, 61], [64251, 61], [64550, 62], [64841, 71], [65141, 70], [65440, 71], [65740, 71], [66040, 71], [66339, 72], [66639, 72], [66939, 72], [67238, 73], [67538, 72], [67838, 72], [68138, 72], [68438, 72], [68738, 72], [69037, 73], [69337, 73], [69637, 73], [69938, 72], [70238, 71], [70538, 71], [70838, 71], [71138, 71], [71438, 71], [71738, 71], [72038, 71], [72338, 71], [72639, 69], [72939, 69], [73239, 69], [73539, 69], [73839, 69], [74139, 69], [74440, 68], [74740, 68], [75040, 68], [75341, 66], [75641, 66], [75941, 66], [76241, 66], [76542, 65], [76842, 65], [77142, 65], [77443, 64], [77743, 63], [78044, 62], [78344, 62], [78644, 62], [78945, 61], [79245, 61], [79546, 60], [79847, 59], [80148, 57], [80448, 57], [80749, 56], [81050, 55], [81351, 54], [81653, 52], [81954, 51], [82255, 50], [82556, 49], [82858, 46], [83159, 45], [83460, 44], [83762, 42], [84064, 40], [84365, 39], [84667, 37], [84969, 35], [85271, 32], [85573, 30], [85878, 28], [86179, 29], [86479, 31], [86780, 23], [86804, 6], [87080, 23], [87105, 5], [87381, 22], [87682, 21], [87982, 20]], "point": [168, 156]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-4.136, -4.136, 5.716, 5.716, 3.5567708, 3.5567708]], "forceVisible": true, "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [103, 21, 299, 168], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22304, 196], [22604, 196], [22904, 196], [23204, 196], [23504, 196], [23804, 196], [24104, 196], [24405, 195], [24705, 195], [25005, 195], [25305, 195], [25605, 195], [25905, 195], [26205, 195], [26505, 195], [26805, 195], [27106, 194], [27406, 194], [27706, 194], [28006, 194], [28306, 194], [28606, 194], [28906, 194], [29206, 194], [29507, 193], [29807, 193], [30107, 193], [30407, 193], [30707, 193], [31007, 193], [31307, 193], [31608, 192], [31908, 192], [32208, 192], [32508, 192], [32808, 192], [33108, 192], [33408, 192], [33709, 191], [34009, 191], [34309, 191], [34609, 191], [34909, 191], [35209, 191], [35509, 191], [35810, 190], [36110, 190], [36410, 190], [36710, 190], [37010, 190], [37310, 190], [37610, 190], [37911, 189], [38211, 189], [38511, 189], [38811, 189], [39111, 189], [39411, 189], [39711, 189], [40012, 188], [40312, 188], [40612, 187], [40912, 187], [41212, 186], [41512, 186], [41812, 185], [42112, 184], [42413, 183], [42713, 182], [43013, 182], [43313, 181], [43613, 181], [43913, 180], [44213, 180], [44514, 178], [44814, 178], [45114, 177], [45414, 176], [45714, 176], [46014, 175], [46314, 175], [46615, 173], [46915, 173], [47215, 172], [47515, 172], [47815, 171], [48115, 171], [48416, 169], [48716, 169], [49016, 168], [49316, 168], [49616, 167], [49916, 167], [50217, 165]], "point": [201, 93]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-4.136, -4.136, 5.716, 5.716, 3.5567708, 3.5567708]], "forceVisible": true, "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [103, 21, 299, 168], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22304, 196], [22604, 196], [22904, 196], [23204, 196], [23504, 196], [23804, 196], [24104, 196], [24405, 195], [24705, 195], [25005, 195], [25305, 195], [25605, 195], [25905, 195], [26205, 195], [26505, 195], [26805, 195], [27106, 194], [27406, 194], [27706, 194], [28006, 194], [28306, 194], [28606, 194], [28906, 194], [29206, 194], [29507, 193], [29807, 193], [30107, 193], [30407, 193], [30707, 193], [31007, 193], [31307, 193], [31608, 192], [31908, 192], [32208, 192], [32508, 192], [32808, 192], [33108, 192], [33408, 192], [33709, 191], [34009, 191], [34309, 191], [34609, 191], [34909, 191], [35209, 191], [35509, 191], [35810, 190], [36110, 190], [36410, 190], [36710, 190], [37010, 190], [37310, 190], [37610, 190], [37911, 189], [38211, 189], [38511, 189], [38811, 189], [39111, 189], [39411, 189], [39711, 189], [40012, 188], [40312, 188], [40612, 187], [40912, 187], [41212, 186], [41512, 186], [41812, 185], [42112, 184], [42413, 183], [42713, 182], [43013, 182], [43313, 181], [43613, 181], [43913, 180], [44213, 180], [44514, 178], [44814, 178], [45114, 177], [45414, 176], [45714, 176], [46014, 175], [46314, 175], [46615, 173], [46915, 173], [47215, 172], [47515, 172], [47815, 171], [48115, 171], [48416, 169], [48716, 169], [49016, 168], [49316, 168], [49616, 167], [49916, 167], [50217, 165]], "point": [201, 93]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [103, 21, 299, 168], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22304, 196], [22604, 196], [22904, 196], [23204, 196], [23504, 196], [23804, 196], [24104, 196], [24405, 195], [24705, 195], [25005, 195], [25305, 195], [25605, 195], [25905, 195], [26205, 195], [26505, 195], [26805, 195], [27106, 194], [27406, 194], [27706, 194], [28006, 194], [28306, 194], [28606, 194], [28906, 194], [29206, 194], [29507, 193], [29807, 193], [30107, 193], [30407, 193], [30707, 193], [31007, 193], [31307, 193], [31608, 192], [31908, 192], [32208, 192], [32508, 192], [32808, 192], [33108, 192], [33408, 192], [33709, 191], [34009, 191], [34309, 191], [34609, 191], [34909, 191], [35209, 191], [35509, 191], [35810, 190], [36110, 190], [36410, 190], [36710, 190], [37010, 190], [37310, 190], [37610, 190], [37911, 189], [38211, 189], [38511, 189], [38811, 189], [39111, 189], [39411, 189], [39711, 189], [40012, 188], [40312, 188], [40612, 187], [40912, 187], [41212, 186], [41512, 186], [41812, 185], [42112, 184], [42413, 183], [42713, 182], [43013, 182], [43313, 181], [43613, 181], [43913, 180], [44213, 180], [44514, 178], [44814, 178], [45114, 177], [45414, 176], [45714, 176], [46014, 175], [46314, 175], [46615, 173], [46915, 173], [47215, 172], [47515, 172], [47815, 171], [48115, 171], [48416, 169], [48716, 169], [49016, 168], [49316, 168], [49616, 167], [49916, 167], [50217, 165]], "point": [201, 93]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.01|+00.95|+00.62"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [141, 100, 163, 128], "mask": [[29849, 7], [30147, 11], [30446, 13], [30745, 15], [31044, 17], [31344, 18], [31643, 19], [31943, 19], [32242, 21], [32542, 21], [32841, 23], [33141, 23], [33441, 23], [33741, 23], [34041, 23], [34341, 23], [34641, 23], [34941, 23], [35241, 23], [35541, 23], [35842, 21], [36142, 21], [36442, 21], [36743, 19], [37044, 17], [37345, 15], [37646, 13], [37947, 11], [38249, 7]], "point": [152, 113]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [37, 21, 299, 294], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22294, 206], [22593, 207], [22893, 207], [23193, 207], [23492, 208], [23792, 208], [24092, 208], [24391, 209], [24691, 209], [24991, 209], [25291, 209], [25590, 210], [25890, 120], [26034, 66], [26190, 120], [26334, 66], [26489, 121], [26634, 66], [26789, 121], [26933, 67], [27089, 121], [27233, 67], [27388, 122], [27533, 67], [27688, 122], [27832, 68], [27988, 121], [28132, 68], [28287, 122], [28432, 68], [28587, 122], [28731, 69], [28887, 122], [29031, 69], [29187, 122], [29331, 69], [29486, 123], [29630, 70], [29786, 123], [29930, 70], [30086, 122], [30230, 70], [30385, 123], [30530, 70], [30685, 123], [30829, 71], [30985, 123], [31129, 71], [31284, 124], [31429, 71], [31584, 124], [31728, 72], [31884, 124], [32028, 72], [32183, 125], [32328, 72], [32483, 126], [32628, 72], [32783, 127], [32927, 73], [33083, 129], [33226, 74], [33382, 131], [33525, 75], [33682, 134], [33822, 78], [33982, 218], [34281, 219], [34581, 219], [34881, 219], [35180, 220], [35480, 220], [35780, 220], [36080, 220], [36379, 221], [36679, 221], [36979, 221], [37278, 222], [37578, 222], [37878, 222], [38177, 223], [38477, 223], [38777, 223], [39076, 224], [39376, 224], [39676, 224], [39976, 224], [40275, 225], [40575, 224], [40875, 224], [41174, 224], [41474, 224], [41774, 223], [42073, 223], [42373, 223], [42673, 222], [42972, 223], [43272, 222], [43572, 222], [43872, 221], [44171, 222], [44471, 221], [44771, 221], [45070, 221], [45370, 220], [45670, 220], [45969, 220], [46269, 220], [46569, 219], [46868, 220], [47168, 219], [47468, 219], [47768, 218], [48067, 219], [48367, 218], [48667, 218], [48966, 218], [49266, 218], [49566, 217], [49865, 218], [50165, 217], [50465, 52], [50765, 52], [51064, 53], [51364, 53], [51664, 53], [51963, 54], [52263, 53], [52563, 53], [52862, 54], [53162, 54], [53462, 54], [53761, 55], [54061, 55], [54361, 55], [54661, 55], [54960, 55], [55260, 55], [55560, 55], [55859, 56], [56159, 56], [56459, 56], [56758, 57], [57058, 57], [57358, 56], [57657, 57], [57957, 57], [58257, 57], [58557, 57], [58856, 58], [59156, 58], [59456, 58], [59755, 59], [60055, 58], [60355, 58], [60654, 59], [60954, 59], [61254, 59], [61553, 60], [61853, 60], [62153, 60], [62453, 59], [62752, 60], [63052, 60], [63352, 60], [63651, 61], [63951, 61], [64251, 61], [64550, 62], [64841, 71], [65141, 70], [65440, 71], [65740, 71], [66040, 71], [66339, 72], [66639, 72], [66939, 72], [67238, 73], [67538, 72], [67838, 72], [68138, 72], [68438, 72], [68738, 72], [69037, 73], [69337, 73], [69637, 73], [69938, 72], [70238, 71], [70538, 71], [70838, 71], [71138, 71], [71438, 71], [71738, 71], [72038, 71], [72338, 71], [72639, 69], [72939, 69], [73239, 69], [73539, 69], [73839, 69], [74139, 69], [74440, 68], [74740, 68], [75040, 68], [75341, 66], [75641, 66], [75941, 66], [76241, 66], [76542, 65], [76842, 65], [77142, 65], [77443, 64], [77743, 63], [78044, 62], [78344, 62], [78644, 62], [78945, 61], [79245, 61], [79546, 60], [79847, 59], [80148, 57], [80448, 57], [80749, 56], [81050, 55], [81351, 54], [81653, 52], [81954, 51], [82255, 50], [82556, 49], [82858, 46], [83159, 45], [83460, 44], [83762, 42], [84064, 40], [84365, 39], [84667, 37], [84969, 35], [85271, 32], [85573, 30], [85878, 28], [86179, 29], [86479, 31], [86780, 23], [86804, 6], [87080, 23], [87105, 5], [87381, 22], [87682, 21], [87982, 20]], "point": [168, 156]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.01|+00.95|+00.62", "placeStationary": true, "receptacleObjectId": "CounterTop|-01.01|+00.94|-00.04"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 113, 299, 223], "mask": [[33600, 2], [33696, 98], [33803, 98], [33996, 96], [34103, 97], [34296, 94], [34404, 96], [34596, 92], [34706, 94], [34896, 91], [35007, 93], [35196, 91], [35308, 92], [35496, 90], [35608, 92], [35796, 89], [35909, 91], [36095, 90], [36209, 91], [36395, 90], [36509, 91], [36695, 90], [36810, 90], [36994, 91], [37110, 90], [37294, 91], [37409, 32], [37454, 46], [37594, 91], [37709, 31], [37755, 45], [37894, 91], [38009, 31], [38055, 45], [38193, 91], [38309, 31], [38355, 45], [38493, 91], [38609, 32], [38656, 44], [38793, 91], [38908, 33], [38956, 44], [39093, 91], [39208, 33], [39256, 44], [39393, 91], [39508, 33], [39557, 43], [39692, 92], [39808, 34], [39857, 43], [39992, 92], [40108, 34], [40157, 43], [40292, 92], [40407, 35], [40458, 42], [40592, 91], [40707, 35], [40758, 42], [40891, 92], [41007, 36], [41058, 42], [41191, 92], [41307, 36], [41358, 42], [41491, 92], [41607, 37], [41658, 42], [41791, 92], [41906, 41], [41955, 45], [42090, 93], [42206, 44], [42253, 47], [42390, 94], [42506, 45], [42554, 46], [42690, 94], [42806, 45], [42854, 46], [42990, 95], [43105, 47], [43155, 45], [43289, 97], [43405, 47], [43455, 45], [43589, 98], [43704, 49], [43755, 45], [43889, 100], [44002, 51], [44056, 44], [44189, 102], [44300, 54], [44356, 44], [44488, 166], [44657, 43], [44788, 167], [44957, 43], [45088, 167], [45258, 42], [45388, 168], [45558, 42], [45688, 168], [45859, 41], [45987, 170], [46159, 41], [46287, 170], [46459, 41], [46587, 171], [46760, 40], [46887, 171], [47060, 40], [47186, 173], [47361, 39], [47486, 172], [47662, 38], [47786, 173], [47963, 37], [48086, 173], [48263, 37], [48385, 174], [48563, 37], [48685, 175], [48864, 36], [48985, 175], [49164, 36], [49285, 175], [49464, 36], [49585, 176], [49765, 35], [49885, 176], [50065, 36], [50185, 176], [50366, 35], [50408, 70], [50484, 178], [50666, 36], [50707, 71], [50784, 178], [50966, 297], [51267, 296], [51567, 296], [51867, 297], [52168, 296], [52468, 296], [52769, 296], [53069, 296], [53369, 297], [53670, 296], [53970, 296], [54270, 297], [54571, 296], [54871, 12029]], "point": [149, 167]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan30", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": 1.75, "y": 0.9304675, "z": -1.5}, "object_poses": [{"objectName": "Ladle_ff5a3b19", "position": {"x": 3.18144274, "y": 1.50150359, "z": -1.599825}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_ff5a3b19", "position": {"x": -0.9910205, "y": 0.5551319, "z": 1.60589182}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "ButterKnife_db774b98", "position": {"x": -0.8683, "y": 0.326634049, "z": 1.25590825}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_20771abe", "position": {"x": -0.991696239, "y": 0.901016653, "z": -0.6402203}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Glassbottle_20771abe", "position": {"x": 0.105427817, "y": 0.901016653, "z": -1.43042028}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Kettle_449c396f", "position": {"x": -0.2394, "y": 0.902399957, "z": -1.0895}, "rotation": {"x": 0.0, "y": 315.000153, "z": 0.0}}, {"objectName": "Kettle_449c396f", "position": {"x": -0.4203, "y": 0.902399957, "z": -0.9203}, "rotation": {"x": 0.0, "y": 134.999832, "z": 0.0}}, {"objectName": "Pot_7a7d333f", "position": {"x": 3.20006251, "y": 0.900943637, "z": -0.5990837}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_64c860f2", "position": {"x": -0.950113654, "y": 0.341591835, "z": 1.60589182}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Spatula_64c860f2", "position": {"x": 0.622025, "y": 0.9259599, "z": 0.7275939}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Spoon_0fe289e3", "position": {"x": -0.123990804, "y": 0.323789716, "z": -1.25940931}, "rotation": {"x": 0.0, "y": 135.0, "z": 0.0}}, {"objectName": "Spoon_0fe289e3", "position": {"x": 3.07000017, "y": 0.748487055, "z": 0.165224791}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_12a59a81", "position": {"x": -0.8683, "y": 0.131640509, "z": 0.6642}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_bf099a5c", "position": {"x": -0.909206867, "y": 0.323989272, "z": 0.16358766}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_c39806f6", "position": {"x": -0.909206867, "y": 0.5142952, "z": 1.34340417}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_c39806f6", "position": {"x": 2.91544056, "y": 0.173548162, "z": 0.84102875}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_6d2fd9f8", "position": {"x": 0.179621279, "y": 0.8966061, "z": -1.37360859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_5121dd32", "position": {"x": 0.20406355, "y": 1.26885355, "z": -1.6727}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c285ee2", "position": {"x": 3.11145973, "y": 0.7998927, "z": -0.100778744}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_be22aed5", "position": {"x": 0.889355838, "y": 0.9059324, "z": 0.249637455}, "rotation": {"x": 0.0, "y": 36.8105049, "z": 0.0}}, {"objectName": "Cup_be22aed5", "position": {"x": -0.7254907, "y": 0.8981324, "z": -0.8039087}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Knife_3e58a77d", "position": {"x": -0.9910205, "y": 0.7323958, "z": -0.186395854}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_bf5eb4b5", "position": {"x": 0.8088945, "y": 1.68780029, "z": -1.55020607}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_20771abe", "position": {"x": 3.044557, "y": 1.46528029, "z": -1.6488}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_5121dd32", "position": {"x": -1.00817108, "y": 0.909403, "z": 0.176187515}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_ed324e47", "position": {"x": 1.008, "y": 1.28645647, "z": -1.44805622}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_12a59a81", "position": {"x": 0.897994161, "y": 0.91150856, "z": 0.6087306}, "rotation": {"x": 0.0, "y": 36.8105049, "z": 0.0}}, {"objectName": "Ladle_ff5a3b19", "position": {"x": -0.9910205, "y": 0.167829067, "z": 1.5183959}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_7a7d333f", "position": {"x": 0.4232557, "y": 0.90096, "z": 0.869321465}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_449c396f", "position": {"x": 1.22986388, "y": 0.9091413, "z": 0.6057565}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_64c860f2", "position": {"x": -0.86270237, "y": 0.9152999, "z": 0.399594069}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_3e58a77d", "position": {"x": -0.789968, "y": 0.9259184, "z": 0.623000562}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_99ab2e9c", "position": {"x": -1.00817108, "y": 0.965891, "z": 0.846407056}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_be22aed5", "position": {"x": -0.2322971, "y": 1.26144266, "z": -1.55225611}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "SaltShaker_c39806f6", "position": {"x": 0.61148417, "y": 0.90726614, "z": 0.457591474}, "rotation": {"x": 0.0, "y": 36.8105049, "z": 0.0}}, {"objectName": "Egg_7c285ee2", "position": {"x": -1.00817108, "y": 0.9528351, "z": 0.623000562}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_6d2fd9f8", "position": {"x": 2.97177649, "y": 0.173548162, "z": 0.489106685}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_db774b98", "position": {"x": 0.5336, "y": 0.90814203, "z": 0.4267226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_1e1bef89", "position": {"x": -0.09992669, "y": 0.9741304, "z": -1.63577485}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Pan_000ae861", "position": {"x": 0.341, "y": 0.8731569, "z": -1.649}, "rotation": {"x": 0.0, "y": 167.065247, "z": 0.0}}, {"objectName": "Bread_1e8ab5c8", "position": {"x": 0.716874, "y": 0.9898338, "z": 0.135014385}, "rotation": {"x": 0.0, "y": 36.8105049, "z": 0.0}}, {"objectName": "CellPhone_bf099a5c", "position": {"x": -0.950113654, "y": 0.515589237, "z": 0.489208221}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_3cec4766", "position": {"x": 0.2850523, "y": 0.320265383, "z": -1.5157}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_bfd17fb4", "position": {"x": 1.0861237, "y": 0.923085749, "z": -1.44805622}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_0fe289e3", "position": {"x": 0.5139215, "y": 0.5132551, "z": -1.5157}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_46f611f6", "position": {"x": 1.16011488, "y": 1.67199731, "z": -1.48049784}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_ba5c7501", "position": {"x": -1.11901546, "y": 0.9926588, "z": 1.52397466}, "rotation": {"x": 0.0, "y": 179.999847, "z": 0.0}}], "object_toggles": [], "random_seed": 3800780538, "scene_num": 30}, "task_id": "trial_T20190909_130325_005206", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2871R3LEPWMMK_33C7UALJVOPFT64VB0CJRX6JWWH81Q", "high_descs": ["Turn left, hang a right at the stove and walk to the counter on the left by the mirror.", "Pick up the egg to the left of the head of lettuce on the counter.", "Turn right and walk to the microwave on the left.", "Put the egg inside the microwave, heat it, remove it and close the door.", "Turn left and walk to the counter with the toaster near the mirror on the right.", "Place the heated egg near the edge of the counter to the right of the toaster."], "task_desc": "Place a heated egg on a counter.", "votes": [1, 1]}, {"assignment_id": "A31681CCEVDIH3_3DQQ64TANJCT0ZJFU53F69R3HKKWPT", "high_descs": ["Walk around the counter to the countertop next to the microwave.", "Pick up the egg from the countertop.", "Turn right to walk to the microwave.", "Heat up the egg in the microwave, removing it afterwards.", "Turn back to the counter to the left of the microwave.", "Set the egg down on the countertop."], "task_desc": "Heat up an egg in the microwave to set it down on the countertop.", "votes": [1, 1]}, {"assignment_id": "ANBWJZYU2A68T_3TMFV4NEPB58U7KY8O14KNWDS9TW8G", "high_descs": ["Turn left, move a few steps, turn right, move past the toaster, and turn left to face the counter to the left of the microwave", "Pick up the egg from the counter", "Turn to your right, take a step, and face the microwave", "Heat the egg for a few seconds in the microwave and take it out", "Go back to the counter with the toaster", "Place the egg on the counter"], "task_desc": "Place a warmed egg on the counter", "votes": [1, 1]}]}}