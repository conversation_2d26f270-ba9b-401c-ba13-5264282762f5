{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000200.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000201.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000202.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000203.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000204.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000205.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000206.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000207.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000208.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000209.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000210.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000211.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000212.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 26}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "SideTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|1|5|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [3.7773812, 3.7773812, 4.95396232, 4.95396232, 4.82079028, 4.82079028]], "coordinateReceptacleObjectId": ["SideTable", [4.088, 4.088, 3.4844, 3.4844, 4.9732, 4.9732]], "forceVisible": true, "objectId": "Cup|+00.94|+01.21|+01.24"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|3|1|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|1|4|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "sidetable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [3.7773812, 3.7773812, 4.95396232, 4.95396232, 4.82079028, 4.82079028]], "coordinateReceptacleObjectId": ["SideTable", [4.088, 4.088, 3.4844, 3.4844, 4.9732, 4.9732]], "forceVisible": true, "objectId": "Cup|+00.94|+01.21|+01.24", "receptacleObjectId": "SideTable|+01.02|+01.24|+00.87"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+00.94|+01.21|+01.24"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [136, 87, 170, 135], "mask": [[25947, 14], [26244, 20], [26541, 25], [26840, 27], [27138, 31], [27438, 32], [27737, 33], [28036, 35], [28336, 35], [28636, 35], [28936, 35], [29236, 35], [29536, 35], [29836, 35], [30136, 35], [30436, 35], [30737, 34], [31037, 34], [31337, 33], [31638, 32], [31938, 31], [32239, 29], [32540, 27], [32840, 27], [33141, 25], [33441, 25], [33741, 25], [34042, 23], [34342, 23], [34642, 23], [34943, 21], [35243, 21], [35543, 21], [35843, 21], [36143, 21], [36443, 21], [36742, 22], [37042, 22], [37342, 22], [37642, 22], [37942, 22], [38242, 22], [38543, 21], [38843, 21], [39144, 19], [39445, 17], [39746, 15], [40046, 14], [40347, 4], [40356, 3]], "point": [153, 110]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 106], [50560, 47], [50734, 101], [50865, 42], [51034, 98], [51168, 39], [51335, 93], [51472, 35], [51635, 91], [51774, 33], [51935, 89], [52076, 31], [52235, 87], [52378, 28], [52536, 85], [52679, 27], [52836, 83], [52981, 25], [53136, 82], [53282, 24], [53437, 79], [53584, 22], [53737, 78], [53885, 21], [54037, 77]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+00.94|+01.21|+01.24", "placeStationary": true, "receptacleObjectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 214], [35100, 214], [35400, 214], [35700, 214], [36000, 214], [36300, 213], [36600, 213], [36900, 213], [37200, 213], [37500, 213], [37800, 213], [38100, 213], [38400, 213], [38700, 212], [39000, 212], [39300, 212], [39600, 212], [39900, 212], [40200, 212], [40500, 212], [40800, 212], [41100, 211], [41400, 211], [41700, 211], [42000, 211], [42300, 211], [42600, 211], [42900, 211], [43200, 210], [43500, 210], [43800, 210], [44100, 210], [44400, 210], [44700, 210], [45000, 210], [45300, 210], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 140], [50560, 47], [50700, 135], [50865, 42], [51000, 132], [51168, 39], [51300, 128], [51472, 35], [51600, 126], [51774, 33], [51900, 124], [52076, 31], [52200, 122], [52378, 28], [52500, 43], [52679, 27], [52800, 42], [52981, 25], [53100, 41], [53282, 24], [53400, 41], [53584, 22], [53700, 40], [53885, 21], [54000, 40], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 34], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 22], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 4], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 126], [32542, 73], [32700, 123], [32844, 71], [33000, 120], [33146, 69], [33300, 119], [33448, 67], [33600, 118], [33748, 67], [33900, 117], [34049, 66], [34200, 117], [34349, 65], [34500, 117], [34649, 65], [34800, 117], [34949, 65], [35100, 118], [35249, 65], [35400, 118], [35549, 65], [35700, 118], [35848, 66], [36000, 118], [36148, 66], [36300, 119], [36447, 66], [36600, 119], [36747, 66], [36900, 120], [37046, 67], [37200, 120], [37346, 67], [37500, 121], [37646, 67], [37800, 121], [37945, 68], [38100, 122], [38245, 68], [38400, 122], [38545, 68], [38700, 122], [38844, 68], [39000, 123], [39144, 68], [39300, 123], [39444, 68], [39600, 123], [39744, 68], [39900, 124], [40044, 68], [40200, 124], [40344, 68], [40500, 124], [40644, 68], [40800, 124], [40944, 68], [41100, 124], [41244, 67], [41400, 124], [41543, 68], [41700, 124], [41844, 67], [42000, 124], [42144, 67], [42300, 124], [42444, 67], [42600, 124], [42744, 67], [42900, 124], [43044, 67], [43200, 124], [43344, 66], [43500, 124], [43644, 66], [43800, 124], [43944, 66], [44100, 125], [44243, 67], [44400, 125], [44543, 67], [44700, 125], [44843, 67], [45000, 131], [45136, 74], [45300, 132], [45436, 74], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 34], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 22], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 4], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+00.94|+01.21|+01.24"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [117, 109, 148, 152], "mask": [[32526, 16], [32823, 21], [33120, 26], [33419, 29], [33718, 30], [34017, 32], [34317, 32], [34617, 32], [34917, 32], [35218, 31], [35518, 31], [35818, 30], [36118, 30], [36419, 28], [36719, 28], [37020, 26], [37320, 26], [37621, 25], [37921, 24], [38222, 23], [38522, 23], [38822, 22], [39123, 21], [39423, 21], [39723, 21], [40024, 20], [40324, 20], [40624, 20], [40924, 20], [41224, 20], [41524, 19], [41824, 20], [42124, 20], [42424, 20], [42724, 20], [43024, 20], [43324, 20], [43624, 20], [43924, 20], [44225, 18], [44525, 18], [44825, 18], [45131, 5], [45432, 4]], "point": [132, 129]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 214], [35100, 214], [35400, 214], [35700, 214], [36000, 214], [36300, 213], [36600, 213], [36900, 213], [37200, 213], [37500, 213], [37800, 213], [38100, 213], [38400, 213], [38700, 212], [39000, 212], [39300, 212], [39600, 212], [39900, 212], [40200, 212], [40500, 212], [40800, 212], [41100, 211], [41400, 211], [41700, 211], [42000, 211], [42300, 211], [42600, 211], [42900, 211], [43200, 210], [43500, 210], [43800, 210], [44100, 210], [44400, 210], [44700, 210], [45000, 210], [45300, 210], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 138], [47562, 47], [47700, 133], [47867, 41], [48000, 129], [48171, 37], [48300, 127], [48473, 35], [48600, 124], [48776, 32], [48900, 122], [49078, 30], [49200, 120], [49380, 28], [49500, 118], [49682, 26], [49800, 117], [49983, 25], [50100, 115], [50285, 22], [50400, 114], [50586, 21], [50700, 112], [50888, 19], [51000, 111], [51189, 18], [51300, 110], [51490, 17], [51600, 109], [51791, 16], [51900, 108], [52092, 15], [52200, 108], [52392, 14], [52500, 43], [52693, 13], [52800, 42], [52994, 12], [53100, 41], [53294, 12], [53400, 41], [53595, 11], [53700, 40], [53896, 10], [54000, 40], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 34], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 22], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 4], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+00.94|+01.21|+01.24", "placeStationary": true, "receptacleObjectId": "SideTable|+01.02|+01.24|+00.87"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [40, 85, 299, 188], "mask": [[25278, 1], [25578, 2], [25878, 2], [26177, 4], [26477, 5], [26777, 5], [26802, 1], [27076, 6], [27102, 1], [27376, 6], [27401, 2], [27675, 6], [27701, 3], [27975, 6], [28001, 3], [28183, 2], [28275, 6], [28301, 3], [28483, 2], [28574, 6], [28601, 3], [28783, 3], [28874, 6], [28901, 3], [29084, 3], [29174, 6], [29202, 2], [29385, 2], [29473, 6], [29503, 2], [29685, 3], [29773, 6], [29804, 1], [29986, 3], [30072, 7], [30104, 1], [30287, 2], [30372, 6], [30587, 3], [30672, 6], [30888, 3], [30971, 7], [31189, 2], [31271, 6], [31489, 3], [31571, 6], [31790, 3], [31870, 7], [32091, 2], [32170, 6], [32391, 3], [32470, 6], [32692, 3], [32769, 7], [32993, 2], [33069, 6], [33293, 3], [33368, 7], [33594, 3], [33668, 7], [33895, 2], [33968, 6], [34140, 1], [34195, 3], [34267, 7], [34317, 20], [34430, 21], [34496, 3], [34567, 7], [34613, 29], [34727, 29], [34797, 2], [34867, 6], [34909, 36], [35024, 36], [35097, 3], [35166, 7], [35206, 42], [35321, 42], [35398, 2], [35466, 7], [35503, 47], [35619, 47], [35698, 2], [35765, 7], [35801, 51], [35917, 51], [35999, 1], [36065, 7], [36100, 54], [36216, 54], [36365, 7], [36398, 57], [36515, 57], [36664, 7], [36696, 61], [36814, 61], [36964, 7], [36994, 64], [37112, 65], [37264, 7], [37293, 66], [37412, 66], [37563, 7], [37592, 68], [37711, 68], [37863, 7], [37891, 70], [38011, 70], [38163, 7], [38190, 72], [38310, 72], [38462, 7], [38489, 74], [38610, 73], [38762, 7], [38788, 76], [38909, 76], [39061, 8], [39087, 77], [39209, 77], [39361, 7], [39386, 79], [39508, 79], [39661, 7], [39686, 79], [39808, 80], [39960, 8], [39985, 80], [40108, 81], [40260, 7], [40285, 81], [40408, 81], [40560, 7], [40584, 38], [40628, 38], [40708, 38], [40752, 38], [40859, 8], [40884, 36], [40930, 36], [41008, 36], [41054, 37], [41159, 7], [41183, 36], [41231, 36], [41308, 36], [41356, 36], [41458, 8], [41483, 35], [41532, 35], [41608, 36], [41657, 36], [41758, 8], [41782, 36], [41832, 35], [41908, 36], [41958, 35], [42058, 7], [42082, 36], [42132, 35], [42209, 35], [42258, 36], [42357, 8], [42382, 36], [42431, 36], [42509, 36], [42558, 36], [42657, 8], [42682, 37], [42730, 37], [42810, 37], [42858, 36], [42957, 7], [42982, 39], [43028, 39], [43110, 39], [43156, 39], [43256, 8], [43282, 84], [43411, 84], [43556, 8], [43582, 84], [43711, 84], [43856, 7], [43882, 84], [44011, 85], [44155, 8], [44182, 84], [44312, 84], [44455, 8], [44482, 84], [44612, 84], [44754, 8], [44782, 83], [44913, 83], [45054, 8], [45083, 82], [45214, 82], [45354, 8], [45383, 81], [45515, 81], [45653, 8], [45684, 79], [45816, 80], [45953, 8], [45984, 79], [46117, 79], [46253, 8], [46285, 77], [46418, 77], [46552, 8], [46585, 76], [46719, 76], [46852, 8], [46886, 75], [47020, 75], [47151, 9], [47186, 74], [47321, 74], [47451, 8], [47487, 51], [47622, 72], [47751, 8], [47788, 45], [47924, 69], [48050, 9], [48089, 40], [48226, 67], [48350, 8], [48390, 37], [48527, 65], [48650, 8], [48691, 33], [48829, 62], [48949, 9], [48993, 29], [49130, 60], [49249, 8], [49294, 26], [49432, 57], [49549, 8], [49595, 23], [49734, 54], [49848, 9], [49897, 20], [50037, 49], [50148, 8], [50200, 15], [50340, 44], [50447, 9], [50502, 12], [50643, 39], [50747, 9], [50805, 7], [50945, 35], [51047, 8], [51107, 4], [51248, 30], [51346, 9], [51557, 13], [51646, 54], [51946, 62], [52245, 63], [52392, 9], [52545, 62], [52693, 59], [52844, 62], [52994, 106], [53144, 62], [53294, 106], [53444, 61], [53595, 105], [53743, 61], [53896, 104], [54043, 61], [54196, 104], [54343, 60], [54497, 103], [54642, 61], [54797, 103], [54942, 61], [55097, 103], [55242, 61], [55397, 103], [55541, 62], [55697, 103], [55841, 62], [55997, 103], [56140, 63], [56297, 103]], "point": [166, 137]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan3", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -0.25, "y": 1.12401652, "z": 0.5}, "object_poses": [{"objectName": "Potato_e8912d85", "position": {"x": 0.9822707, "y": 1.9858737, "z": 1.78890634}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "DishSponge_a15664d1", "position": {"x": -1.38003659, "y": 1.32256436, "z": -2.9817524}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": -1.6140399, "y": 0.3447429, "z": 0.211331308}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": -1.66056263, "y": 1.32354856, "z": 0.120481014}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.6457262, "y": 0.4350211, "z": -1.086478}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": 0.766335964, "y": 1.33799994, "z": -1.312883}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_930b560d", "position": {"x": -0.2394, "y": 1.3317, "z": -3.3019}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": -1.68100739, "y": 0.321009457, "z": 2.316678}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_f306e730", "position": {"x": 0.900508642, "y": 0.34236908, "z": -1.75116086}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": 0.976771951, "y": 0.345585465, "z": -2.1643877}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -1.53081059, "y": 1.35270047, "z": -2.20134926}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": 1.285346, "y": 1.35270047, "z": -1.312883}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -1.68255043, "y": 0.344005942, "z": 1.42058778}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -1.54634547, "y": 0.344005942, "z": 0.110898912}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": -1.23730922, "y": 1.35692108, "z": -3.203495}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": -1.66056263, "y": 1.35692108, "z": 0.445294976}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": -1.88644969, "y": 0.3421222, "z": 1.52389443}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": 0.5634017, "y": 1.32130814, "z": -2.78936434}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b13b6e3e", "position": {"x": -1.52276421, "y": 1.31800008, "z": -3.42523718}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": -1.40105689, "y": 1.39739525, "z": 1.094923}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": -1.81848335, "y": 0.33881402, "z": 1.11066759}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": 0.965999663, "y": 1.3713994, "z": -1.53499949}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": -1.634, "y": 0.32906872, "z": 2.193}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": -1.79031563, "y": 1.39739525, "z": 1.094923}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_930b560d", "position": {"x": -0.6309, "y": 1.3317, "z": -3.3019}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -1.9200685, "y": 1.35270047, "z": 1.4197371}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": 0.9777746, "y": 1.75552821, "z": 2.28940654}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Potato_e8912d85", "position": {"x": -1.53080976, "y": 1.36275363, "z": 0.445294976}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -1.40105808, "y": 1.32319188, "z": -1.94141138}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": -2.04982138, "y": 1.32354856, "z": 2.069365}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_f306e730", "position": {"x": 0.9443453, "y": 1.20519757, "z": 1.23849058}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": -1.6140399, "y": 0.344855428, "z": 0.5126284}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": 1.19326913, "y": 1.24862444, "z": 1.25445545}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_9d168802", "position": {"x": -1.88114953, "y": 0.327762932, "z": -2.03532434}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b13b6e3e", "position": {"x": 1.27624369, "y": 1.2028, "z": 1.2225256}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": -1.665155, "y": 0.3447429, "z": -1.9633193}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": -1.8914665, "y": 1.39760256, "z": -1.05691934}, "rotation": {"x": 14.9525652, "y": 0.658896446, "z": 355.878357}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.56123328, "y": 0.4350211, "z": -1.086478}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_ddd73f57", "position": {"x": -1.52276421, "y": 1.42728543, "z": -2.76001024}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": -1.82631838, "y": 1.09791088, "z": -0.5422326}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": -1.817123, "y": 0.344428062, "z": 0.211331308}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_c15f34b3", "position": {"x": 0.6347654, "y": 1.31800008, "z": -3.34372234}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a15664d1", "position": {"x": -1.954416, "y": 0.343378425, "z": 1.317281}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": -2.00278068, "y": 1.325272, "z": -1.18448138}, "rotation": {"x": 1.70944774, "y": 2.774898, "z": 359.491882}}, {"objectName": "Apple_e84ad935", "position": {"x": -2.077612, "y": 1.37356687, "z": -1.18665552}, "rotation": {"x": -0.00562774437, "y": -3.67383182e-05, "z": 340.013428}}, {"objectName": "Mug_259b36f2", "position": {"x": -1.6980927, "y": 1.04604959, "z": -0.587838531}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 165981904, "scene_num": 3}, "task_id": "trial_T20190908_211022_646179", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A3UF6XXFFRR237_3JBT3HLQFBTXUUOK5SNM5DLLYA4PZ4", "high_descs": ["go to the microwave", "pick up the green cup from the counter", "approach the microwave with the cup", "put the cup in the microwave, warm, then remove", "take the cup back to the counter", "set the cup on the corner of the counter"], "task_desc": "get the green cup, warm it in the microwave, then put it on the corner of the counter", "votes": [1, 1]}, {"assignment_id": "AFU00NU09CFXE_3LPW2N6LKWTCFLCRFQF0QF1P5SPU5Z", "high_descs": ["Turn around and move to the microwave cart, which is to the right of the fridge.", "Pick up the green cup from the cart.", "Carry the cup a step to your right, so you're closer to the handle of the microwave.", "Place the cup in the microwave and then remove once heated.", "Carry the cup a step to your left, so you're still in front of the cart, but closer to the fridge.", "Place the cup on the cart in the front corner closest to the fridge."], "task_desc": "Heat a green cup in the microwave.", "votes": [1, 1]}, {"assignment_id": "A1S1K7134S2VUC_3PQ8K71NH0BP6IAA4HPQYP5HQ2QAAZ", "high_descs": ["Turn right, move to the kitchen cart, left of the microwave.", "Pick up the cup from the kitchen cart.", "Turn right, move to the kitchen cart, in front of the microwave.", "Put the cup in the microwave, cook it, pick it back up.", "Turn left, move to the kitchen cart, left of the microwave.", "Put the cup on the kitchen cart."], "task_desc": "<PERSON> a empty cup and return it to the cart.", "votes": [1, 1]}]}}