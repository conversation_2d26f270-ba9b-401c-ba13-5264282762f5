{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000212.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 41}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "CounterTop", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-2|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-4.47874116, -4.47874116, -4.9645624, -4.9645624, 3.3667964, 3.3667964]], "coordinateReceptacleObjectId": ["SinkBasin", [-5.3792, -5.3792, -5.0, -5.0, 3.356, 3.356]], "forceVisible": true, "objectId": "Cup|-01.12|+00.84|-01.24"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|-2|2|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [1.132, 1.132, -5.3248, -5.3248, 3.6003664, 3.6003664]], "forceVisible": true, "objectId": "Microwave|+00.28|+00.90|-01.33"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-1|-2|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "countertop"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-4.47874116, -4.47874116, -4.9645624, -4.9645624, 3.3667964, 3.3667964]], "coordinateReceptacleObjectId": ["CounterTop", [0.284, 0.284, -4.796, -4.796, 3.7872, 3.7872]], "forceVisible": true, "objectId": "Cup|-01.12|+00.84|-01.24", "receptacleObjectId": "CounterTop|+00.07|+00.95|-01.20"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.12|+00.84|-01.24"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [169, 111, 194, 157], "mask": [[33179, 7], [33476, 13], [33774, 17], [34073, 20], [34372, 22], [34672, 22], [34972, 23], [35272, 23], [35571, 24], [35871, 24], [36171, 24], [36471, 24], [36771, 24], [37071, 24], [37371, 24], [37671, 23], [37971, 23], [38271, 23], [38572, 22], [38872, 21], [39172, 21], [39473, 19], [39773, 18], [40074, 16], [40375, 14], [40676, 12], [40978, 9], [41280, 2], [41580, 2], [41880, 2], [42180, 2], [42474, 10], [42772, 14], [43071, 17], [43370, 19], [43669, 21], [43969, 21], [44269, 22], [44569, 22], [44869, 22], [45169, 22], [45470, 21], [45770, 20], [46071, 18], [46373, 15], [46675, 11], [46978, 5]], "point": [181, 133]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.12|+00.84|-01.24", "placeStationary": true, "receptacleObjectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 4, 252, 267], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15322, 229], [15620, 230], [15919, 231], [16219, 231], [16518, 232], [16817, 232], [17116, 233], [17415, 234], [17715, 234], [18014, 235], [18313, 236], [18612, 236], [18911, 237], [19211, 237], [19510, 237], [19809, 238], [20108, 239], [20407, 240], [20707, 239], [21006, 240], [21305, 241], [21604, 241], [21903, 242], [22203, 242], [22502, 243], [22801, 243], [23100, 244], [23400, 244], [23700, 243], [24000, 243], [24300, 243], [24600, 243], [24900, 242], [25200, 242], [25500, 242], [25800, 241], [26100, 241], [26400, 241], [26700, 241], [27000, 240], [27300, 240], [27600, 240], [27900, 240], [28200, 239], [28500, 239], [28800, 239], [29100, 238], [29400, 238], [29700, 238], [30000, 238], [30300, 237], [30600, 237], [30900, 237], [31200, 236], [31500, 236], [31800, 236], [32100, 236], [32400, 235], [32700, 235], [33000, 235], [33300, 234], [33600, 234], [33900, 234], [34200, 234], [34500, 233], [34800, 233], [35100, 233], [35400, 232], [35700, 232], [36000, 232], [36300, 232], [36600, 231], [36900, 231], [37200, 231], [37500, 230], [37800, 230], [38100, 230], [38400, 230], [38700, 229], [39000, 229], [39300, 229], [39600, 228], [39900, 228], [40200, 228], [40500, 228], [40800, 227], [41100, 227], [41400, 227], [41700, 226], [42000, 226], [42300, 225], [42600, 226], [42900, 225], [43200, 225], [43500, 225], [43800, 56], [43858, 166], [44100, 56], [44158, 166], [44400, 56], [44458, 166], [44700, 55], [44759, 165], [45000, 55], [45300, 54], [45600, 54], [45900, 54], [46200, 31], [46233, 20], [46500, 31], [46534, 19], [46800, 30], [46834, 19], [47100, 30], [47134, 18], [47400, 30], [47434, 18], [47700, 30], [47734, 18], [48000, 30], [48033, 18], [48300, 31], [48332, 19], [48600, 50], [48900, 50], [49200, 50], [49500, 49], [49800, 49], [50100, 49], [50400, 48], [50700, 48], [51000, 48], [51300, 47], [51600, 47], [51900, 46], [52200, 46], [52500, 46], [52800, 45], [53100, 45], [53400, 45], [53700, 44], [54000, 44], [54300, 43], [54600, 43], [54900, 43], [55200, 42], [55500, 42], [55800, 42], [56100, 41], [56400, 41], [56700, 41], [57000, 40], [57300, 40], [57600, 39], [57900, 39], [58200, 39], [58500, 38], [58800, 38], [59100, 38], [59400, 37], [59700, 37], [60000, 37], [60300, 36], [60600, 36], [60900, 35], [61200, 35], [61500, 35], [61800, 34], [62100, 34], [62400, 34], [62700, 33], [63000, 33], [63300, 33], [63600, 32], [63900, 32], [64200, 31], [64500, 31], [64800, 31], [65100, 30], [65400, 30], [65700, 30], [66000, 29], [66300, 29], [66600, 28], [66900, 28], [67200, 28], [67500, 27], [67800, 27], [68100, 27], [68400, 26], [68700, 26], [69000, 26], [69300, 25], [69600, 25], [69900, 24], [70200, 24], [70500, 24], [70800, 23], [71100, 23], [71400, 23], [71700, 22], [72000, 22], [72300, 22], [72600, 21], [72900, 21], [73200, 20], [73500, 20], [73800, 20], [74100, 19], [74400, 19], [74700, 19], [75000, 18], [75300, 18], [75600, 18], [75900, 17], [76200, 17], [76500, 16], [76800, 16], [77100, 16], [77400, 15], [77700, 17], [78000, 18], [78300, 19], [78600, 19], [78900, 14], [78915, 2], [79200, 13], [79500, 13], [79800, 12]], "point": [126, 134]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 4, 252, 267], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15322, 229], [15620, 230], [15919, 231], [16219, 231], [16518, 232], [16817, 232], [17116, 233], [17415, 234], [17715, 234], [18014, 235], [18313, 236], [18612, 236], [18911, 237], [19211, 237], [19510, 237], [19809, 133], [19963, 84], [20108, 133], [20264, 83], [20407, 133], [20565, 82], [20707, 133], [20865, 81], [21006, 133], [21165, 81], [21305, 134], [21466, 80], [21604, 135], [21766, 79], [21903, 136], [22066, 79], [22203, 136], [22366, 79], [22502, 137], [22666, 79], [22801, 138], [22966, 78], [23100, 139], [23266, 78], [23400, 139], [23565, 79], [23700, 140], [23865, 78], [24000, 140], [24165, 78], [24300, 140], [24465, 78], [24600, 140], [24764, 79], [24900, 141], [25064, 78], [25200, 141], [25364, 78], [25500, 141], [25663, 79], [25800, 142], [25963, 78], [26100, 142], [26262, 79], [26400, 143], [26562, 79], [26700, 143], [26861, 80], [27000, 144], [27160, 80], [27300, 145], [27459, 81], [27600, 147], [27758, 82], [27900, 149], [28056, 84], [28200, 151], [28353, 86], [28500, 151], [28653, 86], [28800, 151], [28953, 86], [29100, 151], [29253, 85], [29400, 151], [29553, 85], [29700, 151], [29853, 85], [30000, 151], [30153, 85], [30300, 151], [30453, 84], [30600, 149], [30756, 81], [30900, 145], [31059, 78], [31200, 143], [31361, 75], [31500, 142], [31662, 74], [31800, 141], [31963, 73], [32100, 141], [32264, 72], [32400, 140], [32564, 71], [32700, 140], [32865, 70], [33000, 140], [33164, 71], [33300, 140], [33464, 70], [33600, 141], [33764, 70], [33900, 142], [34063, 71], [34200, 143], [34362, 72], [34500, 145], [34660, 73], [34800, 147], [34957, 76], [35100, 233], [35400, 232], [35700, 232], [36000, 232], [36300, 232], [36600, 231], [36900, 231], [37200, 231], [37500, 230], [37800, 230], [38100, 230], [38400, 230], [38700, 229], [39000, 229], [39300, 229], [39600, 228], [39900, 228], [40200, 228], [40500, 228], [40800, 227], [41100, 227], [41400, 227], [41700, 226], [42000, 226], [42300, 225], [42600, 226], [42900, 225], [43200, 225], [43500, 225], [43800, 56], [43858, 166], [44100, 56], [44158, 166], [44400, 56], [44458, 166], [44700, 55], [44759, 165], [45000, 55], [45300, 54], [45600, 54], [45900, 54], [46200, 31], [46233, 20], [46500, 31], [46534, 19], [46800, 30], [46834, 19], [47100, 30], [47134, 18], [47400, 30], [47434, 18], [47700, 30], [47734, 18], [48000, 30], [48033, 18], [48300, 31], [48332, 19], [48600, 50], [48900, 50], [49200, 50], [49500, 49], [49800, 49], [50100, 49], [50400, 48], [50700, 48], [51000, 48], [51300, 47], [51600, 47], [51900, 46], [52200, 46], [52500, 46], [52800, 45], [53100, 45], [53400, 45], [53700, 44], [54000, 44], [54300, 43], [54600, 43], [54900, 43], [55200, 42], [55500, 42], [55800, 42], [56100, 41], [56400, 41], [56700, 41], [57000, 40], [57300, 40], [57600, 39], [57900, 39], [58200, 39], [58500, 38], [58800, 38], [59100, 38], [59400, 37], [59700, 37], [60000, 37], [60300, 36], [60600, 36], [60900, 35], [61200, 35], [61500, 35], [61800, 34], [62100, 34], [62400, 34], [62700, 33], [63000, 33], [63300, 33], [63600, 32], [63900, 32], [64200, 31], [64500, 31], [64800, 31], [65100, 30], [65400, 30], [65700, 30], [66000, 29], [66300, 29], [66600, 28], [66900, 28], [67200, 28], [67500, 27], [67800, 27], [68100, 27], [68400, 26], [68700, 26], [69000, 26], [69300, 25], [69600, 25], [69900, 24], [70200, 24], [70500, 24], [70800, 23], [71100, 23], [71400, 23], [71700, 22], [72000, 22], [72300, 22], [72600, 21], [72900, 21], [73200, 20], [73500, 20], [73800, 20], [74100, 19], [74400, 19], [74700, 19], [75000, 18], [75300, 18], [75600, 18], [75900, 17], [76200, 17], [76500, 16], [76800, 16], [77100, 16], [77400, 15], [77700, 17], [78000, 18], [78300, 19], [78600, 19], [78900, 14], [78915, 2], [79200, 13], [79500, 13], [79800, 12]], "point": [126, 134]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [1.132, 1.132, -5.3248, -5.3248, 3.6003664, 3.6003664]], "forceVisible": true, "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [1.132, 1.132, -5.3248, -5.3248, 3.6003664, 3.6003664]], "forceVisible": true, "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.12|+00.84|-01.24"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [139, 67, 165, 117], "mask": [[19942, 21], [20241, 23], [20540, 25], [20840, 25], [21139, 26], [21439, 27], [21739, 27], [22039, 27], [22339, 27], [22639, 27], [22939, 27], [23239, 27], [23539, 26], [23840, 25], [24140, 25], [24440, 25], [24740, 24], [25041, 23], [25341, 23], [25641, 22], [25942, 21], [26242, 20], [26543, 19], [26843, 18], [27144, 16], [27445, 14], [27747, 11], [28049, 7], [28351, 2], [28651, 2], [28951, 2], [29251, 2], [29551, 2], [29851, 2], [30151, 2], [30451, 2], [30749, 7], [31045, 14], [31343, 18], [31642, 20], [31941, 22], [32241, 23], [32540, 24], [32840, 25], [33140, 24], [33440, 24], [33741, 23], [34042, 21], [34343, 19], [34645, 15], [34947, 10]], "point": [152, 91]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 4, 252, 267], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15322, 229], [15620, 230], [15919, 231], [16219, 231], [16518, 232], [16817, 232], [17116, 233], [17415, 234], [17715, 234], [18014, 235], [18313, 236], [18612, 236], [18911, 237], [19211, 237], [19510, 237], [19809, 238], [20108, 239], [20407, 240], [20707, 239], [21006, 240], [21305, 241], [21604, 241], [21903, 242], [22203, 242], [22502, 243], [22801, 243], [23100, 244], [23400, 244], [23700, 243], [24000, 243], [24300, 243], [24600, 243], [24900, 242], [25200, 242], [25500, 242], [25800, 241], [26100, 241], [26400, 241], [26700, 241], [27000, 240], [27300, 240], [27600, 240], [27900, 240], [28200, 239], [28500, 239], [28800, 239], [29100, 238], [29400, 238], [29700, 238], [30000, 238], [30300, 237], [30600, 237], [30900, 237], [31200, 236], [31500, 236], [31800, 236], [32100, 236], [32400, 235], [32700, 235], [33000, 235], [33300, 234], [33600, 234], [33900, 234], [34200, 234], [34500, 233], [34800, 233], [35100, 233], [35400, 232], [35700, 232], [36000, 232], [36300, 232], [36600, 231], [36900, 231], [37200, 231], [37500, 230], [37800, 230], [38100, 230], [38400, 230], [38700, 229], [39000, 229], [39300, 229], [39600, 228], [39900, 228], [40200, 228], [40500, 228], [40800, 227], [41100, 227], [41400, 227], [41700, 226], [42000, 226], [42300, 225], [42600, 226], [42900, 225], [43200, 225], [43500, 225], [43800, 56], [43858, 166], [44100, 56], [44158, 166], [44400, 56], [44458, 166], [44700, 55], [44759, 165], [45000, 55], [45300, 54], [45600, 54], [45900, 54], [46200, 31], [46233, 20], [46500, 31], [46534, 19], [46800, 30], [46834, 19], [47100, 30], [47134, 18], [47400, 30], [47434, 18], [47700, 30], [47734, 18], [48000, 30], [48033, 18], [48300, 31], [48332, 19], [48600, 50], [48900, 50], [49200, 50], [49500, 49], [49800, 49], [50100, 49], [50400, 48], [50700, 48], [51000, 48], [51300, 47], [51600, 47], [51900, 46], [52200, 46], [52500, 46], [52800, 45], [53100, 45], [53400, 45], [53700, 44], [54000, 44], [54300, 43], [54600, 43], [54900, 43], [55200, 42], [55500, 42], [55800, 42], [56100, 41], [56400, 41], [56700, 41], [57000, 40], [57300, 40], [57600, 39], [57900, 39], [58200, 39], [58500, 38], [58800, 38], [59100, 38], [59400, 37], [59700, 37], [60000, 37], [60300, 36], [60600, 36], [60900, 35], [61200, 35], [61500, 35], [61800, 34], [62100, 34], [62400, 34], [62700, 33], [63000, 33], [63300, 33], [63600, 32], [63900, 32], [64200, 31], [64500, 31], [64800, 31], [65100, 30], [65400, 30], [65700, 30], [66000, 29], [66300, 29], [66600, 28], [66900, 28], [67200, 28], [67500, 27], [67800, 27], [68100, 27], [68400, 26], [68700, 26], [69000, 26], [69300, 25], [69600, 25], [69900, 24], [70200, 24], [70500, 24], [70800, 23], [71100, 23], [71400, 23], [71700, 22], [72000, 22], [72300, 22], [72600, 21], [72900, 21], [73200, 20], [73500, 20], [73800, 20], [74100, 19], [74400, 19], [74700, 19], [75000, 18], [75300, 18], [75600, 18], [75900, 17], [76200, 17], [76500, 16], [76800, 16], [77100, 16], [77400, 15], [77700, 17], [78000, 18], [78300, 19], [78600, 19], [78900, 14], [78915, 2], [79200, 13], [79500, 13], [79800, 12]], "point": [126, 134]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.12|+00.84|-01.24", "placeStationary": true, "receptacleObjectId": "CounterTop|+00.07|+00.95|-01.20"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 96, 299, 228], "mask": [[28599, 201], [28899, 201], [29200, 200], [29500, 193], [29799, 79], [29894, 98], [30099, 76], [30199, 93], [30399, 73], [30503, 89], [30699, 71], [30805, 87], [30998, 71], [31106, 87], [31298, 69], [31408, 85], [31598, 67], [31709, 85], [31898, 66], [32010, 85], [32197, 66], [32311, 84], [32497, 65], [32612, 84], [32797, 64], [32913, 84], [33097, 64], [33214, 83], [33396, 64], [33515, 83], [33696, 63], [33816, 83], [33996, 63], [34116, 83], [34296, 62], [34417, 83], [34596, 62], [34718, 82], [34895, 62], [35018, 82], [35195, 62], [35319, 81], [35495, 62], [35619, 81], [35795, 61], [35920, 80], [36094, 62], [36220, 80], [36394, 62], [36520, 80], [36694, 62], [36821, 79], [36994, 62], [37121, 79], [37293, 63], [37421, 79], [37593, 63], [37722, 78], [37893, 63], [38022, 78], [38193, 63], [38322, 78], [38492, 64], [38622, 78], [38792, 65], [38922, 78], [39092, 65], [39222, 78], [39392, 65], [39522, 78], [39691, 67], [39822, 78], [39991, 67], [40122, 78], [40291, 68], [40421, 79], [40591, 69], [40721, 79], [40890, 70], [41021, 79], [41190, 71], [41321, 79], [41490, 72], [41620, 80], [41790, 73], [41920, 80], [42089, 75], [42219, 81], [42389, 76], [42518, 82], [42689, 77], [42817, 83], [42989, 79], [43116, 84], [43289, 80], [43414, 86], [43588, 84], [43712, 42], [43756, 44], [43888, 86], [44010, 38], [44059, 41], [44188, 89], [44307, 38], [44361, 39], [44488, 93], [44603, 41], [44662, 38], [44787, 102], [44894, 48], [44963, 278], [45263, 278], [45564, 276], [45864, 276], [46164, 276], [46465, 275], [46765, 165], [46934, 106], [47064, 165], [47231, 4], [47241, 99], [47364, 168], [47536, 5], [47549, 91], [47664, 165], [47832, 4], [47896, 45], [47963, 170], [48137, 5], [48155, 9], [48198, 43], [48263, 167], [48434, 4], [48452, 21], [48498, 44], [48562, 172], [48738, 4], [48751, 32], [48797, 46], [48861, 178], [49049, 95], [49160, 185], [49346, 99], [49458, 190], [49652, 96], [49756, 186], [49958, 278], [50264, 270], [50566, 266], [50868, 261], [51171, 256], [51473, 252], [51775, 248], [52077, 245], [52378, 243], [52679, 241], [52980, 239], [53281, 238], [53581, 237], [53882, 235], [54183, 233], [54484, 231], [54785, 229], [55086, 227], [55387, 226], [55687, 226], [55987, 225], [56288, 224], [56588, 224], [56888, 223], [57189, 222], [57489, 221], [57790, 220], [58090, 220], [58390, 219], [58691, 218], [58991, 218], [59291, 218], [59591, 218], [59891, 218], [60191, 218], [60491, 218], [60791, 218], [61091, 218], [61391, 218], [61691, 218], [61991, 218], [62291, 218], [62591, 218], [62891, 218], [63191, 218], [63491, 218], [63791, 218], [64091, 219], [64390, 220], [64690, 220], [64990, 220], [65290, 220], [65590, 220], [65890, 220], [66190, 221], [66489, 222], [66789, 223], [67088, 224], [67388, 224], [67688, 225], [67987, 226], [68287, 113]], "point": [149, 163]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan22", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": 0.5, "y": 0.9009992, "z": 2.5}, "object_poses": [{"objectName": "Potato_5ea35e17", "position": {"x": -0.598039, "y": 0.9335, "z": -1.119659}, "rotation": {"x": 0.0, "y": 47.53002, "z": 0.0}}, {"objectName": "CreditCard_35b71b93", "position": {"x": -2.52118, "y": 0.908296, "z": 0.02159518}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_1cf3b23c", "position": {"x": 0.240374386, "y": 1.11274135, "z": 1.17194486}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Kettle_1cf3b23c", "position": {"x": -0.6579682, "y": 1.66351271, "z": -1.36000526}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_86f597c8", "position": {"x": -2.6225, "y": 0.9287, "z": -0.3964}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_4b9198b3", "position": {"x": -2.60430479, "y": 0.9124294, "z": 0.1906048}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Plate_ad96aa4f", "position": {"x": 0.688191056, "y": 0.913163841, "z": -1.199}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_e02c5e35", "position": {"x": -2.920978, "y": 1.70842516, "z": 1.09428716}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Knife_1fa5d324", "position": {"x": -2.499291, "y": 0.7699078, "z": 0.226726711}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_1fa5d324", "position": {"x": -2.438055, "y": 0.9343805, "z": -0.0629096553}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_f346af22", "position": {"x": -2.10609436, "y": 1.66372752, "z": -1.39770389}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_f346af22", "position": {"x": -2.84877825, "y": 2.04494977, "z": 1.370662}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9ffe4e75", "position": {"x": -2.80053973, "y": 1.72271085, "z": 0.7712375}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Tomato_9ffe4e75", "position": {"x": 0.6636004, "y": 1.161149, "z": 0.902641356}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "PepperShaker_f59054f9", "position": {"x": 0.6108486, "y": 0.9076061, "z": -1.03210258}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f88244ed", "position": {"x": 1.02924943, "y": 1.145, "z": 0.209302634}, "rotation": {"x": 0.0, "y": 228.424088, "z": 0.0}}, {"objectName": "Mug_f88244ed", "position": {"x": -1.49493659, "y": 1.65747142, "z": -1.36000526}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a99f93a6", "position": {"x": -2.61988378, "y": 1.52573359, "z": 0.9327631}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pan_304c8d90", "position": {"x": -2.59, "y": 0.9, "z": -1.147}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a99f93a6", "position": {"x": -2.75755763, "y": 0.089363575, "z": 1.39676857}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_b9d165af", "position": {"x": -0.27649647, "y": 0.901713431, "z": -1.11815751}, "rotation": {"x": 0.154637411, "y": -0.00114239077, "z": 359.698547}}, {"objectName": "Kettle_1cf3b23c", "position": {"x": -1.41871345, "y": 0.85265696, "z": -1.19472015}, "rotation": {"x": 1.40334183e-14, "y": 3.208973e-43, "z": 2.89269767e-27}}, {"objectName": "CreditCard_35b71b93", "position": {"x": -2.54226041, "y": 0.7438233, "z": 0.163269192}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_81a93477", "position": {"x": -0.401600122, "y": 0.9483048, "z": -1.29149938}, "rotation": {"x": -6.95604249e-05, "y": 5.9807604e-05, "z": -6.607245e-05}}, {"objectName": "Plate_ad96aa4f", "position": {"x": -1.048614, "y": 1.66433537, "z": -1.39770389}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_f346af22", "position": {"x": -1.45504689, "y": 0.11854583, "z": -1.06876659}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_3b50a8bf", "position": {"x": -2.55216455, "y": 0.922, "z": 0.0104080439}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e2bf19ed", "position": {"x": -2.6608, "y": 0.8744, "z": 0.9831}, "rotation": {"x": 31.0465031, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_4f98d325", "position": {"x": -2.695, "y": 0.9, "z": -0.132}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9ffe4e75", "position": {"x": -2.92097664, "y": 1.523436, "z": 0.690474749}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "PepperShaker_f59054f9", "position": {"x": -1.87934422, "y": 1.65877759, "z": -1.473101}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_dc49bd4c", "position": {"x": -2.22248077, "y": 0.9, "z": -0.997339845}, "rotation": {"x": 0.0, "y": 323.977722, "z": 0.0}}, {"objectName": "Pot_86f597c8", "position": {"x": 0.47126016, "y": 1.11294746, "z": 1.40283}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Potato_5ea35e17", "position": {"x": -0.598039, "y": 0.9335, "z": -1.119659}, "rotation": {"x": 0.0, "y": 47.53002, "z": 0.0}}, {"objectName": "DishSponge_9171dd8d", "position": {"x": -1.221, "y": 0.8416841, "z": -1.1264}, "rotation": {"x": 0.0, "y": 41.1644974, "z": 0.0}}, {"objectName": "Egg_e02c5e35", "position": {"x": -2.6801033, "y": 1.5090251, "z": 1.09428787}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spoon_4b9198b3", "position": {"x": 0.3594984, "y": 0.747329533, "z": -1.05214345}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_1fa5d324", "position": {"x": -2.71413827, "y": 0.7696693, "z": 0.036354214}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_46773746", "position": {"x": -1.11968529, "y": 0.8416991, "z": -1.2411406}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f88244ed", "position": {"x": -2.438055, "y": 0.9063, "z": 0.359614462}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_b7a8ccec", "position": {"x": -2.046209, "y": 0.9, "z": -1.16398358}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 527652194, "scene_num": 22}, "task_id": "trial_T20190909_022057_173698", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1ELPYAFO7MANS_36AHBNMV1U3O07BP4XU8RCVUE0JDYF", "high_descs": ["Turn around and walk to the sink.", "Pick up the yellow cup in the sink.", "Take a few steps left and face the microwave.", "Heat the cup in the microwave.", "Take a step right.", "Place the cup on the counter in front of the bread and fork."], "task_desc": "Place a heated cup on a counter.", "votes": [1, 1]}, {"assignment_id": "A3PPLDHC3CG0YN_3PMBY0YE2AUZUEX3WP7H8PKLKRQ9CQ", "high_descs": ["Walk over to the sink on the other side of the room. ", "Pick up the wine glass from the sink. ", "Walk over to the microwave on the counter to the left of the dishwasher. ", "Open the microwave door, put the glass in the microwave, close the door, heat up the glass, take the glass out of the microwave, and close the door. ", "Walk to the counter to the right of the microwave. ", "Place the wine glass down at the edge of the counter. "], "task_desc": "To heat a glass and put it on the edge of the counter. ", "votes": [1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_3SBEHTYCWQU58IRBWBVJI9B765LIYG", "high_descs": ["turn left and walk over to the kitchen sink which is on the left side of the room now, at the end of the room", "grab a cup out of the kitchen sink", "turn left and walk over to the microwave on the kitchen counter on the right up ahead", "place the cup inside the microwave, microwave it and then take it back out", "move to the right just a little bit to face the kitchen counter to the right of the microwave", "place the cup down on top of the kitchen counter there"], "task_desc": "place a microwaved cup down on the kitchen counter", "votes": [1, 1]}]}}