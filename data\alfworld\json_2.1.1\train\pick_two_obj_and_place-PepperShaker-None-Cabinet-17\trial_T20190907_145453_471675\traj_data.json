{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000120.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000123.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000156.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000157.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000158.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000159.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000160.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000161.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000162.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000163.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000164.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000165.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000166.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000167.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000168.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000169.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000170.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000171.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000172.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000173.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000174.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000175.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000176.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000179.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000180.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000181.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000207.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000208.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000209.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000210.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000211.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000212.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000213.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000214.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000215.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000216.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000217.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000218.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000219.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000220.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000221.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000222.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000223.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000224.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000225.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000226.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000227.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000228.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000229.png", "low_idx": 39}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON><PERSON><PERSON><PERSON>", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-2|0|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["peppershaker"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON><PERSON><PERSON><PERSON>", [-3.254747392, -3.254747392, -2.3579532, -2.3579532, 3.6336248, 3.6336248]], "coordinateReceptacleObjectId": ["CounterTop", [-4.0076, -4.0076, -2.676, -2.676, 3.7904, 3.7904]], "forceVisible": true, "objectId": "<PERSON><PERSON><PERSON><PERSON>|-00.81|+00.91|-00.59"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|0|5|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["peppershaker", "cabinet"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON><PERSON><PERSON><PERSON>", [-3.254747392, -3.254747392, -2.3579532, -2.3579532, 3.6336248, 3.6336248]], "coordinateReceptacleObjectId": ["Cabinet", [3.955002548, 3.955002548, 2.326399804, 2.326399804, 1.610000132, 1.610000132]], "forceVisible": true, "objectId": "<PERSON><PERSON><PERSON><PERSON>|-00.81|+00.91|-00.59", "receptacleObjectId": "Cabinet|+00.99|+00.40|+00.58"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-2|10|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["peppershaker"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON><PERSON><PERSON><PERSON>", [-5.16206216, -5.16206216, 9.836, 9.836, 2.98372984, 2.98372984]], "coordinateReceptacleObjectId": ["DiningTable", [-4.8448, -4.8448, 9.836, 9.836, 3.1376, 3.1376]], "forceVisible": true, "objectId": "<PERSON><PERSON><PERSON><PERSON>|-01.29|+00.75|+02.46"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|0|5|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["peppershaker", "cabinet"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON><PERSON><PERSON><PERSON>", [-5.16206216, -5.16206216, 9.836, 9.836, 2.98372984, 2.98372984]], "coordinateReceptacleObjectId": ["Cabinet", [3.955002548, 3.955002548, 2.326399804, 2.326399804, 1.610000132, 1.610000132]], "forceVisible": true, "objectId": "<PERSON><PERSON><PERSON><PERSON>|-01.29|+00.75|+02.46", "receptacleObjectId": "Cabinet|+00.99|+00.40|+00.58"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "<PERSON><PERSON><PERSON><PERSON>|-00.81|+00.91|-00.59"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [233, 143, 253, 173], "mask": [[42846, 5], [43145, 8], [43445, 9], [43745, 9], [44044, 10], [44343, 11], [44642, 11], [44941, 12], [45241, 12], [45540, 13], [45840, 13], [46139, 14], [46439, 14], [46738, 14], [47038, 14], [47337, 15], [47637, 15], [47936, 15], [48236, 15], [48535, 16], [48835, 16], [49134, 16], [49434, 16], [49734, 16], [50034, 16], [50333, 16], [50634, 15], [50935, 14], [51236, 13], [51537, 11], [51840, 6]], "point": [243, 157]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|+00.99|+00.40|+00.58"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [205, 135, 281, 203], "mask": [[40422, 59], [40721, 60], [41021, 61], [41321, 60], [41621, 60], [41920, 60], [42220, 60], [42520, 59], [42820, 59], [43119, 59], [43419, 59], [43719, 58], [44019, 58], [44318, 59], [44618, 58], [44918, 58], [45218, 57], [45517, 58], [45817, 57], [46117, 57], [46417, 56], [46716, 57], [47016, 56], [47316, 56], [47615, 56], [47915, 56], [48215, 55], [48515, 55], [48814, 55], [49114, 55], [49414, 54], [49714, 54], [50013, 54], [50313, 54], [50613, 53], [50913, 53], [51212, 53], [51512, 53], [51812, 52], [52112, 52], [52411, 52], [52711, 52], [53011, 52], [53311, 51], [53610, 52], [53910, 51], [54210, 51], [54509, 51], [54809, 51], [55109, 50], [55409, 50], [55708, 50], [56008, 50], [56308, 49], [56608, 49], [56907, 49], [57207, 49], [57507, 48], [57807, 48], [58106, 48], [58406, 48], [58706, 47], [59006, 47], [59305, 47], [59605, 47], [59905, 46], [60205, 46], [60505, 45], [60805, 45]], "point": [243, 168]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "<PERSON><PERSON><PERSON><PERSON>|-00.81|+00.91|-00.59", "placeStationary": true, "receptacleObjectId": "Cabinet|+00.99|+00.40|+00.58"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [205, 135, 299, 245], "mask": [[40423, 58], [40722, 61], [41022, 62], [41322, 62], [41622, 63], [41921, 64], [42221, 65], [42521, 65], [42820, 67], [43120, 67], [43420, 68], [43720, 68], [44019, 70], [44319, 70], [44619, 71], [44919, 72], [45218, 73], [45518, 74], [45818, 74], [46118, 75], [46417, 76], [46717, 77], [47017, 77], [47317, 78], [47616, 79], [47916, 80], [48216, 81], [48515, 82], [48815, 83], [49115, 83], [49415, 84], [49714, 85], [50014, 86], [50314, 86], [50614, 86], [50913, 87], [51213, 87], [51513, 87], [51813, 87], [52112, 88], [52412, 88], [52712, 88], [53012, 88], [53311, 89], [53611, 89], [53911, 89], [54210, 90], [54510, 90], [54810, 90], [55110, 90], [55409, 91], [55709, 91], [56009, 91], [56309, 91], [56608, 92], [56908, 92], [57208, 92], [57508, 92], [57807, 93], [58107, 93], [58407, 93], [58706, 93], [59006, 92], [59306, 92], [59606, 91], [59905, 91], [60248, 48], [60548, 47], [60848, 46], [61148, 45], [61449, 44], [61749, 43], [62049, 42], [62350, 41], [62650, 40], [62950, 39], [63251, 38], [63551, 37], [63851, 36], [64152, 34], [64452, 34], [64752, 33], [65053, 31], [65353, 31], [65653, 30], [65953, 29], [66254, 28], [66554, 27], [66854, 26], [67155, 25], [67455, 24], [67755, 23], [68056, 21], [68356, 21], [68656, 20], [68957, 18], [69257, 18], [69557, 17], [69858, 15], [70158, 15], [70458, 14], [70758, 13], [71059, 11], [71359, 11], [71659, 10], [71960, 8], [72260, 8], [72560, 7], [72861, 5], [73161, 5], [73461, 4]], "point": [252, 189]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|+00.99|+00.40|+00.58"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [205, 135, 299, 245], "mask": [[40423, 58], [40722, 61], [41022, 62], [41322, 62], [41622, 63], [41921, 64], [42221, 65], [42521, 65], [42820, 67], [43120, 67], [43420, 68], [43720, 68], [44019, 70], [44319, 70], [44619, 71], [44919, 72], [45218, 73], [45518, 74], [45818, 74], [46118, 75], [46417, 76], [46717, 77], [47017, 77], [47317, 78], [47616, 79], [47916, 80], [48216, 81], [48515, 82], [48815, 83], [49115, 83], [49415, 84], [49714, 85], [50014, 86], [50314, 86], [50614, 86], [50913, 87], [51213, 87], [51513, 87], [51813, 87], [52112, 88], [52412, 88], [52712, 88], [53012, 88], [53311, 89], [53611, 89], [53911, 89], [54210, 90], [54510, 90], [54810, 90], [55110, 5], [55119, 81], [55409, 6], [55420, 80], [55709, 5], [55719, 81], [56009, 5], [56019, 81], [56309, 4], [56319, 81], [56608, 5], [56619, 81], [56908, 4], [56919, 81], [57208, 4], [57219, 81], [57508, 3], [57519, 81], [57807, 4], [57819, 81], [58107, 4], [58119, 81], [58407, 4], [58418, 82], [58706, 6], [58718, 81], [59006, 7], [59017, 81], [59306, 92], [59606, 91], [59905, 91], [60248, 48], [60548, 47], [60848, 46], [61148, 45], [61449, 44], [61749, 43], [62049, 42], [62350, 41], [62650, 40], [62950, 39], [63251, 38], [63551, 37], [63851, 36], [64152, 34], [64452, 34], [64752, 33], [65053, 31], [65353, 31], [65653, 30], [65953, 29], [66254, 28], [66554, 27], [66854, 26], [67155, 25], [67455, 24], [67755, 23], [68056, 21], [68356, 21], [68656, 20], [68957, 18], [69257, 18], [69557, 17], [69858, 15], [70158, 15], [70458, 14], [70758, 13], [71059, 11], [71359, 11], [71659, 10], [71960, 8], [72260, 8], [72560, 7], [72861, 5], [73161, 5], [73461, 4]], "point": [252, 189]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "<PERSON><PERSON><PERSON><PERSON>|-01.29|+00.75|+02.46"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [135, 138, 146, 162], "mask": [[41239, 2], [41537, 6], [41837, 6], [42137, 6], [42437, 6], [42737, 7], [43036, 8], [43336, 9], [43636, 9], [43936, 9], [44235, 10], [44535, 10], [44835, 11], [45135, 11], [45435, 11], [45735, 11], [46035, 11], [46335, 11], [46635, 12], [46935, 12], [47235, 12], [47535, 11], [47836, 10], [48136, 9], [48439, 3]], "point": [140, 149]}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|+00.99|+00.40|+00.58"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [205, 135, 281, 203], "mask": [[40422, 59], [40721, 60], [41021, 61], [41321, 60], [41621, 60], [41920, 60], [42220, 60], [42520, 59], [42820, 59], [43119, 59], [43419, 59], [43719, 58], [44019, 58], [44318, 59], [44618, 58], [44918, 58], [45218, 57], [45517, 58], [45817, 57], [46117, 57], [46417, 56], [46716, 57], [47016, 56], [47316, 56], [47615, 56], [47915, 56], [48215, 55], [48515, 55], [48814, 55], [49114, 55], [49414, 54], [49714, 54], [50013, 54], [50313, 54], [50613, 53], [50913, 53], [51212, 53], [51512, 53], [51812, 52], [52112, 52], [52411, 52], [52711, 52], [53011, 52], [53311, 51], [53610, 52], [53910, 51], [54210, 51], [54509, 51], [54809, 51], [55109, 50], [55409, 50], [55708, 50], [56008, 50], [56308, 49], [56608, 49], [56907, 49], [57207, 49], [57507, 48], [57807, 48], [58106, 48], [58406, 48], [58706, 47], [59006, 47], [59305, 47], [59605, 47], [59905, 46], [60205, 46], [60505, 45], [60805, 45]], "point": [243, 168]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "<PERSON><PERSON><PERSON><PERSON>|-01.29|+00.75|+02.46", "placeStationary": true, "receptacleObjectId": "Cabinet|+00.99|+00.40|+00.58"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [205, 135, 299, 245], "mask": [[40423, 58], [40722, 61], [41022, 62], [41322, 62], [41622, 63], [41921, 64], [42221, 65], [42521, 65], [42820, 67], [43120, 67], [43420, 68], [43720, 68], [44019, 70], [44319, 70], [44619, 71], [44919, 72], [45218, 73], [45518, 74], [45818, 74], [46118, 75], [46417, 76], [46717, 77], [47017, 77], [47317, 78], [47616, 79], [47916, 80], [48216, 81], [48515, 82], [48815, 83], [49115, 83], [49415, 84], [49714, 85], [50014, 86], [50314, 86], [50614, 86], [50913, 87], [51213, 87], [51513, 87], [51813, 87], [52112, 88], [52412, 88], [52712, 88], [53012, 88], [53311, 89], [53611, 89], [53911, 89], [54210, 90], [54510, 90], [54810, 90], [55110, 5], [55119, 81], [55409, 6], [55420, 80], [55709, 5], [55719, 81], [56009, 5], [56019, 81], [56309, 4], [56319, 81], [56608, 5], [56619, 81], [56908, 4], [56919, 81], [57208, 4], [57219, 81], [57508, 3], [57519, 81], [57807, 4], [57819, 81], [58107, 4], [58119, 81], [58407, 4], [58418, 82], [58706, 6], [58718, 81], [59006, 7], [59017, 81], [59306, 92], [59606, 91], [59905, 91], [60248, 48], [60548, 47], [60848, 46], [61148, 45], [61449, 44], [61749, 43], [62049, 42], [62350, 41], [62650, 40], [62950, 39], [63251, 38], [63551, 37], [63851, 36], [64152, 34], [64452, 34], [64752, 33], [65053, 31], [65353, 31], [65653, 30], [65953, 29], [66254, 28], [66554, 27], [66854, 26], [67155, 25], [67455, 24], [67755, 23], [68056, 21], [68356, 21], [68656, 20], [68957, 18], [69257, 18], [69557, 17], [69858, 15], [70158, 15], [70458, 14], [70758, 13], [71059, 11], [71359, 11], [71659, 10], [71960, 8], [72260, 8], [72560, 7], [72861, 5], [73161, 5], [73461, 4]], "point": [252, 189]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|+00.99|+00.40|+00.58"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [205, 135, 299, 245], "mask": [[40423, 58], [40722, 61], [41022, 62], [41322, 62], [41622, 63], [41921, 64], [42221, 65], [42521, 65], [42820, 67], [43120, 67], [43420, 68], [43720, 68], [44019, 70], [44319, 70], [44619, 71], [44919, 72], [45218, 73], [45518, 74], [45818, 74], [46118, 75], [46417, 76], [46717, 77], [47017, 77], [47317, 78], [47616, 79], [47916, 80], [48216, 81], [48515, 82], [48815, 83], [49115, 83], [49415, 84], [49714, 85], [50014, 86], [50314, 86], [50614, 86], [50913, 87], [51213, 87], [51513, 87], [51813, 87], [52112, 88], [52412, 88], [52712, 88], [53012, 88], [53311, 89], [53611, 89], [53911, 89], [54210, 90], [54510, 90], [54810, 90], [55110, 5], [55119, 8], [55130, 70], [55409, 6], [55420, 7], [55431, 69], [55709, 5], [55719, 7], [55731, 69], [56009, 5], [56019, 6], [56031, 69], [56309, 4], [56319, 6], [56331, 69], [56608, 5], [56619, 5], [56631, 69], [56908, 4], [56919, 4], [56930, 70], [57208, 4], [57219, 4], [57230, 70], [57508, 3], [57519, 3], [57530, 70], [57807, 4], [57819, 3], [57830, 70], [58107, 4], [58119, 3], [58130, 70], [58407, 4], [58418, 4], [58429, 71], [58706, 6], [58718, 5], [58729, 70], [59006, 7], [59017, 7], [59029, 69], [59306, 92], [59606, 91], [59905, 91], [60248, 48], [60548, 47], [60848, 46], [61148, 45], [61449, 44], [61749, 43], [62049, 42], [62350, 41], [62650, 40], [62950, 39], [63251, 38], [63551, 37], [63851, 36], [64152, 34], [64452, 34], [64752, 33], [65053, 31], [65353, 31], [65653, 30], [65953, 29], [66254, 28], [66554, 27], [66854, 26], [67155, 25], [67455, 24], [67755, 23], [68056, 21], [68356, 21], [68656, 20], [68957, 18], [69257, 18], [69557, 17], [69858, 15], [70158, 15], [70458, 14], [70758, 13], [71059, 11], [71359, 11], [71659, 10], [71960, 8], [72260, 8], [72560, 7], [72861, 5], [73161, 5], [73461, 4]], "point": [252, 189]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan17", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -0.25, "y": 0.908999562, "z": 0.0}, "object_poses": [{"objectName": "DishSponge_4b57442b", "position": {"x": 1.20097, "y": 0.885864258, "z": 2.477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_4b57442b", "position": {"x": 0.08650948, "y": 0.11620301, "z": -0.446127653}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_6018293c", "position": {"x": 1.26311541, "y": 2.15743446, "z": 0.5119468}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_c40d8d1f", "position": {"x": -1.01254034, "y": 0.7506676, "z": 2.273428}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_1d6d1e51", "position": {"x": -0.144537389, "y": 0.759695351, "z": -0.6125}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_9f8771c2", "position": {"x": 1.450345, "y": 0.886848569, "z": 2.692625}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_9f8771c2", "position": {"x": -1.19785714, "y": 0.7501748, "z": 2.83014464}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": 1.20097, "y": 0.9012999, "z": 2.692625}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_92502562", "position": {"x": 0.5029, "y": 0.9393, "z": -0.7406}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Egg_1ede3fc6", "position": {"x": 1.444309, "y": 0.9456665, "z": 0.7067195}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_1ede3fc6", "position": {"x": -1.383174, "y": 0.783066452, "z": 2.83014464}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_572193ee", "position": {"x": -1.32097435, "y": 0.75040555, "z": 0.7786249}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_572193ee", "position": {"x": -1.20685518, "y": 1.4205054, "z": 0.9880003}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Knife_268c1c72", "position": {"x": 1.28637147, "y": 0.9334927, "z": -0.144580841}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_57296338", "position": {"x": 1.20740271, "y": 0.961228848, "z": 0.1391859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_e0c21b7c", "position": {"x": 1.29790676, "y": 1.66186, "z": 0.2535392}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_74101882", "position": {"x": -1.01254034, "y": 0.813041151, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b6eaf348", "position": {"x": -1.383174, "y": 0.7452062, "z": 2.459}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b6eaf348", "position": {"x": -1.29051554, "y": 0.74593246, "z": 2.459}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c297f602", "position": {"x": -1.47583234, "y": 0.7439, "z": 2.459}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_7daf87a0", "position": {"x": -1.32900286, "y": 1.07565665, "z": -0.109642848}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_7daf87a0", "position": {"x": -1.43365359, "y": 0.972304, "z": -0.907535136}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_74101882", "position": {"x": 1.36722, "y": 0.9497149, "z": 2.261375}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_6018293c", "position": {"x": -1.3209759, "y": 0.7518743, "z": 1.30206239}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Fork_9f8771c2", "position": {"x": -1.01254034, "y": 0.7501748, "z": 1.90228331}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_c40d8d1f", "position": {"x": 1.03472006, "y": 0.8873413, "z": 2.15356255}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_57296338", "position": {"x": 1.35799479, "y": 0.135980517, "z": 1.44079888}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": 0.994427, "y": 2.15623665, "z": -0.7877707}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_e3d6b27e", "position": {"x": 1.09901655, "y": 0.773710251, "z": 0.4035492}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6438ff8b", "position": {"x": -1.383174, "y": 0.8205948, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_92502562", "position": {"x": 0.802, "y": 0.9393, "z": -0.7406}, "rotation": {"x": 0.0, "y": 89.9998245, "z": 0.0}}, {"objectName": "SoapBottle_e0c21b7c", "position": {"x": -1.26434183, "y": 0.118386388, "z": 0.28963995}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "WineBottle_6b7178a8", "position": {"x": -1.0601548, "y": 0.913028657, "z": -0.817232251}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": 1.28637147, "y": 0.9264999, "z": 0.1391859}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Bowl_7e041f70", "position": {"x": -0.503703535, "y": 0.91054064, "z": -0.5099766}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_7daf87a0", "position": {"x": -0.968678534, "y": 0.972304, "z": -0.828023434}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_572193ee", "position": {"x": -0.0824483, "y": 0.7578041, "z": -0.5731131}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_268c1c72", "position": {"x": 1.117845, "y": 0.9082927, "z": 2.36918736}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_1ede3fc6", "position": {"x": -1.20685577, "y": 0.7853241, "z": 1.19737518}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_8429d040", "position": {"x": 0.238430381, "y": 0.908406138, "z": -0.933621049}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b6eaf348", "position": {"x": -0.813686848, "y": 0.9084062, "z": -0.5894883}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_edec52fd", "position": {"x": -1.3209753, "y": 1.68188834, "z": 1.08649826}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": -1.10519874, "y": 0.74966836, "z": 3.015717}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_4b57442b", "position": {"x": 0.103818983, "y": 0.7581204, "z": -0.533726156}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_a0065d6c", "position": {"x": -1.332, "y": 0.9, "z": -0.699}, "rotation": {"x": 0.0, "y": 345.0, "z": 0.0}}, {"objectName": "Spoon_1d6d1e51", "position": {"x": 0.0417298935, "y": 0.7596856, "z": -0.65188694}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_4f8d0eee", "position": {"x": -0.170651615, "y": 0.116042852, "z": -0.5338761}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c297f602", "position": {"x": -1.27866185, "y": 0.9071, "z": -0.430464923}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 4149417531, "scene_num": 17}, "task_id": "trial_T20190907_145453_471675", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A3T9K2RY06UO14_3GA6AFUKOR5WBVX550VRRHUD3OX3HL", "high_descs": ["Turn and go to the counter by the sink.", "Pick up the salt shaker on the counter.", "Turn to the left and go to the cabinet under the counter.", "Put the salt shaker in the cabinet. ", "Turn around and go to the table.", "Pick up the salt shaker on the table. ", "Turn around and go to the cabinet under the counter.", "Put the salt shaker in the cabinet. "], "task_desc": "Move two salt shakers to the cabinets under the counter. ", "votes": [1, 1, 1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_32Z9ZLUT1O1LXKQROYFJFKSOYEKHOJ", "high_descs": ["look down and move to the right a bit", "grab the salt shaker off of the counter top", "turn around and walk over to the red garbage at the end of the room on the right", "place the salt shaker in the cabinet to the bottom right of the kitchen cabinet", "turn around and walk over to the kitchen counter table on the left", "grab the pepper shaker off of the table top there", "turn around and walk over to the garbage on the right at the end of the room", "place the pepper shaker in the same kitchen cabinet you put the salt shaker in"], "task_desc": "place objects inside of the kitchen cabinet", "votes": [1, 1, 1, 0]}, {"assignment_id": "A3MLUEOP3CCLXL_3A7Y0R2P2R54IZZ9DP50AAZWNBEXJW", "high_descs": ["Take a step to the right and face the counter to the right of the sink", "Pick up the salt shaker that is on the counter", "Turn around and walk to the fridge then take a right and talk to the counter across the room", "Open the second cabinet from the left and place the salt shaker in the cabinet then shut the door", "Turn around and walk to the white table across the room", "Pick up the left salt shaker", "Turn to your left and walk to the fridge the turn left and walk to the counter across the room", "Open the second cabinet from the left and then place the salt shaker to the right of the salt shaker that's already there then close the door"], "task_desc": "Place salt shakers in a cabinet", "votes": [1, 1]}, {"assignment_id": "AFPMG8TLP1TND_354P56DE9NK32NC8I6AG8CDVH907SV", "high_descs": ["turn right and walk straight towards the microwave then turn left towards the counter", "pick up the salt shaker next to the bowl", "turn around then walk straight and then turn right towards the garbage can, walk straight towards the counter and garbage can", "open the counter door on the right side and put the salt inside and shut the door", "turn left walk straight and then turn left towards the white table and walk straight", "pick up the salt shaker in front of the other salt shaker", "turn left then walk straight towards the fridge and then turn left towards the garbage can and counter and walk straight", "open the counter door on the right and put the salt next to the other salt and close the door"], "task_desc": "Pick up salt shakers, put them into the counter door, shut the door", "votes": [1, 1]}, {"assignment_id": "A6U2C66WQ7QQN_3PS7W85Z82J8V6DGM3DQWD0NEHJT9X", "high_descs": ["Take a couple steps to the right, Take the salt from the counter", "Turn around, head to the toaster", "Open the cabinet door under the toaster, Put the salt in the cabinet, close the door", "Turn around, head to the white table across the room", "Take the salt from the white table", "Turn around, head to the toaster", "Open the cabinet door under the toaster", "Put the salt in the cabinet, close the door"], "task_desc": "Put the two salt shakers in the cabinet under the toaster", "votes": [0, 1, 1, 1]}, {"assignment_id": "A2KAGFQU28JY43_3R3YRB5GRIK1022AEEH0X4X93E5UA3", "high_descs": ["Move over to your right so that you are in front of the dishwasher. ", "Pick up the pepper shaker on the counter, in front of the tomato. ", "Turn to your left and go to the end of the kitchen counter, towards the toaster. ", "Put the pepper shaker in the bottom, second to last, kitchen cupboard, below the toaster. ", "Turn around and go to the white table with the tea kettle on it. ", "Pick of the salt shaker, closest to the loaf of bread. ", "Turn around and walk back to the kitchen counter with the toaster on it. ", "Place the salt shaker in the bottom, second to last, kitchen cupboard, to the right of the pepper shaker. "], "task_desc": "Put the salt and pepper shakers in the kitchen cupboard. ", "votes": [1, 1]}]}}