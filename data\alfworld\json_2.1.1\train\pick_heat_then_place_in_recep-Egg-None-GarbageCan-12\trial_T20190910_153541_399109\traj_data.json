{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 1, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000033.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000034.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 2}, {"high_idx": 2, "image_name": "000000037.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000038.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000039.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000040.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000041.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000042.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000043.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000044.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000045.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000046.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000047.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000048.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000049.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 8}, {"high_idx": 3, "image_name": "000000094.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000243.png", "low_idx": 27}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "GarbageCan", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|4|5|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [5.55240536, 5.55240536, 5.47880936, 5.47880936, 3.5144244, 3.5144244]], "coordinateReceptacleObjectId": ["SinkBasin", [5.84550096, 5.84550096, 5.47880888, 5.47880888, 3.3322824, 3.3322824]], "forceVisible": true, "objectId": "Egg|+01.39|+00.88|+01.37"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|6|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|4|11|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "garbagecan"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [5.55240536, 5.55240536, 5.47880936, 5.47880936, 3.5144244, 3.5144244]], "coordinateReceptacleObjectId": ["GarbageCan", [5.80777024, 5.80777024, 11.65005396, 11.65005396, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|+01.39|+00.88|+01.37", "receptacleObjectId": "GarbageCan|+01.45|+00.00|+02.91"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+01.39|+00.88|+01.37"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [103, 148, 119, 165], "mask": [[44208, 6], [44506, 10], [44805, 12], [45105, 13], [45404, 15], [45704, 15], [46003, 16], [46303, 17], [46603, 17], [46903, 17], [47203, 17], [47503, 17], [47804, 16], [48104, 15], [48405, 14], [48706, 12], [49007, 10], [49308, 7]], "point": [111, 155]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+01.39|+00.88|+01.37", "placeStationary": true, "receptacleObjectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 123], [10930, 127], [11100, 122], [11231, 126], [11400, 120], [11532, 125], [11700, 120], [11833, 124], [12000, 119], [12134, 123], [12300, 118], [12435, 122], [12600, 118], [12735, 122], [12900, 117], [13036, 120], [13200, 117], [13336, 120], [13500, 116], [13637, 119], [13800, 116], [13937, 119], [14100, 116], [14238, 118], [14400, 115], [14538, 118], [14700, 115], [14838, 118], [15000, 115], [15139, 117], [15300, 115], [15439, 117], [15600, 114], [15739, 117], [15900, 114], [16039, 116], [16200, 114], [16339, 116], [16500, 114], [16640, 115], [16800, 114], [16940, 115], [17100, 114], [17240, 115], [17400, 114], [17540, 115], [17700, 114], [17840, 115], [18000, 114], [18140, 115], [18300, 114], [18440, 115], [18600, 114], [18740, 115], [18900, 115], [19039, 115], [19200, 115], [19339, 115], [19500, 115], [19639, 115], [19800, 116], [19939, 115], [20100, 116], [20238, 116], [20400, 116], [20538, 116], [20700, 117], [20837, 117], [21000, 118], [21137, 117], [21300, 118], [21436, 118], [21600, 119], [21735, 119], [21900, 120], [22035, 118], [22200, 121], [22334, 119], [22500, 122], [22632, 121], [22800, 124], [22931, 122], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [139, 63]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+01.39|+00.88|+01.37"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [114, 37, 139, 77], "mask": [[10923, 7], [11222, 9], [11520, 12], [11820, 13], [12119, 15], [12418, 17], [12718, 17], [13017, 19], [13317, 19], [13616, 21], [13916, 21], [14216, 22], [14515, 23], [14815, 23], [15115, 24], [15415, 24], [15714, 25], [16014, 25], [16314, 25], [16614, 26], [16914, 26], [17214, 26], [17514, 26], [17814, 26], [18114, 26], [18414, 26], [18714, 26], [19015, 24], [19315, 24], [19615, 24], [19916, 23], [20216, 22], [20516, 22], [20817, 20], [21118, 19], [21418, 18], [21719, 16], [22020, 15], [22321, 13], [22622, 10], [22924, 7]], "point": [126, 56]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+01.39|+00.88|+01.37", "placeStationary": true, "receptacleObjectId": "GarbageCan|+01.45|+00.00|+02.91"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [64, 137, 150, 241], "mask": [[40886, 55], [41182, 62], [41479, 69], [41778, 70], [42077, 72], [42376, 74], [42675, 75], [42975, 76], [43275, 76], [43575, 76], [43874, 77], [44174, 77], [44474, 77], [44774, 77], [45074, 77], [45374, 77], [45673, 78], [45973, 78], [46273, 78], [46573, 78], [46873, 78], [47173, 78], [47472, 79], [47772, 79], [48072, 79], [48372, 79], [48672, 79], [48971, 80], [49271, 80], [49571, 80], [49871, 80], [50171, 80], [50471, 80], [50770, 81], [51070, 81], [51370, 81], [51670, 81], [51970, 81], [52270, 81], [52569, 82], [52869, 82], [53169, 82], [53469, 82], [53769, 82], [54069, 82], [54368, 83], [54668, 83], [54968, 83], [55268, 83], [55568, 83], [55868, 48], [55926, 25], [56167, 48], [56227, 24], [56467, 46], [56529, 22], [56767, 45], [56830, 21], [57067, 44], [57130, 21], [57367, 43], [57431, 20], [57667, 43], [57731, 20], [57966, 43], [58032, 19], [58266, 43], [58332, 19], [58566, 43], [58632, 19], [58866, 43], [58932, 19], [59166, 43], [59232, 19], [59466, 43], [59532, 19], [59765, 44], [59832, 19], [60065, 44], [60132, 19], [60365, 44], [60432, 19], [60665, 45], [60732, 19], [60965, 45], [61032, 19], [61265, 46], [61332, 19], [61564, 48], [61632, 19], [61864, 48], [61932, 19], [62164, 49], [62232, 19], [62464, 49], [62532, 19], [62764, 50], [62831, 20], [63064, 51], [63130, 21], [63365, 50], [63430, 20], [63665, 51], [63728, 22], [63965, 84], [64266, 82], [64566, 82], [64868, 80], [65170, 78], [65473, 74], [65775, 72], [66077, 69], [66379, 67], [66682, 63], [66985, 59], [67287, 57], [67589, 54], [67891, 52], [68193, 50], [68494, 48], [68795, 47], [69095, 47], [69396, 46], [69697, 45], [69998, 44], [70298, 44], [70599, 43], [70900, 41], [71200, 41], [71501, 39], [71801, 38], [72104, 33]], "point": [107, 188]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan12", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 1.0, "y": 0.9009999, "z": 1.25}, "object_poses": [{"objectName": "Pan_9f7abbea", "position": {"x": -0.9242284, "y": 1.4989289, "z": 0.119000085}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Potato_22312ae0", "position": {"x": -1.01831, "y": 1.5319767, "z": 0.348953784}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": -1.0390892, "y": 1.82829392, "z": -0.08169362}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_d40bfead", "position": {"x": 1.2854023, "y": 0.9384485, "z": 0.9402925}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_d40bfead", "position": {"x": -0.7937801, "y": 0.938448548, "z": 0.765623748}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_733b0828", "position": {"x": 1.275738, "y": 0.7618369, "z": 2.4599247}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -1.109, "y": 0.9307289, "z": 1.4704}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": -0.804073453, "y": 0.0866650939, "z": 2.24127}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": -0.791773558, "y": 0.08874029, "z": 0.8450084}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_09f78d94", "position": {"x": 1.55062962, "y": 0.944003344, "z": 1.7985332}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": -0.840011239, "y": 0.196582675, "z": -0.04450339}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": 1.352982, "y": 0.07861984, "z": 0.988712549}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -1.16145623, "y": 1.49741375, "z": 2.186901}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -1.0813, "y": 1.49741375, "z": 2.246276}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Lettuce_f3700d17", "position": {"x": 1.66985226, "y": 1.02830076, "z": 0.8063456}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": -1.0011425, "y": 1.49610877, "z": 2.5241127}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Lettuce_f3700d17", "position": {"x": -1.12628, "y": 1.02830076, "z": 0.765623748}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_09f78d94", "position": {"x": 1.48997676, "y": 0.0695113838, "z": 2.91251349}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_20fd7fed", "position": {"x": -0.922979057, "y": 0.9906555, "z": 2.265522}, "rotation": {"x": 359.990417, "y": 302.472137, "z": 359.9812}}, {"objectName": "Fork_d40bfead", "position": {"x": -0.96002996, "y": 0.938448548, "z": 0.7656238}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -0.924227834, "y": 1.56053746, "z": -0.110953882}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": -0.971268654, "y": 1.31560028, "z": 0.04234863}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -1.109, "y": 0.9307289, "z": 1.8657}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_733b0828", "position": {"x": 1.36262441, "y": 0.959697366, "z": 1.58957636}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": -0.861997247, "y": 0.7685599, "z": 0.779062569}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": 1.76781189, "y": 0.9342061, "z": 1.88749206}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": -0.860395, "y": 0.615049839, "z": 0.0423489437}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": 1.28540039, "y": 0.9342062, "z": 2.31}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": -1.12628, "y": 0.9379421, "z": 0.8433119}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_22312ae0", "position": {"x": 1.40962565, "y": 0.9773547, "z": 1.71495056}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": 1.37759125, "y": 1.05943823, "z": 0.0120204445}, "rotation": {"x": 5.02878138e-05, "y": -5.42833041e-05, "z": -5.802093e-06}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": -1.04543281, "y": 0.9318707, "z": 2.15807319}, "rotation": {"x": 357.256378, "y": 359.913452, "z": 0.270402}}, {"objectName": "Egg_83ce1479", "position": {"x": 1.38810134, "y": 0.8786061, "z": 1.36970234}, "rotation": {"x": 1.40334191e-14, "y": 180.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": -0.9648363, "y": 0.8136976, "z": 0.0423486531}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pan_9f7abbea", "position": {"x": 1.3781, "y": 1.73753989, "z": 0.3929}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": -0.792800069, "y": 0.7476966, "z": 0.9829687}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": 1.59296286, "y": 0.9329, "z": 2.02297}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 384899673, "scene_num": 12}, "task_id": "trial_T20190910_153541_399109", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2KAGFQU28JY43_34S6N1K2ZYALCO9S7EO8OP8WXAHLH7", "high_descs": ["Step back from the sink. ", "Pick up the egg that is in the sink. ", "Go to the microwave a little to your left and above the sink. ", "Place the egg in the microwave, heat it up, then remove the egg from the microwave. ", "Turn to your left and go to the garbage can to your right, on the floor, at the end of the counter. ", "Place the egg in the garbage can. "], "task_desc": "Put a warm egg in the garbage can. ", "votes": [1, 1, 1]}, {"assignment_id": "A1G1OO4IWM8EP5_3TXMY6UCAHF5MDEPDRI3UKAAUP1CQI", "high_descs": ["Look down at the sink.", "Pick up the egg from the sink.", "Open the microwave door.", "Put the egg in the microwave above the sink.  Turn on.  Open the door and remove the egg.  Close the door.", "Turn left and walk to the edge of the kitchen counter to the trash can.", "Put egg in the trash can in the back left corner."], "task_desc": "Heat up egg and put in trash can.", "votes": [1, 1, 1]}, {"assignment_id": "A3C81THYYSBGVD_3C2NJ6JBKD87LY9V061GED6JDMWN2Q", "high_descs": ["Face the sink in front of you.", "Take the potato from the sink in front of you.", "Turn left then face the microwave on top of the sink to your right.", "Place the potato in the microwave and take it out.", "Turn left then face the metal bin on your right.", "Place the potato in the metal bin in front of you."], "task_desc": "Place a hot potato in the metal bin.", "votes": [1, 0, 1]}]}}