{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000193.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000194.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000195.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000196.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000197.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000198.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000199.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000200.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000201.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000202.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000203.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000204.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000205.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000206.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000268.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000269.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000270.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000271.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000272.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000273.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000274.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000275.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000277.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000278.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000279.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000280.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000281.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000282.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000284.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000285.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000286.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000287.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000288.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000289.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000290.png", "low_idx": 47}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Ladle", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-8|4|2|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["ladle"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Ladle", [-8.04541492, -8.04541492, 0.761312248, 0.761312248, 4.65627052, 4.65627052]], "coordinateReceptacleObjectId": ["CounterTop", [-9.1232, -9.1232, 1.516, 1.516, 4.6416, 4.6416]], "forceVisible": true, "objectId": "Ladle|-02.01|+01.16|+00.19"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-9|6|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["ladle", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Ladle", [-8.04541492, -8.04541492, 0.761312248, 0.761312248, 4.65627052, 4.65627052]], "coordinateReceptacleObjectId": ["Drawer", [-8.172122, -8.172122, 2.3791896, 2.3791896, 2.4331572, 2.4331572]], "forceVisible": true, "objectId": "Ladle|-02.01|+01.16|+00.19", "receptacleObjectId": "Drawer|-02.04|+00.61|+00.59"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-2|5|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["ladle"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Ladle", [-2.1552288, -2.1552288, 7.6828704, 7.6828704, 4.34526, 4.34526]], "coordinateReceptacleObjectId": ["DiningTable", [-2.48, -2.48, 9.944, 9.944, 0.0698738544, 0.0698738544]], "forceVisible": true, "objectId": "Ladle|-00.54|+01.09|+01.92"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-9|6|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["ladle", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Ladle", [-2.1552288, -2.1552288, 7.6828704, 7.6828704, 4.34526, 4.34526]], "coordinateReceptacleObjectId": ["Drawer", [-8.172122, -8.172122, 2.3791896, 2.3791896, 2.4331572, 2.4331572]], "forceVisible": true, "objectId": "Ladle|-00.54|+01.09|+01.92", "receptacleObjectId": "Drawer|-02.04|+00.61|+00.59"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Ladle|-02.01|+01.16|+00.19"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [143, 130, 167, 169], "mask": [[38852, 1], [39151, 3], [39451, 3], [39751, 3], [40051, 4], [40352, 3], [40652, 3], [40952, 3], [41252, 3], [41552, 3], [41852, 3], [42152, 3], [42452, 3], [42752, 3], [43052, 3], [43352, 4], [43652, 4], [43952, 4], [44252, 4], [44552, 4], [44853, 3], [45150, 6], [45446, 13], [45745, 17], [46044, 20], [46343, 22], [46643, 23], [46943, 24], [47243, 25], [47543, 25], [47844, 24], [48144, 24], [48445, 22], [48745, 22], [49046, 20], [49347, 18], [49648, 16], [49949, 14], [50251, 10], [50554, 4]], "point": [155, 148]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-02.04|+00.61|+00.59"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [48, 148, 151, 202], "mask": [[44149, 103], [44449, 103], [44748, 104], [45048, 104], [45349, 103], [45649, 103], [45949, 103], [46250, 102], [46550, 102], [46851, 101], [47151, 101], [47451, 101], [47752, 100], [48052, 100], [48353, 99], [48653, 99], [48953, 99], [49254, 97], [49554, 97], [49855, 96], [50155, 96], [50455, 96], [50756, 95], [51056, 95], [51356, 95], [51657, 94], [51957, 94], [52258, 93], [52558, 93], [52858, 93], [53159, 92], [53459, 92], [53760, 91], [54060, 91], [54360, 91], [54661, 90], [54961, 90], [55262, 89], [55562, 89], [55862, 89], [56163, 88], [56463, 88], [56764, 87], [57064, 87], [57364, 87], [57665, 86], [57965, 86], [58266, 85], [58566, 85], [58866, 85], [59167, 84], [59467, 84], [59767, 84], [60068, 83], [60368, 83]], "point": [99, 174]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Ladle|-02.01|+01.16|+00.19", "placeStationary": true, "receptacleObjectId": "Drawer|-02.04|+00.61|+00.59"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [27, 150, 151, 253], "mask": [[44755, 92], [45055, 92], [45354, 93], [45654, 93], [45954, 93], [46253, 94], [46553, 94], [46852, 95], [47152, 95], [47452, 95], [47751, 96], [48051, 96], [48351, 96], [48650, 98], [48950, 98], [49249, 99], [49549, 99], [49849, 99], [50148, 100], [50448, 100], [50748, 100], [51047, 101], [51347, 101], [51647, 101], [51946, 102], [52246, 102], [52545, 103], [52845, 103], [53145, 103], [53444, 104], [53744, 104], [54044, 104], [54343, 105], [54643, 105], [54943, 105], [55242, 106], [55542, 106], [55841, 107], [56141, 107], [56441, 107], [56740, 108], [57040, 108], [57340, 108], [57639, 109], [57939, 109], [58239, 109], [58538, 110], [58838, 110], [59137, 111], [59437, 111], [59737, 111], [60036, 112], [60328, 124], [60627, 125], [60927, 125], [61227, 125], [61527, 125], [61827, 125], [62128, 124], [62428, 124], [62729, 123], [63030, 122], [63330, 122], [63631, 121], [63931, 121], [64232, 120], [64533, 119], [64833, 119], [65134, 118], [65434, 118], [65735, 117], [66036, 116], [66336, 116], [66637, 115], [66937, 115], [67238, 114], [67539, 113], [67839, 113], [68140, 112], [68440, 112], [68741, 111], [69042, 110], [69342, 110], [69643, 109], [69943, 109], [70244, 108], [70545, 107], [70845, 107], [71146, 106], [71446, 106], [71747, 105], [72048, 104], [72348, 104], [72649, 24], [72682, 70], [72949, 17], [72988, 64], [73250, 13], [73291, 61], [73551, 9], [73594, 58], [73851, 6], [73896, 56], [74152, 3], [74197, 55], [74452, 1], [74498, 54], [74800, 52], [75101, 50], [75401, 50], [75702, 49]], "point": [89, 200]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-02.04|+00.61|+00.59"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [27, 150, 151, 253], "mask": [[44755, 92], [45055, 92], [45354, 93], [45654, 93], [45954, 93], [46253, 94], [46553, 94], [46852, 95], [47152, 95], [47452, 95], [47751, 96], [48051, 96], [48351, 96], [48650, 98], [48950, 98], [49249, 99], [49549, 99], [49849, 99], [50148, 100], [50448, 100], [50748, 100], [51047, 101], [51347, 101], [51647, 101], [51946, 102], [52246, 102], [52545, 103], [52845, 103], [53145, 103], [53444, 104], [53744, 104], [54044, 104], [54343, 105], [54643, 105], [54943, 42], [54989, 59], [55242, 43], [55291, 57], [55542, 42], [55592, 56], [55841, 43], [55893, 55], [56141, 43], [56193, 55], [56441, 43], [56494, 54], [56740, 44], [56794, 54], [57040, 43], [57095, 53], [57340, 43], [57395, 53], [57639, 44], [57695, 53], [57939, 44], [57995, 53], [58239, 44], [58295, 53], [58538, 45], [58595, 53], [58838, 45], [58895, 53], [59137, 46], [59195, 53], [59437, 47], [59494, 54], [59737, 47], [59794, 54], [60036, 48], [60094, 54], [60328, 124], [60627, 125], [60927, 125], [61227, 125], [61527, 125], [61827, 125], [62128, 124], [62428, 124], [62729, 123], [63030, 122], [63330, 122], [63631, 121], [63931, 121], [64232, 120], [64533, 119], [64833, 119], [65134, 118], [65434, 118], [65735, 117], [66036, 116], [66336, 116], [66637, 115], [66937, 115], [67238, 114], [67539, 113], [67839, 113], [68140, 112], [68440, 112], [68741, 111], [69042, 110], [69342, 110], [69643, 109], [69943, 109], [70244, 108], [70545, 107], [70845, 107], [71146, 106], [71446, 106], [71747, 105], [72048, 104], [72348, 104], [72649, 103], [72949, 103], [73250, 102], [73551, 101], [73851, 101], [74152, 100], [74452, 100], [74753, 99], [75054, 97], [75354, 97], [75655, 96]], "point": [89, 201]}}, "high_idx": 3}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Ladle|-00.54|+01.09|+01.92"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [87, 106, 181, 126], "mask": [[31598, 7], [31894, 16], [32192, 20], [32490, 24], [32789, 26], [33088, 28], [33388, 29], [33687, 31], [33987, 31], [34287, 31], [34587, 47], [34887, 62], [35188, 76], [35488, 29], [35529, 50], [35789, 28], [35845, 37], [36089, 28], [36160, 22], [36390, 26], [36475, 7], [36692, 23], [36993, 20], [37295, 16], [37597, 12]], "point": [133, 115]}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-02.04|+00.61|+00.59"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [48, 148, 151, 202], "mask": [[44149, 103], [44449, 103], [44748, 104], [45048, 104], [45349, 103], [45649, 103], [45949, 103], [46250, 102], [46550, 102], [46851, 101], [47151, 101], [47451, 101], [47752, 100], [48052, 100], [48353, 99], [48653, 99], [48953, 99], [49254, 97], [49554, 97], [49855, 96], [50155, 96], [50455, 96], [50756, 95], [51056, 95], [51356, 95], [51657, 94], [51957, 94], [52258, 93], [52558, 93], [52858, 93], [53159, 92], [53459, 92], [53760, 91], [54060, 91], [54360, 91], [54661, 90], [54961, 90], [55262, 89], [55562, 89], [55862, 89], [56163, 88], [56463, 88], [56764, 87], [57064, 87], [57364, 87], [57665, 86], [57965, 86], [58266, 85], [58566, 85], [58866, 85], [59167, 84], [59467, 84], [59767, 84], [60068, 83], [60368, 83]], "point": [99, 174]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Ladle|-00.54|+01.09|+01.92", "placeStationary": true, "receptacleObjectId": "Drawer|-02.04|+00.61|+00.59"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [27, 150, 151, 253], "mask": [[44755, 92], [45055, 92], [45354, 93], [45654, 93], [45954, 93], [46253, 94], [46553, 94], [46852, 95], [47152, 95], [47452, 95], [47751, 96], [48051, 96], [48351, 96], [48650, 98], [48950, 98], [49249, 99], [49549, 99], [49849, 99], [50148, 100], [50448, 100], [50748, 100], [51047, 101], [51347, 101], [51647, 101], [51946, 102], [52246, 102], [52545, 103], [52845, 103], [53145, 103], [53444, 104], [53744, 104], [54044, 104], [54343, 105], [54643, 105], [54943, 42], [54989, 59], [55242, 43], [55291, 57], [55542, 42], [55592, 56], [55841, 43], [55893, 55], [56141, 43], [56193, 55], [56441, 43], [56494, 54], [56740, 44], [56794, 54], [57040, 43], [57095, 53], [57340, 43], [57395, 53], [57639, 44], [57695, 53], [57939, 44], [57995, 53], [58239, 44], [58295, 53], [58538, 45], [58595, 53], [58838, 45], [58895, 53], [59137, 46], [59195, 53], [59437, 47], [59494, 54], [59737, 47], [59794, 54], [60036, 48], [60094, 54], [60328, 124], [60627, 125], [60927, 125], [61227, 125], [61527, 125], [61827, 125], [62128, 124], [62428, 124], [62729, 123], [63030, 122], [63330, 122], [63631, 121], [63931, 121], [64232, 120], [64533, 119], [64833, 119], [65134, 118], [65434, 118], [65735, 117], [66036, 116], [66336, 116], [66637, 115], [66937, 115], [67238, 114], [67539, 113], [67839, 113], [68140, 112], [68440, 112], [68741, 111], [69042, 110], [69342, 110], [69643, 109], [69943, 109], [70244, 108], [70545, 107], [70845, 107], [71146, 29], [71179, 73], [71446, 21], [71486, 66], [71747, 17], [71789, 63], [72048, 13], [72091, 61], [72348, 11], [72394, 58], [72649, 7], [72695, 57], [72949, 6], [72996, 56], [73250, 3], [73297, 55], [73551, 1], [73599, 53], [73900, 52], [74201, 51], [74501, 51], [74802, 50], [75102, 49], [75403, 48], [75703, 48]], "point": [89, 201]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-02.04|+00.61|+00.59"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [27, 150, 151, 253], "mask": [[44755, 92], [45055, 92], [45354, 93], [45654, 93], [45954, 93], [46253, 94], [46553, 94], [46852, 95], [47152, 95], [47452, 95], [47751, 96], [48051, 96], [48351, 96], [48650, 98], [48950, 98], [49249, 99], [49549, 99], [49849, 99], [50148, 100], [50448, 100], [50748, 100], [51047, 41], [51092, 56], [51347, 40], [51394, 54], [51647, 40], [51695, 53], [51946, 41], [51995, 53], [52246, 41], [52296, 52], [52545, 42], [52597, 51], [52845, 42], [52897, 51], [53145, 41], [53197, 51], [53444, 42], [53498, 50], [53744, 42], [53798, 50], [54044, 42], [54098, 50], [54343, 43], [54398, 50], [54643, 43], [54698, 50], [54943, 42], [54997, 51], [55242, 43], [55297, 51], [55542, 42], [55597, 51], [55841, 43], [55896, 52], [56141, 43], [56196, 52], [56441, 43], [56495, 53], [56740, 44], [56794, 54], [57040, 43], [57095, 53], [57340, 43], [57395, 53], [57639, 44], [57695, 53], [57939, 44], [57995, 53], [58239, 44], [58295, 53], [58538, 45], [58595, 53], [58838, 45], [58895, 53], [59137, 46], [59195, 53], [59437, 47], [59495, 53], [59737, 47], [59795, 53], [60036, 48], [60095, 53], [60328, 124], [60627, 125], [60927, 125], [61227, 125], [61527, 125], [61827, 125], [62128, 124], [62428, 124], [62729, 123], [63030, 122], [63330, 122], [63631, 121], [63931, 121], [64232, 120], [64533, 119], [64833, 119], [65134, 118], [65434, 118], [65735, 117], [66036, 116], [66336, 116], [66637, 115], [66937, 115], [67238, 114], [67539, 113], [67839, 113], [68140, 112], [68440, 112], [68741, 111], [69042, 110], [69342, 110], [69643, 109], [69943, 109], [70244, 108], [70545, 107], [70845, 107], [71146, 106], [71446, 106], [71747, 105], [72048, 104], [72348, 104], [72649, 103], [72949, 103], [73250, 102], [73551, 101], [73851, 101], [74152, 100], [74452, 100], [74753, 99], [75054, 97], [75354, 97], [75655, 96]], "point": [89, 201]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan4", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -0.75, "y": 0.9009999, "z": 1.25}, "object_poses": [{"objectName": "Ladle_f4537974", "position": {"x": -2.01135373, "y": 1.16406763, "z": 0.190328062}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Ladle_f4537974", "position": {"x": -0.785364747, "y": 1.08740556, "z": 3.04208541}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_130c30e2", "position": {"x": -3.79859781, "y": 0.112053685, "z": 2.03875422}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_130c30e2", "position": {"x": -2.45075154, "y": 0.487611622, "z": 0.613230169}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8393cb4d", "position": {"x": -0.5388072, "y": 1.04718959, "z": 2.59353828}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8393cb4d", "position": {"x": -0.785364747, "y": 1.04828012, "z": 2.817812}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_1f60da4f", "position": {"x": -0.168970883, "y": 1.04878652, "z": 2.14499116}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_9dba57b8", "position": {"x": -0.415528417, "y": 1.04827678, "z": 2.3692646}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_9dba57b8", "position": {"x": -0.168970883, "y": 1.04936731, "z": 1.9207176}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_0271e4d1", "position": {"x": -1.4651612, "y": 0.946556032, "z": 0.359536}, "rotation": {"x": -1.40334191e-14, "y": 180.0, "z": 0.0}}, {"objectName": "Spatula_0271e4d1", "position": {"x": -2.51405215, "y": 1.1399, "z": 0.473335981}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7b2f7cfb", "position": {"x": -0.603957951, "y": 1.15510356, "z": 0.659750342}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_7b1a480e", "position": {"x": -1.19964325, "y": 0.9295632, "z": 0.4288}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_7b1a480e", "position": {"x": -3.26640654, "y": 0.7260677, "z": 3.06190181}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_ca5fe19d", "position": {"x": -0.908643544, "y": 1.04675734, "z": 2.14499116}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_58fe0a5f", "position": {"x": -2.13702822, "y": 1.12543952, "z": 0.473335981}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_58fe0a5f", "position": {"x": -2.51405215, "y": 1.12543952, "z": 0.09599209}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_9496ee5e", "position": {"x": -1.522666, "y": 0.0469315648, "z": 0.381979883}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_e61894bc", "position": {"x": -2.08889937, "y": 0.8998355, "z": 0.612271369}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_e61894bc", "position": {"x": -1.94843364, "y": 0.0911935046, "z": 0.5459646}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_4cc43937", "position": {"x": -0.908643544, "y": 1.04345369, "z": 3.26635885}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_4cc43937", "position": {"x": -2.35645461, "y": 0.0911935046, "z": 0.613230169}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_d5b8c410", "position": {"x": -2.38837767, "y": 1.21164966, "z": 0.473335981}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_d5b8c410", "position": {"x": -3.37359357, "y": 1.37350965, "z": 3.061901}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_ff353859", "position": {"x": -0.908643544, "y": 1.04214752, "z": 2.817812}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_ff353859", "position": {"x": -1.15619731, "y": 0.0444408655, "z": 0.437948167}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_f6bdaee7", "position": {"x": -3.50640655, "y": 1.35413992, "z": 2.84919739}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_1f60da4f", "position": {"x": -1.995582, "y": 0.09630655, "z": 0.579597354}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_58fe0a5f", "position": {"x": -3.54231119, "y": 0.113028817, "z": 1.94883811}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_ab8a6692", "position": {"x": -3.5728128, "y": 0.916275144, "z": 2.991}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Ladle_f4537974", "position": {"x": -0.5388072, "y": 1.086315, "z": 1.9207176}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_4679e4a5", "position": {"x": -3.742703, "y": 1.15538549, "z": 0.6620079}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ca5fe19d", "position": {"x": -1.15619731, "y": 0.0479601622, "z": 0.6082015}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_d5b8c410", "position": {"x": -0.908643544, "y": 1.13498759, "z": 3.04208541}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_7c6826ef", "position": {"x": -0.908643544, "y": 1.12452459, "z": 1.9207176}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_0271e4d1", "position": {"x": -0.5388072, "y": 1.06323791, "z": 2.3692646}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_447fa38c", "position": {"x": -3.475, "y": 1.1332, "z": 0.2271}, "rotation": {"x": 0.0, "y": 90.00039, "z": 0.0}}, {"objectName": "Egg_7b2f7cfb", "position": {"x": -3.29100013, "y": 0.953061163, "z": 2.419}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_7b1a480e", "position": {"x": -1.55366719, "y": 0.9295632, "z": 0.221008}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_e61894bc", "position": {"x": -0.662086, "y": 1.04454422, "z": 2.14499116}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_e6e9017d", "position": {"x": -0.7158578, "y": 1.15733731, "z": 0.206081033}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_9496ee5e", "position": {"x": -0.908643544, "y": 1.04474556, "z": 2.59353828}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_4cc43937", "position": {"x": -2.51405215, "y": 1.12120628, "z": 0.284664035}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8393cb4d", "position": {"x": -1.28814924, "y": 0.931598246, "z": 0.359536}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_f6bdaee7", "position": {"x": -1.28814924, "y": 0.998936, "z": 0.636592}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_1473e57e", "position": {"x": -0.168970883, "y": 1.08882356, "z": 2.3692646}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_130c30e2", "position": {"x": -0.77180773, "y": 1.12446439, "z": 0.296814859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_9dba57b8", "position": {"x": -0.785364747, "y": 1.04936743, "z": 2.14499116}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_ff353859", "position": {"x": -3.639219, "y": 1.5554601, "z": 2.778296}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}], "object_toggles": [], "random_seed": 1463295819, "scene_num": 4}, "task_id": "trial_T20190908_154908_741855", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A98E8M4QLI9RS_31T4R4OBOV7VGV9AHS9XUTTFPVV7CT", "high_descs": ["turn to the left and move forward just passed the sink on the left and turn to the left to face the counter that is to the right of the sink", "pick up the soup spoon with the black handle that is on the counter to the left of the plant", "move back slightly to have access to the drawers below the counter", "open up the middle drawer on the left and put the spoon in to the drawer, close the drawer", "turn to the left and move forward to the table on the left, turn left to face the table", "pick up the soup spoon from the table", "turn to the left and move passed the sink on the left to the counter to the right of the sink, turn to face the counter", "open up the middle drawer on the left, put the spoon in to the drawer and close the drawer"], "task_desc": "put two soup spoons in to the drawer", "votes": [1, 1]}, {"assignment_id": "A1O3TWBUDONVLO_36NEMU28XI4NFWM22PBNP5X8C03WMX", "high_descs": ["Turn around and step forward to face the counter.", "Pick up the ladle on the counter.", "Take a step back and look down to face the bottom left cabinet.", "Place the ladle in the bottom left cabinet.", "Turn left and walk forward and turn left to face the end of the kitchen table.", "Pick up the ladle on the end of the kitchen table.", "Turn around and walk across the room and look down to face the bottom left cabinet.", "Place the ladle in the bottom left cabinet next to the other ladle."], "task_desc": "To place two ladles in the bottom left cabinet below the sink.", "votes": [1, 1]}, {"assignment_id": "AISNLDPD2DFEG_3G5F9DBFOSOO8TLMLIQEBY6YO5JHVG", "high_descs": ["Turn left, go forward, turn left after the sink", "Pick up the spoon on the counter", "Turn around, go forward a bit, turn around", "Put the knife into the drawer second from the top", "Turn left, go forward, turn left at the table ", "Pick up the spoon on the table", "Turn left, go forward a bit, turn right, go forward a bit, turn left, go forward, turn left", "Put the knife into the drawer second from the top"], "task_desc": "Put two spoons into the drawer", "votes": [1, 1]}]}}