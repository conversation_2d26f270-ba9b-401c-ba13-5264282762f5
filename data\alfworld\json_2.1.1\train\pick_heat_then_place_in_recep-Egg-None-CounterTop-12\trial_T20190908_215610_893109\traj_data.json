{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000034.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000047.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000048.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000049.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000243.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000244.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000245.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000246.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000247.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000248.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000249.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 32}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "CounterTop", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|4|8|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [6.6794138, 6.6794138, 8.09188, 8.09188, 3.8922532, 3.8922532]], "coordinateReceptacleObjectId": ["CounterTop", [6.02, 6.02, 9.24, 9.24, 3.8936, 3.8936]], "forceVisible": true, "objectId": "Egg|+01.67|+00.97|+02.02"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|6|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|3|9|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "countertop"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [6.6794138, 6.6794138, 8.09188, 8.09188, 3.8922532, 3.8922532]], "coordinateReceptacleObjectId": ["CounterTop", [6.02, 6.02, 9.24, 9.24, 3.8936, 3.8936]], "forceVisible": true, "objectId": "Egg|+01.67|+00.97|+02.02", "receptacleObjectId": "CounterTop|+01.51|+00.97|+02.31"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+01.67|+00.97|+02.02"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [136, 130, 150, 148], "mask": [[38841, 5], [39139, 8], [39438, 10], [39738, 11], [40037, 12], [40337, 13], [40636, 14], [40936, 15], [41236, 15], [41536, 15], [41836, 15], [42136, 15], [42436, 15], [42737, 13], [43037, 13], [43338, 11], [43638, 11], [43939, 9], [44241, 5]], "point": [143, 138]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+01.67|+00.97|+02.02", "placeStationary": true, "receptacleObjectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 17], [6318, 241], [6600, 17], [6618, 241], [6900, 17], [6918, 240], [7213, 5], [7219, 239], [7520, 238], [7825, 233], [8130, 228], [8434, 224], [8736, 222], [9037, 221], [9338, 220], [9640, 218], [9941, 216], [10242, 215], [10542, 215], [10843, 214], [11143, 214], [11443, 214], [11744, 213], [12044, 213], [12344, 213], [12645, 212], [12945, 211], [13245, 211], [13545, 211], [13845, 211], [14145, 211], [14445, 211], [14745, 211], [15044, 212], [15344, 212], [15644, 212], [15944, 211], [16244, 211], [16544, 211], [16843, 212], [17143, 212], [17443, 212], [17742, 213], [18042, 213], [18341, 214], [18600, 1], [18641, 214], [18900, 2], [18940, 214], [19200, 2], [19240, 214], [19500, 3], [19540, 214], [19800, 3], [19839, 215], [20100, 5], [20139, 215], [20400, 6], [20438, 216], [20700, 7], [20738, 216], [21000, 9], [21036, 218], [21300, 10], [21335, 219], [21600, 12], [21634, 220], [21900, 14], [21933, 220], [22200, 20], [22230, 223], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 17], [6318, 241], [6600, 17], [6618, 241], [6900, 17], [6918, 240], [7213, 5], [7219, 239], [7520, 238], [7825, 233], [8130, 228], [8434, 224], [8736, 222], [9037, 221], [9338, 220], [9640, 218], [9941, 216], [10242, 215], [10542, 215], [10843, 80], [10930, 127], [11143, 79], [11231, 126], [11443, 77], [11532, 125], [11744, 76], [11833, 124], [12044, 75], [12134, 123], [12344, 74], [12435, 122], [12645, 73], [12735, 122], [12945, 72], [13036, 120], [13245, 72], [13336, 120], [13545, 71], [13637, 119], [13845, 71], [13937, 119], [14145, 71], [14238, 118], [14445, 70], [14538, 118], [14745, 70], [14838, 118], [15044, 71], [15139, 117], [15344, 71], [15439, 117], [15644, 70], [15739, 117], [15944, 70], [16039, 116], [16244, 70], [16339, 116], [16544, 70], [16640, 115], [16843, 71], [16940, 115], [17143, 71], [17240, 115], [17443, 71], [17540, 115], [17742, 72], [17840, 115], [18042, 72], [18140, 115], [18341, 73], [18440, 115], [18600, 1], [18641, 73], [18740, 115], [18900, 2], [18940, 75], [19039, 115], [19200, 2], [19240, 75], [19339, 115], [19500, 3], [19540, 75], [19639, 115], [19800, 3], [19839, 77], [19939, 115], [20100, 5], [20139, 77], [20238, 116], [20400, 6], [20438, 78], [20538, 116], [20700, 7], [20738, 79], [20837, 117], [21000, 9], [21036, 82], [21137, 117], [21300, 10], [21335, 83], [21436, 118], [21600, 12], [21634, 85], [21735, 119], [21900, 14], [21933, 87], [22035, 118], [22200, 20], [22230, 91], [22334, 119], [22500, 122], [22632, 121], [22800, 124], [22931, 122], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [139, 63]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+01.67|+00.97|+02.02"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [114, 37, 139, 77], "mask": [[10923, 7], [11222, 9], [11520, 12], [11820, 13], [12119, 15], [12418, 17], [12718, 17], [13017, 19], [13317, 19], [13616, 21], [13916, 21], [14216, 22], [14515, 23], [14815, 23], [15115, 24], [15415, 24], [15714, 25], [16014, 25], [16314, 25], [16614, 26], [16914, 26], [17214, 26], [17514, 26], [17814, 26], [18114, 26], [18414, 26], [18714, 26], [19015, 24], [19315, 24], [19615, 24], [19916, 23], [20216, 22], [20516, 22], [20817, 20], [21118, 19], [21418, 18], [21719, 16], [22020, 15], [22321, 13], [22622, 10], [22924, 7]], "point": [126, 56]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 17], [6318, 241], [6600, 17], [6618, 241], [6900, 17], [6918, 240], [7213, 5], [7219, 239], [7520, 238], [7825, 233], [8130, 228], [8434, 224], [8736, 222], [9037, 221], [9338, 220], [9640, 218], [9941, 216], [10242, 215], [10542, 215], [10843, 214], [11143, 214], [11443, 214], [11744, 213], [12044, 213], [12344, 213], [12645, 212], [12945, 211], [13245, 211], [13545, 211], [13845, 211], [14145, 211], [14445, 211], [14745, 211], [15044, 212], [15344, 212], [15644, 212], [15944, 211], [16244, 211], [16544, 211], [16843, 212], [17143, 212], [17443, 212], [17742, 213], [18042, 213], [18341, 214], [18600, 1], [18641, 214], [18900, 2], [18940, 214], [19200, 2], [19240, 214], [19500, 3], [19540, 214], [19800, 3], [19839, 215], [20100, 5], [20139, 215], [20400, 6], [20438, 216], [20700, 7], [20738, 216], [21000, 9], [21036, 218], [21300, 10], [21335, 219], [21600, 12], [21634, 220], [21900, 14], [21933, 220], [22200, 20], [22230, 223], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+01.67|+00.97|+02.02", "placeStationary": true, "receptacleObjectId": "CounterTop|+01.51|+00.97|+02.31"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [7, 85, 299, 193], "mask": [[25354, 146], [25654, 146], [25954, 146], [26254, 146], [26554, 146], [26854, 146], [27154, 146], [27454, 146], [27754, 146], [28054, 146], [28354, 146], [28654, 146], [28954, 145], [29254, 142], [29554, 140], [29854, 138], [30154, 136], [30454, 134], [30754, 133], [31054, 132], [31354, 131], [31654, 130], [31954, 129], [32254, 89], [32554, 86], [32854, 84], [33154, 83], [33454, 4], [33460, 76], [33754, 4], [33760, 1], [33765, 70], [34054, 4], [34059, 2], [34064, 2], [34073, 8], [34086, 49], [34354, 4], [34359, 1], [34363, 2], [34416, 18], [34654, 6], [34662, 2], [34667, 3], [34679, 14], [34717, 17], [34954, 9], [34966, 2], [34977, 26], [35017, 17], [35254, 13], [35276, 58], [35554, 80], [35854, 80], [36154, 80], [36340, 2], [36454, 81], [36640, 2], [36754, 81], [36939, 4], [37054, 81], [37241, 3], [37354, 82], [37543, 3], [37654, 82], [37844, 3], [37954, 82], [38144, 5], [38254, 83], [38444, 7], [38554, 83], [38745, 8], [38854, 84], [39045, 10], [39155, 83], [39345, 12], [39455, 83], [39645, 14], [39755, 84], [39946, 15], [40055, 84], [40246, 18], [40355, 84], [40546, 20], [40655, 85], [40846, 23], [40955, 85], [41146, 25], [41255, 85], [41447, 27], [41499, 9], [41555, 86], [41747, 30], [41798, 23], [41854, 87], [42047, 36], [42098, 52], [42154, 88], [42347, 42], [42398, 52], [42454, 88], [42647, 49], [42698, 52], [42754, 88], [42948, 102], [43054, 89], [43248, 102], [43354, 89], [43548, 103], [43654, 89], [43848, 196], [44148, 196], [44449, 195], [44749, 196], [45049, 196], [45349, 197], [45650, 196], [45950, 196], [46250, 197], [46550, 197], [46850, 197], [47151, 197], [47451, 197], [47751, 198], [48051, 198], [48351, 198], [48652, 198], [48952, 198], [49252, 199], [49519, 1], [49552, 200], [49819, 2], [49853, 200], [50118, 4], [50153, 200], [50418, 5], [50453, 201], [50717, 6], [50753, 202], [51017, 7], [51053, 203], [51317, 8], [51352, 205], [51616, 10], [51652, 207], [51916, 10], [51952, 208], [52215, 12], [52251, 210], [52515, 13], [52551, 212], [52814, 15], [52851, 214], [53114, 15], [53149, 217], [53413, 17], [53448, 221], [53713, 19], [53745, 226], [54012, 22], [54043, 232], [54312, 24], [54340, 239], [54611, 289], [54911, 289], [55210, 290], [55510, 290], [55809, 291], [56109, 291], [56408, 292], [56708, 292], [57008, 292], [57307, 293], [57607, 293]], "point": [154, 139]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan12", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": 0.75, "y": 0.9009999, "z": 1.0}, "object_poses": [{"objectName": "DishSponge_a0f18af8", "position": {"x": 1.48997676, "y": 0.06976977, "z": 2.879911}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": -0.804073453, "y": 0.08175957, "z": 2.169395}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_d40bfead", "position": {"x": 1.59296286, "y": 0.938448548, "z": 2.11864662}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Fork_d40bfead", "position": {"x": -0.876905, "y": 0.938448548, "z": 0.921}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": -0.876905, "y": 0.9390294, "z": 1.15406442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_733b0828", "position": {"x": -0.7236026, "y": 0.761567056, "z": 0.847031236}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_733b0828", "position": {"x": -0.827398658, "y": 0.761567056, "z": 2.51703739}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -1.109, "y": 0.9307289, "z": 1.4704}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": -0.7106551, "y": 0.9730633, "z": 0.9986881}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": 1.28540039, "y": 0.9364782, "z": 2.59703}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": 1.346072, "y": 0.0995715857, "z": 0.474614143}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": 1.39171886, "y": 0.0867086649, "z": 2.532645}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -1.0149709, "y": 0.6270374, "z": 0.0423484966}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -1.209405, "y": 0.999118, "z": 1.15406442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": 1.44873142, "y": 0.7431432, "z": 0.7126938}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -0.861997366, "y": 0.7431432, "z": 0.914999962}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -1.12137806, "y": 1.49741375, "z": 2.305651}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": 1.52899969, "y": 1.00298607, "z": 2.34560037}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": -1.01497161, "y": 0.560819447, "z": 0.272302449}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": 1.65219283, "y": 1.6457032, "z": 1.808613}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": 1.42390835, "y": 0.119435936, "z": 2.97771883}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_f3700d17", "position": {"x": 1.46078944, "y": 1.01439834, "z": 1.677775}, "rotation": {"x": 5.162539, "y": 59.5351868, "z": 356.178833}}, {"objectName": "SoapBottle_09f78d94", "position": {"x": -1.00186861, "y": 1.5013057, "z": 1.01843715}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_20fd7fed", "position": {"x": -0.9230002, "y": 0.990676045, "z": 2.26549983}, "rotation": {"x": 4.161468e-05, "y": 302.470367, "z": 6.4569067e-06}}, {"objectName": "Fork_d40bfead", "position": {"x": -0.792800069, "y": 0.747115731, "z": 0.9829687}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -0.971269131, "y": 1.38043737, "z": 0.195651278}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": -0.971269369, "y": 1.49570036, "z": 0.2723026}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -1.109, "y": 0.9307289, "z": 1.8657}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_733b0828", "position": {"x": 1.275738, "y": 0.7618369, "z": 0.848631263}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": -0.758201241, "y": 0.7685599, "z": 2.51703739}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": -1.12628, "y": 0.9342062, "z": 0.921}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": -0.877187848, "y": 1.54854989, "z": 0.0423489}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -1.043155, "y": 0.9342062, "z": 0.765623748}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": 1.3765, "y": 0.736837, "z": 2.2341}, "rotation": {"x": 0.0, "y": 29.9998684, "z": 0.0}}, {"objectName": "Potato_22312ae0", "position": {"x": -0.7106551, "y": 0.9705572, "z": 0.765623748}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": -1.0766772, "y": 1.83225048, "z": 0.3563524}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": -1.0753144, "y": 1.94948971, "z": 1.83000422}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": 1.66985345, "y": 0.9730633, "z": 2.02297}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": 1.30961347, "y": 0.0995715857, "z": 0.2726476}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_9f7abbea", "position": {"x": 1.3781, "y": 1.73753989, "z": 0.3929}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": -0.8769051, "y": 0.9390294, "z": 0.6879356}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": -1.10601223, "y": 1.94650507, "z": 1.50512326}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 268501791, "scene_num": 12}, "task_id": "trial_T20190908_215610_893109", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1RLO9LNUJIW5S_3PJ71Z61R7TF7BN5GSIXRT1UGR9914", "high_descs": ["Take two steps forward then turn right to face the counter.", "Pick up the egg that's in front of you near the fork.", "Take a large step to your right so you're standing in front of the sink.and turn to face it when you get there.", "Look up and cook the egg in the microwave then take it back out and close the door. ", "Take a large step to your left then take a small step backwards.", "Put the egg on the counter under the right corner of the coffee maker and to the far right of the cup to it's sitting near the edge under the forks tines that's way above it.."], "task_desc": "Put a clean egg on the counter.", "votes": [1, 1]}, {"assignment_id": "A1SX8IVV82M0LW_3QRYMNZ7F1815X38JLLCF0F9DYDNTK", "high_descs": ["Walk to the kitchen counter on your right.", "Grab an egg from the counter.", "Go to the microwave on your right.", "Put the egg in the microwave, then grab it.", "Go to the kitchen counter left of the microwave.", "Place the egg on the counter."], "task_desc": "Heat an egg and replace it.", "votes": [1, 1]}, {"assignment_id": "A24RGLS3BRZ60J_3YOH7BII0CYFAJMUR3TBOQMQGOCKVG", "high_descs": ["Move ahead a step and face to the right. ", "Pick up the egg from the back of the counter.", "Carry the egg and take a step right and look up at the microwave.", "Open the microwave and place the egg inside. Shut the door then open the door. Take out the egg and shut the door.", "Hold the egg and turn to left and and face the coffee machine on the right.", "Place the egg on the counter, just in front of the coffee machine."], "task_desc": "Heat an egg and place it on the counter.", "votes": [1, 1]}]}}