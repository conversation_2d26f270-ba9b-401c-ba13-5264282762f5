
(define (problem plan_trial_T20190907_060414_846460)
(:domain alfred)
(:objects
agent1 - agent
Curtains - object
        SprayBottle - object
        Mirror - object
        HousePlant - object
        Watch - object
        TeddyBear - object
        WateringCan - object
        Bathtub - object
        LightSwitch - object
        Vase - object
        Candle - object
        Bread - object
        Statue - object
        HandTowel - object
        StoveKnob - object
        PaperTowelRoll - object
        Pan - object
        PepperShaker - object
        Painting - object
        CellPhone - object
        Plunger - object
        Spoon - object
        SaltShaker - object
        KeyChain - object
        Footstool - object
        Poster - object
        Television - object
        LaundryHamperLid - object
        Glassbottle - object
        AlarmClock - object
        WineBottle - object
        Bowl - object
        Sink - object
        TissueBox - object
        TennisRacket - object
        ToiletPaper - object
        ScrubBrush - object
        CreditCard - object
        Laptop - object
        Cloth - object
        Cup - object
        Chair - object
        BasketBall - object
        Kettle - object
        PaperTowel - object
        ShowerDoor - object
        BaseballBat - object
        Potato - object
        Pillow - object
        Lettuce - object
        RemoteControl - object
        Window - object
        DishSponge - object
        CD - object
        Boots - object
        Blinds - object
        Pen - object
        Newspaper - object
        Plate - object
        Mug - object
        ShowerGlass - object
        ToiletPaperRoll - object
        SoapBar - object
        FloorLamp - object
        SoapBottle - object
        Pot - object
        Pencil - object
        Spatula - object
        Towel - object
        Fork - object
        Ladle - object
        Knife - object
        Apple - object
        Box - object
        Tomato - object
        ButterKnife - object
        Egg - object
        Book - object
        DeskLamp - object
CurtainsType - otype
        SprayBottleType - otype
        MirrorType - otype
        HousePlantType - otype
        WatchType - otype
        TeddyBearType - otype
        WateringCanType - otype
        BathtubType - otype
        LightSwitchType - otype
        VaseType - otype
        CandleType - otype
        BreadType - otype
        StatueType - otype
        HandTowelType - otype
        StoveKnobType - otype
        PaperTowelRollType - otype
        PanType - otype
        PepperShakerType - otype
        PaintingType - otype
        CellPhoneType - otype
        PlungerType - otype
        SpoonType - otype
        SaltShakerType - otype
        KeyChainType - otype
        FootstoolType - otype
        PosterType - otype
        TelevisionType - otype
        LaundryHamperLidType - otype
        GlassbottleType - otype
        AlarmClockType - otype
        WineBottleType - otype
        BowlType - otype
        SinkType - otype
        TissueBoxType - otype
        TennisRacketType - otype
        ToiletPaperType - otype
        ScrubBrushType - otype
        CreditCardType - otype
        LaptopType - otype
        ClothType - otype
        CupType - otype
        ChairType - otype
        BasketBallType - otype
        KettleType - otype
        PaperTowelType - otype
        ShowerDoorType - otype
        BaseballBatType - otype
        PotatoType - otype
        PillowType - otype
        LettuceType - otype
        RemoteControlType - otype
        WindowType - otype
        DishSpongeType - otype
        CDType - otype
        BootsType - otype
        BlindsType - otype
        PenType - otype
        NewspaperType - otype
        PlateType - otype
        MugType - otype
        ShowerGlassType - otype
        ToiletPaperRollType - otype
        SoapBarType - otype
        FloorLampType - otype
        SoapBottleType - otype
        PotType - otype
        PencilType - otype
        SpatulaType - otype
        TowelType - otype
        ForkType - otype
        LadleType - otype
        KnifeType - otype
        AppleType - otype
        BoxType - otype
        TomatoType - otype
        ButterKnifeType - otype
        EggType - otype
        BookType - otype
        DeskLampType - otype
CabinetType - rtype
        ShelfType - rtype
        DiningTableType - rtype
        TowelHolderType - rtype
        PaintingHangerType - rtype
        TVStandType - rtype
        LaundryHamperType - rtype
        OttomanType - rtype
        SafeType - rtype
        CoffeeMachineType - rtype
        CounterTopType - rtype
        ToiletType - rtype
        FridgeType - rtype
        CoffeeTableType - rtype
        SinkBasinType - rtype
        GarbageCanType - rtype
        BathtubBasinType - rtype
        SideTableType - rtype
        CartType - rtype
        BedType - rtype
        DeskType - rtype
        SofaType - rtype
        ArmChairType - rtype
        MicrowaveType - rtype
        HandTowelHolderType - rtype
        ToasterType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        DrawerType - rtype
        DresserType - rtype


        AlarmClock_bar__plus_00_dot_08_bar__plus_00_dot_73_bar__minus_02_dot_59 - object
        AlarmClock_bar__plus_00_dot_08_bar__plus_00_dot_73_bar__minus_02_dot_76 - object
        BaseballBat_bar__minus_01_dot_63_bar__plus_00_dot_65_bar__plus_00_dot_42 - object
        Blinds_bar__plus_00_dot_45_bar__plus_02_dot_16_bar__minus_02_dot_85 - object
        Book_bar__plus_00_dot_34_bar__plus_00_dot_72_bar__minus_02_dot_65 - object
        Box_bar__plus_02_dot_00_bar__plus_00_dot_32_bar__minus_01_dot_04 - object
        CD_bar__plus_01_dot_38_bar__plus_00_dot_05_bar__minus_02_dot_28 - object
        CD_bar__plus_02_dot_22_bar__plus_00_dot_04_bar__plus_00_dot_31 - object
        CellPhone_bar__minus_01_dot_02_bar__plus_00_dot_42_bar__minus_01_dot_61 - object
        Chair_bar__minus_01_dot_56_bar__plus_00_dot_00_bar__minus_00_dot_31 - object
        Cloth_bar__plus_02_dot_18_bar__plus_00_dot_00_bar__minus_00_dot_57 - object
        CreditCard_bar__plus_00_dot_72_bar__plus_00_dot_11_bar__minus_02_dot_49 - object
        CreditCard_bar__minus_01_dot_79_bar__plus_00_dot_51_bar__minus_01_dot_15 - object
        DeskLamp_bar__minus_01_dot_85_bar__plus_00_dot_59_bar__minus_00_dot_79 - object
        KeyChain_bar__minus_01_dot_94_bar__plus_00_dot_51_bar__minus_01_dot_15 - object
        Laptop_bar__minus_01_dot_76_bar__plus_00_dot_59_bar__minus_00_dot_11 - object
        LightSwitch_bar__minus_00_dot_64_bar__plus_01_dot_15_bar__plus_00_dot_50 - object
        Mirror_bar__minus_01_dot_07_bar__plus_01_dot_47_bar__plus_00_dot_50 - object
        Mug_bar__plus_00_dot_54_bar__plus_00_dot_73_bar__minus_02_dot_47 - object
        Mug_bar__plus_01_dot_32_bar__plus_01_dot_18_bar__minus_02_dot_28 - object
        Mug_bar__plus_01_dot_66_bar__plus_01_dot_58_bar__minus_02_dot_32 - object
        Pencil_bar__minus_01_dot_71_bar__plus_00_dot_60_bar__minus_00_dot_68 - object
        Pen_bar__plus_01_dot_98_bar__plus_00_dot_98_bar__minus_02_dot_24 - object
        Pen_bar__minus_01_dot_86_bar__plus_00_dot_51_bar__minus_01_dot_07 - object
        Pillow_bar__minus_01_dot_55_bar__plus_00_dot_46_bar__minus_01_dot_83 - object
        Poster_bar__plus_00_dot_00_bar__plus_01_dot_50_bar__minus_02_dot_64 - object
        TennisRacket_bar__plus_02_dot_24_bar__plus_00_dot_28_bar__minus_01_dot_90 - object
        Vase_bar__plus_00_dot_77_bar__plus_00_dot_73_bar__minus_02_dot_71 - object
        Vase_bar__plus_01_dot_19_bar__plus_00_dot_36_bar__minus_02_dot_30 - object
        Vase_bar__minus_01_dot_65_bar__plus_00_dot_60_bar__minus_00_dot_53 - object
        Window_bar__plus_00_dot_44_bar__plus_01_dot_40_bar__minus_02_dot_86 - object
        Bed_bar__minus_01_dot_05_bar__plus_00_dot_00_bar__minus_01_dot_85 - receptacle
        Desk_bar__minus_01_dot_71_bar__plus_00_dot_00_bar__minus_00_dot_37 - receptacle
        Drawer_bar__plus_00_dot_45_bar__plus_00_dot_20_bar__minus_02_dot_43 - receptacle
        Drawer_bar__plus_00_dot_45_bar__plus_00_dot_50_bar__minus_02_dot_43 - receptacle
        Drawer_bar__minus_01_dot_79_bar__plus_00_dot_10_bar__minus_01_dot_15 - receptacle
        Drawer_bar__minus_01_dot_79_bar__plus_00_dot_25_bar__minus_01_dot_15 - receptacle
        Drawer_bar__minus_01_dot_79_bar__plus_00_dot_39_bar__minus_01_dot_15 - receptacle
        Dresser_bar__plus_00_dot_44_bar__plus_00_dot_04_bar__minus_02_dot_65 - receptacle
        GarbageCan_bar__plus_02_dot_22_bar__plus_00_dot_00_bar__plus_00_dot_31 - receptacle
        Shelf_bar__plus_01_dot_31_bar__plus_00_dot_05_bar__minus_02_dot_30 - receptacle
        Shelf_bar__plus_01_dot_31_bar__plus_00_dot_35_bar__minus_02_dot_30 - receptacle
        Shelf_bar__plus_01_dot_31_bar__plus_00_dot_97_bar__minus_02_dot_30 - receptacle
        Shelf_bar__plus_01_dot_31_bar__plus_01_dot_17_bar__minus_02_dot_30 - receptacle
        Shelf_bar__plus_01_dot_31_bar__plus_01_dot_37_bar__minus_02_dot_30 - receptacle
        Shelf_bar__plus_01_dot_31_bar__plus_01_dot_57_bar__minus_02_dot_30 - receptacle
        Shelf_bar__plus_01_dot_84_bar__plus_00_dot_05_bar__minus_02_dot_30 - receptacle
        Shelf_bar__plus_01_dot_84_bar__plus_00_dot_35_bar__minus_02_dot_30 - receptacle
        Shelf_bar__plus_01_dot_84_bar__plus_00_dot_97_bar__minus_02_dot_30 - receptacle
        Shelf_bar__plus_01_dot_84_bar__plus_01_dot_17_bar__minus_02_dot_30 - receptacle
        Shelf_bar__plus_01_dot_84_bar__plus_01_dot_37_bar__minus_02_dot_30 - receptacle
        Shelf_bar__plus_01_dot_84_bar__plus_01_dot_57_bar__minus_02_dot_30 - receptacle
        SideTable_bar__minus_01_dot_82_bar__plus_00_dot_00_bar__minus_01_dot_14 - receptacle
        loc_bar_7_bar__minus_7_bar_2_bar_45 - location
        loc_bar__minus_4_bar__minus_1_bar_3_bar_60 - location
        loc_bar_2_bar__minus_8_bar_2_bar_60 - location
        loc_bar_3_bar__minus_5_bar_2_bar_60 - location
        loc_bar__minus_4_bar__minus_3_bar_3_bar_60 - location
        loc_bar__minus_4_bar__minus_2_bar_2_bar_45 - location
        loc_bar_2_bar__minus_8_bar_2_bar__minus_30 - location
        loc_bar_7_bar_0_bar_1_bar_60 - location
        loc_bar__minus_2_bar__minus_2_bar_3_bar_45 - location
        loc_bar_5_bar__minus_7_bar_2_bar_15 - location
        loc_bar__minus_4_bar_0_bar_0_bar_15 - location
        loc_bar_7_bar__minus_7_bar_2_bar_15 - location
        loc_bar__minus_4_bar_0_bar_3_bar_60 - location
        loc_bar_5_bar__minus_5_bar_2_bar_60 - location
        loc_bar__minus_3_bar__minus_3_bar_3_bar_60 - location
        loc_bar_6_bar__minus_6_bar_2_bar_60 - location
        loc_bar_7_bar__minus_6_bar_2_bar_60 - location
        loc_bar_1_bar__minus_8_bar_2_bar_15 - location
        loc_bar_7_bar__minus_7_bar_2_bar_30 - location
        loc_bar_6_bar__minus_4_bar_1_bar_60 - location
        loc_bar__minus_3_bar__minus_2_bar_3_bar_60 - location
        loc_bar_5_bar__minus_7_bar_2_bar_45 - location
        loc_bar_7_bar__minus_2_bar_1_bar_60 - location
        loc_bar_7_bar__minus_7_bar_1_bar_60 - location
        loc_bar_5_bar__minus_7_bar_2_bar_30 - location
        loc_bar__minus_3_bar_0_bar_0_bar_45 - location
        loc_bar__minus_3_bar__minus_3_bar_3_bar_45 - location
        loc_bar__minus_2_bar__minus_3_bar_1_bar_30 - location
        )
    

(:init


        (receptacleType Shelf_bar__plus_01_dot_84_bar__plus_00_dot_05_bar__minus_02_dot_30 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_31_bar__plus_01_dot_17_bar__minus_02_dot_30 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_31_bar__plus_01_dot_57_bar__minus_02_dot_30 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_84_bar__plus_01_dot_17_bar__minus_02_dot_30 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_31_bar__plus_00_dot_35_bar__minus_02_dot_30 ShelfType)
        (receptacleType Bed_bar__minus_01_dot_05_bar__plus_00_dot_00_bar__minus_01_dot_85 BedType)
        (receptacleType SideTable_bar__minus_01_dot_82_bar__plus_00_dot_00_bar__minus_01_dot_14 SideTableType)
        (receptacleType Dresser_bar__plus_00_dot_44_bar__plus_00_dot_04_bar__minus_02_dot_65 DresserType)
        (receptacleType Drawer_bar__minus_01_dot_79_bar__plus_00_dot_39_bar__minus_01_dot_15 DrawerType)
        (receptacleType Shelf_bar__plus_01_dot_84_bar__plus_00_dot_35_bar__minus_02_dot_30 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_84_bar__plus_01_dot_57_bar__minus_02_dot_30 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_31_bar__plus_01_dot_37_bar__minus_02_dot_30 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_84_bar__plus_00_dot_97_bar__minus_02_dot_30 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_84_bar__plus_01_dot_37_bar__minus_02_dot_30 ShelfType)
        (receptacleType Drawer_bar__plus_00_dot_45_bar__plus_00_dot_50_bar__minus_02_dot_43 DrawerType)
        (receptacleType Desk_bar__minus_01_dot_71_bar__plus_00_dot_00_bar__minus_00_dot_37 DeskType)
        (receptacleType Drawer_bar__plus_00_dot_45_bar__plus_00_dot_20_bar__minus_02_dot_43 DrawerType)
        (receptacleType Drawer_bar__minus_01_dot_79_bar__plus_00_dot_25_bar__minus_01_dot_15 DrawerType)
        (receptacleType GarbageCan_bar__plus_02_dot_22_bar__plus_00_dot_00_bar__plus_00_dot_31 GarbageCanType)
        (receptacleType Shelf_bar__plus_01_dot_31_bar__plus_00_dot_05_bar__minus_02_dot_30 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_31_bar__plus_00_dot_97_bar__minus_02_dot_30 ShelfType)
        (receptacleType Drawer_bar__minus_01_dot_79_bar__plus_00_dot_10_bar__minus_01_dot_15 DrawerType)
        (objectType Vase_bar__minus_01_dot_65_bar__plus_00_dot_60_bar__minus_00_dot_53 VaseType)
        (objectType Pillow_bar__minus_01_dot_55_bar__plus_00_dot_46_bar__minus_01_dot_83 PillowType)
        (objectType Pencil_bar__minus_01_dot_71_bar__plus_00_dot_60_bar__minus_00_dot_68 PencilType)
        (objectType CreditCard_bar__minus_01_dot_79_bar__plus_00_dot_51_bar__minus_01_dot_15 CreditCardType)
        (objectType Vase_bar__plus_01_dot_19_bar__plus_00_dot_36_bar__minus_02_dot_30 VaseType)
        (objectType BaseballBat_bar__minus_01_dot_63_bar__plus_00_dot_65_bar__plus_00_dot_42 BaseballBatType)
        (objectType Mug_bar__plus_01_dot_32_bar__plus_01_dot_18_bar__minus_02_dot_28 MugType)
        (objectType Cloth_bar__plus_02_dot_18_bar__plus_00_dot_00_bar__minus_00_dot_57 ClothType)
        (objectType Book_bar__plus_00_dot_34_bar__plus_00_dot_72_bar__minus_02_dot_65 BookType)
        (objectType CD_bar__plus_01_dot_38_bar__plus_00_dot_05_bar__minus_02_dot_28 CDType)
        (objectType Pen_bar__minus_01_dot_86_bar__plus_00_dot_51_bar__minus_01_dot_07 PenType)
        (objectType KeyChain_bar__minus_01_dot_94_bar__plus_00_dot_51_bar__minus_01_dot_15 KeyChainType)
        (objectType AlarmClock_bar__plus_00_dot_08_bar__plus_00_dot_73_bar__minus_02_dot_59 AlarmClockType)
        (objectType Laptop_bar__minus_01_dot_76_bar__plus_00_dot_59_bar__minus_00_dot_11 LaptopType)
        (objectType Mug_bar__plus_01_dot_66_bar__plus_01_dot_58_bar__minus_02_dot_32 MugType)
        (objectType Pen_bar__plus_01_dot_98_bar__plus_00_dot_98_bar__minus_02_dot_24 PenType)
        (objectType Vase_bar__plus_00_dot_77_bar__plus_00_dot_73_bar__minus_02_dot_71 VaseType)
        (objectType AlarmClock_bar__plus_00_dot_08_bar__plus_00_dot_73_bar__minus_02_dot_76 AlarmClockType)
        (objectType CD_bar__plus_02_dot_22_bar__plus_00_dot_04_bar__plus_00_dot_31 CDType)
        (objectType Blinds_bar__plus_00_dot_45_bar__plus_02_dot_16_bar__minus_02_dot_85 BlindsType)
        (objectType Mirror_bar__minus_01_dot_07_bar__plus_01_dot_47_bar__plus_00_dot_50 MirrorType)
        (objectType LightSwitch_bar__minus_00_dot_64_bar__plus_01_dot_15_bar__plus_00_dot_50 LightSwitchType)
        (objectType Box_bar__plus_02_dot_00_bar__plus_00_dot_32_bar__minus_01_dot_04 BoxType)
        (objectType Mug_bar__plus_00_dot_54_bar__plus_00_dot_73_bar__minus_02_dot_47 MugType)
        (objectType CreditCard_bar__plus_00_dot_72_bar__plus_00_dot_11_bar__minus_02_dot_49 CreditCardType)
        (objectType Window_bar__plus_00_dot_44_bar__plus_01_dot_40_bar__minus_02_dot_86 WindowType)
        (objectType DeskLamp_bar__minus_01_dot_85_bar__plus_00_dot_59_bar__minus_00_dot_79 DeskLampType)
        (objectType Chair_bar__minus_01_dot_56_bar__plus_00_dot_00_bar__minus_00_dot_31 ChairType)
        (objectType Poster_bar__plus_00_dot_00_bar__plus_01_dot_50_bar__minus_02_dot_64 PosterType)
        (objectType CellPhone_bar__minus_01_dot_02_bar__plus_00_dot_42_bar__minus_01_dot_61 CellPhoneType)
        (objectType TennisRacket_bar__plus_02_dot_24_bar__plus_00_dot_28_bar__minus_01_dot_90 TennisRacketType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType CDType)
        (canContain ShelfType PencilType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PenType)
        (canContain ShelfType ClothType)
        (canContain ShelfType BoxType)
        (canContain ShelfType MugType)
        (canContain ShelfType BookType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType CDType)
        (canContain ShelfType PencilType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PenType)
        (canContain ShelfType ClothType)
        (canContain ShelfType BoxType)
        (canContain ShelfType MugType)
        (canContain ShelfType BookType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType CDType)
        (canContain ShelfType PencilType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PenType)
        (canContain ShelfType ClothType)
        (canContain ShelfType BoxType)
        (canContain ShelfType MugType)
        (canContain ShelfType BookType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType CDType)
        (canContain ShelfType PencilType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PenType)
        (canContain ShelfType ClothType)
        (canContain ShelfType BoxType)
        (canContain ShelfType MugType)
        (canContain ShelfType BookType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType CDType)
        (canContain ShelfType PencilType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PenType)
        (canContain ShelfType ClothType)
        (canContain ShelfType BoxType)
        (canContain ShelfType MugType)
        (canContain ShelfType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType BaseballBatType)
        (canContain BedType BookType)
        (canContain BedType TennisRacketType)
        (canContain BedType PillowType)
        (canContain BedType LaptopType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType AlarmClockType)
        (canContain SideTableType BaseballBatType)
        (canContain SideTableType CDType)
        (canContain SideTableType PencilType)
        (canContain SideTableType TennisRacketType)
        (canContain SideTableType VaseType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType PenType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType ClothType)
        (canContain SideTableType BoxType)
        (canContain SideTableType MugType)
        (canContain SideTableType BookType)
        (canContain DresserType CellPhoneType)
        (canContain DresserType KeyChainType)
        (canContain DresserType AlarmClockType)
        (canContain DresserType CDType)
        (canContain DresserType PencilType)
        (canContain DresserType TennisRacketType)
        (canContain DresserType VaseType)
        (canContain DresserType CreditCardType)
        (canContain DresserType PenType)
        (canContain DresserType LaptopType)
        (canContain DresserType ClothType)
        (canContain DresserType BoxType)
        (canContain DresserType MugType)
        (canContain DresserType BookType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CDType)
        (canContain DrawerType PencilType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PenType)
        (canContain DrawerType ClothType)
        (canContain DrawerType BookType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType CDType)
        (canContain ShelfType PencilType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PenType)
        (canContain ShelfType ClothType)
        (canContain ShelfType BoxType)
        (canContain ShelfType MugType)
        (canContain ShelfType BookType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType CDType)
        (canContain ShelfType PencilType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PenType)
        (canContain ShelfType ClothType)
        (canContain ShelfType BoxType)
        (canContain ShelfType MugType)
        (canContain ShelfType BookType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType CDType)
        (canContain ShelfType PencilType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PenType)
        (canContain ShelfType ClothType)
        (canContain ShelfType BoxType)
        (canContain ShelfType MugType)
        (canContain ShelfType BookType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType CDType)
        (canContain ShelfType PencilType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PenType)
        (canContain ShelfType ClothType)
        (canContain ShelfType BoxType)
        (canContain ShelfType MugType)
        (canContain ShelfType BookType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType CDType)
        (canContain ShelfType PencilType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PenType)
        (canContain ShelfType ClothType)
        (canContain ShelfType BoxType)
        (canContain ShelfType MugType)
        (canContain ShelfType BookType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CDType)
        (canContain DrawerType PencilType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PenType)
        (canContain DrawerType ClothType)
        (canContain DrawerType BookType)
        (canContain DeskType CellPhoneType)
        (canContain DeskType KeyChainType)
        (canContain DeskType AlarmClockType)
        (canContain DeskType CDType)
        (canContain DeskType PencilType)
        (canContain DeskType TennisRacketType)
        (canContain DeskType VaseType)
        (canContain DeskType CreditCardType)
        (canContain DeskType PenType)
        (canContain DeskType LaptopType)
        (canContain DeskType ClothType)
        (canContain DeskType BoxType)
        (canContain DeskType MugType)
        (canContain DeskType BookType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CDType)
        (canContain DrawerType PencilType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PenType)
        (canContain DrawerType ClothType)
        (canContain DrawerType BookType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CDType)
        (canContain DrawerType PencilType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PenType)
        (canContain DrawerType ClothType)
        (canContain DrawerType BookType)
        (canContain GarbageCanType CDType)
        (canContain GarbageCanType PencilType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType ClothType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType CDType)
        (canContain ShelfType PencilType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PenType)
        (canContain ShelfType ClothType)
        (canContain ShelfType BoxType)
        (canContain ShelfType MugType)
        (canContain ShelfType BookType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType CDType)
        (canContain ShelfType PencilType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PenType)
        (canContain ShelfType ClothType)
        (canContain ShelfType BoxType)
        (canContain ShelfType MugType)
        (canContain ShelfType BookType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CDType)
        (canContain DrawerType PencilType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PenType)
        (canContain DrawerType ClothType)
        (canContain DrawerType BookType)
        (pickupable Vase_bar__minus_01_dot_65_bar__plus_00_dot_60_bar__minus_00_dot_53)
        (pickupable Pillow_bar__minus_01_dot_55_bar__plus_00_dot_46_bar__minus_01_dot_83)
        (pickupable Pencil_bar__minus_01_dot_71_bar__plus_00_dot_60_bar__minus_00_dot_68)
        (pickupable CreditCard_bar__minus_01_dot_79_bar__plus_00_dot_51_bar__minus_01_dot_15)
        (pickupable Vase_bar__plus_01_dot_19_bar__plus_00_dot_36_bar__minus_02_dot_30)
        (pickupable BaseballBat_bar__minus_01_dot_63_bar__plus_00_dot_65_bar__plus_00_dot_42)
        (pickupable Mug_bar__plus_01_dot_32_bar__plus_01_dot_18_bar__minus_02_dot_28)
        (pickupable Cloth_bar__plus_02_dot_18_bar__plus_00_dot_00_bar__minus_00_dot_57)
        (pickupable Book_bar__plus_00_dot_34_bar__plus_00_dot_72_bar__minus_02_dot_65)
        (pickupable CD_bar__plus_01_dot_38_bar__plus_00_dot_05_bar__minus_02_dot_28)
        (pickupable Pen_bar__minus_01_dot_86_bar__plus_00_dot_51_bar__minus_01_dot_07)
        (pickupable KeyChain_bar__minus_01_dot_94_bar__plus_00_dot_51_bar__minus_01_dot_15)
        (pickupable AlarmClock_bar__plus_00_dot_08_bar__plus_00_dot_73_bar__minus_02_dot_59)
        (pickupable Laptop_bar__minus_01_dot_76_bar__plus_00_dot_59_bar__minus_00_dot_11)
        (pickupable Mug_bar__plus_01_dot_66_bar__plus_01_dot_58_bar__minus_02_dot_32)
        (pickupable Pen_bar__plus_01_dot_98_bar__plus_00_dot_98_bar__minus_02_dot_24)
        (pickupable Vase_bar__plus_00_dot_77_bar__plus_00_dot_73_bar__minus_02_dot_71)
        (pickupable AlarmClock_bar__plus_00_dot_08_bar__plus_00_dot_73_bar__minus_02_dot_76)
        (pickupable CD_bar__plus_02_dot_22_bar__plus_00_dot_04_bar__plus_00_dot_31)
        (pickupable Box_bar__plus_02_dot_00_bar__plus_00_dot_32_bar__minus_01_dot_04)
        (pickupable Mug_bar__plus_00_dot_54_bar__plus_00_dot_73_bar__minus_02_dot_47)
        (pickupable CreditCard_bar__plus_00_dot_72_bar__plus_00_dot_11_bar__minus_02_dot_49)
        (pickupable CellPhone_bar__minus_01_dot_02_bar__plus_00_dot_42_bar__minus_01_dot_61)
        (pickupable TennisRacket_bar__plus_02_dot_24_bar__plus_00_dot_28_bar__minus_01_dot_90)
        (isReceptacleObject Mug_bar__plus_01_dot_32_bar__plus_01_dot_18_bar__minus_02_dot_28)
        (isReceptacleObject Mug_bar__plus_01_dot_66_bar__plus_01_dot_58_bar__minus_02_dot_32)
        (isReceptacleObject Box_bar__plus_02_dot_00_bar__plus_00_dot_32_bar__minus_01_dot_04)
        (isReceptacleObject Mug_bar__plus_00_dot_54_bar__plus_00_dot_73_bar__minus_02_dot_47)
        (openable Drawer_bar__minus_01_dot_79_bar__plus_00_dot_39_bar__minus_01_dot_15)
        (openable Drawer_bar__minus_01_dot_79_bar__plus_00_dot_25_bar__minus_01_dot_15)
        (openable Drawer_bar__minus_01_dot_79_bar__plus_00_dot_10_bar__minus_01_dot_15)
        
        (atLocation agent1 loc_bar__minus_2_bar__minus_3_bar_1_bar_30)
        
        (cleanable Mug_bar__plus_01_dot_32_bar__plus_01_dot_18_bar__minus_02_dot_28)
        (cleanable Cloth_bar__plus_02_dot_18_bar__plus_00_dot_00_bar__minus_00_dot_57)
        (cleanable Mug_bar__plus_01_dot_66_bar__plus_01_dot_58_bar__minus_02_dot_32)
        (cleanable Mug_bar__plus_00_dot_54_bar__plus_00_dot_73_bar__minus_02_dot_47)
        
        (heatable Mug_bar__plus_01_dot_32_bar__plus_01_dot_18_bar__minus_02_dot_28)
        (heatable Mug_bar__plus_01_dot_66_bar__plus_01_dot_58_bar__minus_02_dot_32)
        (heatable Mug_bar__plus_00_dot_54_bar__plus_00_dot_73_bar__minus_02_dot_47)
        (coolable Mug_bar__plus_01_dot_32_bar__plus_01_dot_18_bar__minus_02_dot_28)
        (coolable Mug_bar__plus_01_dot_66_bar__plus_01_dot_58_bar__minus_02_dot_32)
        (coolable Mug_bar__plus_00_dot_54_bar__plus_00_dot_73_bar__minus_02_dot_47)
        
        
        (toggleable DeskLamp_bar__minus_01_dot_85_bar__plus_00_dot_59_bar__minus_00_dot_79)
        
        
        
        
        (inReceptacle Pen_bar__plus_01_dot_98_bar__plus_00_dot_98_bar__minus_02_dot_24 Shelf_bar__plus_01_dot_84_bar__plus_00_dot_97_bar__minus_02_dot_30)
        (inReceptacle Vase_bar__plus_00_dot_77_bar__plus_00_dot_73_bar__minus_02_dot_71 Dresser_bar__plus_00_dot_44_bar__plus_00_dot_04_bar__minus_02_dot_65)
        (inReceptacle AlarmClock_bar__plus_00_dot_08_bar__plus_00_dot_73_bar__minus_02_dot_59 Dresser_bar__plus_00_dot_44_bar__plus_00_dot_04_bar__minus_02_dot_65)
        (inReceptacle AlarmClock_bar__plus_00_dot_08_bar__plus_00_dot_73_bar__minus_02_dot_76 Dresser_bar__plus_00_dot_44_bar__plus_00_dot_04_bar__minus_02_dot_65)
        (inReceptacle Window_bar__plus_00_dot_44_bar__plus_01_dot_40_bar__minus_02_dot_86 Dresser_bar__plus_00_dot_44_bar__plus_00_dot_04_bar__minus_02_dot_65)
        (inReceptacle Book_bar__plus_00_dot_34_bar__plus_00_dot_72_bar__minus_02_dot_65 Dresser_bar__plus_00_dot_44_bar__plus_00_dot_04_bar__minus_02_dot_65)
        (inReceptacle Mug_bar__plus_00_dot_54_bar__plus_00_dot_73_bar__minus_02_dot_47 Dresser_bar__plus_00_dot_44_bar__plus_00_dot_04_bar__minus_02_dot_65)
        (inReceptacle Vase_bar__minus_01_dot_65_bar__plus_00_dot_60_bar__minus_00_dot_53 Desk_bar__minus_01_dot_71_bar__plus_00_dot_00_bar__minus_00_dot_37)
        (inReceptacle Laptop_bar__minus_01_dot_76_bar__plus_00_dot_59_bar__minus_00_dot_11 Desk_bar__minus_01_dot_71_bar__plus_00_dot_00_bar__minus_00_dot_37)
        (inReceptacle Pencil_bar__minus_01_dot_71_bar__plus_00_dot_60_bar__minus_00_dot_68 Desk_bar__minus_01_dot_71_bar__plus_00_dot_00_bar__minus_00_dot_37)
        (inReceptacle DeskLamp_bar__minus_01_dot_85_bar__plus_00_dot_59_bar__minus_00_dot_79 Desk_bar__minus_01_dot_71_bar__plus_00_dot_00_bar__minus_00_dot_37)
        (inReceptacle Mug_bar__plus_01_dot_32_bar__plus_01_dot_18_bar__minus_02_dot_28 Shelf_bar__plus_01_dot_31_bar__plus_01_dot_17_bar__minus_02_dot_30)
        (inReceptacle CreditCard_bar__plus_00_dot_72_bar__plus_00_dot_11_bar__minus_02_dot_49 Drawer_bar__plus_00_dot_45_bar__plus_00_dot_20_bar__minus_02_dot_43)
        (inReceptacle Mug_bar__plus_01_dot_66_bar__plus_01_dot_58_bar__minus_02_dot_32 Shelf_bar__plus_01_dot_84_bar__plus_01_dot_57_bar__minus_02_dot_30)
        (inReceptacle Pen_bar__minus_01_dot_86_bar__plus_00_dot_51_bar__minus_01_dot_07 SideTable_bar__minus_01_dot_82_bar__plus_00_dot_00_bar__minus_01_dot_14)
        (inReceptacle KeyChain_bar__minus_01_dot_94_bar__plus_00_dot_51_bar__minus_01_dot_15 SideTable_bar__minus_01_dot_82_bar__plus_00_dot_00_bar__minus_01_dot_14)
        (inReceptacle CreditCard_bar__minus_01_dot_79_bar__plus_00_dot_51_bar__minus_01_dot_15 SideTable_bar__minus_01_dot_82_bar__plus_00_dot_00_bar__minus_01_dot_14)
        (inReceptacle CD_bar__plus_01_dot_38_bar__plus_00_dot_05_bar__minus_02_dot_28 Shelf_bar__plus_01_dot_31_bar__plus_00_dot_05_bar__minus_02_dot_30)
        (inReceptacle Vase_bar__plus_01_dot_19_bar__plus_00_dot_36_bar__minus_02_dot_30 Shelf_bar__plus_01_dot_31_bar__plus_00_dot_35_bar__minus_02_dot_30)
        (inReceptacle Pillow_bar__minus_01_dot_55_bar__plus_00_dot_46_bar__minus_01_dot_83 Bed_bar__minus_01_dot_05_bar__plus_00_dot_00_bar__minus_01_dot_85)
        (inReceptacle CellPhone_bar__minus_01_dot_02_bar__plus_00_dot_42_bar__minus_01_dot_61 Bed_bar__minus_01_dot_05_bar__plus_00_dot_00_bar__minus_01_dot_85)
        
        
        (receptacleAtLocation Bed_bar__minus_01_dot_05_bar__plus_00_dot_00_bar__minus_01_dot_85 loc_bar__minus_4_bar__minus_2_bar_2_bar_45)
        (receptacleAtLocation Desk_bar__minus_01_dot_71_bar__plus_00_dot_00_bar__minus_00_dot_37 loc_bar__minus_3_bar__minus_2_bar_3_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_45_bar__plus_00_dot_20_bar__minus_02_dot_43 loc_bar_2_bar__minus_8_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_45_bar__plus_00_dot_50_bar__minus_02_dot_43 loc_bar_2_bar__minus_8_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_79_bar__plus_00_dot_10_bar__minus_01_dot_15 loc_bar__minus_2_bar__minus_2_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_01_dot_79_bar__plus_00_dot_25_bar__minus_01_dot_15 loc_bar__minus_3_bar__minus_3_bar_3_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_79_bar__plus_00_dot_39_bar__minus_01_dot_15 loc_bar__minus_3_bar__minus_3_bar_3_bar_45)
        (receptacleAtLocation Dresser_bar__plus_00_dot_44_bar__plus_00_dot_04_bar__minus_02_dot_65 loc_bar_2_bar__minus_8_bar_2_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_02_dot_22_bar__plus_00_dot_00_bar__plus_00_dot_31 loc_bar_7_bar_0_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_31_bar__plus_00_dot_05_bar__minus_02_dot_30 loc_bar_3_bar__minus_5_bar_2_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_31_bar__plus_00_dot_35_bar__minus_02_dot_30 loc_bar_7_bar__minus_6_bar_2_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_31_bar__plus_00_dot_97_bar__minus_02_dot_30 loc_bar_5_bar__minus_7_bar_2_bar_45)
        (receptacleAtLocation Shelf_bar__plus_01_dot_31_bar__plus_01_dot_17_bar__minus_02_dot_30 loc_bar_5_bar__minus_7_bar_2_bar_45)
        (receptacleAtLocation Shelf_bar__plus_01_dot_31_bar__plus_01_dot_37_bar__minus_02_dot_30 loc_bar_5_bar__minus_7_bar_2_bar_30)
        (receptacleAtLocation Shelf_bar__plus_01_dot_31_bar__plus_01_dot_57_bar__minus_02_dot_30 loc_bar_5_bar__minus_7_bar_2_bar_15)
        (receptacleAtLocation Shelf_bar__plus_01_dot_84_bar__plus_00_dot_05_bar__minus_02_dot_30 loc_bar_5_bar__minus_5_bar_2_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_84_bar__plus_00_dot_35_bar__minus_02_dot_30 loc_bar_6_bar__minus_6_bar_2_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_84_bar__plus_00_dot_97_bar__minus_02_dot_30 loc_bar_7_bar__minus_7_bar_2_bar_45)
        (receptacleAtLocation Shelf_bar__plus_01_dot_84_bar__plus_01_dot_17_bar__minus_02_dot_30 loc_bar_7_bar__minus_7_bar_2_bar_45)
        (receptacleAtLocation Shelf_bar__plus_01_dot_84_bar__plus_01_dot_37_bar__minus_02_dot_30 loc_bar_7_bar__minus_7_bar_2_bar_30)
        (receptacleAtLocation Shelf_bar__plus_01_dot_84_bar__plus_01_dot_57_bar__minus_02_dot_30 loc_bar_7_bar__minus_7_bar_2_bar_15)
        (receptacleAtLocation SideTable_bar__minus_01_dot_82_bar__plus_00_dot_00_bar__minus_01_dot_14 loc_bar__minus_4_bar__minus_3_bar_3_bar_60)
        (objectAtLocation Mug_bar__plus_00_dot_54_bar__plus_00_dot_73_bar__minus_02_dot_47 loc_bar_2_bar__minus_8_bar_2_bar_60)
        (objectAtLocation Pen_bar__minus_01_dot_86_bar__plus_00_dot_51_bar__minus_01_dot_07 loc_bar__minus_4_bar__minus_3_bar_3_bar_60)
        (objectAtLocation AlarmClock_bar__plus_00_dot_08_bar__plus_00_dot_73_bar__minus_02_dot_76 loc_bar_2_bar__minus_8_bar_2_bar_60)
        (objectAtLocation CreditCard_bar__minus_01_dot_79_bar__plus_00_dot_51_bar__minus_01_dot_15 loc_bar__minus_4_bar__minus_3_bar_3_bar_60)
        (objectAtLocation Vase_bar__plus_00_dot_77_bar__plus_00_dot_73_bar__minus_02_dot_71 loc_bar_2_bar__minus_8_bar_2_bar_60)
        (objectAtLocation Vase_bar__minus_01_dot_65_bar__plus_00_dot_60_bar__minus_00_dot_53 loc_bar__minus_3_bar__minus_2_bar_3_bar_60)
        (objectAtLocation Mug_bar__plus_01_dot_66_bar__plus_01_dot_58_bar__minus_02_dot_32 loc_bar_7_bar__minus_7_bar_2_bar_15)
        (objectAtLocation Box_bar__plus_02_dot_00_bar__plus_00_dot_32_bar__minus_01_dot_04 loc_bar_6_bar__minus_4_bar_1_bar_60)
        (objectAtLocation Chair_bar__minus_01_dot_56_bar__plus_00_dot_00_bar__minus_00_dot_31 loc_bar__minus_4_bar__minus_1_bar_3_bar_60)
        (objectAtLocation Book_bar__plus_00_dot_34_bar__plus_00_dot_72_bar__minus_02_dot_65 loc_bar_2_bar__minus_8_bar_2_bar_60)
        (objectAtLocation Pillow_bar__minus_01_dot_55_bar__plus_00_dot_46_bar__minus_01_dot_83 loc_bar__minus_4_bar__minus_2_bar_2_bar_45)
        (objectAtLocation CellPhone_bar__minus_01_dot_02_bar__plus_00_dot_42_bar__minus_01_dot_61 loc_bar__minus_4_bar__minus_2_bar_2_bar_45)
        (objectAtLocation Vase_bar__plus_01_dot_19_bar__plus_00_dot_36_bar__minus_02_dot_30 loc_bar_7_bar__minus_6_bar_2_bar_60)
        (objectAtLocation DeskLamp_bar__minus_01_dot_85_bar__plus_00_dot_59_bar__minus_00_dot_79 loc_bar__minus_3_bar__minus_2_bar_3_bar_60)
        (objectAtLocation BaseballBat_bar__minus_01_dot_63_bar__plus_00_dot_65_bar__plus_00_dot_42 loc_bar__minus_4_bar_0_bar_3_bar_60)
        (objectAtLocation CreditCard_bar__plus_00_dot_72_bar__plus_00_dot_11_bar__minus_02_dot_49 loc_bar_2_bar__minus_8_bar_2_bar_60)
        (objectAtLocation TennisRacket_bar__plus_02_dot_24_bar__plus_00_dot_28_bar__minus_01_dot_90 loc_bar_7_bar__minus_7_bar_1_bar_60)
        (objectAtLocation KeyChain_bar__minus_01_dot_94_bar__plus_00_dot_51_bar__minus_01_dot_15 loc_bar__minus_4_bar__minus_3_bar_3_bar_60)
        (objectAtLocation LightSwitch_bar__minus_00_dot_64_bar__plus_01_dot_15_bar__plus_00_dot_50 loc_bar__minus_3_bar_0_bar_0_bar_45)
        (objectAtLocation AlarmClock_bar__plus_00_dot_08_bar__plus_00_dot_73_bar__minus_02_dot_59 loc_bar_2_bar__minus_8_bar_2_bar_60)
        (objectAtLocation Poster_bar__plus_00_dot_00_bar__plus_01_dot_50_bar__minus_02_dot_64 loc_bar_1_bar__minus_8_bar_2_bar_15)
        (objectAtLocation Cloth_bar__plus_02_dot_18_bar__plus_00_dot_00_bar__minus_00_dot_57 loc_bar_7_bar__minus_2_bar_1_bar_60)
        (objectAtLocation Laptop_bar__minus_01_dot_76_bar__plus_00_dot_59_bar__minus_00_dot_11 loc_bar__minus_3_bar__minus_2_bar_3_bar_60)
        (objectAtLocation Mirror_bar__minus_01_dot_07_bar__plus_01_dot_47_bar__plus_00_dot_50 loc_bar__minus_4_bar_0_bar_0_bar_15)
        (objectAtLocation CD_bar__plus_02_dot_22_bar__plus_00_dot_04_bar__plus_00_dot_31 loc_bar_7_bar_0_bar_1_bar_60)
        (objectAtLocation CD_bar__plus_01_dot_38_bar__plus_00_dot_05_bar__minus_02_dot_28 loc_bar_3_bar__minus_5_bar_2_bar_60)
        (objectAtLocation Pen_bar__plus_01_dot_98_bar__plus_00_dot_98_bar__minus_02_dot_24 loc_bar_7_bar__minus_7_bar_2_bar_45)
        (objectAtLocation Window_bar__plus_00_dot_44_bar__plus_01_dot_40_bar__minus_02_dot_86 loc_bar_2_bar__minus_8_bar_2_bar_60)
        (objectAtLocation Pencil_bar__minus_01_dot_71_bar__plus_00_dot_60_bar__minus_00_dot_68 loc_bar__minus_3_bar__minus_2_bar_3_bar_60)
        (objectAtLocation Mug_bar__plus_01_dot_32_bar__plus_01_dot_18_bar__minus_02_dot_28 loc_bar_5_bar__minus_7_bar_2_bar_45)
        (objectAtLocation Blinds_bar__plus_00_dot_45_bar__plus_02_dot_16_bar__minus_02_dot_85 loc_bar_2_bar__minus_8_bar_2_bar__minus_30)
        )
    

        (:goal
             (and
                 (exists (?ot - object
                          ?r - receptacle
                          ?a - agent
                          ?l - location)
                     (and
                         (objectType ?ot DeskLampType)
                         (toggleable ?ot)
                         (isToggled ?ot)
                         (receptacleAtLocation ?r ?l)
                         (atLocation ?a ?l)
                         (inReceptacle ?ot ?r)
                     )
                 )
                 (exists (?o - object
                          ?a - agent)
                     (and
                         (objectType ?o BaseballBatType)
                         (holds ?a ?o)
                     )
                 )
             )
        )
    )
    