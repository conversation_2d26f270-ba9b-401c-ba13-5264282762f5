{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000248.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000249.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000254.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000255.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000256.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000258.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000259.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000260.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000261.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000262.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000263.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000264.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000265.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000266.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000267.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000268.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000269.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000270.png", "low_idx": 41}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|0|-4|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [0.441168844, 0.441168844, -6.144452, -6.144452, 0.46035242, 0.46035242]], "coordinateReceptacleObjectId": ["Cabinet", [-0.8108, -0.8108, -5.5664, -5.5664, 1.6076, 1.6076]], "forceVisible": true, "objectId": "Cup|+00.11|+00.12|-01.54"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|2|0|1|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [5.688, 5.688, 0.092, 0.092, 4.6, 4.6]], "forceVisible": true, "objectId": "Microwave|+01.42|+01.15|+00.02"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|2|-2|2|60"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [0.441168844, 0.441168844, -6.144452, -6.144452, 0.46035242, 0.46035242]], "coordinateReceptacleObjectId": ["Cabinet", [-0.8108, -0.8108, -5.5664, -5.5664, 1.6076, 1.6076]], "forceVisible": true, "objectId": "Cup|+00.11|+00.12|-01.54", "receptacleObjectId": "Cabinet|-00.20|+00.40|-01.39"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-00.20|+00.40|-01.39"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [69, 178, 203, 224], "mask": [[53170, 133], [53470, 133], [53769, 134], [54069, 134], [54369, 135], [54669, 134], [54970, 133], [55271, 131], [55571, 131], [55872, 129], [56173, 128], [56474, 126], [56774, 126], [57075, 124], [57376, 123], [57676, 123], [57977, 121], [58278, 120], [58578, 119], [58879, 118], [59180, 116], [59480, 116], [59781, 114], [60082, 113], [60382, 113], [60683, 111], [60984, 110], [61284, 109], [61585, 108], [61886, 106], [62187, 105], [62487, 104], [62788, 103], [63089, 101], [63389, 101], [63690, 100], [63991, 98], [64291, 98], [64592, 96], [64893, 95], [65193, 94], [65494, 93], [65795, 91], [66095, 91], [66396, 89], [66697, 88], [66998, 86]], "point": [136, 200]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+00.11|+00.12|-01.54"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [120, 178, 139, 201], "mask": [[53229, 2], [53525, 9], [53824, 12], [54123, 14], [54422, 15], [54722, 16], [55021, 18], [55321, 18], [55621, 18], [55920, 19], [56220, 20], [56520, 20], [56820, 20], [57121, 18], [57421, 18], [57721, 18], [58022, 16], [58323, 15], [58624, 14], [58925, 13], [59225, 12], [59526, 11], [59826, 10], [60128, 6]], "point": [129, 188]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-00.20|+00.40|-01.39"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [43, 178, 201, 300], "mask": [[53164, 138], [53464, 138], [53764, 137], [54064, 137], [54364, 137], [54664, 107], [54776, 24], [54963, 106], [55078, 22], [55263, 105], [55380, 19], [55563, 104], [55680, 19], [55863, 103], [55981, 17], [56163, 103], [56281, 17], [56462, 104], [56582, 16], [56762, 104], [56882, 15], [57062, 104], [57182, 15], [57362, 104], [57482, 14], [57662, 83], [57755, 11], [57781, 15], [57962, 76], [58062, 4], [58081, 14], [58261, 74], [58365, 1], [58380, 15], [58561, 72], [58680, 15], [58861, 69], [58979, 15], [59161, 67], [59279, 15], [59461, 65], [59579, 14], [59760, 65], [59878, 15], [60060, 63], [60177, 15], [60360, 62], [60478, 14], [60660, 60], [60780, 12], [60960, 59], [61081, 10], [61260, 58], [61382, 9], [61559, 58], [61683, 7], [61859, 57], [61984, 6], [62159, 56], [62285, 4], [62459, 55], [62586, 3], [62759, 54], [62887, 2], [63058, 54], [63358, 54], [63658, 53], [63958, 52], [64258, 51], [64558, 51], [64857, 51], [65157, 51], [65457, 50], [65757, 50], [66057, 41], [66357, 41], [66656, 42], [66956, 42], [67256, 42], [67556, 41], [67856, 41], [68155, 42], [68455, 42], [68755, 42], [69055, 42], [69355, 42], [69655, 42], [69954, 43], [70254, 43], [70554, 42], [70854, 42], [71154, 42], [71453, 43], [71753, 43], [72053, 43], [72353, 43], [72653, 43], [72953, 43], [73252, 44], [73552, 43], [73852, 43], [74152, 43], [74452, 43], [74752, 43], [75051, 44], [75351, 44], [75651, 44], [75951, 44], [76251, 44], [76550, 44], [76850, 44], [77150, 44], [77450, 44], [77750, 44], [78050, 44], [78349, 45], [78649, 45], [78949, 45], [79249, 45], [79549, 44], [79848, 45], [80148, 45], [80448, 45], [80748, 45], [81048, 45], [81348, 45], [81647, 46], [81947, 46], [82247, 46], [82547, 45], [82847, 45], [83146, 46], [83446, 46], [83746, 46], [84046, 46], [84346, 46], [84646, 46], [84945, 47], [85245, 47], [85545, 46], [85845, 46], [86145, 46], [86445, 46], [86744, 47], [87044, 47], [87344, 47], [87644, 47], [87944, 47], [88243, 48], [88543, 47], [88843, 47], [89143, 47], [89443, 47], [89743, 47]], "point": [106, 219]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+00.11|+00.12|-01.54", "placeStationary": true, "receptacleObjectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 24, 252, 209], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9622, 226], [9918, 233], [10214, 239], [10510, 243], [10806, 247], [11102, 251], [11400, 253], [11700, 252], [12000, 252], [12300, 252], [12600, 252], [12900, 252], [13200, 251], [13500, 251], [13800, 251], [14100, 251], [14400, 251], [14700, 251], [15000, 250], [15300, 250], [15600, 250], [15900, 250], [16200, 250], [16500, 249], [16800, 249], [17100, 249], [17400, 249], [17700, 249], [18000, 248], [18300, 248], [18600, 248], [18900, 248], [19200, 248], [19500, 248], [19800, 247], [20100, 247], [20400, 247], [20700, 247], [21000, 247], [21300, 246], [21600, 246], [21900, 246], [22200, 246], [22500, 246], [22800, 246], [23100, 245], [23400, 245], [23700, 245], [24000, 245], [24300, 245], [24600, 244], [24900, 244], [25200, 244], [25500, 244], [25800, 244], [26100, 244], [26400, 243], [26700, 243], [27000, 243], [27300, 243], [27600, 243], [27900, 242], [28200, 242], [28500, 242], [28800, 242], [29100, 242], [29400, 242], [29700, 241], [30000, 241], [30300, 241], [30600, 241], [30900, 241], [31200, 240], [31500, 240], [31800, 240], [32100, 240], [32400, 240], [32700, 240], [33000, 239], [33300, 239], [33600, 239], [33900, 239], [34200, 239], [34500, 238], [34800, 238], [35100, 238], [35400, 238], [35700, 238], [36000, 238], [36300, 237], [36600, 237], [36900, 237], [37200, 237], [37500, 237], [37800, 236], [38100, 236], [38400, 236], [38700, 236], [39000, 236], [39300, 236], [39600, 55], [39677, 6], [39798, 37], [39900, 54], [39977, 4], [40098, 37], [40200, 54], [40399, 36], [40500, 53], [40800, 52], [41100, 52], [41400, 51], [41700, 50], [42000, 49], [42300, 49], [42600, 48], [42900, 47], [43200, 47], [43500, 46], [43800, 45], [44100, 44], [44400, 44], [44700, 43], [45000, 42], [45300, 41], [45600, 41], [45900, 40], [46200, 39], [46500, 39], [46800, 38], [47100, 37], [47400, 36], [47700, 36], [48000, 35], [48300, 34], [48600, 34], [48900, 33], [49200, 32], [49500, 31], [49800, 31], [50100, 30], [50400, 29], [50700, 29], [51000, 28], [51300, 27], [51600, 26], [51900, 26], [52200, 25], [52500, 24], [52800, 24], [53100, 23], [53400, 22], [53700, 21], [54000, 21], [54300, 20], [54600, 19], [54900, 19], [55200, 18], [55500, 17], [55800, 16], [56100, 16], [56400, 15], [56700, 14], [57000, 14], [57300, 13], [57600, 12], [57900, 11], [58200, 11], [58500, 10], [58800, 9], [59100, 9], [59400, 8], [59700, 7], [60000, 6], [60300, 6], [60600, 5], [60900, 4], [61200, 4], [61500, 3], [61800, 2], [62100, 1], [62400, 1]], "point": [126, 115]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 24, 252, 209], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9622, 226], [9918, 233], [10214, 239], [10510, 243], [10806, 247], [11102, 251], [11400, 253], [11700, 252], [12000, 252], [12300, 252], [12600, 252], [12900, 252], [13200, 251], [13500, 251], [13800, 251], [14100, 251], [14400, 251], [14700, 251], [15000, 250], [15300, 250], [15600, 250], [15900, 250], [16200, 250], [16500, 146], [16667, 82], [16800, 143], [16970, 79], [17100, 142], [17271, 78], [17400, 142], [17571, 78], [17700, 141], [17872, 77], [18000, 141], [18172, 76], [18300, 141], [18472, 76], [18600, 141], [18772, 76], [18900, 141], [19072, 76], [19200, 140], [19373, 75], [19500, 140], [19673, 75], [19800, 140], [19973, 74], [20100, 140], [20273, 74], [20400, 140], [20573, 74], [20700, 140], [20872, 75], [21000, 140], [21172, 75], [21300, 140], [21472, 74], [21600, 141], [21772, 74], [21900, 141], [22072, 74], [22200, 141], [22371, 75], [22500, 142], [22671, 75], [22800, 142], [22971, 75], [23100, 142], [23271, 74], [23400, 143], [23570, 75], [23700, 143], [23870, 75], [24000, 144], [24169, 76], [24300, 144], [24469, 76], [24600, 145], [24768, 76], [24900, 145], [25068, 76], [25200, 145], [25367, 77], [25500, 146], [25667, 77], [25800, 146], [25967, 77], [26100, 146], [26267, 77], [26400, 146], [26566, 77], [26700, 146], [26866, 77], [27000, 146], [27166, 77], [27300, 146], [27466, 77], [27600, 146], [27766, 77], [27900, 146], [28066, 76], [28200, 146], [28366, 76], [28500, 146], [28666, 76], [28800, 146], [28967, 75], [29100, 145], [29267, 75], [29400, 145], [29567, 75], [29700, 145], [29867, 74], [30000, 145], [30167, 74], [30300, 145], [30467, 74], [30600, 145], [30767, 74], [30900, 147], [31066, 75], [31200, 149], [31363, 77], [31500, 240], [31800, 240], [32100, 240], [32400, 240], [32700, 240], [33000, 239], [33300, 239], [33600, 239], [33900, 239], [34200, 239], [34500, 238], [34800, 238], [35100, 238], [35400, 238], [35700, 238], [36000, 238], [36300, 237], [36600, 237], [36900, 237], [37200, 237], [37500, 237], [37800, 236], [38100, 236], [38400, 236], [38700, 236], [39000, 236], [39300, 236], [39600, 55], [39677, 6], [39798, 37], [39900, 54], [39977, 4], [40098, 37], [40200, 54], [40399, 36], [40500, 53], [40800, 52], [41100, 52], [41400, 51], [41700, 50], [42000, 49], [42300, 49], [42600, 48], [42900, 47], [43200, 47], [43500, 46], [43800, 45], [44100, 44], [44400, 44], [44700, 43], [45000, 42], [45300, 41], [45600, 41], [45900, 40], [46200, 39], [46500, 39], [46800, 38], [47100, 37], [47400, 36], [47700, 36], [48000, 35], [48300, 34], [48600, 34], [48900, 33], [49200, 32], [49500, 31], [49800, 31], [50100, 30], [50400, 29], [50700, 29], [51000, 28], [51300, 27], [51600, 26], [51900, 26], [52200, 25], [52500, 24], [52800, 24], [53100, 23], [53400, 22], [53700, 21], [54000, 21], [54300, 20], [54600, 19], [54900, 19], [55200, 18], [55500, 17], [55800, 16], [56100, 16], [56400, 15], [56700, 14], [57000, 14], [57300, 13], [57600, 12], [57900, 11], [58200, 11], [58500, 10], [58800, 9], [59100, 9], [59400, 8], [59700, 7], [60000, 6], [60300, 6], [60600, 5], [60900, 4], [61200, 4], [61500, 3], [61800, 2], [62100, 1], [62400, 1]], "point": [126, 115]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [5.688, 5.688, 0.092, 0.092, 4.6, 4.6]], "forceVisible": true, "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [5.688, 5.688, 0.092, 0.092, 4.6, 4.6]], "forceVisible": true, "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+00.11|+00.12|-01.54"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [140, 56, 172, 105], "mask": [[16646, 21], [16943, 27], [17242, 29], [17542, 29], [17841, 31], [18141, 31], [18441, 31], [18741, 31], [19041, 31], [19340, 33], [19640, 33], [19940, 33], [20240, 33], [20540, 33], [20840, 32], [21140, 32], [21440, 32], [21741, 31], [22041, 31], [22341, 30], [22642, 29], [22942, 29], [23242, 29], [23543, 27], [23843, 27], [24144, 25], [24444, 25], [24745, 23], [25045, 23], [25345, 22], [25646, 21], [25946, 21], [26246, 21], [26546, 20], [26846, 20], [27146, 20], [27446, 20], [27746, 20], [28046, 20], [28346, 20], [28646, 20], [28946, 21], [29245, 22], [29545, 22], [29845, 22], [30145, 22], [30445, 22], [30745, 22], [31047, 19], [31349, 14]], "point": [156, 79]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 24, 252, 209], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9622, 226], [9918, 233], [10214, 239], [10510, 243], [10806, 247], [11102, 251], [11400, 253], [11700, 252], [12000, 252], [12300, 252], [12600, 252], [12900, 252], [13200, 251], [13500, 251], [13800, 251], [14100, 251], [14400, 251], [14700, 251], [15000, 250], [15300, 250], [15600, 250], [15900, 250], [16200, 250], [16500, 249], [16800, 249], [17100, 249], [17400, 249], [17700, 249], [18000, 248], [18300, 248], [18600, 248], [18900, 248], [19200, 248], [19500, 248], [19800, 247], [20100, 247], [20400, 247], [20700, 247], [21000, 247], [21300, 246], [21600, 246], [21900, 246], [22200, 246], [22500, 246], [22800, 246], [23100, 245], [23400, 245], [23700, 245], [24000, 245], [24300, 245], [24600, 244], [24900, 244], [25200, 244], [25500, 244], [25800, 244], [26100, 244], [26400, 243], [26700, 243], [27000, 243], [27300, 243], [27600, 243], [27900, 242], [28200, 242], [28500, 242], [28800, 242], [29100, 242], [29400, 242], [29700, 241], [30000, 241], [30300, 241], [30600, 241], [30900, 241], [31200, 240], [31500, 240], [31800, 240], [32100, 240], [32400, 240], [32700, 240], [33000, 239], [33300, 239], [33600, 239], [33900, 239], [34200, 239], [34500, 238], [34800, 238], [35100, 238], [35400, 238], [35700, 238], [36000, 238], [36300, 237], [36600, 237], [36900, 237], [37200, 237], [37500, 237], [37800, 236], [38100, 236], [38400, 236], [38700, 236], [39000, 236], [39300, 236], [39600, 55], [39677, 6], [39798, 37], [39900, 54], [39977, 4], [40098, 37], [40200, 54], [40399, 36], [40500, 53], [40800, 52], [41100, 52], [41400, 51], [41700, 50], [42000, 49], [42300, 49], [42600, 48], [42900, 47], [43200, 47], [43500, 46], [43800, 45], [44100, 44], [44400, 44], [44700, 43], [45000, 42], [45300, 41], [45600, 41], [45900, 40], [46200, 39], [46500, 39], [46800, 38], [47100, 37], [47400, 36], [47700, 36], [48000, 35], [48300, 34], [48600, 34], [48900, 33], [49200, 32], [49500, 31], [49800, 31], [50100, 30], [50400, 29], [50700, 29], [51000, 28], [51300, 27], [51600, 26], [51900, 26], [52200, 25], [52500, 24], [52800, 24], [53100, 23], [53400, 22], [53700, 21], [54000, 21], [54300, 20], [54600, 19], [54900, 19], [55200, 18], [55500, 17], [55800, 16], [56100, 16], [56400, 15], [56700, 14], [57000, 14], [57300, 13], [57600, 12], [57900, 11], [58200, 11], [58500, 10], [58800, 9], [59100, 9], [59400, 8], [59700, 7], [60000, 6], [60300, 6], [60600, 5], [60900, 4], [61200, 4], [61500, 3], [61800, 2], [62100, 1], [62400, 1]], "point": [126, 115]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-00.20|+00.40|-01.39"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [181, 79, 299, 147], "mask": [[23594, 106], [23894, 106], [24194, 106], [24494, 106], [24793, 107], [25093, 106], [25393, 105], [25693, 105], [25993, 104], [26292, 104], [26592, 104], [26892, 103], [27192, 102], [27492, 102], [27791, 102], [28091, 101], [28391, 101], [28691, 100], [28991, 99], [29290, 100], [29590, 99], [29890, 98], [30190, 97], [30490, 97], [30789, 97], [31089, 96], [31389, 96], [31689, 95], [31989, 94], [32288, 95], [32588, 94], [32888, 93], [33188, 93], [33488, 92], [33787, 92], [34087, 92], [34387, 91], [34687, 90], [34987, 89], [35286, 90], [35586, 89], [35886, 88], [36186, 88], [36486, 87], [36785, 87], [37085, 87], [37385, 86], [37685, 85], [37985, 85], [38284, 85], [38584, 84], [38884, 83], [39184, 83], [39484, 82], [39783, 82], [40083, 82], [40383, 81], [40683, 80], [40983, 80], [41282, 80], [41582, 79], [41882, 79], [42182, 78], [42482, 77], [42781, 78], [43081, 77], [43381, 76], [43681, 75], [43981, 75]], "point": [240, 112]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+00.11|+00.12|-01.54", "placeStationary": true, "receptacleObjectId": "Cabinet|-00.20|+00.40|-01.39"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [177, 79, 298, 196], "mask": [[23590, 109], [23890, 108], [24189, 109], [24489, 108], [24789, 107], [25089, 107], [25388, 107], [25688, 106], [25988, 106], [26288, 105], [26587, 105], [26887, 105], [27187, 104], [27487, 103], [27787, 103], [28086, 103], [28386, 102], [28686, 102], [28986, 101], [29286, 100], [29586, 100], [29885, 100], [30185, 99], [30485, 99], [30785, 98], [31085, 97], [31385, 97], [31684, 97], [31984, 96], [32284, 96], [32584, 95], [32884, 94], [33183, 95], [33483, 94], [33783, 60], [33848, 28], [34083, 58], [34150, 26], [34383, 57], [34451, 24], [34683, 57], [34752, 22], [34982, 58], [35053, 21], [35282, 57], [35353, 20], [35582, 57], [35653, 19], [35882, 56], [35953, 18], [36182, 56], [36253, 18], [36482, 55], [36552, 18], [36781, 56], [36851, 18], [37081, 56], [37151, 18], [37381, 55], [37450, 18], [37681, 55], [37750, 17], [37981, 55], [38049, 18], [38280, 57], [38348, 18], [38580, 57], [38648, 17], [38880, 58], [38947, 18], [39180, 60], [39246, 18], [39480, 83], [39780, 83], [40079, 83], [40379, 82], [40679, 82], [40979, 81], [41279, 80], [41579, 80], [41878, 80], [42178, 79], [42478, 79], [42778, 78], [43078, 77], [43377, 25], [43677, 25], [43977, 25], [44277, 26], [44577, 26], [44877, 26], [45177, 26], [45478, 25], [45778, 25], [46078, 25], [46378, 25], [46678, 25], [46978, 26], [47278, 26], [47578, 26], [47878, 26], [48178, 26], [48478, 26], [48779, 25], [49081, 23], [49382, 23], [49683, 22], [49984, 21], [50285, 20], [50586, 19], [50887, 18], [51188, 17], [51488, 16], [51789, 15], [52089, 14], [52390, 13], [52690, 13], [52991, 11], [53291, 11], [53591, 10], [53891, 10], [54191, 10], [54491, 9], [54792, 8], [55092, 7], [55392, 7], [55692, 6], [55992, 6], [56293, 5], [56593, 4], [56893, 4], [57193, 3], [57493, 3], [57793, 2], [58093, 2], [58393, 2], [58693, 1]], "point": [237, 136]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-00.20|+00.40|-01.39"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [177, 79, 298, 215], "mask": [[23590, 109], [23890, 108], [24189, 109], [24489, 108], [24789, 107], [25089, 107], [25388, 107], [25688, 106], [25988, 106], [26288, 105], [26587, 105], [26887, 105], [27187, 104], [27487, 103], [27787, 103], [28086, 103], [28386, 102], [28686, 102], [28986, 101], [29286, 100], [29586, 100], [29885, 100], [30185, 99], [30485, 99], [30785, 98], [31085, 97], [31385, 97], [31684, 97], [31984, 96], [32284, 96], [32584, 95], [32884, 94], [33183, 95], [33483, 94], [33783, 26], [33814, 29], [33848, 28], [34083, 24], [34116, 25], [34150, 26], [34383, 23], [34418, 22], [34451, 24], [34683, 22], [34718, 22], [34752, 22], [34982, 22], [35019, 21], [35053, 21], [35282, 21], [35319, 20], [35353, 20], [35582, 21], [35620, 19], [35653, 19], [35882, 21], [35920, 18], [35953, 18], [36182, 21], [36220, 18], [36253, 18], [36482, 20], [36520, 17], [36552, 18], [36781, 21], [36820, 17], [36851, 18], [37081, 22], [37119, 18], [37151, 18], [37381, 22], [37419, 17], [37450, 18], [37681, 22], [37718, 18], [37750, 17], [37981, 23], [38018, 18], [38049, 18], [38280, 23], [38317, 20], [38348, 18], [38580, 23], [38616, 21], [38648, 17], [38880, 22], [38914, 24], [38947, 18], [39180, 22], [39214, 26], [39246, 18], [39480, 22], [39513, 50], [39780, 22], [39813, 50], [40079, 24], [40113, 49], [40379, 25], [40412, 49], [40679, 26], [40711, 50], [40979, 81], [41279, 80], [41579, 80], [41878, 80], [42178, 79], [42478, 79], [42778, 78], [43078, 77], [43377, 25], [43677, 25], [43977, 25], [44277, 26], [44577, 26], [44877, 26], [45177, 26], [45478, 25], [45778, 25], [46078, 25], [46378, 25], [46678, 25], [46978, 26], [47278, 26], [47578, 26], [47878, 26], [48178, 26], [48478, 26], [48778, 26], [49078, 26], [49378, 27], [49678, 27], [49978, 27], [50278, 27], [50579, 26], [50879, 26], [51179, 26], [51479, 25], [51779, 25], [52079, 24], [52379, 24], [52679, 24], [52979, 23], [53279, 23], [53579, 22], [53879, 22], [54179, 22], [54479, 21], [54779, 21], [55079, 20], [55380, 19], [55680, 18], [55980, 18], [56280, 18], [56580, 17], [56880, 17], [57180, 16], [57480, 16], [57780, 15], [58080, 15], [58380, 15], [58680, 14], [58980, 14], [59280, 13], [59580, 13], [59880, 12], [60181, 11], [60481, 11], [60781, 10], [61081, 10], [61381, 9], [61681, 9], [61981, 9], [62281, 8], [62581, 8], [62882, 6], [63182, 6], [63482, 5], [63782, 5], [64082, 5], [64382, 4]], "point": [237, 143]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan8", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": 0.5, "y": 0.9009992, "z": 0.75}, "object_poses": [{"objectName": "Pan_c0048524", "position": {"x": -1.0811, "y": 0.949799955, "z": -1.5272}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_abf5c390", "position": {"x": -2.16595912, "y": 1.65546834, "z": -1.86689568}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SprayBottle_abf5c390", "position": {"x": 1.30206108, "y": 0.213943, "z": 1.02203965}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_73e168e3", "position": {"x": -1.95722759, "y": 0.9131528, "z": 0.463499784}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_73e168e3", "position": {"x": -2.445942, "y": 0.9131528, "z": 0.291}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_238538e2", "position": {"x": 1.41487837, "y": 1.20656252, "z": -1.00233626}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_238538e2", "position": {"x": 1.09300041, "y": 0.159571648, "z": -1.18653619}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_7cca838f", "position": {"x": 0.115530714, "y": 0.9195921, "z": -1.65757918}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "ButterKnife_7cca838f", "position": {"x": -2.116709, "y": 0.9105421, "z": -1.44825029}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_b7024f32", "position": {"x": -0.6526729, "y": 0.111328661, "z": -1.58419991}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_b308514b", "position": {"x": 1.459939, "y": 0.215255618, "z": 1.05101991}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_1d30af71", "position": {"x": 1.53373933, "y": 1.1772, "z": -1.00233626}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_1d30af71", "position": {"x": -1.71287048, "y": 0.925499856, "z": 0.5497497}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_e08d0eeb", "position": {"x": 0.2177765, "y": 0.9488791, "z": -1.85936069}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_bfcb0336", "position": {"x": -1.88603711, "y": 0.910978854, "z": -1.707}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_9b9c5535", "position": {"x": 1.34259534, "y": 1.656733, "z": -1.26409376}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_9b9c5535", "position": {"x": 1.14889407, "y": 0.115593553, "z": -0.699439943}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_af290bda", "position": {"x": 0.320022345, "y": 0.9492503, "z": -1.6575793}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_c69c5340", "position": {"x": 0.0418224037, "y": 1.65547836, "z": -1.87626863}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_c69c5340", "position": {"x": -0.477255583, "y": 0.9112034, "z": -1.70446813}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Tomato_441a8bda", "position": {"x": 1.332814, "y": 1.85734427, "z": 2.55745077}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "PepperShaker_1de14ecd", "position": {"x": -2.07940626, "y": 0.9055, "z": 0.463499784}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1de14ecd", "position": {"x": 1.05215132, "y": 1.65057492, "z": -1.80173135}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_a2768593", "position": {"x": -1.43889976, "y": 1.85330641, "z": -1.802726}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_a2768593", "position": {"x": -0.67566514, "y": 1.65057492, "z": -1.87626863}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_9b638b9f", "position": {"x": 1.17697, "y": 0.989441156, "z": 0.031709373}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_9b638b9f", "position": {"x": -2.20158482, "y": 0.988641143, "z": 0.3772499}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_49b86ad0", "position": {"x": 1.42646933, "y": 1.36246753, "z": 2.226}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Mug_49b86ad0", "position": {"x": 1.37600017, "y": 1.36246753, "z": 2.10100031}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Tomato_441a8bda", "position": {"x": 0.183694571, "y": 0.9596676, "z": -1.75846982}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Glassbottle_b308514b", "position": {"x": 1.494119, "y": 1.16291678, "z": -1.68895817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_af290bda", "position": {"x": -2.07940626, "y": 0.940200269, "z": 0.118500233}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_9b9c5535", "position": {"x": 0.64981544, "y": 0.117729604, "z": -1.58531845}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_7aaf3771", "position": {"x": -2.07940626, "y": 0.911048532, "z": 0.291}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_238538e2", "position": {"x": -0.7033658, "y": 0.95566237, "z": -1.70446813}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_1cf505ed", "position": {"x": 1.27506173, "y": 1.40926373, "z": 1.85100031}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Lettuce_0f08525d", "position": {"x": 1.4264679, "y": 1.43663275, "z": 1.72599983}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Kettle_e75d4607", "position": {"x": -1.431, "y": 0.949799955, "z": -1.81}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_73e168e3", "position": {"x": 1.41487837, "y": 1.164853, "z": -1.34564734}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_a2768593", "position": {"x": 1.0923003, "y": 0.112860918, "z": 0.430900037}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_bfcb0336", "position": {"x": 0.110292211, "y": 0.115088105, "z": -1.536113}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_e08d0eeb", "position": {"x": -0.08693077, "y": 0.805728555, "z": -1.78119993}, "rotation": {"x": 1.40334191e-14, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_1d30af71", "position": {"x": 0.949094832, "y": 0.789916754, "z": -1.013817}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bowl_a0ece05a", "position": {"x": 1.45449877, "y": 1.162751, "z": -0.659025431}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_abf5c390", "position": {"x": 0.9731128, "y": 0.114328861, "z": -0.7362524}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_c0048524", "position": {"x": -1.431, "y": 0.949799955, "z": -1.5257}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_c69c5340", "position": {"x": -1.48177278, "y": 1.85820985, "z": -1.91064763}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_316d55f0", "position": {"x": -1.0811, "y": 0.949799955, "z": -1.81}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_7cca838f", "position": {"x": -0.477255583, "y": 0.911342, "z": -1.44882691}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1de14ecd", "position": {"x": -2.20158482, "y": 0.9055, "z": 0.118500233}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_b7024f32", "position": {"x": 0.279654443, "y": 1.65513921, "z": -1.80173135}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_9b638b9f", "position": {"x": 1.33563781, "y": 1.24034131, "z": -1.34564734}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_b66a32b5", "position": {"x": -1.73225582, "y": 0.94518286, "z": -1.62075019}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_6f7c42a7", "position": {"x": -2.32376337, "y": 0.9116294, "z": 0.463499784}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_49b86ad0", "position": {"x": -0.133226469, "y": 0.109609365, "z": -1.536113}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2234796107, "scene_num": 8}, "task_id": "trial_T20190907_170428_184361", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A320QA9HJFUOZO_345LHZDED091GH3P5Z2QAWJ3X3F3UG", "high_descs": ["Turn left and walk up to the kitchen sink", "Open the cabinet below the sink and remove the clear glass from it", "Turn around and walk a few feet then turn right to face the microwave", "Heat the glass in the microwave and remove it", "Turn right and walk back over to the kitchen sink", "Open the cabinet below the sink and place the glass in it"], "task_desc": "Put the heated glass in the kitchen cabinet", "votes": [1, 1]}, {"assignment_id": "A1WJU1IQ3UTRC6_38BQUHLA9ZH7PYR2FVVW7P0KF7KOME", "high_descs": ["Turn left and walk to the sink.", "Open the cupboard under the sink and pick up the glass.", "Turn around and take the glass to the microwave.", "Heat the glass in the microwave.", "Take the glass back to the cupboard.", "Put the glass inside the cupboard again."], "task_desc": "Heat the glass and put it back in the cupboard.", "votes": [1, 1]}, {"assignment_id": "A36DK84J5YJ942_3X0H8UUIT45IS2D9QEIS0SCCAZVWS6", "high_descs": ["Move to the sink to the left", "pick up a cup from the cabinet", "move to the microwave to the left of the sink", "heat the cup in the microwave", "move to the counter to the left of the sink", "put the cup in the cabinet under the sink"], "task_desc": "Put a heated cup in the cabinet under the sink.", "votes": [1, 1]}]}}