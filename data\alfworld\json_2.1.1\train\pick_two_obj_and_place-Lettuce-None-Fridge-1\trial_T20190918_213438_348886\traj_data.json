{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000091.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000092.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000093.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000094.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000095.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000096.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000097.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000098.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000099.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000100.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000101.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000103.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000104.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000105.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000106.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000107.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000108.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000109.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000110.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000111.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000112.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000113.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000114.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000243.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000244.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000245.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000246.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000247.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000248.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000249.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000283.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000284.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000285.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000286.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000287.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000288.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000289.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000290.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000291.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000292.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000293.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000294.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000295.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000296.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000297.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000298.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000299.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000300.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000301.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000302.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000303.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000304.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000305.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000306.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000307.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000308.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000309.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000310.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000311.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000312.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000313.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000314.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000315.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000316.png", "low_idx": 64}, {"high_idx": 7, "image_name": "000000317.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000318.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000319.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000320.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000321.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000322.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000323.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000324.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000325.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000326.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000327.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000328.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000329.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000330.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000331.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000332.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000333.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000334.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000335.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000336.png", "low_idx": 67}, {"high_idx": 7, "image_name": "000000337.png", "low_idx": 67}, {"high_idx": 7, "image_name": "000000338.png", "low_idx": 67}, {"high_idx": 7, "image_name": "000000339.png", "low_idx": 67}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Lettuce", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|1|1|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["lettuce"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Lettuce", [-0.789180516, -0.789180516, 1.007095336, 1.007095336, 4.7648902, 4.7648902]], "coordinateReceptacleObjectId": ["CounterTop", [-0.316, -0.316, -0.004, -0.004, 4.5884, 4.5884]], "forceVisible": true, "objectId": "Lettuce|-00.20|+01.19|+00.25"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-5|4|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["lettuce", "fridge"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Lettuce", [-0.789180516, -0.789180516, 1.007095336, 1.007095336, 4.7648902, 4.7648902]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-8.388, -8.388, 4.352, 4.352, 0.0, 0.0]], "forceVisible": true, "objectId": "Lettuce|-00.20|+01.19|+00.25", "receptacleObjectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-5|-7|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["lettuce"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Lettuce", [-5.163356, -5.163356, -9.93382644, -9.93382644, 3.964089872, 3.964089872]], "coordinateReceptacleObjectId": ["CounterTop", [-7.472, -7.472, -4.824, -4.824, 3.7876, 3.7876]], "forceVisible": true, "objectId": "Lettuce|-01.29|+00.99|-02.48"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-5|4|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["lettuce", "fridge"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Lettuce", [-5.163356, -5.163356, -9.93382644, -9.93382644, 3.964089872, 3.964089872]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-8.388, -8.388, 4.352, 4.352, 0.0, 0.0]], "forceVisible": true, "objectId": "Lettuce|-01.29|+00.99|-02.48", "receptacleObjectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Lettuce|-00.20|+01.19|+00.25"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [114, 115, 184, 155], "mask": [[34354, 19], [34649, 26], [34945, 32], [35242, 37], [35539, 41], [35835, 46], [36133, 49], [36430, 52], [36728, 55], [37026, 58], [37324, 60], [37622, 62], [37921, 63], [38219, 66], [38518, 67], [38816, 69], [39115, 70], [39415, 70], [39715, 70], [40015, 70], [40314, 71], [40614, 71], [40914, 71], [41215, 70], [41515, 70], [41815, 70], [42115, 70], [42416, 69], [42718, 66], [43020, 64], [43322, 62], [43624, 59], [43927, 56], [44229, 53], [44532, 49], [44835, 45], [45139, 41], [45444, 34], [45748, 28], [46051, 23], [46364, 7]], "point": [149, 134]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 222], "mask": [[0, 17100], [17101, 299], [17402, 298], [17707, 293], [18008, 292], [18308, 292], [18609, 291], [18909, 291], [19210, 290], [19511, 289], [19811, 289], [20112, 288], [20412, 288], [20713, 287], [21014, 286], [21314, 286], [21615, 285], [21915, 285], [22216, 284], [22517, 283], [22817, 283], [23118, 282], [23418, 282], [23719, 281], [24020, 280], [24320, 280], [24621, 279], [24921, 279], [25222, 278], [25523, 277], [25823, 277], [26124, 276], [26424, 276], [26725, 275], [27025, 275], [27326, 274], [27627, 273], [27927, 273], [28228, 272], [28528, 272], [28829, 271], [29130, 270], [29430, 270], [29731, 269], [30031, 269], [30332, 268], [30633, 267], [30933, 267], [31234, 266], [31534, 266], [31835, 265], [32136, 264], [32436, 264], [32737, 263], [33037, 263], [33338, 262], [33639, 261], [33939, 261], [34240, 260], [34540, 260], [34841, 259], [35142, 258], [35442, 258], [35743, 257], [36043, 257], [36344, 256], [36645, 255], [36945, 255], [37246, 254], [37546, 254], [37847, 253], [38147, 253], [38448, 252], [38749, 251], [39049, 251], [39350, 250], [39650, 250], [39951, 249], [40252, 248], [40552, 248], [40853, 247], [41153, 247], [41454, 246], [41755, 245], [42055, 245], [42356, 244], [42656, 244], [42957, 243], [43258, 242], [43558, 242], [43859, 241], [44159, 241], [44460, 239], [44761, 237], [45061, 236], [45362, 234], [45662, 233], [45963, 231], [46264, 229], [46564, 228], [46865, 226], [47165, 225], [47466, 223], [47767, 221], [48067, 220], [48368, 218], [48668, 217], [48969, 215], [49270, 213], [49570, 212], [49871, 210], [50171, 209], [50472, 207], [50772, 206], [51073, 204], [51374, 202], [51674, 201], [51975, 199], [52275, 198], [52576, 196], [52877, 194], [53177, 193], [53478, 191], [53778, 190], [54079, 189], [54380, 187], [54680, 186], [54981, 184], [55281, 183], [55582, 181], [55883, 179], [56183, 178], [56484, 176], [56784, 175], [57085, 173], [57386, 171], [57686, 170], [57987, 168], [58287, 167], [58588, 165], [58889, 163], [59189, 162], [59489, 56], [59554, 96], [59789, 55], [59855, 94], [60089, 53], [60156, 92], [60389, 52], [60458, 89], [60689, 50], [60760, 86], [60989, 49], [61061, 84], [61289, 48], [61362, 82], [61588, 48], [61664, 79], [61888, 48], [61965, 77], [62188, 47], [62266, 75], [62489, 45], [62567, 74], [62790, 43], [62868, 72], [63091, 41], [63169, 70], [63391, 40], [63470, 68], [63693, 37], [63771, 65], [63996, 33], [64072, 60], [64300, 28], [64373, 56], [64603, 24], [64673, 53], [64907, 20], [64974, 48], [65212, 14], [65275, 42], [65518, 7], [65576, 35], [65877, 28], [66178, 19], [66478, 10]], "point": [149, 110]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Lettuce|-00.20|+01.19|+00.25", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 18473], [18474, 296], [18777, 287], [19066, 2], [19078, 284], [19379, 282], [19680, 280], [19982, 277], [20283, 276], [20583, 275], [20884, 274], [21184, 274], [21484, 274], [21784, 274], [22084, 274], [22384, 274], [22684, 264], [22950, 8], [22984, 262], [23250, 2], [23255, 3], [23283, 263], [23550, 2], [23556, 2], [23583, 259], [23843, 3], [23850, 2], [23856, 2], [23883, 257], [24143, 3], [24150, 2], [24156, 3], [24182, 257], [24443, 3], [24450, 2], [24456, 3], [24481, 258], [24743, 3], [24750, 2], [24756, 3], [24781, 258], [25043, 3], [25050, 2], [25056, 3], [25080, 259], [25343, 3], [25350, 2], [25356, 3], [25379, 260], [25643, 3], [25650, 2], [25656, 3], [25662, 1], [25678, 261], [25943, 3], [25950, 2], [25956, 3], [25962, 4], [25975, 264], [26243, 3], [26250, 2], [26256, 3], [26262, 277], [26543, 3], [26550, 2], [26556, 3], [26562, 277], [26843, 3], [26850, 2], [26856, 3], [26862, 277], [27143, 3], [27150, 2], [27156, 3], [27162, 277], [27443, 3], [27450, 2], [27456, 3], [27462, 277], [27743, 3], [27750, 2], [27756, 3], [27762, 277], [28043, 3], [28050, 2], [28056, 3], [28062, 277], [28343, 3], [28350, 2], [28356, 3], [28362, 277], [28643, 3], [28650, 2], [28656, 3], [28662, 277], [28943, 3], [28950, 2], [28956, 3], [28962, 277], [29243, 3], [29250, 2], [29257, 2], [29262, 277], [29543, 3], [29550, 2], [29557, 2], [29563, 276], [29843, 3], [29850, 2], [29857, 2], [29863, 276], [30143, 3], [30150, 2], [30157, 2], [30163, 1774], [31963, 274], [32262, 275], [32562, 275], [32862, 275], [33162, 275], [33462, 276], [33763, 275], [34063, 275], [34363, 275], [34663, 136], [34800, 138], [34963, 135], [35100, 138], [35262, 135], [35400, 138], [35562, 135], [35700, 139], [35862, 134], [36000, 139], [36162, 133], [36300, 139], [36461, 133], [36600, 139], [36761, 132], [36900, 139], [37061, 132], [37200, 139], [37361, 131], [37500, 139], [37661, 130], [37800, 139], [37961, 129], [38100, 140], [38261, 128], [38400, 140], [38560, 128], [38700, 140], [38860, 128], [39000, 142], [39159, 128], [39300, 144], [39456, 130], [39600, 285], [39900, 284], [40200, 284], [40500, 283], [40800, 282], [41100, 281], [41400, 280], [41700, 280], [42000, 279], [42300, 278], [42600, 277], [42900, 276], [43200, 276], [43500, 275], [43800, 274], [44100, 273], [44400, 272], [44700, 272], [45000, 271], [45300, 270], [45600, 269], [45900, 268], [46200, 267], [46500, 267], [46800, 266], [47100, 265], [47400, 264], [47700, 263], [48000, 263], [48300, 262], [48600, 261], [48900, 260], [49200, 259], [49500, 259], [49800, 258], [50100, 257], [50400, 256], [50700, 255], [51000, 255], [51300, 254], [51600, 253], [51900, 252], [52200, 251], [52500, 251], [52800, 250], [53100, 249], [53400, 248], [53700, 247], [54000, 246], [54300, 246], [54600, 245], [54900, 244], [55200, 243], [55500, 242], [55800, 242], [56100, 241], [56400, 240], [56700, 239], [57000, 239], [57300, 239], [57600, 239], [57900, 239], [58200, 239], [58500, 240], [58800, 240], [59100, 240], [59400, 145], [59554, 86], [59700, 144], [59855, 85], [60000, 142], [60156, 84], [60300, 141], [60458, 83], [60600, 139], [60760, 81], [60900, 138], [61061, 80], [61200, 137], [61362, 79], [61500, 136], [61664, 77], [61800, 136], [61965, 77], [62100, 135], [62266, 75], [62400, 134], [62567, 74], [62700, 89], [62790, 43], [62868, 72], [63000, 89], [63091, 41], [63169, 70], [63300, 89], [63391, 40], [63470, 68], [63600, 88], [63693, 37], [63771, 65], [63900, 88], [63996, 33], [64072, 60], [64200, 88], [64300, 28], [64373, 56], [64500, 88], [64603, 24], [64673, 53], [64800, 88], [64907, 20], [64974, 48], [65100, 88], [65212, 14], [65275, 42], [65400, 88], [65518, 7], [65576, 35], [65700, 88], [65877, 28], [66000, 87], [66178, 19], [66300, 87], [66478, 10], [66600, 87], [66900, 87], [67200, 87], [67500, 87], [67800, 87], [68100, 87], [68400, 86], [68700, 86], [69000, 86], [69300, 86], [69600, 86], [69900, 86], [70200, 86], [70500, 86], [70800, 85], [71100, 85], [71400, 85], [71700, 85], [72000, 85], [72300, 85], [72600, 85], [72900, 85], [73200, 84], [73500, 84], [73800, 84], [74100, 84], [74400, 84], [74700, 84], [75000, 84], [75300, 84], [75600, 83], [75900, 83], [76200, 83], [76500, 83], [76800, 83], [77100, 83], [77400, 83], [77700, 82], [78000, 82], [78300, 82], [78600, 82], [78900, 82], [79200, 82], [79500, 82], [79800, 82], [80100, 81], [80400, 81], [80700, 81], [81000, 81], [81300, 81], [81600, 81], [81900, 81], [82200, 81], [82500, 80], [82800, 80], [83100, 80], [83400, 80], [83700, 80], [84000, 80], [84300, 80], [84600, 80], [84900, 79], [85200, 79], [85500, 79], [85800, 79], [86100, 79], [86400, 79], [86700, 79], [87000, 79], [87300, 78], [87600, 78], [87900, 78], [88200, 78], [88500, 78], [88800, 78], [89100, 78], [89400, 78], [89700, 77]], "point": [149, 149]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 16013], [16047, 266], [16347, 266], [16646, 267], [16946, 267], [17245, 268], [17545, 269], [17844, 270], [18144, 270], [18443, 30], [18474, 240], [18743, 27], [18777, 237], [19043, 21], [19066, 2], [19078, 237], [19342, 20], [19379, 236], [19642, 19], [19680, 235], [19942, 18], [19982, 234], [20241, 18], [20283, 233], [20541, 18], [20583, 234], [20840, 18], [20884, 234], [21139, 19], [21184, 234], [21439, 19], [21484, 235], [21738, 20], [21784, 236], [22037, 21], [22084, 237], [22337, 21], [22384, 237], [22636, 22], [22684, 238], [22935, 13], [22950, 8], [22984, 239], [23235, 11], [23250, 2], [23255, 3], [23283, 240], [23534, 12], [23550, 2], [23556, 2], [23583, 241], [23834, 8], [23843, 3], [23850, 2], [23856, 2], [23883, 242], [24133, 7], [24143, 3], [24150, 2], [24156, 3], [24182, 245], [24432, 7], [24443, 3], [24450, 2], [24456, 3], [24481, 258], [24743, 3], [24750, 2], [24756, 3], [24781, 258], [25043, 3], [25050, 2], [25056, 3], [25080, 259], [25343, 3], [25350, 2], [25356, 3], [25379, 260], [25643, 3], [25650, 2], [25656, 3], [25662, 1], [25678, 261], [25943, 3], [25950, 2], [25956, 3], [25962, 4], [25975, 264], [26243, 3], [26250, 2], [26256, 3], [26262, 277], [26543, 3], [26550, 2], [26556, 3], [26562, 277], [26843, 3], [26850, 2], [26856, 3], [26862, 277], [27143, 3], [27150, 2], [27156, 3], [27162, 277], [27443, 3], [27450, 2], [27456, 3], [27462, 277], [27743, 3], [27750, 2], [27756, 3], [27762, 277], [28043, 3], [28050, 2], [28056, 3], [28062, 277], [28343, 3], [28350, 2], [28356, 3], [28362, 277], [28643, 3], [28650, 2], [28656, 3], [28662, 277], [28943, 3], [28950, 2], [28956, 3], [28962, 277], [29243, 3], [29250, 2], [29257, 2], [29262, 277], [29543, 3], [29550, 2], [29557, 2], [29563, 276], [29843, 3], [29850, 2], [29857, 2], [29863, 276], [30143, 3], [30150, 2], [30157, 2], [30163, 1774], [31963, 274], [32262, 275], [32562, 275], [32862, 275], [33162, 275], [33462, 276], [33763, 275], [34063, 275], [34363, 275], [34663, 136], [34800, 138], [34963, 135], [35100, 138], [35262, 135], [35400, 138], [35562, 135], [35700, 139], [35862, 134], [36000, 139], [36162, 133], [36300, 139], [36461, 133], [36600, 139], [36761, 132], [36900, 139], [37061, 132], [37200, 139], [37361, 131], [37500, 139], [37661, 130], [37800, 139], [37961, 129], [38100, 140], [38261, 128], [38400, 140], [38560, 128], [38700, 140], [38860, 128], [39000, 142], [39159, 128], [39300, 144], [39456, 130], [39600, 285], [39900, 284], [40200, 284], [40500, 283], [40800, 282], [41100, 281], [41400, 280], [41700, 280], [42000, 279], [42300, 278], [42600, 277], [42900, 276], [43200, 276], [43500, 275], [43800, 274], [44100, 273], [44400, 272], [44700, 272], [45000, 271], [45300, 270], [45600, 269], [45900, 268], [46200, 267], [46500, 267], [46800, 266], [47100, 265], [47400, 264], [47700, 263], [48000, 263], [48300, 262], [48600, 261], [48900, 260], [49200, 259], [49500, 259], [49800, 258], [50100, 257], [50400, 256], [50700, 255], [51000, 255], [51300, 254], [51600, 253], [51900, 252], [52200, 251], [52500, 251], [52800, 250], [53100, 249], [53400, 248], [53700, 247], [54000, 246], [54300, 246], [54600, 245], [54900, 244], [55200, 243], [55500, 242], [55800, 242], [56100, 241], [56400, 240], [56700, 239], [57000, 239], [57300, 239], [57600, 239], [57900, 239], [58200, 239], [58500, 240], [58800, 240], [59100, 240], [59400, 240], [59700, 240], [60000, 240], [60300, 241], [60600, 241], [60900, 241], [61200, 241], [61500, 241], [61800, 242], [62100, 241], [62400, 241], [62700, 89], [62790, 150], [63000, 89], [63091, 148], [63300, 89], [63391, 147], [63600, 88], [63693, 143], [63900, 88], [63996, 136], [64200, 88], [64300, 129], [64500, 88], [64603, 123], [64800, 88], [64907, 115], [65100, 88], [65212, 105], [65400, 88], [65518, 93], [65700, 88], [65824, 81], [66000, 87], [66132, 65], [66300, 87], [66442, 46], [66600, 87], [66900, 87], [67200, 87], [67500, 87], [67800, 87], [68100, 87], [68400, 86], [68700, 86], [69000, 86], [69300, 86], [69600, 86], [69900, 86], [70200, 86], [70500, 86], [70800, 85], [71100, 85], [71400, 85], [71700, 85], [72000, 85], [72300, 85], [72600, 85], [72900, 85], [73200, 84], [73500, 84], [73800, 84], [74100, 84], [74400, 84], [74700, 84], [75000, 84], [75300, 84], [75600, 83], [75900, 83], [76200, 83], [76500, 83], [76800, 83], [77100, 83], [77400, 83], [77700, 82], [78000, 82], [78300, 82], [78600, 82], [78900, 82], [79200, 82], [79500, 82], [79800, 82], [80100, 81], [80400, 81], [80700, 81], [81000, 81], [81300, 81], [81600, 81], [81900, 81], [82200, 81], [82500, 80], [82800, 80], [83100, 80], [83400, 80], [83700, 80], [84000, 80], [84300, 80], [84600, 80], [84900, 79], [85200, 79], [85500, 79], [85800, 79], [86100, 79], [86400, 79], [86700, 79], [87000, 79], [87300, 78], [87600, 78], [87900, 78], [88200, 78], [88500, 78], [88800, 78], [89100, 78], [89400, 78], [89700, 77]], "point": [149, 149]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Lettuce|-01.29|+00.99|-02.48"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [142, 96, 179, 151], "mask": [[28657, 6], [28955, 12], [29253, 16], [29551, 19], [29850, 22], [30148, 26], [30447, 28], [30746, 29], [31046, 30], [31345, 32], [31645, 32], [31944, 34], [32244, 34], [32543, 35], [32843, 36], [33143, 36], [33443, 36], [33743, 36], [34043, 36], [34343, 36], [34643, 36], [34943, 36], [35243, 37], [35543, 36], [35842, 37], [36142, 37], [36442, 37], [36742, 37], [37042, 37], [37342, 37], [37642, 36], [37942, 36], [38243, 34], [38543, 34], [38843, 34], [39143, 33], [39444, 32], [39744, 32], [40044, 32], [40345, 30], [40645, 30], [40946, 28], [41247, 27], [41548, 25], [41848, 25], [42149, 23], [42450, 21], [42750, 21], [43051, 19], [43352, 18], [43653, 17], [43955, 14], [44256, 13], [44556, 12], [44857, 10], [45159, 7]], "point": [160, 122]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 222], "mask": [[0, 17100], [17101, 299], [17402, 298], [17707, 293], [18008, 292], [18308, 292], [18609, 291], [18909, 291], [19210, 290], [19511, 289], [19811, 289], [20112, 288], [20412, 288], [20713, 287], [21014, 286], [21314, 286], [21615, 285], [21915, 285], [22216, 284], [22517, 283], [22817, 283], [23118, 282], [23418, 282], [23719, 281], [24020, 280], [24320, 280], [24621, 279], [24921, 279], [25222, 278], [25523, 277], [25823, 277], [26124, 276], [26424, 276], [26725, 275], [27025, 275], [27326, 274], [27627, 273], [27927, 273], [28228, 272], [28528, 272], [28829, 271], [29130, 270], [29430, 270], [29731, 269], [30031, 269], [30332, 268], [30633, 267], [30933, 267], [31234, 266], [31534, 266], [31835, 265], [32136, 264], [32436, 264], [32737, 263], [33037, 263], [33338, 262], [33639, 261], [33939, 261], [34240, 260], [34540, 260], [34841, 259], [35142, 258], [35442, 258], [35743, 257], [36043, 257], [36344, 256], [36645, 255], [36945, 255], [37246, 254], [37546, 254], [37847, 253], [38147, 253], [38448, 252], [38749, 251], [39049, 251], [39350, 250], [39650, 250], [39951, 249], [40252, 248], [40552, 248], [40853, 247], [41153, 247], [41454, 246], [41755, 245], [42055, 245], [42356, 244], [42656, 244], [42957, 243], [43258, 242], [43558, 242], [43859, 241], [44159, 241], [44460, 239], [44761, 237], [45061, 236], [45362, 234], [45662, 233], [45963, 231], [46264, 229], [46564, 228], [46865, 226], [47165, 225], [47466, 223], [47767, 221], [48067, 220], [48368, 218], [48668, 217], [48969, 215], [49270, 213], [49570, 212], [49871, 210], [50171, 209], [50472, 207], [50772, 206], [51073, 204], [51374, 202], [51674, 201], [51975, 199], [52275, 198], [52576, 196], [52877, 194], [53177, 193], [53478, 191], [53778, 190], [54079, 189], [54380, 187], [54680, 186], [54981, 184], [55281, 183], [55582, 181], [55883, 179], [56183, 178], [56484, 61], [56554, 106], [56784, 59], [56856, 103], [57085, 56], [57158, 100], [57386, 54], [57459, 98], [57686, 53], [57760, 96], [57987, 51], [58061, 94], [58287, 50], [58362, 92], [58588, 49], [58662, 91], [58889, 47], [58963, 89], [59189, 46], [59264, 87], [59489, 46], [59565, 85], [59789, 45], [59865, 84], [60089, 44], [60166, 82], [60389, 44], [60467, 80], [60689, 43], [60767, 79], [60989, 43], [61068, 77], [61289, 42], [61369, 75], [61588, 42], [61670, 73], [61888, 42], [61970, 72], [62188, 41], [62271, 70], [62489, 39], [62572, 69], [62790, 38], [62872, 68], [63091, 36], [63173, 66], [63391, 35], [63474, 64], [63693, 33], [63774, 62], [63996, 29], [64075, 57], [64300, 25], [64375, 54], [64603, 21], [64676, 50], [64907, 16], [64977, 45], [65212, 11], [65277, 40], [65518, 4], [65578, 33], [65878, 27], [66179, 18], [66479, 9]], "point": [149, 110]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Lettuce|-01.29|+00.99|-02.48", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 16013], [16047, 266], [16347, 266], [16646, 267], [16946, 267], [17245, 268], [17545, 269], [17844, 270], [18144, 270], [18443, 30], [18474, 240], [18743, 27], [18777, 237], [19043, 21], [19066, 2], [19078, 237], [19342, 20], [19379, 236], [19642, 19], [19680, 235], [19942, 18], [19982, 234], [20241, 18], [20283, 233], [20541, 18], [20583, 234], [20840, 18], [20884, 234], [21139, 19], [21184, 234], [21439, 19], [21484, 235], [21738, 20], [21784, 236], [22037, 21], [22084, 237], [22337, 21], [22384, 237], [22636, 22], [22684, 238], [22935, 13], [22950, 8], [22984, 239], [23235, 11], [23250, 2], [23255, 3], [23283, 240], [23534, 12], [23550, 2], [23556, 2], [23583, 241], [23834, 8], [23843, 3], [23850, 2], [23856, 2], [23883, 242], [24133, 7], [24143, 3], [24150, 2], [24156, 3], [24182, 245], [24432, 7], [24443, 3], [24450, 2], [24456, 3], [24481, 258], [24743, 3], [24750, 2], [24756, 3], [24781, 258], [25043, 3], [25050, 2], [25056, 3], [25080, 259], [25343, 3], [25350, 2], [25356, 3], [25379, 260], [25643, 3], [25650, 2], [25656, 3], [25662, 1], [25678, 261], [25943, 3], [25950, 2], [25956, 3], [25962, 4], [25975, 264], [26243, 3], [26250, 2], [26256, 3], [26262, 277], [26543, 3], [26550, 2], [26556, 3], [26562, 277], [26843, 3], [26850, 2], [26856, 3], [26862, 277], [27143, 3], [27150, 2], [27156, 3], [27162, 277], [27443, 3], [27450, 2], [27456, 3], [27462, 277], [27743, 3], [27750, 2], [27756, 3], [27762, 277], [28043, 3], [28050, 2], [28056, 3], [28062, 277], [28343, 3], [28350, 2], [28356, 3], [28362, 277], [28643, 3], [28650, 2], [28656, 3], [28662, 277], [28943, 3], [28950, 2], [28956, 3], [28962, 277], [29243, 3], [29250, 2], [29257, 2], [29262, 277], [29543, 3], [29550, 2], [29557, 2], [29563, 276], [29843, 3], [29850, 2], [29857, 2], [29863, 276], [30143, 3], [30150, 2], [30157, 2], [30163, 1774], [31963, 274], [32262, 275], [32562, 275], [32862, 275], [33162, 275], [33462, 276], [33763, 275], [34063, 275], [34363, 275], [34663, 136], [34800, 138], [34963, 135], [35100, 138], [35262, 135], [35400, 138], [35562, 135], [35700, 139], [35862, 134], [36000, 139], [36162, 133], [36300, 139], [36461, 133], [36600, 139], [36761, 132], [36900, 139], [37061, 132], [37200, 139], [37361, 131], [37500, 139], [37661, 130], [37800, 139], [37961, 129], [38100, 140], [38261, 128], [38400, 140], [38560, 128], [38700, 140], [38860, 128], [39000, 142], [39159, 128], [39300, 144], [39456, 130], [39600, 285], [39900, 284], [40200, 284], [40500, 283], [40800, 282], [41100, 281], [41400, 280], [41700, 280], [42000, 279], [42300, 278], [42600, 277], [42900, 276], [43200, 276], [43500, 275], [43800, 274], [44100, 273], [44400, 272], [44700, 272], [45000, 271], [45300, 270], [45600, 269], [45900, 268], [46200, 267], [46500, 267], [46800, 266], [47100, 265], [47400, 264], [47700, 263], [48000, 263], [48300, 262], [48600, 261], [48900, 260], [49200, 259], [49500, 259], [49800, 258], [50100, 257], [50400, 256], [50700, 255], [51000, 255], [51300, 254], [51600, 253], [51900, 252], [52200, 251], [52500, 251], [52800, 250], [53100, 249], [53400, 248], [53700, 247], [54000, 246], [54300, 246], [54600, 245], [54900, 244], [55200, 243], [55500, 242], [55800, 242], [56100, 241], [56400, 145], [56554, 86], [56700, 143], [56856, 83], [57000, 141], [57158, 81], [57300, 140], [57459, 80], [57600, 139], [57760, 79], [57900, 138], [58061, 78], [58200, 137], [58362, 77], [58500, 137], [58662, 78], [58800, 136], [58963, 77], [59100, 135], [59264, 76], [59400, 135], [59565, 75], [59700, 134], [59865, 75], [60000, 133], [60166, 74], [60300, 133], [60467, 74], [60600, 132], [60767, 74], [60900, 132], [61068, 73], [61200, 131], [61369, 72], [61500, 130], [61670, 71], [61800, 130], [61970, 72], [62100, 129], [62271, 70], [62400, 128], [62572, 69], [62700, 89], [62790, 38], [62872, 68], [63000, 89], [63091, 36], [63173, 66], [63300, 89], [63391, 35], [63474, 64], [63600, 88], [63693, 33], [63774, 62], [63900, 88], [63996, 29], [64075, 57], [64200, 88], [64300, 25], [64375, 54], [64500, 88], [64603, 21], [64676, 50], [64800, 88], [64907, 16], [64977, 45], [65100, 88], [65212, 11], [65277, 40], [65400, 88], [65518, 4], [65578, 33], [65700, 88], [65878, 27], [66000, 87], [66179, 18], [66300, 87], [66479, 9], [66600, 87], [66900, 87], [67200, 87], [67500, 87], [67800, 87], [68100, 87], [68400, 86], [68700, 86], [69000, 86], [69300, 86], [69600, 86], [69900, 86], [70200, 86], [70500, 86], [70800, 85], [71100, 85], [71400, 85], [71700, 85], [72000, 85], [72300, 85], [72600, 85], [72900, 85], [73200, 84], [73500, 84], [73800, 84], [74100, 84], [74400, 84], [74700, 84], [75000, 84], [75300, 84], [75600, 83], [75900, 83], [76200, 83], [76500, 83], [76800, 83], [77100, 83], [77400, 83], [77700, 82], [78000, 82], [78300, 82], [78600, 82], [78900, 82], [79200, 82], [79500, 82], [79800, 82], [80100, 81], [80400, 81], [80700, 81], [81000, 81], [81300, 81], [81600, 81], [81900, 81], [82200, 81], [82500, 80], [82800, 80], [83100, 80], [83400, 80], [83700, 80], [84000, 80], [84300, 80], [84600, 80], [84900, 79], [85200, 79], [85500, 79], [85800, 79], [86100, 79], [86400, 79], [86700, 79], [87000, 79], [87300, 78], [87600, 78], [87900, 78], [88200, 78], [88500, 78], [88800, 78], [89100, 78], [89400, 78], [89700, 77]], "point": [149, 149]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 16013], [16047, 46], [16130, 183], [16347, 47], [16430, 183], [16646, 48], [16730, 183], [16946, 48], [17030, 183], [17245, 49], [17329, 184], [17545, 50], [17629, 185], [17844, 51], [17929, 185], [18144, 51], [18229, 185], [18443, 30], [18474, 22], [18529, 185], [18743, 27], [18777, 19], [18829, 185], [19043, 21], [19066, 2], [19078, 19], [19129, 186], [19342, 20], [19379, 18], [19428, 187], [19642, 19], [19680, 18], [19728, 187], [19942, 18], [19982, 17], [20028, 188], [20241, 18], [20283, 16], [20327, 189], [20541, 18], [20583, 17], [20627, 190], [20840, 18], [20884, 16], [20926, 192], [21139, 19], [21184, 17], [21226, 192], [21439, 19], [21484, 18], [21526, 193], [21738, 20], [21784, 18], [21825, 195], [22037, 21], [22084, 19], [22125, 196], [22337, 21], [22384, 21], [22425, 196], [22636, 22], [22684, 22], [22724, 198], [22935, 13], [22950, 8], [22984, 23], [23024, 199], [23235, 11], [23250, 2], [23255, 3], [23283, 26], [23323, 200], [23534, 12], [23550, 2], [23556, 2], [23583, 27], [23623, 201], [23834, 8], [23843, 3], [23850, 2], [23856, 2], [23883, 28], [23923, 202], [24133, 7], [24143, 3], [24150, 2], [24156, 3], [24182, 31], [24221, 206], [24432, 7], [24443, 3], [24450, 2], [24456, 3], [24481, 33], [24519, 220], [24743, 3], [24750, 2], [24756, 3], [24781, 258], [25043, 3], [25050, 2], [25056, 3], [25080, 259], [25343, 3], [25350, 2], [25356, 3], [25379, 260], [25643, 3], [25650, 2], [25656, 3], [25662, 1], [25678, 261], [25943, 3], [25950, 2], [25956, 3], [25962, 4], [25975, 264], [26243, 3], [26250, 2], [26256, 3], [26262, 277], [26543, 3], [26550, 2], [26556, 3], [26562, 277], [26843, 3], [26850, 2], [26856, 3], [26862, 277], [27143, 3], [27150, 2], [27156, 3], [27162, 277], [27443, 3], [27450, 2], [27456, 3], [27462, 277], [27743, 3], [27750, 2], [27756, 3], [27762, 277], [28043, 3], [28050, 2], [28056, 3], [28062, 277], [28343, 3], [28350, 2], [28356, 3], [28362, 277], [28643, 3], [28650, 2], [28656, 3], [28662, 277], [28943, 3], [28950, 2], [28956, 3], [28962, 277], [29243, 3], [29250, 2], [29257, 2], [29262, 277], [29543, 3], [29550, 2], [29557, 2], [29563, 276], [29843, 3], [29850, 2], [29857, 2], [29863, 276], [30143, 3], [30150, 2], [30157, 2], [30163, 1774], [31963, 274], [32262, 275], [32562, 275], [32862, 275], [33162, 275], [33462, 276], [33763, 275], [34063, 275], [34363, 275], [34663, 136], [34800, 138], [34963, 135], [35100, 138], [35262, 135], [35400, 138], [35562, 135], [35700, 139], [35862, 134], [36000, 139], [36162, 133], [36300, 139], [36461, 133], [36600, 139], [36761, 132], [36900, 139], [37061, 132], [37200, 139], [37361, 131], [37500, 139], [37661, 130], [37800, 139], [37961, 129], [38100, 140], [38261, 128], [38400, 140], [38560, 128], [38700, 140], [38860, 128], [39000, 142], [39159, 128], [39300, 144], [39456, 130], [39600, 285], [39900, 284], [40200, 284], [40500, 283], [40800, 282], [41100, 281], [41400, 280], [41700, 280], [42000, 279], [42300, 278], [42600, 277], [42900, 276], [43200, 276], [43500, 275], [43800, 274], [44100, 273], [44400, 272], [44700, 272], [45000, 271], [45300, 270], [45600, 269], [45900, 268], [46200, 267], [46500, 267], [46800, 266], [47100, 265], [47400, 264], [47700, 263], [48000, 263], [48300, 262], [48600, 261], [48900, 260], [49200, 259], [49500, 259], [49800, 258], [50100, 257], [50400, 256], [50700, 255], [51000, 255], [51300, 254], [51600, 253], [51900, 252], [52200, 251], [52500, 251], [52800, 250], [53100, 249], [53400, 248], [53700, 247], [54000, 246], [54300, 246], [54600, 245], [54900, 244], [55200, 243], [55500, 242], [55800, 242], [56100, 241], [56400, 240], [56700, 239], [57000, 239], [57300, 239], [57600, 239], [57900, 239], [58200, 239], [58500, 240], [58800, 240], [59100, 240], [59400, 240], [59700, 240], [60000, 240], [60300, 241], [60600, 241], [60900, 241], [61200, 241], [61500, 241], [61800, 242], [62100, 241], [62400, 241], [62700, 89], [62790, 150], [63000, 89], [63091, 148], [63300, 89], [63391, 147], [63600, 88], [63693, 143], [63900, 88], [63996, 136], [64200, 88], [64300, 129], [64500, 88], [64603, 123], [64800, 88], [64907, 115], [65100, 88], [65212, 105], [65400, 88], [65518, 93], [65700, 88], [65824, 81], [66000, 87], [66132, 65], [66300, 87], [66442, 46], [66600, 87], [66900, 87], [67200, 87], [67500, 87], [67800, 87], [68100, 87], [68400, 86], [68700, 86], [69000, 86], [69300, 86], [69600, 86], [69900, 86], [70200, 86], [70500, 86], [70800, 85], [71100, 85], [71400, 85], [71700, 85], [72000, 85], [72300, 85], [72600, 85], [72900, 85], [73200, 84], [73500, 84], [73800, 84], [74100, 84], [74400, 84], [74700, 84], [75000, 84], [75300, 84], [75600, 83], [75900, 83], [76200, 83], [76500, 83], [76800, 83], [77100, 83], [77400, 83], [77700, 82], [78000, 82], [78300, 82], [78600, 82], [78900, 82], [79200, 82], [79500, 82], [79800, 82], [80100, 81], [80400, 81], [80700, 81], [81000, 81], [81300, 81], [81600, 81], [81900, 81], [82200, 81], [82500, 80], [82800, 80], [83100, 80], [83400, 80], [83700, 80], [84000, 80], [84300, 80], [84600, 80], [84900, 79], [85200, 79], [85500, 79], [85800, 79], [86100, 79], [86400, 79], [86700, 79], [87000, 79], [87300, 78], [87600, 78], [87900, 78], [88200, 78], [88500, 78], [88800, 78], [89100, 78], [89400, 78], [89700, 77]], "point": [149, 149]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan1", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 1.5, "y": 0.9009995, "z": -0.75}, "object_poses": [{"objectName": "Statue_1a4cbefa", "position": {"x": -0.9956643, "y": 0.910197, "z": -2.406642}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_01a9d33b", "position": {"x": 1.685766, "y": 0.5500546, "z": -2.71822071}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "CreditCard_01a9d33b", "position": {"x": -2.01913023, "y": 0.908396065, "z": -0.6989651}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ba789e3c", "position": {"x": -1.76507258, "y": 0.126816392, "z": 0.158000976}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_30762d8a", "position": {"x": -0.328587532, "y": 1.82566321, "z": -2.61057}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_0fe5b61f", "position": {"x": 1.58455479, "y": 0.881811738, "z": -2.53643084}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "SoapBottle_0fe5b61f", "position": {"x": 0.0790954158, "y": 1.11258078, "z": -0.253773928}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b714915a", "position": {"x": -0.473685682, "y": 1.10790622, "z": 0.251773834}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b714915a", "position": {"x": -0.473685682, "y": 1.10790622, "z": 0.7573217}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_816f44ce", "position": {"x": -1.905086, "y": 0.132606149, "z": -1.38995492}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PepperShaker_816f44ce", "position": {"x": 0.2172907, "y": 1.10790622, "z": 0.251773834}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_35d530e8", "position": {"x": -2.080045, "y": 0.612978041, "z": 1.00317287}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_e166a32f", "position": {"x": -1.290839, "y": 0.991022468, "z": -2.48345661}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e166a32f", "position": {"x": -0.197295129, "y": 1.19122255, "z": 0.251773834}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a0b666be", "position": {"x": -1.96337986, "y": 0.8203338, "z": -1.24219179}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_c9d70d0f", "position": {"x": -1.85282016, "y": 0.795105755, "z": -1.67392874}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_b5db8038", "position": {"x": 0.2172907, "y": 1.1066, "z": -0.253773928}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_b5db8038", "position": {"x": -1.7313, "y": 0.956599951, "z": -0.1689994}, "rotation": {"x": 0.0, "y": 89.99987, "z": 0.0}}, {"objectName": "Spatula_1e920a81", "position": {"x": -0.3354904, "y": 1.12659991, "z": 0.5045478}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "ButterKnife_f63cc56c", "position": {"x": 0.0790954158, "y": 1.11164212, "z": -0.00100004673}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f63cc56c", "position": {"x": 1.00827694, "y": 0.481583, "z": -2.38039064}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_7ed44c70", "position": {"x": 0.56084305, "y": 1.66104138, "z": -2.5966444}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_c5dfb511", "position": {"x": -0.84807694, "y": 0.912116647, "z": -2.63708615}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_c5dfb511", "position": {"x": -1.79754007, "y": 0.7704729, "z": -1.24219179}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "WineBottle_c21bc5e7", "position": {"x": 0.0790954158, "y": 1.1125288, "z": -0.5065479}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_56e11318", "position": {"x": -1.96337986, "y": 0.769320369, "z": -1.41488659}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_56e11318", "position": {"x": -1.935, "y": 0.0475771464, "z": 2.12468362}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Book_f5814e4b", "position": {"x": 0.155, "y": 1.1, "z": 0.617}, "rotation": {"x": 0.0, "y": 315.826447, "z": 0.0}}, {"objectName": "Glassbottle_c5dfb511", "position": {"x": -1.79754019, "y": 0.7704729, "z": -1.58758128}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_c9d70d0f", "position": {"x": 0.969297051, "y": 0.93604964, "z": -2.33036733}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_35d530e8", "position": {"x": 0.0790954158, "y": 1.18406522, "z": 0.251773834}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_89238ee6", "position": {"x": 1.09816635, "y": 0.911248565, "z": -2.637633}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_dc7b7f7e", "position": {"x": -1.99939144, "y": 1.53346038, "z": 1.34379089}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_0fe5b61f", "position": {"x": -0.473685682, "y": 1.11258078, "z": -0.00100004673}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_7ed44c70", "position": {"x": -0.4652, "y": 0.950499952, "z": -2.576}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}, {"objectName": "Pan_e8d2711b", "position": {"x": 0.7211994, "y": 0.9, "z": -2.41542959}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ba789e3c", "position": {"x": -0.059099853, "y": 1.10851872, "z": -0.7593218}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_811f3b02", "position": {"x": -2.03700638, "y": 0.837581336, "z": 1.08588624}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Vase_d1ae33eb", "position": {"x": 2.042472, "y": 0.5407073, "z": -2.49500537}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_30762d8a", "position": {"x": -1.79754007, "y": 0.8049194, "z": -1.32853913}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "CreditCard_01a9d33b", "position": {"x": -0.197295129, "y": 1.10859609, "z": 0.7573217}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Statue_1a4cbefa", "position": {"x": -0.3354904, "y": 1.1103971, "z": -0.5065479}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "WineBottle_c21bc5e7", "position": {"x": -1.935, "y": 0.0489416756, "z": 1.98115826}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pot_9e34d9fc", "position": {"x": -0.0361, "y": 0.950499952, "z": -2.5762}, "rotation": {"x": 0.0, "y": 270.000061, "z": 0.0}}, {"objectName": "Spatula_1e920a81", "position": {"x": -1.586057, "y": 0.9263998, "z": -2.056193}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_2c38299b", "position": {"x": -1.733601, "y": 1.01568532, "z": -2.25301242}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_77fad61b", "position": {"x": 1.99396765, "y": 0.878713, "z": -2.61793756}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Vase_5ffb3206", "position": {"x": 0.355485976, "y": 1.109299, "z": 0.251773834}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_9960ee75", "position": {"x": -1.01363826, "y": 1.65292048, "z": -2.56874371}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b714915a", "position": {"x": 1.58206713, "y": 0.549364746, "z": -2.51112533}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "PepperShaker_816f44ce", "position": {"x": 1.47836733, "y": 0.5493647, "z": -2.4593513}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Lettuce_e166a32f", "position": {"x": -1.80697548, "y": 0.973761737, "z": -0.942994237}, "rotation": {"x": 359.9812, "y": 256.7714, "z": 0.0007696777}}, {"objectName": "ButterKnife_f63cc56c", "position": {"x": -1.69341934, "y": 0.911442041, "z": -2.366023}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a0b666be", "position": {"x": -0.3354904, "y": 1.16217768, "z": -0.253773928}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_56e11318", "position": {"x": 0.2172907, "y": 1.11116433, "z": -0.5065479}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_cd066502", "position": {"x": -1.74000025, "y": 0.15496856, "z": -0.1495683}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_b5db8038", "position": {"x": -1.96337986, "y": 0.764756143, "z": -1.50123394}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1835762096, "scene_num": 1}, "task_id": "trial_T20190918_213438_348886", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "AHBWX3WYMAB0E_3LOTDFNYAAQH9MULG27AUFDZFZOWFN", "high_descs": ["Turn around and move to the other side of the island by the fridge.", "Pick up the head of lettuce from the island.", "Turn around and move to the fridge.", "Put the head of lettuce on the top shelf of the fridge to the left of the tomato.", "Turn left and head to the counter across the room on the right of the stove.", "Pick up the head of lettuce from the counter.", "Turn around and head back to the fridge.", "Put the second head of lettuce on the top shelf of the fridge to the right of the tomato."], "task_desc": "Put two heads of lettuce on the top shelf in the fridge.", "votes": [1, 1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_3137ONMDKJWT6M4P6MJMQU6B98IEGJ", "high_descs": ["turn around and walk to the end of the room, then hang a right and walk over to the center of the kitchen island counter on your right", "grab the lettuce off of the counter there", "turn around and walk over to the fridge on your right", "place the lettuce inside of the fridge there", "turn left and walk over to the kitchen counter at the end of the room", "grab the lettuce off of the kitchen counter", "turn around and walk back over to the fridge on your left at the end of the room", "place the lettuce inside of the fridge there"], "task_desc": "place two lettuce plants inside of the kitchen fridge", "votes": [1, 0, 1]}, {"assignment_id": "A24RGLS3BRZ60J_3A4NIXBJ79Q742A3YB4LGUGSET8LM2", "high_descs": ["Turn around and move across the kitchen. Turn right and face the island on the right.", "Pick up the lettuce from the far side of the island.", "Carry the lettuce and turn around to the fridge.", "Open the fridge and place the lettuce inside. Shut the door.", "Turn to the left and locate the lettuce on the counter.", "Pick up the lettuce from the counter.", "Hold the lettuce and turn around to go to the fridge.", "Open the fridge and place the lettuce inside. Shut the door."], "task_desc": "Move two heads of lettuce to the fridge.", "votes": [0, 1, 1]}]}}