{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000302.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000303.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000304.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000305.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000306.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000307.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000308.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000309.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000310.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000311.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000312.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000313.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000314.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000315.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000316.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000317.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000318.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000319.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000320.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000321.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000322.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000323.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000324.png", "low_idx": 52}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-11|-12|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-14.405104, -14.405104, -13.04582976, -13.04582976, 3.04316616, 3.04316616]], "coordinateReceptacleObjectId": ["DiningTable", [-14.464, -14.464, -13.296, -13.296, 2.673336984, 2.673336984]], "forceVisible": true, "objectId": "Cup|-03.60|+00.76|-03.26"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-8|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-9|2|-15"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-14.405104, -14.405104, -13.04582976, -13.04582976, 3.04316616, 3.04316616]], "coordinateReceptacleObjectId": ["Cabinet", [-1.3861756, -1.3861756, -13.1733904, -13.1733904, 7.56638432, 7.56638432]], "forceVisible": true, "objectId": "Cup|-03.60|+00.76|-03.26", "receptacleObjectId": "Cabinet|-00.35|+01.89|-03.29"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-03.60|+00.76|-03.26"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [74, 116, 105, 151], "mask": [[34585, 8], [34882, 13], [35180, 17], [35479, 19], [35778, 21], [36077, 23], [36377, 24], [36676, 25], [36976, 26], [37276, 27], [37575, 28], [37875, 29], [38175, 29], [38474, 30], [38774, 30], [39074, 31], [39374, 31], [39674, 31], [39974, 31], [40275, 30], [40575, 31], [40875, 31], [41175, 31], [41475, 31], [41775, 30], [42076, 29], [42376, 29], [42676, 28], [42977, 27], [43278, 25], [43579, 24], [43880, 22], [44180, 20], [44481, 18], [44784, 13], [45087, 6]], "point": [89, 132]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-03.60|+00.76|-03.26", "placeStationary": true, "receptacleObjectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 32743], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [149, 62]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 7937], [7955, 278], [8260, 271], [8562, 268], [8863, 267], [9163, 266], [9464, 265], [9764, 264], [10065, 263], [10365, 263], [10666, 261], [10966, 261], [11267, 259], [11567, 259], [11867, 258], [12168, 257], [12468, 257], [12768, 257], [13069, 255], [13369, 255], [13669, 255], [13970, 254], [14270, 253], [14570, 253], [14870, 253], [15170, 253], [15470, 253], [15770, 253], [16070, 253], [16370, 253], [16670, 253], [16970, 253], [17270, 253], [17570, 253], [17870, 253], [18170, 253], [18470, 253], [18770, 253], [19070, 253], [19370, 253], [19670, 253], [19970, 254], [20270, 254], [20569, 255], [20869, 256], [21169, 256], [21468, 258], [21768, 258], [22067, 259], [22367, 260], [22666, 262], [22965, 264], [23264, 266], [23563, 271], [23860, 8883], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [149, 80]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-03.60|+00.76|-03.26"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [123, 27, 169, 80], "mask": [[7937, 18], [8233, 27], [8531, 31], [8830, 33], [9130, 33], [9429, 35], [9729, 35], [10028, 37], [10328, 37], [10628, 38], [10927, 39], [11227, 40], [11526, 41], [11826, 41], [12125, 43], [12425, 43], [12725, 43], [13025, 44], [13324, 45], [13624, 45], [13924, 46], [14224, 46], [14523, 47], [14823, 47], [15123, 47], [15423, 47], [15723, 47], [16023, 47], [16323, 47], [16623, 47], [16923, 47], [17223, 47], [17523, 47], [17823, 47], [18123, 47], [18423, 47], [18723, 47], [19023, 47], [19323, 47], [19623, 47], [19923, 47], [20224, 46], [20524, 45], [20824, 45], [21125, 44], [21425, 43], [21726, 42], [22026, 41], [22326, 41], [22627, 39], [22928, 37], [23229, 35], [23530, 33], [23834, 26]], "point": [146, 52]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 32743], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [149, 62]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-00.35|+01.89|-03.29"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 59, 99, 242], "mask": [[17408, 3], [17707, 6], [18007, 9], [18307, 11], [18607, 14], [18907, 16], [19207, 19], [19506, 22], [19806, 25], [20106, 27], [20406, 30], [20706, 32], [21006, 35], [21305, 39], [21605, 41], [21905, 44], [22205, 46], [22505, 49], [22805, 51], [23104, 55], [23404, 57], [23704, 60], [24004, 62], [24304, 65], [24604, 67], [24903, 71], [25203, 73], [25503, 76], [25803, 79], [26103, 81], [26403, 84], [26702, 87], [27002, 90], [27302, 92], [27602, 95], [27902, 97], [28202, 98], [28501, 99], [28801, 99], [29101, 99], [29401, 99], [29701, 99], [30001, 99], [30300, 100], [30600, 100], [30900, 100], [31200, 100], [31500, 100], [31800, 100], [32100, 100], [32400, 100], [32700, 100], [33000, 100], [33300, 100], [33600, 99], [33900, 99], [34200, 99], [34500, 99], [34800, 99], [35100, 99], [35400, 99], [35700, 99], [36000, 99], [36300, 99], [36600, 99], [36900, 99], [37200, 99], [37500, 99], [37800, 99], [38100, 99], [38400, 99], [38700, 99], [39000, 99], [39300, 98], [39600, 98], [39900, 98], [40200, 98], [40500, 98], [40800, 98], [41100, 98], [41400, 98], [41700, 98], [42000, 98], [42300, 98], [42600, 98], [42900, 98], [43200, 98], [43500, 98], [43800, 98], [44100, 98], [44400, 98], [44700, 97], [45000, 97], [45300, 97], [45600, 97], [45900, 97], [46200, 97], [46500, 97], [46800, 97], [47100, 97], [47400, 97], [47700, 97], [48000, 97], [48300, 97], [48600, 97], [48900, 97], [49200, 97], [49500, 97], [49800, 97], [50100, 96], [50400, 96], [50700, 96], [51000, 96], [51300, 96], [51600, 96], [51900, 96], [52200, 96], [52500, 96], [52800, 96], [53100, 96], [53400, 96], [53700, 96], [54000, 96], [54300, 96], [54600, 96], [54900, 96], [55200, 96], [55500, 96], [55800, 95], [56100, 95], [56400, 95], [56700, 95], [57000, 95], [57300, 95], [57600, 95], [57900, 95], [58200, 95], [58500, 95], [58800, 95], [59100, 95], [59400, 95], [59700, 95], [60000, 95], [60300, 95], [60600, 95], [60900, 95], [61200, 94], [61500, 94], [61800, 94], [62100, 94], [62400, 94], [62700, 94], [63000, 94], [63300, 94], [63600, 94], [63900, 94], [64200, 94], [64500, 94], [64800, 94], [65100, 94], [65400, 94], [65700, 94], [66000, 94], [66300, 94], [66600, 94], [66900, 93], [67200, 93], [67500, 93], [67800, 93], [68100, 93], [68400, 93], [68700, 93], [69000, 93], [69300, 93], [69600, 93], [69900, 93], [70200, 93], [70500, 93], [70800, 93], [71100, 88], [71400, 69], [71700, 50], [72000, 32], [72300, 13]], "point": [49, 149]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-03.60|+00.76|-03.26", "placeStationary": true, "receptacleObjectId": "Cabinet|-00.35|+01.89|-03.29"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 98, 256], "mask": [[50, 10], [350, 10], [649, 11], [948, 12], [1247, 13], [1547, 13], [1846, 14], [2145, 14], [2444, 15], [2743, 16], [3043, 16], [3342, 17], [3641, 18], [3940, 19], [4240, 19], [4539, 20], [4838, 20], [5137, 21], [5436, 22], [5736, 22], [6035, 23], [6334, 24], [6633, 25], [6933, 25], [7232, 26], [7531, 26], [7830, 27], [8130, 27], [8429, 28], [8728, 29], [9027, 30], [9326, 31], [9626, 31], [9925, 32], [10224, 32], [10523, 33], [10823, 33], [11122, 34], [11421, 35], [11720, 36], [12019, 37], [12319, 37], [12618, 38], [12917, 38], [13216, 39], [13516, 39], [13815, 40], [14114, 41], [14413, 42], [14713, 42], [15012, 43], [15311, 44], [15610, 44], [15909, 45], [16209, 45], [16508, 46], [16807, 47], [17106, 48], [17406, 48], [17706, 48], [18006, 48], [18306, 47], [18605, 48], [18905, 48], [19205, 48], [19505, 48], [19805, 48], [20105, 48], [20405, 48], [20704, 49], [21004, 49], [21304, 48], [21604, 48], [21904, 48], [22204, 48], [22503, 49], [22803, 49], [23103, 51], [23403, 54], [23703, 56], [24003, 59], [24302, 63], [24602, 65], [24902, 68], [25202, 70], [25502, 73], [25802, 75], [26101, 79], [26401, 82], [26701, 84], [27001, 87], [27301, 89], [27601, 92], [27900, 95], [28200, 98], [28500, 99], [28800, 99], [29100, 99], [29400, 99], [29700, 99], [30000, 99], [30300, 99], [30600, 49], [30652, 47], [30900, 49], [30955, 43], [31200, 49], [31258, 40], [31500, 49], [31562, 36], [31800, 52], [31865, 33], [32100, 56], [32168, 30], [32400, 59], [32471, 27], [32700, 62], [32774, 24], [33000, 65], [33077, 21], [33300, 69], [33380, 18], [33600, 72], [33683, 15], [33900, 75], [33987, 11], [34200, 78], [34290, 8], [34500, 82], [34593, 5], [34800, 85], [34896, 2], [35100, 88], [35400, 91], [35700, 94], [36000, 98], [36300, 97], [36600, 97], [36900, 97], [37200, 97], [37500, 97], [37800, 97], [38100, 97], [38400, 97], [38700, 97], [39000, 97], [39300, 97], [39600, 97], [39900, 97], [40200, 97], [40500, 97], [40800, 97], [41100, 97], [41400, 97], [41700, 96], [42000, 96], [42300, 96], [42600, 96], [42900, 96], [43200, 96], [43500, 96], [43800, 96], [44100, 96], [44400, 96], [44700, 96], [45000, 96], [45300, 96], [45600, 96], [45900, 96], [46200, 96], [46500, 96], [46800, 96], [47100, 95], [47400, 95], [47700, 95], [48000, 95], [48300, 95], [48600, 95], [48900, 95], [49200, 95], [49500, 95], [49800, 95], [50100, 95], [50400, 95], [50700, 95], [51000, 95], [51300, 95], [51600, 95], [51900, 95], [52200, 95], [52500, 94], [52800, 94], [53100, 94], [53400, 94], [53700, 94], [54000, 94], [54300, 94], [54600, 94], [54900, 94], [55200, 94], [55500, 94], [55800, 94], [56100, 94], [56400, 94], [56700, 94], [57000, 94], [57300, 94], [57600, 94], [57900, 93], [58200, 93], [58500, 93], [58800, 93], [59100, 93], [59400, 93], [59700, 93], [60000, 93], [60300, 93], [60600, 93], [60900, 93], [61200, 93], [61500, 93], [61800, 93], [62100, 93], [62400, 93], [62700, 93], [63000, 93], [63300, 92], [63600, 92], [63900, 92], [64200, 92], [64500, 92], [64800, 92], [65100, 92], [65400, 92], [65700, 92], [66000, 92], [66300, 92], [66600, 92], [66900, 92], [67200, 92], [67500, 92], [67800, 92], [68100, 92], [68400, 92], [68700, 91], [69000, 91], [69300, 91], [69600, 91], [69900, 91], [70200, 91], [70500, 91], [70800, 84], [71100, 64], [71400, 45], [71700, 34], [72000, 34], [72300, 34], [72600, 34], [72900, 34], [73200, 33], [73500, 33], [73800, 33], [74100, 33], [74400, 33], [74701, 32], [75005, 28], [75309, 24], [75613, 20], [75917, 15], [76221, 11], [76525, 6]], "point": [49, 127]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-00.35|+01.89|-03.29"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 98, 256], "mask": [[50, 10], [350, 10], [649, 11], [948, 12], [1247, 13], [1547, 13], [1846, 14], [2145, 14], [2444, 15], [2743, 16], [3043, 16], [3342, 17], [3641, 18], [3940, 19], [4240, 19], [4539, 20], [4838, 20], [5137, 21], [5436, 22], [5736, 22], [6035, 23], [6334, 24], [6633, 25], [6933, 25], [7232, 26], [7531, 26], [7830, 27], [8130, 27], [8429, 28], [8728, 29], [9027, 30], [9326, 31], [9626, 31], [9925, 32], [10224, 32], [10523, 33], [10823, 33], [11122, 34], [11421, 35], [11720, 36], [12019, 37], [12319, 37], [12618, 38], [12917, 38], [13216, 39], [13516, 39], [13815, 40], [14114, 41], [14413, 42], [14713, 42], [15012, 43], [15311, 44], [15610, 44], [15909, 45], [16209, 45], [16508, 46], [16807, 47], [17106, 48], [17406, 48], [17706, 48], [18006, 48], [18306, 47], [18605, 48], [18905, 48], [19205, 48], [19505, 48], [19805, 48], [20105, 48], [20405, 48], [20704, 49], [21004, 49], [21304, 48], [21604, 48], [21904, 48], [22204, 48], [22503, 49], [22803, 49], [23103, 51], [23403, 54], [23703, 56], [24003, 59], [24302, 63], [24602, 65], [24902, 68], [25202, 70], [25502, 73], [25802, 75], [26101, 79], [26401, 82], [26701, 84], [27001, 87], [27301, 89], [27601, 92], [27900, 95], [28200, 98], [28500, 99], [28800, 99], [29100, 99], [29400, 99], [29700, 99], [30000, 99], [30300, 99], [30600, 49], [30652, 47], [30900, 49], [30955, 43], [31200, 49], [31258, 40], [31500, 49], [31562, 36], [31800, 52], [31865, 33], [32100, 56], [32168, 30], [32400, 59], [32471, 27], [32700, 62], [32774, 24], [33000, 65], [33077, 21], [33300, 69], [33380, 18], [33600, 72], [33683, 15], [33900, 75], [33987, 11], [34200, 78], [34290, 8], [34500, 82], [34593, 5], [34800, 85], [34896, 2], [35100, 88], [35400, 91], [35700, 94], [36000, 98], [36300, 97], [36600, 97], [36900, 97], [37200, 97], [37500, 97], [37800, 97], [38100, 97], [38400, 97], [38700, 97], [39000, 97], [39300, 97], [39600, 97], [39900, 97], [40200, 97], [40500, 97], [40800, 97], [41100, 97], [41400, 97], [41700, 96], [42000, 96], [42300, 96], [42600, 96], [42900, 96], [43200, 96], [43500, 96], [43800, 96], [44100, 96], [44400, 96], [44700, 96], [45000, 96], [45300, 96], [45600, 96], [45900, 96], [46200, 96], [46500, 96], [46800, 96], [47100, 95], [47400, 95], [47700, 95], [48000, 95], [48300, 95], [48600, 95], [48900, 95], [49200, 95], [49500, 95], [49800, 95], [50100, 95], [50400, 95], [50700, 95], [51000, 95], [51300, 95], [51600, 95], [51900, 95], [52200, 95], [52500, 94], [52800, 94], [53100, 94], [53400, 94], [53700, 94], [54000, 94], [54300, 94], [54600, 94], [54900, 94], [55200, 94], [55500, 94], [55800, 94], [56100, 94], [56400, 94], [56700, 94], [57000, 94], [57300, 94], [57600, 94], [57900, 93], [58200, 93], [58500, 93], [58800, 93], [59100, 93], [59400, 93], [59700, 93], [60000, 93], [60300, 93], [60600, 93], [60900, 93], [61200, 93], [61500, 93], [61800, 43], [61860, 33], [62100, 42], [62162, 31], [62400, 42], [62462, 31], [62700, 41], [62763, 30], [63000, 40], [63063, 30], [63300, 40], [63364, 28], [63600, 39], [63664, 28], [63900, 39], [63964, 28], [64200, 38], [64264, 28], [64500, 38], [64565, 27], [64800, 37], [64865, 27], [65100, 37], [65165, 27], [65400, 37], [65465, 27], [65700, 37], [65765, 27], [66000, 36], [66065, 27], [66300, 36], [66365, 27], [66600, 36], [66665, 27], [66900, 36], [66965, 27], [67200, 36], [67265, 27], [67500, 36], [67564, 28], [67800, 36], [67864, 28], [68100, 36], [68164, 28], [68400, 36], [68464, 28], [68700, 36], [68763, 28], [69000, 37], [69063, 28], [69300, 37], [69362, 29], [69600, 38], [69661, 30], [69900, 39], [69960, 31], [70200, 41], [70257, 34], [70500, 91], [70800, 84], [71100, 64], [71400, 45], [71700, 34], [72000, 34], [72300, 34], [72600, 34], [72900, 34], [73200, 33], [73500, 33], [73800, 33], [74100, 33], [74400, 33], [74701, 32], [75005, 28], [75309, 24], [75613, 20], [75917, 15], [76221, 11], [76525, 6]], "point": [49, 127]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan28", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -1.25, "y": 0.900998235, "z": -1.0}, "object_poses": [{"objectName": "Potato_5e3edff7", "position": {"x": -2.56082582, "y": 0.926596642, "z": -0.6338309}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Potato_5e3edff7", "position": {"x": -0.321308225, "y": 1.52068627, "z": -0.413377553}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "DishSponge_7e2c9e01", "position": {"x": -0.509394348, "y": 0.7514796, "z": -2.84604383}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_e676ac70", "position": {"x": -4.080143, "y": 0.760130465, "z": -3.2276957}, "rotation": {"x": 0.0, "y": 109.125885, "z": 0.0}}, {"objectName": "ButterKnife_e676ac70", "position": {"x": -2.96263933, "y": 0.894662857, "z": -0.376522064}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Spoon_7dcfa4cd", "position": {"x": -4.080143, "y": 0.7612178, "z": -3.2276957}, "rotation": {"x": 0.0, "y": 109.125885, "z": 0.0}}, {"objectName": "Spoon_7dcfa4cd", "position": {"x": -0.157691017, "y": 0.9424294, "z": -1.5381465}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_1d69618b", "position": {"x": -3.22479749, "y": 0.775088251, "z": -3.59047}, "rotation": {"x": 0.0, "y": 109.125885, "z": 0.0}}, {"objectName": "Egg_571582a1", "position": {"x": -4.121099, "y": 0.80115515, "z": -3.34579587}, "rotation": {"x": 0.0, "y": 109.125885, "z": 0.0}}, {"objectName": "Egg_571582a1", "position": {"x": -0.237226725, "y": 0.9833667, "z": -2.96039343}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Cup_41873c33", "position": {"x": -3.56536055, "y": 0.895324, "z": -0.20498088}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Cup_41873c33", "position": {"x": -4.122419, "y": 0.6912032, "z": -0.430667937}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_41873c33", "position": {"x": -3.601276, "y": 0.76079154, "z": -3.26145744}, "rotation": {"x": 0.0, "y": 109.125885, "z": 0.0}}, {"objectName": "Plate_ba5a8c1e", "position": {"x": -4.182487, "y": 0.7046318, "z": -0.187332019}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_b1760e7a", "position": {"x": -0.252378225, "y": 1.33736539, "z": -0.2970674}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Tomato_b1760e7a", "position": {"x": -3.8821454, "y": 0.742062449, "z": -0.369833916}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_1f51c27f", "position": {"x": -0.126795739, "y": 1.93725908, "z": -2.28551221}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_4d72833d", "position": {"x": -2.35991621, "y": 0.9760524, "z": -0.204987615}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Lettuce_ec37c659", "position": {"x": -0.321309954, "y": 1.57969487, "z": -0.9949339}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Lettuce_ec37c659", "position": {"x": -3.92534733, "y": 0.8510728, "z": -3.39901733}, "rotation": {"x": 0.0, "y": 244.125916, "z": 0.0}}, {"objectName": "SaltShaker_6f13b4f9", "position": {"x": -2.560823, "y": 0.890926957, "z": -0.119217604}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "PepperShaker_8ae9f292", "position": {"x": -1.18577826, "y": 0.7503003, "z": -3.44731569}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_8ae9f292", "position": {"x": -3.70451212, "y": 0.7563945, "z": -3.50029922}, "rotation": {"x": 0.0, "y": 244.125916, "z": 0.0}}, {"objectName": "Mug_f3c7368a", "position": {"x": -0.359176427, "y": 1.48371041, "z": -0.5296887}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Mug_f3c7368a", "position": {"x": -3.94221377, "y": 0.6855, "z": -0.491501927}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_4eadea0d", "position": {"x": -4.002282, "y": 0.7527082, "z": -0.126498014}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_0407be4c", "position": {"x": -0.05784577, "y": 1.50082648, "z": -2.96471334}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pot_c7418f98", "position": {"x": -0.4374, "y": 0.961999953, "z": -1.8457}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Fork_938fe393", "position": {"x": -0.7214115, "y": 0.8140154, "z": -3.49191141}, "rotation": {"x": 9.923132e-15, "y": 315.000031, "z": -9.92312e-15}}, {"objectName": "Cup_41873c33", "position": {"x": -3.58681631, "y": 0.76079154, "z": -2.841071}, "rotation": {"x": 0.0, "y": 199.125946, "z": 0.0}}, {"objectName": "Tomato_b1760e7a", "position": {"x": -0.293651253, "y": 1.33736539, "z": -0.7623111}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Pan_d1250561", "position": {"x": -0.2221, "y": 0.961999953, "z": -2.243}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ba5a8c1e", "position": {"x": -0.351777434, "y": 1.51787663, "z": -3.47133732}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Bread_4d72833d", "position": {"x": -3.36445379, "y": 0.9760524, "z": -0.3765198}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Spatula_1d69618b", "position": {"x": -0.818319, "y": 0.8284667, "z": -3.51064134}, "rotation": {"x": -9.923121e-15, "y": 225.000031, "z": -9.92313243e-15}}, {"objectName": "Knife_1b546504", "position": {"x": -1.47139764, "y": 0.967918336, "z": -3.59545565}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_6f13b4f9", "position": {"x": -2.56082487, "y": 0.890926957, "z": -0.462293148}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "PepperShaker_8ae9f292", "position": {"x": -0.09783375, "y": 1.93873167, "z": -0.766370654}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_e676ac70", "position": {"x": -0.157691017, "y": 0.9413421, "z": -1.58196974}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_4eadea0d", "position": {"x": -0.3970436, "y": 1.55012226, "z": -0.2970661}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "DishSponge_7e2c9e01", "position": {"x": -0.09783375, "y": 1.94198978, "z": -0.660120666}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5e3edff7", "position": {"x": -2.962638, "y": 0.926596642, "z": -0.1192154}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Egg_571582a1", "position": {"x": -4.002282, "y": 0.7315668, "z": -0.369833976}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_7dcfa4cd", "position": {"x": -2.76173139, "y": 0.895750165, "z": -0.2907543}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Lettuce_ec37c659", "position": {"x": -3.2874794, "y": 0.8510728, "z": -3.29803181}, "rotation": {"x": 0.0, "y": 244.125916, "z": 0.0}}, {"objectName": "Mug_f3c7368a", "position": {"x": -3.8821454, "y": 0.686296344, "z": -0.187332}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_1f51c27f", "position": {"x": -0.212670773, "y": 1.50094545, "z": -2.70088148}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 239565598, "scene_num": 28}, "task_id": "trial_T20190909_053018_834213", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "ANBWJZYU2A68T_39ZSFO5CABNKMKRC4SJURPKUNWEJUA", "high_descs": ["Turn around and go across the room to face the black table ", "Pick up the cup from the center of the table", "Turn around and go to the stove", "Place the cup inside the microwave above the stove, turn the microwave on for a few seconds, and take out the cup", "Take the cup  and go to the right to the sink", "Put the cup inside the cabinet above the sink"], "task_desc": "To place a heated cup inside a cabinet", "votes": [1, 1]}, {"assignment_id": "A20FCMWP43CVIU_3EJPLAJKEP7PKO6FQWODZDU438QZ6Q", "high_descs": ["walk to face round black table ", "pick up black glass on left on table", "walk to face microwave", "heat glass in microwave, pick up glass from microwave", "turn to face cabinet above sink", "put glass in cabinet above sink"], "task_desc": "put heated glass away in kitchen cabinet", "votes": [1, 1]}, {"assignment_id": "AM2KK02JXXW48_3NPFYT4IZFV2CMV61R9EEJRIPM6XG4", "high_descs": ["Turn around, move to the counter, turn right, move to the round black table.", "Pick up the cup in the middle of the table. ", "Turn around, bring the cup to the microwave above the stove. ", "Heat the cup in the microwave. ", "Take the heated cup to the sink on the right of the microwave.", "Put the heated cup in the cabinet above the sink in the corner. "], "task_desc": "Put a heated cup away in a cabinet over the sink. ", "votes": [1, 1]}]}}