{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 8}, {"high_idx": 3, "image_name": "000000085.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000086.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000087.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000088.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000089.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000090.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000091.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000092.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000093.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000094.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 9}, {"high_idx": 4, "image_name": "000000100.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000101.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000102.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000103.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000104.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000105.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000106.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000107.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000108.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000109.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000110.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000111.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000112.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000113.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000114.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000115.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000116.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000117.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000118.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000119.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000120.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000121.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000122.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000123.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 19}, {"high_idx": 5, "image_name": "000000156.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000157.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000158.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000159.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000160.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000161.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000162.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000163.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000164.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000165.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000166.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000167.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000168.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000169.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000170.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000171.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000172.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000173.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000174.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000175.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000176.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000177.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000178.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000179.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000180.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000181.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000227.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000228.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000229.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000230.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000231.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000232.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000233.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000234.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000235.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000236.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000237.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000238.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000239.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000240.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000241.png", "low_idx": 31}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "ToiletPaper", "parent_target": "<PERSON><PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["toiletpaperhanger"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-2|-10|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [-0.37360012, -0.37360012, -10.47559928, -10.47559928, 3.368, 3.368]], "coordinateReceptacleObjectId": ["ToiletPaperHanger", [0.0, 0.0, -10.148, -10.148, 3.772, 3.772]], "forceVisible": true, "objectId": "ToiletPaper|-00.09|+00.84|-02.62"}}, {"discrete_action": {"action": "GotoLocation", "args": ["toilet"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-2|-10|2|15"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "toilet"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [-0.37360012, -0.37360012, -10.47559928, -10.47559928, 3.368, 3.368]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON>", [-1.810838224, -1.810838224, -12.2095146, -12.2095146, 0.0025726184, 0.0025726184]], "forceVisible": true, "objectId": "ToiletPaper|-00.09|+00.84|-02.62", "receptacleObjectId": "Toilet|-00.45|+00.00|-03.05"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-8|-14|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [-11.53910636, -11.53910636, -14.36422636, -14.36422636, 3.214, 3.214]], "coordinateReceptacleObjectId": ["CounterTop", [-10.532, -10.532, -12.164, -12.164, 3.376, 3.376]], "forceVisible": true, "objectId": "ToiletPaper|-02.88|+00.80|-03.59"}}, {"discrete_action": {"action": "GotoLocation", "args": ["toilet"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-2|-10|2|15"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "toilet"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [-11.53910636, -11.53910636, -14.36422636, -14.36422636, 3.214, 3.214]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON>", [-1.810838224, -1.810838224, -12.2095146, -12.2095146, 0.0025726184, 0.0025726184]], "forceVisible": true, "objectId": "ToiletPaper|-02.88|+00.80|-03.59", "receptacleObjectId": "Toilet|-00.45|+00.00|-03.05"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|-00.09|+00.84|-02.62"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [150, 93, 164, 97], "mask": [[27750, 15], [28050, 15], [28350, 15], [28650, 15], [28950, 10]], "point": [157, 94]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|-00.09|+00.84|-02.62", "placeStationary": true, "receptacleObjectId": "Toilet|-00.45|+00.00|-03.05"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [197, 173, 299, 300], "mask": [[51797, 27], [51848, 27], [52097, 27], [52148, 27], [52397, 27], [52449, 26], [52697, 27], [52749, 8], [52765, 10], [52997, 27], [53049, 7], [53067, 9], [53297, 28], [53349, 7], [53368, 10], [53597, 29], [53649, 7], [53669, 10], [53692, 1], [53897, 60], [53970, 10], [53991, 3], [54198, 60], [54272, 10], [54291, 5], [54498, 61], [54573, 24], [54799, 61], [54873, 26], [55099, 62], [55173, 27], [55399, 63], [55472, 28], [55699, 101], [55999, 101], [56299, 101], [56599, 101], [56899, 101], [57199, 101], [57499, 101], [57800, 100], [58100, 100], [58400, 100], [58700, 100], [59000, 100], [59300, 100], [59600, 100], [59900, 100], [60200, 100], [60500, 100], [60800, 100], [61100, 100], [61400, 100], [61700, 100], [62000, 100], [62300, 100], [62600, 100], [62900, 100], [63200, 99], [63500, 99], [63800, 99], [64100, 99], [64400, 98], [64700, 98], [65001, 97], [65301, 97], [65601, 96], [65901, 96], [66201, 96], [66501, 96], [66800, 96], [67100, 96], [67400, 96], [67700, 96], [68000, 95], [68300, 95], [68600, 95], [68900, 95], [69200, 94], [69500, 94], [69800, 94], [70100, 94], [70400, 94], [70700, 93], [71000, 93], [71300, 93], [71600, 93], [71900, 93], [72200, 92], [72500, 92], [72800, 92], [73100, 92], [73400, 92], [73700, 92], [74000, 92], [74300, 91], [74600, 91], [74900, 91], [75200, 91], [75500, 91], [75800, 91], [76101, 89], [76401, 89], [76701, 89], [77002, 88], [77302, 88], [77602, 88], [77903, 87], [78203, 86], [78503, 86], [78803, 86], [79104, 85], [79404, 85], [79704, 85], [80005, 83], [80305, 83], [80605, 83], [80906, 82], [81206, 82], [81506, 82], [81806, 81], [82107, 80], [82407, 80], [82716, 70], [83016, 62], [83316, 63], [83616, 64], [83916, 65], [84216, 66], [84516, 67], [84816, 67], [85116, 68], [85416, 69], [85716, 70], [86017, 70], [86317, 71], [86617, 72], [86917, 74], [87217, 75], [87517, 77], [87817, 78], [88117, 79], [88418, 80], [88718, 81], [89018, 82], [89318, 82], [89618, 82], [89918, 82]], "point": [248, 235]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|-02.88|+00.80|-03.59"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [116, 105, 140, 140], "mask": [[31324, 10], [31621, 15], [31919, 19], [32218, 21], [32517, 23], [32816, 24], [33116, 24], [33416, 25], [33716, 25], [34016, 25], [34316, 25], [34617, 24], [34917, 24], [35217, 24], [35517, 24], [35817, 24], [36117, 24], [36417, 24], [36717, 24], [37017, 24], [37318, 23], [37618, 23], [37918, 23], [38218, 23], [38518, 23], [38818, 23], [39118, 23], [39418, 23], [39719, 22], [40019, 22], [40319, 21], [40619, 20], [40920, 19], [41222, 16], [41523, 12], [41826, 7]], "point": [128, 121]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|-02.88|+00.80|-03.59", "placeStationary": true, "receptacleObjectId": "Toilet|-00.45|+00.00|-03.05"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [197, 173, 299, 300], "mask": [[51797, 16], [51819, 5], [51848, 27], [52097, 16], [52119, 5], [52148, 27], [52397, 15], [52419, 5], [52449, 26], [52697, 15], [52719, 5], [52749, 8], [52765, 10], [52997, 15], [53019, 5], [53049, 7], [53067, 9], [53297, 15], [53319, 6], [53349, 7], [53368, 10], [53597, 15], [53619, 7], [53649, 7], [53669, 10], [53692, 1], [53897, 15], [53919, 38], [53970, 10], [53991, 3], [54198, 14], [54219, 39], [54272, 10], [54291, 5], [54498, 14], [54519, 40], [54573, 24], [54799, 13], [54819, 41], [54873, 26], [55099, 14], [55118, 43], [55173, 27], [55399, 63], [55472, 28], [55699, 101], [55999, 101], [56299, 101], [56599, 101], [56899, 101], [57199, 101], [57499, 101], [57800, 100], [58100, 100], [58400, 100], [58700, 100], [59000, 100], [59300, 100], [59600, 100], [59900, 100], [60200, 100], [60500, 100], [60800, 100], [61100, 100], [61400, 100], [61700, 100], [62000, 100], [62300, 100], [62600, 100], [62900, 100], [63200, 99], [63500, 99], [63800, 99], [64100, 99], [64400, 98], [64700, 98], [65001, 97], [65301, 97], [65601, 96], [65901, 96], [66201, 96], [66501, 96], [66800, 96], [67100, 96], [67400, 96], [67700, 96], [68000, 95], [68300, 95], [68600, 95], [68900, 95], [69200, 94], [69500, 94], [69800, 94], [70100, 94], [70400, 94], [70700, 93], [71000, 93], [71300, 93], [71600, 93], [71900, 93], [72200, 92], [72500, 92], [72800, 92], [73100, 92], [73400, 92], [73700, 92], [74000, 92], [74300, 91], [74600, 91], [74900, 91], [75200, 91], [75500, 91], [75800, 91], [76101, 89], [76401, 89], [76701, 89], [77002, 88], [77302, 88], [77602, 88], [77903, 87], [78203, 86], [78503, 86], [78803, 86], [79104, 85], [79404, 85], [79704, 85], [80005, 83], [80305, 83], [80605, 83], [80906, 82], [81206, 82], [81506, 82], [81806, 81], [82107, 80], [82407, 80], [82716, 70], [83016, 62], [83316, 63], [83616, 64], [83916, 65], [84216, 66], [84516, 67], [84816, 67], [85116, 68], [85416, 69], [85716, 70], [86017, 70], [86317, 71], [86617, 72], [86917, 74], [87217, 75], [87517, 77], [87817, 78], [88117, 79], [88418, 80], [88718, 81], [89018, 82], [89318, 82], [89618, 82], [89918, 82]], "point": [248, 235]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan418", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -1.75, "y": 0.901641965, "z": -2.25}, "object_poses": [{"objectName": "Cloth_57ecd3c7", "position": {"x": -2.6203053, "y": 0.806803942, "z": -2.328937}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBar_cc56b620", "position": {"x": -0.129710108, "y": 1.04541028, "z": -3.2456522}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SoapBar_cc56b620", "position": {"x": -2.637314, "y": 0.87252593, "z": -3.128395}, "rotation": {"x": 1.40334208e-14, "y": 90.0, "z": 0.0}}, {"objectName": "Candle_f876c62f", "position": {"x": -2.68901014, "y": 0.07580155, "z": -3.75502729}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Candle_f876c62f", "position": {"x": -2.355834, "y": 0.815858066, "z": -3.64916849}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_ea8e94da", "position": {"x": -2.7640202, "y": 0.06715262, "z": -2.436451}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_94c9196d", "position": {"x": -0.129709333, "y": 1.04740262, "z": -2.98336458}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "HandTowel_88b2545f", "position": {"x": -2.9620862, "y": 1.584, "z": -2.569}, "rotation": {"x": 0.0, "y": 270.000122, "z": 0.0}}, {"objectName": "Candle_f876c62f", "position": {"x": -0.0378088951, "y": 1.05469859, "z": -2.917793}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Cloth_57ecd3c7", "position": {"x": -2.6203053, "y": 0.806804, "z": -3.7072804}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "HandTowel_88b2545f", "position": {"x": -2.9620862, "y": 1.584, "z": -3.55099988}, "rotation": {"x": 0.0, "y": 270.000122, "z": 0.0}}, {"objectName": "Plunger_ed669751", "position": {"x": -0.1256311, "y": 0.0006431546, "z": -3.49332213}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBar_cc56b620", "position": {"x": -0.1909765, "y": 1.04541028, "z": -3.11450815}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "ToiletPaper_0ae85b0d", "position": {"x": -2.88477659, "y": 0.8035, "z": -3.59105659}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_1963f6ed", "position": {"x": -0.09340003, "y": 0.842, "z": -2.61889982}, "rotation": {"x": -5.122642e-06, "y": 270.0, "z": 270.000061}}, {"objectName": "Towel_c13c7186", "position": {"x": -1.29189992, "y": 1.386, "z": -0.63}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_94c9196d", "position": {"x": -2.355834, "y": 0.8085621, "z": -3.53294468}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_ea8e94da", "position": {"x": -2.7640202, "y": 0.06715262, "z": -2.61063337}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ScrubBrush_d4414847", "position": {"x": -0.325021982, "y": 0.0006431546, "z": -3.342877}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 998914046, "scene_num": 418}, "task_id": "trial_T20190910_045657_571849", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A2KAGFQU28JY43_3AZHRG4CU7BTLKD730DNMBFYB4530K", "high_descs": ["Turn to the small floor space between the toilet and the shower. ", "Pick up the empty toilet paper roll on the floor, underneath the toilet paper dispenser. ", "Get as close to the back of the toilet as you can.", "Place the empty toilet paper roll to the left of the pump bottle, on the back of the toilet. ", "Turn around and go to the far, left end of the bathroom counter behind you. ", "Pick up the roll of toilet paper, to the left of the sink, on the counter. ", "Turn to your right and walk to the end of the sink, then turn to your right and go back to the small space between the shower and the toilet. ", "Place the toilet paper roll on the toilet paper holder, hanging from the wall, to the left of the toilet. "], "task_desc": "Put a new roll of toilet paper on the holder. ", "votes": [1, 1, 1]}, {"assignment_id": "A1O3TWBUDONVLO_3BXQMRHWK2PANSXX7PWM7HCBRJAMU4", "high_descs": ["Turn left and step forward to face the floor to the left of the toilet.", "Pick up the empty toilet paper roll on the floor to the left of the toilet.", "Look up to face the back of the toilet.", "Place the empty toilet paper roll on the back of the toilet.", "Turn around and step forward to face the sink.", "Pick up the roll of toilet paper on the left side of the sink.", "Turn around and step forward to face the back of the toilet.", "Place the toilet paper roll on the back of the toilet."], "task_desc": "To move an empty plus a full toilet paper roll to the back of the toilet.", "votes": [1, 1, 1]}, {"assignment_id": "ARB8SJAWXUWTD_3QY7M81QHADDZTLY95QVIC6NU3QK73", "high_descs": ["Go to the toilet", "Pick up the empty toilet roll", "Look up at the top of the toilet", "Put the empty toilet roll on top of the toilet", "Go to the sink", "Pick up the full toilet roll", "Go to the toilet", "Put the full toilet roll on top of the toilet"], "task_desc": "Move a full and an empty toilet roll to the top of the toilet", "votes": [1, 1, 0]}]}}