
(define (problem plan_trial_T20190910_231706_006365)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Book_bar__minus_01_dot_48_bar__plus_00_dot_40_bar__plus_03_dot_61 - object
        Boots_bar__plus_00_dot_03_bar__plus_00_dot_02_bar__plus_03_dot_09 - object
        Box_bar__minus_03_dot_43_bar__plus_00_dot_64_bar__plus_02_dot_44 - object
        CellPhone_bar__minus_05_dot_74_bar__plus_00_dot_73_bar__plus_02_dot_55 - object
        Chair_bar__minus_00_dot_73_bar__plus_00_dot_02_bar__plus_00_dot_50 - object
        Chair_bar__minus_02_dot_16_bar__plus_00_dot_02_bar__plus_03_dot_88 - object
        Chair_bar__minus_04_dot_01_bar__plus_00_dot_02_bar__plus_03_dot_90 - object
        CreditCard_bar__minus_02_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_60 - object
        CreditCard_bar__minus_02_dot_90_bar__plus_00_dot_41_bar__plus_02_dot_15 - object
        FloorLamp_bar__minus_00_dot_20_bar__plus_00_dot_02_bar__plus_04_dot_49 - object
        HousePlant_bar__minus_05_dot_54_bar__plus_00_dot_72_bar__plus_00_dot_30 - object
        KeyChain_bar__minus_00_dot_07_bar__plus_00_dot_77_bar__plus_01_dot_45 - object
        KeyChain_bar__minus_00_dot_16_bar__plus_00_dot_77_bar__plus_01_dot_02 - object
        KeyChain_bar__minus_03_dot_43_bar__plus_00_dot_43_bar__plus_00_dot_60 - object
        Laptop_bar__minus_00_dot_33_bar__plus_00_dot_77_bar__plus_00_dot_72 - object
        LightSwitch_bar__minus_02_dot_50_bar__plus_01_dot_53_bar__plus_04_dot_85 - object
        Painting_bar__minus_00_dot_05_bar__plus_01_dot_22_bar__plus_00_dot_46 - object
        Painting_bar__minus_00_dot_05_bar__plus_01_dot_87_bar__plus_00_dot_50 - object
        Painting_bar__minus_01_dot_00_bar__plus_01_dot_70_bar__plus_00_dot_04 - object
        Painting_bar__minus_03_dot_31_bar__plus_01_dot_67_bar__plus_00_dot_04 - object
        Pencil_bar__minus_00_dot_29_bar__plus_00_dot_78_bar__plus_00_dot_31 - object
        Pencil_bar__minus_00_dot_34_bar__plus_00_dot_77_bar__plus_01_dot_59 - object
        Pencil_bar__minus_05_dot_72_bar__plus_00_dot_73_bar__plus_00_dot_15 - object
        Pencil_bar__minus_05_dot_72_bar__plus_00_dot_73_bar__plus_00_dot_24 - object
        Pen_bar__minus_03_dot_19_bar__plus_00_dot_42_bar__plus_02_dot_05 - object
        Pen_bar__minus_05_dot_74_bar__plus_00_dot_73_bar__plus_02_dot_40 - object
        Pillow_bar__minus_02_dot_15_bar__plus_00_dot_62_bar__plus_00_dot_59 - object
        RemoteControl_bar__minus_00_dot_01_bar__plus_00_dot_77_bar__plus_01_dot_66 - object
        RemoteControl_bar__minus_05_dot_70_bar__plus_00_dot_73_bar__plus_02_dot_25 - object
        Statue_bar__minus_00_dot_14_bar__plus_00_dot_78_bar__plus_01_dot_59 - object
        Statue_bar__minus_02_dot_75_bar__plus_00_dot_42_bar__plus_02_dot_34 - object
        Statue_bar__minus_02_dot_75_bar__plus_00_dot_42_bar__plus_02_dot_63 - object
        Television_bar__minus_05_dot_76_bar__plus_01_dot_21_bar__plus_02_dot_39 - object
        TissueBox_bar__minus_00_dot_34_bar__plus_00_dot_77_bar__plus_01_dot_31 - object
        TissueBox_bar__minus_03_dot_04_bar__plus_00_dot_42_bar__plus_02_dot_63 - object
        TissueBox_bar__minus_03_dot_48_bar__plus_00_dot_42_bar__plus_02_dot_63 - object
        WateringCan_bar__minus_05_dot_24_bar__plus_00_dot_02_bar__plus_00_dot_14 - object
        Window_bar__plus_00_dot_13_bar__plus_02_dot_38_bar__plus_02_dot_42 - object
        Window_bar__plus_00_dot_17_bar__plus_01_dot_02_bar__plus_02_dot_44 - object
        Window_bar__plus_00_dot_17_bar__plus_01_dot_74_bar__plus_01_dot_38 - object
        Window_bar__plus_00_dot_17_bar__plus_01_dot_74_bar__plus_03_dot_51 - object
        ArmChair_bar__minus_01_dot_32_bar__plus_00_dot_00_bar__plus_03_dot_65 - receptacle
        CoffeeTable_bar__minus_03_dot_04_bar__plus_00_dot_03_bar__plus_02_dot_34 - receptacle
        Drawer_bar__minus_05_dot_58_bar__plus_00_dot_21_bar__plus_02_dot_13 - receptacle
        Drawer_bar__minus_05_dot_58_bar__plus_00_dot_21_bar__plus_02_dot_68 - receptacle
        Drawer_bar__minus_05_dot_58_bar__plus_00_dot_52_bar__plus_02_dot_13 - receptacle
        Drawer_bar__minus_05_dot_58_bar__plus_00_dot_52_bar__plus_02_dot_68 - receptacle
        Dresser_bar__minus_05_dot_77_bar__plus_00_dot_02_bar__plus_02_dot_40 - receptacle
        GarbageCan_bar__minus_05_dot_82_bar__plus_00_dot_02_bar__plus_04_dot_66 - receptacle
        SideTable_bar__minus_00_dot_21_bar__plus_00_dot_02_bar__plus_01_dot_52 - receptacle
        SideTable_bar__minus_00_dot_31_bar__plus_00_dot_02_bar__plus_00_dot_62 - receptacle
        SideTable_bar__minus_05_dot_57_bar__plus_00_dot_02_bar__plus_00_dot_29 - receptacle
        Sofa_bar__minus_03_dot_13_bar__plus_00_dot_02_bar__plus_00_dot_41 - receptacle
        loc_bar__minus_11_bar_17_bar_0_bar_15 - location
        loc_bar__minus_20_bar_13_bar_2_bar_60 - location
        loc_bar__minus_22_bar_17_bar_0_bar_60 - location
        loc_bar__minus_5_bar_2_bar_1_bar_60 - location
        loc_bar__minus_2_bar_10_bar_1_bar__minus_30 - location
        loc_bar__minus_5_bar_2_bar_1_bar__minus_15 - location
        loc_bar__minus_2_bar_10_bar_1_bar_45 - location
        loc_bar__minus_17_bar_17_bar_2_bar_60 - location
        loc_bar__minus_5_bar_2_bar_2_bar_0 - location
        loc_bar__minus_21_bar_5_bar_2_bar_60 - location
        loc_bar__minus_1_bar_13_bar_1_bar_60 - location
        loc_bar__minus_21_bar_14_bar_2_bar_60 - location
        loc_bar__minus_14_bar_6_bar_2_bar_60 - location
        loc_bar__minus_1_bar_15_bar_0_bar_60 - location
        loc_bar__minus_4_bar_7_bar_1_bar_60 - location
        loc_bar__minus_19_bar_2_bar_3_bar_60 - location
        loc_bar__minus_20_bar_10_bar_3_bar_60 - location
        loc_bar__minus_21_bar_12_bar_2_bar_60 - location
        loc_bar__minus_20_bar_15_bar_2_bar_60 - location
        loc_bar__minus_5_bar_2_bar_1_bar_15 - location
        loc_bar__minus_12_bar_14_bar_2_bar_60 - location
        loc_bar__minus_11_bar_16_bar_1_bar_60 - location
        loc_bar__minus_2_bar_14_bar_1_bar_0 - location
        loc_bar__minus_6_bar_11_bar_0_bar_60 - location
        loc_bar__minus_3_bar_6_bar_1_bar_0 - location
        loc_bar__minus_13_bar_5_bar_2_bar_0 - location
        loc_bar__minus_14_bar_7_bar_2_bar_30 - location
        )
    

(:init


        (receptacleType GarbageCan_bar__minus_05_dot_82_bar__plus_00_dot_02_bar__plus_04_dot_66 GarbageCanType)
        (receptacleType ArmChair_bar__minus_01_dot_32_bar__plus_00_dot_00_bar__plus_03_dot_65 ArmChairType)
        (receptacleType Sofa_bar__minus_03_dot_13_bar__plus_00_dot_02_bar__plus_00_dot_41 SofaType)
        (receptacleType Drawer_bar__minus_05_dot_58_bar__plus_00_dot_21_bar__plus_02_dot_68 DrawerType)
        (receptacleType Drawer_bar__minus_05_dot_58_bar__plus_00_dot_21_bar__plus_02_dot_13 DrawerType)
        (receptacleType Drawer_bar__minus_05_dot_58_bar__plus_00_dot_52_bar__plus_02_dot_68 DrawerType)
        (receptacleType CoffeeTable_bar__minus_03_dot_04_bar__plus_00_dot_03_bar__plus_02_dot_34 CoffeeTableType)
        (receptacleType SideTable_bar__minus_00_dot_31_bar__plus_00_dot_02_bar__plus_00_dot_62 SideTableType)
        (receptacleType SideTable_bar__minus_05_dot_57_bar__plus_00_dot_02_bar__plus_00_dot_29 SideTableType)
        (receptacleType Drawer_bar__minus_05_dot_58_bar__plus_00_dot_52_bar__plus_02_dot_13 DrawerType)
        (receptacleType SideTable_bar__minus_00_dot_21_bar__plus_00_dot_02_bar__plus_01_dot_52 SideTableType)
        (receptacleType Dresser_bar__minus_05_dot_77_bar__plus_00_dot_02_bar__plus_02_dot_40 DresserType)
        (objectType Pencil_bar__minus_05_dot_72_bar__plus_00_dot_73_bar__plus_00_dot_24 PencilType)
        (objectType RemoteControl_bar__minus_00_dot_01_bar__plus_00_dot_77_bar__plus_01_dot_66 RemoteControlType)
        (objectType Chair_bar__minus_04_dot_01_bar__plus_00_dot_02_bar__plus_03_dot_90 ChairType)
        (objectType Pen_bar__minus_03_dot_19_bar__plus_00_dot_42_bar__plus_02_dot_05 PenType)
        (objectType Painting_bar__minus_03_dot_31_bar__plus_01_dot_67_bar__plus_00_dot_04 PaintingType)
        (objectType FloorLamp_bar__minus_00_dot_20_bar__plus_00_dot_02_bar__plus_04_dot_49 FloorLampType)
        (objectType CreditCard_bar__minus_02_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_60 CreditCardType)
        (objectType Box_bar__minus_03_dot_43_bar__plus_00_dot_64_bar__plus_02_dot_44 BoxType)
        (objectType Pencil_bar__minus_00_dot_29_bar__plus_00_dot_78_bar__plus_00_dot_31 PencilType)
        (objectType TissueBox_bar__minus_03_dot_48_bar__plus_00_dot_42_bar__plus_02_dot_63 TissueBoxType)
        (objectType CreditCard_bar__minus_02_dot_90_bar__plus_00_dot_41_bar__plus_02_dot_15 CreditCardType)
        (objectType HousePlant_bar__minus_05_dot_54_bar__plus_00_dot_72_bar__plus_00_dot_30 HousePlantType)
        (objectType Window_bar__plus_00_dot_17_bar__plus_01_dot_74_bar__plus_01_dot_38 WindowType)
        (objectType Painting_bar__minus_00_dot_05_bar__plus_01_dot_87_bar__plus_00_dot_50 PaintingType)
        (objectType Painting_bar__minus_00_dot_05_bar__plus_01_dot_22_bar__plus_00_dot_46 PaintingType)
        (objectType Statue_bar__minus_02_dot_75_bar__plus_00_dot_42_bar__plus_02_dot_34 StatueType)
        (objectType Chair_bar__minus_02_dot_16_bar__plus_00_dot_02_bar__plus_03_dot_88 ChairType)
        (objectType TissueBox_bar__minus_00_dot_34_bar__plus_00_dot_77_bar__plus_01_dot_31 TissueBoxType)
        (objectType RemoteControl_bar__minus_05_dot_70_bar__plus_00_dot_73_bar__plus_02_dot_25 RemoteControlType)
        (objectType Pillow_bar__minus_02_dot_15_bar__plus_00_dot_62_bar__plus_00_dot_59 PillowType)
        (objectType KeyChain_bar__minus_00_dot_16_bar__plus_00_dot_77_bar__plus_01_dot_02 KeyChainType)
        (objectType LightSwitch_bar__minus_02_dot_50_bar__plus_01_dot_53_bar__plus_04_dot_85 LightSwitchType)
        (objectType Laptop_bar__minus_00_dot_33_bar__plus_00_dot_77_bar__plus_00_dot_72 LaptopType)
        (objectType Painting_bar__minus_01_dot_00_bar__plus_01_dot_70_bar__plus_00_dot_04 PaintingType)
        (objectType Statue_bar__minus_00_dot_14_bar__plus_00_dot_78_bar__plus_01_dot_59 StatueType)
        (objectType CellPhone_bar__minus_05_dot_74_bar__plus_00_dot_73_bar__plus_02_dot_55 CellPhoneType)
        (objectType Statue_bar__minus_02_dot_75_bar__plus_00_dot_42_bar__plus_02_dot_63 StatueType)
        (objectType Window_bar__plus_00_dot_17_bar__plus_01_dot_74_bar__plus_03_dot_51 WindowType)
        (objectType KeyChain_bar__minus_03_dot_43_bar__plus_00_dot_43_bar__plus_00_dot_60 KeyChainType)
        (objectType Book_bar__minus_01_dot_48_bar__plus_00_dot_40_bar__plus_03_dot_61 BookType)
        (objectType Pen_bar__minus_05_dot_74_bar__plus_00_dot_73_bar__plus_02_dot_40 PenType)
        (objectType Window_bar__plus_00_dot_13_bar__plus_02_dot_38_bar__plus_02_dot_42 WindowType)
        (objectType Pencil_bar__minus_05_dot_72_bar__plus_00_dot_73_bar__plus_00_dot_15 PencilType)
        (objectType Boots_bar__plus_00_dot_03_bar__plus_00_dot_02_bar__plus_03_dot_09 BootsType)
        (objectType WateringCan_bar__minus_05_dot_24_bar__plus_00_dot_02_bar__plus_00_dot_14 WateringCanType)
        (objectType Chair_bar__minus_00_dot_73_bar__plus_00_dot_02_bar__plus_00_dot_50 ChairType)
        (objectType Pencil_bar__minus_00_dot_34_bar__plus_00_dot_77_bar__plus_01_dot_59 PencilType)
        (objectType TissueBox_bar__minus_03_dot_04_bar__plus_00_dot_42_bar__plus_02_dot_63 TissueBoxType)
        (objectType KeyChain_bar__minus_00_dot_07_bar__plus_00_dot_77_bar__plus_01_dot_45 KeyChainType)
        (objectType Window_bar__plus_00_dot_17_bar__plus_01_dot_02_bar__plus_02_dot_44 WindowType)
        (objectType Television_bar__minus_05_dot_76_bar__plus_01_dot_21_bar__plus_02_dot_39 TelevisionType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType PencilType)
        (canContain GarbageCanType TissueBoxType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType BookType)
        (canContain ArmChairType CellPhoneType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType BookType)
        (canContain SofaType CellPhoneType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType CreditCardType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType TissueBoxType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType TissueBoxType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType TissueBoxType)
        (canContain DrawerType WateringCanType)
        (canContain CoffeeTableType PenType)
        (canContain CoffeeTableType BookType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType CellPhoneType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType PencilType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType TissueBoxType)
        (canContain CoffeeTableType StatueType)
        (canContain CoffeeTableType WateringCanType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType BoxType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType TissueBoxType)
        (canContain SideTableType StatueType)
        (canContain SideTableType WateringCanType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType BoxType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType TissueBoxType)
        (canContain SideTableType StatueType)
        (canContain SideTableType WateringCanType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType TissueBoxType)
        (canContain DrawerType WateringCanType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType BoxType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType TissueBoxType)
        (canContain SideTableType StatueType)
        (canContain SideTableType WateringCanType)
        (canContain DresserType PenType)
        (canContain DresserType BookType)
        (canContain DresserType BoxType)
        (canContain DresserType CellPhoneType)
        (canContain DresserType KeyChainType)
        (canContain DresserType CreditCardType)
        (canContain DresserType LaptopType)
        (canContain DresserType PencilType)
        (canContain DresserType RemoteControlType)
        (canContain DresserType TissueBoxType)
        (canContain DresserType StatueType)
        (canContain DresserType WateringCanType)
        (pickupable Pencil_bar__minus_05_dot_72_bar__plus_00_dot_73_bar__plus_00_dot_24)
        (pickupable RemoteControl_bar__minus_00_dot_01_bar__plus_00_dot_77_bar__plus_01_dot_66)
        (pickupable Pen_bar__minus_03_dot_19_bar__plus_00_dot_42_bar__plus_02_dot_05)
        (pickupable CreditCard_bar__minus_02_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_60)
        (pickupable Box_bar__minus_03_dot_43_bar__plus_00_dot_64_bar__plus_02_dot_44)
        (pickupable Pencil_bar__minus_00_dot_29_bar__plus_00_dot_78_bar__plus_00_dot_31)
        (pickupable TissueBox_bar__minus_03_dot_48_bar__plus_00_dot_42_bar__plus_02_dot_63)
        (pickupable CreditCard_bar__minus_02_dot_90_bar__plus_00_dot_41_bar__plus_02_dot_15)
        (pickupable Statue_bar__minus_02_dot_75_bar__plus_00_dot_42_bar__plus_02_dot_34)
        (pickupable TissueBox_bar__minus_00_dot_34_bar__plus_00_dot_77_bar__plus_01_dot_31)
        (pickupable RemoteControl_bar__minus_05_dot_70_bar__plus_00_dot_73_bar__plus_02_dot_25)
        (pickupable Pillow_bar__minus_02_dot_15_bar__plus_00_dot_62_bar__plus_00_dot_59)
        (pickupable KeyChain_bar__minus_00_dot_16_bar__plus_00_dot_77_bar__plus_01_dot_02)
        (pickupable Laptop_bar__minus_00_dot_33_bar__plus_00_dot_77_bar__plus_00_dot_72)
        (pickupable Statue_bar__minus_00_dot_14_bar__plus_00_dot_78_bar__plus_01_dot_59)
        (pickupable CellPhone_bar__minus_05_dot_74_bar__plus_00_dot_73_bar__plus_02_dot_55)
        (pickupable Statue_bar__minus_02_dot_75_bar__plus_00_dot_42_bar__plus_02_dot_63)
        (pickupable KeyChain_bar__minus_03_dot_43_bar__plus_00_dot_43_bar__plus_00_dot_60)
        (pickupable Book_bar__minus_01_dot_48_bar__plus_00_dot_40_bar__plus_03_dot_61)
        (pickupable Pen_bar__minus_05_dot_74_bar__plus_00_dot_73_bar__plus_02_dot_40)
        (pickupable Pencil_bar__minus_05_dot_72_bar__plus_00_dot_73_bar__plus_00_dot_15)
        (pickupable Boots_bar__plus_00_dot_03_bar__plus_00_dot_02_bar__plus_03_dot_09)
        (pickupable WateringCan_bar__minus_05_dot_24_bar__plus_00_dot_02_bar__plus_00_dot_14)
        (pickupable Pencil_bar__minus_00_dot_34_bar__plus_00_dot_77_bar__plus_01_dot_59)
        (pickupable TissueBox_bar__minus_03_dot_04_bar__plus_00_dot_42_bar__plus_02_dot_63)
        (pickupable KeyChain_bar__minus_00_dot_07_bar__plus_00_dot_77_bar__plus_01_dot_45)
        (isReceptacleObject Box_bar__minus_03_dot_43_bar__plus_00_dot_64_bar__plus_02_dot_44)
        (openable Drawer_bar__minus_05_dot_58_bar__plus_00_dot_21_bar__plus_02_dot_68)
        (openable Drawer_bar__minus_05_dot_58_bar__plus_00_dot_21_bar__plus_02_dot_13)
        (openable Drawer_bar__minus_05_dot_58_bar__plus_00_dot_52_bar__plus_02_dot_68)
        (openable Drawer_bar__minus_05_dot_58_bar__plus_00_dot_52_bar__plus_02_dot_13)
        
        (atLocation agent1 loc_bar__minus_14_bar_7_bar_2_bar_30)
        
        
        
        
        
        
        
        (toggleable FloorLamp_bar__minus_00_dot_20_bar__plus_00_dot_02_bar__plus_04_dot_49)
        
        
        
        
        (inReceptacle Pen_bar__minus_05_dot_74_bar__plus_00_dot_73_bar__plus_02_dot_40 Dresser_bar__minus_05_dot_77_bar__plus_00_dot_02_bar__plus_02_dot_40)
        (inReceptacle CellPhone_bar__minus_05_dot_74_bar__plus_00_dot_73_bar__plus_02_dot_55 Dresser_bar__minus_05_dot_77_bar__plus_00_dot_02_bar__plus_02_dot_40)
        (inReceptacle Television_bar__minus_05_dot_76_bar__plus_01_dot_21_bar__plus_02_dot_39 Dresser_bar__minus_05_dot_77_bar__plus_00_dot_02_bar__plus_02_dot_40)
        (inReceptacle RemoteControl_bar__minus_05_dot_70_bar__plus_00_dot_73_bar__plus_02_dot_25 Dresser_bar__minus_05_dot_77_bar__plus_00_dot_02_bar__plus_02_dot_40)
        (inReceptacle Book_bar__minus_01_dot_48_bar__plus_00_dot_40_bar__plus_03_dot_61 ArmChair_bar__minus_01_dot_32_bar__plus_00_dot_00_bar__plus_03_dot_65)
        (inReceptacle RemoteControl_bar__minus_00_dot_01_bar__plus_00_dot_77_bar__plus_01_dot_66 SideTable_bar__minus_00_dot_21_bar__plus_00_dot_02_bar__plus_01_dot_52)
        (inReceptacle Pencil_bar__minus_00_dot_34_bar__plus_00_dot_77_bar__plus_01_dot_59 SideTable_bar__minus_00_dot_21_bar__plus_00_dot_02_bar__plus_01_dot_52)
        (inReceptacle KeyChain_bar__minus_00_dot_07_bar__plus_00_dot_77_bar__plus_01_dot_45 SideTable_bar__minus_00_dot_21_bar__plus_00_dot_02_bar__plus_01_dot_52)
        (inReceptacle TissueBox_bar__minus_00_dot_34_bar__plus_00_dot_77_bar__plus_01_dot_31 SideTable_bar__minus_00_dot_21_bar__plus_00_dot_02_bar__plus_01_dot_52)
        (inReceptacle Statue_bar__minus_00_dot_14_bar__plus_00_dot_78_bar__plus_01_dot_59 SideTable_bar__minus_00_dot_21_bar__plus_00_dot_02_bar__plus_01_dot_52)
        (inReceptacle Statue_bar__minus_02_dot_75_bar__plus_00_dot_42_bar__plus_02_dot_63 CoffeeTable_bar__minus_03_dot_04_bar__plus_00_dot_03_bar__plus_02_dot_34)
        (inReceptacle TissueBox_bar__minus_03_dot_04_bar__plus_00_dot_42_bar__plus_02_dot_63 CoffeeTable_bar__minus_03_dot_04_bar__plus_00_dot_03_bar__plus_02_dot_34)
        (inReceptacle Pen_bar__minus_03_dot_19_bar__plus_00_dot_42_bar__plus_02_dot_05 CoffeeTable_bar__minus_03_dot_04_bar__plus_00_dot_03_bar__plus_02_dot_34)
        (inReceptacle Statue_bar__minus_02_dot_75_bar__plus_00_dot_42_bar__plus_02_dot_34 CoffeeTable_bar__minus_03_dot_04_bar__plus_00_dot_03_bar__plus_02_dot_34)
        (inReceptacle Box_bar__minus_03_dot_43_bar__plus_00_dot_64_bar__plus_02_dot_44 CoffeeTable_bar__minus_03_dot_04_bar__plus_00_dot_03_bar__plus_02_dot_34)
        (inReceptacle TissueBox_bar__minus_03_dot_48_bar__plus_00_dot_42_bar__plus_02_dot_63 CoffeeTable_bar__minus_03_dot_04_bar__plus_00_dot_03_bar__plus_02_dot_34)
        (inReceptacle CreditCard_bar__minus_02_dot_90_bar__plus_00_dot_41_bar__plus_02_dot_15 CoffeeTable_bar__minus_03_dot_04_bar__plus_00_dot_03_bar__plus_02_dot_34)
        (inReceptacle Pencil_bar__minus_05_dot_72_bar__plus_00_dot_73_bar__plus_00_dot_15 SideTable_bar__minus_05_dot_57_bar__plus_00_dot_02_bar__plus_00_dot_29)
        (inReceptacle Pencil_bar__minus_05_dot_72_bar__plus_00_dot_73_bar__plus_00_dot_24 SideTable_bar__minus_05_dot_57_bar__plus_00_dot_02_bar__plus_00_dot_29)
        (inReceptacle HousePlant_bar__minus_05_dot_54_bar__plus_00_dot_72_bar__plus_00_dot_30 SideTable_bar__minus_05_dot_57_bar__plus_00_dot_02_bar__plus_00_dot_29)
        (inReceptacle Pillow_bar__minus_02_dot_15_bar__plus_00_dot_62_bar__plus_00_dot_59 Sofa_bar__minus_03_dot_13_bar__plus_00_dot_02_bar__plus_00_dot_41)
        (inReceptacle CreditCard_bar__minus_02_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_60 Sofa_bar__minus_03_dot_13_bar__plus_00_dot_02_bar__plus_00_dot_41)
        (inReceptacle KeyChain_bar__minus_03_dot_43_bar__plus_00_dot_43_bar__plus_00_dot_60 Sofa_bar__minus_03_dot_13_bar__plus_00_dot_02_bar__plus_00_dot_41)
        (inReceptacle KeyChain_bar__minus_00_dot_16_bar__plus_00_dot_77_bar__plus_01_dot_02 SideTable_bar__minus_00_dot_31_bar__plus_00_dot_02_bar__plus_00_dot_62)
        (inReceptacle Pencil_bar__minus_00_dot_29_bar__plus_00_dot_78_bar__plus_00_dot_31 SideTable_bar__minus_00_dot_31_bar__plus_00_dot_02_bar__plus_00_dot_62)
        (inReceptacle Laptop_bar__minus_00_dot_33_bar__plus_00_dot_77_bar__plus_00_dot_72 SideTable_bar__minus_00_dot_31_bar__plus_00_dot_02_bar__plus_00_dot_62)
        
        
        (receptacleAtLocation ArmChair_bar__minus_01_dot_32_bar__plus_00_dot_00_bar__plus_03_dot_65 loc_bar__minus_6_bar_11_bar_0_bar_60)
        (receptacleAtLocation CoffeeTable_bar__minus_03_dot_04_bar__plus_00_dot_03_bar__plus_02_dot_34 loc_bar__minus_12_bar_14_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__minus_05_dot_58_bar__plus_00_dot_21_bar__plus_02_dot_13 loc_bar__minus_20_bar_13_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__minus_05_dot_58_bar__plus_00_dot_21_bar__plus_02_dot_68 loc_bar__minus_20_bar_15_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__minus_05_dot_58_bar__plus_00_dot_52_bar__plus_02_dot_13 loc_bar__minus_21_bar_12_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__minus_05_dot_58_bar__plus_00_dot_52_bar__plus_02_dot_68 loc_bar__minus_21_bar_14_bar_2_bar_60)
        (receptacleAtLocation Dresser_bar__minus_05_dot_77_bar__plus_00_dot_02_bar__plus_02_dot_40 loc_bar__minus_20_bar_10_bar_3_bar_60)
        (receptacleAtLocation GarbageCan_bar__minus_05_dot_82_bar__plus_00_dot_02_bar__plus_04_dot_66 loc_bar__minus_22_bar_17_bar_0_bar_60)
        (receptacleAtLocation SideTable_bar__minus_00_dot_21_bar__plus_00_dot_02_bar__plus_01_dot_52 loc_bar__minus_4_bar_7_bar_1_bar_60)
        (receptacleAtLocation SideTable_bar__minus_00_dot_31_bar__plus_00_dot_02_bar__plus_00_dot_62 loc_bar__minus_5_bar_2_bar_1_bar_60)
        (receptacleAtLocation SideTable_bar__minus_05_dot_57_bar__plus_00_dot_02_bar__plus_00_dot_29 loc_bar__minus_21_bar_5_bar_2_bar_60)
        (receptacleAtLocation Sofa_bar__minus_03_dot_13_bar__plus_00_dot_02_bar__plus_00_dot_41 loc_bar__minus_14_bar_6_bar_2_bar_60)
        (objectAtLocation Statue_bar__minus_02_dot_75_bar__plus_00_dot_42_bar__plus_02_dot_34 loc_bar__minus_12_bar_14_bar_2_bar_60)
        (objectAtLocation Pencil_bar__minus_00_dot_29_bar__plus_00_dot_78_bar__plus_00_dot_31 loc_bar__minus_5_bar_2_bar_1_bar_60)
        (objectAtLocation Pen_bar__minus_03_dot_19_bar__plus_00_dot_42_bar__plus_02_dot_05 loc_bar__minus_12_bar_14_bar_2_bar_60)
        (objectAtLocation RemoteControl_bar__minus_05_dot_70_bar__plus_00_dot_73_bar__plus_02_dot_25 loc_bar__minus_20_bar_10_bar_3_bar_60)
        (objectAtLocation CreditCard_bar__minus_02_dot_90_bar__plus_00_dot_41_bar__plus_02_dot_15 loc_bar__minus_12_bar_14_bar_2_bar_60)
        (objectAtLocation TissueBox_bar__minus_03_dot_48_bar__plus_00_dot_42_bar__plus_02_dot_63 loc_bar__minus_12_bar_14_bar_2_bar_60)
        (objectAtLocation KeyChain_bar__minus_00_dot_16_bar__plus_00_dot_77_bar__plus_01_dot_02 loc_bar__minus_5_bar_2_bar_1_bar_60)
        (objectAtLocation KeyChain_bar__minus_00_dot_07_bar__plus_00_dot_77_bar__plus_01_dot_45 loc_bar__minus_4_bar_7_bar_1_bar_60)
        (objectAtLocation Pencil_bar__minus_00_dot_34_bar__plus_00_dot_77_bar__plus_01_dot_59 loc_bar__minus_4_bar_7_bar_1_bar_60)
        (objectAtLocation Pencil_bar__minus_05_dot_72_bar__plus_00_dot_73_bar__plus_00_dot_24 loc_bar__minus_21_bar_5_bar_2_bar_60)
        (objectAtLocation TissueBox_bar__minus_03_dot_04_bar__plus_00_dot_42_bar__plus_02_dot_63 loc_bar__minus_12_bar_14_bar_2_bar_60)
        (objectAtLocation Chair_bar__minus_02_dot_16_bar__plus_00_dot_02_bar__plus_03_dot_88 loc_bar__minus_11_bar_16_bar_1_bar_60)
        (objectAtLocation Chair_bar__minus_00_dot_73_bar__plus_00_dot_02_bar__plus_00_dot_50 loc_bar__minus_5_bar_2_bar_1_bar_60)
        (objectAtLocation Book_bar__minus_01_dot_48_bar__plus_00_dot_40_bar__plus_03_dot_61 loc_bar__minus_6_bar_11_bar_0_bar_60)
        (objectAtLocation Chair_bar__minus_04_dot_01_bar__plus_00_dot_02_bar__plus_03_dot_90 loc_bar__minus_17_bar_17_bar_2_bar_60)
        (objectAtLocation Box_bar__minus_03_dot_43_bar__plus_00_dot_64_bar__plus_02_dot_44 loc_bar__minus_12_bar_14_bar_2_bar_60)
        (objectAtLocation KeyChain_bar__minus_03_dot_43_bar__plus_00_dot_43_bar__plus_00_dot_60 loc_bar__minus_14_bar_6_bar_2_bar_60)
        (objectAtLocation Pillow_bar__minus_02_dot_15_bar__plus_00_dot_62_bar__plus_00_dot_59 loc_bar__minus_14_bar_6_bar_2_bar_60)
        (objectAtLocation FloorLamp_bar__minus_00_dot_20_bar__plus_00_dot_02_bar__plus_04_dot_49 loc_bar__minus_1_bar_15_bar_0_bar_60)
        (objectAtLocation WateringCan_bar__minus_05_dot_24_bar__plus_00_dot_02_bar__plus_00_dot_14 loc_bar__minus_19_bar_2_bar_3_bar_60)
        (objectAtLocation TissueBox_bar__minus_00_dot_34_bar__plus_00_dot_77_bar__plus_01_dot_31 loc_bar__minus_4_bar_7_bar_1_bar_60)
        (objectAtLocation HousePlant_bar__minus_05_dot_54_bar__plus_00_dot_72_bar__plus_00_dot_30 loc_bar__minus_21_bar_5_bar_2_bar_60)
        (objectAtLocation CreditCard_bar__minus_02_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_60 loc_bar__minus_14_bar_6_bar_2_bar_60)
        (objectAtLocation RemoteControl_bar__minus_00_dot_01_bar__plus_00_dot_77_bar__plus_01_dot_66 loc_bar__minus_4_bar_7_bar_1_bar_60)
        (objectAtLocation Boots_bar__plus_00_dot_03_bar__plus_00_dot_02_bar__plus_03_dot_09 loc_bar__minus_1_bar_13_bar_1_bar_60)
        (objectAtLocation LightSwitch_bar__minus_02_dot_50_bar__plus_01_dot_53_bar__plus_04_dot_85 loc_bar__minus_11_bar_17_bar_0_bar_15)
        (objectAtLocation Window_bar__plus_00_dot_13_bar__plus_02_dot_38_bar__plus_02_dot_42 loc_bar__minus_2_bar_10_bar_1_bar__minus_30)
        (objectAtLocation Painting_bar__minus_00_dot_05_bar__plus_01_dot_22_bar__plus_00_dot_46 loc_bar__minus_5_bar_2_bar_1_bar_15)
        (objectAtLocation Painting_bar__minus_00_dot_05_bar__plus_01_dot_87_bar__plus_00_dot_50 loc_bar__minus_5_bar_2_bar_1_bar__minus_15)
        (objectAtLocation CellPhone_bar__minus_05_dot_74_bar__plus_00_dot_73_bar__plus_02_dot_55 loc_bar__minus_20_bar_10_bar_3_bar_60)
        (objectAtLocation Pen_bar__minus_05_dot_74_bar__plus_00_dot_73_bar__plus_02_dot_40 loc_bar__minus_20_bar_10_bar_3_bar_60)
        (objectAtLocation Television_bar__minus_05_dot_76_bar__plus_01_dot_21_bar__plus_02_dot_39 loc_bar__minus_20_bar_10_bar_3_bar_60)
        (objectAtLocation Laptop_bar__minus_00_dot_33_bar__plus_00_dot_77_bar__plus_00_dot_72 loc_bar__minus_5_bar_2_bar_1_bar_60)
        (objectAtLocation Window_bar__plus_00_dot_17_bar__plus_01_dot_74_bar__plus_03_dot_51 loc_bar__minus_2_bar_14_bar_1_bar_0)
        (objectAtLocation Window_bar__plus_00_dot_17_bar__plus_01_dot_74_bar__plus_01_dot_38 loc_bar__minus_3_bar_6_bar_1_bar_0)
        (objectAtLocation Window_bar__plus_00_dot_17_bar__plus_01_dot_02_bar__plus_02_dot_44 loc_bar__minus_2_bar_10_bar_1_bar_45)
        (objectAtLocation Painting_bar__minus_01_dot_00_bar__plus_01_dot_70_bar__plus_00_dot_04 loc_bar__minus_5_bar_2_bar_2_bar_0)
        (objectAtLocation Painting_bar__minus_03_dot_31_bar__plus_01_dot_67_bar__plus_00_dot_04 loc_bar__minus_13_bar_5_bar_2_bar_0)
        (objectAtLocation Pencil_bar__minus_05_dot_72_bar__plus_00_dot_73_bar__plus_00_dot_15 loc_bar__minus_21_bar_5_bar_2_bar_60)
        (objectAtLocation Statue_bar__minus_00_dot_14_bar__plus_00_dot_78_bar__plus_01_dot_59 loc_bar__minus_4_bar_7_bar_1_bar_60)
        (objectAtLocation Statue_bar__minus_02_dot_75_bar__plus_00_dot_42_bar__plus_02_dot_63 loc_bar__minus_12_bar_14_bar_2_bar_60)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 PencilType)
                                    (receptacleType ?r CoffeeTableType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 PencilType)
                                            (receptacleType ?r CoffeeTableType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            