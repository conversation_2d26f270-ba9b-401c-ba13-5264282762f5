{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000209.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000210.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000211.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000212.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000284.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000285.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000286.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000287.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000288.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000289.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000290.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000291.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000292.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000293.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000294.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000295.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000296.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000297.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000298.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000299.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000300.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000301.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000302.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000303.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000304.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000305.png", "low_idx": 41}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "ToiletPaper", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["toiletpaperhanger"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-1|8|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [1.235199688, 1.235199688, 7.64920044, 7.64920044, 2.4624, 2.4624]], "coordinateReceptacleObjectId": ["ToiletPaperHanger", [1.5076, 1.5076, 8.038, 8.038, 2.5956, 2.5956]], "forceVisible": true, "objectId": "ToiletPaper|+00.31|+00.62|+01.91"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-3|8|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [1.235199688, 1.235199688, 7.64920044, 7.64920044, 2.4624, 2.4624]], "coordinateReceptacleObjectId": ["Drawer", [1.773921372, 1.773921372, 10.2648, 10.2648, 1.2936, 1.2936]], "forceVisible": true, "objectId": "ToiletPaper|+00.31|+00.62|+01.91", "receptacleObjectId": "Drawer|+00.44|+00.32|+02.57"}}, {"discrete_action": {"action": "GotoLocation", "args": ["toilet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-2|7|1|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [2.498536, 2.498536, 6.656631, 6.656631, 4.16745376, 4.16745376]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON>", [1.084, 1.084, 5.856, 5.856, 0.000664353372, 0.000664353372]], "forceVisible": true, "objectId": "ToiletPaper|+00.62|+01.04|+01.66"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-3|8|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [2.498536, 2.498536, 6.656631, 6.656631, 4.16745376, 4.16745376]], "coordinateReceptacleObjectId": ["Drawer", [1.773921372, 1.773921372, 10.2648, 10.2648, 1.2936, 1.2936]], "forceVisible": true, "objectId": "ToiletPaper|+00.62|+01.04|+01.66", "receptacleObjectId": "Drawer|+00.44|+00.32|+02.57"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|+00.31|+00.62|+01.91"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [158, 120, 182, 154], "mask": [[35869, 4], [36164, 12], [36462, 17], [36760, 19], [37059, 21], [37359, 22], [37658, 23], [37958, 23], [38258, 23], [38558, 23], [38858, 23], [39158, 23], [39458, 23], [39758, 24], [40058, 24], [40358, 24], [40658, 24], [40958, 24], [41258, 24], [41558, 24], [41858, 24], [42158, 24], [42458, 24], [42758, 24], [43058, 24], [43358, 24], [43658, 25], [43958, 25], [44258, 11], [44273, 10], [44558, 10], [44573, 10], [44858, 10], [44873, 9], [45159, 9], [45173, 8], [45473, 8], [45774, 5], [46074, 2]], "point": [170, 136]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|+00.44|+00.32|+02.57"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 170, 89, 201], "mask": [[50781, 1], [51015, 67], [51315, 67], [51616, 66], [51917, 66], [52217, 66], [52518, 65], [52818, 66], [53119, 65], [53419, 65], [53720, 64], [54020, 65], [54321, 64], [54622, 63], [54922, 64], [55223, 63], [55523, 63], [55824, 62], [56124, 63], [56425, 62], [56726, 61], [57026, 62], [57327, 61], [57627, 61], [57928, 60], [58228, 61], [58529, 60], [58830, 59], [59130, 60], [59431, 59], [59731, 59], [60032, 58]], "point": [52, 184]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|+00.31|+00.62|+01.91", "placeStationary": true, "receptacleObjectId": "Drawer|+00.44|+00.32|+02.57"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 171, 87, 266], "mask": [[51020, 62], [51319, 63], [51619, 63], [51918, 65], [52218, 65], [52517, 66], [52817, 66], [53117, 67], [53416, 68], [53716, 68], [54015, 70], [54315, 70], [54614, 71], [54914, 71], [55213, 73], [55513, 73], [55812, 74], [56112, 75], [56411, 76], [56711, 76], [57011, 76], [57310, 78], [57610, 78], [57909, 79], [58209, 79], [58508, 80], [58808, 80], [59107, 80], [59407, 80], [59706, 81], [60006, 81], [60305, 82], [60605, 81], [60904, 82], [61204, 82], [61504, 82], [61803, 83], [62103, 82], [62402, 83], [62702, 83], [63001, 84], [63301, 84], [63600, 84], [63900, 84], [64200, 84], [64500, 84], [64800, 84], [65100, 83], [65400, 83], [65700, 83], [66000, 83], [66300, 83], [66600, 82], [66900, 82], [67200, 82], [67500, 82], [67800, 82], [68100, 81], [68400, 81], [68700, 81], [69000, 81], [69300, 80], [69600, 80], [69900, 80], [70200, 80], [70500, 80], [70800, 79], [71100, 79], [71400, 79], [71700, 79], [72000, 79], [72300, 78], [72600, 78], [72900, 78], [73200, 78], [73500, 78], [73800, 77], [74100, 77], [74400, 77], [74700, 77], [75000, 77], [75300, 76], [75600, 76], [75900, 76], [76200, 76], [76500, 76], [76800, 76], [77100, 76], [77401, 76], [77702, 75], [78003, 74], [78304, 74], [78605, 73], [78906, 72], [79207, 70], [79508, 69]], "point": [43, 217]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|+00.44|+00.32|+02.57"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 171, 87, 266], "mask": [[51020, 62], [51319, 63], [51619, 63], [51918, 65], [52218, 65], [52517, 66], [52817, 66], [53117, 24], [53148, 36], [53416, 23], [53449, 35], [53716, 21], [53750, 34], [54015, 21], [54051, 34], [54315, 20], [54352, 33], [54614, 20], [54652, 33], [54914, 20], [54952, 33], [55213, 21], [55253, 33], [55513, 21], [55553, 33], [55812, 21], [55854, 32], [56112, 22], [56154, 33], [56411, 23], [56455, 32], [56711, 24], [56755, 32], [57011, 24], [57055, 32], [57310, 26], [57356, 32], [57610, 26], [57656, 32], [57909, 28], [57956, 32], [58209, 28], [58256, 32], [58508, 30], [58556, 32], [58808, 30], [58855, 33], [59107, 32], [59154, 33], [59407, 32], [59453, 34], [59706, 34], [59752, 35], [60006, 35], [60050, 37], [60305, 40], [60348, 39], [60605, 81], [60904, 82], [61204, 82], [61504, 82], [61803, 83], [62103, 82], [62402, 83], [62702, 83], [63001, 84], [63301, 84], [63600, 84], [63900, 84], [64200, 84], [64500, 84], [64800, 84], [65100, 83], [65400, 83], [65700, 83], [66000, 83], [66300, 83], [66600, 82], [66900, 82], [67200, 82], [67500, 82], [67800, 82], [68100, 81], [68400, 81], [68700, 81], [69000, 81], [69300, 80], [69600, 80], [69900, 80], [70200, 80], [70500, 80], [70800, 79], [71100, 79], [71400, 79], [71700, 79], [72000, 79], [72300, 78], [72600, 78], [72900, 78], [73200, 78], [73500, 78], [73800, 77], [74100, 77], [74400, 77], [74700, 77], [75000, 77], [75300, 76], [75600, 76], [75900, 76], [76200, 76], [76500, 76], [76800, 76], [77100, 76], [77401, 76], [77702, 75], [78003, 74], [78304, 74], [78605, 73], [78906, 72], [79207, 70], [79508, 69]], "point": [43, 217]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|+00.62|+01.04|+01.66"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [164, 105, 171, 131], "mask": [[31367, 3], [31665, 7], [31965, 7], [32265, 7], [32565, 7], [32865, 7], [33165, 7], [33465, 7], [33765, 7], [34065, 7], [34365, 7], [34665, 7], [34965, 7], [35265, 7], [35565, 7], [35865, 7], [36165, 7], [36465, 7], [36765, 7], [37065, 7], [37365, 7], [37664, 8], [37964, 8], [38264, 8], [38564, 7], [38864, 7], [39165, 6]], "point": [167, 117]}}, "high_idx": 5}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|+00.44|+00.32|+02.57"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 170, 89, 201], "mask": [[50781, 1], [51015, 67], [51315, 67], [51616, 66], [51917, 66], [52217, 66], [52518, 65], [52818, 66], [53119, 65], [53419, 65], [53720, 64], [54020, 65], [54321, 64], [54622, 63], [54922, 64], [55223, 63], [55523, 63], [55824, 62], [56124, 63], [56425, 62], [56726, 61], [57026, 62], [57327, 61], [57627, 61], [57928, 60], [58228, 61], [58529, 60], [58830, 59], [59130, 60], [59431, 59], [59731, 59], [60032, 58]], "point": [52, 184]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|+00.62|+01.04|+01.66", "placeStationary": true, "receptacleObjectId": "Drawer|+00.44|+00.32|+02.57"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 171, 87, 266], "mask": [[51020, 62], [51319, 63], [51619, 63], [51918, 65], [52218, 65], [52517, 66], [52817, 66], [53117, 24], [53148, 36], [53416, 23], [53449, 35], [53716, 21], [53750, 34], [54015, 21], [54051, 34], [54315, 20], [54352, 33], [54614, 20], [54652, 33], [54914, 20], [54952, 33], [55213, 21], [55253, 33], [55513, 21], [55553, 33], [55812, 21], [55854, 32], [56112, 22], [56154, 33], [56411, 23], [56455, 32], [56711, 24], [56755, 32], [57011, 24], [57055, 32], [57310, 26], [57356, 32], [57610, 26], [57656, 32], [57909, 28], [57956, 32], [58209, 28], [58256, 32], [58508, 30], [58556, 32], [58808, 30], [58855, 33], [59107, 32], [59154, 33], [59407, 32], [59453, 34], [59706, 34], [59752, 35], [60006, 35], [60050, 37], [60305, 40], [60348, 39], [60605, 81], [60904, 82], [61204, 82], [61504, 82], [61803, 83], [62103, 82], [62402, 83], [62702, 83], [63001, 84], [63301, 84], [63600, 84], [63900, 84], [64200, 84], [64500, 84], [64800, 84], [65100, 83], [65400, 83], [65700, 83], [66000, 83], [66300, 83], [66600, 82], [66900, 82], [67200, 82], [67500, 82], [67800, 82], [68100, 81], [68400, 81], [68700, 81], [69000, 81], [69300, 80], [69600, 80], [69900, 80], [70200, 80], [70500, 80], [70800, 79], [71100, 79], [71400, 79], [71700, 79], [72000, 79], [72300, 78], [72600, 78], [72900, 78], [73200, 78], [73500, 78], [73800, 77], [74100, 77], [74400, 77], [74700, 77], [75000, 77], [75300, 76], [75600, 76], [75900, 76], [76200, 76], [76500, 76], [76800, 76], [77100, 76], [77401, 76], [77702, 75], [78003, 74], [78304, 74], [78605, 73], [78906, 72], [79207, 70], [79508, 69]], "point": [43, 217]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|+00.44|+00.32|+02.57"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 171, 87, 266], "mask": [[51020, 62], [51319, 63], [51619, 63], [51918, 65], [52218, 65], [52517, 66], [52817, 66], [53117, 24], [53148, 36], [53416, 23], [53449, 35], [53716, 21], [53750, 34], [54015, 21], [54051, 34], [54315, 20], [54352, 9], [54364, 21], [54614, 20], [54652, 8], [54665, 20], [54914, 20], [54952, 7], [54966, 19], [55213, 21], [55253, 6], [55266, 20], [55513, 21], [55553, 7], [55566, 20], [55812, 21], [55854, 6], [55867, 19], [56112, 22], [56154, 7], [56167, 20], [56411, 23], [56455, 6], [56467, 20], [56711, 24], [56755, 6], [56768, 19], [57011, 24], [57055, 7], [57068, 19], [57310, 26], [57356, 6], [57369, 19], [57610, 26], [57656, 7], [57669, 19], [57909, 28], [57956, 7], [57969, 19], [58209, 28], [58256, 7], [58269, 19], [58508, 30], [58556, 8], [58569, 19], [58808, 30], [58855, 9], [58868, 20], [59107, 32], [59154, 33], [59407, 32], [59453, 34], [59706, 34], [59752, 35], [60006, 35], [60050, 37], [60305, 40], [60348, 39], [60605, 81], [60904, 82], [61204, 82], [61504, 82], [61803, 83], [62103, 82], [62402, 83], [62702, 83], [63001, 84], [63301, 84], [63600, 84], [63900, 84], [64200, 84], [64500, 84], [64800, 84], [65100, 83], [65400, 83], [65700, 83], [66000, 83], [66300, 83], [66600, 82], [66900, 82], [67200, 82], [67500, 82], [67800, 82], [68100, 81], [68400, 81], [68700, 81], [69000, 81], [69300, 80], [69600, 80], [69900, 80], [70200, 80], [70500, 80], [70800, 79], [71100, 79], [71400, 79], [71700, 79], [72000, 79], [72300, 78], [72600, 78], [72900, 78], [73200, 78], [73500, 78], [73800, 77], [74100, 77], [74400, 77], [74700, 77], [75000, 77], [75300, 76], [75600, 76], [75900, 76], [76200, 76], [76500, 76], [76800, 76], [77100, 76], [77401, 76], [77702, 75], [78003, 74], [78304, 74], [78605, 73], [78906, 72], [79207, 70], [79508, 69]], "point": [43, 217]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan409", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -1.25, "y": 0.9011651, "z": 3.5}, "object_poses": [{"objectName": "Cloth_0a396f26", "position": {"x": 0.5026288, "y": 0.517042637, "z": 3.35225534}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cloth_0a396f26", "position": {"x": 0.4465894, "y": 0.216620177, "z": 2.52787924}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Candle_0bc9cce6", "position": {"x": 0.521930158, "y": 0.821159, "z": 3.23942661}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_d5e13b2f", "position": {"x": 0.323645264, "y": 0.8209466, "z": 3.12973285}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_d5e13b2f", "position": {"x": 0.521930158, "y": 0.8209466, "z": 3.45881438}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Towel_9ca2fbae", "position": {"x": 0.6096284, "y": 1.376, "z": 0.583}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_bfb43855", "position": {"x": 0.521930158, "y": 0.827141464, "z": 2.10657978}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "HandTowel_3326814a", "position": {"x": 0.634889, "y": 1.512, "z": 1.8920002}, "rotation": {"x": 0.0, "y": 90.00026, "z": 0.0}}, {"objectName": "Towel_9ca2fbae", "position": {"x": -0.4423, "y": 1.3751, "z": 1.282367}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBar_abaf7d3b", "position": {"x": 0.389740229, "y": 0.819612, "z": 2.38081431}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_5db3475e", "position": {"x": 0.308799922, "y": 0.6156, "z": 1.91230011}, "rotation": {"x": 0.0, "y": 180.000015, "z": 90.0}}, {"objectName": "ToiletPaper_321c92a6", "position": {"x": 0.624634, "y": 1.04186344, "z": 1.66415775}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Candle_0bc9cce6", "position": {"x": 0.6552665, "y": 1.04648, "z": 1.33629847}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "ScrubBrush_b98b86ba", "position": {"x": 0.5530571, "y": 0.000166088343, "z": 1.725937}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cloth_0a396f26", "position": {"x": 0.558668137, "y": 0.215942591, "z": 2.979755}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plunger_17d994b3", "position": {"x": 0.5940484, "y": -0.0002968982, "z": 1.89854741}, "rotation": {"x": -0.00118541578, "y": 0.0004420988, "z": 0.0007844724}}, {"objectName": "SprayBottle_d5e13b2f", "position": {"x": 0.563366234, "y": 1.04626751, "z": 1.3362987}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}], "object_toggles": [], "random_seed": 2623366820, "scene_num": 409}, "task_id": "trial_T20190906_193444_042317", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "AT3C00TKZK13L_3GNCZX450L4O8XUEXSI6NYAAUWEAPL", "high_descs": ["Walk right, turn left and walk to the counter", "Take a toilet paper roll from the holder", "Walk and turn to the right then to the left and back to the counter", "Put the paper roll inside the counter drawer", "Walk closer to the toilet ", "Take an empty toilet paper roll from the top of the toilet", "Walk and turn right and left and back to the counter", "Set the empty toilet paper roll and set it inside the counter drawer"], "task_desc": "Grab the toilet paper roll, set it inside the drawer, take the empty roll and put it inside the drawer", "votes": [1, 1, 1]}, {"assignment_id": "A1RLO9LNUJIW5S_3NGI5ARFTWMSE9HE19PDCXH7Q6W1PK", "high_descs": ["Turn around and walk almost to the glass wall but stop short and turn left and walk to the sink counter to the right of the sink.", "Take the toilet paper of the toilet paper holder.", "Back up a step and face the same way.", "Open the second drawer to the left on the bottom row and put the toilet paper in it and close it.", "Turn right take a step then turn left.", "Take the tube off of the back of the toilet.", "Go back to where you put the roll of toilet paper.", "Put the tube to the right of the roll of toilet paper in a  drawer."], "task_desc": "Put a tube next to a roll of toilet paper in a drawer.", "votes": [1, 1, 1]}, {"assignment_id": "A1ELPYAFO7MANS_3OONKJ5DKF0B67N5O65NT9PQ29AOBK", "high_descs": ["Turn around and walk to the toilet.", "Take the toilet paper off the toilet paper holder.", "Take a step left.", "Put the toilet paper roll in the bottom middle cabinet.", "Step right to the toilet.", "Take the empty toilet paper roll off the top of the toilet tank.", "Take a step left to the same cabinet.", "Put the roll to the left of the other roll."], "task_desc": "Place two toilet paper rolls in a cabinet.", "votes": [1, 0, 1]}]}}