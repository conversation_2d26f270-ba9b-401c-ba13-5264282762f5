{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000091.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000092.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000093.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000094.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000095.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000096.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000097.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000098.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000099.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000100.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000101.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000102.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000103.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000104.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000105.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000106.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000107.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000108.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000109.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000110.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000111.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000112.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000113.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000114.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000115.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000116.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000117.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000118.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000119.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000120.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000121.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000122.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000123.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000124.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000125.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000126.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000127.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000128.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000129.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000130.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000131.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000132.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000133.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000135.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000136.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000137.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000138.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000139.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000140.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000141.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000142.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000144.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000145.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000146.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000147.png", "low_idx": 30}, {"high_idx": 1, "image_name": "000000148.png", "low_idx": 30}, {"high_idx": 1, "image_name": "000000149.png", "low_idx": 30}, {"high_idx": 1, "image_name": "000000150.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000186.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000187.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000188.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000189.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000190.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000191.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000192.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000193.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000194.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000195.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000196.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000197.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000198.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000199.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000200.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000201.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000202.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000203.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000204.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000205.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000206.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000207.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000208.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000209.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000210.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000211.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000212.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000213.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000214.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000215.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000216.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000217.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000218.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000219.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000220.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000221.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000222.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000223.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000224.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000225.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000226.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000227.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000228.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000229.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000230.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000231.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000232.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000233.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000234.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000235.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000236.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000237.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000238.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000239.png", "low_idx": 48}, {"high_idx": 2, "image_name": "000000240.png", "low_idx": 48}, {"high_idx": 2, "image_name": "000000241.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000242.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000243.png", "low_idx": 50}, {"high_idx": 2, "image_name": "000000244.png", "low_idx": 50}, {"high_idx": 2, "image_name": "000000245.png", "low_idx": 51}, {"high_idx": 2, "image_name": "000000246.png", "low_idx": 51}, {"high_idx": 2, "image_name": "000000247.png", "low_idx": 52}, {"high_idx": 2, "image_name": "000000248.png", "low_idx": 52}, {"high_idx": 2, "image_name": "000000249.png", "low_idx": 53}, {"high_idx": 2, "image_name": "000000250.png", "low_idx": 53}, {"high_idx": 2, "image_name": "000000251.png", "low_idx": 54}, {"high_idx": 2, "image_name": "000000252.png", "low_idx": 54}, {"high_idx": 2, "image_name": "000000253.png", "low_idx": 55}, {"high_idx": 2, "image_name": "000000254.png", "low_idx": 55}, {"high_idx": 2, "image_name": "000000255.png", "low_idx": 56}, {"high_idx": 2, "image_name": "000000256.png", "low_idx": 56}, {"high_idx": 2, "image_name": "000000257.png", "low_idx": 57}, {"high_idx": 2, "image_name": "000000258.png", "low_idx": 57}, {"high_idx": 2, "image_name": "000000259.png", "low_idx": 58}, {"high_idx": 2, "image_name": "000000260.png", "low_idx": 58}, {"high_idx": 2, "image_name": "000000261.png", "low_idx": 58}, {"high_idx": 2, "image_name": "000000262.png", "low_idx": 58}, {"high_idx": 2, "image_name": "000000263.png", "low_idx": 58}, {"high_idx": 2, "image_name": "000000264.png", "low_idx": 58}, {"high_idx": 2, "image_name": "000000265.png", "low_idx": 58}, {"high_idx": 2, "image_name": "000000266.png", "low_idx": 58}, {"high_idx": 2, "image_name": "000000267.png", "low_idx": 58}, {"high_idx": 2, "image_name": "000000268.png", "low_idx": 58}, {"high_idx": 2, "image_name": "000000269.png", "low_idx": 58}, {"high_idx": 3, "image_name": "000000270.png", "low_idx": 59}, {"high_idx": 3, "image_name": "000000271.png", "low_idx": 59}, {"high_idx": 3, "image_name": "000000272.png", "low_idx": 59}, {"high_idx": 3, "image_name": "000000273.png", "low_idx": 59}, {"high_idx": 3, "image_name": "000000274.png", "low_idx": 60}, {"high_idx": 3, "image_name": "000000275.png", "low_idx": 60}, {"high_idx": 3, "image_name": "000000276.png", "low_idx": 60}, {"high_idx": 3, "image_name": "000000277.png", "low_idx": 60}, {"high_idx": 3, "image_name": "000000278.png", "low_idx": 60}, {"high_idx": 3, "image_name": "000000279.png", "low_idx": 60}, {"high_idx": 3, "image_name": "000000280.png", "low_idx": 60}, {"high_idx": 3, "image_name": "000000281.png", "low_idx": 60}, {"high_idx": 3, "image_name": "000000282.png", "low_idx": 60}, {"high_idx": 3, "image_name": "000000283.png", "low_idx": 60}, {"high_idx": 3, "image_name": "000000284.png", "low_idx": 60}, {"high_idx": 3, "image_name": "000000285.png", "low_idx": 60}, {"high_idx": 3, "image_name": "000000286.png", "low_idx": 60}, {"high_idx": 3, "image_name": "000000287.png", "low_idx": 60}, {"high_idx": 3, "image_name": "000000288.png", "low_idx": 60}, {"high_idx": 3, "image_name": "000000289.png", "low_idx": 61}, {"high_idx": 3, "image_name": "000000290.png", "low_idx": 61}, {"high_idx": 3, "image_name": "000000291.png", "low_idx": 61}, {"high_idx": 3, "image_name": "000000292.png", "low_idx": 61}, {"high_idx": 3, "image_name": "000000293.png", "low_idx": 62}, {"high_idx": 3, "image_name": "000000294.png", "low_idx": 62}, {"high_idx": 3, "image_name": "000000295.png", "low_idx": 62}, {"high_idx": 3, "image_name": "000000296.png", "low_idx": 62}, {"high_idx": 3, "image_name": "000000297.png", "low_idx": 62}, {"high_idx": 3, "image_name": "000000298.png", "low_idx": 62}, {"high_idx": 3, "image_name": "000000299.png", "low_idx": 62}, {"high_idx": 3, "image_name": "000000300.png", "low_idx": 62}, {"high_idx": 3, "image_name": "000000301.png", "low_idx": 62}, {"high_idx": 3, "image_name": "000000302.png", "low_idx": 62}, {"high_idx": 3, "image_name": "000000303.png", "low_idx": 62}, {"high_idx": 3, "image_name": "000000304.png", "low_idx": 62}, {"high_idx": 3, "image_name": "000000305.png", "low_idx": 62}, {"high_idx": 3, "image_name": "000000306.png", "low_idx": 62}, {"high_idx": 3, "image_name": "000000307.png", "low_idx": 62}, {"high_idx": 3, "image_name": "000000308.png", "low_idx": 62}, {"high_idx": 3, "image_name": "000000309.png", "low_idx": 62}, {"high_idx": 3, "image_name": "000000310.png", "low_idx": 62}, {"high_idx": 3, "image_name": "000000311.png", "low_idx": 63}, {"high_idx": 3, "image_name": "000000312.png", "low_idx": 63}, {"high_idx": 3, "image_name": "000000313.png", "low_idx": 63}, {"high_idx": 3, "image_name": "000000314.png", "low_idx": 63}, {"high_idx": 3, "image_name": "000000315.png", "low_idx": 63}, {"high_idx": 3, "image_name": "000000316.png", "low_idx": 63}, {"high_idx": 3, "image_name": "000000317.png", "low_idx": 64}, {"high_idx": 3, "image_name": "000000318.png", "low_idx": 64}, {"high_idx": 3, "image_name": "000000319.png", "low_idx": 64}, {"high_idx": 3, "image_name": "000000320.png", "low_idx": 64}, {"high_idx": 3, "image_name": "000000321.png", "low_idx": 65}, {"high_idx": 3, "image_name": "000000322.png", "low_idx": 65}, {"high_idx": 3, "image_name": "000000323.png", "low_idx": 65}, {"high_idx": 3, "image_name": "000000324.png", "low_idx": 65}, {"high_idx": 3, "image_name": "000000325.png", "low_idx": 65}, {"high_idx": 3, "image_name": "000000326.png", "low_idx": 65}, {"high_idx": 3, "image_name": "000000327.png", "low_idx": 65}, {"high_idx": 3, "image_name": "000000328.png", "low_idx": 65}, {"high_idx": 3, "image_name": "000000329.png", "low_idx": 65}, {"high_idx": 3, "image_name": "000000330.png", "low_idx": 65}, {"high_idx": 3, "image_name": "000000331.png", "low_idx": 65}, {"high_idx": 3, "image_name": "000000332.png", "low_idx": 65}, {"high_idx": 3, "image_name": "000000333.png", "low_idx": 65}, {"high_idx": 3, "image_name": "000000334.png", "low_idx": 65}, {"high_idx": 3, "image_name": "000000335.png", "low_idx": 65}, {"high_idx": 3, "image_name": "000000336.png", "low_idx": 66}, {"high_idx": 3, "image_name": "000000337.png", "low_idx": 66}, {"high_idx": 3, "image_name": "000000338.png", "low_idx": 66}, {"high_idx": 3, "image_name": "000000339.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000340.png", "low_idx": 67}, {"high_idx": 4, "image_name": "000000341.png", "low_idx": 67}, {"high_idx": 4, "image_name": "000000342.png", "low_idx": 67}, {"high_idx": 4, "image_name": "000000343.png", "low_idx": 67}, {"high_idx": 4, "image_name": "000000344.png", "low_idx": 67}, {"high_idx": 4, "image_name": "000000345.png", "low_idx": 67}, {"high_idx": 4, "image_name": "000000346.png", "low_idx": 67}, {"high_idx": 4, "image_name": "000000347.png", "low_idx": 67}, {"high_idx": 4, "image_name": "000000348.png", "low_idx": 67}, {"high_idx": 4, "image_name": "000000349.png", "low_idx": 67}, {"high_idx": 4, "image_name": "000000350.png", "low_idx": 67}, {"high_idx": 4, "image_name": "000000351.png", "low_idx": 68}, {"high_idx": 4, "image_name": "000000352.png", "low_idx": 68}, {"high_idx": 4, "image_name": "000000353.png", "low_idx": 68}, {"high_idx": 4, "image_name": "000000354.png", "low_idx": 68}, {"high_idx": 4, "image_name": "000000355.png", "low_idx": 68}, {"high_idx": 4, "image_name": "000000356.png", "low_idx": 68}, {"high_idx": 4, "image_name": "000000357.png", "low_idx": 68}, {"high_idx": 4, "image_name": "000000358.png", "low_idx": 68}, {"high_idx": 4, "image_name": "000000359.png", "low_idx": 68}, {"high_idx": 4, "image_name": "000000360.png", "low_idx": 68}, {"high_idx": 4, "image_name": "000000361.png", "low_idx": 68}, {"high_idx": 4, "image_name": "000000362.png", "low_idx": 69}, {"high_idx": 4, "image_name": "000000363.png", "low_idx": 69}, {"high_idx": 4, "image_name": "000000364.png", "low_idx": 70}, {"high_idx": 4, "image_name": "000000365.png", "low_idx": 70}, {"high_idx": 4, "image_name": "000000366.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000367.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000368.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000369.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000370.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000371.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000372.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000373.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000374.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000375.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000376.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000377.png", "low_idx": 72}, {"high_idx": 4, "image_name": "000000378.png", "low_idx": 72}, {"high_idx": 4, "image_name": "000000379.png", "low_idx": 73}, {"high_idx": 4, "image_name": "000000380.png", "low_idx": 73}, {"high_idx": 4, "image_name": "000000381.png", "low_idx": 74}, {"high_idx": 4, "image_name": "000000382.png", "low_idx": 74}, {"high_idx": 4, "image_name": "000000383.png", "low_idx": 75}, {"high_idx": 4, "image_name": "000000384.png", "low_idx": 75}, {"high_idx": 4, "image_name": "000000385.png", "low_idx": 76}, {"high_idx": 4, "image_name": "000000386.png", "low_idx": 76}, {"high_idx": 4, "image_name": "000000387.png", "low_idx": 77}, {"high_idx": 4, "image_name": "000000388.png", "low_idx": 77}, {"high_idx": 4, "image_name": "000000389.png", "low_idx": 78}, {"high_idx": 4, "image_name": "000000390.png", "low_idx": 78}, {"high_idx": 4, "image_name": "000000391.png", "low_idx": 79}, {"high_idx": 4, "image_name": "000000392.png", "low_idx": 79}, {"high_idx": 4, "image_name": "000000393.png", "low_idx": 80}, {"high_idx": 4, "image_name": "000000394.png", "low_idx": 80}, {"high_idx": 4, "image_name": "000000395.png", "low_idx": 81}, {"high_idx": 4, "image_name": "000000396.png", "low_idx": 81}, {"high_idx": 4, "image_name": "000000397.png", "low_idx": 82}, {"high_idx": 4, "image_name": "000000398.png", "low_idx": 82}, {"high_idx": 4, "image_name": "000000399.png", "low_idx": 83}, {"high_idx": 4, "image_name": "000000400.png", "low_idx": 83}, {"high_idx": 4, "image_name": "000000401.png", "low_idx": 84}, {"high_idx": 4, "image_name": "000000402.png", "low_idx": 84}, {"high_idx": 4, "image_name": "000000403.png", "low_idx": 85}, {"high_idx": 4, "image_name": "000000404.png", "low_idx": 85}, {"high_idx": 4, "image_name": "000000405.png", "low_idx": 86}, {"high_idx": 4, "image_name": "000000406.png", "low_idx": 86}, {"high_idx": 4, "image_name": "000000407.png", "low_idx": 87}, {"high_idx": 4, "image_name": "000000408.png", "low_idx": 87}, {"high_idx": 4, "image_name": "000000409.png", "low_idx": 88}, {"high_idx": 4, "image_name": "000000410.png", "low_idx": 88}, {"high_idx": 4, "image_name": "000000411.png", "low_idx": 89}, {"high_idx": 4, "image_name": "000000412.png", "low_idx": 89}, {"high_idx": 4, "image_name": "000000413.png", "low_idx": 89}, {"high_idx": 4, "image_name": "000000414.png", "low_idx": 89}, {"high_idx": 4, "image_name": "000000415.png", "low_idx": 89}, {"high_idx": 4, "image_name": "000000416.png", "low_idx": 89}, {"high_idx": 4, "image_name": "000000417.png", "low_idx": 89}, {"high_idx": 4, "image_name": "000000418.png", "low_idx": 89}, {"high_idx": 4, "image_name": "000000419.png", "low_idx": 89}, {"high_idx": 4, "image_name": "000000420.png", "low_idx": 89}, {"high_idx": 4, "image_name": "000000421.png", "low_idx": 89}, {"high_idx": 4, "image_name": "000000422.png", "low_idx": 90}, {"high_idx": 4, "image_name": "000000423.png", "low_idx": 90}, {"high_idx": 4, "image_name": "000000424.png", "low_idx": 91}, {"high_idx": 4, "image_name": "000000425.png", "low_idx": 91}, {"high_idx": 4, "image_name": "000000426.png", "low_idx": 91}, {"high_idx": 4, "image_name": "000000427.png", "low_idx": 91}, {"high_idx": 4, "image_name": "000000428.png", "low_idx": 91}, {"high_idx": 4, "image_name": "000000429.png", "low_idx": 91}, {"high_idx": 4, "image_name": "000000430.png", "low_idx": 91}, {"high_idx": 4, "image_name": "000000431.png", "low_idx": 91}, {"high_idx": 4, "image_name": "000000432.png", "low_idx": 91}, {"high_idx": 4, "image_name": "000000433.png", "low_idx": 91}, {"high_idx": 4, "image_name": "000000434.png", "low_idx": 91}, {"high_idx": 4, "image_name": "000000435.png", "low_idx": 92}, {"high_idx": 4, "image_name": "000000436.png", "low_idx": 92}, {"high_idx": 4, "image_name": "000000437.png", "low_idx": 93}, {"high_idx": 4, "image_name": "000000438.png", "low_idx": 93}, {"high_idx": 4, "image_name": "000000439.png", "low_idx": 94}, {"high_idx": 4, "image_name": "000000440.png", "low_idx": 94}, {"high_idx": 4, "image_name": "000000441.png", "low_idx": 95}, {"high_idx": 4, "image_name": "000000442.png", "low_idx": 95}, {"high_idx": 4, "image_name": "000000443.png", "low_idx": 95}, {"high_idx": 4, "image_name": "000000444.png", "low_idx": 95}, {"high_idx": 4, "image_name": "000000445.png", "low_idx": 95}, {"high_idx": 4, "image_name": "000000446.png", "low_idx": 95}, {"high_idx": 4, "image_name": "000000447.png", "low_idx": 95}, {"high_idx": 4, "image_name": "000000448.png", "low_idx": 95}, {"high_idx": 4, "image_name": "000000449.png", "low_idx": 95}, {"high_idx": 4, "image_name": "000000450.png", "low_idx": 95}, {"high_idx": 4, "image_name": "000000451.png", "low_idx": 95}, {"high_idx": 4, "image_name": "000000452.png", "low_idx": 96}, {"high_idx": 4, "image_name": "000000453.png", "low_idx": 96}, {"high_idx": 5, "image_name": "000000454.png", "low_idx": 97}, {"high_idx": 5, "image_name": "000000455.png", "low_idx": 97}, {"high_idx": 5, "image_name": "000000456.png", "low_idx": 97}, {"high_idx": 5, "image_name": "000000457.png", "low_idx": 97}, {"high_idx": 5, "image_name": "000000458.png", "low_idx": 97}, {"high_idx": 5, "image_name": "000000459.png", "low_idx": 97}, {"high_idx": 5, "image_name": "000000460.png", "low_idx": 97}, {"high_idx": 5, "image_name": "000000461.png", "low_idx": 97}, {"high_idx": 5, "image_name": "000000462.png", "low_idx": 97}, {"high_idx": 5, "image_name": "000000463.png", "low_idx": 97}, {"high_idx": 5, "image_name": "000000464.png", "low_idx": 97}, {"high_idx": 5, "image_name": "000000465.png", "low_idx": 97}, {"high_idx": 5, "image_name": "000000466.png", "low_idx": 97}, {"high_idx": 5, "image_name": "000000467.png", "low_idx": 97}, {"high_idx": 5, "image_name": "000000468.png", "low_idx": 97}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "DiningTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|5|23|1|0"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [8.20783804, 8.20783804, 22.504, 22.504, 6.26252652, 6.26252652]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [8.14, 8.14, 22.504, 22.504, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|+02.05|+01.57|+05.63"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|6|2|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|3|26|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "diningtable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [8.20783804, 8.20783804, 22.504, 22.504, 6.26252652, 6.26252652]], "coordinateReceptacleObjectId": ["DiningTable", [-0.624711872, -0.624711872, 25.896156, 25.896156, 2.673336984, 2.673336984]], "forceVisible": true, "objectId": "Egg|+02.05|+01.57|+05.63", "receptacleObjectId": "DiningTable|-00.16|+00.67|+06.47"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|+02.04|+00.00|+05.63"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 90000]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+02.05|+01.57|+05.63"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [178, 139, 202, 173], "mask": [[41587, 7], [41886, 9], [42184, 13], [42483, 15], [42782, 16], [43082, 17], [43381, 19], [43681, 19], [43980, 21], [44280, 21], [44579, 22], [44879, 23], [45179, 23], [45479, 23], [45778, 25], [46078, 25], [46378, 25], [46678, 25], [46978, 25], [47278, 25], [47578, 25], [47878, 25], [48178, 25], [48478, 24], [48779, 23], [49079, 23], [49379, 22], [49680, 21], [49981, 19], [50281, 18], [50582, 17], [50883, 15], [51184, 13], [51485, 10], [51787, 6]], "point": [190, 155]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|+02.04|+00.00|+05.63"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 40009], [40011, 262], [40274, 35], [40311, 262], [40574, 35], [40610, 250], [40862, 10], [40874, 4], [40883, 12], [40923, 236], [41186, 8], [41224, 233], [41487, 6], [41525, 232], [41788, 5], [41825, 231], [42089, 3], [42126, 230], [42389, 3], [42426, 229], [42689, 2], [42726, 229], [42989, 2], [43026, 229], [43290, 1], [43327, 227], [43590, 1], [43627, 227], [43890, 1], [43927, 227], [44227, 227], [44527, 227], [44827, 227], [45089, 1], [45126, 228], [45389, 1], [45426, 228], [45689, 1], [45726, 228], [45989, 2], [46026, 229], [46289, 2], [46326, 229], [46589, 2], [46626, 229], [46889, 2], [46926, 229], [47188, 4], [47225, 231], [47488, 4], [47525, 231], [47788, 4], [47825, 231], [48088, 5], [48125, 232], [48388, 5], [48425, 232], [48687, 6], [48724, 234], [48987, 7], [49024, 234], [49287, 7], [49324, 235], [49586, 9], [49623, 236], [49886, 9], [49923, 237], [50186, 9], [50223, 237], [50485, 11], [50522, 239], [50785, 11], [50822, 239], [51084, 13], [51122, 241], [51383, 15], [51421, 243], [51682, 17], [51720, 280], [52019, 14429], [66452, 292], [66756, 286], [67058, 283], [67359, 280], [67661, 277], [67962, 275], [68263, 273], [68564, 271], [68865, 269], [69166, 268], [69466, 267], [69767, 265], [70068, 264], [70368, 263], [70669, 262], [70969, 261], [71270, 260], [71571, 258], [71871, 258], [72171, 257], [72472, 256], [72772, 255], [73072, 255], [73373, 254], [73673, 253], [73974, 252], [74274, 252], [74574, 252], [74875, 250], [75175, 250], [75475, 250], [75775, 250], [76075, 250], [76375, 249], [76676, 248], [76976, 248], [77276, 248], [77576, 248], [77876, 248], [78176, 248], [78476, 248], [78776, 248], [79076, 248], [79376, 248], [79676, 248], [79976, 248], [80276, 248], [80576, 248], [80876, 249], [81176, 249], [81475, 250], [81775, 250], [82075, 250], [82375, 250], [82675, 251], [82974, 252], [83274, 253], [83574, 253], [83873, 254], [84173, 255], [84472, 256], [84772, 256], [85071, 258], [85371, 259], [85670, 261], [85970, 261], [86269, 263], [86568, 265], [86867, 267], [87166, 269], [87465, 270], [87765, 272], [88064, 274], [88362, 277], [88661, 280], [88959, 284], [89257, 289], [89554, 446]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+02.05|+01.57|+05.63", "placeStationary": true, "receptacleObjectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 166], [23795, 167], [24094, 168], [24394, 168], [24693, 169], [24992, 169], [25292, 169], [25591, 170], [25890, 171], [26190, 170], [26489, 49], [26553, 107], [26788, 48], [26855, 105], [27087, 48], [27155, 105], [27387, 48], [27459, 101], [27686, 49], [27760, 99], [27985, 50], [28055, 3], [28060, 99], [28285, 50], [28355, 3], [28360, 99], [28584, 51], [28655, 4], [28660, 99], [28883, 52], [28955, 4], [28961, 98], [29183, 52], [29255, 4], [29260, 98], [29482, 53], [29555, 3], [29560, 98], [29781, 54], [29855, 3], [29860, 98], [30081, 54], [30155, 3], [30160, 98], [30380, 55], [30455, 2], [30459, 98], [30679, 56], [30755, 2], [30759, 98], [30978, 57], [31058, 99], [31278, 58], [31357, 100], [31577, 59], [31656, 101], [31876, 60], [31955, 101], [32176, 60], [32255, 101], [32475, 61], [32555, 101], [32775, 61], [32855, 101], [33075, 61], [33155, 100], [33375, 62], [33454, 101], [33675, 63], [33753, 102], [33975, 67], [34049, 106], [34275, 180], [34576, 178], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 32], [46281, 32], [46582, 31], [46882, 31], [47182, 31], [47482, 30], [47782, 30], [48082, 30], [48383, 29], [48683, 28], [48983, 28], [49283, 28], [49583, 28], [49883, 27], [50183, 27], [50484, 26], [50784, 26], [51084, 25], [51384, 25], [51684, 25], [51984, 25], [52285, 23], [52585, 23], [52885, 23], [53185, 23], [53485, 23], [53785, 22], [54085, 22], [54386, 21], [54686, 21], [54986, 20], [55286, 20], [55586, 20], [55886, 20], [56187, 18], [56487, 19], [56787, 21], [57087, 22], [57387, 17], [57406, 3], [57687, 17], [57706, 3], [57987, 17], [58006, 3], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 166], [23795, 167], [24094, 168], [24394, 168], [24693, 169], [24992, 169], [25292, 169], [25591, 170], [25890, 171], [26190, 170], [26489, 49], [26553, 107], [26788, 48], [26855, 105], [27087, 48], [27155, 105], [27387, 48], [27459, 101], [27686, 49], [27760, 27], [27792, 67], [27985, 50], [28055, 3], [28060, 25], [28093, 66], [28285, 50], [28355, 3], [28360, 24], [28394, 65], [28584, 51], [28655, 4], [28660, 24], [28695, 64], [28883, 52], [28955, 4], [28961, 22], [28995, 64], [29183, 52], [29255, 4], [29260, 22], [29296, 62], [29482, 53], [29555, 3], [29560, 22], [29596, 62], [29781, 54], [29855, 3], [29860, 21], [29896, 62], [30081, 54], [30155, 3], [30160, 21], [30196, 62], [30380, 55], [30455, 2], [30459, 22], [30497, 60], [30679, 56], [30755, 2], [30759, 22], [30797, 60], [30978, 57], [31058, 23], [31097, 60], [31278, 58], [31357, 24], [31397, 60], [31577, 59], [31656, 25], [31696, 61], [31876, 60], [31955, 26], [31996, 60], [32176, 60], [32255, 26], [32296, 60], [32475, 61], [32555, 26], [32596, 60], [32775, 61], [32855, 27], [32895, 61], [33075, 61], [33155, 27], [33194, 61], [33375, 62], [33454, 29], [33494, 61], [33675, 63], [33753, 31], [33793, 62], [33975, 67], [34049, 36], [34091, 64], [34275, 180], [34576, 178], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 32], [46281, 32], [46582, 31], [46882, 31], [47182, 31], [47482, 30], [47782, 30], [48082, 30], [48383, 29], [48683, 28], [48983, 28], [49283, 28], [49583, 28], [49883, 27], [50183, 27], [50484, 26], [50784, 26], [51084, 25], [51384, 25], [51684, 25], [51984, 25], [52285, 23], [52585, 23], [52885, 23], [53185, 23], [53485, 23], [53785, 22], [54085, 22], [54386, 21], [54686, 21], [54986, 20], [55286, 20], [55586, 20], [55886, 20], [56187, 18], [56487, 19], [56787, 21], [57087, 22], [57387, 17], [57406, 3], [57687, 17], [57706, 3], [57987, 17], [58006, 3], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+02.05|+01.57|+05.63"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [181, 93, 196, 114], "mask": [[27787, 5], [28085, 8], [28384, 10], [28684, 11], [28983, 12], [29282, 14], [29582, 14], [29881, 15], [30181, 15], [30481, 16], [30781, 16], [31081, 16], [31381, 16], [31681, 15], [31981, 15], [32281, 15], [32581, 15], [32882, 13], [33182, 12], [33483, 11], [33784, 9], [34085, 6]], "point": [188, 102]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 166], [23795, 167], [24094, 168], [24394, 168], [24693, 169], [24992, 169], [25292, 169], [25591, 170], [25890, 171], [26190, 170], [26489, 49], [26553, 107], [26788, 48], [26855, 105], [27087, 48], [27155, 105], [27387, 48], [27459, 101], [27686, 49], [27760, 99], [27985, 50], [28055, 3], [28060, 99], [28285, 50], [28355, 3], [28360, 99], [28584, 51], [28655, 4], [28660, 99], [28883, 52], [28955, 4], [28961, 98], [29183, 52], [29255, 4], [29260, 98], [29482, 53], [29555, 3], [29560, 98], [29781, 54], [29855, 3], [29860, 98], [30081, 54], [30155, 3], [30160, 98], [30380, 55], [30455, 2], [30459, 98], [30679, 56], [30755, 2], [30759, 98], [30978, 57], [31058, 99], [31278, 58], [31357, 100], [31577, 59], [31656, 101], [31876, 60], [31955, 101], [32176, 60], [32255, 101], [32475, 61], [32555, 101], [32775, 61], [32855, 101], [33075, 61], [33155, 100], [33375, 62], [33454, 101], [33675, 63], [33753, 102], [33975, 67], [34049, 106], [34275, 180], [34576, 178], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 32], [46281, 32], [46582, 31], [46882, 31], [47182, 31], [47482, 30], [47782, 30], [48082, 30], [48383, 29], [48683, 28], [48983, 28], [49283, 28], [49583, 28], [49883, 27], [50183, 27], [50484, 26], [50784, 26], [51084, 25], [51384, 25], [51684, 25], [51984, 25], [52285, 23], [52585, 23], [52885, 23], [53185, 23], [53485, 23], [53785, 22], [54085, 22], [54386, 21], [54686, 21], [54986, 20], [55286, 20], [55586, 20], [55886, 20], [56187, 18], [56487, 19], [56787, 21], [57087, 22], [57387, 17], [57406, 3], [57687, 17], [57706, 3], [57987, 17], [58006, 3], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+02.05|+01.57|+05.63", "placeStationary": true, "receptacleObjectId": "DiningTable|-00.16|+00.67|+06.47"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 71, 299, 300], "mask": [[21141, 3], [21148, 5], [21420, 1], [21424, 1], [21441, 2], [21448, 3], [21468, 3], [21714, 7], [21725, 1], [21727, 1], [21740, 3], [21747, 3], [21767, 13], [22008, 14], [22040, 3], [22047, 2], [22067, 17], [22303, 20], [22326, 1], [22328, 1], [22340, 2], [22346, 2], [22368, 21], [22597, 5], [22605, 18], [22640, 2], [22646, 1], [22669, 24], [22894, 10], [22908, 16], [22929, 1], [22940, 2], [22946, 1], [22970, 27], [23191, 15], [23210, 15], [23229, 1], [23240, 2], [23245, 1], [23271, 31], [23487, 21], [23511, 14], [23530, 1], [23540, 1], [23571, 35], [23784, 25], [23813, 13], [23830, 1], [23839, 2], [23844, 1], [23871, 37], [24081, 30], [24116, 11], [24139, 2], [24172, 39], [24378, 34], [24419, 8], [24431, 1], [24439, 1], [24473, 41], [24675, 40], [24721, 7], [24731, 1], [24739, 1], [24773, 43], [24972, 46], [25023, 5], [25039, 1], [25073, 46], [25269, 52], [25325, 4], [25339, 1], [25374, 47], [25567, 55], [25627, 2], [25674, 50], [25865, 56], [25929, 1], [25942, 1], [25974, 52], [26163, 57], [26224, 2], [26242, 1], [26274, 55], [26461, 58], [26523, 3], [26528, 2], [26575, 56], [26759, 58], [26822, 5], [26829, 2], [26875, 58], [27057, 71], [27130, 1], [27142, 1], [27175, 60], [27355, 74], [27430, 2], [27441, 1], [27474, 62], [27653, 47], [27706, 24], [27731, 1], [27741, 1], [27774, 64], [27951, 48], [28007, 23], [28041, 1], [28074, 66], [28249, 49], [28308, 15], [28374, 67], [28547, 51], [28609, 13], [28674, 69], [28845, 52], [28910, 12], [28973, 72], [29144, 53], [29210, 12], [29273, 73], [29443, 54], [29511, 11], [29572, 76], [29741, 55], [29811, 11], [29871, 79], [30040, 56], [30111, 10], [30171, 80], [30338, 58], [30411, 10], [30470, 83], [30637, 59], [30711, 10], [30769, 86], [30936, 61], [31011, 10], [31067, 89], [31234, 63], [31311, 10], [31366, 91], [31533, 64], [31611, 10], [31676, 82], [31832, 66], [31911, 10], [31953, 4], [31959, 5], [31976, 83], [32130, 68], [32210, 11], [32253, 11], [32276, 41], [32323, 37], [32429, 70], [32509, 11], [32553, 11], [32576, 40], [32624, 37], [32728, 72], [32808, 12], [32853, 11], [32876, 38], [32925, 37], [33026, 76], [33107, 13], [33153, 11], [33176, 38], [33226, 38], [33325, 95], [33453, 11], [33476, 37], [33526, 39], [33623, 97], [33753, 11], [33776, 36], [33826, 40], [33922, 98], [34053, 11], [34076, 36], [34135, 32], [34222, 99], [34353, 11], [34377, 34], [34438, 30], [34521, 100], [34653, 12], [34676, 35], [34740, 29], [34820, 101], [34953, 17], [34972, 39], [35042, 28], [35119, 102], [35253, 17], [35272, 38], [35343, 28], [35418, 103], [35553, 17], [35572, 38], [35644, 28], [35717, 104], [35852, 18], [35872, 39], [35945, 28], [36016, 106], [36152, 18], [36172, 39], [36247, 28], [36315, 107], [36452, 18], [36472, 39], [36547, 29], [36614, 108], [36752, 18], [36772, 39], [36847, 29], [36914, 70], [36991, 31], [37052, 19], [37072, 40], [37148, 29], [37213, 70], [37293, 29], [37352, 19], [37372, 40], [37448, 30], [37512, 69], [37596, 27], [37652, 19], [37672, 41], [37748, 30], [37811, 69], [37897, 26], [37951, 20], [37973, 42], [38049, 30], [38110, 70], [38198, 25], [38251, 20], [38273, 36], [38316, 1], [38349, 31], [38409, 71], [38499, 24], [38551, 20], [38573, 35], [38649, 31], [38708, 72], [38800, 24], [38851, 20], [38874, 32], [38949, 32], [39007, 73], [39101, 23], [39151, 20], [39174, 31], [39250, 32], [39307, 72], [39402, 22], [39451, 20], [39474, 31], [39549, 33], [39606, 74], [39703, 22], [39750, 21], [39774, 30], [39846, 37], [39905, 75], [40004, 21], [40050, 21], [40074, 30], [40142, 1], [40146, 38], [40204, 77], [40304, 21], [40350, 21], [40374, 30], [40442, 2], [40446, 38], [40503, 79], [40604, 21], [40650, 21], [40674, 30], [40741, 3], [40746, 39], [40803, 79], [40904, 22], [40950, 21], [40982, 23], [41041, 4], [41047, 39], [41102, 82], [41204, 22], [41249, 22], [41284, 21], [41340, 5], [41347, 39], [41402, 83], [41504, 23], [41549, 20], [41585, 20], [41640, 6], [41648, 39], [41701, 85], [41803, 24], [41849, 19], [41886, 19], [41939, 7], [41948, 40], [42001, 48], [42059, 28], [42102, 25], [42149, 18], [42187, 18], [42239, 8], [42248, 40], [42300, 46], [42362, 27], [42401, 27], [42448, 19], [42488, 17], [42538, 9], [42549, 40], [42600, 44], [42664, 27], [42699, 29], [42748, 18], [42788, 16], [42837, 10], [42849, 41], [42900, 43], [42965, 31], [42997, 69], [43089, 15], [43137, 11], [43150, 40], [43200, 41], [43266, 100], [43389, 15], [43436, 12], [43450, 41], [43500, 40], [43567, 98], [43689, 14], [43736, 13], [43751, 41], [43800, 39], [43868, 97], [43990, 13], [44035, 14], [44051, 41], [44100, 38], [44169, 96], [44290, 12], [44335, 14], [44352, 40], [44400, 37], [44469, 96], [44590, 12], [44634, 15], [44653, 40], [44700, 35], [44770, 60], [44831, 33], [44890, 12], [44933, 17], [44953, 40], [45000, 34], [45071, 59], [45131, 33], [45190, 11], [45232, 18], [45253, 40], [45300, 32], [45372, 58], [45431, 33], [45490, 11], [45532, 18], [45554, 39], [45600, 31], [45673, 57], [45731, 28], [45789, 12], [45818, 1], [45831, 20], [45854, 40], [45900, 30], [45973, 57], [46031, 26], [46089, 12], [46118, 2], [46130, 21], [46154, 40], [46200, 28], [46274, 56], [46331, 24], [46389, 11], [46417, 6], [46428, 23], [46455, 39], [46500, 27], [46574, 80], [46688, 12], [46717, 35], [46755, 40], [46800, 26], [46875, 78], [46987, 12], [47017, 35], [47055, 40], [47100, 25], [47175, 78], [47286, 13], [47317, 35], [47356, 39], [47400, 24], [47476, 53], [47530, 22], [47585, 14], [47617, 36], [47656, 39], [47700, 23], [47776, 53], [47830, 22], [47884, 15], [47917, 36], [47956, 40], [48000, 22], [48076, 53], [48130, 22], [48182, 16], [48217, 36], [48257, 39], [48300, 22], [48377, 52], [48430, 22], [48480, 18], [48516, 38], [48557, 39], [48600, 21], [48677, 52], [48730, 22], [48772, 26], [48816, 38], [48858, 39], [48900, 20], [48978, 51], [49030, 22], [49072, 26], [49116, 39], [49158, 39], [49200, 19], [49278, 51], [49330, 23], [49371, 27], [49416, 39], [49458, 39], [49500, 19], [49578, 51], [49630, 24], [49671, 28], [49715, 40], [49759, 38], [49800, 18], [49878, 51], [49930, 24], [49970, 30], [50015, 41], [50059, 39], [50100, 18], [50178, 51], [50230, 24], [50270, 31], [50314, 84], [50400, 17], [50478, 51], [50530, 24], [50570, 32], [50613, 85], [50700, 17], [50778, 51], [50830, 24], [50869, 35], [50912, 87], [51000, 16], [51078, 51], [51130, 24], [51169, 130], [51300, 15], [51377, 51], [51430, 24], [51469, 130], [51600, 14], [51677, 51], [51730, 24], [51769, 130], [51900, 14], [51977, 51], [52030, 23], [52070, 143], [52276, 52], [52330, 23], [52370, 142], [52574, 54], [52629, 24], [52670, 142], [52872, 56], [52929, 24], [52970, 141], [53171, 57], [53229, 23], [53270, 141], [53471, 57], [53529, 23], [53570, 141], [53772, 56], [53829, 23], [53870, 140], [54072, 56], [54129, 23], [54170, 140], [54373, 55], [54429, 23], [54470, 141], [54673, 55], [54729, 23], [54770, 129], [54900, 11], [54973, 55], [55029, 23], [55070, 129], [55200, 12], [55274, 53], [55329, 22], [55371, 128], [55500, 12], [55573, 1], [55575, 52], [55629, 22], [55671, 128], [55800, 12], [55876, 51], [55929, 22], [55970, 129], [56100, 13], [56150, 2], [56177, 49], [56229, 22], [56270, 129], [56400, 13], [56448, 5], [56478, 22], [56504, 22], [56529, 22], [56570, 129], [56700, 14], [56745, 8], [56779, 18], [56807, 19], [56829, 22], [56870, 129], [57000, 16], [57042, 12], [57080, 15], [57109, 17], [57129, 23], [57170, 129], [57300, 17], [57337, 18], [57381, 13], [57410, 16], [57429, 23], [57470, 129], [57600, 18], [57630, 25], [57682, 11], [57711, 15], [57729, 23], [57770, 128], [57900, 21], [57926, 30], [57983, 9], [58012, 14], [58029, 24], [58069, 129], [58200, 56], [58283, 8], [58312, 14], [58329, 25], [58368, 130], [58500, 57], [58584, 7], [58613, 13], [58629, 26], [58667, 131], [58800, 57], [58885, 5], [58913, 12], [58929, 28], [58965, 133], [59100, 57], [59186, 4], [59213, 12], [59229, 169], [59400, 58], [59487, 3], [59513, 12], [59529, 169], [59700, 58], [59787, 3], [59813, 12], [59829, 169], [60000, 59], [60087, 3], [60113, 12], [60129, 169], [60300, 59], [60387, 3], [60412, 13], [60428, 170], [60600, 60], [60687, 3], [60712, 13], [60728, 170], [60900, 60], [60987, 3], [61012, 13], [61028, 169], [61200, 60], [61288, 3], [61312, 13], [61328, 169], [61500, 61], [61588, 3], [61612, 13], [61628, 169], [61800, 61], [61888, 4], [61912, 13], [61928, 169], [62100, 61], [62188, 5], [62213, 11], [62228, 169], [62400, 62], [62489, 5], [62513, 11], [62528, 169], [62700, 63], [62789, 6], [62814, 10], [62828, 169], [63000, 64], [63090, 5], [63114, 10], [63128, 168], [63300, 65], [63390, 5], [63415, 9], [63428, 167], [63600, 66], [63690, 5], [63715, 9], [63728, 167], [63900, 66], [63991, 4], [64015, 9], [64028, 166], [64200, 67], [64291, 4], [64315, 9], [64328, 166], [64500, 68], [64592, 3], [64616, 8], [64627, 166], [64800, 69], [64892, 3], [64916, 9], [64927, 166], [65100, 70], [65192, 3], [65217, 175], [65400, 71], [65493, 2], [65517, 175], [65700, 72], [65793, 2], [65817, 174], [66000, 73], [66093, 3], [66118, 173], [66300, 73], [66392, 4], [66418, 172], [66600, 74], [66691, 5], [66718, 172], [66900, 75], [66989, 7], [67018, 171], [67200, 76], [67288, 8], [67318, 171], [67500, 77], [67587, 9], [67618, 170], [67800, 78], [67886, 10], [67918, 170], [68100, 79], [68184, 13], [68218, 27], [68255, 132], [68400, 80], [68483, 14], [68518, 24], [68558, 128], [68700, 97], [68817, 23], [68860, 126], [69000, 98], [69116, 23], [69162, 123], [69300, 98], [69416, 21], [69463, 122], [69600, 99], [69715, 21], [69764, 120], [69900, 100], [70014, 21], [70065, 119], [70201, 100], [70312, 22], [70366, 117], [70502, 101], [70610, 23], [70667, 116], [70803, 129], [70968, 114], [71104, 128], [71268, 114], [71405, 126], [71569, 112], [71705, 125], [71870, 111], [72006, 124], [72170, 110], [72307, 122], [72471, 109], [72608, 121], [72771, 108], [72909, 119], [73072, 107], [73209, 118], [73372, 106], [73510, 117], [73673, 104], [73811, 116], [73973, 103], [74112, 114], [74274, 101], [74413, 113], [74574, 100], [74713, 113], [74874, 99], [75014, 111], [75175, 97], [75315, 110], [75475, 95], [75616, 109], [75775, 94], [75917, 108], [76075, 93], [76217, 107], [76375, 92], [76518, 106], [76676, 90], [76819, 105], [76976, 89], [77120, 104], [77276, 87], [77421, 103], [77576, 86], [77722, 102], [77876, 85], [78022, 102], [78176, 84], [78323, 101], [78476, 83], [78624, 100], [78776, 82], [78925, 99], [79076, 81], [79226, 98], [79376, 79], [79527, 97], [79676, 78], [79829, 95], [79976, 77], [80130, 94], [80275, 77], [80432, 93], [80575, 76], [80734, 91], [80875, 75], [81035, 90], [81175, 73], [81337, 88], [81475, 72], [81639, 87], [81774, 72], [81940, 86], [82074, 71], [82242, 84], [82374, 70], [82544, 83], [82673, 70], [82846, 81], [82973, 69], [83147, 81], [83272, 68], [83449, 79], [83572, 66], [83751, 78], [83871, 64], [84052, 77], [84171, 62], [84354, 76], [84470, 60], [84656, 74], [84770, 58], [84958, 73], [85069, 57], [85259, 73], [85368, 55], [85561, 72], [85667, 54], [85863, 71], [85966, 52], [86164, 71], [86265, 51], [86466, 71], [86564, 49], [86768, 70], [86862, 49], [87070, 70], [87160, 48], [87372, 70], [87458, 48], [87677, 68], [87754, 49], [87981, 120], [88286, 112], [88591, 105], [88895, 98], [89200, 91], [89505, 83], [89809, 67]], "point": [149, 184]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan18", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -0.75, "y": 0.900999844, "z": 7.25}, "object_poses": [{"objectName": "Potato_5b11696a", "position": {"x": -0.1351898, "y": 0.7949763, "z": 6.23786259}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "ButterKnife_8d8b47bb", "position": {"x": 1.34822512, "y": 0.771864057, "z": 3.36045027}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_5453e71d", "position": {"x": 0.04679808, "y": 1.58063388, "z": 0.2071247}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_73d89a5b", "position": {"x": 1.32, "y": 0.988599956, "z": 3.848}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_73d89a5b", "position": {"x": 1.559894, "y": 0.988599956, "z": 4.2942}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_925e7ba3", "position": {"x": -0.0341779739, "y": 0.775088251, "z": 6.941612}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_8ae143f8", "position": {"x": 0.80350095, "y": 0.7729708, "z": 0.451127946}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_8ae143f8", "position": {"x": 1.44258, "y": 0.891180933, "z": 1.98313773}, "rotation": {"x": 1.40334208e-14, "y": -9.659347e-06, "z": -1.71913052e-21}}, {"objectName": "Egg_8021024b", "position": {"x": -0.377019346, "y": 0.810111, "z": 6.261198}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Egg_8021024b", "position": {"x": -0.24574241, "y": 0.810111, "z": 6.83076859}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Knife_94d26365", "position": {"x": 1.2923851, "y": 0.801405668, "z": 3.22306514}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_94d26365", "position": {"x": 0.150742471, "y": 0.789788663, "z": 6.416539}, "rotation": {"x": 0.0, "y": 4.829673e-06, "z": 0.0}}, {"objectName": "Tomato_8b4d7f57", "position": {"x": -0.0272482336, "y": 0.80955404, "z": 6.61227465}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "SoapBottle_33d3f1ac", "position": {"x": 1.13578975, "y": 0.9927622, "z": 4.76232}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_cff2324e", "position": {"x": 0.3052411, "y": 0.7550883, "z": 6.354039}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Lettuce_6d4d89ae", "position": {"x": -0.467251956, "y": 0.848502, "z": 6.541539}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_a939ee5b", "position": {"x": 2.05195951, "y": 1.59014213, "z": 5.876}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bowl_9f9e1c57", "position": {"x": 1.22515035, "y": 0.9917379, "z": 3.32445574}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_b7525b79", "position": {"x": 1.09999228, "y": 1.0487318, "z": 0.434997439}, "rotation": {"x": -0.0006395338, "y": -0.0002419045, "z": 0.003708425}}, {"objectName": "Egg_8021024b", "position": {"x": 2.05195951, "y": 1.56563163, "z": 5.626}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_94d26365", "position": {"x": 0.7357707, "y": 0.802122533, "z": 0.451127946}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_8ae143f8", "position": {"x": 0.771972537, "y": 0.9932486, "z": 0.6700103}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a939ee5b", "position": {"x": 2.09459472, "y": 1.59014213, "z": 6.001}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Kettle_5453e71d", "position": {"x": 1.593, "y": 0.9816944, "z": 3.25}, "rotation": {"x": 0.0, "y": 255.000153, "z": 0.0}}, {"objectName": "Plate_1f77d579", "position": {"x": 0.8476807, "y": 0.9932941, "z": 0.2120077}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b7138966", "position": {"x": 0.0157748461, "y": 0.7550884, "z": 6.74238}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Lettuce_6d4d89ae", "position": {"x": 0.123716265, "y": 0.84850204, "z": 6.10775661}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "SoapBottle_33d3f1ac", "position": {"x": 0.256298929, "y": 0.760150552, "z": 6.24033928}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Spatula_925e7ba3", "position": {"x": -0.158254743, "y": 0.775088251, "z": 6.604039}, "rotation": {"x": 0.0, "y": 4.829673e-06, "z": 0.0}}, {"objectName": "PaperTowelRoll_bd2376ff", "position": {"x": 2.05555582, "y": 0.185002416, "z": 6.46908}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_73d89a5b", "position": {"x": 1.317894, "y": 0.988599956, "z": 4.2942}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5b11696a", "position": {"x": 2.00932431, "y": 1.55049694, "z": 5.251}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "ButterKnife_8d8b47bb", "position": {"x": -0.481585175, "y": 0.7714228, "z": 0.45141685}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_cff2324e", "position": {"x": 0.150742471, "y": 0.7550883, "z": 6.541539}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pan_08687688", "position": {"x": 1.562, "y": 0.988599956, "z": 3.848}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_8b4d7f57", "position": {"x": -0.6113827, "y": 1.04216564, "z": 0.486808717}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_6d7ef995", "position": {"x": 1.29384458, "y": 0.771185935, "z": 4.753865}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_8e8a26e1", "position": {"x": -0.07261342, "y": 0.7562329, "z": 6.83076859}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Spoon_cefc2847", "position": {"x": 1.31450844, "y": 0.9938295, "z": 1.3502}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_6d84c70f", "position": {"x": 0.2733106, "y": 1.09214664, "z": 0.245674372}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3490060117, "scene_num": 18}, "task_id": "trial_T20190908_051346_079516", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AM2KK02JXXW48_3DR23U6WE8VPQ24XUETCOR1Y5UKET9", "high_descs": ["Turn left, move around the table to the left, go to the fridge. ", "Take the egg out of the fridge. ", "Take the egg to the microwave on the counter, across the room to the right. ", "Head the egg in the microwave. ", "Bring the egg back across the room to the right side of the black table. ", "Put the egg on the right side of the table."], "task_desc": "Put a heated egg on the table. ", "votes": [1, 1]}, {"assignment_id": "A3C81THYYSBGVD_3J88R45B2JF04A2782AK76L8WXFPXT", "high_descs": ["Turn left then face the fridge on your left by the round table.", "Take out an egg from the fridge in front of you and close it.", "Turn around then face the microwave to your left.", "Place the egg in the microwave and take it out.", "Turn around and face the round table to your left by the fridge.", "Place the egg on top of the round table in front of you."], "task_desc": "Place a warm egg on the table.", "votes": [1, 1]}, {"assignment_id": "AD0NVUGLDYDYN_3K5TEWLKGYSGJZA8VSJIAONBU7RVIX", "high_descs": ["turn left, walk to the refrigerator", "put the refrigerator, take the egg on the right out", "turn right, walk to the microwave", "open the microwave, heat the egg in it for a while, take the egg out", "turn around, walk to the table on the left", "put the egg on the table"], "task_desc": "cool the egg in the refrigerator, put the egg on the table", "votes": [1, 1]}]}}