{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000186.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000187.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000188.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000189.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000190.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000191.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000192.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000193.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000194.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000245.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000246.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000247.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000248.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000249.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000250.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000251.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000252.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000253.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000254.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000255.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000256.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000257.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000258.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000259.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000260.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000261.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000262.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000263.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000264.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000320.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000321.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000322.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000323.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000324.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000325.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000326.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000327.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000328.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000329.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000330.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000331.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000332.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000333.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000334.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000335.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000336.png", "low_idx": 58}, {"high_idx": 5, "image_name": "000000337.png", "low_idx": 58}, {"high_idx": 5, "image_name": "000000338.png", "low_idx": 58}, {"high_idx": 5, "image_name": "000000339.png", "low_idx": 58}, {"high_idx": 5, "image_name": "000000340.png", "low_idx": 58}, {"high_idx": 5, "image_name": "000000341.png", "low_idx": 58}, {"high_idx": 5, "image_name": "000000342.png", "low_idx": 58}, {"high_idx": 5, "image_name": "000000343.png", "low_idx": 58}, {"high_idx": 5, "image_name": "000000344.png", "low_idx": 58}, {"high_idx": 5, "image_name": "000000345.png", "low_idx": 58}, {"high_idx": 5, "image_name": "000000346.png", "low_idx": 58}, {"high_idx": 5, "image_name": "000000347.png", "low_idx": 58}, {"high_idx": 5, "image_name": "000000348.png", "low_idx": 58}, {"high_idx": 5, "image_name": "000000349.png", "low_idx": 58}, {"high_idx": 5, "image_name": "000000350.png", "low_idx": 58}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "GarbageCan", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|6|1|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [8.011756, 8.011756, 0.3201586, 0.3201586, 0.387196064, 0.387196064]], "coordinateReceptacleObjectId": ["GarbageCan", [7.696, 7.696, 0.552, 0.552, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|+02.00|+00.10|+00.08"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|-5|1|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [7.308, 7.308, -5.416, -5.416, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.83|+00.90|-01.35"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|6|1|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "garbagecan"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [8.011756, 8.011756, 0.3201586, 0.3201586, 0.387196064, 0.387196064]], "coordinateReceptacleObjectId": ["GarbageCan", [7.696, 7.696, 0.552, 0.552, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|+02.00|+00.10|+00.08", "receptacleObjectId": "GarbageCan|+01.92|+00.00|+00.14"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+02.00|+00.10|+00.08"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [174, 197, 183, 207], "mask": [[58977, 3], [59276, 6], [59575, 8], [59874, 10], [60174, 10], [60474, 10], [60774, 10], [61074, 10], [61374, 9], [61675, 7], [61977, 4]], "point": [178, 201]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.83|+00.90|-01.35"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [78, 19, 297, 144], "mask": [[5496, 165], [5795, 167], [6095, 168], [6394, 170], [6694, 171], [6993, 173], [7293, 174], [7593, 174], [7892, 176], [8192, 177], [8491, 179], [8791, 180], [9091, 181], [9390, 183], [9690, 184], [9989, 185], [10289, 186], [10588, 188], [10888, 189], [11188, 190], [11487, 192], [11787, 193], [12086, 195], [12386, 195], [12685, 197], [12985, 198], [13285, 199], [13584, 201], [13884, 202], [14183, 204], [14483, 204], [14782, 206], [15082, 207], [15382, 208], [15681, 210], [15981, 210], [16281, 210], [16581, 212], [16880, 214], [17180, 215], [17479, 217], [17779, 218], [18078, 219], [18378, 220], [18678, 220], [18978, 220], [19278, 220], [19578, 220], [19878, 220], [20178, 220], [20478, 220], [20778, 220], [21078, 220], [21378, 220], [21678, 220], [21978, 220], [22278, 220], [22578, 220], [22878, 219], [23178, 219], [23478, 218], [23779, 217], [24079, 216], [24379, 216], [24679, 216], [24979, 215], [25280, 214], [25580, 213], [25880, 213], [26180, 212], [26480, 212], [26781, 210], [27081, 210], [27381, 210], [27681, 209], [27982, 208], [28282, 207], [28582, 207], [28882, 206], [29182, 206], [29483, 204], [29783, 204], [30083, 204], [30383, 203], [30684, 202], [30984, 201], [31284, 201], [31584, 200], [31884, 200], [32185, 198], [32485, 198], [32785, 198], [33085, 197], [33385, 197], [33686, 195], [33986, 195], [34286, 194], [34586, 194], [34887, 192], [35187, 192], [35487, 192], [35787, 191], [36087, 191], [36388, 189], [36688, 189], [36988, 188], [37288, 188], [37588, 188], [37889, 186], [38189, 186], [38489, 185], [38789, 185], [39090, 183], [39390, 183], [39690, 182], [39990, 182], [40290, 182], [40591, 180], [40891, 180], [41191, 179], [41491, 179], [41792, 177], [42092, 177], [42392, 176], [42692, 176], [42992, 175]], "point": [187, 80]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+02.00|+00.10|+00.08", "placeStationary": true, "receptacleObjectId": "Microwave|+01.83|+00.90|-01.35"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 19, 297, 268], "mask": [[5496, 165], [5795, 167], [6095, 168], [6394, 170], [6694, 171], [6993, 173], [7293, 174], [7593, 174], [7892, 176], [8192, 177], [8491, 179], [8791, 180], [9091, 181], [9390, 183], [9690, 184], [9989, 185], [10289, 186], [10588, 188], [10888, 189], [11188, 190], [11487, 192], [11787, 193], [12086, 195], [12386, 195], [12685, 197], [12985, 198], [13285, 199], [13584, 201], [13884, 202], [14183, 204], [14483, 204], [14782, 206], [15082, 207], [15382, 208], [15681, 210], [15981, 210], [16267, 224], [16567, 226], [16866, 228], [17165, 230], [17465, 231], [17764, 233], [18064, 233], [18363, 235], [18662, 236], [18962, 236], [19261, 237], [19561, 237], [19860, 238], [20160, 238], [20459, 239], [20759, 239], [21058, 240], [21358, 100], [21467, 131], [21657, 99], [21769, 129], [21957, 98], [22070, 128], [22256, 98], [22371, 127], [22555, 98], [22672, 126], [22855, 97], [22973, 124], [23154, 98], [23274, 123], [23454, 97], [23574, 122], [23753, 98], [23875, 121], [24053, 98], [24175, 120], [24352, 99], [24475, 120], [24652, 99], [24775, 120], [24951, 100], [25075, 119], [25251, 100], [25375, 119], [25550, 101], [25674, 119], [25850, 101], [25974, 119], [26149, 102], [26274, 118], [26449, 103], [26574, 118], [26748, 104], [26873, 118], [27048, 105], [27173, 118], [27347, 106], [27472, 119], [27647, 107], [27772, 118], [27946, 109], [28071, 119], [28246, 109], [28371, 118], [28545, 111], [28669, 120], [28845, 112], [28968, 120], [29144, 115], [29266, 122], [29444, 243], [29743, 244], [30043, 244], [30342, 244], [30642, 244], [30941, 244], [31241, 244], [31540, 244], [31840, 244], [32139, 244], [32439, 244], [32738, 245], [33038, 244], [33337, 245], [33637, 244], [33936, 245], [34236, 244], [34535, 245], [34835, 244], [35134, 245], [35434, 245], [35733, 245], [36033, 245], [36332, 245], [36632, 245], [36931, 245], [37230, 246], [37530, 246], [37829, 246], [38129, 246], [38428, 246], [38728, 246], [39027, 246], [39327, 246], [39626, 246], [39926, 246], [40225, 247], [40525, 246], [40824, 247], [41124, 74], [41329, 41], [41423, 75], [41630, 40], [41723, 75], [41930, 39], [42022, 75], [42230, 39], [42322, 75], [42531, 37], [42621, 76], [42831, 37], [42921, 76], [43132, 35], [43220, 77], [43520, 76], [43819, 77], [44119, 77], [44418, 78], [44718, 77], [45017, 78], [45317, 78], [45616, 79], [45916, 79], [46215, 79], [46515, 79], [46814, 80], [47114, 80], [47413, 81], [47713, 80], [48012, 81], [48312, 81], [48611, 82], [48911, 82], [49210, 82], [49510, 82], [49809, 83], [50109, 83], [50408, 83], [50708, 83], [51007, 84], [51307, 84], [51606, 85], [51905, 85], [52205, 85], [52504, 86], [52804, 86], [53103, 87], [53403, 86], [53702, 87], [54002, 87], [54301, 88], [54601, 88], [54900, 88], [55200, 88], [55500, 88], [55800, 88], [56100, 87], [56400, 87], [56700, 87], [57000, 87], [57300, 87], [57600, 86], [57900, 86], [58200, 86], [58500, 86], [58800, 86], [59100, 85], [59400, 85], [59700, 85], [60000, 85], [60300, 85], [60600, 84], [60900, 84], [61200, 84], [61500, 84], [61800, 83], [62100, 83], [62400, 83], [62700, 83], [63000, 83], [63300, 82], [63600, 82], [63900, 82], [64200, 82], [64501, 81], [64801, 80], [65102, 79], [65403, 78], [65704, 77], [66005, 76], [66305, 75], [66606, 74], [66907, 73], [67208, 72], [67508, 71], [67809, 70], [68110, 69], [68411, 68], [68711, 68], [69012, 66], [69313, 65], [69614, 64], [69915, 63], [70215, 63], [70516, 61], [70817, 60], [71118, 59], [71418, 59], [71719, 58], [72020, 56], [72321, 55], [72621, 55], [72922, 54], [73223, 52], [73524, 51], [73824, 51], [74125, 50], [74426, 49], [74727, 47], [75028, 46], [75328, 46], [75629, 45], [75930, 44], [76231, 42], [76531, 42], [76832, 41], [77133, 40], [77434, 39], [77734, 38], [78035, 40], [78336, 41], [78637, 41], [78938, 33], [78972, 6], [79238, 33], [79273, 5], [79540, 31], [79574, 2], [79844, 27], [80148, 22]], "point": [148, 136]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.83|+00.90|-01.35"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 297, 268], "mask": [[5496, 165], [5795, 167], [6095, 168], [6394, 170], [6694, 171], [6993, 173], [7293, 174], [7593, 174], [7892, 176], [8192, 177], [8491, 179], [8791, 180], [9091, 181], [9390, 183], [9690, 184], [9989, 185], [10289, 186], [10588, 188], [10888, 189], [11188, 190], [11487, 192], [11787, 193], [12086, 195], [12386, 195], [12685, 197], [12985, 198], [13285, 199], [13584, 201], [13884, 202], [14183, 204], [14483, 204], [14782, 206], [15082, 207], [15382, 208], [15681, 210], [15981, 210], [16267, 224], [16567, 226], [16866, 228], [17165, 230], [17465, 231], [17764, 233], [18064, 233], [18363, 235], [18662, 236], [18962, 236], [19261, 237], [19561, 237], [19860, 238], [20160, 238], [20459, 239], [20759, 239], [21058, 240], [21358, 100], [21467, 131], [21657, 99], [21769, 129], [21957, 98], [22070, 128], [22256, 98], [22371, 127], [22555, 98], [22672, 126], [22855, 97], [22973, 124], [23154, 98], [23274, 123], [23454, 97], [23574, 122], [23753, 98], [23875, 121], [24053, 98], [24175, 120], [24352, 99], [24475, 120], [24652, 99], [24775, 120], [24951, 100], [25075, 119], [25251, 100], [25375, 119], [25550, 101], [25674, 119], [25850, 101], [25974, 119], [26149, 102], [26274, 118], [26449, 103], [26574, 118], [26748, 104], [26873, 118], [27048, 105], [27173, 118], [27347, 106], [27472, 119], [27647, 107], [27772, 118], [27946, 109], [28071, 119], [28246, 101], [28350, 5], [28371, 118], [28545, 100], [28652, 4], [28669, 120], [28845, 99], [28954, 3], [28968, 120], [29144, 99], [29255, 4], [29266, 122], [29444, 98], [29555, 132], [29743, 98], [29856, 131], [30043, 98], [30156, 131], [30342, 98], [30457, 129], [30642, 98], [30757, 129], [30941, 99], [31057, 128], [31241, 99], [31357, 128], [31540, 100], [31657, 127], [31840, 100], [31957, 127], [32139, 101], [32257, 126], [32439, 101], [32557, 126], [32738, 103], [32857, 126], [33038, 103], [33156, 126], [33337, 105], [33455, 127], [33637, 106], [33754, 127], [33936, 108], [34053, 128], [34236, 110], [34351, 129], [34535, 245], [34835, 244], [35134, 245], [35434, 245], [35733, 245], [36033, 245], [36332, 245], [36632, 245], [36931, 245], [37230, 246], [37530, 246], [37829, 246], [38129, 246], [38428, 246], [38728, 246], [39027, 246], [39327, 246], [39626, 246], [39926, 246], [40225, 247], [40525, 246], [40824, 247], [41124, 74], [41329, 41], [41423, 75], [41630, 40], [41723, 75], [41930, 39], [42022, 75], [42230, 39], [42322, 75], [42531, 37], [42621, 76], [42831, 37], [42921, 76], [43132, 35], [43220, 77], [43520, 76], [43819, 77], [44119, 77], [44418, 78], [44718, 77], [45017, 78], [45317, 78], [45616, 79], [45916, 79], [46215, 79], [46515, 79], [46814, 80], [47114, 80], [47413, 81], [47713, 80], [48012, 81], [48312, 81], [48611, 82], [48911, 82], [49210, 82], [49510, 82], [49809, 83], [50109, 83], [50408, 83], [50708, 83], [51007, 84], [51307, 84], [51606, 85], [51905, 85], [52205, 85], [52504, 86], [52804, 86], [53103, 87], [53403, 86], [53702, 87], [54002, 87], [54301, 88], [54601, 88], [54900, 88], [55200, 88], [55500, 88], [55800, 88], [56100, 87], [56400, 87], [56700, 87], [57000, 87], [57300, 87], [57600, 86], [57900, 86], [58200, 86], [58500, 86], [58800, 86], [59100, 85], [59400, 85], [59700, 85], [60000, 85], [60300, 85], [60600, 84], [60900, 84], [61200, 84], [61500, 84], [61800, 83], [62100, 83], [62400, 83], [62700, 83], [63000, 83], [63300, 82], [63600, 82], [63900, 82], [64200, 82], [64501, 81], [64801, 80], [65102, 79], [65403, 78], [65704, 77], [66005, 76], [66305, 75], [66606, 74], [66907, 73], [67208, 72], [67508, 71], [67809, 70], [68110, 69], [68411, 68], [68711, 68], [69012, 66], [69313, 65], [69614, 64], [69915, 63], [70215, 63], [70516, 61], [70817, 60], [71118, 59], [71418, 59], [71719, 58], [72020, 56], [72321, 55], [72621, 55], [72922, 54], [73223, 52], [73524, 51], [73824, 51], [74125, 50], [74426, 49], [74727, 47], [75028, 46], [75328, 46], [75629, 45], [75930, 44], [76231, 42], [76531, 42], [76832, 41], [77133, 40], [77434, 39], [77734, 38], [78035, 40], [78336, 41], [78637, 41], [78938, 33], [78972, 6], [79238, 33], [79273, 5], [79540, 31], [79574, 2], [79844, 27], [80148, 22]], "point": [148, 136]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [7.308, 7.308, -5.416, -5.416, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.83|+00.90|-01.35"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [78, 19, 297, 144], "mask": [[5496, 165], [5795, 167], [6095, 168], [6394, 170], [6694, 171], [6993, 173], [7293, 174], [7593, 174], [7892, 176], [8192, 177], [8491, 179], [8791, 180], [9091, 181], [9390, 183], [9690, 184], [9989, 185], [10289, 186], [10588, 188], [10888, 189], [11188, 190], [11487, 192], [11787, 193], [12086, 195], [12386, 195], [12685, 197], [12985, 198], [13285, 199], [13584, 201], [13884, 202], [14183, 204], [14483, 204], [14782, 206], [15082, 207], [15382, 208], [15681, 210], [15981, 210], [16281, 210], [16581, 212], [16880, 214], [17180, 215], [17479, 217], [17779, 218], [18078, 219], [18378, 220], [18678, 220], [18978, 220], [19278, 220], [19578, 220], [19878, 220], [20178, 220], [20478, 220], [20778, 220], [21078, 220], [21378, 220], [21678, 220], [21978, 220], [22278, 220], [22578, 220], [22878, 219], [23178, 219], [23478, 218], [23779, 217], [24079, 216], [24379, 216], [24679, 216], [24979, 215], [25280, 214], [25580, 213], [25880, 213], [26180, 212], [26480, 212], [26781, 210], [27081, 210], [27381, 210], [27681, 209], [27982, 208], [28282, 207], [28582, 207], [28882, 206], [29182, 206], [29483, 204], [29783, 204], [30083, 204], [30383, 203], [30684, 202], [30984, 201], [31284, 201], [31584, 200], [31884, 200], [32185, 198], [32485, 198], [32785, 198], [33085, 197], [33385, 197], [33686, 195], [33986, 195], [34286, 194], [34586, 194], [34887, 192], [35187, 192], [35487, 192], [35787, 191], [36087, 191], [36388, 189], [36688, 189], [36988, 188], [37288, 188], [37588, 188], [37889, 186], [38189, 186], [38489, 185], [38789, 185], [39090, 183], [39390, 183], [39690, 182], [39990, 182], [40290, 182], [40591, 180], [40891, 180], [41191, 179], [41491, 179], [41792, 177], [42092, 177], [42392, 176], [42692, 176], [42992, 175]], "point": [187, 80]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [7.308, 7.308, -5.416, -5.416, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.83|+00.90|-01.35"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [78, 19, 297, 144], "mask": [[5496, 165], [5795, 167], [6095, 168], [6394, 170], [6694, 171], [6993, 173], [7293, 174], [7593, 174], [7892, 176], [8192, 177], [8491, 179], [8791, 180], [9091, 181], [9390, 183], [9690, 184], [9989, 185], [10289, 186], [10588, 188], [10888, 189], [11188, 190], [11487, 192], [11787, 193], [12086, 195], [12386, 195], [12685, 197], [12985, 198], [13285, 199], [13584, 201], [13884, 202], [14183, 204], [14483, 204], [14782, 206], [15082, 207], [15382, 208], [15681, 210], [15981, 210], [16281, 210], [16581, 212], [16880, 214], [17180, 215], [17479, 217], [17779, 218], [18078, 219], [18378, 220], [18678, 220], [18978, 220], [19278, 220], [19578, 220], [19878, 220], [20178, 220], [20478, 220], [20778, 220], [21078, 220], [21378, 220], [21678, 220], [21978, 220], [22278, 220], [22578, 220], [22878, 219], [23178, 219], [23478, 218], [23779, 217], [24079, 216], [24379, 216], [24679, 216], [24979, 215], [25280, 214], [25580, 213], [25880, 213], [26180, 212], [26480, 212], [26781, 210], [27081, 210], [27381, 210], [27681, 209], [27982, 208], [28282, 207], [28582, 207], [28882, 206], [29182, 206], [29483, 204], [29783, 204], [30083, 204], [30383, 203], [30684, 202], [30984, 201], [31284, 201], [31584, 200], [31884, 200], [32185, 198], [32485, 198], [32785, 198], [33085, 197], [33385, 197], [33686, 195], [33986, 195], [34286, 194], [34586, 194], [34887, 192], [35187, 192], [35487, 192], [35787, 191], [36087, 191], [36388, 189], [36688, 189], [36988, 188], [37288, 188], [37588, 188], [37889, 186], [38189, 186], [38489, 185], [38789, 185], [39090, 183], [39390, 183], [39690, 182], [39990, 182], [40290, 182], [40591, 180], [40891, 180], [41191, 179], [41491, 179], [41792, 177], [42092, 177], [42392, 176], [42692, 176], [42992, 175]], "point": [187, 80]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.83|+00.90|-01.35"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [78, 19, 297, 144], "mask": [[5496, 165], [5795, 167], [6095, 168], [6394, 170], [6694, 171], [6993, 173], [7293, 174], [7593, 174], [7892, 176], [8192, 177], [8491, 179], [8791, 180], [9091, 181], [9390, 183], [9690, 184], [9989, 185], [10289, 186], [10588, 188], [10888, 189], [11188, 190], [11487, 192], [11787, 193], [12086, 195], [12386, 195], [12685, 197], [12985, 198], [13285, 199], [13584, 201], [13884, 202], [14183, 204], [14483, 204], [14782, 206], [15082, 207], [15382, 208], [15681, 210], [15981, 210], [16281, 210], [16581, 212], [16880, 214], [17180, 215], [17479, 217], [17779, 218], [18078, 219], [18378, 220], [18678, 220], [18978, 220], [19278, 220], [19578, 220], [19878, 220], [20178, 220], [20478, 220], [20778, 220], [21078, 220], [21378, 220], [21678, 220], [21978, 220], [22278, 220], [22578, 220], [22878, 219], [23178, 219], [23478, 218], [23779, 217], [24079, 216], [24379, 216], [24679, 216], [24979, 215], [25280, 214], [25580, 213], [25880, 213], [26180, 212], [26480, 212], [26781, 210], [27081, 210], [27381, 210], [27681, 209], [27982, 208], [28282, 207], [28582, 207], [28882, 206], [29182, 206], [29483, 204], [29783, 204], [30083, 204], [30383, 203], [30684, 202], [30984, 201], [31284, 201], [31584, 200], [31884, 200], [32185, 198], [32485, 198], [32785, 198], [33085, 197], [33385, 197], [33686, 195], [33986, 195], [34286, 194], [34586, 194], [34887, 192], [35187, 192], [35487, 192], [35787, 191], [36087, 191], [36388, 189], [36688, 189], [36988, 188], [37288, 188], [37588, 188], [37889, 186], [38189, 186], [38489, 185], [38789, 185], [39090, 183], [39390, 183], [39690, 182], [39990, 182], [40290, 182], [40591, 180], [40891, 180], [41191, 179], [41491, 179], [41792, 177], [42092, 177], [42392, 176], [42692, 176], [42992, 175]], "point": [187, 80]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+02.00|+00.10|+00.08"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [140, 95, 156, 115], "mask": [[28347, 3], [28645, 7], [28944, 10], [29243, 12], [29542, 13], [29841, 15], [30141, 15], [30440, 17], [30740, 17], [31040, 17], [31340, 17], [31640, 17], [31940, 17], [32240, 17], [32540, 17], [32841, 16], [33141, 15], [33442, 13], [33743, 11], [34044, 9], [34346, 5]], "point": [148, 104]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.83|+00.90|-01.35"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 297, 268], "mask": [[5496, 165], [5795, 167], [6095, 168], [6394, 170], [6694, 171], [6993, 173], [7293, 174], [7593, 174], [7892, 176], [8192, 177], [8491, 179], [8791, 180], [9091, 181], [9390, 183], [9690, 184], [9989, 185], [10289, 186], [10588, 188], [10888, 189], [11188, 190], [11487, 192], [11787, 193], [12086, 195], [12386, 195], [12685, 197], [12985, 198], [13285, 199], [13584, 201], [13884, 202], [14183, 204], [14483, 204], [14782, 206], [15082, 207], [15382, 208], [15681, 210], [15981, 210], [16267, 224], [16567, 226], [16866, 228], [17165, 230], [17465, 231], [17764, 233], [18064, 233], [18363, 235], [18662, 236], [18962, 236], [19261, 237], [19561, 237], [19860, 238], [20160, 238], [20459, 239], [20759, 239], [21058, 240], [21358, 100], [21467, 131], [21657, 99], [21769, 129], [21957, 98], [22070, 128], [22256, 98], [22371, 127], [22555, 98], [22672, 126], [22855, 97], [22973, 124], [23154, 98], [23274, 123], [23454, 97], [23574, 122], [23753, 98], [23875, 121], [24053, 98], [24175, 120], [24352, 99], [24475, 120], [24652, 99], [24775, 120], [24951, 100], [25075, 119], [25251, 100], [25375, 119], [25550, 101], [25674, 119], [25850, 101], [25974, 119], [26149, 102], [26274, 118], [26449, 103], [26574, 118], [26748, 104], [26873, 118], [27048, 105], [27173, 118], [27347, 106], [27472, 119], [27647, 107], [27772, 118], [27946, 109], [28071, 119], [28246, 109], [28371, 118], [28545, 111], [28669, 120], [28845, 112], [28968, 120], [29144, 115], [29266, 122], [29444, 243], [29743, 244], [30043, 244], [30342, 244], [30642, 244], [30941, 244], [31241, 244], [31540, 244], [31840, 244], [32139, 244], [32439, 244], [32738, 245], [33038, 244], [33337, 245], [33637, 244], [33936, 245], [34236, 244], [34535, 245], [34835, 244], [35134, 245], [35434, 245], [35733, 245], [36033, 245], [36332, 245], [36632, 245], [36931, 245], [37230, 246], [37530, 246], [37829, 246], [38129, 246], [38428, 246], [38728, 246], [39027, 246], [39327, 246], [39626, 246], [39926, 246], [40225, 247], [40525, 246], [40824, 247], [41124, 74], [41329, 41], [41423, 75], [41630, 40], [41723, 75], [41930, 39], [42022, 75], [42230, 39], [42322, 75], [42531, 37], [42621, 76], [42831, 37], [42921, 76], [43132, 35], [43220, 77], [43520, 76], [43819, 77], [44119, 77], [44418, 78], [44718, 77], [45017, 78], [45317, 78], [45616, 79], [45916, 79], [46215, 79], [46515, 79], [46814, 80], [47114, 80], [47413, 81], [47713, 80], [48012, 81], [48312, 81], [48611, 82], [48911, 82], [49210, 82], [49510, 82], [49809, 83], [50109, 83], [50408, 83], [50708, 83], [51007, 84], [51307, 84], [51606, 85], [51905, 85], [52205, 85], [52504, 86], [52804, 86], [53103, 87], [53403, 86], [53702, 87], [54002, 87], [54301, 88], [54601, 88], [54900, 88], [55200, 88], [55500, 88], [55800, 88], [56100, 87], [56400, 87], [56700, 87], [57000, 87], [57300, 87], [57600, 86], [57900, 86], [58200, 86], [58500, 86], [58800, 86], [59100, 85], [59400, 85], [59700, 85], [60000, 85], [60300, 85], [60600, 84], [60900, 84], [61200, 84], [61500, 84], [61800, 83], [62100, 83], [62400, 83], [62700, 83], [63000, 83], [63300, 82], [63600, 82], [63900, 82], [64200, 82], [64501, 81], [64801, 80], [65102, 79], [65403, 78], [65704, 77], [66005, 76], [66305, 75], [66606, 74], [66907, 73], [67208, 72], [67508, 71], [67809, 70], [68110, 69], [68411, 68], [68711, 68], [69012, 66], [69313, 65], [69614, 64], [69915, 63], [70215, 63], [70516, 61], [70817, 60], [71118, 59], [71418, 59], [71719, 58], [72020, 56], [72321, 55], [72621, 55], [72922, 54], [73223, 52], [73524, 51], [73824, 51], [74125, 50], [74426, 49], [74727, 47], [75028, 46], [75328, 46], [75629, 45], [75930, 44], [76231, 42], [76531, 42], [76832, 41], [77133, 40], [77434, 39], [77734, 38], [78035, 40], [78336, 41], [78637, 41], [78938, 33], [78972, 6], [79238, 33], [79273, 5], [79540, 31], [79574, 2], [79844, 27], [80148, 22]], "point": [148, 136]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+02.00|+00.10|+00.08", "placeStationary": true, "receptacleObjectId": "GarbageCan|+01.92|+00.00|+00.14"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [136, 148, 216, 245], "mask": [[44275, 17], [44549, 49], [44847, 53], [45144, 59], [45443, 61], [45742, 63], [46041, 65], [46340, 67], [46639, 69], [46939, 69], [47239, 70], [47539, 70], [47839, 70], [48138, 72], [48438, 72], [48738, 72], [49038, 72], [49338, 72], [49638, 72], [49938, 72], [50238, 73], [50538, 73], [50838, 73], [51138, 73], [51438, 73], [51738, 73], [52037, 74], [52337, 74], [52637, 75], [52937, 75], [53237, 75], [53537, 75], [53837, 75], [54137, 75], [54437, 75], [54737, 76], [55037, 76], [55337, 76], [55637, 76], [55937, 76], [56237, 76], [56537, 76], [56837, 77], [57137, 77], [57437, 77], [57737, 77], [58037, 77], [58337, 77], [58637, 77], [58937, 77], [59237, 78], [59537, 78], [59837, 78], [60137, 78], [60437, 78], [60737, 23], [60766, 49], [61037, 20], [61068, 47], [61337, 19], [61370, 45], [61637, 18], [61671, 45], [61937, 17], [61971, 45], [62237, 16], [62272, 44], [62537, 16], [62572, 44], [62837, 16], [62872, 44], [63136, 17], [63173, 43], [63436, 18], [63472, 44], [63736, 18], [63771, 45], [64036, 20], [64070, 46], [64336, 23], [64368, 49], [64636, 81], [64936, 81], [65236, 81], [65536, 81], [65836, 81], [66136, 81], [66436, 81], [66736, 81], [67037, 17], [67077, 40], [67337, 17], [67377, 40], [67637, 17], [67677, 40], [67937, 17], [67977, 40], [68237, 17], [68277, 39], [68538, 17], [68577, 39], [68838, 77], [69139, 76], [69440, 74], [69741, 73], [70041, 72], [70342, 70], [70644, 66], [70946, 63], [71253, 54], [71557, 47], [71859, 35], [72161, 30], [72462, 27], [72763, 24], [73064, 21], [73365, 18]], "point": [176, 195]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan5", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -1.25, "y": 0.9009992, "z": 1.5}, "object_poses": [{"objectName": "Potato_8a9da01b", "position": {"x": -0.0310187489, "y": 0.793841541, "z": -2.008}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_8a9da01b", "position": {"x": -1.41390681, "y": 0.943985343, "z": -0.353479475}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_43faf0e4", "position": {"x": -1.04562736, "y": 1.1684401, "z": 0.485696}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_43faf0e4", "position": {"x": -0.205553234, "y": 0.95044, "z": 0.241651565}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_42dba209", "position": {"x": 1.84506106, "y": 0.06268937, "z": 0.166980162}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_42dba209", "position": {"x": 3.105321, "y": 0.8793954, "z": 0.1142299}, "rotation": {"x": 3.415104e-06, "y": 180.0003, "z": -3.4150853e-06}}, {"objectName": "ButterKnife_1a7bf448", "position": {"x": 0.00897419453, "y": 0.9135421, "z": 0.241651565}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_1a7bf448", "position": {"x": -0.993096, "y": 0.911342, "z": -1.89137816}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_b40d1b1e", "position": {"x": 1.63, "y": 0.118561387, "z": -1.332}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_b40d1b1e", "position": {"x": 1.89104831, "y": 1.42946327, "z": -0.458186448}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Spatula_ef52a4ea", "position": {"x": -1.40436709, "y": 0.9262998, "z": -1.384933}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Fork_66b6c857", "position": {"x": 0.0713647, "y": 1.13204873, "z": 0.706205964}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_66b6c857", "position": {"x": -0.290060669, "y": 0.7639047, "z": -2.06327963}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c0097cec", "position": {"x": -1.3173, "y": 1.65687013, "z": -1.78984189}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_84756f6d", "position": {"x": -0.993096, "y": 0.9348276, "z": -1.722563}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_84756f6d", "position": {"x": -0.623526633, "y": 0.7179415, "z": -1.9351815}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_74fc855a", "position": {"x": -1.33056784, "y": 0.963060439, "z": -0.170473665}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_74fc855a", "position": {"x": -0.152033746, "y": 1.18106067, "z": 0.529798}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_0d0ef808", "position": {"x": 1.945802, "y": 1.50974894, "z": -0.737496257}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Lettuce_9396b7e6", "position": {"x": 1.41928542, "y": 0.98780483, "z": -2.014}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_3526507b", "position": {"x": 1.78121793, "y": 1.06404126, "z": -1.29777479}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Apple_3526507b", "position": {"x": -1.37952316, "y": 0.959888458, "z": -2.014}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_14c38bc8", "position": {"x": 1.89104807, "y": 1.09839845, "z": -0.5512897}, "rotation": {"x": 0.0, "y": 0.000169038554, "z": 0.0}}, {"objectName": "Knife_84756f6d", "position": {"x": -1.27819037, "y": 0.9370277, "z": 0.1888}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_4de763d6", "position": {"x": 1.4690001, "y": 1.65055108, "z": -2.12806129}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_66b6c857", "position": {"x": -0.117366061, "y": 0.7639047, "z": -1.95272}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_11735285", "position": {"x": 0.1015501, "y": 0.750666142, "z": -1.9382143}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_43faf0e4", "position": {"x": -0.26475, "y": 0.153161764, "z": 0.163000017}, "rotation": {"x": 7.01670955e-15, "y": 180.0, "z": 7.01670955e-15}}, {"objectName": "Apple_3526507b", "position": {"x": -0.420080632, "y": 0.9620885, "z": 0.08309689}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_7c4fce22", "position": {"x": -1.0561, "y": 0.956400037, "z": -1.127}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_777c51a0", "position": {"x": 1.87392831, "y": 1.66184461, "z": -1.197}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_8a9da01b", "position": {"x": 1.96346951, "y": 0.0936105251, "z": 0.166980162}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_b40d1b1e", "position": {"x": -1.2899, "y": 0.957, "z": -1.02}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_0d0ef808", "position": {"x": -1.148, "y": 0.9816431, "z": -0.193000019}, "rotation": {"x": 2.95911013e-06, "y": 1.50147389e-06, "z": -1.9953e-05}}, {"objectName": "Plate_c0097cec", "position": {"x": 1.914, "y": 1.65075445, "z": -1.60664058}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_ef52a4ea", "position": {"x": -1.15760458, "y": 0.9262998, "z": -1.722563}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_e7f3845e", "position": {"x": -1.04562736, "y": 1.23578548, "z": 0.618002}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Vase_f4e7dd3c", "position": {"x": -1.27819037, "y": 0.9198706, "z": 0.294503123}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_c00db6cc", "position": {"x": 2.002939, "y": 0.096799016, "z": 0.08003965}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_99ef625f", "position": {"x": -1.15760458, "y": 0.9113983, "z": -1.63815558}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_52c85e6d", "position": {"x": -1.04562736, "y": 1.12780631, "z": 0.529798}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_9396b7e6", "position": {"x": 1.945803, "y": 0.8936437, "z": -0.458185971}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "PepperShaker_b0cc376f", "position": {"x": -0.822229, "y": 1.12780631, "z": 0.485696}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_1a7bf448", "position": {"x": -0.375432163, "y": 1.13154221, "z": 0.5739}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_42dba209", "position": {"x": -0.5775266, "y": 0.691602468, "z": 0.146518633}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Vase_3478a6e4", "position": {"x": 2.9006145, "y": 0.8775301, "z": 0.168567464}, "rotation": {"x": 3.415104e-06, "y": 180.0003, "z": -3.4150853e-06}}, {"objectName": "Spoon_80376f38", "position": {"x": -1.06366289, "y": 0.9146294, "z": 0.241651565}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_74fc855a", "position": {"x": 2.05531216, "y": 1.14834952, "z": -0.2719805}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Mug_8066794b", "position": {"x": -1.3173, "y": 1.653428, "z": -1.60041583}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Statue_bb70ba4e", "position": {"x": 2.69476676, "y": 0.5591226, "z": 0.142100289}, "rotation": {"x": -1.20782931e-06, "y": 90.00033, "z": -1.20783614e-06}}], "object_toggles": [], "random_seed": 2401372567, "scene_num": 5}, "task_id": "trial_T20190906_190707_776806", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1RLO9LNUJIW5S_3NKQQ8O391MZY95MYO82N55FN9GDUL", "high_descs": ["Turn right and walk until you are even with the door on the left then turn right and walk a few steps so you are even with the blue garbage and walk over to it so you are facing it lengthwise.", "Pick up the egg that's in the garbage.", "Turn around and walk over to the microwave near the fridge.", "Cook the egg in the microwave then take it back out.", "Walk back over to the blue garbage.", "Put the egg in the top left corner of the interior of the garbage."], "task_desc": "Put a cooked egg in the garbage.", "votes": [1, 1, 1]}, {"assignment_id": "AM2KK02JXXW48_3WI0P0II6497IHD5UUDXIUIVGDIRD7", "high_descs": ["Move to the trash can.", "Take the egg out of the trash can.", "Bring the egg to the microwave. ", "Heat the egg in the the microwave for a few seconds.", "Bring the egg back to the trash can.", "Put the egg in the trash can. "], "task_desc": "Throw out the heated egg in the trash can. ", "votes": [1, 1, 1]}, {"assignment_id": "A1GVTH5YS3WOK0_3IO1LGZLKCE2F3JULYZJL7DY9VV860", "high_descs": ["Turn right and head towards the door on the left and then turns right towards the blue bin on the floor.", "Pick up the egg from the bin.", "Turn around towards the counter, turn right towards the microwave on the counter.", "Place the egg in the microwave to warm it up and take it out of the microwave.", "Turn right, walk past the fridge and then turn left to face the blue bin on the floor.", "Place the egg inside the bin."], "task_desc": "To heat up the egg inside the blue bin.", "votes": [1, 0, 1]}]}}