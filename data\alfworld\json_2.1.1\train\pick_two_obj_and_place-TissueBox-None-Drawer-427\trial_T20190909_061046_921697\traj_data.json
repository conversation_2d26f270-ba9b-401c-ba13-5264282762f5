{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000152.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000153.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000154.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000155.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000156.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000157.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000158.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000159.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000160.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000161.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000162.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000163.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000164.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000165.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000166.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000167.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000168.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000169.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000170.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000171.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000172.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000173.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000174.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000175.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000176.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000177.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000178.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000179.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000180.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000181.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000226.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000227.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000228.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000229.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000230.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000231.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000232.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000233.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000234.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000235.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000236.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000237.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000238.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000239.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000240.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000241.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000242.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000243.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000244.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000245.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000246.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000247.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000248.png", "low_idx": 35}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "TissueBox", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-7|0|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["tissuebox"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["TissueBox", [-6.660128, -6.660128, -3.0234844, -3.0234844, 2.816065072, 2.816065072]], "coordinateReceptacleObjectId": ["CounterTop", [-11.2, -11.2, -2.376, -2.376, 0.004, 0.004]], "forceVisible": true, "objectId": "TissueBox|-01.67|+00.70|-00.76"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-8|2|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["tissuebox", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["TissueBox", [-6.660128, -6.660128, -3.0234844, -3.0234844, 2.816065072, 2.816065072]], "coordinateReceptacleObjectId": ["Drawer", [-5.77998448, -5.77998448, -2.483496664, -2.483496664, 2.1270964, 2.1270964]], "forceVisible": true, "objectId": "TissueBox|-01.67|+00.70|-00.76", "receptacleObjectId": "Drawer|-01.44|+00.53|-00.62"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-8|0|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["tissuebox"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["TissueBox", [-7.5707426, -7.5707426, -2.1135288, -2.1135288, 2.816065072, 2.816065072]], "coordinateReceptacleObjectId": ["CounterTop", [-11.2, -11.2, -2.376, -2.376, 0.004, 0.004]], "forceVisible": true, "objectId": "TissueBox|-01.89|+00.70|-00.53"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-8|2|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["tissuebox", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["TissueBox", [-7.5707426, -7.5707426, -2.1135288, -2.1135288, 2.816065072, 2.816065072]], "coordinateReceptacleObjectId": ["Drawer", [-5.77998448, -5.77998448, -2.483496664, -2.483496664, 2.1270964, 2.1270964]], "forceVisible": true, "objectId": "TissueBox|-01.89|+00.70|-00.53", "receptacleObjectId": "Drawer|-01.44|+00.53|-00.62"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "TissueBox|-01.67|+00.70|-00.76"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [116, 142, 141, 185], "mask": [[42423, 2], [42720, 5], [42728, 14], [43020, 4], [43028, 14], [43320, 4], [43328, 14], [43620, 4], [43628, 14], [43919, 5], [43928, 14], [44219, 5], [44228, 14], [44519, 5], [44528, 14], [44819, 5], [44828, 14], [45119, 5], [45128, 14], [45419, 4], [45428, 14], [45719, 4], [45728, 14], [46019, 4], [46028, 14], [46319, 4], [46329, 13], [46618, 5], [46629, 13], [46918, 5], [46929, 13], [47218, 5], [47230, 12], [47518, 6], [47530, 12], [47818, 6], [47830, 12], [48118, 5], [48130, 12], [48418, 6], [48430, 12], [48718, 6], [48730, 12], [49017, 7], [49031, 11], [49317, 7], [49332, 10], [49617, 7], [49632, 10], [49917, 7], [49932, 10], [50217, 7], [50232, 10], [50517, 7], [50532, 10], [50817, 7], [50832, 10], [51117, 7], [51132, 10], [51417, 7], [51432, 10], [51716, 8], [51732, 10], [52016, 8], [52032, 10], [52317, 7], [52332, 10], [52617, 7], [52632, 10], [52917, 7], [52932, 10], [53217, 7], [53233, 9], [53517, 7], [53533, 9], [53817, 7], [53833, 9], [54117, 7], [54133, 8], [54418, 6], [54433, 8], [54718, 6], [54733, 8], [55018, 6], [55033, 8], [55318, 6], [55333, 8]], "point": [130, 162]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-01.44|+00.53|-00.62"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 151, 90, 182], "mask": [[45000, 84], [45300, 84], [45600, 85], [45900, 85], [46200, 85], [46500, 85], [46800, 86], [47100, 86], [47400, 86], [47700, 86], [48000, 87], [48301, 86], [48601, 86], [48902, 85], [49203, 85], [49503, 85], [49804, 84], [50104, 84], [50405, 84], [50706, 83], [51006, 83], [51307, 82], [51607, 83], [51908, 82], [52209, 81], [52509, 82], [52810, 81], [53110, 81], [53411, 80], [53712, 79], [54012, 79], [54313, 78]], "point": [45, 165]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "TissueBox|-01.67|+00.70|-00.76", "placeStationary": true, "receptacleObjectId": "Drawer|-01.44|+00.53|-00.62"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 151, 88, 248], "mask": [[45001, 81], [45300, 83], [45600, 83], [45900, 83], [46200, 83], [46500, 84], [46800, 84], [47100, 84], [47400, 84], [47700, 85], [48000, 85], [48300, 85], [48600, 85], [48900, 86], [49200, 86], [49500, 86], [49800, 86], [50100, 87], [50400, 87], [50700, 87], [51000, 87], [51300, 88], [51600, 88], [51900, 88], [52200, 89], [52500, 89], [52800, 88], [53100, 88], [53400, 88], [53700, 88], [54000, 87], [54300, 87], [54600, 87], [54900, 87], [55200, 87], [55500, 86], [55800, 86], [56100, 86], [56400, 86], [56700, 86], [57000, 85], [57300, 85], [57600, 85], [57900, 85], [58200, 84], [58500, 84], [58800, 84], [59100, 84], [59400, 84], [59700, 83], [60000, 83], [60300, 83], [60600, 83], [60900, 83], [61200, 82], [61500, 82], [61800, 82], [62100, 82], [62400, 81], [62700, 81], [63000, 81], [63300, 81], [63600, 81], [63900, 80], [64200, 80], [64500, 80], [64800, 80], [65100, 79], [65400, 79], [65700, 79], [66000, 79], [66300, 79], [66600, 78], [66900, 78], [67200, 78], [67500, 78], [67800, 78], [68100, 77], [68400, 77], [68700, 77], [69000, 77], [69300, 76], [69600, 76], [69900, 76], [70200, 76], [70500, 76], [70800, 75], [71100, 75], [71400, 76], [71700, 76], [72000, 75], [72300, 75], [72600, 75], [72900, 75], [73200, 75], [73500, 74], [73800, 74], [74100, 74]], "point": [44, 198]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-01.44|+00.53|-00.62"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 151, 88, 248], "mask": [[45001, 81], [45300, 83], [45600, 83], [45900, 83], [46200, 83], [46500, 84], [46800, 84], [47100, 84], [47400, 84], [47700, 85], [48000, 85], [48300, 85], [48600, 85], [48900, 86], [49200, 86], [49500, 86], [49800, 86], [50100, 87], [50400, 87], [50700, 87], [51000, 87], [51300, 88], [51600, 17], [51636, 52], [51900, 15], [51936, 52], [52200, 14], [52237, 52], [52500, 13], [52537, 52], [52800, 13], [52838, 50], [53100, 13], [53138, 50], [53400, 12], [53439, 49], [53700, 12], [53739, 49], [54000, 12], [54040, 47], [54300, 12], [54340, 47], [54600, 13], [54640, 47], [54900, 12], [54940, 47], [55200, 12], [55239, 48], [55500, 11], [55539, 47], [55800, 11], [55838, 48], [56100, 10], [56138, 48], [56400, 10], [56438, 48], [56700, 10], [56737, 49], [57000, 9], [57037, 48], [57300, 9], [57336, 49], [57600, 8], [57636, 49], [57900, 8], [57936, 49], [58200, 7], [58235, 49], [58500, 7], [58535, 49], [58800, 6], [58835, 49], [59100, 6], [59134, 50], [59400, 5], [59434, 50], [59700, 5], [59733, 50], [60000, 4], [60033, 50], [60300, 4], [60333, 50], [60600, 5], [60632, 51], [60900, 6], [60932, 51], [61200, 7], [61232, 50], [61500, 7], [61531, 51], [61800, 8], [61831, 51], [62100, 9], [62130, 52], [62400, 9], [62430, 51], [62700, 10], [62730, 51], [63000, 81], [63300, 81], [63600, 81], [63900, 80], [64200, 80], [64500, 80], [64800, 80], [65100, 79], [65400, 79], [65700, 79], [66000, 79], [66300, 79], [66600, 78], [66900, 78], [67200, 78], [67500, 78], [67800, 78], [68100, 77], [68400, 77], [68700, 77], [69000, 77], [69300, 76], [69600, 76], [69900, 76], [70200, 76], [70500, 76], [70800, 75], [71100, 75], [71400, 76], [71700, 76], [72000, 77], [72300, 77], [72600, 78], [72900, 78], [73200, 78], [73500, 78], [73800, 78], [74100, 78]], "point": [44, 198]}}, "high_idx": 3}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "TissueBox|-01.89|+00.70|-00.53"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [105, 115, 135, 165], "mask": [[34309, 26], [34609, 26], [34909, 26], [35209, 26], [35509, 26], [35809, 26], [36109, 26], [36409, 26], [36708, 27], [37008, 28], [37308, 28], [37608, 28], [37908, 28], [38208, 28], [38508, 28], [38808, 28], [39108, 28], [39408, 28], [39708, 27], [40007, 28], [40307, 28], [40607, 28], [40907, 28], [41207, 28], [41507, 28], [41807, 28], [42107, 28], [42407, 28], [42707, 28], [43006, 29], [43306, 29], [43606, 29], [43906, 29], [44206, 29], [44506, 29], [44806, 29], [45106, 29], [45406, 29], [45706, 29], [46005, 30], [46305, 30], [46606, 29], [46906, 29], [47206, 29], [47506, 29], [47807, 28], [48107, 28], [48407, 27], [48708, 26], [49008, 26], [49308, 26]], "point": [120, 139]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-01.44|+00.53|-00.62"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 151, 90, 182], "mask": [[45000, 84], [45300, 84], [45600, 85], [45900, 85], [46200, 85], [46500, 85], [46800, 86], [47100, 86], [47400, 86], [47700, 86], [48000, 87], [48301, 86], [48601, 86], [48902, 85], [49203, 85], [49503, 85], [49804, 84], [50104, 84], [50405, 84], [50706, 83], [51006, 83], [51307, 82], [51607, 83], [51908, 82], [52209, 81], [52509, 82], [52810, 81], [53110, 81], [53411, 80], [53712, 79], [54012, 79], [54313, 78]], "point": [45, 165]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "TissueBox|-01.89|+00.70|-00.53", "placeStationary": true, "receptacleObjectId": "Drawer|-01.44|+00.53|-00.62"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 151, 88, 248], "mask": [[45001, 81], [45300, 83], [45600, 83], [45900, 83], [46200, 83], [46500, 84], [46800, 84], [47100, 84], [47400, 84], [47700, 85], [48000, 85], [48300, 85], [48600, 85], [48900, 86], [49200, 86], [49500, 86], [49800, 86], [50100, 87], [50400, 87], [50700, 87], [51000, 87], [51300, 88], [51600, 17], [51636, 52], [51900, 15], [51936, 52], [52200, 14], [52237, 52], [52500, 13], [52537, 52], [52800, 13], [52838, 50], [53100, 13], [53138, 50], [53400, 12], [53439, 49], [53700, 12], [53739, 49], [54000, 12], [54040, 47], [54300, 12], [54340, 47], [54600, 13], [54640, 47], [54900, 12], [54940, 47], [55200, 12], [55239, 48], [55500, 11], [55539, 47], [55800, 11], [55838, 48], [56100, 10], [56138, 48], [56400, 10], [56438, 48], [56700, 10], [56737, 49], [57000, 9], [57037, 48], [57300, 9], [57336, 49], [57600, 8], [57636, 49], [57900, 8], [57936, 49], [58200, 7], [58235, 49], [58500, 7], [58535, 49], [58800, 6], [58835, 49], [59100, 6], [59134, 50], [59400, 5], [59434, 50], [59700, 5], [59733, 50], [60000, 4], [60033, 50], [60300, 4], [60333, 50], [60600, 5], [60632, 51], [60900, 6], [60932, 51], [61200, 7], [61232, 50], [61500, 7], [61531, 51], [61800, 8], [61831, 51], [62100, 9], [62130, 52], [62400, 9], [62430, 51], [62700, 10], [62730, 51], [63000, 81], [63300, 80], [63600, 79], [63900, 79], [64200, 79], [64500, 79], [64800, 79], [65100, 79], [65400, 78], [65700, 78], [66000, 78], [66300, 78], [66600, 78], [66900, 78], [67200, 78], [67500, 77], [67800, 77], [68100, 77], [68400, 77], [68700, 77], [69000, 77], [69300, 76], [69600, 76], [69900, 76], [70200, 76], [70500, 76], [70800, 75], [71100, 75], [71400, 76], [71700, 76], [72000, 75], [72300, 75], [72600, 75], [72900, 75], [73200, 75], [73500, 75], [73800, 75], [74100, 74]], "point": [44, 198]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-01.44|+00.53|-00.62"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 151, 88, 248], "mask": [[45001, 81], [45300, 83], [45600, 83], [45900, 83], [46200, 83], [46500, 84], [46800, 84], [47100, 84], [47400, 84], [47700, 85], [48000, 85], [48300, 85], [48600, 85], [48900, 86], [49200, 86], [49500, 86], [49800, 86], [50100, 87], [50400, 87], [50700, 87], [51000, 87], [51300, 88], [51600, 17], [51636, 9], [51663, 25], [51900, 15], [51936, 8], [51964, 24], [52200, 14], [52237, 6], [52264, 25], [52500, 13], [52537, 5], [52564, 25], [52800, 13], [52838, 5], [52865, 23], [53100, 13], [53138, 5], [53165, 23], [53400, 12], [53439, 3], [53466, 22], [53700, 12], [53739, 3], [53766, 22], [54000, 12], [54040, 2], [54066, 21], [54300, 12], [54340, 2], [54367, 20], [54600, 13], [54640, 1], [54666, 21], [54900, 12], [54940, 1], [54966, 21], [55200, 12], [55239, 1], [55266, 21], [55500, 11], [55539, 1], [55565, 21], [55800, 11], [55838, 2], [55865, 21], [56100, 10], [56138, 1], [56165, 21], [56400, 10], [56438, 1], [56465, 21], [56700, 10], [56737, 2], [56764, 22], [57000, 9], [57037, 1], [57064, 21], [57300, 9], [57336, 2], [57364, 21], [57600, 8], [57636, 1], [57663, 22], [57900, 8], [57936, 1], [57963, 22], [58200, 7], [58235, 2], [58263, 21], [58500, 7], [58535, 1], [58563, 21], [58800, 6], [58835, 1], [58862, 22], [59100, 6], [59134, 2], [59162, 22], [59400, 5], [59434, 1], [59462, 22], [59700, 5], [59733, 2], [59761, 22], [60000, 4], [60033, 1], [60061, 22], [60300, 4], [60333, 2], [60361, 22], [60600, 5], [60632, 3], [60661, 22], [60900, 6], [60932, 4], [60960, 23], [61200, 7], [61232, 4], [61260, 22], [61500, 7], [61531, 6], [61560, 22], [61800, 8], [61831, 6], [61859, 23], [62100, 9], [62130, 8], [62159, 23], [62400, 9], [62430, 8], [62459, 22], [62700, 10], [62730, 9], [62759, 22], [63000, 81], [63300, 81], [63600, 81], [63900, 80], [64200, 80], [64500, 80], [64800, 80], [65100, 79], [65400, 79], [65700, 79], [66000, 79], [66300, 79], [66600, 78], [66900, 78], [67200, 78], [67500, 78], [67800, 78], [68100, 77], [68400, 77], [68700, 77], [69000, 77], [69300, 76], [69600, 76], [69900, 76], [70200, 76], [70500, 76], [70800, 75], [71100, 75], [71400, 76], [71700, 76], [72000, 77], [72300, 77], [72600, 78], [72900, 78], [73200, 78], [73500, 78], [73800, 78], [74100, 78]], "point": [36, 194]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan427", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -1.0, "y": 0.902041554, "z": 1.25}, "object_poses": [{"objectName": "Cloth_3b9d62d5", "position": {"x": -1.94297791, "y": 0.17483151, "z": -0.683939934}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "TissueBox_0568c766", "position": {"x": -1.665032, "y": 0.704016268, "z": -0.7558711}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "TissueBox_0568c766", "position": {"x": -1.89268565, "y": 0.704016268, "z": -0.5283822}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "TissueBox_0568c766", "position": {"x": -2.12033939, "y": 0.704016268, "z": -0.528381944}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "SprayBottle_13477bc4", "position": {"x": -1.52785861, "y": 0.0257805288, "z": 2.08790851}, "rotation": {"x": 0.0, "y": 179.999863, "z": 0.0}}, {"objectName": "Candle_014ddfaf", "position": {"x": -1.30014575, "y": 0.4412011, "z": -0.6353395}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "SoapBar_1b2cf64e", "position": {"x": -1.79812777, "y": 0.4416263, "z": -0.7325411}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "SoapBar_1b2cf64e", "position": {"x": -1.94297791, "y": 0.4416263, "z": -0.732540965}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "DishSponge_b55ab5e2", "position": {"x": -2.74311376, "y": 0.443120778, "z": -0.5381362}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "DishSponge_b55ab5e2", "position": {"x": -3.60287166, "y": 0.0406776667, "z": 0.595}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "HandTowel_a528e9d7", "position": {"x": -2.576, "y": 1.683, "z": 2.2447}, "rotation": {"x": 0.0, "y": -6.83018834e-06, "z": 0.0}}, {"objectName": "Towel_08b02c7c", "position": {"x": -3.66, "y": 1.567, "z": -0.459400028}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Candle_014ddfaf", "position": {"x": -1.74984431, "y": 0.442385077, "z": -0.635339}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "HandTowel_a528e9d7", "position": {"x": -1.275, "y": 1.683, "z": 2.2444}, "rotation": {"x": 0.0, "y": -6.83018834e-06, "z": 0.0}}, {"objectName": "SoapBar_1b2cf64e", "position": {"x": -3.51652431, "y": 0.0391832, "z": 0.224625111}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_d548da56", "position": {"x": -1.38050878, "y": 0.695359349, "z": -0.751498342}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plunger_e84c4ed6", "position": {"x": -1.08817315, "y": 0.0005790219, "z": -0.5260009}, "rotation": {"x": -0.00118385791, "y": 0.000438582065, "z": 0.0007821216}}, {"objectName": "SprayBottle_13477bc4", "position": {"x": -1.665032, "y": 0.7003167, "z": -0.6421268}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "SoapBottle_f315e63f", "position": {"x": -0.669500947, "y": 0.9571862, "z": -0.722207963}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "TissueBox_0568c766", "position": {"x": -2.80330038, "y": 0.704016268, "z": -0.585253537}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "ToiletPaper_6b21fc4b", "position": {"x": -1.43737817, "y": 0.7003167, "z": -0.4715104}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "PaperTowelRoll_0da11a15", "position": {"x": -0.8570008, "y": 1.06032741, "z": -0.7757917}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "ScrubBrush_80c1e216", "position": {"x": -1.041, "y": 0.0010420084, "z": -0.707}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cloth_3b9d62d5", "position": {"x": -2.57564688, "y": 0.702243149, "z": -0.642125964}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "DishSponge_b55ab5e2", "position": {"x": -2.12033939, "y": 0.704880953, "z": -0.6421263}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}], "object_toggles": [], "random_seed": 1125873361, "scene_num": 427}, "task_id": "trial_T20190909_061046_921697", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A3F7G1FSFWQPLE_3EA3QWIZ4LM9RWSHM58KASJSR4QITG", "high_descs": ["Turn right and walk forward, then hang a left and walk up to the dresser.", "Pick up the leftmost box of tissues off of the dresser.", "Turn around and walk towards the sink, then turn left and take a step forward, turn left again to face the dresser.", "Open the upper left drawer of the dresser and put the box of tissues inside, then close the drawer.", "Walk forward up to the dresser.", "Pick up the leftmost box of tissues off of the dresser.", "Turn around and walk towards the sink, then turn around to face the dresser.", "Open the leftmost upper drawer of the dresser and put the box of tissues inside, then close the drawer."], "task_desc": "Move two boxes of tissues into a drawer.", "votes": [1, 1]}, {"assignment_id": "AO33H4GL9KZX9_3U0SRXB7CGWOPIQGFWGHFLERPIZNR0", "high_descs": ["Go forward to the wooden white dresser across the bathroom.", "Pick up the box of tissues behind the yellow spray bottle. ", "Move back from the dresser to be able to open a drawer.", "Place the box of tissues in the left top drawer. ", "Move forward to the dresser", "Pick up the box of tissues on the left.", "Move back from the dresser so a drawer can be opened.", "Place the second box of tissues in the drawer on the right of the first box."], "task_desc": "Move two boxes of tissues from the top of the dresser to the drawer.", "votes": [1, 1]}, {"assignment_id": "A1GVTH5YS3WOK0_3VE8AYVF8PO6JLY3IZR4GZADXZP8F2", "high_descs": ["Turn right, take a few steps, and then turn left towards the dresser.", "Pick up the box of tissue closest to the wall on the dresser.", "Take a step back from where you are.", "Open the top left drawer of the dresser and place the tissue box in it.", "Take a step forward from where you are at.", "Pick up the left tissue box on the dresser.", "Take a step back from where you are.", "Open the top left drawer of the dresser and place the tissue box in it."], "task_desc": "Place two tissue boxes from the dresser into the left drawer of the dresser.", "votes": [1, 1]}]}}