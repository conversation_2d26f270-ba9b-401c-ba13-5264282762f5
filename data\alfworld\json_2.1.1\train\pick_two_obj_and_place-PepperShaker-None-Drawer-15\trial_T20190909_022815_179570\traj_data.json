{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000034.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000047.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000048.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000049.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000187.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000188.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000189.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000190.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000191.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000192.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000193.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000194.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000238.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000239.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000240.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000241.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000242.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000243.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000244.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000245.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000246.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000247.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000248.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000249.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000250.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000251.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000252.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000253.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000254.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000255.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000256.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000257.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000258.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000259.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000260.png", "low_idx": 41}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON><PERSON><PERSON><PERSON>", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-5|5|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["peppershaker"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON><PERSON><PERSON><PERSON>", [-5.5650816, -5.5650816, 2.2586372, 2.2586372, 3.5511152, 3.5511152]], "coordinateReceptacleObjectId": ["DiningTable", [-5.38, -5.38, 1.332, 1.332, 0.0562074184, 0.0562074184]], "forceVisible": true, "objectId": "<PERSON><PERSON><PERSON><PERSON>|-01.39|+00.89|+00.56"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-9|5|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["peppershaker", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON><PERSON><PERSON><PERSON>", [-5.5650816, -5.5650816, 2.2586372, 2.2586372, 3.5511152, 3.5511152]], "coordinateReceptacleObjectId": ["Drawer", [-12.486696, -12.486696, 5.43067692, 5.43067692, 3.102284, 3.102284]], "forceVisible": true, "objectId": "<PERSON><PERSON><PERSON><PERSON>|-01.39|+00.89|+00.56", "receptacleObjectId": "Drawer|-03.12|+00.78|+01.36"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-9|0|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["peppershaker"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON><PERSON><PERSON><PERSON>", [-6.62204648, -6.62204648, -0.214339018, -0.214339018, 3.5511152, 3.5511152]], "coordinateReceptacleObjectId": ["DiningTable", [-5.38, -5.38, 1.332, 1.332, 0.0562074184, 0.0562074184]], "forceVisible": true, "objectId": "<PERSON><PERSON><PERSON><PERSON>|-01.66|+00.89|-00.05"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-9|5|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["peppershaker", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON><PERSON><PERSON><PERSON>", [-6.62204648, -6.62204648, -0.214339018, -0.214339018, 3.5511152, 3.5511152]], "coordinateReceptacleObjectId": ["Drawer", [-12.486696, -12.486696, 5.43067692, 5.43067692, 3.102284, 3.102284]], "forceVisible": true, "objectId": "<PERSON><PERSON><PERSON><PERSON>|-01.66|+00.89|-00.05", "receptacleObjectId": "Drawer|-03.12|+00.78|+01.36"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "<PERSON><PERSON><PERSON><PERSON>|-01.39|+00.89|+00.56"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [169, 153, 190, 181], "mask": [[45779, 6], [46077, 10], [46377, 11], [46676, 13], [46976, 14], [47276, 15], [47575, 15], [47875, 15], [48175, 15], [48474, 16], [48774, 15], [49073, 16], [49373, 15], [49673, 14], [49972, 14], [50272, 14], [50571, 14], [50871, 13], [51171, 13], [51471, 12], [51771, 11], [52071, 11], [52370, 11], [52669, 11], [52969, 9], [53269, 9], [53570, 7], [53871, 6], [54172, 4]], "point": [179, 166]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-03.12|+00.78|+01.36"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [97, 146, 256, 170], "mask": [[43597, 160], [43897, 160], [44197, 160], [44497, 160], [44797, 159], [45097, 159], [45398, 158], [45698, 157], [45998, 157], [46298, 156], [46598, 156], [46899, 155], [47199, 154], [47499, 154], [47799, 153], [48099, 153], [48400, 152], [48700, 151], [49000, 151], [49300, 150], [49600, 150], [49901, 148], [50201, 148], [50501, 148], [50801, 147]], "point": [176, 157]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "<PERSON><PERSON><PERSON><PERSON>|-01.39|+00.89|+00.56", "placeStationary": true, "receptacleObjectId": "Drawer|-03.12|+00.78|+01.36"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [87, 146, 275, 215], "mask": [[43598, 158], [43897, 159], [44197, 160], [44497, 160], [44797, 161], [45097, 161], [45396, 163], [45696, 163], [45996, 163], [46296, 164], [46596, 164], [46895, 166], [47195, 166], [47495, 166], [47795, 167], [48095, 167], [48394, 169], [48694, 169], [48994, 170], [49294, 170], [49594, 170], [49893, 172], [50193, 172], [50493, 173], [50793, 173], [51093, 173], [51392, 175], [51692, 175], [51992, 176], [52292, 176], [52592, 177], [52891, 178], [53191, 178], [53491, 179], [53791, 179], [54091, 180], [54390, 181], [54690, 181], [54990, 182], [55290, 182], [55590, 183], [55889, 184], [56189, 185], [56489, 185], [56789, 185], [57088, 187], [57387, 189], [57687, 189], [57988, 188], [58288, 187], [58588, 187], [58888, 186], [59189, 185], [59489, 184], [59789, 183], [60090, 182], [60390, 181], [60690, 181], [60990, 180], [61291, 178], [61591, 178], [61891, 177], [62192, 176], [62492, 175], [62792, 175], [63092, 174], [63393, 54], [63453, 112], [63693, 51], [63756, 109], [63993, 50], [64057, 107], [64294, 47], [64359, 105]], "point": [181, 179]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-03.12|+00.78|+01.36"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [87, 146, 275, 215], "mask": [[43598, 158], [43897, 159], [44197, 160], [44497, 160], [44797, 161], [45097, 161], [45396, 163], [45696, 163], [45996, 163], [46296, 164], [46596, 164], [46895, 166], [47195, 166], [47495, 166], [47795, 167], [48095, 167], [48394, 169], [48694, 169], [48994, 170], [49294, 170], [49594, 170], [49893, 172], [50193, 172], [50493, 173], [50793, 173], [51093, 173], [51392, 175], [51692, 175], [51992, 176], [52292, 149], [52443, 25], [52592, 147], [52745, 24], [52891, 148], [53046, 23], [53191, 147], [53347, 22], [53491, 146], [53647, 23], [53791, 145], [53946, 24], [54091, 145], [54246, 25], [54390, 145], [54546, 25], [54690, 144], [54846, 25], [54990, 144], [55146, 26], [55290, 143], [55445, 27], [55590, 143], [55745, 28], [55889, 143], [56045, 28], [56189, 143], [56345, 29], [56489, 185], [56789, 185], [57088, 187], [57387, 189], [57687, 189], [57988, 188], [58288, 187], [58588, 187], [58888, 186], [59189, 185], [59489, 184], [59789, 183], [60090, 182], [60390, 181], [60690, 181], [60990, 180], [61291, 178], [61591, 178], [61891, 177], [62192, 176], [62492, 175], [62792, 175], [63092, 174], [63393, 172], [63693, 172], [63993, 171], [64294, 170]], "point": [181, 179]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "<PERSON><PERSON><PERSON><PERSON>|-01.66|+00.89|-00.05"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [158, 78, 172, 109], "mask": [[23265, 4], [23563, 8], [23863, 8], [24163, 9], [24463, 8], [24763, 8], [25062, 9], [25362, 10], [25661, 11], [25961, 12], [26261, 12], [26560, 13], [26860, 13], [27160, 13], [27460, 13], [27760, 13], [28059, 14], [28359, 14], [28659, 14], [28959, 14], [29259, 14], [29558, 15], [29859, 14], [30159, 14], [30460, 13], [30760, 13], [31060, 13], [31360, 12], [31660, 12], [31960, 11], [32261, 9], [32565, 2]], "point": [165, 92]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-03.12|+00.78|+01.36"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [97, 146, 256, 170], "mask": [[43597, 160], [43897, 160], [44197, 160], [44497, 160], [44797, 159], [45097, 159], [45398, 158], [45698, 157], [45998, 157], [46298, 156], [46598, 156], [46899, 155], [47199, 154], [47499, 154], [47799, 153], [48099, 153], [48400, 152], [48700, 151], [49000, 151], [49300, 150], [49600, 150], [49901, 148], [50201, 148], [50501, 148], [50801, 147]], "point": [176, 157]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "<PERSON><PERSON><PERSON><PERSON>|-01.66|+00.89|-00.05", "placeStationary": true, "receptacleObjectId": "Drawer|-03.12|+00.78|+01.36"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [87, 146, 275, 215], "mask": [[43598, 158], [43897, 159], [44197, 160], [44497, 160], [44797, 161], [45097, 161], [45396, 163], [45696, 163], [45996, 163], [46296, 164], [46596, 164], [46895, 166], [47195, 166], [47495, 166], [47795, 167], [48095, 167], [48394, 169], [48694, 169], [48994, 170], [49294, 170], [49594, 170], [49893, 172], [50193, 172], [50493, 173], [50793, 173], [51093, 173], [51392, 175], [51692, 175], [51992, 176], [52292, 149], [52443, 25], [52592, 147], [52745, 24], [52891, 148], [53046, 23], [53191, 147], [53347, 22], [53491, 146], [53647, 23], [53791, 145], [53946, 24], [54091, 145], [54246, 25], [54390, 145], [54546, 25], [54690, 144], [54846, 25], [54990, 144], [55146, 26], [55290, 143], [55445, 27], [55590, 143], [55745, 28], [55889, 143], [56045, 28], [56189, 143], [56345, 29], [56489, 185], [56789, 185], [57088, 187], [57387, 189], [57687, 189], [57988, 188], [58288, 187], [58588, 187], [58888, 186], [59189, 185], [59489, 184], [59789, 183], [60090, 182], [60390, 181], [60690, 181], [60990, 180], [61291, 178], [61591, 178], [61891, 177], [62192, 176], [62492, 175], [62792, 175], [63092, 174], [63393, 172], [63693, 172], [63993, 171], [64294, 170]], "point": [181, 179]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-03.12|+00.78|+01.36"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [87, 146, 275, 215], "mask": [[43598, 158], [43897, 159], [44197, 160], [44497, 160], [44797, 161], [45097, 161], [45396, 163], [45696, 163], [45996, 163], [46296, 164], [46596, 164], [46895, 166], [47195, 166], [47495, 166], [47795, 167], [48095, 167], [48394, 169], [48694, 169], [48994, 170], [49294, 170], [49594, 170], [49893, 172], [50193, 172], [50493, 173], [50793, 173], [51093, 173], [51392, 175], [51692, 175], [51992, 176], [52292, 128], [52422, 19], [52443, 25], [52592, 126], [52724, 15], [52745, 24], [52891, 127], [53025, 14], [53046, 23], [53191, 127], [53325, 13], [53347, 22], [53491, 126], [53625, 12], [53647, 23], [53791, 125], [53925, 11], [53946, 24], [54091, 124], [54225, 11], [54246, 25], [54390, 125], [54525, 10], [54546, 25], [54690, 124], [54825, 9], [54846, 25], [54990, 124], [55125, 9], [55146, 26], [55290, 123], [55425, 8], [55445, 27], [55590, 123], [55725, 8], [55745, 28], [55889, 123], [56025, 7], [56045, 28], [56189, 123], [56324, 8], [56345, 29], [56489, 185], [56789, 185], [57088, 187], [57387, 189], [57687, 189], [57988, 188], [58288, 187], [58588, 187], [58888, 186], [59189, 185], [59489, 184], [59789, 183], [60090, 182], [60390, 181], [60690, 181], [60990, 180], [61291, 178], [61591, 178], [61891, 177], [62192, 176], [62492, 175], [62792, 175], [63092, 174], [63393, 172], [63693, 172], [63993, 171], [64294, 170]], "point": [181, 179]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan15", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -1.25, "y": 0.914953351, "z": 2.5}, "object_poses": [{"objectName": "DishSponge_73731673", "position": {"x": -1.22447133, "y": 0.0873552859, "z": 3.72432113}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "DishSponge_73731673", "position": {"x": -2.410143, "y": 0.080576, "z": 3.664132}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_657c04b2", "position": {"x": -2.50335121, "y": 0.915542, "z": 3.99884}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_657c04b2", "position": {"x": -3.55922174, "y": 0.9158421, "z": 1.5243156}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_2242b994", "position": {"x": -1.81549835, "y": 0.9164286, "z": 4.231027}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_2242b994", "position": {"x": -3.081253, "y": 0.916728735, "z": 1.44065785}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_58cf1806", "position": {"x": -1.78763235, "y": 0.892602, "z": 0.358577967}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_58cf1806", "position": {"x": -1.25914979, "y": 0.892602, "z": 0.04945588}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_46b03e61", "position": {"x": -3.4247, "y": 0.9668, "z": 2.3743}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_0c310241", "position": {"x": -1.25914979, "y": 0.9266359, "z": 0.3585779}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_0c310241", "position": {"x": -1.25604284, "y": 0.122954339, "z": 3.88426757}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_69fc67c7", "position": {"x": -3.442233, "y": 1.5460856, "z": 2.01631045}, "rotation": {"x": 0.0, "y": 89.99987, "z": 0.0}}, {"objectName": "Cup_69fc67c7", "position": {"x": -1.98548973, "y": 0.789343, "z": 3.737314}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_4b738b3b", "position": {"x": -1.3912704, "y": 0.9150003, "z": 0.3585779}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_0e75bb4f", "position": {"x": -3.65481567, "y": 0.9146891, "z": 1.357}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_d1c7827d", "position": {"x": -3.073865, "y": 0.07987678, "z": 1.4349438}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_94539d9a", "position": {"x": -1.3912704, "y": 0.8877788, "z": 0.5646593}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_94931e65", "position": {"x": -1.12702918, "y": 0.9775047, "z": 0.152496547}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_94931e65", "position": {"x": -1.3912704, "y": 0.9775047, "z": -0.0535848141}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_0af3d2bb", "position": {"x": -1.72990179, "y": 0.78455615, "z": 3.7}, "rotation": {"x": -1.40334191e-14, "y": 180.0, "z": 0.0}}, {"objectName": "Mug_0af3d2bb", "position": {"x": -0.556813061, "y": 1.26883721, "z": 4.09796524}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Apple_8bd3d3d2", "position": {"x": -0.440000236, "y": 1.60171008, "z": 4.033449}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Egg_0c310241", "position": {"x": -0.08956373, "y": 1.30900049, "z": 4.16863728}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Knife_4b738b3b", "position": {"x": -1.12702918, "y": 0.9150003, "z": 0.358577877}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_72bf05b8", "position": {"x": -2.80491138, "y": 0.916048467, "z": 3.78304267}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_0fed2699", "position": {"x": -3.36803436, "y": 0.9971903, "z": 1.27334213}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_08de48c7", "position": {"x": -1.16132855, "y": 0.120157927, "z": 3.85227823}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Plate_a5e935d4", "position": {"x": -3.156548, "y": 0.08544004, "z": 2.97624731}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_46b03e61", "position": {"x": -3.4247, "y": 0.9668, "z": 1.9715}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_a09ab4be", "position": {"x": -3.1481, "y": 0.9668, "z": 2.3743}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_2242b994", "position": {"x": -2.40283132, "y": 0.9164286, "z": 3.56724548}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_0e75bb4f", "position": {"x": -0.673624039, "y": 0.72177577, "z": 3.84322023}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Tomato_66cbdca0", "position": {"x": -1.65551162, "y": 0.9601291, "z": 0.564659357}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_69fc67c7", "position": {"x": -1.25914979, "y": 0.8912595, "z": 0.5646593}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_fd79f50b", "position": {"x": -3.36803436, "y": 0.930499852, "z": 3.66382527}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_da34a2c3", "position": {"x": -3.45919847, "y": 1.99503958, "z": 2.47921348}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_94931e65", "position": {"x": -1.759, "y": 0.9749, "z": 0.085}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_7e5e2cad", "position": {"x": -2.61477637, "y": 0.902653933, "z": 3.62041235}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_8bd3d3d2", "position": {"x": -3.47386122, "y": 1.60627162, "z": 2.17260146}, "rotation": {"x": 0.0, "y": 89.99987, "z": 0.0}}, {"objectName": "SaltShaker_d1c7827d", "position": {"x": -3.06812, "y": 0.7437031, "z": 2.75063}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_94539d9a", "position": {"x": -1.65551162, "y": 0.8877788, "z": -0.0535847545}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "ButterKnife_657c04b2", "position": {"x": -1.12702918, "y": 0.8915147, "z": 0.04945588}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_73731673", "position": {"x": -3.126574, "y": 0.745579839, "z": 1.43372357}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_58cf1806", "position": {"x": -3.36803436, "y": 0.9169294, "z": 1.44065785}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_0af3d2bb", "position": {"x": -0.556813, "y": 1.536737, "z": 4.102067}, "rotation": {"x": 0.0, "y": -0.000324434, "z": 0.0}}], "object_toggles": [], "random_seed": 3619094837, "scene_num": 15}, "task_id": "trial_T20190909_022815_179570", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A2A028LRDJB7ZB_3CFJTT4SXWHMTYVA925J5WRA27EI7J", "high_descs": ["Turn left walk straight to the table", "Pick up the salt shaker on the table", "Turn right then head to the drawer on the left counter", "Open the drawer and put in the salt shaker then close the cabinet", "Turn left then face the left side of the table", "Pick up the salt shaker beside the cabbage", "Turn left then head to the drawer on the counter", "Open the drawer and put in the salt shaker then close the cabinet"], "task_desc": "Put the two salt shaker in the drawer", "votes": [1, 1]}, {"assignment_id": "A3PPLDHC3CG0YN_3HYA4D452UAVX6WE2II368XABZX2FT", "high_descs": ["Turn left and walk to the white table. ", "Pick up the salt shaker to the right of the cup. ", "Walk to the counter to the left of the stove. ", "Open the drawer below the counter, put the salt in the drawer, and close the drawer. ", "Walk back over to the white table. ", "Pick up the salt shaker to the right of the head of lettuce. ", "Walk back over to the counter to the left of the stove. ", "Open the drawer below the counter, put the salt shaker inside the drawer to the left of the first one, and close the drawer. "], "task_desc": "To move two salt shakers to the drawer. ", "votes": [1, 1]}, {"assignment_id": "A20FCMWP43CVIU_3IJXV6UZ10AWBHFPIG3PADIA3J2IRC", "high_descs": ["walk to face white table ", "pick up salt shaker from table", "walk to face counter to left of stove", "put salt shaker in drawer below counter", "walk to face right side of white table ", "pick up salt shaker from table", "walk to face counter to left of stove", "put salt shaker in drawer below counter"], "task_desc": "put two salt shakers in a kitchen drawer", "votes": [1, 1]}]}}