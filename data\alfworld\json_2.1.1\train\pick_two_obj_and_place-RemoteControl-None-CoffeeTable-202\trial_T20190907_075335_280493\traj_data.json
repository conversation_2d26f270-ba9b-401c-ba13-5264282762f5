{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000187.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000188.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000189.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000190.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000191.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000192.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000193.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000194.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000195.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000196.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000255.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000256.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000257.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000258.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000259.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000260.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000261.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000262.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000263.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000264.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000265.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000266.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000267.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000268.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000269.png", "low_idx": 36}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "RemoteControl", "parent_target": "CoffeeTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sofa"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-6|5|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["remotecontrol"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["RemoteControl", [-6.40808344, -6.40808344, 1.884668468, 1.884668468, 2.189641, 2.189641]], "coordinateReceptacleObjectId": ["So<PERSON>", [-4.644, -4.644, 1.808, 1.808, 0.0001871585844, 0.0001871585844]], "forceVisible": true, "objectId": "RemoteControl|-01.60|+00.55|+00.47"}}, {"discrete_action": {"action": "GotoLocation", "args": ["coffeetable"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-6|5|0|60"}}, {"discrete_action": {"action": "PutObject", "args": ["remotecontrol", "coffeetable"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["RemoteControl", [-6.40808344, -6.40808344, 1.884668468, 1.884668468, 2.189641, 2.189641]], "coordinateReceptacleObjectId": ["CoffeeTable", [-6.94, -6.94, 9.444, 9.444, 0.012, 0.012]], "forceVisible": true, "objectId": "RemoteControl|-01.60|+00.55|+00.47", "receptacleObjectId": "CoffeeTable|-01.74|+00.00|+02.36"}}, {"discrete_action": {"action": "GotoLocation", "args": ["armchair"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-13|5|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["remotecontrol"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["RemoteControl", [-13.3015232, -13.3015232, 3.017972, 3.017972, 2.263171436, 2.263171436]], "coordinateReceptacleObjectId": ["ArmChair", [-12.3, -12.3, 1.492, 1.492, 1.890258312, 1.890258312]], "forceVisible": true, "objectId": "RemoteControl|-03.33|+00.57|+00.75"}}, {"discrete_action": {"action": "GotoLocation", "args": ["coffeetable"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-6|5|0|60"}}, {"discrete_action": {"action": "PutObject", "args": ["remotecontrol", "coffeetable"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["RemoteControl", [-13.3015232, -13.3015232, 3.017972, 3.017972, 2.263171436, 2.263171436]], "coordinateReceptacleObjectId": ["CoffeeTable", [-6.94, -6.94, 9.444, 9.444, 0.012, 0.012]], "forceVisible": true, "objectId": "RemoteControl|-03.33|+00.57|+00.75", "receptacleObjectId": "CoffeeTable|-01.74|+00.00|+02.36"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "RemoteControl|-01.60|+00.55|+00.47"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [164, 105, 176, 140], "mask": [[31364, 11], [31664, 11], [31964, 11], [32264, 11], [32564, 11], [32864, 11], [33164, 11], [33464, 11], [33764, 11], [34064, 12], [34364, 12], [34664, 12], [34964, 12], [35264, 12], [35565, 11], [35865, 11], [36165, 11], [36465, 11], [36765, 11], [37065, 11], [37365, 11], [37665, 11], [37965, 11], [38265, 11], [38565, 11], [38865, 11], [39165, 12], [39465, 12], [39765, 12], [40065, 12], [40365, 12], [40665, 12], [40965, 12], [41265, 12], [41565, 12], [41865, 12]], "point": [170, 121]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "RemoteControl|-01.60|+00.55|+00.47", "placeStationary": true, "receptacleObjectId": "CoffeeTable|-01.74|+00.00|+02.36"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [14, 48, 205, 166], "mask": [[14164, 35], [14234, 61], [14464, 35], [14534, 61], [14764, 35], [14834, 45], [14881, 1], [14883, 12], [15064, 35], [15134, 45], [15182, 14], [15364, 34], [15433, 45], [15483, 13], [15664, 34], [15733, 45], [15782, 14], [15964, 34], [16033, 44], [16081, 15], [16264, 34], [16333, 25], [16360, 18], [16381, 15], [16564, 34], [16633, 24], [16661, 17], [16681, 15], [16863, 35], [16933, 23], [16961, 17], [16980, 16], [17163, 34], [17233, 23], [17262, 15], [17280, 16], [17437, 1], [17463, 34], [17533, 23], [17562, 15], [17579, 18], [17736, 3], [17764, 33], [17833, 23], [17862, 14], [17879, 18], [18036, 4], [18065, 32], [18133, 23], [18161, 14], [18178, 19], [18336, 5], [18365, 32], [18433, 24], [18461, 13], [18478, 19], [18635, 8], [18660, 1], [18666, 31], [18733, 23], [18761, 13], [18777, 20], [18935, 9], [18958, 4], [18967, 30], [19033, 16], [19053, 2], [19067, 1], [19077, 20], [19235, 11], [19256, 7], [19267, 29], [19333, 15], [19376, 21], [19534, 14], [19555, 8], [19568, 28], [19633, 14], [19675, 22], [19834, 14], [19855, 9], [19868, 28], [19933, 12], [19972, 26], [20134, 14], [20155, 10], [20169, 27], [20233, 12], [20269, 29], [20433, 16], [20455, 11], [20470, 26], [20533, 11], [20549, 1], [20566, 32], [20733, 16], [20756, 10], [20770, 26], [20833, 11], [20848, 3], [20865, 33], [21033, 16], [21056, 11], [21071, 25], [21133, 12], [21149, 3], [21165, 33], [21332, 18], [21356, 12], [21371, 24], [21433, 13], [21449, 4], [21464, 34], [21632, 18], [21656, 12], [21672, 23], [21732, 16], [21751, 2], [21763, 35], [21932, 18], [21957, 12], [21973, 22], [22032, 16], [22064, 35], [22231, 20], [22257, 13], [22273, 22], [22332, 17], [22364, 35], [22531, 20], [22557, 13], [22574, 21], [22632, 17], [22652, 1], [22664, 35], [22831, 20], [22858, 13], [22874, 21], [22932, 17], [22951, 2], [22964, 35], [23131, 20], [23158, 14], [23175, 20], [23232, 20], [23264, 35], [23430, 22], [23458, 14], [23476, 20], [23532, 20], [23564, 35], [23730, 22], [23754, 1], [23758, 15], [23775, 77], [23864, 35], [24030, 25], [24059, 93], [24164, 35], [24329, 26], [24359, 92], [24464, 36], [24629, 27], [24659, 92], [24757, 1], [24764, 36], [24929, 27], [24960, 91], [25057, 2], [25064, 36], [25228, 28], [25260, 91], [25357, 2], [25363, 37], [25528, 29], [25560, 91], [25657, 2], [25663, 37], [25828, 29], [25860, 91], [25956, 4], [25963, 37], [26127, 30], [26161, 90], [26256, 4], [26263, 37], [26427, 31], [26461, 79], [26574, 26], [26727, 31], [26761, 79], [26874, 27], [27026, 32], [27062, 78], [27174, 27], [27326, 33], [27362, 78], [27474, 27], [27626, 33], [27662, 78], [27774, 27], [27925, 35], [27961, 79], [28074, 27], [28225, 115], [28374, 27], [28525, 115], [28674, 27], [28824, 116], [28974, 27], [29124, 116], [29274, 28], [29424, 115], [29574, 28], [29724, 115], [29874, 28], [30023, 116], [30175, 27], [30323, 116], [30475, 27], [30623, 116], [30775, 27], [30922, 117], [31075, 27], [31222, 117], [31375, 28], [31522, 117], [31675, 28], [31821, 118], [31975, 28], [32121, 118], [32275, 28], [32421, 118], [32575, 28], [32720, 119], [32875, 28], [33020, 119], [33175, 28], [33320, 119], [33475, 28], [33619, 120], [33775, 29], [33919, 120], [34075, 29], [34219, 120], [34375, 29], [34518, 121], [34675, 29], [34818, 121], [34975, 29], [35118, 186], [35418, 186], [35717, 187], [36017, 188], [36317, 188], [36616, 189], [36916, 189], [37216, 189], [37515, 190], [37815, 190], [38115, 191], [38414, 192], [38715, 190], [39016, 189], [39318, 186], [39619, 185], [39919, 184], [40220, 183], [40521, 182], [40822, 180], [41122, 180], [41423, 12], [41593, 9], [41724, 12], [41892, 9], [42025, 12], [42192, 9], [42326, 11], [42492, 9], [42626, 12], [42792, 8], [42927, 12], [43091, 9], [43228, 11], [43391, 9], [43529, 11], [43691, 8], [43830, 11], [43991, 8], [44130, 12], [44290, 9], [44431, 11], [44590, 8], [44732, 11], [44890, 8], [45033, 11], [45190, 8], [45333, 11], [45489, 9], [45634, 11], [45789, 8], [45935, 11], [46089, 8], [46236, 10], [46388, 9], [46537, 10], [46688, 8], [46837, 11], [46988, 8], [47138, 11], [47288, 8], [47439, 10], [47587, 8], [47740, 10], [47887, 8], [48040, 11], [48187, 8], [48341, 10], [48487, 7], [48642, 9], [48787, 7], [48943, 8], [49087, 7], [49244, 6], [49387, 6], [49544, 6], [49687, 6]], "point": [109, 106]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "RemoteControl|-03.33|+00.57|+00.75"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [160, 147, 174, 190], "mask": [[43961, 11], [44260, 13], [44560, 13], [44860, 13], [45160, 13], [45460, 13], [45760, 13], [46060, 13], [46360, 13], [46660, 13], [46960, 13], [47260, 13], [47560, 13], [47860, 13], [48161, 12], [48461, 12], [48761, 13], [49061, 13], [49361, 13], [49661, 13], [49961, 13], [50261, 13], [50561, 13], [50861, 13], [51161, 13], [51461, 13], [51761, 13], [52061, 13], [52361, 13], [52661, 13], [52961, 13], [53261, 13], [53561, 13], [53861, 14], [54161, 14], [54461, 14], [54761, 14], [55061, 14], [55361, 14], [55661, 14], [55961, 14], [56261, 14], [56561, 14], [56861, 14]], "point": [167, 167]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "RemoteControl|-03.33|+00.57|+00.75", "placeStationary": true, "receptacleObjectId": "CoffeeTable|-01.74|+00.00|+02.36"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [14, 48, 205, 166], "mask": [[14164, 35], [14234, 61], [14464, 35], [14534, 61], [14764, 35], [14834, 45], [14881, 1], [14883, 12], [15064, 35], [15134, 45], [15182, 14], [15364, 34], [15433, 45], [15483, 13], [15664, 34], [15733, 45], [15782, 14], [15964, 34], [16033, 44], [16081, 15], [16264, 34], [16333, 25], [16360, 18], [16381, 15], [16564, 34], [16633, 24], [16661, 17], [16681, 15], [16863, 35], [16933, 23], [16961, 17], [16980, 16], [17163, 34], [17233, 23], [17262, 15], [17280, 16], [17437, 1], [17463, 34], [17533, 23], [17562, 15], [17579, 18], [17736, 3], [17764, 33], [17833, 23], [17862, 14], [17879, 18], [18036, 4], [18065, 32], [18133, 23], [18161, 14], [18178, 19], [18336, 5], [18365, 32], [18433, 24], [18461, 13], [18478, 19], [18635, 8], [18660, 1], [18666, 31], [18733, 23], [18761, 13], [18777, 20], [18935, 9], [18958, 4], [18967, 30], [19033, 16], [19053, 2], [19067, 1], [19077, 20], [19235, 11], [19256, 7], [19267, 29], [19333, 15], [19376, 21], [19534, 14], [19555, 8], [19568, 28], [19633, 14], [19675, 22], [19834, 14], [19855, 9], [19868, 28], [19933, 12], [19972, 26], [20134, 14], [20155, 10], [20169, 27], [20233, 12], [20269, 29], [20433, 16], [20455, 11], [20470, 26], [20533, 11], [20549, 1], [20566, 32], [20733, 16], [20756, 10], [20770, 26], [20833, 11], [20848, 3], [20865, 33], [21033, 16], [21056, 11], [21071, 25], [21133, 12], [21149, 3], [21165, 33], [21332, 18], [21356, 12], [21371, 24], [21433, 13], [21449, 4], [21464, 34], [21632, 18], [21656, 12], [21672, 23], [21732, 16], [21751, 2], [21763, 35], [21932, 18], [21957, 12], [21973, 22], [22032, 16], [22064, 35], [22231, 20], [22257, 13], [22273, 22], [22332, 17], [22364, 35], [22531, 20], [22557, 13], [22574, 21], [22632, 17], [22652, 1], [22664, 35], [22831, 20], [22858, 13], [22874, 21], [22932, 17], [22951, 2], [22964, 35], [23131, 20], [23158, 14], [23175, 20], [23232, 20], [23264, 35], [23430, 22], [23458, 14], [23476, 20], [23532, 20], [23564, 35], [23730, 22], [23754, 1], [23758, 15], [23775, 77], [23864, 35], [24030, 25], [24059, 93], [24164, 35], [24329, 26], [24359, 92], [24464, 36], [24629, 27], [24659, 92], [24757, 1], [24764, 36], [24929, 27], [24960, 91], [25057, 2], [25064, 36], [25228, 28], [25260, 91], [25357, 2], [25363, 37], [25528, 29], [25560, 91], [25657, 2], [25663, 37], [25828, 29], [25860, 91], [25956, 4], [25963, 37], [26127, 30], [26161, 47], [26217, 34], [26256, 4], [26263, 37], [26427, 31], [26461, 47], [26518, 22], [26574, 26], [26727, 31], [26761, 47], [26818, 22], [26874, 27], [27026, 32], [27062, 45], [27118, 22], [27174, 27], [27326, 33], [27362, 45], [27418, 22], [27474, 27], [27626, 33], [27662, 45], [27717, 23], [27774, 27], [27925, 35], [27961, 46], [28017, 23], [28074, 27], [28225, 82], [28317, 23], [28374, 27], [28525, 82], [28617, 23], [28674, 27], [28824, 83], [28917, 23], [28974, 27], [29124, 83], [29217, 23], [29274, 28], [29424, 83], [29517, 22], [29574, 28], [29724, 82], [29817, 22], [29874, 28], [30023, 83], [30117, 22], [30175, 27], [30323, 83], [30417, 22], [30475, 27], [30623, 83], [30717, 22], [30775, 27], [30922, 84], [31017, 22], [31075, 27], [31222, 84], [31317, 22], [31375, 28], [31522, 84], [31616, 23], [31675, 28], [31821, 85], [31916, 23], [31975, 28], [32121, 85], [32216, 23], [32275, 28], [32421, 85], [32516, 23], [32575, 28], [32720, 85], [32816, 23], [32875, 28], [33020, 85], [33116, 23], [33175, 28], [33320, 85], [33416, 23], [33475, 28], [33619, 86], [33716, 23], [33775, 29], [33919, 86], [34016, 23], [34075, 29], [34219, 86], [34316, 23], [34375, 29], [34518, 87], [34616, 23], [34675, 29], [34818, 87], [34916, 23], [34975, 29], [35118, 87], [35215, 89], [35418, 186], [35717, 187], [36017, 188], [36317, 188], [36616, 189], [36916, 189], [37216, 189], [37515, 190], [37815, 190], [38115, 191], [38414, 192], [38715, 190], [39016, 189], [39318, 186], [39619, 185], [39919, 184], [40220, 183], [40521, 182], [40822, 180], [41122, 180], [41423, 12], [41593, 9], [41724, 12], [41892, 9], [42025, 12], [42192, 9], [42326, 11], [42492, 9], [42626, 12], [42792, 8], [42927, 12], [43091, 9], [43228, 11], [43391, 9], [43529, 11], [43691, 8], [43830, 11], [43991, 8], [44130, 12], [44290, 9], [44431, 11], [44590, 8], [44732, 11], [44890, 8], [45033, 11], [45190, 8], [45333, 11], [45489, 9], [45634, 11], [45789, 8], [45935, 11], [46089, 8], [46236, 10], [46388, 9], [46537, 10], [46688, 8], [46837, 11], [46988, 8], [47138, 11], [47288, 8], [47439, 10], [47587, 8], [47740, 10], [47887, 8], [48040, 11], [48187, 8], [48341, 10], [48487, 7], [48642, 9], [48787, 7], [48943, 8], [49087, 7], [49244, 6], [49387, 6], [49544, 6], [49687, 6]], "point": [105, 106]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan202", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -1.75, "y": 0.9010459, "z": 1.75}, "object_poses": [{"objectName": "Book_5cd72af5", "position": {"x": -2.82461929, "y": 0.565264165, "z": 0.7544929}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Watch_4c46810f", "position": {"x": 0.627, "y": 0.240272671, "z": 0.466551065}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Watch_4c46810f", "position": {"x": -1.79840386, "y": 0.710860252, "z": 3.759139}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "KeyChain_2197d4a8", "position": {"x": -1.85218859, "y": 0.5468825, "z": 0.381444752}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_a4a44225", "position": {"x": -3.075, "y": 0.563560545, "z": 0.382033467}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "RemoteControl_e89cdb06", "position": {"x": -1.60202086, "y": 0.54741025, "z": 0.471167117}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "RemoteControl_e89cdb06", "position": {"x": -3.3253808, "y": 0.565792859, "z": 0.754493}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_76ac64dc", "position": {"x": -1.10168529, "y": 0.5440468, "z": 0.7403342}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_76ac64dc", "position": {"x": -1.49920559, "y": 0.708587646, "z": 3.6492784}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Book_5cd72af5", "position": {"x": -1.71406972, "y": 0.449699581, "z": 2.52143216}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Box_08dfd057", "position": {"x": 0.332, "y": 0.892, "z": 0.488}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_76ac64dc", "position": {"x": -1.85218859, "y": 0.5440468, "z": 0.7403342}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "KeyChain_2197d4a8", "position": {"x": 0.500444651, "y": 0.7148357, "z": 0.09659839}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pillow_617abc68", "position": {"x": -0.506, "y": 0.664, "z": 0.431}, "rotation": {"x": 36.2882767, "y": 0.0, "z": 0.0}}, {"objectName": "Statue_a36dc426", "position": {"x": -1.4613781, "y": 0.45388782, "z": 2.200568}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "RemoteControl_e89cdb06", "position": {"x": 0.627, "y": 0.71920836, "z": 0.421133816}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_a4a44225", "position": {"x": -1.85218859, "y": 0.54604274, "z": 0.5608895}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Watch_4c46810f", "position": {"x": 0.500444651, "y": 0.7142726, "z": 0.583401561}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1167592799, "scene_num": 202}, "task_id": "trial_T20190907_075335_280493", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A2CT57W84KXX25_3WS1NTTKE1TGG772RNP8MYKTKW10FO", "high_descs": ["Take a step to the left.", "Pick up the remote between the laptops on the couch. ", "Turn around, face the coffee table. ", "Put the remote on the coffee table beside the statue. ", "Turn to the left, walk to the window, turn left, face the white striped chair. ", "Pick up the remote off of the chair. ", "Turn to the left, walk to the couch, turn to the coffee table. ", "Put the remote on the coffee table, in front of the plant."], "task_desc": "Put the two remotes on the coffee table. ", "votes": [1, 1, 1, 1]}, {"assignment_id": "A1RLO9LNUJIW5S_3QUO65DNQX5Q26WBOXMIYZHESUGOU1", "high_descs": ["Walk to the couch in front of you and take a step left.", "Pick up the remote that's between the laptops.", "Turn around to face the coffee table.", "Put the remote to the left of the statue so it's three remote widths away sitting vertically.", "Turn left and walk to the curtain then turn left.", "Pick up the remote that's in front of you.", "Go back to where you put the first remote.", "Put the remote to the left of the first one about four widths away."], "task_desc": "Put two remotes on the table.", "votes": [1, 1, 1, 1]}, {"assignment_id": "A2WKZI3JQCE3Z6_3SNLUL3WO74IWLTEB56Q6IDKXOQUL3", "high_descs": ["Turn left, move to the center of the two laptops, turn right, move to the couch.", "Pick up the remote.", "Turn around.", "Put the remote on the coffee table ", "Turn left, move to the window, turn left", "Pick up the remote on the chair", "Turn left, move to between the laptops, turn left", "Put the remote on the coffee table to the left of the other remote."], "task_desc": "Put two remote controls on the coffee table.", "votes": [1, 1, 1, 1]}, {"assignment_id": "A1RYC2OETJVEG7_3DY46V3X3SZN9H0EIHIBHHP7CAA557", "high_descs": ["Walk forward to the couch with two laptops on it.", "Pick up the remote that is on the couch between the laptops.", "Turn around and face the brown coffee table.", "Place the remote to the left of the black statue on the brown coffee table.", "Turn left and walk across the room to the blue and white striped couch.", "Pick up the remote that is on the blue and white striped couch.", "Turn left and walk across the room to the brown coffee table.", "Place the remote on the brown coffee table to the left of the other remote."], "task_desc": "Put both remotes on the brown coffee table.", "votes": [1, 1]}, {"assignment_id": "A2A4UAFZ5LW71K_3RXCAC0YIU64CW80XB85XYQN4AY8G5", "high_descs": ["take a few steps to the left, face the couch", "pick up the remote that is on the couch", "turn around, face the table behind you", "put the remote on the table", "turn left, walk towards window, turn left to face the chair", "pick the remote up that is on the chair", "turn left, walk forward, stop to face the table on the left", "put the remote on the table"], "task_desc": "put two remotes on the table", "votes": [1, 1]}, {"assignment_id": "A36DK84J5YJ942_3PEIJLRY6WAQOJZ8KOOFZ0T4PEZWXG", "high_descs": ["Move to the couch to the left of the arm chair", "pick up a remote from the couch", "move to the coffee table in the middle of the room", "put the remote on the coffee table", "move to the arm chair to the right of the couch", "pick up a remote from the chair", "move to the coffee table in the middle of the room", "put the remote on the coffee table"], "task_desc": "Put two remotes on the coffee table.", "votes": [1, 1]}]}}