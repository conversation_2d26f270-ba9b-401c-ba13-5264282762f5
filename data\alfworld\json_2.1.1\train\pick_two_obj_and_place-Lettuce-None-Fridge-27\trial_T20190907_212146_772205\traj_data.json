{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000283.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000284.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000285.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000286.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000287.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000288.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000289.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000290.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000291.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000292.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000293.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000294.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000295.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000296.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000297.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000298.png", "low_idx": 64}, {"high_idx": 7, "image_name": "000000299.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000300.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000301.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000302.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000303.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000304.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000305.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000306.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000307.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000308.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000309.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000310.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000311.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000312.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000313.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000314.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000315.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000316.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000317.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000318.png", "low_idx": 67}, {"high_idx": 7, "image_name": "000000319.png", "low_idx": 67}, {"high_idx": 7, "image_name": "000000320.png", "low_idx": 67}, {"high_idx": 7, "image_name": "000000321.png", "low_idx": 67}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Lettuce", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|2|8|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["lettuce"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Lettuce", [-0.215429306, -0.215429306, 11.03152276, 11.03152276, 3.997936248, 3.997936248]], "coordinateReceptacleObjectId": ["CounterTop", [4.264, 4.264, 10.6656, 10.6656, 3.8928, 3.8928]], "forceVisible": true, "objectId": "Lettuce|-00.05|+01.00|+02.76"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|5|-1|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["lettuce", "fridge"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Lettuce", [-0.215429306, -0.215429306, 11.03152276, 11.03152276, 3.997936248, 3.997936248]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [8.416, 8.416, -1.116, -1.116, 0.0, 0.0]], "forceVisible": true, "objectId": "Lettuce|-00.05|+01.00|+02.76", "receptacleObjectId": "<PERSON><PERSON>|+02.10|+00.00|-00.28"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|2|8|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["lettuce"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Lettuce", [1.108, 1.108, 10.444, 10.444, 3.956, 3.956]], "coordinateReceptacleObjectId": ["CounterTop", [4.264, 4.264, 10.6656, 10.6656, 3.8928, 3.8928]], "forceVisible": true, "objectId": "Lettuce|+00.28|+00.99|+02.61"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|5|-1|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["lettuce", "fridge"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Lettuce", [1.108, 1.108, 10.444, 10.444, 3.956, 3.956]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [8.416, 8.416, -1.116, -1.116, 0.0, 0.0]], "forceVisible": true, "objectId": "Lettuce|+00.28|+00.99|+02.61", "receptacleObjectId": "<PERSON><PERSON>|+02.10|+00.00|-00.28"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Lettuce|-00.05|+01.00|+02.76"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [0, 98, 36, 145], "mask": [[29122, 3], [29419, 9], [29716, 13], [30014, 16], [30312, 19], [30610, 22], [30908, 24], [31207, 25], [31505, 28], [31804, 29], [32103, 31], [32402, 32], [32701, 34], [33000, 35], [33300, 35], [33600, 36], [33900, 36], [34200, 36], [34500, 37], [34800, 37], [35100, 37], [35400, 37], [35700, 37], [36000, 36], [36300, 36], [36600, 36], [36900, 36], [37200, 36], [37500, 35], [37800, 35], [38100, 34], [38400, 34], [38700, 33], [39000, 33], [39300, 33], [39600, 32], [39900, 32], [40200, 32], [40500, 32], [40800, 31], [41100, 30], [41400, 29], [41700, 28], [42000, 26], [42300, 25], [42602, 22], [42904, 19], [43209, 3]], "point": [18, 120]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|+02.10|+00.00|-00.28"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 210], "mask": [[0, 36900], [36901, 299], [37202, 298], [37503, 297], [37804, 296], [38105, 295], [38405, 295], [38706, 294], [39007, 293], [39308, 292], [39609, 291], [39910, 290], [40211, 289], [40511, 289], [40812, 288], [41113, 287], [41414, 286], [41715, 284], [42016, 282], [42316, 281], [42617, 279], [42918, 277], [43219, 275], [43520, 274], [43821, 272], [44121, 271], [44422, 269], [44723, 267], [45024, 265], [45325, 263], [45626, 261], [45927, 259], [46227, 258], [46528, 256], [46829, 254], [47130, 252], [47431, 250], [47732, 248], [48032, 248], [48333, 246], [48634, 244], [48935, 242], [49236, 240], [49537, 238], [49838, 236], [50138, 235], [50439, 233], [50740, 231], [51041, 229], [51342, 227], [51643, 225], [51943, 224], [52244, 223], [52545, 221], [52846, 219], [53147, 217], [53448, 215], [53749, 213], [54049, 212], [54350, 210], [54651, 208], [54952, 206], [55253, 204], [55554, 202], [55854, 201], [56155, 199], [56456, 197], [56757, 196], [57058, 194], [57359, 192], [57660, 190], [57960, 189], [58261, 187], [58562, 185], [58863, 183], [59164, 181], [59465, 76], [59550, 94], [59765, 74], [59854, 89], [60066, 72], [60155, 87], [60367, 69], [60456, 85], [60668, 67], [60757, 83], [60969, 65], [61057, 82], [61270, 64], [61358, 81], [61570, 63], [61659, 79], [61871, 61], [61960, 77], [62172, 60], [62262, 74], [62473, 57], [62563, 72], [62774, 55], [62864, 70]], "point": [149, 104]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Lettuce|-00.05|+01.00|+02.76", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|+02.10|+00.00|-00.28"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 21300], [21301, 299], [21602, 144], [21766, 134], [21902, 145], [22065, 135], [22203, 145], [22364, 136], [22504, 145], [22664, 136], [22804, 146], [22963, 137], [23105, 146], [23261, 139], [23406, 143], [23562, 138], [23706, 142], [23863, 137], [24007, 140], [24164, 136], [24308, 139], [24464, 136], [24608, 139], [24765, 135], [24909, 138], [25064, 136], [25210, 137], [25364, 136], [25510, 138], [25664, 136], [25811, 137], [25963, 137], [26111, 138], [26262, 138], [26412, 139], [26560, 140], [26713, 287], [27013, 287], [27314, 286], [27615, 285], [27915, 285], [28216, 284], [28517, 283], [28817, 283], [29118, 282], [29419, 281], [29719, 281], [30020, 280], [30321, 279], [30621, 279], [30922, 278], [31223, 277], [31523, 277], [31824, 276], [32125, 275], [32425, 275], [32726, 274], [33026, 274], [33327, 273], [33628, 272], [33928, 272], [34229, 271], [34530, 270], [34830, 270], [35131, 269], [35432, 268], [35732, 268], [36033, 267], [36334, 266], [36634, 266], [36935, 265], [37236, 264], [37536, 264], [37837, 263], [38138, 262], [38438, 262], [38739, 261], [39040, 260], [39340, 260], [39641, 259], [39941, 259], [40242, 258], [40543, 257], [40843, 257], [41144, 256], [41445, 255], [41745, 255], [42046, 254], [42347, 253], [42647, 253], [42948, 252], [43249, 251], [43549, 251], [43850, 250], [44151, 249], [44451, 249], [44752, 248], [45053, 247], [45353, 247], [45654, 246], [45955, 245], [46255, 245], [46556, 244], [46856, 244], [47157, 243], [47458, 242], [47758, 242], [48059, 241], [48360, 240], [48660, 240], [48961, 239], [49262, 238], [49562, 238], [49863, 237], [50164, 236], [50464, 236], [50765, 235], [51066, 234], [51366, 234], [51667, 233], [51968, 232], [52268, 232], [52569, 231], [52869, 231], [53170, 230], [53471, 229], [53771, 229], [54072, 228], [54373, 227], [54673, 227], [54974, 226], [55275, 225], [55575, 225], [55876, 224], [56177, 223], [56479, 221], [56930, 70], [57230, 70], [57531, 69], [57831, 69], [58131, 69], [58431, 69], [58731, 69], [59031, 69], [59332, 68], [59632, 68], [59932, 68], [60232, 68], [60532, 68], [60832, 68], [61133, 67], [61433, 67], [61733, 67], [62033, 67], [62333, 67], [62633, 67], [62934, 66], [63234, 66], [63534, 66], [63834, 66], [64134, 66], [64434, 66], [64735, 65], [65035, 65], [65335, 65], [65635, 65], [65935, 65], [66235, 65], [66536, 64], [66836, 64], [67136, 64], [67436, 64], [67736, 64], [68036, 64], [68336, 64], [68637, 63], [68937, 63], [69237, 63], [69537, 63], [69837, 63], [70137, 63], [70438, 62], [70738, 62], [71038, 62], [71338, 62], [71638, 62], [71938, 62], [72239, 61], [72539, 61], [72839, 61], [73139, 61], [73439, 61], [73739, 61], [74040, 60], [74340, 60], [74640, 60], [74940, 60], [75240, 60], [75540, 60], [75841, 59], [76141, 59], [76441, 59], [76741, 59], [77041, 59], [77341, 59], [77642, 58], [77942, 58], [78242, 58], [78542, 58], [78842, 58], [79142, 58], [79443, 57], [79743, 57], [80043, 57], [80343, 57], [80643, 57], [80943, 57], [81244, 56], [81544, 56], [81844, 56], [82144, 56], [82444, 56], [82744, 56], [83045, 55], [83345, 55], [83645, 55], [83945, 55], [84245, 55], [84545, 55], [84846, 54], [85146, 54], [85446, 54], [85746, 54], [86046, 54], [86346, 54], [86646, 54], [86947, 53], [87247, 53], [87547, 53], [87847, 53], [88147, 53], [88447, 53], [88748, 52], [89048, 52], [89348, 52], [89648, 52], [89948, 52]], "point": [149, 149]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|+02.10|+00.00|-00.28"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 21300], [21301, 299], [21602, 104], [21730, 16], [21766, 134], [21902, 103], [22030, 17], [22065, 135], [22203, 102], [22330, 18], [22364, 136], [22504, 101], [22630, 19], [22664, 136], [22804, 101], [22930, 20], [22963, 137], [23105, 100], [23230, 21], [23261, 139], [23406, 99], [23530, 19], [23562, 138], [23706, 99], [23831, 17], [23863, 137], [24007, 97], [24131, 16], [24164, 136], [24308, 96], [24432, 15], [24464, 136], [24608, 96], [24731, 16], [24765, 135], [24909, 95], [25031, 16], [25064, 136], [25210, 94], [25331, 16], [25364, 136], [25510, 94], [25631, 17], [25664, 136], [25811, 93], [25931, 17], [25963, 137], [26111, 93], [26230, 19], [26262, 138], [26412, 92], [26530, 21], [26560, 140], [26713, 91], [26830, 170], [27013, 92], [27130, 170], [27314, 91], [27429, 171], [27615, 91], [27729, 171], [27915, 91], [28028, 172], [28216, 91], [28328, 172], [28517, 91], [28627, 173], [28817, 91], [28927, 173], [29118, 91], [29226, 174], [29419, 91], [29525, 175], [29719, 92], [29825, 175], [30020, 92], [30124, 176], [30321, 92], [30423, 177], [30621, 92], [30722, 178], [30922, 92], [31022, 178], [31223, 93], [31321, 179], [31523, 277], [31824, 276], [32125, 275], [32425, 275], [32726, 274], [33026, 274], [33327, 273], [33628, 272], [33928, 272], [34229, 271], [34530, 270], [34830, 270], [35131, 269], [35432, 268], [35732, 268], [36033, 267], [36334, 266], [36634, 266], [36935, 265], [37236, 264], [37536, 264], [37837, 263], [38138, 262], [38438, 262], [38739, 261], [39040, 260], [39340, 260], [39641, 259], [39941, 259], [40242, 258], [40543, 257], [40843, 257], [41144, 256], [41445, 255], [41745, 255], [42046, 254], [42347, 253], [42647, 253], [42948, 252], [43249, 251], [43549, 251], [43850, 250], [44151, 249], [44451, 249], [44752, 248], [45053, 247], [45353, 247], [45654, 246], [45955, 245], [46255, 245], [46556, 244], [46856, 244], [47157, 243], [47458, 242], [47758, 242], [48059, 241], [48360, 240], [48660, 240], [48961, 239], [49262, 238], [49562, 238], [49863, 237], [50164, 236], [50464, 236], [50765, 235], [51066, 234], [51366, 234], [51667, 233], [51968, 232], [52268, 232], [52569, 231], [52869, 231], [53170, 230], [53471, 229], [53771, 229], [54072, 228], [54373, 227], [54673, 227], [54974, 226], [55275, 225], [55575, 225], [55876, 224], [56177, 223], [56479, 221], [56930, 70], [57230, 70], [57531, 69], [57831, 69], [58131, 69], [58431, 69], [58731, 69], [59031, 69], [59332, 68], [59632, 68], [59932, 68], [60232, 68], [60532, 68], [60832, 68], [61133, 67], [61433, 67], [61733, 67], [62033, 67], [62333, 67], [62633, 67], [62934, 66], [63234, 66], [63534, 66], [63834, 66], [64134, 66], [64434, 66], [64735, 65], [65035, 65], [65335, 65], [65635, 65], [65935, 65], [66235, 65], [66536, 64], [66836, 64], [67136, 64], [67436, 64], [67736, 64], [68036, 64], [68336, 64], [68637, 63], [68937, 63], [69237, 63], [69537, 63], [69837, 63], [70137, 63], [70438, 62], [70738, 62], [71038, 62], [71338, 62], [71638, 62], [71938, 62], [72239, 61], [72539, 61], [72839, 61], [73139, 61], [73439, 61], [73739, 61], [74040, 60], [74340, 60], [74640, 60], [74940, 60], [75240, 60], [75540, 60], [75841, 59], [76141, 59], [76441, 59], [76741, 59], [77041, 59], [77341, 59], [77642, 58], [77942, 58], [78242, 58], [78542, 58], [78842, 58], [79142, 58], [79443, 57], [79743, 57], [80043, 57], [80343, 57], [80643, 57], [80943, 57], [81244, 56], [81544, 56], [81844, 56], [82144, 56], [82444, 56], [82744, 56], [83045, 55], [83345, 55], [83645, 55], [83945, 55], [84245, 55], [84545, 55], [84846, 54], [85146, 54], [85446, 54], [85746, 54], [86046, 54], [86346, 54], [86646, 54], [86947, 53], [87247, 53], [87547, 53], [87847, 53], [88147, 53], [88447, 53], [88748, 52], [89048, 52], [89348, 52], [89648, 52], [89948, 52]], "point": [149, 149]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Lettuce|+00.28|+00.99|+02.61"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [41, 126, 110, 168], "mask": [[37577, 11], [37874, 23], [38170, 31], [38467, 37], [38765, 42], [39062, 46], [39360, 49], [39658, 52], [39956, 54], [40254, 56], [40553, 58], [40851, 60], [41150, 61], [41449, 61], [41748, 62], [42047, 62], [42346, 62], [42645, 62], [42944, 62], [43243, 62], [43543, 61], [43843, 60], [44142, 61], [44442, 61], [44742, 60], [45042, 59], [45342, 59], [45642, 58], [45942, 57], [46242, 56], [46542, 55], [46841, 55], [47141, 54], [47442, 52], [47743, 49], [48045, 45], [48346, 42], [48647, 39], [48948, 35], [49249, 32], [49550, 29], [49854, 22], [50159, 15]], "point": [75, 146]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|+02.10|+00.00|-00.28"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 210], "mask": [[0, 36900], [36901, 299], [37202, 298], [37503, 297], [37804, 296], [38105, 295], [38405, 295], [38706, 294], [39007, 293], [39308, 292], [39609, 291], [39910, 290], [40211, 289], [40511, 289], [40812, 288], [41113, 287], [41414, 286], [41715, 284], [42016, 282], [42316, 281], [42617, 279], [42918, 277], [43219, 275], [43520, 274], [43821, 272], [44121, 271], [44422, 269], [44723, 267], [45024, 265], [45325, 263], [45626, 261], [45927, 259], [46227, 258], [46528, 256], [46829, 254], [47130, 252], [47431, 250], [47732, 248], [48032, 248], [48333, 246], [48634, 244], [48935, 242], [49236, 240], [49537, 238], [49838, 236], [50138, 235], [50439, 233], [50740, 231], [51041, 229], [51342, 227], [51643, 225], [51943, 224], [52244, 223], [52545, 221], [52846, 219], [53147, 217], [53448, 215], [53749, 213], [54049, 212], [54350, 210], [54651, 208], [54952, 206], [55253, 204], [55554, 202], [55854, 201], [56155, 199], [56456, 197], [56757, 196], [57058, 194], [57359, 192], [57660, 190], [57960, 189], [58261, 187], [58562, 185], [58863, 183], [59164, 181], [59465, 76], [59550, 94], [59765, 74], [59854, 89], [60066, 72], [60155, 87], [60367, 69], [60456, 85], [60668, 67], [60757, 83], [60969, 65], [61057, 82], [61270, 64], [61358, 81], [61570, 63], [61659, 79], [61871, 61], [61960, 77], [62172, 60], [62262, 74], [62473, 57], [62563, 72], [62774, 55], [62864, 70]], "point": [149, 104]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Lettuce|+00.28|+00.99|+02.61", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|+02.10|+00.00|-00.28"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 21300], [21301, 299], [21602, 104], [21730, 16], [21766, 134], [21902, 103], [22030, 17], [22065, 135], [22203, 102], [22330, 18], [22364, 136], [22504, 101], [22630, 19], [22664, 136], [22804, 101], [22930, 20], [22963, 137], [23105, 100], [23230, 21], [23261, 139], [23406, 99], [23530, 19], [23562, 138], [23706, 99], [23831, 17], [23863, 137], [24007, 97], [24131, 16], [24164, 136], [24308, 96], [24432, 15], [24464, 136], [24608, 96], [24731, 16], [24765, 135], [24909, 95], [25031, 16], [25064, 136], [25210, 94], [25331, 16], [25364, 136], [25510, 94], [25631, 17], [25664, 136], [25811, 93], [25931, 17], [25963, 137], [26111, 93], [26230, 19], [26262, 138], [26412, 92], [26530, 21], [26560, 140], [26713, 91], [26830, 170], [27013, 92], [27130, 170], [27314, 91], [27429, 171], [27615, 91], [27729, 171], [27915, 91], [28028, 172], [28216, 91], [28328, 172], [28517, 91], [28627, 173], [28817, 91], [28927, 173], [29118, 91], [29226, 174], [29419, 91], [29525, 175], [29719, 92], [29825, 175], [30020, 92], [30124, 176], [30321, 92], [30423, 177], [30621, 92], [30722, 178], [30922, 92], [31022, 178], [31223, 93], [31321, 179], [31523, 277], [31824, 276], [32125, 275], [32425, 275], [32726, 274], [33026, 274], [33327, 273], [33628, 272], [33928, 272], [34229, 271], [34530, 270], [34830, 270], [35131, 269], [35432, 268], [35732, 268], [36033, 267], [36334, 266], [36634, 266], [36935, 265], [37236, 264], [37536, 264], [37837, 263], [38138, 262], [38438, 262], [38739, 261], [39040, 260], [39340, 260], [39641, 259], [39941, 259], [40242, 258], [40543, 257], [40843, 257], [41144, 256], [41445, 255], [41745, 255], [42046, 254], [42347, 253], [42647, 253], [42948, 252], [43249, 251], [43549, 251], [43850, 250], [44151, 249], [44451, 249], [44752, 248], [45053, 247], [45353, 247], [45654, 246], [45955, 245], [46255, 245], [46556, 244], [46856, 244], [47157, 243], [47458, 242], [47758, 242], [48059, 241], [48360, 240], [48660, 240], [48961, 239], [49262, 238], [49562, 238], [49863, 237], [50164, 236], [50464, 236], [50765, 235], [51066, 234], [51366, 234], [51667, 233], [51968, 232], [52268, 232], [52569, 231], [52869, 231], [53170, 230], [53471, 229], [53771, 229], [54072, 228], [54373, 227], [54673, 227], [54974, 226], [55275, 225], [55575, 225], [55876, 224], [56177, 223], [56479, 221], [56930, 70], [57230, 70], [57531, 69], [57831, 69], [58131, 69], [58431, 69], [58731, 69], [59031, 69], [59332, 68], [59632, 68], [59932, 68], [60232, 68], [60532, 68], [60832, 68], [61133, 67], [61433, 67], [61733, 67], [62033, 67], [62333, 67], [62633, 67], [62934, 66], [63234, 66], [63534, 66], [63834, 66], [64134, 66], [64434, 66], [64735, 65], [65035, 65], [65335, 65], [65635, 65], [65935, 65], [66235, 65], [66536, 64], [66836, 64], [67136, 64], [67436, 64], [67736, 64], [68036, 64], [68336, 64], [68637, 63], [68937, 63], [69237, 63], [69537, 63], [69837, 63], [70137, 63], [70438, 62], [70738, 62], [71038, 62], [71338, 62], [71638, 62], [71938, 62], [72239, 61], [72539, 61], [72839, 61], [73139, 61], [73439, 61], [73739, 61], [74040, 60], [74340, 60], [74640, 60], [74940, 60], [75240, 60], [75540, 60], [75841, 59], [76141, 59], [76441, 59], [76741, 59], [77041, 59], [77341, 59], [77642, 58], [77942, 58], [78242, 58], [78542, 58], [78842, 58], [79142, 58], [79443, 57], [79743, 57], [80043, 57], [80343, 57], [80643, 57], [80943, 57], [81244, 56], [81544, 56], [81844, 56], [82144, 56], [82444, 56], [82744, 56], [83045, 55], [83345, 55], [83645, 55], [83945, 55], [84245, 55], [84545, 55], [84846, 54], [85146, 54], [85446, 54], [85746, 54], [86046, 54], [86346, 54], [86646, 54], [86947, 53], [87247, 53], [87547, 53], [87847, 53], [88147, 53], [88447, 53], [88748, 52], [89048, 52], [89348, 52], [89648, 52], [89948, 52]], "point": [149, 149]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|+02.10|+00.00|-00.28"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 21300], [21301, 299], [21602, 104], [21730, 16], [21766, 15], [21806, 94], [21902, 103], [22030, 17], [22065, 16], [22106, 94], [22203, 102], [22330, 18], [22364, 17], [22407, 93], [22504, 101], [22630, 19], [22664, 17], [22707, 93], [22804, 101], [22930, 20], [22963, 18], [23007, 93], [23105, 100], [23230, 21], [23261, 19], [23307, 93], [23406, 99], [23530, 19], [23562, 18], [23608, 92], [23706, 99], [23831, 17], [23863, 17], [23908, 92], [24007, 97], [24131, 16], [24164, 16], [24208, 92], [24308, 96], [24432, 15], [24464, 17], [24508, 92], [24608, 96], [24731, 16], [24765, 16], [24808, 92], [24909, 95], [25031, 16], [25064, 17], [25108, 92], [25210, 94], [25331, 16], [25364, 17], [25408, 92], [25510, 94], [25631, 17], [25664, 17], [25708, 92], [25811, 93], [25931, 17], [25963, 18], [26008, 92], [26111, 93], [26230, 19], [26262, 19], [26307, 93], [26412, 92], [26530, 21], [26560, 21], [26607, 93], [26713, 91], [26830, 51], [26907, 93], [27013, 92], [27130, 51], [27207, 93], [27314, 91], [27429, 53], [27506, 94], [27615, 91], [27729, 53], [27806, 94], [27915, 91], [28028, 55], [28106, 94], [28216, 91], [28328, 56], [28405, 95], [28517, 91], [28627, 58], [28705, 95], [28817, 91], [28927, 59], [29005, 95], [29118, 91], [29226, 61], [29304, 96], [29419, 91], [29525, 63], [29604, 96], [29719, 92], [29825, 64], [29903, 97], [30020, 92], [30124, 67], [30203, 97], [30321, 92], [30423, 69], [30503, 97], [30621, 92], [30722, 71], [30802, 98], [30922, 92], [31022, 72], [31101, 99], [31223, 93], [31321, 74], [31400, 100], [31523, 277], [31824, 276], [32125, 275], [32425, 275], [32726, 274], [33026, 274], [33327, 273], [33628, 272], [33928, 272], [34229, 271], [34530, 270], [34830, 270], [35131, 269], [35432, 268], [35732, 268], [36033, 267], [36334, 266], [36634, 266], [36935, 265], [37236, 264], [37536, 264], [37837, 263], [38138, 262], [38438, 262], [38739, 261], [39040, 260], [39340, 260], [39641, 259], [39941, 259], [40242, 258], [40543, 257], [40843, 257], [41144, 256], [41445, 255], [41745, 255], [42046, 254], [42347, 253], [42647, 253], [42948, 252], [43249, 251], [43549, 251], [43850, 250], [44151, 249], [44451, 249], [44752, 248], [45053, 247], [45353, 247], [45654, 246], [45955, 245], [46255, 245], [46556, 244], [46856, 244], [47157, 243], [47458, 242], [47758, 242], [48059, 241], [48360, 240], [48660, 240], [48961, 239], [49262, 238], [49562, 238], [49863, 237], [50164, 236], [50464, 236], [50765, 235], [51066, 234], [51366, 234], [51667, 233], [51968, 232], [52268, 232], [52569, 231], [52869, 231], [53170, 230], [53471, 229], [53771, 229], [54072, 228], [54373, 227], [54673, 227], [54974, 226], [55275, 225], [55575, 225], [55876, 224], [56177, 223], [56479, 221], [56930, 70], [57230, 70], [57531, 69], [57831, 69], [58131, 69], [58431, 69], [58731, 69], [59031, 69], [59332, 68], [59632, 68], [59932, 68], [60232, 68], [60532, 68], [60832, 68], [61133, 67], [61433, 67], [61733, 67], [62033, 67], [62333, 67], [62633, 67], [62934, 66], [63234, 66], [63534, 66], [63834, 66], [64134, 66], [64434, 66], [64735, 65], [65035, 65], [65335, 65], [65635, 65], [65935, 65], [66235, 65], [66536, 64], [66836, 64], [67136, 64], [67436, 64], [67736, 64], [68036, 64], [68336, 64], [68637, 63], [68937, 63], [69237, 63], [69537, 63], [69837, 63], [70137, 63], [70438, 62], [70738, 62], [71038, 62], [71338, 62], [71638, 62], [71938, 62], [72239, 61], [72539, 61], [72839, 61], [73139, 61], [73439, 61], [73739, 61], [74040, 60], [74340, 60], [74640, 60], [74940, 60], [75240, 60], [75540, 60], [75841, 59], [76141, 59], [76441, 59], [76741, 59], [77041, 59], [77341, 59], [77642, 58], [77942, 58], [78242, 58], [78542, 58], [78842, 58], [79142, 58], [79443, 57], [79743, 57], [80043, 57], [80343, 57], [80643, 57], [80943, 57], [81244, 56], [81544, 56], [81844, 56], [82144, 56], [82444, 56], [82744, 56], [83045, 55], [83345, 55], [83645, 55], [83945, 55], [84245, 55], [84545, 55], [84846, 54], [85146, 54], [85446, 54], [85746, 54], [86046, 54], [86346, 54], [86646, 54], [86947, 53], [87247, 53], [87547, 53], [87847, 53], [88147, 53], [88447, 53], [88748, 52], [89048, 52], [89348, 52], [89648, 52], [89948, 52]], "point": [149, 149]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan27", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": 1.5, "y": 0.9010001, "z": 0.5}, "object_poses": [{"objectName": "WineBottle_2a35bd39", "position": {"x": 0.060256362, "y": 0.9386287, "z": 2.897542}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_db580e26", "position": {"x": -0.425777048, "y": 0.8059636, "z": 1.049764}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_9b26f0a9", "position": {"x": 1.97660708, "y": 0.747938633, "z": 0.652292669}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_3133524b", "position": {"x": -0.119527027, "y": 0.79151535, "z": 0.3771075}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_3133524b", "position": {"x": -0.103412524, "y": 0.746872, "z": 2.1294117}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_343518b0", "position": {"x": -0.2948274, "y": 1.07805347, "z": 1.90751112}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_922e45ca", "position": {"x": 1.29829383, "y": 0.0855824947, "z": 2.52336955}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_540cf8b1", "position": {"x": 1.67308259, "y": 0.9633184, "z": 2.82104349}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "SoapBottle_e54ab207", "position": {"x": 2.1509285, "y": 0.938483059, "z": 2.16226172}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_e54ab207", "position": {"x": 1.01391888, "y": 0.0821934938, "z": 2.562471}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_fac53a9c", "position": {"x": 1.84060025, "y": 0.9845922, "z": 0.5837}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_fac53a9c", "position": {"x": -0.0429645181, "y": 0.837259, "z": 1.38609231}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_dbaf3456", "position": {"x": 1.96414566, "y": 0.0789958239, "z": 2.12878227}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_dbaf3456", "position": {"x": -0.00485464931, "y": 0.07808387, "z": 1.99337959}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1fe08306", "position": {"x": -0.196089536, "y": 0.785963655, "z": 1.21792817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_598ee640", "position": {"x": -0.0538573265, "y": 0.999484062, "z": 2.75788069}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_5c2a3ea2", "position": {"x": 2.16538882, "y": 1.49853539, "z": 0.7850112}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_92427322", "position": {"x": 2.112262, "y": 1.37144148, "z": -0.09774992}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Egg_343518b0", "position": {"x": 1.11809373, "y": 0.844361842, "z": 2.69527984}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_598ee640", "position": {"x": 0.277, "y": 0.989, "z": 2.611}, "rotation": {"x": 0.0, "y": 60.2165527, "z": 0.0}}, {"objectName": "Fork_3133524b", "position": {"x": 0.00205624476, "y": 0.747341752, "z": 1.99347425}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_e54ab207", "position": {"x": -0.0429645181, "y": 0.7917499, "z": 1.21792817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_158e5727", "position": {"x": 1.837193, "y": 0.790092766, "z": 0.7210319}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Ladle_741f0242", "position": {"x": -0.167971015, "y": 0.9820624, "z": 2.75788069}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_4be1a058", "position": {"x": -0.199893564, "y": 0.8239382, "z": 0.813959}, "rotation": {"x": 0.0162805375, "y": 0.0151289012, "z": 359.887421}}, {"objectName": "SaltShaker_8e528c3b", "position": {"x": 1.85568142, "y": 1.49802363, "z": 2.7679956}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_d7da2d9d", "position": {"x": -0.425777048, "y": 0.831192434, "z": 0.713435769}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_f15c1655", "position": {"x": 2.1402, "y": 0.9351, "z": 1.5045}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "WineBottle_2a35bd39", "position": {"x": 1.91749084, "y": 0.9392287, "z": 0.3613872}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_db580e26", "position": {"x": -0.0429645181, "y": 0.8059667, "z": 1.049764}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_540cf8b1", "position": {"x": 1.94175351, "y": 0.7724276, "z": 0.4460751}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_fac53a9c", "position": {"x": -0.0429645181, "y": 0.8372559, "z": 0.545271635}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_92427322", "position": {"x": 2.27950335, "y": 1.37144148, "z": -0.46025005}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "ButterKnife_a2d86e36", "position": {"x": 1.91749084, "y": 0.9383421, "z": 0.8060128}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pot_a2dda372", "position": {"x": 1.85448289, "y": 0.9379, "z": 1.497}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1fe08306", "position": {"x": 2.09164739, "y": 1.49635053, "z": 2.46997452}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_7054e289", "position": {"x": 2.20738077, "y": 0.600678861, "z": -0.278999954}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "DishSponge_6b8b37c4", "position": {"x": 1.95469666, "y": 0.746371269, "z": 1.99161363}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_922e45ca", "position": {"x": -0.3120002, "y": 0.781068146, "z": 0.4090001}, "rotation": {"x": -0.001719736, "y": 0.000235511383, "z": 0.0006841732}}, {"objectName": "Spoon_9b26f0a9", "position": {"x": 0.6297573, "y": 0.796519, "z": 2.64}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_5c2a3ea2", "position": {"x": -0.349214524, "y": 0.7859668, "z": 1.21792817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_dbaf3456", "position": {"x": 2.20534277, "y": 1.945743, "z": 1.56363583}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2327704991, "scene_num": 27}, "task_id": "trial_T20190907_212146_772205", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "AFU00NU09CFXE_33TIN5LC07R4CSCAW90P4US2THM9YJ", "high_descs": ["Move to stand in front of the kitchen sink.", "Pick up the head of lettuce from the counter that's closest to the wine bottle. ", "Turn and carry the head of lettuce to the fridge. ", "Open the fridge, and place the lettuce on the top shelf. ", "Close the fridge door and move back to the counter space to the left of the kitchen sink. ", "Pick up the other head of lettuce from the counter next to the sink. ", "Carry the head of lettuce to the fridge. ", "Open the fridge and place the second head of lettuce inside."], "task_desc": "Move two heads of lettuce to the fridge. ", "votes": [1, 1]}, {"assignment_id": "A1S1K7134S2VUC_3F6HPJW4JGHPN3WBWZ3M9AZ8C582WL", "high_descs": ["Turn right, move to the counter left of the sink.", "Pick up the lettuce from the counter nearest the wine bottle.", "Turn right, move to in front of the refrigerator.", "Put the lettuce in the refrigerator.", "Turn left, move to the counter left of the sink.", "Pick up the lettuce on the counter.", "Turn right, move to in front of the refrigerator.", "Put the lettuce in the refrigerator."], "task_desc": "Place two lettuce pieces into the refrigerator.", "votes": [1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3S06PH7KSULJPCEOW0YIC30IDKQD1A", "high_descs": ["Walk across the room to the white table, then turn right and walk over to the counter.", "Pick up the leftmost head of lettuce off of the counter.", "Turn around and begin walking across the room, then hang a left and walk up to the fridge.", "Open the fridge and put the head of lettuce on the second shelf from the top, then close the door.", "Turn around and walk over to the chair, the turn right and walk up to the counter.", "Pick up the head of lettuce off of the counter.", "Turn around and walk towards the door, then hang a left and walk up to the fridge.", "Open the fridge and put the head of lettuce inside on the second shelf from the top, then close the door."], "task_desc": "Move two heads of lettuce to the fridge.", "votes": [1, 1]}]}}