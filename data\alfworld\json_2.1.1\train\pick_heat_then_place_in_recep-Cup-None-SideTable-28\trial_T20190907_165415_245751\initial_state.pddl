
(define (problem plan_trial_T20190907_165415_245751)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Apple_bar__minus_03_dot_49_bar__plus_00_dot_82_bar__minus_03_dot_60 - object
        Blinds_bar__minus_01_dot_37_bar__plus_02_dot_21_bar__minus_03_dot_96 - object
        Bowl_bar__minus_00_dot_16_bar__plus_01_dot_94_bar__minus_02_dot_18 - object
        Bowl_bar__minus_00_dot_33_bar__plus_01_dot_28_bar__minus_00_dot_30 - object
        Bowl_bar__minus_00_dot_42_bar__plus_01_dot_28_bar__minus_00_dot_65 - object
        Bread_bar__minus_02_dot_76_bar__plus_00_dot_98_bar__minus_00_dot_38 - object
        Bread_bar__minus_03_dot_57_bar__plus_00_dot_98_bar__minus_00_dot_29 - object
        ButterKnife_bar__minus_02_dot_56_bar__plus_00_dot_89_bar__minus_00_dot_46 - object
        ButterKnife_bar__minus_03_dot_36_bar__plus_00_dot_89_bar__minus_00_dot_55 - object
        ButterKnife_bar__minus_03_dot_57_bar__plus_00_dot_89_bar__minus_00_dot_46 - object
        Cup_bar__minus_00_dot_37_bar__plus_01_dot_50_bar__minus_03_dot_54 - object
        Cup_bar__minus_03_dot_46_bar__plus_00_dot_76_bar__minus_03_dot_05 - object
        DishSponge_bar__minus_01_dot_11_bar__plus_00_dot_94_bar__minus_03_dot_80 - object
        DishSponge_bar__minus_02_dot_39_bar__plus_00_dot_10_bar__minus_03_dot_57 - object
        Egg_bar__minus_03_dot_45_bar__plus_00_dot_80_bar__minus_03_dot_24 - object
        Faucet_bar__minus_00_dot_39_bar__plus_00_dot_93_bar__minus_03_dot_61 - object
        Fork_bar__minus_00_dot_15_bar__plus_00_dot_94_bar__minus_02_dot_93 - object
        Fork_bar__minus_00_dot_16_bar__plus_00_dot_94_bar__minus_01_dot_32 - object
        Fork_bar__minus_00_dot_32_bar__plus_00_dot_94_bar__minus_01_dot_32 - object
        Knife_bar__minus_00_dot_41_bar__plus_00_dot_78_bar__minus_02_dot_69 - object
        Knife_bar__minus_03_dot_20_bar__plus_00_dot_79_bar__minus_03_dot_12 - object
        Knife_bar__minus_03_dot_54_bar__plus_00_dot_79_bar__minus_03_dot_63 - object
        Lettuce_bar__minus_00_dot_40_bar__plus_01_dot_03_bar__minus_02_dot_79 - object
        Lettuce_bar__minus_03_dot_23_bar__plus_00_dot_85_bar__minus_03_dot_06 - object
        Lettuce_bar__minus_03_dot_53_bar__plus_00_dot_85_bar__minus_02_dot_86 - object
        LightSwitch_bar__minus_04_dot_37_bar__plus_01_dot_49_bar__minus_01_dot_03 - object
        Mug_bar__minus_00_dot_10_bar__plus_01_dot_93_bar__minus_00_dot_95 - object
        Mug_bar__minus_00_dot_32_bar__plus_01_dot_48_bar__minus_00_dot_41 - object
        Mug_bar__minus_01_dot_18_bar__plus_00_dot_08_bar__minus_03_dot_57 - object
        Pan_bar__minus_00_dot_44_bar__plus_00_dot_96_bar__minus_01_dot_85 - object
        PepperShaker_bar__minus_00_dot_52_bar__plus_00_dot_74_bar__minus_01_dot_41 - object
        PepperShaker_bar__minus_03_dot_79_bar__plus_00_dot_76_bar__minus_03_dot_33 - object
        Plate_bar__minus_01_dot_69_bar__plus_00_dot_96_bar__minus_03_dot_67 - object
        Plate_bar__minus_03_dot_82_bar__plus_00_dot_77_bar__minus_02_dot_90 - object
        Potato_bar__minus_00_dot_42_bar__plus_01_dot_32_bar__minus_00_dot_76 - object
        Pot_bar__minus_00_dot_44_bar__plus_00_dot_96_bar__minus_02_dot_24 - object
        SaltShaker_bar__minus_03_dot_26_bar__plus_00_dot_76_bar__minus_03_dot_35 - object
        SaltShaker_bar__minus_03_dot_64_bar__plus_00_dot_76_bar__minus_02_dot_99 - object
        SaltShaker_bar__minus_03_dot_68_bar__plus_00_dot_76_bar__minus_03_dot_50 - object
        Sink_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_03_dot_39 - object
        SoapBottle_bar__minus_02_dot_96_bar__plus_00_dot_89_bar__minus_00_dot_38 - object
        Spatula_bar__minus_00_dot_37_bar__plus_00_dot_96_bar__minus_02_dot_94 - object
        Spatula_bar__minus_02_dot_36_bar__plus_00_dot_91_bar__minus_00_dot_38 - object
        Spoon_bar__minus_00_dot_44_bar__plus_00_dot_75_bar__minus_02_dot_85 - object
        StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_01_dot_83 - object
        StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_01_dot_98 - object
        StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_02_dot_13 - object
        StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_02_dot_29 - object
        Tomato_bar__minus_02_dot_76_bar__plus_00_dot_95_bar__minus_00_dot_20 - object
        Window_bar__minus_01_dot_39_bar__plus_01_dot_61_bar__minus_04_dot_11 - object
        Cabinet_bar__minus_00_dot_33_bar__plus_01_dot_89_bar__minus_02_dot_51 - receptacle
        Cabinet_bar__minus_00_dot_34_bar__plus_01_dot_89_bar__minus_01_dot_29 - receptacle
        Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_00_dot_39 - receptacle
        Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_01_dot_27 - receptacle
        Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_01_dot_63 - receptacle
        Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_02_dot_50 - receptacle
        Cabinet_bar__minus_00_dot_35_bar__plus_01_dot_89_bar__minus_03_dot_29 - receptacle
        Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_01_dot_61 - receptacle
        Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_02_dot_51 - receptacle
        Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_03_dot_01 - receptacle
        Cabinet_bar__minus_01_dot_01_bar__plus_00_dot_39_bar__minus_03_dot_37 - receptacle
        CoffeeMachine_bar__minus_02_dot_51_bar__plus_00_dot_93_bar__minus_03_dot_80 - receptacle
        CounterTop_bar__minus_00_dot_33_bar__plus_00_dot_98_bar__minus_01_dot_45 - receptacle
        CounterTop_bar__minus_01_dot_94_bar__plus_00_dot_98_bar__minus_03_dot_67 - receptacle
        DiningTable_bar__minus_03_dot_03_bar__plus_00_dot_00_bar__minus_00_dot_44 - receptacle
        DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32 - receptacle
        Drawer_bar__minus_00_dot_48_bar__plus_00_dot_78_bar__minus_02_dot_74 - receptacle
        Drawer_bar__minus_00_dot_50_bar__plus_00_dot_78_bar__minus_01_dot_45 - receptacle
        Drawer_bar__minus_01_dot_15_bar__plus_00_dot_78_bar__minus_03_dot_50 - receptacle
        Fridge_bar__minus_00_dot_31_bar__plus_00_dot_00_bar__minus_00_dot_65 - receptacle
        GarbageCan_bar__minus_02_dot_42_bar__plus_00_dot_00_bar__minus_03_dot_54 - receptacle
        Microwave_bar__minus_00_dot_22_bar__plus_01_dot_47_bar__minus_02_dot_06 - receptacle
        SideTable_bar__minus_04_dot_07_bar__plus_00_dot_73_bar__minus_00_dot_31 - receptacle
        Sink_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_03_dot_39_bar_SinkBasin - receptacle
        StoveBurner_bar__minus_00_dot_22_bar__plus_00_dot_92_bar__minus_01_dot_85 - receptacle
        StoveBurner_bar__minus_00_dot_22_bar__plus_00_dot_92_bar__minus_02_dot_24 - receptacle
        StoveBurner_bar__minus_00_dot_44_bar__plus_00_dot_92_bar__minus_01_dot_85 - receptacle
        StoveBurner_bar__minus_00_dot_44_bar__plus_00_dot_92_bar__minus_02_dot_24 - receptacle
        Toaster_bar__minus_01_dot_97_bar__plus_00_dot_93_bar__minus_03_dot_76 - receptacle
        loc_bar__minus_6_bar__minus_12_bar_2_bar_0 - location
        loc_bar__minus_6_bar__minus_4_bar_1_bar__minus_15 - location
        loc_bar__minus_8_bar__minus_11_bar_1_bar_30 - location
        loc_bar__minus_4_bar__minus_6_bar_1_bar_45 - location
        loc_bar__minus_6_bar__minus_12_bar_2_bar_45 - location
        loc_bar__minus_5_bar__minus_3_bar_1_bar_60 - location
        loc_bar__minus_6_bar__minus_12_bar_2_bar__minus_30 - location
        loc_bar__minus_7_bar__minus_10_bar_2_bar_60 - location
        loc_bar__minus_4_bar__minus_12_bar_2_bar_60 - location
        loc_bar__minus_4_bar__minus_8_bar_1_bar_15 - location
        loc_bar__minus_12_bar__minus_5_bar_0_bar_60 - location
        loc_bar__minus_4_bar__minus_7_bar_1_bar_45 - location
        loc_bar__minus_6_bar__minus_9_bar_1_bar_60 - location
        loc_bar__minus_5_bar__minus_10_bar_1_bar_45 - location
        loc_bar__minus_14_bar__minus_9_bar_2_bar_45 - location
        loc_bar__minus_4_bar__minus_6_bar_1_bar__minus_15 - location
        loc_bar__minus_6_bar__minus_6_bar_1_bar__minus_15 - location
        loc_bar__minus_4_bar__minus_9_bar_1_bar_45 - location
        loc_bar__minus_4_bar__minus_7_bar_1_bar_60 - location
        loc_bar__minus_4_bar__minus_10_bar_1_bar__minus_30 - location
        loc_bar__minus_15_bar__minus_4_bar_3_bar_15 - location
        loc_bar__minus_8_bar__minus_12_bar_2_bar_45 - location
        loc_bar__minus_10_bar__minus_12_bar_2_bar_45 - location
        loc_bar__minus_7_bar__minus_11_bar_1_bar_45 - location
        loc_bar__minus_4_bar__minus_8_bar_1_bar_60 - location
        loc_bar__minus_4_bar__minus_9_bar_2_bar__minus_15 - location
        loc_bar__minus_4_bar__minus_7_bar_1_bar__minus_30 - location
        loc_bar__minus_15_bar__minus_5_bar_0_bar_45 - location
        loc_bar__minus_4_bar__minus_12_bar_1_bar_60 - location
        loc_bar__minus_10_bar__minus_12_bar_2_bar_60 - location
        loc_bar__minus_4_bar__minus_12_bar_1_bar__minus_15 - location
        loc_bar__minus_4_bar__minus_6_bar_1_bar_60 - location
        loc_bar__minus_4_bar__minus_9_bar_1_bar_60 - location
        loc_bar__minus_6_bar__minus_6_bar_1_bar_60 - location
        loc_bar__minus_10_bar__minus_9_bar_1_bar_30 - location
        )
    

(:init


        (receptacleType CounterTop_bar__minus_01_dot_94_bar__plus_00_dot_98_bar__minus_03_dot_67 CounterTopType)
        (receptacleType SideTable_bar__minus_04_dot_07_bar__plus_00_dot_73_bar__minus_00_dot_31 SideTableType)
        (receptacleType Cabinet_bar__minus_00_dot_35_bar__plus_01_dot_89_bar__minus_03_dot_29 CabinetType)
        (receptacleType Sink_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_03_dot_39_bar_SinkBasin SinkBasinType)
        (receptacleType DiningTable_bar__minus_03_dot_03_bar__plus_00_dot_00_bar__minus_00_dot_44 DiningTableType)
        (receptacleType Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_00_dot_39 CabinetType)
        (receptacleType Cabinet_bar__minus_00_dot_33_bar__plus_01_dot_89_bar__minus_02_dot_51 CabinetType)
        (receptacleType CoffeeMachine_bar__minus_02_dot_51_bar__plus_00_dot_93_bar__minus_03_dot_80 CoffeeMachineType)
        (receptacleType Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_02_dot_50 CabinetType)
        (receptacleType CounterTop_bar__minus_00_dot_33_bar__plus_00_dot_98_bar__minus_01_dot_45 CounterTopType)
        (receptacleType Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_01_dot_27 CabinetType)
        (receptacleType Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_01_dot_61 CabinetType)
        (receptacleType Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_03_dot_01 CabinetType)
        (receptacleType StoveBurner_bar__minus_00_dot_44_bar__plus_00_dot_92_bar__minus_01_dot_85 StoveBurnerType)
        (receptacleType Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_02_dot_51 CabinetType)
        (receptacleType Drawer_bar__minus_01_dot_15_bar__plus_00_dot_78_bar__minus_03_dot_50 DrawerType)
        (receptacleType StoveBurner_bar__minus_00_dot_44_bar__plus_00_dot_92_bar__minus_02_dot_24 StoveBurnerType)
        (receptacleType Fridge_bar__minus_00_dot_31_bar__plus_00_dot_00_bar__minus_00_dot_65 FridgeType)
        (receptacleType Drawer_bar__minus_00_dot_50_bar__plus_00_dot_78_bar__minus_01_dot_45 DrawerType)
        (receptacleType Microwave_bar__minus_00_dot_22_bar__plus_01_dot_47_bar__minus_02_dot_06 MicrowaveType)
        (receptacleType Drawer_bar__minus_00_dot_48_bar__plus_00_dot_78_bar__minus_02_dot_74 DrawerType)
        (receptacleType Cabinet_bar__minus_00_dot_34_bar__plus_01_dot_89_bar__minus_01_dot_29 CabinetType)
        (receptacleType Cabinet_bar__minus_01_dot_01_bar__plus_00_dot_39_bar__minus_03_dot_37 CabinetType)
        (receptacleType StoveBurner_bar__minus_00_dot_22_bar__plus_00_dot_92_bar__minus_01_dot_85 StoveBurnerType)
        (receptacleType GarbageCan_bar__minus_02_dot_42_bar__plus_00_dot_00_bar__minus_03_dot_54 GarbageCanType)
        (receptacleType Toaster_bar__minus_01_dot_97_bar__plus_00_dot_93_bar__minus_03_dot_76 ToasterType)
        (receptacleType StoveBurner_bar__minus_00_dot_22_bar__plus_00_dot_92_bar__minus_02_dot_24 StoveBurnerType)
        (receptacleType Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_01_dot_63 CabinetType)
        (receptacleType DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32 DiningTableType)
        (objectType Bread_bar__minus_03_dot_57_bar__plus_00_dot_98_bar__minus_00_dot_29 BreadType)
        (objectType Bread_bar__minus_02_dot_76_bar__plus_00_dot_98_bar__minus_00_dot_38 BreadType)
        (objectType StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_02_dot_29 StoveKnobType)
        (objectType StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_01_dot_98 StoveKnobType)
        (objectType Fork_bar__minus_00_dot_16_bar__plus_00_dot_94_bar__minus_01_dot_32 ForkType)
        (objectType Bowl_bar__minus_00_dot_33_bar__plus_01_dot_28_bar__minus_00_dot_30 BowlType)
        (objectType SaltShaker_bar__minus_03_dot_64_bar__plus_00_dot_76_bar__minus_02_dot_99 SaltShakerType)
        (objectType Mug_bar__minus_01_dot_18_bar__plus_00_dot_08_bar__minus_03_dot_57 MugType)
        (objectType Pot_bar__minus_00_dot_44_bar__plus_00_dot_96_bar__minus_02_dot_24 PotType)
        (objectType ButterKnife_bar__minus_02_dot_56_bar__plus_00_dot_89_bar__minus_00_dot_46 ButterKnifeType)
        (objectType StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_01_dot_83 StoveKnobType)
        (objectType Bowl_bar__minus_00_dot_42_bar__plus_01_dot_28_bar__minus_00_dot_65 BowlType)
        (objectType ButterKnife_bar__minus_03_dot_57_bar__plus_00_dot_89_bar__minus_00_dot_46 ButterKnifeType)
        (objectType Mug_bar__minus_00_dot_32_bar__plus_01_dot_48_bar__minus_00_dot_41 MugType)
        (objectType Lettuce_bar__minus_03_dot_53_bar__plus_00_dot_85_bar__minus_02_dot_86 LettuceType)
        (objectType Plate_bar__minus_01_dot_69_bar__plus_00_dot_96_bar__minus_03_dot_67 PlateType)
        (objectType Tomato_bar__minus_02_dot_76_bar__plus_00_dot_95_bar__minus_00_dot_20 TomatoType)
        (objectType Knife_bar__minus_00_dot_41_bar__plus_00_dot_78_bar__minus_02_dot_69 KnifeType)
        (objectType PepperShaker_bar__minus_03_dot_79_bar__plus_00_dot_76_bar__minus_03_dot_33 PepperShakerType)
        (objectType Spatula_bar__minus_02_dot_36_bar__plus_00_dot_91_bar__minus_00_dot_38 SpatulaType)
        (objectType ButterKnife_bar__minus_03_dot_36_bar__plus_00_dot_89_bar__minus_00_dot_55 ButterKnifeType)
        (objectType Knife_bar__minus_03_dot_20_bar__plus_00_dot_79_bar__minus_03_dot_12 KnifeType)
        (objectType Pan_bar__minus_00_dot_44_bar__plus_00_dot_96_bar__minus_01_dot_85 PanType)
        (objectType SoapBottle_bar__minus_02_dot_96_bar__plus_00_dot_89_bar__minus_00_dot_38 SoapBottleType)
        (objectType LightSwitch_bar__minus_04_dot_37_bar__plus_01_dot_49_bar__minus_01_dot_03 LightSwitchType)
        (objectType PepperShaker_bar__minus_00_dot_52_bar__plus_00_dot_74_bar__minus_01_dot_41 PepperShakerType)
        (objectType Potato_bar__minus_00_dot_42_bar__plus_01_dot_32_bar__minus_00_dot_76 PotatoType)
        (objectType Window_bar__minus_01_dot_39_bar__plus_01_dot_61_bar__minus_04_dot_11 WindowType)
        (objectType Mug_bar__minus_00_dot_10_bar__plus_01_dot_93_bar__minus_00_dot_95 MugType)
        (objectType DishSponge_bar__minus_01_dot_11_bar__plus_00_dot_94_bar__minus_03_dot_80 DishSpongeType)
        (objectType Spoon_bar__minus_00_dot_44_bar__plus_00_dot_75_bar__minus_02_dot_85 SpoonType)
        (objectType Bowl_bar__minus_00_dot_16_bar__plus_01_dot_94_bar__minus_02_dot_18 BowlType)
        (objectType Knife_bar__minus_03_dot_54_bar__plus_00_dot_79_bar__minus_03_dot_63 KnifeType)
        (objectType Apple_bar__minus_03_dot_49_bar__plus_00_dot_82_bar__minus_03_dot_60 AppleType)
        (objectType Plate_bar__minus_03_dot_82_bar__plus_00_dot_77_bar__minus_02_dot_90 PlateType)
        (objectType DishSponge_bar__minus_02_dot_39_bar__plus_00_dot_10_bar__minus_03_dot_57 DishSpongeType)
        (objectType Cup_bar__minus_03_dot_46_bar__plus_00_dot_76_bar__minus_03_dot_05 CupType)
        (objectType Sink_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_03_dot_39 SinkType)
        (objectType Lettuce_bar__minus_03_dot_23_bar__plus_00_dot_85_bar__minus_03_dot_06 LettuceType)
        (objectType Cup_bar__minus_00_dot_37_bar__plus_01_dot_50_bar__minus_03_dot_54 CupType)
        (objectType Fork_bar__minus_00_dot_32_bar__plus_00_dot_94_bar__minus_01_dot_32 ForkType)
        (objectType Egg_bar__minus_03_dot_45_bar__plus_00_dot_80_bar__minus_03_dot_24 EggType)
        (objectType Blinds_bar__minus_01_dot_37_bar__plus_02_dot_21_bar__minus_03_dot_96 BlindsType)
        (objectType SaltShaker_bar__minus_03_dot_26_bar__plus_00_dot_76_bar__minus_03_dot_35 SaltShakerType)
        (objectType SaltShaker_bar__minus_03_dot_68_bar__plus_00_dot_76_bar__minus_03_dot_50 SaltShakerType)
        (objectType Spatula_bar__minus_00_dot_37_bar__plus_00_dot_96_bar__minus_02_dot_94 SpatulaType)
        (objectType StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_02_dot_13 StoveKnobType)
        (objectType Fork_bar__minus_00_dot_15_bar__plus_00_dot_94_bar__minus_02_dot_93 ForkType)
        (objectType Lettuce_bar__minus_00_dot_40_bar__plus_01_dot_03_bar__minus_02_dot_79 LettuceType)
        (canContain CounterTopType SaltShakerType)
        (canContain CounterTopType BreadType)
        (canContain CounterTopType DishSpongeType)
        (canContain CounterTopType BowlType)
        (canContain CounterTopType PotType)
        (canContain CounterTopType MugType)
        (canContain CounterTopType EggType)
        (canContain CounterTopType ForkType)
        (canContain CounterTopType SpoonType)
        (canContain CounterTopType SoapBottleType)
        (canContain CounterTopType LettuceType)
        (canContain CounterTopType PotatoType)
        (canContain CounterTopType ButterKnifeType)
        (canContain CounterTopType CupType)
        (canContain CounterTopType PlateType)
        (canContain CounterTopType PepperShakerType)
        (canContain CounterTopType TomatoType)
        (canContain CounterTopType KnifeType)
        (canContain CounterTopType AppleType)
        (canContain CounterTopType PanType)
        (canContain CounterTopType SpatulaType)
        (canContain SideTableType SaltShakerType)
        (canContain SideTableType BreadType)
        (canContain SideTableType DishSpongeType)
        (canContain SideTableType BowlType)
        (canContain SideTableType PotType)
        (canContain SideTableType MugType)
        (canContain SideTableType EggType)
        (canContain SideTableType ForkType)
        (canContain SideTableType SpoonType)
        (canContain SideTableType SoapBottleType)
        (canContain SideTableType LettuceType)
        (canContain SideTableType PotatoType)
        (canContain SideTableType ButterKnifeType)
        (canContain SideTableType CupType)
        (canContain SideTableType PlateType)
        (canContain SideTableType PepperShakerType)
        (canContain SideTableType TomatoType)
        (canContain SideTableType KnifeType)
        (canContain SideTableType AppleType)
        (canContain SideTableType PanType)
        (canContain SideTableType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain SinkBasinType DishSpongeType)
        (canContain SinkBasinType BowlType)
        (canContain SinkBasinType PotType)
        (canContain SinkBasinType MugType)
        (canContain SinkBasinType EggType)
        (canContain SinkBasinType ForkType)
        (canContain SinkBasinType SpoonType)
        (canContain SinkBasinType LettuceType)
        (canContain SinkBasinType PotatoType)
        (canContain SinkBasinType ButterKnifeType)
        (canContain SinkBasinType CupType)
        (canContain SinkBasinType PlateType)
        (canContain SinkBasinType TomatoType)
        (canContain SinkBasinType KnifeType)
        (canContain SinkBasinType AppleType)
        (canContain SinkBasinType PanType)
        (canContain SinkBasinType SpatulaType)
        (canContain DiningTableType SaltShakerType)
        (canContain DiningTableType BreadType)
        (canContain DiningTableType DishSpongeType)
        (canContain DiningTableType BowlType)
        (canContain DiningTableType PotType)
        (canContain DiningTableType MugType)
        (canContain DiningTableType EggType)
        (canContain DiningTableType ForkType)
        (canContain DiningTableType SpoonType)
        (canContain DiningTableType SoapBottleType)
        (canContain DiningTableType LettuceType)
        (canContain DiningTableType PotatoType)
        (canContain DiningTableType ButterKnifeType)
        (canContain DiningTableType CupType)
        (canContain DiningTableType PlateType)
        (canContain DiningTableType PepperShakerType)
        (canContain DiningTableType TomatoType)
        (canContain DiningTableType KnifeType)
        (canContain DiningTableType AppleType)
        (canContain DiningTableType PanType)
        (canContain DiningTableType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CoffeeMachineType MugType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CounterTopType SaltShakerType)
        (canContain CounterTopType BreadType)
        (canContain CounterTopType DishSpongeType)
        (canContain CounterTopType BowlType)
        (canContain CounterTopType PotType)
        (canContain CounterTopType MugType)
        (canContain CounterTopType EggType)
        (canContain CounterTopType ForkType)
        (canContain CounterTopType SpoonType)
        (canContain CounterTopType SoapBottleType)
        (canContain CounterTopType LettuceType)
        (canContain CounterTopType PotatoType)
        (canContain CounterTopType ButterKnifeType)
        (canContain CounterTopType CupType)
        (canContain CounterTopType PlateType)
        (canContain CounterTopType PepperShakerType)
        (canContain CounterTopType TomatoType)
        (canContain CounterTopType KnifeType)
        (canContain CounterTopType AppleType)
        (canContain CounterTopType PanType)
        (canContain CounterTopType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain FridgeType BreadType)
        (canContain FridgeType BowlType)
        (canContain FridgeType PotType)
        (canContain FridgeType MugType)
        (canContain FridgeType EggType)
        (canContain FridgeType LettuceType)
        (canContain FridgeType PotatoType)
        (canContain FridgeType CupType)
        (canContain FridgeType PlateType)
        (canContain FridgeType TomatoType)
        (canContain FridgeType AppleType)
        (canContain FridgeType PanType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain MicrowaveType MugType)
        (canContain MicrowaveType PotatoType)
        (canContain MicrowaveType EggType)
        (canContain MicrowaveType AppleType)
        (canContain MicrowaveType TomatoType)
        (canContain MicrowaveType BreadType)
        (canContain MicrowaveType CupType)
        (canContain MicrowaveType PlateType)
        (canContain MicrowaveType BowlType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain GarbageCanType BreadType)
        (canContain GarbageCanType DishSpongeType)
        (canContain GarbageCanType EggType)
        (canContain GarbageCanType SoapBottleType)
        (canContain GarbageCanType LettuceType)
        (canContain GarbageCanType PotatoType)
        (canContain GarbageCanType TomatoType)
        (canContain GarbageCanType AppleType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain DiningTableType SaltShakerType)
        (canContain DiningTableType BreadType)
        (canContain DiningTableType DishSpongeType)
        (canContain DiningTableType BowlType)
        (canContain DiningTableType PotType)
        (canContain DiningTableType MugType)
        (canContain DiningTableType EggType)
        (canContain DiningTableType ForkType)
        (canContain DiningTableType SpoonType)
        (canContain DiningTableType SoapBottleType)
        (canContain DiningTableType LettuceType)
        (canContain DiningTableType PotatoType)
        (canContain DiningTableType ButterKnifeType)
        (canContain DiningTableType CupType)
        (canContain DiningTableType PlateType)
        (canContain DiningTableType PepperShakerType)
        (canContain DiningTableType TomatoType)
        (canContain DiningTableType KnifeType)
        (canContain DiningTableType AppleType)
        (canContain DiningTableType PanType)
        (canContain DiningTableType SpatulaType)
        (pickupable Bread_bar__minus_03_dot_57_bar__plus_00_dot_98_bar__minus_00_dot_29)
        (pickupable Bread_bar__minus_02_dot_76_bar__plus_00_dot_98_bar__minus_00_dot_38)
        (pickupable Fork_bar__minus_00_dot_16_bar__plus_00_dot_94_bar__minus_01_dot_32)
        (pickupable Bowl_bar__minus_00_dot_33_bar__plus_01_dot_28_bar__minus_00_dot_30)
        (pickupable SaltShaker_bar__minus_03_dot_64_bar__plus_00_dot_76_bar__minus_02_dot_99)
        (pickupable Mug_bar__minus_01_dot_18_bar__plus_00_dot_08_bar__minus_03_dot_57)
        (pickupable Pot_bar__minus_00_dot_44_bar__plus_00_dot_96_bar__minus_02_dot_24)
        (pickupable ButterKnife_bar__minus_02_dot_56_bar__plus_00_dot_89_bar__minus_00_dot_46)
        (pickupable Bowl_bar__minus_00_dot_42_bar__plus_01_dot_28_bar__minus_00_dot_65)
        (pickupable ButterKnife_bar__minus_03_dot_57_bar__plus_00_dot_89_bar__minus_00_dot_46)
        (pickupable Mug_bar__minus_00_dot_32_bar__plus_01_dot_48_bar__minus_00_dot_41)
        (pickupable Lettuce_bar__minus_03_dot_53_bar__plus_00_dot_85_bar__minus_02_dot_86)
        (pickupable Plate_bar__minus_01_dot_69_bar__plus_00_dot_96_bar__minus_03_dot_67)
        (pickupable Tomato_bar__minus_02_dot_76_bar__plus_00_dot_95_bar__minus_00_dot_20)
        (pickupable Knife_bar__minus_00_dot_41_bar__plus_00_dot_78_bar__minus_02_dot_69)
        (pickupable PepperShaker_bar__minus_03_dot_79_bar__plus_00_dot_76_bar__minus_03_dot_33)
        (pickupable Spatula_bar__minus_02_dot_36_bar__plus_00_dot_91_bar__minus_00_dot_38)
        (pickupable ButterKnife_bar__minus_03_dot_36_bar__plus_00_dot_89_bar__minus_00_dot_55)
        (pickupable Knife_bar__minus_03_dot_20_bar__plus_00_dot_79_bar__minus_03_dot_12)
        (pickupable Pan_bar__minus_00_dot_44_bar__plus_00_dot_96_bar__minus_01_dot_85)
        (pickupable SoapBottle_bar__minus_02_dot_96_bar__plus_00_dot_89_bar__minus_00_dot_38)
        (pickupable PepperShaker_bar__minus_00_dot_52_bar__plus_00_dot_74_bar__minus_01_dot_41)
        (pickupable Potato_bar__minus_00_dot_42_bar__plus_01_dot_32_bar__minus_00_dot_76)
        (pickupable Mug_bar__minus_00_dot_10_bar__plus_01_dot_93_bar__minus_00_dot_95)
        (pickupable DishSponge_bar__minus_01_dot_11_bar__plus_00_dot_94_bar__minus_03_dot_80)
        (pickupable Spoon_bar__minus_00_dot_44_bar__plus_00_dot_75_bar__minus_02_dot_85)
        (pickupable Bowl_bar__minus_00_dot_16_bar__plus_01_dot_94_bar__minus_02_dot_18)
        (pickupable Knife_bar__minus_03_dot_54_bar__plus_00_dot_79_bar__minus_03_dot_63)
        (pickupable Apple_bar__minus_03_dot_49_bar__plus_00_dot_82_bar__minus_03_dot_60)
        (pickupable Plate_bar__minus_03_dot_82_bar__plus_00_dot_77_bar__minus_02_dot_90)
        (pickupable DishSponge_bar__minus_02_dot_39_bar__plus_00_dot_10_bar__minus_03_dot_57)
        (pickupable Cup_bar__minus_03_dot_46_bar__plus_00_dot_76_bar__minus_03_dot_05)
        (pickupable Lettuce_bar__minus_03_dot_23_bar__plus_00_dot_85_bar__minus_03_dot_06)
        (pickupable Cup_bar__minus_00_dot_37_bar__plus_01_dot_50_bar__minus_03_dot_54)
        (pickupable Fork_bar__minus_00_dot_32_bar__plus_00_dot_94_bar__minus_01_dot_32)
        (pickupable Egg_bar__minus_03_dot_45_bar__plus_00_dot_80_bar__minus_03_dot_24)
        (pickupable SaltShaker_bar__minus_03_dot_26_bar__plus_00_dot_76_bar__minus_03_dot_35)
        (pickupable SaltShaker_bar__minus_03_dot_68_bar__plus_00_dot_76_bar__minus_03_dot_50)
        (pickupable Spatula_bar__minus_00_dot_37_bar__plus_00_dot_96_bar__minus_02_dot_94)
        (pickupable Fork_bar__minus_00_dot_15_bar__plus_00_dot_94_bar__minus_02_dot_93)
        (pickupable Lettuce_bar__minus_00_dot_40_bar__plus_01_dot_03_bar__minus_02_dot_79)
        (isReceptacleObject Bowl_bar__minus_00_dot_33_bar__plus_01_dot_28_bar__minus_00_dot_30)
        (isReceptacleObject Mug_bar__minus_01_dot_18_bar__plus_00_dot_08_bar__minus_03_dot_57)
        (isReceptacleObject Pot_bar__minus_00_dot_44_bar__plus_00_dot_96_bar__minus_02_dot_24)
        (isReceptacleObject Bowl_bar__minus_00_dot_42_bar__plus_01_dot_28_bar__minus_00_dot_65)
        (isReceptacleObject Mug_bar__minus_00_dot_32_bar__plus_01_dot_48_bar__minus_00_dot_41)
        (isReceptacleObject Plate_bar__minus_01_dot_69_bar__plus_00_dot_96_bar__minus_03_dot_67)
        (isReceptacleObject Pan_bar__minus_00_dot_44_bar__plus_00_dot_96_bar__minus_01_dot_85)
        (isReceptacleObject Mug_bar__minus_00_dot_10_bar__plus_01_dot_93_bar__minus_00_dot_95)
        (isReceptacleObject Bowl_bar__minus_00_dot_16_bar__plus_01_dot_94_bar__minus_02_dot_18)
        (isReceptacleObject Plate_bar__minus_03_dot_82_bar__plus_00_dot_77_bar__minus_02_dot_90)
        (isReceptacleObject Cup_bar__minus_03_dot_46_bar__plus_00_dot_76_bar__minus_03_dot_05)
        (isReceptacleObject Cup_bar__minus_00_dot_37_bar__plus_01_dot_50_bar__minus_03_dot_54)
        (openable Cabinet_bar__minus_00_dot_35_bar__plus_01_dot_89_bar__minus_03_dot_29)
        (openable Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_00_dot_39)
        (openable Cabinet_bar__minus_00_dot_33_bar__plus_01_dot_89_bar__minus_02_dot_51)
        (openable Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_01_dot_27)
        (openable Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_01_dot_61)
        (openable Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_03_dot_01)
        (openable Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_02_dot_51)
        (openable Fridge_bar__minus_00_dot_31_bar__plus_00_dot_00_bar__minus_00_dot_65)
        (openable Microwave_bar__minus_00_dot_22_bar__plus_01_dot_47_bar__minus_02_dot_06)
        (openable Drawer_bar__minus_00_dot_48_bar__plus_00_dot_78_bar__minus_02_dot_74)
        (openable Cabinet_bar__minus_00_dot_34_bar__plus_01_dot_89_bar__minus_01_dot_29)
        (openable Cabinet_bar__minus_01_dot_01_bar__plus_00_dot_39_bar__minus_03_dot_37)
        
        (atLocation agent1 loc_bar__minus_10_bar__minus_9_bar_1_bar_30)
        
        (cleanable Fork_bar__minus_00_dot_16_bar__plus_00_dot_94_bar__minus_01_dot_32)
        (cleanable Bowl_bar__minus_00_dot_33_bar__plus_01_dot_28_bar__minus_00_dot_30)
        (cleanable Mug_bar__minus_01_dot_18_bar__plus_00_dot_08_bar__minus_03_dot_57)
        (cleanable Pot_bar__minus_00_dot_44_bar__plus_00_dot_96_bar__minus_02_dot_24)
        (cleanable ButterKnife_bar__minus_02_dot_56_bar__plus_00_dot_89_bar__minus_00_dot_46)
        (cleanable Bowl_bar__minus_00_dot_42_bar__plus_01_dot_28_bar__minus_00_dot_65)
        (cleanable ButterKnife_bar__minus_03_dot_57_bar__plus_00_dot_89_bar__minus_00_dot_46)
        (cleanable Mug_bar__minus_00_dot_32_bar__plus_01_dot_48_bar__minus_00_dot_41)
        (cleanable Lettuce_bar__minus_03_dot_53_bar__plus_00_dot_85_bar__minus_02_dot_86)
        (cleanable Plate_bar__minus_01_dot_69_bar__plus_00_dot_96_bar__minus_03_dot_67)
        (cleanable Tomato_bar__minus_02_dot_76_bar__plus_00_dot_95_bar__minus_00_dot_20)
        (cleanable Knife_bar__minus_00_dot_41_bar__plus_00_dot_78_bar__minus_02_dot_69)
        (cleanable Spatula_bar__minus_02_dot_36_bar__plus_00_dot_91_bar__minus_00_dot_38)
        (cleanable ButterKnife_bar__minus_03_dot_36_bar__plus_00_dot_89_bar__minus_00_dot_55)
        (cleanable Knife_bar__minus_03_dot_20_bar__plus_00_dot_79_bar__minus_03_dot_12)
        (cleanable Pan_bar__minus_00_dot_44_bar__plus_00_dot_96_bar__minus_01_dot_85)
        (cleanable Potato_bar__minus_00_dot_42_bar__plus_01_dot_32_bar__minus_00_dot_76)
        (cleanable Mug_bar__minus_00_dot_10_bar__plus_01_dot_93_bar__minus_00_dot_95)
        (cleanable DishSponge_bar__minus_01_dot_11_bar__plus_00_dot_94_bar__minus_03_dot_80)
        (cleanable Spoon_bar__minus_00_dot_44_bar__plus_00_dot_75_bar__minus_02_dot_85)
        (cleanable Bowl_bar__minus_00_dot_16_bar__plus_01_dot_94_bar__minus_02_dot_18)
        (cleanable Knife_bar__minus_03_dot_54_bar__plus_00_dot_79_bar__minus_03_dot_63)
        (cleanable Apple_bar__minus_03_dot_49_bar__plus_00_dot_82_bar__minus_03_dot_60)
        (cleanable Plate_bar__minus_03_dot_82_bar__plus_00_dot_77_bar__minus_02_dot_90)
        (cleanable DishSponge_bar__minus_02_dot_39_bar__plus_00_dot_10_bar__minus_03_dot_57)
        (cleanable Cup_bar__minus_03_dot_46_bar__plus_00_dot_76_bar__minus_03_dot_05)
        (cleanable Lettuce_bar__minus_03_dot_23_bar__plus_00_dot_85_bar__minus_03_dot_06)
        (cleanable Cup_bar__minus_00_dot_37_bar__plus_01_dot_50_bar__minus_03_dot_54)
        (cleanable Fork_bar__minus_00_dot_32_bar__plus_00_dot_94_bar__minus_01_dot_32)
        (cleanable Egg_bar__minus_03_dot_45_bar__plus_00_dot_80_bar__minus_03_dot_24)
        (cleanable Spatula_bar__minus_00_dot_37_bar__plus_00_dot_96_bar__minus_02_dot_94)
        (cleanable Fork_bar__minus_00_dot_15_bar__plus_00_dot_94_bar__minus_02_dot_93)
        (cleanable Lettuce_bar__minus_00_dot_40_bar__plus_01_dot_03_bar__minus_02_dot_79)
        
        (heatable Bread_bar__minus_03_dot_57_bar__plus_00_dot_98_bar__minus_00_dot_29)
        (heatable Bread_bar__minus_02_dot_76_bar__plus_00_dot_98_bar__minus_00_dot_38)
        (heatable Mug_bar__minus_01_dot_18_bar__plus_00_dot_08_bar__minus_03_dot_57)
        (heatable Mug_bar__minus_00_dot_32_bar__plus_01_dot_48_bar__minus_00_dot_41)
        (heatable Plate_bar__minus_01_dot_69_bar__plus_00_dot_96_bar__minus_03_dot_67)
        (heatable Tomato_bar__minus_02_dot_76_bar__plus_00_dot_95_bar__minus_00_dot_20)
        (heatable Potato_bar__minus_00_dot_42_bar__plus_01_dot_32_bar__minus_00_dot_76)
        (heatable Mug_bar__minus_00_dot_10_bar__plus_01_dot_93_bar__minus_00_dot_95)
        (heatable Apple_bar__minus_03_dot_49_bar__plus_00_dot_82_bar__minus_03_dot_60)
        (heatable Plate_bar__minus_03_dot_82_bar__plus_00_dot_77_bar__minus_02_dot_90)
        (heatable Cup_bar__minus_03_dot_46_bar__plus_00_dot_76_bar__minus_03_dot_05)
        (heatable Cup_bar__minus_00_dot_37_bar__plus_01_dot_50_bar__minus_03_dot_54)
        (heatable Egg_bar__minus_03_dot_45_bar__plus_00_dot_80_bar__minus_03_dot_24)
        (coolable Bread_bar__minus_03_dot_57_bar__plus_00_dot_98_bar__minus_00_dot_29)
        (coolable Bread_bar__minus_02_dot_76_bar__plus_00_dot_98_bar__minus_00_dot_38)
        (coolable Bowl_bar__minus_00_dot_33_bar__plus_01_dot_28_bar__minus_00_dot_30)
        (coolable Mug_bar__minus_01_dot_18_bar__plus_00_dot_08_bar__minus_03_dot_57)
        (coolable Pot_bar__minus_00_dot_44_bar__plus_00_dot_96_bar__minus_02_dot_24)
        (coolable Bowl_bar__minus_00_dot_42_bar__plus_01_dot_28_bar__minus_00_dot_65)
        (coolable Mug_bar__minus_00_dot_32_bar__plus_01_dot_48_bar__minus_00_dot_41)
        (coolable Lettuce_bar__minus_03_dot_53_bar__plus_00_dot_85_bar__minus_02_dot_86)
        (coolable Plate_bar__minus_01_dot_69_bar__plus_00_dot_96_bar__minus_03_dot_67)
        (coolable Tomato_bar__minus_02_dot_76_bar__plus_00_dot_95_bar__minus_00_dot_20)
        (coolable Pan_bar__minus_00_dot_44_bar__plus_00_dot_96_bar__minus_01_dot_85)
        (coolable Potato_bar__minus_00_dot_42_bar__plus_01_dot_32_bar__minus_00_dot_76)
        (coolable Mug_bar__minus_00_dot_10_bar__plus_01_dot_93_bar__minus_00_dot_95)
        (coolable Bowl_bar__minus_00_dot_16_bar__plus_01_dot_94_bar__minus_02_dot_18)
        (coolable Apple_bar__minus_03_dot_49_bar__plus_00_dot_82_bar__minus_03_dot_60)
        (coolable Plate_bar__minus_03_dot_82_bar__plus_00_dot_77_bar__minus_02_dot_90)
        (coolable Cup_bar__minus_03_dot_46_bar__plus_00_dot_76_bar__minus_03_dot_05)
        (coolable Lettuce_bar__minus_03_dot_23_bar__plus_00_dot_85_bar__minus_03_dot_06)
        (coolable Cup_bar__minus_00_dot_37_bar__plus_01_dot_50_bar__minus_03_dot_54)
        (coolable Egg_bar__minus_03_dot_45_bar__plus_00_dot_80_bar__minus_03_dot_24)
        (coolable Lettuce_bar__minus_00_dot_40_bar__plus_01_dot_03_bar__minus_02_dot_79)
        
        
        
        
        
        (sliceable Bread_bar__minus_03_dot_57_bar__plus_00_dot_98_bar__minus_00_dot_29)
        (sliceable Bread_bar__minus_02_dot_76_bar__plus_00_dot_98_bar__minus_00_dot_38)
        (sliceable Lettuce_bar__minus_03_dot_53_bar__plus_00_dot_85_bar__minus_02_dot_86)
        (sliceable Tomato_bar__minus_02_dot_76_bar__plus_00_dot_95_bar__minus_00_dot_20)
        (sliceable Potato_bar__minus_00_dot_42_bar__plus_01_dot_32_bar__minus_00_dot_76)
        (sliceable Apple_bar__minus_03_dot_49_bar__plus_00_dot_82_bar__minus_03_dot_60)
        (sliceable Lettuce_bar__minus_03_dot_23_bar__plus_00_dot_85_bar__minus_03_dot_06)
        (sliceable Egg_bar__minus_03_dot_45_bar__plus_00_dot_80_bar__minus_03_dot_24)
        (sliceable Lettuce_bar__minus_00_dot_40_bar__plus_01_dot_03_bar__minus_02_dot_79)
        
        (inReceptacle Plate_bar__minus_01_dot_69_bar__plus_00_dot_96_bar__minus_03_dot_67 CounterTop_bar__minus_01_dot_94_bar__plus_00_dot_98_bar__minus_03_dot_67)
        (inReceptacle DishSponge_bar__minus_01_dot_11_bar__plus_00_dot_94_bar__minus_03_dot_80 CounterTop_bar__minus_01_dot_94_bar__plus_00_dot_98_bar__minus_03_dot_67)
        (inReceptacle Spatula_bar__minus_00_dot_37_bar__plus_00_dot_96_bar__minus_02_dot_94 CounterTop_bar__minus_01_dot_94_bar__plus_00_dot_98_bar__minus_03_dot_67)
        (inReceptacle Fork_bar__minus_00_dot_15_bar__plus_00_dot_94_bar__minus_02_dot_93 CounterTop_bar__minus_01_dot_94_bar__plus_00_dot_98_bar__minus_03_dot_67)
        (inReceptacle Lettuce_bar__minus_00_dot_40_bar__plus_01_dot_03_bar__minus_02_dot_79 CounterTop_bar__minus_01_dot_94_bar__plus_00_dot_98_bar__minus_03_dot_67)
        (inReceptacle Bread_bar__minus_03_dot_57_bar__plus_00_dot_98_bar__minus_00_dot_29 DiningTable_bar__minus_03_dot_03_bar__plus_00_dot_00_bar__minus_00_dot_44)
        (inReceptacle SoapBottle_bar__minus_02_dot_96_bar__plus_00_dot_89_bar__minus_00_dot_38 DiningTable_bar__minus_03_dot_03_bar__plus_00_dot_00_bar__minus_00_dot_44)
        (inReceptacle ButterKnife_bar__minus_03_dot_57_bar__plus_00_dot_89_bar__minus_00_dot_46 DiningTable_bar__minus_03_dot_03_bar__plus_00_dot_00_bar__minus_00_dot_44)
        (inReceptacle Bread_bar__minus_02_dot_76_bar__plus_00_dot_98_bar__minus_00_dot_38 DiningTable_bar__minus_03_dot_03_bar__plus_00_dot_00_bar__minus_00_dot_44)
        (inReceptacle Tomato_bar__minus_02_dot_76_bar__plus_00_dot_95_bar__minus_00_dot_20 DiningTable_bar__minus_03_dot_03_bar__plus_00_dot_00_bar__minus_00_dot_44)
        (inReceptacle Spatula_bar__minus_02_dot_36_bar__plus_00_dot_91_bar__minus_00_dot_38 DiningTable_bar__minus_03_dot_03_bar__plus_00_dot_00_bar__minus_00_dot_44)
        (inReceptacle ButterKnife_bar__minus_03_dot_36_bar__plus_00_dot_89_bar__minus_00_dot_55 DiningTable_bar__minus_03_dot_03_bar__plus_00_dot_00_bar__minus_00_dot_44)
        (inReceptacle ButterKnife_bar__minus_02_dot_56_bar__plus_00_dot_89_bar__minus_00_dot_46 DiningTable_bar__minus_03_dot_03_bar__plus_00_dot_00_bar__minus_00_dot_44)
        (inReceptacle Knife_bar__minus_03_dot_54_bar__plus_00_dot_79_bar__minus_03_dot_63 DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32)
        (inReceptacle Apple_bar__minus_03_dot_49_bar__plus_00_dot_82_bar__minus_03_dot_60 DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32)
        (inReceptacle Plate_bar__minus_03_dot_82_bar__plus_00_dot_77_bar__minus_02_dot_90 DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32)
        (inReceptacle Cup_bar__minus_03_dot_46_bar__plus_00_dot_76_bar__minus_03_dot_05 DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32)
        (inReceptacle Lettuce_bar__minus_03_dot_53_bar__plus_00_dot_85_bar__minus_02_dot_86 DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32)
        (inReceptacle Lettuce_bar__minus_03_dot_23_bar__plus_00_dot_85_bar__minus_03_dot_06 DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32)
        (inReceptacle Egg_bar__minus_03_dot_45_bar__plus_00_dot_80_bar__minus_03_dot_24 DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32)
        (inReceptacle SaltShaker_bar__minus_03_dot_26_bar__plus_00_dot_76_bar__minus_03_dot_35 DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32)
        (inReceptacle SaltShaker_bar__minus_03_dot_64_bar__plus_00_dot_76_bar__minus_02_dot_99 DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32)
        (inReceptacle PepperShaker_bar__minus_03_dot_79_bar__plus_00_dot_76_bar__minus_03_dot_33 DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32)
        (inReceptacle Knife_bar__minus_03_dot_20_bar__plus_00_dot_79_bar__minus_03_dot_12 DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32)
        (inReceptacle SaltShaker_bar__minus_03_dot_68_bar__plus_00_dot_76_bar__minus_03_dot_50 DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32)
        (inReceptacle Pan_bar__minus_00_dot_44_bar__plus_00_dot_96_bar__minus_01_dot_85 StoveBurner_bar__minus_00_dot_44_bar__plus_00_dot_92_bar__minus_01_dot_85)
        (inReceptacle Pan_bar__minus_00_dot_44_bar__plus_00_dot_96_bar__minus_01_dot_85 StoveBurner_bar__minus_00_dot_22_bar__plus_00_dot_92_bar__minus_01_dot_85)
        (inReceptacle Pot_bar__minus_00_dot_44_bar__plus_00_dot_96_bar__minus_02_dot_24 StoveBurner_bar__minus_00_dot_44_bar__plus_00_dot_92_bar__minus_02_dot_24)
        (inReceptacle PepperShaker_bar__minus_00_dot_52_bar__plus_00_dot_74_bar__minus_01_dot_41 Drawer_bar__minus_00_dot_50_bar__plus_00_dot_78_bar__minus_01_dot_45)
        (inReceptacle Knife_bar__minus_00_dot_41_bar__plus_00_dot_78_bar__minus_02_dot_69 Drawer_bar__minus_00_dot_48_bar__plus_00_dot_78_bar__minus_02_dot_74)
        (inReceptacle Spoon_bar__minus_00_dot_44_bar__plus_00_dot_75_bar__minus_02_dot_85 Drawer_bar__minus_00_dot_48_bar__plus_00_dot_78_bar__minus_02_dot_74)
        (inReceptacle Mug_bar__minus_00_dot_10_bar__plus_01_dot_93_bar__minus_00_dot_95 Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_01_dot_27)
        (inReceptacle Bowl_bar__minus_00_dot_16_bar__plus_01_dot_94_bar__minus_02_dot_18 Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_02_dot_50)
        (inReceptacle Cup_bar__minus_00_dot_37_bar__plus_01_dot_50_bar__minus_03_dot_54 Cabinet_bar__minus_00_dot_35_bar__plus_01_dot_89_bar__minus_03_dot_29)
        (inReceptacle Mug_bar__minus_01_dot_18_bar__plus_00_dot_08_bar__minus_03_dot_57 Cabinet_bar__minus_01_dot_01_bar__plus_00_dot_39_bar__minus_03_dot_37)
        (inReceptacle Fork_bar__minus_00_dot_16_bar__plus_00_dot_94_bar__minus_01_dot_32 CounterTop_bar__minus_00_dot_33_bar__plus_00_dot_98_bar__minus_01_dot_45)
        (inReceptacle Pan_bar__minus_00_dot_44_bar__plus_00_dot_96_bar__minus_01_dot_85 CounterTop_bar__minus_00_dot_33_bar__plus_00_dot_98_bar__minus_01_dot_45)
        (inReceptacle Fork_bar__minus_00_dot_32_bar__plus_00_dot_94_bar__minus_01_dot_32 CounterTop_bar__minus_00_dot_33_bar__plus_00_dot_98_bar__minus_01_dot_45)
        (inReceptacle Pot_bar__minus_00_dot_44_bar__plus_00_dot_96_bar__minus_02_dot_24 StoveBurner_bar__minus_00_dot_22_bar__plus_00_dot_92_bar__minus_02_dot_24)
        (inReceptacle DishSponge_bar__minus_02_dot_39_bar__plus_00_dot_10_bar__minus_03_dot_57 GarbageCan_bar__minus_02_dot_42_bar__plus_00_dot_00_bar__minus_03_dot_54)
        (inReceptacle Bowl_bar__minus_00_dot_42_bar__plus_01_dot_28_bar__minus_00_dot_65 Fridge_bar__minus_00_dot_31_bar__plus_00_dot_00_bar__minus_00_dot_65)
        (inReceptacle Potato_bar__minus_00_dot_42_bar__plus_01_dot_32_bar__minus_00_dot_76 Fridge_bar__minus_00_dot_31_bar__plus_00_dot_00_bar__minus_00_dot_65)
        (inReceptacle Bowl_bar__minus_00_dot_33_bar__plus_01_dot_28_bar__minus_00_dot_30 Fridge_bar__minus_00_dot_31_bar__plus_00_dot_00_bar__minus_00_dot_65)
        (inReceptacle Mug_bar__minus_00_dot_32_bar__plus_01_dot_48_bar__minus_00_dot_41 Fridge_bar__minus_00_dot_31_bar__plus_00_dot_00_bar__minus_00_dot_65)
        
        
        (receptacleAtLocation Cabinet_bar__minus_00_dot_33_bar__plus_01_dot_89_bar__minus_02_dot_51 loc_bar__minus_4_bar__minus_12_bar_1_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_34_bar__plus_01_dot_89_bar__minus_01_dot_29 loc_bar__minus_4_bar__minus_6_bar_1_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_00_dot_39 loc_bar__minus_6_bar__minus_4_bar_1_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_01_dot_27 loc_bar__minus_6_bar__minus_6_bar_1_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_01_dot_63 loc_bar__minus_4_bar__minus_7_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_02_dot_50 loc_bar__minus_4_bar__minus_10_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_35_bar__plus_01_dot_89_bar__minus_03_dot_29 loc_bar__minus_4_bar__minus_9_bar_2_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_01_dot_61 loc_bar__minus_6_bar__minus_6_bar_1_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_02_dot_51 loc_bar__minus_6_bar__minus_9_bar_1_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_03_dot_01 loc_bar__minus_7_bar__minus_11_bar_1_bar_45)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_01_bar__plus_00_dot_39_bar__minus_03_dot_37 loc_bar__minus_7_bar__minus_10_bar_2_bar_60)
        (receptacleAtLocation CoffeeMachine_bar__minus_02_dot_51_bar__plus_00_dot_93_bar__minus_03_dot_80 loc_bar__minus_10_bar__minus_12_bar_2_bar_45)
        (receptacleAtLocation CounterTop_bar__minus_00_dot_33_bar__plus_00_dot_98_bar__minus_01_dot_45 loc_bar__minus_4_bar__minus_6_bar_1_bar_45)
        (receptacleAtLocation CounterTop_bar__minus_01_dot_94_bar__plus_00_dot_98_bar__minus_03_dot_67 loc_bar__minus_6_bar__minus_12_bar_2_bar_45)
        (receptacleAtLocation DiningTable_bar__minus_03_dot_03_bar__plus_00_dot_00_bar__minus_00_dot_44 loc_bar__minus_12_bar__minus_5_bar_0_bar_60)
        (receptacleAtLocation DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32 loc_bar__minus_14_bar__minus_9_bar_2_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_48_bar__plus_00_dot_78_bar__minus_02_dot_74 loc_bar__minus_5_bar__minus_10_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_50_bar__plus_00_dot_78_bar__minus_01_dot_45 loc_bar__minus_4_bar__minus_6_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_15_bar__plus_00_dot_78_bar__minus_03_dot_50 loc_bar__minus_4_bar__minus_12_bar_2_bar_60)
        (receptacleAtLocation Fridge_bar__minus_00_dot_31_bar__plus_00_dot_00_bar__minus_00_dot_65 loc_bar__minus_5_bar__minus_3_bar_1_bar_60)
        (receptacleAtLocation GarbageCan_bar__minus_02_dot_42_bar__plus_00_dot_00_bar__minus_03_dot_54 loc_bar__minus_10_bar__minus_12_bar_2_bar_60)
        (receptacleAtLocation Microwave_bar__minus_00_dot_22_bar__plus_01_dot_47_bar__minus_02_dot_06 loc_bar__minus_4_bar__minus_8_bar_1_bar_15)
        (receptacleAtLocation SideTable_bar__minus_04_dot_07_bar__plus_00_dot_73_bar__minus_00_dot_31 loc_bar__minus_15_bar__minus_5_bar_0_bar_45)
        (receptacleAtLocation Sink_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_03_dot_39_bar_SinkBasin loc_bar__minus_8_bar__minus_11_bar_1_bar_30)
        (receptacleAtLocation StoveBurner_bar__minus_00_dot_22_bar__plus_00_dot_92_bar__minus_01_dot_85 loc_bar__minus_4_bar__minus_7_bar_1_bar_45)
        (receptacleAtLocation StoveBurner_bar__minus_00_dot_22_bar__plus_00_dot_92_bar__minus_02_dot_24 loc_bar__minus_4_bar__minus_9_bar_1_bar_45)
        (receptacleAtLocation StoveBurner_bar__minus_00_dot_44_bar__plus_00_dot_92_bar__minus_01_dot_85 loc_bar__minus_4_bar__minus_7_bar_1_bar_45)
        (receptacleAtLocation StoveBurner_bar__minus_00_dot_44_bar__plus_00_dot_92_bar__minus_02_dot_24 loc_bar__minus_4_bar__minus_9_bar_1_bar_45)
        (receptacleAtLocation Toaster_bar__minus_01_dot_97_bar__plus_00_dot_93_bar__minus_03_dot_76 loc_bar__minus_8_bar__minus_12_bar_2_bar_45)
        (objectAtLocation Bowl_bar__minus_00_dot_33_bar__plus_01_dot_28_bar__minus_00_dot_30 loc_bar__minus_5_bar__minus_3_bar_1_bar_60)
        (objectAtLocation Mug_bar__minus_00_dot_32_bar__plus_01_dot_48_bar__minus_00_dot_41 loc_bar__minus_5_bar__minus_3_bar_1_bar_60)
        (objectAtLocation Lettuce_bar__minus_03_dot_53_bar__plus_00_dot_85_bar__minus_02_dot_86 loc_bar__minus_14_bar__minus_9_bar_2_bar_45)
        (objectAtLocation DishSponge_bar__minus_01_dot_11_bar__plus_00_dot_94_bar__minus_03_dot_80 loc_bar__minus_6_bar__minus_12_bar_2_bar_45)
        (objectAtLocation ButterKnife_bar__minus_03_dot_36_bar__plus_00_dot_89_bar__minus_00_dot_55 loc_bar__minus_12_bar__minus_5_bar_0_bar_60)
        (objectAtLocation PepperShaker_bar__minus_03_dot_79_bar__plus_00_dot_76_bar__minus_03_dot_33 loc_bar__minus_14_bar__minus_9_bar_2_bar_45)
        (objectAtLocation SaltShaker_bar__minus_03_dot_64_bar__plus_00_dot_76_bar__minus_02_dot_99 loc_bar__minus_14_bar__minus_9_bar_2_bar_45)
        (objectAtLocation Knife_bar__minus_03_dot_20_bar__plus_00_dot_79_bar__minus_03_dot_12 loc_bar__minus_14_bar__minus_9_bar_2_bar_45)
        (objectAtLocation Spatula_bar__minus_02_dot_36_bar__plus_00_dot_91_bar__minus_00_dot_38 loc_bar__minus_12_bar__minus_5_bar_0_bar_60)
        (objectAtLocation Bread_bar__minus_03_dot_57_bar__plus_00_dot_98_bar__minus_00_dot_29 loc_bar__minus_12_bar__minus_5_bar_0_bar_60)
        (objectAtLocation Plate_bar__minus_01_dot_69_bar__plus_00_dot_96_bar__minus_03_dot_67 loc_bar__minus_6_bar__minus_12_bar_2_bar_45)
        (objectAtLocation Cup_bar__minus_00_dot_37_bar__plus_01_dot_50_bar__minus_03_dot_54 loc_bar__minus_4_bar__minus_9_bar_2_bar__minus_15)
        (objectAtLocation Fork_bar__minus_00_dot_16_bar__plus_00_dot_94_bar__minus_01_dot_32 loc_bar__minus_4_bar__minus_6_bar_1_bar_45)
        (objectAtLocation Mug_bar__minus_00_dot_10_bar__plus_01_dot_93_bar__minus_00_dot_95 loc_bar__minus_6_bar__minus_6_bar_1_bar__minus_15)
        (objectAtLocation SaltShaker_bar__minus_03_dot_68_bar__plus_00_dot_76_bar__minus_03_dot_50 loc_bar__minus_14_bar__minus_9_bar_2_bar_45)
        (objectAtLocation Lettuce_bar__minus_00_dot_40_bar__plus_01_dot_03_bar__minus_02_dot_79 loc_bar__minus_6_bar__minus_12_bar_2_bar_45)
        (objectAtLocation Bowl_bar__minus_00_dot_42_bar__plus_01_dot_28_bar__minus_00_dot_65 loc_bar__minus_5_bar__minus_3_bar_1_bar_60)
        (objectAtLocation Knife_bar__minus_00_dot_41_bar__plus_00_dot_78_bar__minus_02_dot_69 loc_bar__minus_5_bar__minus_10_bar_1_bar_45)
        (objectAtLocation Fork_bar__minus_00_dot_15_bar__plus_00_dot_94_bar__minus_02_dot_93 loc_bar__minus_6_bar__minus_12_bar_2_bar_45)
        (objectAtLocation ButterKnife_bar__minus_03_dot_57_bar__plus_00_dot_89_bar__minus_00_dot_46 loc_bar__minus_12_bar__minus_5_bar_0_bar_60)
        (objectAtLocation Sink_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_03_dot_39 loc_bar__minus_4_bar__minus_12_bar_1_bar_60)
        (objectAtLocation SoapBottle_bar__minus_02_dot_96_bar__plus_00_dot_89_bar__minus_00_dot_38 loc_bar__minus_12_bar__minus_5_bar_0_bar_60)
        (objectAtLocation Pot_bar__minus_00_dot_44_bar__plus_00_dot_96_bar__minus_02_dot_24 loc_bar__minus_4_bar__minus_9_bar_1_bar_45)
        (objectAtLocation StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_01_dot_83 loc_bar__minus_4_bar__minus_7_bar_1_bar_60)
        (objectAtLocation StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_02_dot_13 loc_bar__minus_4_bar__minus_9_bar_1_bar_60)
        (objectAtLocation StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_02_dot_29 loc_bar__minus_4_bar__minus_9_bar_1_bar_60)
        (objectAtLocation StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_01_dot_98 loc_bar__minus_4_bar__minus_8_bar_1_bar_60)
        (objectAtLocation Fork_bar__minus_00_dot_32_bar__plus_00_dot_94_bar__minus_01_dot_32 loc_bar__minus_4_bar__minus_6_bar_1_bar_45)
        (objectAtLocation Cup_bar__minus_03_dot_46_bar__plus_00_dot_76_bar__minus_03_dot_05 loc_bar__minus_14_bar__minus_9_bar_2_bar_45)
        (objectAtLocation Tomato_bar__minus_02_dot_76_bar__plus_00_dot_95_bar__minus_00_dot_20 loc_bar__minus_12_bar__minus_5_bar_0_bar_60)
        (objectAtLocation Pan_bar__minus_00_dot_44_bar__plus_00_dot_96_bar__minus_01_dot_85 loc_bar__minus_4_bar__minus_6_bar_1_bar_45)
        (objectAtLocation Plate_bar__minus_03_dot_82_bar__plus_00_dot_77_bar__minus_02_dot_90 loc_bar__minus_14_bar__minus_9_bar_2_bar_45)
        (objectAtLocation Window_bar__minus_01_dot_39_bar__plus_01_dot_61_bar__minus_04_dot_11 loc_bar__minus_6_bar__minus_12_bar_2_bar_0)
        (objectAtLocation LightSwitch_bar__minus_04_dot_37_bar__plus_01_dot_49_bar__minus_01_dot_03 loc_bar__minus_15_bar__minus_4_bar_3_bar_15)
        (objectAtLocation Bread_bar__minus_02_dot_76_bar__plus_00_dot_98_bar__minus_00_dot_38 loc_bar__minus_12_bar__minus_5_bar_0_bar_60)
        (objectAtLocation Spatula_bar__minus_00_dot_37_bar__plus_00_dot_96_bar__minus_02_dot_94 loc_bar__minus_6_bar__minus_12_bar_2_bar_45)
        (objectAtLocation Knife_bar__minus_03_dot_54_bar__plus_00_dot_79_bar__minus_03_dot_63 loc_bar__minus_14_bar__minus_9_bar_2_bar_45)
        (objectAtLocation SaltShaker_bar__minus_03_dot_26_bar__plus_00_dot_76_bar__minus_03_dot_35 loc_bar__minus_14_bar__minus_9_bar_2_bar_45)
        (objectAtLocation PepperShaker_bar__minus_00_dot_52_bar__plus_00_dot_74_bar__minus_01_dot_41 loc_bar__minus_4_bar__minus_6_bar_1_bar_60)
        (objectAtLocation ButterKnife_bar__minus_02_dot_56_bar__plus_00_dot_89_bar__minus_00_dot_46 loc_bar__minus_12_bar__minus_5_bar_0_bar_60)
        (objectAtLocation Apple_bar__minus_03_dot_49_bar__plus_00_dot_82_bar__minus_03_dot_60 loc_bar__minus_14_bar__minus_9_bar_2_bar_45)
        (objectAtLocation DishSponge_bar__minus_02_dot_39_bar__plus_00_dot_10_bar__minus_03_dot_57 loc_bar__minus_10_bar__minus_12_bar_2_bar_60)
        (objectAtLocation Potato_bar__minus_00_dot_42_bar__plus_01_dot_32_bar__minus_00_dot_76 loc_bar__minus_5_bar__minus_3_bar_1_bar_60)
        (objectAtLocation Egg_bar__minus_03_dot_45_bar__plus_00_dot_80_bar__minus_03_dot_24 loc_bar__minus_14_bar__minus_9_bar_2_bar_45)
        (objectAtLocation Spoon_bar__minus_00_dot_44_bar__plus_00_dot_75_bar__minus_02_dot_85 loc_bar__minus_5_bar__minus_10_bar_1_bar_45)
        (objectAtLocation Lettuce_bar__minus_03_dot_23_bar__plus_00_dot_85_bar__minus_03_dot_06 loc_bar__minus_14_bar__minus_9_bar_2_bar_45)
        (objectAtLocation Mug_bar__minus_01_dot_18_bar__plus_00_dot_08_bar__minus_03_dot_57 loc_bar__minus_7_bar__minus_10_bar_2_bar_60)
        (objectAtLocation Blinds_bar__minus_01_dot_37_bar__plus_02_dot_21_bar__minus_03_dot_96 loc_bar__minus_6_bar__minus_12_bar_2_bar__minus_30)
        (objectAtLocation Bowl_bar__minus_00_dot_16_bar__plus_01_dot_94_bar__minus_02_dot_18 loc_bar__minus_4_bar__minus_10_bar_1_bar__minus_30)
        )
    

        (:goal
            (and
                (exists (?r - receptacle)
                    (exists (?o - object)
                        (and
                            (heatable ?o)
                            (objectType ?o CupType)
                            (receptacleType ?r SideTableType)
                            (isHot ?o)
                            (inReceptacle ?o ?r)
                        )
                    )
                )
            )
        )
    )
    