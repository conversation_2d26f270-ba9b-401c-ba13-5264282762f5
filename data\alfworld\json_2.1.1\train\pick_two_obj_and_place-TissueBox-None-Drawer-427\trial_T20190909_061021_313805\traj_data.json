{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000187.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000188.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000189.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000190.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000191.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000192.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000193.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000194.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000256.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000257.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000258.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000259.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000260.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000261.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000262.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000263.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000264.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000265.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000266.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000267.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000268.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000269.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000270.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000271.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000272.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000273.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000274.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000275.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000277.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000278.png", "low_idx": 32}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "TissueBox", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-9|0|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["tissuebox"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["TissueBox", [-9.39197256, -9.39197256, -2.795993328, -2.795993328, 2.816065072, 2.816065072]], "coordinateReceptacleObjectId": ["CounterTop", [-11.2, -11.2, -2.376, -2.376, 0.004, 0.004]], "forceVisible": true, "objectId": "TissueBox|-02.35|+00.70|-00.70"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-9|2|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["tissuebox", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["TissueBox", [-9.39197256, -9.39197256, -2.795993328, -2.795993328, 2.816065072, 2.816065072]], "coordinateReceptacleObjectId": ["Drawer", [-11.165588, -11.165588, -2.4834924, -2.4834924, 1.085167408, 1.085167408]], "forceVisible": true, "objectId": "TissueBox|-02.35|+00.70|-00.70", "receptacleObjectId": "Drawer|-02.79|+00.27|-00.62"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-10|0|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["tissuebox"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["TissueBox", [-10.30258752, -10.30258752, -2.1135264, -2.1135264, 2.816065072, 2.816065072]], "coordinateReceptacleObjectId": ["CounterTop", [-11.2, -11.2, -2.376, -2.376, 0.004, 0.004]], "forceVisible": true, "objectId": "TissueBox|-02.58|+00.70|-00.53"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-9|2|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["tissuebox", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["TissueBox", [-10.30258752, -10.30258752, -2.1135264, -2.1135264, 2.816065072, 2.816065072]], "coordinateReceptacleObjectId": ["Drawer", [-11.165588, -11.165588, -2.4834924, -2.4834924, 1.085167408, 1.085167408]], "forceVisible": true, "objectId": "TissueBox|-02.58|+00.70|-00.53", "receptacleObjectId": "Drawer|-02.79|+00.27|-00.62"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "TissueBox|-02.35|+00.70|-00.70"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [161, 81, 187, 128], "mask": [[24170, 1], [24462, 23], [24762, 23], [25062, 23], [25362, 24], [25662, 24], [25962, 24], [26261, 25], [26561, 25], [26861, 25], [27161, 25], [27461, 25], [27761, 25], [28061, 25], [28361, 25], [28661, 26], [28961, 26], [29261, 26], [29561, 26], [29861, 26], [30161, 26], [30461, 26], [30761, 26], [31061, 26], [31361, 26], [31661, 26], [31961, 27], [32262, 26], [32562, 26], [32862, 26], [33162, 26], [33462, 26], [33762, 26], [34062, 26], [34362, 26], [34662, 26], [34962, 26], [35262, 26], [35562, 26], [35862, 26], [36162, 25], [36462, 25], [36762, 25], [37062, 25], [37362, 25], [37662, 24], [37962, 24], [38262, 24]], "point": [174, 103]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-02.79|+00.27|-00.62"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [200, 182, 283, 208], "mask": [[54506, 1], [54805, 79], [55105, 79], [55405, 78], [55705, 77], [56004, 78], [56304, 77], [56604, 77], [56904, 76], [57203, 76], [57503, 76], [57803, 75], [58103, 75], [58402, 75], [58702, 75], [59002, 74], [59302, 73], [59601, 74], [59901, 73], [60201, 73], [60501, 72], [60800, 72], [61100, 72], [61400, 71], [61700, 71], [62000, 70], [62300, 69]], "point": [241, 194]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "TissueBox|-02.35|+00.70|-00.70", "placeStationary": true, "receptacleObjectId": "Drawer|-02.79|+00.27|-00.62"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [202, 183, 299, 270], "mask": [[54807, 72], [55107, 73], [55407, 73], [55706, 75], [56006, 75], [56306, 76], [56606, 76], [56905, 77], [57205, 78], [57505, 78], [57805, 79], [58104, 80], [58404, 81], [58704, 81], [59004, 82], [59303, 83], [59603, 83], [59903, 84], [60203, 84], [60502, 86], [60803, 85], [61103, 86], [61403, 86], [61703, 86], [62003, 87], [62303, 87], [62604, 87], [62904, 87], [63204, 88], [63520, 72], [63821, 72], [64121, 72], [64421, 72], [64721, 73], [65021, 73], [65321, 74], [65622, 73], [65922, 74], [66222, 74], [66522, 75], [66822, 75], [67122, 75], [67422, 76], [67723, 75], [68023, 76], [68323, 76], [68623, 77], [68923, 77], [69223, 77], [69523, 77], [69824, 76], [70124, 76], [70424, 76], [70724, 76], [71024, 76], [71324, 76], [71624, 76], [71924, 76], [72225, 75], [72525, 75], [72825, 75], [73125, 75], [73425, 75], [73725, 75], [74025, 75], [74326, 74], [74626, 74], [74926, 74], [75226, 74], [75526, 74], [75826, 74], [76126, 74], [76427, 73], [76727, 73], [77027, 73], [77327, 73], [77627, 73], [77927, 73], [78227, 73], [78528, 72], [78828, 72], [79128, 72], [79428, 71], [79728, 70], [80028, 69], [80328, 68], [80628, 67], [80929, 65]], "point": [250, 225]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-02.79|+00.27|-00.62"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [202, 183, 299, 270], "mask": [[54807, 72], [55107, 73], [55407, 73], [55706, 75], [56006, 75], [56306, 76], [56606, 76], [56905, 14], [56936, 46], [57205, 14], [57237, 46], [57505, 13], [57537, 46], [57805, 13], [57837, 47], [58104, 14], [58138, 46], [58404, 14], [58438, 47], [58704, 13], [58738, 47], [59004, 13], [59039, 47], [59303, 14], [59339, 47], [59603, 13], [59639, 47], [59903, 13], [59939, 48], [60203, 13], [60239, 48], [60502, 13], [60539, 49], [60803, 12], [60839, 49], [61103, 12], [61138, 51], [61403, 12], [61438, 51], [61703, 12], [61737, 52], [62003, 12], [62037, 53], [62303, 12], [62336, 54], [62604, 12], [62636, 55], [62904, 12], [62936, 55], [63204, 12], [63235, 57], [63504, 12], [63535, 57], [63804, 12], [63834, 59], [64104, 13], [64134, 59], [64405, 12], [64433, 60], [64705, 12], [64733, 61], [65005, 12], [65033, 61], [65305, 22], [65331, 64], [65605, 22], [65630, 65], [65905, 21], [65930, 66], [66206, 19], [66230, 66], [66506, 19], [66529, 68], [66806, 91], [67106, 91], [67406, 92], [67706, 92], [68007, 92], [68307, 92], [68607, 93], [68907, 93], [69207, 93], [69507, 93], [69808, 92], [70108, 92], [70408, 92], [70708, 92], [71008, 92], [71308, 92], [71609, 91], [71909, 91], [72209, 91], [72509, 91], [72809, 91], [73109, 91], [73410, 90], [73710, 90], [74010, 90], [74310, 90], [74610, 90], [74910, 90], [75211, 89], [75511, 89], [75811, 89], [76111, 89], [76411, 89], [76711, 89], [77012, 88], [77312, 88], [77612, 88], [77912, 88], [78212, 88], [78512, 88], [78811, 89], [79111, 89], [79410, 89], [79710, 88], [80010, 87], [80310, 86], [80610, 85], [80910, 84]], "point": [250, 225]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "TissueBox|-02.58|+00.70|-00.53"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [157, 115, 185, 165], "mask": [[34357, 25], [34657, 26], [34957, 26], [35257, 26], [35557, 26], [35857, 26], [36157, 26], [36457, 26], [36757, 26], [37057, 26], [37357, 26], [37657, 26], [37957, 26], [38257, 26], [38557, 27], [38857, 27], [39157, 27], [39457, 27], [39757, 27], [40057, 27], [40357, 27], [40657, 27], [40957, 27], [41257, 27], [41557, 27], [41857, 27], [42157, 27], [42457, 28], [42757, 28], [43057, 28], [43357, 28], [43657, 28], [43957, 28], [44257, 28], [44557, 28], [44857, 28], [45157, 28], [45457, 28], [45757, 28], [46057, 29], [46357, 29], [46657, 28], [46957, 28], [47257, 28], [47557, 28], [47857, 27], [48157, 27], [48457, 27], [48757, 27], [49057, 26], [49357, 26]], "point": [171, 139]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-02.79|+00.27|-00.62"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [200, 182, 283, 208], "mask": [[54506, 1], [54805, 79], [55105, 79], [55405, 78], [55705, 77], [56004, 78], [56304, 77], [56604, 77], [56904, 76], [57203, 76], [57503, 76], [57803, 75], [58103, 75], [58402, 75], [58702, 75], [59002, 74], [59302, 73], [59601, 74], [59901, 73], [60201, 73], [60501, 72], [60800, 72], [61100, 72], [61400, 71], [61700, 71], [62000, 70], [62300, 69]], "point": [241, 194]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "TissueBox|-02.58|+00.70|-00.53", "placeStationary": true, "receptacleObjectId": "Drawer|-02.79|+00.27|-00.62"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [202, 183, 299, 270], "mask": [[54807, 72], [55107, 73], [55407, 73], [55706, 75], [56006, 75], [56306, 76], [56606, 76], [56905, 14], [56936, 46], [57205, 14], [57237, 46], [57505, 13], [57537, 46], [57805, 13], [57837, 47], [58104, 14], [58138, 46], [58404, 14], [58438, 47], [58704, 13], [58738, 47], [59004, 13], [59039, 47], [59303, 14], [59339, 47], [59603, 13], [59639, 47], [59903, 13], [59939, 48], [60203, 13], [60239, 48], [60502, 13], [60539, 49], [60803, 12], [60839, 49], [61103, 12], [61138, 51], [61403, 12], [61438, 51], [61703, 12], [61737, 52], [62003, 12], [62037, 53], [62303, 12], [62336, 54], [62604, 12], [62636, 55], [62904, 12], [62936, 55], [63204, 12], [63235, 57], [63535, 57], [63834, 59], [64134, 59], [64433, 60], [64733, 61], [65033, 61], [65321, 6], [65331, 64], [65622, 5], [65630, 65], [65922, 4], [65930, 66], [66222, 3], [66230, 66], [66522, 3], [66529, 68], [66822, 75], [67122, 75], [67422, 76], [67723, 75], [68023, 76], [68323, 76], [68623, 77], [68923, 77], [69223, 77], [69523, 77], [69824, 76], [70124, 76], [70424, 76], [70724, 76], [71024, 76], [71324, 76], [71624, 76], [71924, 76], [72225, 75], [72525, 75], [72825, 75], [73125, 75], [73425, 75], [73725, 75], [74025, 75], [74326, 74], [74626, 74], [74926, 74], [75226, 74], [75526, 74], [75826, 74], [76126, 74], [76427, 73], [76727, 73], [77027, 73], [77327, 73], [77627, 73], [77927, 73], [78227, 73], [78528, 72], [78828, 72], [79128, 72], [79428, 71], [79728, 70], [80028, 69], [80328, 68], [80628, 67], [80929, 65]], "point": [250, 225]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-02.79|+00.27|-00.62"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [202, 183, 299, 270], "mask": [[54807, 72], [55107, 73], [55407, 73], [55706, 75], [56006, 75], [56306, 76], [56606, 76], [56905, 14], [56936, 8], [56962, 20], [57205, 14], [57237, 7], [57262, 21], [57505, 13], [57537, 6], [57563, 20], [57805, 13], [57837, 6], [57863, 21], [58104, 14], [58138, 5], [58163, 21], [58404, 14], [58438, 4], [58464, 21], [58704, 13], [58738, 4], [58764, 21], [59004, 13], [59039, 2], [59064, 22], [59303, 14], [59339, 2], [59365, 21], [59603, 13], [59639, 1], [59665, 21], [59903, 13], [59939, 1], [59966, 21], [60203, 13], [60239, 1], [60266, 21], [60502, 13], [60565, 23], [60803, 12], [60865, 23], [61103, 12], [61164, 25], [61403, 12], [61463, 26], [61703, 12], [61763, 26], [62003, 12], [62062, 28], [62303, 12], [62336, 2], [62362, 28], [62604, 12], [62636, 2], [62661, 30], [62904, 12], [62936, 2], [62961, 30], [63204, 12], [63235, 3], [63260, 32], [63504, 12], [63535, 4], [63560, 32], [63804, 12], [63834, 5], [63859, 34], [64104, 13], [64134, 5], [64159, 34], [64405, 12], [64433, 6], [64458, 35], [64705, 12], [64733, 7], [64757, 37], [65005, 12], [65033, 7], [65057, 37], [65305, 22], [65331, 21], [65356, 39], [65605, 22], [65630, 22], [65656, 39], [65905, 21], [65930, 21], [65956, 40], [66206, 19], [66230, 21], [66256, 40], [66506, 19], [66529, 22], [66555, 42], [66806, 91], [67106, 91], [67406, 92], [67706, 92], [68007, 92], [68307, 92], [68607, 93], [68907, 93], [69207, 93], [69507, 93], [69808, 92], [70108, 92], [70408, 92], [70708, 92], [71008, 92], [71308, 92], [71609, 91], [71909, 91], [72209, 91], [72509, 91], [72809, 91], [73109, 91], [73410, 90], [73710, 90], [74010, 90], [74310, 90], [74610, 90], [74910, 90], [75211, 89], [75511, 89], [75811, 89], [76111, 89], [76411, 89], [76711, 89], [77012, 88], [77312, 88], [77612, 88], [77912, 88], [78212, 88], [78512, 88], [78811, 89], [79111, 89], [79410, 89], [79710, 88], [80010, 87], [80310, 86], [80610, 85], [80910, 84]], "point": [250, 225]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan427", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -2.25, "y": 0.902041554, "z": 0.0}, "object_poses": [{"objectName": "Cloth_3b9d62d5", "position": {"x": -3.689219, "y": 0.0380398631, "z": 0.965374947}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cloth_3b9d62d5", "position": {"x": -1.44499636, "y": 0.17483151, "z": -0.6839403}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "TissueBox_0568c766", "position": {"x": -2.34799314, "y": 0.704016268, "z": -0.698998332}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "SprayBottle_13477bc4", "position": {"x": -1.665032, "y": 0.7003167, "z": -0.7558711}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "Candle_014ddfaf", "position": {"x": -1.44499636, "y": 0.4412011, "z": -0.6839403}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "SoapBar_1b2cf64e", "position": {"x": -2.07561564, "y": 1.019626, "z": 1.98100007}, "rotation": {"x": 1.40334191e-14, "y": 180.0, "z": 0.0}}, {"objectName": "DishSponge_b55ab5e2", "position": {"x": -4.03460836, "y": 0.0406776667, "z": 0.03943765}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_b55ab5e2", "position": {"x": -3.11647344, "y": 0.613860548, "z": -0.0444635153}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_f315e63f", "position": {"x": -3.21532631, "y": 0.6150794, "z": 1.64799738}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Towel_08b02c7c", "position": {"x": -3.66, "y": 1.567, "z": -0.459400028}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Candle_014ddfaf", "position": {"x": -2.29525948, "y": 0.1767959, "z": -0.6839396}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "HandTowel_a528e9d7", "position": {"x": -1.275, "y": 1.683, "z": 2.2444}, "rotation": {"x": 0.0, "y": -6.83018834e-06, "z": 0.0}}, {"objectName": "SoapBar_1b2cf64e", "position": {"x": -0.857000947, "y": 0.954481, "z": -0.749}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "ToiletPaper_d548da56", "position": {"x": -1.38050878, "y": 0.695359349, "z": -0.751498342}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plunger_e84c4ed6", "position": {"x": -1.08817315, "y": 0.0005790219, "z": -0.5260009}, "rotation": {"x": -0.00118385791, "y": 0.000438582065, "z": 0.0007821216}}, {"objectName": "SprayBottle_13477bc4", "position": {"x": -1.665032, "y": 0.7003167, "z": -0.6421268}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "SoapBottle_f315e63f", "position": {"x": -0.732000947, "y": 0.956825137, "z": -0.722208142}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "TissueBox_0568c766", "position": {"x": -2.57564688, "y": 0.704016268, "z": -0.5283816}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "ToiletPaper_6b21fc4b", "position": {"x": -1.89268589, "y": 0.7003167, "z": -0.8127431}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "PaperTowelRoll_0da11a15", "position": {"x": -1.66503179, "y": 0.809602, "z": -0.471510172}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "ScrubBrush_80c1e216", "position": {"x": -1.041, "y": 0.0010420084, "z": -0.707}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cloth_3b9d62d5", "position": {"x": -2.39182615, "y": 0.44166714, "z": -0.586737454}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "DishSponge_b55ab5e2", "position": {"x": -3.14574051, "y": 0.613860548, "z": 1.72399926}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1933966511, "scene_num": 427}, "task_id": "trial_T20190909_061021_313805", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A20FCMWP43CVIU_36H9ULYP65LV32UM93OV5PJRDAKJFY", "high_descs": ["walk to face shelf with tissues", "pick up box of tissues from shelf", "back up to face drawers under shelf", "put tissues in bottom right drawer", "move forward to face top shelf with tissue box", "pick up box of tissues from shelf", "back up to face drawers under shelf", "put tissues in bottom right drawer"], "task_desc": "put two tissue boxes in drawer", "votes": [1, 1, 1]}, {"assignment_id": "A3SFPSMFRSRTB3_3R2PKQ87NZZ5E12XD878OEO0XWOMID", "high_descs": ["Turn around to face dresser.", "Remove tissue box from dresser, to the left of the other tissue box.", "Move to the right, closer to the tub.", "Open lower right drawer, put tissue box in drawer, left back corner, shut drawer.", "Move closer to tissue box on top of counter.", "Pick up tissue box from counter.", "Move to the right towards the tub.", "Open right lower drawer, put box of tissues in drawer, to the right of the other tissue box."], "task_desc": "Move two tissue boxes from dresser to lower right drawer.", "votes": [1, 1, 1]}, {"assignment_id": "A98E8M4QLI9RS_3RGU30DZTDZ19CM3VQS80F30AA8MJI", "high_descs": ["turn around to face the counter ", "pick up the tissue box that is to the left ", "move slightly to the right of the counter", "open up the bottom right drawer, put the box of tissues in to the drawer and close it", "move forwards slightly closer to the counter", "pick up the tissue box from the counter", "move to the right of the counter slightly towards the drawers on the right side", "open the bottom right drawer, but the second box of tissues in to the drawer to the right of the first box, close the drawer"], "task_desc": "put two boxes of tissues in to the drawer", "votes": [0, 1, 1]}]}}