{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000291.png", "low_idx": 67}, {"high_idx": 5, "image_name": "000000292.png", "low_idx": 67}, {"high_idx": 5, "image_name": "000000293.png", "low_idx": 67}, {"high_idx": 5, "image_name": "000000294.png", "low_idx": 67}, {"high_idx": 5, "image_name": "000000295.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000296.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000297.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000298.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000299.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000300.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000301.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000302.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000303.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000304.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000305.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000306.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000307.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000308.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000309.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000310.png", "low_idx": 69}, {"high_idx": 5, "image_name": "000000311.png", "low_idx": 69}, {"high_idx": 5, "image_name": "000000312.png", "low_idx": 69}, {"high_idx": 5, "image_name": "000000313.png", "low_idx": 69}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["egg"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|3|-4|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [5.55414248, 5.55414248, -5.09750176, -5.09750176, 3.814028264, 3.814028264]], "forceVisible": true, "objectId": "Egg|+01.39|+00.95|-01.27"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-7|10|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-10.3232, -10.3232, 9.764, 9.764, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-02.58|+00.90|+02.44"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-7|-3|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [5.55414248, 5.55414248, -5.09750176, -5.09750176, 3.814028264, 3.814028264]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-9.90399932, -9.90399932, -3.132, -3.132, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|+01.39|+00.95|-01.27", "receptacleObjectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+01.39|+00.95|-01.27"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [222, 139, 237, 156], "mask": [[41627, 7], [41926, 10], [42225, 11], [42524, 13], [42823, 15], [43123, 15], [43422, 16], [43722, 16], [44022, 16], [44322, 16], [44622, 16], [44922, 16], [45222, 15], [45523, 14], [45823, 13], [46124, 11], [46425, 9], [46728, 3]], "point": [229, 146]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+01.39|+00.95|-01.27", "placeStationary": true, "receptacleObjectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 237], [20100, 237], [20400, 237], [20700, 237], [21000, 236], [21300, 236], [21600, 236], [21900, 236], [22200, 235], [22500, 235], [22800, 235], [23100, 235], [23400, 234], [23700, 234], [24000, 234], [24300, 234], [24600, 233], [24900, 233], [25200, 233], [25500, 233], [25800, 232], [26100, 232], [26400, 232], [26700, 232], [27000, 231], [27300, 231], [27600, 231], [27900, 231], [28200, 230], [28500, 230], [28800, 230], [29100, 230], [29400, 229], [29700, 229], [30000, 229], [30300, 229], [30600, 228], [30900, 228], [31200, 228], [31500, 228], [31800, 227], [32100, 227], [32400, 227], [32700, 227], [33000, 226], [33300, 226], [33600, 226], [33900, 225], [34200, 225], [34500, 225], [34800, 225], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 237], [20100, 237], [20400, 237], [20700, 237], [21000, 236], [21300, 236], [21600, 236], [21900, 236], [22200, 235], [22500, 235], [22800, 235], [23100, 235], [23400, 234], [23700, 234], [24000, 234], [24300, 234], [24600, 233], [24900, 233], [25200, 233], [25500, 233], [25800, 232], [26100, 232], [26400, 232], [26700, 232], [27000, 231], [27300, 231], [27600, 231], [27900, 231], [28200, 230], [28500, 230], [28800, 230], [29100, 230], [29400, 229], [29700, 157], [29861, 68], [30000, 156], [30163, 66], [30300, 154], [30464, 65], [30600, 154], [30765, 63], [30900, 153], [31065, 63], [31200, 153], [31366, 62], [31500, 152], [31666, 62], [31800, 152], [31967, 60], [32100, 152], [32267, 60], [32400, 152], [32567, 60], [32700, 152], [32867, 60], [33000, 152], [33167, 59], [33300, 152], [33466, 60], [33600, 152], [33766, 60], [33900, 153], [34066, 59], [34200, 153], [34365, 60], [34500, 154], [34664, 61], [34800, 156], [34963, 62], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-10.3232, -10.3232, 9.764, 9.764, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-10.3232, -10.3232, 9.764, 9.764, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+01.39|+00.95|-01.27"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [152, 100, 166, 117], "mask": [[29857, 4], [30156, 7], [30454, 10], [30754, 11], [31053, 12], [31353, 13], [31652, 14], [31952, 15], [32252, 15], [32552, 15], [32852, 15], [33152, 15], [33452, 14], [33752, 14], [34053, 13], [34353, 12], [34654, 10], [34956, 7]], "point": [159, 107]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 237], [20100, 237], [20400, 237], [20700, 237], [21000, 236], [21300, 236], [21600, 236], [21900, 236], [22200, 235], [22500, 235], [22800, 235], [23100, 235], [23400, 234], [23700, 234], [24000, 234], [24300, 234], [24600, 233], [24900, 233], [25200, 233], [25500, 233], [25800, 232], [26100, 232], [26400, 232], [26700, 232], [27000, 231], [27300, 231], [27600, 231], [27900, 231], [28200, 230], [28500, 230], [28800, 230], [29100, 230], [29400, 229], [29700, 229], [30000, 229], [30300, 229], [30600, 228], [30900, 228], [31200, 228], [31500, 228], [31800, 227], [32100, 227], [32400, 227], [32700, 227], [33000, 226], [33300, 226], [33600, 226], [33900, 225], [34200, 225], [34500, 225], [34800, 225], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 243], "mask": [[0, 48599], [48600, 298], [48900, 299], [49200, 299], [49500, 299], [49800, 299], [50100, 299], [50400, 299], [50700, 298], [51000, 297], [51300, 296], [51600, 295], [51901, 293], [52202, 290], [52503, 288], [52805, 285], [53106, 282], [53407, 280], [53708, 278], [54009, 275], [54311, 272], [54612, 270], [54913, 267], [55214, 265], [55515, 262], [55816, 260], [56118, 257], [56419, 254], [56720, 252], [57021, 249], [57322, 247], [57624, 244], [57925, 241], [58226, 239], [58527, 236], [58828, 234], [59129, 232], [59431, 229], [59732, 226], [60033, 224], [60334, 222], [60635, 220], [60936, 218], [61238, 215], [61539, 213], [61840, 211], [62141, 209], [62442, 207], [62744, 204], [63045, 202], [63346, 200], [63647, 197], [63948, 195], [64249, 193], [64551, 190], [64852, 188], [65153, 186], [65454, 184], [65755, 182], [66056, 180], [66358, 177], [66658, 176], [66958, 175], [67258, 174], [67557, 175], [67857, 175], [68157, 174], [68457, 174], [68758, 172], [69059, 170], [69360, 168], [69662, 165], [69966, 157], [70269, 150], [70573, 143], [70876, 136], [71180, 128], [71486, 116], [71792, 56], [71852, 44], [72100, 44], [72156, 33], [72408, 34], [72458, 22], [72718, 22], [72760, 10]], "point": [149, 121]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+01.39|+00.95|-01.27", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 22], [33, 93], [148, 39], [231, 87], [336, 90], [448, 39], [531, 84], [638, 88], [748, 39], [831, 82], [939, 87], [1048, 39], [1130, 81], [1240, 86], [1348, 39], [1430, 80], [1541, 85], [1647, 40], [1729, 80], [1841, 85], [1947, 40], [2028, 80], [2141, 86], [2247, 40], [2328, 79], [2442, 85], [2546, 41], [2627, 79], [2742, 85], [2846, 41], [2926, 80], [3042, 86], [3145, 42], [3226, 80], [3346, 82], [3445, 42], [3525, 80], [3648, 81], [3744, 43], [3824, 82], [3949, 80], [4044, 43], [4124, 82], [4250, 80], [4343, 45], [4423, 83], [4544, 1], [4551, 80], [4642, 46], [4722, 85], [4844, 3], [4851, 81], [4941, 47], [5021, 86], [5145, 3], [5152, 82], [5239, 49], [5320, 88], [5445, 3], [5452, 137], [5619, 89], [5745, 4], [5752, 138], [5918, 91], [6046, 3], [6053, 138], [6217, 92], [6346, 4], [6353, 139], [6516, 94], [6646, 4], [6653, 139], [6815, 95], [6946, 4], [6953, 140], [7114, 97], [7247, 3], [7253, 141], [7413, 99], [7547, 3], [7553, 143], [7711, 101], [7847, 4], [7853, 146], [8007, 106], [8148, 3], [8153, 260], [8448, 3], [8453, 261], [8748, 3], [8753, 261], [9049, 2], [9053, 262], [9349, 2], [9353, 263], [9649, 1], [9653, 263], [9953, 264], [10253, 264], [10553, 265], [10852, 266], [11151, 268], [11451, 269], [11752, 268], [12052, 269], [12352, 269], [12652, 270], [12952, 270], [13252, 271], [13551, 272], [13851, 273], [14150, 275], [14449, 276], [14748, 279], [15046, 282], [15344, 286], [15641, 23358], [39000, 298], [39300, 297], [39600, 296], [39900, 295], [40200, 295], [40500, 294], [40800, 293], [41100, 292], [41400, 291], [41700, 290], [42000, 289], [42300, 289], [42600, 288], [42900, 287], [43200, 286], [43500, 285], [43800, 284], [44100, 283], [44400, 282], [44700, 282], [45000, 281], [45300, 280], [45600, 279], [45900, 278], [46200, 277], [46500, 276], [46800, 275], [47100, 275], [47400, 274], [47700, 273], [48000, 272], [48300, 124], [48426, 145], [48600, 123], [48727, 143], [48900, 121], [49028, 141], [49200, 121], [49328, 140], [49500, 120], [49629, 139], [49800, 119], [49929, 138], [50100, 119], [50229, 137], [50400, 118], [50529, 136], [50700, 118], [50829, 135], [51000, 117], [51129, 134], [51300, 118], [51429, 133], [51600, 118], [51729, 132], [51900, 118], [52029, 132], [52200, 118], [52328, 132], [52500, 118], [52628, 131], [52800, 118], [52928, 130], [53100, 119], [53227, 130], [53400, 120], [53527, 129], [53700, 121], [53826, 129], [54000, 122], [54124, 130], [54300, 254], [54600, 253], [54900, 252], [55200, 251], [55500, 250], [55800, 249], [56100, 248], [56400, 247], [56700, 247], [57000, 246], [57300, 245], [57600, 244], [57900, 243], [58200, 242], [58500, 241], [58800, 240], [59100, 240], [59400, 239], [59700, 238], [60000, 237], [60300, 236], [60600, 235], [60900, 234], [61200, 233], [61500, 233], [61800, 232], [62100, 231], [62400, 230], [62700, 229], [63000, 229], [63300, 229], [63600, 229], [63900, 229], [64200, 229], [64500, 229], [64800, 229], [65100, 230], [65400, 230], [65700, 230], [66000, 230], [66300, 230], [66600, 230], [66900, 231], [67200, 231], [67500, 231], [67800, 231], [68100, 231], [68400, 231], [68700, 230], [69000, 58], [69059, 170], [69300, 57], [69360, 168], [69600, 57], [69662, 165], [69900, 57], [69966, 157], [70200, 57], [70269, 150], [70500, 57], [70573, 143], [70800, 56], [70876, 136], [71100, 56], [71180, 128], [71400, 56], [71486, 116], [71700, 56], [71792, 56], [71852, 44], [72000, 56], [72100, 44], [72156, 33], [72300, 56], [72408, 34], [72458, 22], [72600, 55], [72718, 22], [72760, 10], [72900, 55], [73200, 55], [73500, 55], [73800, 55], [74100, 54], [74400, 54], [74700, 54], [75000, 54], [75300, 54], [75600, 54], [75900, 53], [76200, 53], [76500, 53], [76800, 53], [77100, 53], [77400, 52], [77700, 52], [78000, 52], [78300, 52], [78600, 52], [78900, 51], [79200, 51], [79500, 51], [79800, 51], [80100, 51], [80400, 51], [80700, 50], [81000, 50], [81300, 50], [81600, 50], [81900, 50], [82200, 49], [82500, 49], [82800, 49], [83100, 49], [83400, 49], [83700, 49], [84000, 48], [84300, 48], [84600, 48], [84900, 48], [85200, 48], [85500, 47], [85800, 47], [86100, 47], [86400, 47], [86700, 47], [87000, 47], [87300, 46], [87600, 46], [87900, 46], [88200, 46], [88500, 46], [88800, 45], [89100, 45], [89400, 45], [89700, 45]], "point": [149, 149]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 22], [33, 93], [148, 39], [231, 87], [336, 90], [448, 39], [531, 84], [638, 88], [748, 39], [831, 82], [939, 87], [1048, 39], [1130, 81], [1240, 86], [1348, 39], [1430, 80], [1541, 85], [1647, 40], [1729, 80], [1841, 85], [1947, 40], [2028, 80], [2141, 86], [2247, 40], [2328, 79], [2442, 85], [2546, 41], [2627, 79], [2742, 85], [2846, 41], [2926, 80], [3042, 86], [3145, 42], [3226, 80], [3346, 82], [3445, 42], [3525, 80], [3648, 81], [3744, 43], [3824, 82], [3949, 80], [4044, 43], [4124, 82], [4250, 80], [4343, 45], [4423, 83], [4544, 1], [4551, 80], [4642, 46], [4722, 85], [4844, 3], [4851, 81], [4941, 47], [5021, 86], [5145, 3], [5152, 82], [5239, 49], [5320, 88], [5445, 3], [5452, 137], [5619, 89], [5745, 4], [5752, 138], [5918, 91], [6046, 3], [6053, 138], [6217, 92], [6346, 4], [6353, 139], [6516, 94], [6646, 4], [6653, 139], [6815, 95], [6946, 4], [6953, 140], [7114, 97], [7247, 3], [7253, 141], [7413, 99], [7547, 3], [7553, 143], [7711, 101], [7847, 4], [7853, 146], [8007, 106], [8148, 3], [8153, 260], [8448, 3], [8453, 80], [8536, 178], [8748, 3], [8753, 77], [8838, 176], [9049, 2], [9053, 76], [9140, 175], [9349, 2], [9353, 75], [9441, 175], [9649, 1], [9653, 74], [9741, 175], [9953, 74], [10042, 175], [10253, 73], [10343, 174], [10553, 73], [10643, 175], [10852, 73], [10944, 174], [11151, 74], [11244, 175], [11451, 74], [11544, 176], [11752, 73], [11845, 175], [12052, 73], [12145, 176], [12352, 73], [12445, 176], [12652, 73], [12745, 177], [12952, 73], [13045, 177], [13252, 73], [13345, 178], [13551, 74], [13645, 178], [13851, 74], [13944, 180], [14150, 76], [14244, 181], [14449, 77], [14544, 181], [14748, 79], [14843, 184], [15046, 82], [15142, 186], [15344, 84], [15441, 189], [15641, 89], [15740, 291], [16039, 22960], [39000, 298], [39300, 297], [39600, 296], [39900, 295], [40200, 295], [40500, 294], [40800, 293], [41100, 292], [41400, 291], [41700, 290], [42000, 289], [42300, 289], [42600, 288], [42900, 287], [43200, 286], [43500, 285], [43800, 284], [44100, 283], [44400, 282], [44700, 282], [45000, 281], [45300, 280], [45600, 279], [45900, 278], [46200, 277], [46500, 276], [46800, 275], [47100, 275], [47400, 274], [47700, 273], [48000, 272], [48300, 124], [48426, 145], [48600, 123], [48727, 143], [48900, 121], [49028, 141], [49200, 121], [49328, 140], [49500, 120], [49629, 139], [49800, 119], [49929, 138], [50100, 119], [50229, 137], [50400, 118], [50529, 136], [50700, 118], [50829, 135], [51000, 117], [51129, 134], [51300, 118], [51429, 133], [51600, 118], [51729, 132], [51900, 118], [52029, 132], [52200, 118], [52328, 132], [52500, 118], [52628, 131], [52800, 118], [52928, 130], [53100, 119], [53227, 130], [53400, 120], [53527, 129], [53700, 121], [53826, 129], [54000, 122], [54124, 130], [54300, 254], [54600, 253], [54900, 252], [55200, 251], [55500, 250], [55800, 249], [56100, 248], [56400, 247], [56700, 247], [57000, 246], [57300, 245], [57600, 244], [57900, 243], [58200, 242], [58500, 241], [58800, 240], [59100, 240], [59400, 239], [59700, 238], [60000, 237], [60300, 236], [60600, 235], [60900, 234], [61200, 233], [61500, 233], [61800, 232], [62100, 231], [62400, 230], [62700, 229], [63000, 229], [63300, 229], [63600, 229], [63900, 229], [64200, 229], [64500, 229], [64800, 229], [65100, 230], [65400, 230], [65700, 230], [66000, 230], [66300, 230], [66600, 230], [66900, 231], [67200, 231], [67500, 231], [67800, 231], [68100, 231], [68400, 231], [68700, 230], [69000, 58], [69059, 170], [69300, 57], [69360, 168], [69600, 57], [69662, 165], [69900, 57], [69966, 157], [70200, 57], [70269, 150], [70500, 57], [70573, 143], [70800, 56], [70876, 136], [71100, 56], [71180, 128], [71400, 56], [71486, 116], [71700, 56], [71792, 104], [72000, 56], [72100, 89], [72300, 56], [72408, 72], [72600, 55], [72718, 52], [72900, 55], [73200, 55], [73500, 55], [73800, 55], [74100, 54], [74400, 54], [74700, 54], [75000, 54], [75300, 54], [75600, 54], [75900, 53], [76200, 53], [76500, 53], [76800, 53], [77100, 53], [77400, 52], [77700, 52], [78000, 52], [78300, 52], [78600, 52], [78900, 51], [79200, 51], [79500, 51], [79800, 51], [80100, 51], [80400, 51], [80700, 50], [81000, 50], [81300, 50], [81600, 50], [81900, 50], [82200, 49], [82500, 49], [82800, 49], [83100, 49], [83400, 49], [83700, 49], [84000, 48], [84300, 48], [84600, 48], [84900, 48], [85200, 48], [85500, 47], [85800, 47], [86100, 47], [86400, 47], [86700, 47], [87000, 47], [87300, 46], [87600, 46], [87900, 46], [88200, 46], [88500, 46], [88800, 45], [89100, 45], [89400, 45], [89700, 45]], "point": [149, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan6", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -1.0, "y": 0.9009992, "z": -1.5}, "object_poses": [{"objectName": "Potato_b563bb4b", "position": {"x": -0.08153126, "y": 0.9472332, "z": 1.54067779}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_b563bb4b", "position": {"x": -2.53202271, "y": 1.1255461, "z": -0.7909757}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_1f5d88fd", "position": {"x": -0.211414054, "y": 0.9104642, "z": 1.094}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_850ba9bc", "position": {"x": -2.538684, "y": 0.911448538, "z": 1.2309432}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_850ba9bc", "position": {"x": 1.62055063, "y": 0.9114485, "z": -0.8191675}, "rotation": {"x": 0.0, "y": 44.9999161, "z": 0.0}}, {"objectName": "Spoon_0b8e1a97", "position": {"x": 1.45373893, "y": 0.9197595, "z": -1.06914723}, "rotation": {"x": 0.0, "y": 225.000031, "z": 0.0}}, {"objectName": "Spatula_3e5dadd0", "position": {"x": -2.20641732, "y": 0.925899863, "z": 0.331450462}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_f4ae1c7a", "position": {"x": -2.25749445, "y": 0.799445331, "z": 0.391211122}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_f4ae1c7a", "position": {"x": -2.34871125, "y": 0.799445331, "z": 0.391211122}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_b57df01c", "position": {"x": 0.0483515263, "y": 0.912865162, "z": 0.423983335}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_988570b5", "position": {"x": -2.64507818, "y": 1.53480625, "z": 1.41045642}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_81e9b3dd", "position": {"x": -2.2416985, "y": 0.10997802, "z": 0.48987487}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_209e2ea9", "position": {"x": -2.20641732, "y": 0.996719658, "z": 1.53077412}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_57ff6e2f", "position": {"x": -2.37255049, "y": 0.9725113, "z": 0.9311123}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_57ff6e2f", "position": {"x": -2.485795, "y": 1.15082419, "z": -0.6021706}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pan_adb25946", "position": {"x": 1.6916, "y": 0.9295, "z": 0.0642}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_f4ae1c7a", "position": {"x": -2.196683, "y": 0.799445331, "z": 0.391211122}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_57ff6e2f", "position": {"x": 0.5856571, "y": 0.9725113, "z": -1.36997449}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_988570b5", "position": {"x": -2.45561719, "y": 0.9080944, "z": 1.2309432}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_850ba9bc", "position": {"x": -2.37255049, "y": 0.911448538, "z": 0.331450462}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_209e2ea9", "position": {"x": -0.08153126, "y": 0.996719658, "z": 1.31733882}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_7b0d90af", "position": {"x": -0.6010624, "y": 0.913369656, "z": 1.54067779}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_2802642b", "position": {"x": 1.38853562, "y": 0.953507066, "z": -1.27437544}, "rotation": {"x": 0.0, "y": 225.000031, "z": 0.0}}, {"objectName": "Kettle_0884e440", "position": {"x": 1.69797277, "y": 1.65776753, "z": 0.313828051}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_b563bb4b", "position": {"x": -2.39029, "y": 0.245370969, "z": -0.9021578}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_81e9b3dd", "position": {"x": -2.67743039, "y": 1.53300011, "z": 1.84054685}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6f8b6e53", "position": {"x": -2.538684, "y": 0.994053841, "z": 1.53077412}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_b0ad5ef5", "position": {"x": 0.7707138, "y": 0.9087126, "z": -1.57591856}, "rotation": {"x": 0.0, "y": 44.9999161, "z": 0.0}}, {"objectName": "SoapBottle_b57df01c", "position": {"x": -2.37255049, "y": 0.912865162, "z": 0.6312814}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_3e5dadd0", "position": {"x": 1.41557932, "y": 0.93363, "z": -1.24733233}, "rotation": {"x": 0.0, "y": 45.0000343, "z": 0.0}}, {"objectName": "PaperTowelRoll_40552007", "position": {"x": 1.76969028, "y": 1.01538539, "z": 0.3467031}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f05a14b3", "position": {"x": 1.45373893, "y": 0.9186722, "z": -1.06914723}, "rotation": {"x": 0.0, "y": 225.000031, "z": 0.0}}, {"objectName": "PepperShaker_e9198ae5", "position": {"x": -2.560535, "y": 1.53300011, "z": 2.05722642}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_1f5d88fd", "position": {"x": 1.68793631, "y": 0.184795648, "z": 0.706531167}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_bc458cba", "position": {"x": 1.6916, "y": 0.9295, "z": -0.3359}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_0b8e1a97", "position": {"x": -0.6010624, "y": 0.9120294, "z": 1.31733882}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Tomato_9b863229", "position": {"x": -2.20641732, "y": 0.9532293, "z": 1.830605}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_db7f1139", "position": {"x": -2.39333987, "y": 1.0842129, "z": -1.07418323}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1519762360, "scene_num": 6}, "task_id": "trial_T20190907_184422_458884", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1RLO9LNUJIW5S_3D4CH1LGEDA4Q1RA23EJXULYOLIG9E", "high_descs": ["Turn right and take a step then turn right and walk to the sink in front of you.", "Pick up the egg to the left of the sink.", "Turn left and walk to the wall then turn left and walk to the microwave.", "Cook the egg in the microwave and then get it back out and close the door.", "Turn left and walk to the fridge then turn right to face it.", "Put the egg in the top portion of the fridge to the left of the tomato."], "task_desc": "Put a microwaved egg in the microwave.", "votes": [1, 1, 1, 1, 1]}, {"assignment_id": "A24RGLS3BRZ60J_3GDTJDAPVXS4464M8E60A3AK00B8MM", "high_descs": ["Turn around and go straight to the sink.", "Pick up the egg from the back of the sink.", "Carry the egg and turn left to go straight back the room. Turn left to locate the microwave.", "Put the egg inside the microwave and turn it on. Take the egg out and shut the microwave door.", "Hold the egg and turn to the left to find the fridge, on the right side.", "Open the fridge and place the egg inside."], "task_desc": "Heat and then chill an egg.", "votes": [1, 1, 1, 1, 1]}, {"assignment_id": "A3KF6O09H04SP7_3X73LLYYQ4V3FZQFNJF60VSJ8IWNHJ", "high_descs": ["Turn around and move to the sink.", "Pick up the brown egg from the counter.", "Turn left, move to the back wall, then turn left and move to the microwave.", "Open the microwave, place the egg inside, cook it, then remove the egg.", "Move to the refrigerator to the left.", "Place the egg in the refrigerator."], "task_desc": "Cooking and cooling an egg.", "votes": [1, 1]}]}}