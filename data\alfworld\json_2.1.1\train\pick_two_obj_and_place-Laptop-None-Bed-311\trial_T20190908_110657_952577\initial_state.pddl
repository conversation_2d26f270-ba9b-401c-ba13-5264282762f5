
(define (problem plan_trial_T20190908_110657_952577)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        AlarmClock_bar__plus_02_dot_37_bar__plus_00_dot_82_bar__plus_02_dot_39 - object
        AlarmClock_bar__plus_02_dot_47_bar__plus_00_dot_75_bar__minus_01_dot_57 - object
        AlarmClock_bar__minus_00_dot_88_bar__plus_00_dot_92_bar__plus_04_dot_53 - object
        Blinds_bar__minus_00_dot_50_bar__plus_02_dot_17_bar__minus_01_dot_96 - object
        Book_bar__plus_02_dot_58_bar__plus_00_dot_10_bar__plus_00_dot_82 - object
        Bowl_bar__plus_02_dot_37_bar__plus_00_dot_81_bar__plus_03_dot_27 - object
        Bowl_bar__plus_02_dot_53_bar__plus_00_dot_85_bar__plus_00_dot_23 - object
        Bowl_bar__minus_00_dot_82_bar__plus_00_dot_92_bar__plus_04_dot_35 - object
        CD_bar__plus_02_dot_26_bar__plus_00_dot_81_bar__plus_03_dot_05 - object
        CD_bar__plus_02_dot_53_bar__plus_00_dot_84_bar__plus_01_dot_02 - object
        CD_bar__minus_01_dot_22_bar__plus_00_dot_40_bar__minus_01_dot_23 - object
        CellPhone_bar__plus_02_dot_45_bar__plus_00_dot_84_bar__plus_00_dot_49 - object
        CellPhone_bar__minus_00_dot_95_bar__plus_00_dot_91_bar__plus_04_dot_72 - object
        CellPhone_bar__minus_01_dot_04_bar__plus_00_dot_40_bar__minus_01_dot_40 - object
        Chair_bar__plus_02_dot_28_bar__minus_00_dot_01_bar__plus_03_dot_49 - object
        Chair_bar__plus_02_dot_32_bar__minus_00_dot_01_bar__plus_02_dot_62 - object
        Chair_bar__plus_02_dot_59_bar__minus_00_dot_01_bar__plus_02_dot_14 - object
        CreditCard_bar__plus_02_dot_68_bar__plus_00_dot_81_bar__plus_02_dot_83 - object
        CreditCard_bar__plus_02_dot_78_bar__plus_00_dot_81_bar__plus_02_dot_39 - object
        CreditCard_bar__minus_00_dot_75_bar__plus_00_dot_91_bar__plus_03_dot_80 - object
        KeyChain_bar__plus_02_dot_70_bar__plus_00_dot_10_bar__plus_00_dot_43 - object
        KeyChain_bar__plus_02_dot_85_bar__plus_00_dot_84_bar__plus_00_dot_23 - object
        Lamp_bar__plus_02_dot_62_bar__plus_00_dot_74_bar__minus_01_dot_47 - object
        Lamp_bar__plus_02_dot_69_bar__plus_00_dot_80_bar__plus_03_dot_08 - object
        Laptop_bar__plus_02_dot_36_bar__plus_00_dot_40_bar__minus_00_dot_51 - object
        Laptop_bar__plus_02_dot_68_bar__plus_00_dot_81_bar__plus_03_dot_72 - object
        LaundryHamperLid_bar__plus_03_dot_53_bar__plus_00_dot_76_bar__plus_04_dot_43 - object
        LightSwitch_bar__plus_00_dot_80_bar__plus_01_dot_37_bar__plus_05_dot_25 - object
        Mirror_bar__minus_01_dot_18_bar__plus_01_dot_60_bar__plus_04_dot_35 - object
        Mug_bar__plus_02_dot_58_bar__plus_00_dot_81_bar__plus_02_dot_61 - object
        Mug_bar__plus_02_dot_60_bar__plus_00_dot_74_bar__minus_01_dot_66 - object
        Painting_bar__plus_02_dot_99_bar__plus_01_dot_53_bar__plus_02_dot_55 - object
        Painting_bar__plus_02_dot_99_bar__plus_01_dot_53_bar__plus_03_dot_42 - object
        Pencil_bar__plus_02_dot_68_bar__plus_00_dot_81_bar__plus_03_dot_49 - object
        Pen_bar__plus_02_dot_47_bar__plus_00_dot_75_bar__minus_01_dot_42 - object
        Pen_bar__plus_02_dot_53_bar__plus_00_dot_47_bar__plus_00_dot_62 - object
        Pen_bar__plus_02_dot_85_bar__plus_00_dot_85_bar__plus_00_dot_62 - object
        Pillow_bar__minus_00_dot_59_bar__plus_00_dot_79_bar__plus_01_dot_41 - object
        Pillow_bar__minus_00_dot_83_bar__plus_00_dot_79_bar__plus_02_dot_26 - object
        RemoteControl_bar__plus_02_dot_37_bar__plus_00_dot_81_bar__plus_03_dot_49 - object
        RemoteControl_bar__plus_02_dot_85_bar__plus_00_dot_84_bar__plus_01_dot_02 - object
        Television_bar__plus_02_dot_75_bar__plus_01_dot_32_bar__plus_00_dot_62 - object
        Window_bar__minus_00_dot_45_bar__plus_01_dot_45_bar__minus_02_dot_06 - object
        ArmChair_bar__plus_02_dot_43_bar__plus_00_dot_00_bar__minus_00_dot_57 - receptacle
        ArmChair_bar__minus_01_dot_28_bar__plus_00_dot_00_bar__minus_01_dot_17 - receptacle
        Bed_bar__minus_00_dot_59_bar__plus_00_dot_00_bar__plus_02_dot_30 - receptacle
        CounterTop_bar__minus_00_dot_89_bar__plus_00_dot_95_bar__plus_04_dot_35 - receptacle
        DiningTable_bar__plus_02_dot_58_bar__plus_00_dot_00_bar__plus_03_dot_05 - receptacle
        Drawer_bar__plus_02_dot_55_bar__plus_00_dot_21_bar__plus_00_dot_62 - receptacle
        Drawer_bar__plus_02_dot_55_bar__plus_00_dot_57_bar__plus_00_dot_62 - receptacle
        Dresser_bar__plus_02_dot_55_bar__plus_00_dot_57_bar__plus_00_dot_62 - receptacle
        GarbageCan_bar__minus_00_dot_63_bar__plus_00_dot_01_bar__plus_03_dot_74 - receptacle
        LaundryHamper_bar__plus_03_dot_53_bar__plus_00_dot_01_bar__plus_04_dot_43 - receptacle
        SideTable_bar__plus_02_dot_57_bar__plus_00_dot_01_bar__minus_01_dot_54 - receptacle
        loc_bar_7_bar_13_bar_1_bar_60 - location
        loc_bar_7_bar_10_bar_1_bar_0 - location
        loc_bar__minus_1_bar_18_bar_3_bar_45 - location
        loc_bar_6_bar_3_bar_1_bar_45 - location
        loc_bar_5_bar__minus_3_bar_1_bar_60 - location
        loc_bar__minus_2_bar__minus_6_bar_2_bar_15 - location
        loc_bar_7_bar_10_bar_1_bar_60 - location
        loc_bar_10_bar_17_bar_2_bar_15 - location
        loc_bar_8_bar_3_bar_1_bar_60 - location
        loc_bar_8_bar_8_bar_1_bar_60 - location
        loc_bar__minus_2_bar__minus_6_bar_2_bar__minus_30 - location
        loc_bar_10_bar_18_bar_1_bar_60 - location
        loc_bar__minus_1_bar__minus_5_bar_3_bar_60 - location
        loc_bar__minus_2_bar__minus_5_bar_3_bar_60 - location
        loc_bar_5_bar_0_bar_1_bar_45 - location
        loc_bar_6_bar_21_bar_3_bar_15 - location
        loc_bar__minus_1_bar_17_bar_2_bar_60 - location
        loc_bar__minus_2_bar_2_bar_0_bar_45 - location
        loc_bar_7_bar__minus_6_bar_1_bar_60 - location
        loc_bar_10_bar_18_bar_1_bar_45 - location
        loc_bar_4_bar_17_bar_1_bar_30 - location
        )
    

(:init


        (receptacleType ArmChair_bar__plus_02_dot_43_bar__plus_00_dot_00_bar__minus_00_dot_57 ArmChairType)
        (receptacleType Bed_bar__minus_00_dot_59_bar__plus_00_dot_00_bar__plus_02_dot_30 BedType)
        (receptacleType GarbageCan_bar__minus_00_dot_63_bar__plus_00_dot_01_bar__plus_03_dot_74 GarbageCanType)
        (receptacleType Drawer_bar__plus_02_dot_55_bar__plus_00_dot_57_bar__plus_00_dot_62 DrawerType)
        (receptacleType ArmChair_bar__minus_01_dot_28_bar__plus_00_dot_00_bar__minus_01_dot_17 ArmChairType)
        (receptacleType Dresser_bar__plus_02_dot_55_bar__plus_00_dot_57_bar__plus_00_dot_62 DresserType)
        (receptacleType Drawer_bar__plus_02_dot_55_bar__plus_00_dot_21_bar__plus_00_dot_62 DrawerType)
        (receptacleType LaundryHamper_bar__plus_03_dot_53_bar__plus_00_dot_01_bar__plus_04_dot_43 LaundryHamperType)
        (receptacleType CounterTop_bar__minus_00_dot_89_bar__plus_00_dot_95_bar__plus_04_dot_35 CounterTopType)
        (receptacleType DiningTable_bar__plus_02_dot_58_bar__plus_00_dot_00_bar__plus_03_dot_05 DiningTableType)
        (receptacleType SideTable_bar__plus_02_dot_57_bar__plus_00_dot_01_bar__minus_01_dot_54 SideTableType)
        (objectType CreditCard_bar__plus_02_dot_68_bar__plus_00_dot_81_bar__plus_02_dot_83 CreditCardType)
        (objectType RemoteControl_bar__plus_02_dot_85_bar__plus_00_dot_84_bar__plus_01_dot_02 RemoteControlType)
        (objectType CellPhone_bar__minus_00_dot_95_bar__plus_00_dot_91_bar__plus_04_dot_72 CellPhoneType)
        (objectType Bowl_bar__plus_02_dot_37_bar__plus_00_dot_81_bar__plus_03_dot_27 BowlType)
        (objectType Laptop_bar__plus_02_dot_36_bar__plus_00_dot_40_bar__minus_00_dot_51 LaptopType)
        (objectType Window_bar__minus_00_dot_45_bar__plus_01_dot_45_bar__minus_02_dot_06 WindowType)
        (objectType Book_bar__plus_02_dot_58_bar__plus_00_dot_10_bar__plus_00_dot_82 BookType)
        (objectType CellPhone_bar__minus_01_dot_04_bar__plus_00_dot_40_bar__minus_01_dot_40 CellPhoneType)
        (objectType CreditCard_bar__plus_02_dot_78_bar__plus_00_dot_81_bar__plus_02_dot_39 CreditCardType)
        (objectType Laptop_bar__plus_02_dot_68_bar__plus_00_dot_81_bar__plus_03_dot_72 LaptopType)
        (objectType AlarmClock_bar__minus_00_dot_88_bar__plus_00_dot_92_bar__plus_04_dot_53 AlarmClockType)
        (objectType CellPhone_bar__plus_02_dot_45_bar__plus_00_dot_84_bar__plus_00_dot_49 CellPhoneType)
        (objectType Chair_bar__plus_02_dot_28_bar__minus_00_dot_01_bar__plus_03_dot_49 ChairType)
        (objectType Bowl_bar__minus_00_dot_82_bar__plus_00_dot_92_bar__plus_04_dot_35 BowlType)
        (objectType CD_bar__plus_02_dot_26_bar__plus_00_dot_81_bar__plus_03_dot_05 CDType)
        (objectType Television_bar__plus_02_dot_75_bar__plus_01_dot_32_bar__plus_00_dot_62 TelevisionType)
        (objectType CreditCard_bar__minus_00_dot_75_bar__plus_00_dot_91_bar__plus_03_dot_80 CreditCardType)
        (objectType KeyChain_bar__plus_02_dot_85_bar__plus_00_dot_84_bar__plus_00_dot_23 KeyChainType)
        (objectType Chair_bar__plus_02_dot_59_bar__minus_00_dot_01_bar__plus_02_dot_14 ChairType)
        (objectType AlarmClock_bar__plus_02_dot_37_bar__plus_00_dot_82_bar__plus_02_dot_39 AlarmClockType)
        (objectType Pen_bar__plus_02_dot_47_bar__plus_00_dot_75_bar__minus_01_dot_42 PenType)
        (objectType AlarmClock_bar__plus_02_dot_47_bar__plus_00_dot_75_bar__minus_01_dot_57 AlarmClockType)
        (objectType Mirror_bar__minus_01_dot_18_bar__plus_01_dot_60_bar__plus_04_dot_35 MirrorType)
        (objectType Bowl_bar__plus_02_dot_53_bar__plus_00_dot_85_bar__plus_00_dot_23 BowlType)
        (objectType LightSwitch_bar__plus_00_dot_80_bar__plus_01_dot_37_bar__plus_05_dot_25 LightSwitchType)
        (objectType Pen_bar__plus_02_dot_53_bar__plus_00_dot_47_bar__plus_00_dot_62 PenType)
        (objectType Mug_bar__plus_02_dot_60_bar__plus_00_dot_74_bar__minus_01_dot_66 MugType)
        (objectType LaundryHamperLid_bar__plus_03_dot_53_bar__plus_00_dot_76_bar__plus_04_dot_43 LaundryHamperLidType)
        (objectType Pillow_bar__minus_00_dot_83_bar__plus_00_dot_79_bar__plus_02_dot_26 PillowType)
        (objectType Pillow_bar__minus_00_dot_59_bar__plus_00_dot_79_bar__plus_01_dot_41 PillowType)
        (objectType CD_bar__minus_01_dot_22_bar__plus_00_dot_40_bar__minus_01_dot_23 CDType)
        (objectType Pencil_bar__plus_02_dot_68_bar__plus_00_dot_81_bar__plus_03_dot_49 PencilType)
        (objectType Pen_bar__plus_02_dot_85_bar__plus_00_dot_85_bar__plus_00_dot_62 PenType)
        (objectType KeyChain_bar__plus_02_dot_70_bar__plus_00_dot_10_bar__plus_00_dot_43 KeyChainType)
        (objectType Blinds_bar__minus_00_dot_50_bar__plus_02_dot_17_bar__minus_01_dot_96 BlindsType)
        (objectType CD_bar__plus_02_dot_53_bar__plus_00_dot_84_bar__plus_01_dot_02 CDType)
        (objectType RemoteControl_bar__plus_02_dot_37_bar__plus_00_dot_81_bar__plus_03_dot_49 RemoteControlType)
        (objectType Painting_bar__plus_02_dot_99_bar__plus_01_dot_53_bar__plus_02_dot_55 PaintingType)
        (objectType Mug_bar__plus_02_dot_58_bar__plus_00_dot_81_bar__plus_02_dot_61 MugType)
        (objectType Chair_bar__plus_02_dot_32_bar__minus_00_dot_01_bar__plus_02_dot_62 ChairType)
        (objectType Painting_bar__plus_02_dot_99_bar__plus_01_dot_53_bar__plus_03_dot_42 PaintingType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType BookType)
        (canContain ArmChairType CellPhoneType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (canContain BedType LaptopType)
        (canContain BedType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType PillowType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType CDType)
        (canContain GarbageCanType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType RemoteControlType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType BookType)
        (canContain ArmChairType CellPhoneType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (canContain DresserType PenType)
        (canContain DresserType BookType)
        (canContain DresserType BowlType)
        (canContain DresserType CDType)
        (canContain DresserType MugType)
        (canContain DresserType CellPhoneType)
        (canContain DresserType KeyChainType)
        (canContain DresserType CreditCardType)
        (canContain DresserType LaptopType)
        (canContain DresserType PencilType)
        (canContain DresserType RemoteControlType)
        (canContain DresserType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType RemoteControlType)
        (canContain CounterTopType PenType)
        (canContain CounterTopType BookType)
        (canContain CounterTopType BowlType)
        (canContain CounterTopType CDType)
        (canContain CounterTopType MugType)
        (canContain CounterTopType CellPhoneType)
        (canContain CounterTopType KeyChainType)
        (canContain CounterTopType CreditCardType)
        (canContain CounterTopType LaptopType)
        (canContain CounterTopType PencilType)
        (canContain CounterTopType RemoteControlType)
        (canContain CounterTopType AlarmClockType)
        (canContain DiningTableType PenType)
        (canContain DiningTableType BookType)
        (canContain DiningTableType BowlType)
        (canContain DiningTableType CDType)
        (canContain DiningTableType MugType)
        (canContain DiningTableType CellPhoneType)
        (canContain DiningTableType KeyChainType)
        (canContain DiningTableType CreditCardType)
        (canContain DiningTableType LaptopType)
        (canContain DiningTableType PencilType)
        (canContain DiningTableType RemoteControlType)
        (canContain DiningTableType AlarmClockType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType BowlType)
        (canContain SideTableType CDType)
        (canContain SideTableType MugType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType AlarmClockType)
        (pickupable CreditCard_bar__plus_02_dot_68_bar__plus_00_dot_81_bar__plus_02_dot_83)
        (pickupable RemoteControl_bar__plus_02_dot_85_bar__plus_00_dot_84_bar__plus_01_dot_02)
        (pickupable CellPhone_bar__minus_00_dot_95_bar__plus_00_dot_91_bar__plus_04_dot_72)
        (pickupable Bowl_bar__plus_02_dot_37_bar__plus_00_dot_81_bar__plus_03_dot_27)
        (pickupable Laptop_bar__plus_02_dot_36_bar__plus_00_dot_40_bar__minus_00_dot_51)
        (pickupable Book_bar__plus_02_dot_58_bar__plus_00_dot_10_bar__plus_00_dot_82)
        (pickupable CellPhone_bar__minus_01_dot_04_bar__plus_00_dot_40_bar__minus_01_dot_40)
        (pickupable CreditCard_bar__plus_02_dot_78_bar__plus_00_dot_81_bar__plus_02_dot_39)
        (pickupable Laptop_bar__plus_02_dot_68_bar__plus_00_dot_81_bar__plus_03_dot_72)
        (pickupable AlarmClock_bar__minus_00_dot_88_bar__plus_00_dot_92_bar__plus_04_dot_53)
        (pickupable CellPhone_bar__plus_02_dot_45_bar__plus_00_dot_84_bar__plus_00_dot_49)
        (pickupable Bowl_bar__minus_00_dot_82_bar__plus_00_dot_92_bar__plus_04_dot_35)
        (pickupable CD_bar__plus_02_dot_26_bar__plus_00_dot_81_bar__plus_03_dot_05)
        (pickupable CreditCard_bar__minus_00_dot_75_bar__plus_00_dot_91_bar__plus_03_dot_80)
        (pickupable KeyChain_bar__plus_02_dot_85_bar__plus_00_dot_84_bar__plus_00_dot_23)
        (pickupable AlarmClock_bar__plus_02_dot_37_bar__plus_00_dot_82_bar__plus_02_dot_39)
        (pickupable Pen_bar__plus_02_dot_47_bar__plus_00_dot_75_bar__minus_01_dot_42)
        (pickupable AlarmClock_bar__plus_02_dot_47_bar__plus_00_dot_75_bar__minus_01_dot_57)
        (pickupable Bowl_bar__plus_02_dot_53_bar__plus_00_dot_85_bar__plus_00_dot_23)
        (pickupable Pen_bar__plus_02_dot_53_bar__plus_00_dot_47_bar__plus_00_dot_62)
        (pickupable Mug_bar__plus_02_dot_60_bar__plus_00_dot_74_bar__minus_01_dot_66)
        (pickupable Pillow_bar__minus_00_dot_83_bar__plus_00_dot_79_bar__plus_02_dot_26)
        (pickupable Pillow_bar__minus_00_dot_59_bar__plus_00_dot_79_bar__plus_01_dot_41)
        (pickupable CD_bar__minus_01_dot_22_bar__plus_00_dot_40_bar__minus_01_dot_23)
        (pickupable Pencil_bar__plus_02_dot_68_bar__plus_00_dot_81_bar__plus_03_dot_49)
        (pickupable Pen_bar__plus_02_dot_85_bar__plus_00_dot_85_bar__plus_00_dot_62)
        (pickupable KeyChain_bar__plus_02_dot_70_bar__plus_00_dot_10_bar__plus_00_dot_43)
        (pickupable CD_bar__plus_02_dot_53_bar__plus_00_dot_84_bar__plus_01_dot_02)
        (pickupable RemoteControl_bar__plus_02_dot_37_bar__plus_00_dot_81_bar__plus_03_dot_49)
        (pickupable Mug_bar__plus_02_dot_58_bar__plus_00_dot_81_bar__plus_02_dot_61)
        (isReceptacleObject Bowl_bar__plus_02_dot_37_bar__plus_00_dot_81_bar__plus_03_dot_27)
        (isReceptacleObject Bowl_bar__minus_00_dot_82_bar__plus_00_dot_92_bar__plus_04_dot_35)
        (isReceptacleObject Bowl_bar__plus_02_dot_53_bar__plus_00_dot_85_bar__plus_00_dot_23)
        (isReceptacleObject Mug_bar__plus_02_dot_60_bar__plus_00_dot_74_bar__minus_01_dot_66)
        (isReceptacleObject Mug_bar__plus_02_dot_58_bar__plus_00_dot_81_bar__plus_02_dot_61)
        (openable Drawer_bar__plus_02_dot_55_bar__plus_00_dot_57_bar__plus_00_dot_62)
        (openable Drawer_bar__plus_02_dot_55_bar__plus_00_dot_21_bar__plus_00_dot_62)
        
        (atLocation agent1 loc_bar_4_bar_17_bar_1_bar_30)
        
        (cleanable Bowl_bar__plus_02_dot_37_bar__plus_00_dot_81_bar__plus_03_dot_27)
        (cleanable Bowl_bar__minus_00_dot_82_bar__plus_00_dot_92_bar__plus_04_dot_35)
        (cleanable Bowl_bar__plus_02_dot_53_bar__plus_00_dot_85_bar__plus_00_dot_23)
        (cleanable Mug_bar__plus_02_dot_60_bar__plus_00_dot_74_bar__minus_01_dot_66)
        (cleanable Mug_bar__plus_02_dot_58_bar__plus_00_dot_81_bar__plus_02_dot_61)
        
        (heatable Mug_bar__plus_02_dot_60_bar__plus_00_dot_74_bar__minus_01_dot_66)
        (heatable Mug_bar__plus_02_dot_58_bar__plus_00_dot_81_bar__plus_02_dot_61)
        (coolable Bowl_bar__plus_02_dot_37_bar__plus_00_dot_81_bar__plus_03_dot_27)
        (coolable Bowl_bar__minus_00_dot_82_bar__plus_00_dot_92_bar__plus_04_dot_35)
        (coolable Bowl_bar__plus_02_dot_53_bar__plus_00_dot_85_bar__plus_00_dot_23)
        (coolable Mug_bar__plus_02_dot_60_bar__plus_00_dot_74_bar__minus_01_dot_66)
        (coolable Mug_bar__plus_02_dot_58_bar__plus_00_dot_81_bar__plus_02_dot_61)
        
        
        
        
        
        
        
        (inReceptacle Pillow_bar__minus_00_dot_59_bar__plus_00_dot_79_bar__plus_01_dot_41 Bed_bar__minus_00_dot_59_bar__plus_00_dot_00_bar__plus_02_dot_30)
        (inReceptacle Pillow_bar__minus_00_dot_83_bar__plus_00_dot_79_bar__plus_02_dot_26 Bed_bar__minus_00_dot_59_bar__plus_00_dot_00_bar__plus_02_dot_30)
        (inReceptacle Laptop_bar__plus_02_dot_36_bar__plus_00_dot_40_bar__minus_00_dot_51 ArmChair_bar__plus_02_dot_43_bar__plus_00_dot_00_bar__minus_00_dot_57)
        (inReceptacle CellPhone_bar__plus_02_dot_45_bar__plus_00_dot_84_bar__plus_00_dot_49 Dresser_bar__plus_02_dot_55_bar__plus_00_dot_57_bar__plus_00_dot_62)
        (inReceptacle Bowl_bar__plus_02_dot_53_bar__plus_00_dot_85_bar__plus_00_dot_23 Dresser_bar__plus_02_dot_55_bar__plus_00_dot_57_bar__plus_00_dot_62)
        (inReceptacle Pen_bar__plus_02_dot_85_bar__plus_00_dot_85_bar__plus_00_dot_62 Dresser_bar__plus_02_dot_55_bar__plus_00_dot_57_bar__plus_00_dot_62)
        (inReceptacle Television_bar__plus_02_dot_75_bar__plus_01_dot_32_bar__plus_00_dot_62 Dresser_bar__plus_02_dot_55_bar__plus_00_dot_57_bar__plus_00_dot_62)
        (inReceptacle RemoteControl_bar__plus_02_dot_85_bar__plus_00_dot_84_bar__plus_01_dot_02 Dresser_bar__plus_02_dot_55_bar__plus_00_dot_57_bar__plus_00_dot_62)
        (inReceptacle CD_bar__plus_02_dot_53_bar__plus_00_dot_84_bar__plus_01_dot_02 Dresser_bar__plus_02_dot_55_bar__plus_00_dot_57_bar__plus_00_dot_62)
        (inReceptacle KeyChain_bar__plus_02_dot_85_bar__plus_00_dot_84_bar__plus_00_dot_23 Dresser_bar__plus_02_dot_55_bar__plus_00_dot_57_bar__plus_00_dot_62)
        (inReceptacle CellPhone_bar__minus_01_dot_04_bar__plus_00_dot_40_bar__minus_01_dot_40 ArmChair_bar__minus_01_dot_28_bar__plus_00_dot_00_bar__minus_01_dot_17)
        (inReceptacle KeyChain_bar__plus_02_dot_70_bar__plus_00_dot_10_bar__plus_00_dot_43 Drawer_bar__plus_02_dot_55_bar__plus_00_dot_21_bar__plus_00_dot_62)
        (inReceptacle Book_bar__plus_02_dot_58_bar__plus_00_dot_10_bar__plus_00_dot_82 Drawer_bar__plus_02_dot_55_bar__plus_00_dot_21_bar__plus_00_dot_62)
        (inReceptacle Pen_bar__plus_02_dot_53_bar__plus_00_dot_47_bar__plus_00_dot_62 Drawer_bar__plus_02_dot_55_bar__plus_00_dot_57_bar__plus_00_dot_62)
        (inReceptacle CreditCard_bar__plus_02_dot_68_bar__plus_00_dot_81_bar__plus_02_dot_83 DiningTable_bar__plus_02_dot_58_bar__plus_00_dot_00_bar__plus_03_dot_05)
        (inReceptacle Pencil_bar__plus_02_dot_68_bar__plus_00_dot_81_bar__plus_03_dot_49 DiningTable_bar__plus_02_dot_58_bar__plus_00_dot_00_bar__plus_03_dot_05)
        (inReceptacle CD_bar__plus_02_dot_26_bar__plus_00_dot_81_bar__plus_03_dot_05 DiningTable_bar__plus_02_dot_58_bar__plus_00_dot_00_bar__plus_03_dot_05)
        (inReceptacle RemoteControl_bar__plus_02_dot_37_bar__plus_00_dot_81_bar__plus_03_dot_49 DiningTable_bar__plus_02_dot_58_bar__plus_00_dot_00_bar__plus_03_dot_05)
        (inReceptacle Mug_bar__plus_02_dot_58_bar__plus_00_dot_81_bar__plus_02_dot_61 DiningTable_bar__plus_02_dot_58_bar__plus_00_dot_00_bar__plus_03_dot_05)
        (inReceptacle Bowl_bar__plus_02_dot_37_bar__plus_00_dot_81_bar__plus_03_dot_27 DiningTable_bar__plus_02_dot_58_bar__plus_00_dot_00_bar__plus_03_dot_05)
        (inReceptacle AlarmClock_bar__plus_02_dot_37_bar__plus_00_dot_82_bar__plus_02_dot_39 DiningTable_bar__plus_02_dot_58_bar__plus_00_dot_00_bar__plus_03_dot_05)
        (inReceptacle CreditCard_bar__plus_02_dot_78_bar__plus_00_dot_81_bar__plus_02_dot_39 DiningTable_bar__plus_02_dot_58_bar__plus_00_dot_00_bar__plus_03_dot_05)
        (inReceptacle Laptop_bar__plus_02_dot_68_bar__plus_00_dot_81_bar__plus_03_dot_72 DiningTable_bar__plus_02_dot_58_bar__plus_00_dot_00_bar__plus_03_dot_05)
        (inReceptacle Pen_bar__plus_02_dot_47_bar__plus_00_dot_75_bar__minus_01_dot_42 SideTable_bar__plus_02_dot_57_bar__plus_00_dot_01_bar__minus_01_dot_54)
        (inReceptacle Mug_bar__plus_02_dot_60_bar__plus_00_dot_74_bar__minus_01_dot_66 SideTable_bar__plus_02_dot_57_bar__plus_00_dot_01_bar__minus_01_dot_54)
        (inReceptacle AlarmClock_bar__plus_02_dot_47_bar__plus_00_dot_75_bar__minus_01_dot_57 SideTable_bar__plus_02_dot_57_bar__plus_00_dot_01_bar__minus_01_dot_54)
        (inReceptacle Mirror_bar__minus_01_dot_18_bar__plus_01_dot_60_bar__plus_04_dot_35 CounterTop_bar__minus_00_dot_89_bar__plus_00_dot_95_bar__plus_04_dot_35)
        (inReceptacle Bowl_bar__minus_00_dot_82_bar__plus_00_dot_92_bar__plus_04_dot_35 CounterTop_bar__minus_00_dot_89_bar__plus_00_dot_95_bar__plus_04_dot_35)
        (inReceptacle CreditCard_bar__minus_00_dot_75_bar__plus_00_dot_91_bar__plus_03_dot_80 CounterTop_bar__minus_00_dot_89_bar__plus_00_dot_95_bar__plus_04_dot_35)
        (inReceptacle CellPhone_bar__minus_00_dot_95_bar__plus_00_dot_91_bar__plus_04_dot_72 CounterTop_bar__minus_00_dot_89_bar__plus_00_dot_95_bar__plus_04_dot_35)
        (inReceptacle AlarmClock_bar__minus_00_dot_88_bar__plus_00_dot_92_bar__plus_04_dot_53 CounterTop_bar__minus_00_dot_89_bar__plus_00_dot_95_bar__plus_04_dot_35)
        
        
        (receptacleAtLocation ArmChair_bar__plus_02_dot_43_bar__plus_00_dot_00_bar__minus_00_dot_57 loc_bar_5_bar__minus_3_bar_1_bar_60)
        (receptacleAtLocation ArmChair_bar__minus_01_dot_28_bar__plus_00_dot_00_bar__minus_01_dot_17 loc_bar__minus_1_bar__minus_5_bar_3_bar_60)
        (receptacleAtLocation Bed_bar__minus_00_dot_59_bar__plus_00_dot_00_bar__plus_02_dot_30 loc_bar__minus_2_bar_2_bar_0_bar_45)
        (receptacleAtLocation CounterTop_bar__minus_00_dot_89_bar__plus_00_dot_95_bar__plus_04_dot_35 loc_bar__minus_1_bar_18_bar_3_bar_45)
        (receptacleAtLocation DiningTable_bar__plus_02_dot_58_bar__plus_00_dot_00_bar__plus_03_dot_05 loc_bar_7_bar_13_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_02_dot_55_bar__plus_00_dot_21_bar__plus_00_dot_62 loc_bar_5_bar_0_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__plus_02_dot_55_bar__plus_00_dot_57_bar__plus_00_dot_62 loc_bar_6_bar_3_bar_1_bar_45)
        (receptacleAtLocation Dresser_bar__plus_02_dot_55_bar__plus_00_dot_57_bar__plus_00_dot_62 loc_bar_8_bar_3_bar_1_bar_60)
        (receptacleAtLocation GarbageCan_bar__minus_00_dot_63_bar__plus_00_dot_01_bar__plus_03_dot_74 loc_bar__minus_1_bar_17_bar_2_bar_60)
        (receptacleAtLocation LaundryHamper_bar__plus_03_dot_53_bar__plus_00_dot_01_bar__plus_04_dot_43 loc_bar_10_bar_18_bar_1_bar_60)
        (receptacleAtLocation SideTable_bar__plus_02_dot_57_bar__plus_00_dot_01_bar__minus_01_dot_54 loc_bar_7_bar__minus_6_bar_1_bar_60)
        (objectAtLocation Mug_bar__plus_02_dot_60_bar__plus_00_dot_74_bar__minus_01_dot_66 loc_bar_7_bar__minus_6_bar_1_bar_60)
        (objectAtLocation Pen_bar__plus_02_dot_47_bar__plus_00_dot_75_bar__minus_01_dot_42 loc_bar_7_bar__minus_6_bar_1_bar_60)
        (objectAtLocation CD_bar__plus_02_dot_26_bar__plus_00_dot_81_bar__plus_03_dot_05 loc_bar_7_bar_13_bar_1_bar_60)
        (objectAtLocation Laptop_bar__plus_02_dot_36_bar__plus_00_dot_40_bar__minus_00_dot_51 loc_bar_5_bar__minus_3_bar_1_bar_60)
        (objectAtLocation RemoteControl_bar__plus_02_dot_85_bar__plus_00_dot_84_bar__plus_01_dot_02 loc_bar_8_bar_3_bar_1_bar_60)
        (objectAtLocation KeyChain_bar__plus_02_dot_85_bar__plus_00_dot_84_bar__plus_00_dot_23 loc_bar_8_bar_3_bar_1_bar_60)
        (objectAtLocation CreditCard_bar__plus_02_dot_78_bar__plus_00_dot_81_bar__plus_02_dot_39 loc_bar_7_bar_13_bar_1_bar_60)
        (objectAtLocation Bowl_bar__minus_00_dot_82_bar__plus_00_dot_92_bar__plus_04_dot_35 loc_bar__minus_1_bar_18_bar_3_bar_45)
        (objectAtLocation CellPhone_bar__minus_01_dot_04_bar__plus_00_dot_40_bar__minus_01_dot_40 loc_bar__minus_1_bar__minus_5_bar_3_bar_60)
        (objectAtLocation AlarmClock_bar__plus_02_dot_47_bar__plus_00_dot_75_bar__minus_01_dot_57 loc_bar_7_bar__minus_6_bar_1_bar_60)
        (objectAtLocation AlarmClock_bar__plus_02_dot_37_bar__plus_00_dot_82_bar__plus_02_dot_39 loc_bar_7_bar_13_bar_1_bar_60)
        (objectAtLocation CreditCard_bar__plus_02_dot_68_bar__plus_00_dot_81_bar__plus_02_dot_83 loc_bar_7_bar_13_bar_1_bar_60)
        (objectAtLocation Pen_bar__plus_02_dot_53_bar__plus_00_dot_47_bar__plus_00_dot_62 loc_bar_6_bar_3_bar_1_bar_45)
        (objectAtLocation CellPhone_bar__plus_02_dot_45_bar__plus_00_dot_84_bar__plus_00_dot_49 loc_bar_8_bar_3_bar_1_bar_60)
        (objectAtLocation CD_bar__plus_02_dot_53_bar__plus_00_dot_84_bar__plus_01_dot_02 loc_bar_8_bar_3_bar_1_bar_60)
        (objectAtLocation Bowl_bar__plus_02_dot_37_bar__plus_00_dot_81_bar__plus_03_dot_27 loc_bar_7_bar_13_bar_1_bar_60)
        (objectAtLocation Chair_bar__plus_02_dot_32_bar__minus_00_dot_01_bar__plus_02_dot_62 loc_bar_7_bar_10_bar_1_bar_60)
        (objectAtLocation Book_bar__plus_02_dot_58_bar__plus_00_dot_10_bar__plus_00_dot_82 loc_bar_5_bar_0_bar_1_bar_45)
        (objectAtLocation Chair_bar__plus_02_dot_59_bar__minus_00_dot_01_bar__plus_02_dot_14 loc_bar_8_bar_8_bar_1_bar_60)
        (objectAtLocation LaundryHamperLid_bar__plus_03_dot_53_bar__plus_00_dot_76_bar__plus_04_dot_43 loc_bar_10_bar_18_bar_1_bar_45)
        (objectAtLocation Chair_bar__plus_02_dot_28_bar__minus_00_dot_01_bar__plus_03_dot_49 loc_bar_7_bar_13_bar_1_bar_60)
        (objectAtLocation Mirror_bar__minus_01_dot_18_bar__plus_01_dot_60_bar__plus_04_dot_35 loc_bar__minus_1_bar_18_bar_3_bar_45)
        (objectAtLocation AlarmClock_bar__minus_00_dot_88_bar__plus_00_dot_92_bar__plus_04_dot_53 loc_bar__minus_1_bar_18_bar_3_bar_45)
        (objectAtLocation Television_bar__plus_02_dot_75_bar__plus_01_dot_32_bar__plus_00_dot_62 loc_bar_8_bar_3_bar_1_bar_60)
        (objectAtLocation CellPhone_bar__minus_00_dot_95_bar__plus_00_dot_91_bar__plus_04_dot_72 loc_bar__minus_1_bar_18_bar_3_bar_45)
        (objectAtLocation Bowl_bar__plus_02_dot_53_bar__plus_00_dot_85_bar__plus_00_dot_23 loc_bar_8_bar_3_bar_1_bar_60)
        (objectAtLocation CreditCard_bar__minus_00_dot_75_bar__plus_00_dot_91_bar__plus_03_dot_80 loc_bar__minus_1_bar_18_bar_3_bar_45)
        (objectAtLocation KeyChain_bar__plus_02_dot_70_bar__plus_00_dot_10_bar__plus_00_dot_43 loc_bar_5_bar_0_bar_1_bar_45)
        (objectAtLocation RemoteControl_bar__plus_02_dot_37_bar__plus_00_dot_81_bar__plus_03_dot_49 loc_bar_7_bar_13_bar_1_bar_60)
        (objectAtLocation LightSwitch_bar__plus_00_dot_80_bar__plus_01_dot_37_bar__plus_05_dot_25 loc_bar_6_bar_21_bar_3_bar_15)
        (objectAtLocation Laptop_bar__plus_02_dot_68_bar__plus_00_dot_81_bar__plus_03_dot_72 loc_bar_7_bar_13_bar_1_bar_60)
        (objectAtLocation Pencil_bar__plus_02_dot_68_bar__plus_00_dot_81_bar__plus_03_dot_49 loc_bar_7_bar_13_bar_1_bar_60)
        (objectAtLocation Painting_bar__plus_02_dot_99_bar__plus_01_dot_53_bar__plus_03_dot_42 loc_bar_10_bar_17_bar_2_bar_15)
        (objectAtLocation Painting_bar__plus_02_dot_99_bar__plus_01_dot_53_bar__plus_02_dot_55 loc_bar_7_bar_10_bar_1_bar_0)
        (objectAtLocation Pillow_bar__minus_00_dot_59_bar__plus_00_dot_79_bar__plus_01_dot_41 loc_bar__minus_2_bar_2_bar_0_bar_45)
        (objectAtLocation Pillow_bar__minus_00_dot_83_bar__plus_00_dot_79_bar__plus_02_dot_26 loc_bar__minus_2_bar_2_bar_0_bar_45)
        (objectAtLocation CD_bar__minus_01_dot_22_bar__plus_00_dot_40_bar__minus_01_dot_23 loc_bar__minus_2_bar__minus_5_bar_3_bar_60)
        (objectAtLocation Pen_bar__plus_02_dot_85_bar__plus_00_dot_85_bar__plus_00_dot_62 loc_bar_8_bar_3_bar_1_bar_60)
        (objectAtLocation Window_bar__minus_00_dot_45_bar__plus_01_dot_45_bar__minus_02_dot_06 loc_bar__minus_2_bar__minus_6_bar_2_bar_15)
        (objectAtLocation Mug_bar__plus_02_dot_58_bar__plus_00_dot_81_bar__plus_02_dot_61 loc_bar_7_bar_13_bar_1_bar_60)
        (objectAtLocation Blinds_bar__minus_00_dot_50_bar__plus_02_dot_17_bar__minus_01_dot_96 loc_bar__minus_2_bar__minus_6_bar_2_bar__minus_30)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 LaptopType)
                                    (receptacleType ?r BedType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 LaptopType)
                                            (receptacleType ?r BedType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            