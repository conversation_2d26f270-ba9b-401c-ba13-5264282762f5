{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000118.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000119.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000120.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000121.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000122.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000164.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000165.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000166.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000167.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000168.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000169.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000170.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000171.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000172.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000173.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000174.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000175.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000176.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000177.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000178.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000179.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000233.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000234.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000235.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000236.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000237.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000238.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000239.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000240.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000241.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000242.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000243.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000244.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000245.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000246.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000247.png", "low_idx": 32}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Potato", "parent_target": "SinkBasin", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-8|12|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["potato"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Potato", [-11.4049778, -11.4049778, 13.9195938, 13.9195938, 3.4645432, 3.4645432]], "coordinateReceptacleObjectId": ["CounterTop", [-8.324, -8.324, 15.196, 15.196, 3.4344, 3.4344]], "forceVisible": true, "objectId": "Potato|-02.85|+00.87|+03.48"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-7|13|0|60"}}, {"discrete_action": {"action": "PutObject", "args": ["potato", "sinkbasin"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Potato", [-11.4049778, -11.4049778, 13.9195938, 13.9195938, 3.4645432, 3.4645432]], "coordinateReceptacleObjectId": ["SinkBasin", [-6.4064, -6.4064, 15.0988, 15.0988, 2.748, 2.748]], "forceVisible": true, "objectId": "Potato|-02.85|+00.87|+03.48", "receptacleObjectId": "Sink|-01.70|+00.73|+03.83|SinkBasin"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-6|13|0|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["potato"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Potato", [-5.94653272, -5.94653272, 15.34435272, 15.34435272, 5.61018992, 5.61018992]], "coordinateReceptacleObjectId": ["Microwave", [-6.116, -6.116, 15.5288, 15.5288, 5.00112532, 5.00112532]], "forceVisible": true, "objectId": "Potato|-01.49|+01.40|+03.84"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-7|13|0|60"}}, {"discrete_action": {"action": "PutObject", "args": ["potato", "sinkbasin"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Potato", [-5.94653272, -5.94653272, 15.34435272, 15.34435272, 5.61018992, 5.61018992]], "coordinateReceptacleObjectId": ["SinkBasin", [-6.4064, -6.4064, 15.0988, 15.0988, 2.748, 2.748]], "forceVisible": true, "objectId": "Potato|-01.49|+01.40|+03.84", "receptacleObjectId": "Sink|-01.70|+00.73|+03.83|SinkBasin"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Potato|-02.85|+00.87|+03.48"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [248, 117, 277, 137], "mask": [[35060, 7], [35357, 13], [35655, 17], [35954, 20], [36252, 23], [36551, 25], [36851, 26], [37150, 27], [37449, 29], [37749, 29], [38048, 30], [38348, 30], [38648, 29], [38949, 28], [39249, 27], [39550, 25], [39851, 23], [40152, 21], [40453, 19], [40756, 14], [41059, 8]], "point": [262, 126]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Potato|-02.85|+00.87|+03.48", "placeStationary": true, "receptacleObjectId": "Sink|-01.70|+00.73|+03.83|SinkBasin"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [97, 119, 287, 177], "mask": [[35509, 39], [35592, 75], [35806, 44], [35890, 79], [36105, 47], [36189, 82], [36404, 7], [36413, 40], [36488, 84], [36703, 6], [36714, 39], [36787, 86], [37002, 7], [37014, 40], [37087, 87], [37302, 7], [37314, 40], [37387, 88], [37601, 8], [37614, 40], [37687, 88], [37901, 8], [37914, 41], [37987, 89], [38201, 8], [38213, 42], [38287, 89], [38501, 7], [38513, 42], [38587, 89], [38801, 7], [38813, 42], [38887, 89], [39101, 7], [39113, 42], [39187, 90], [39401, 7], [39413, 42], [39487, 90], [39701, 7], [39713, 42], [39787, 39], [39829, 48], [40000, 8], [40012, 43], [40087, 35], [40133, 45], [40300, 8], [40312, 43], [40387, 34], [40435, 43], [40600, 8], [40612, 43], [40687, 32], [40737, 40], [40900, 8], [40911, 44], [40987, 31], [41038, 38], [41200, 8], [41211, 44], [41287, 31], [41339, 36], [41500, 8], [41511, 44], [41588, 29], [41640, 34], [41800, 8], [41811, 44], [41888, 29], [41941, 33], [42100, 8], [42110, 45], [42188, 29], [42241, 32], [42400, 8], [42410, 45], [42488, 28], [42542, 31], [42699, 9], [42710, 45], [42788, 26], [42842, 31], [42999, 9], [43010, 45], [43088, 23], [43142, 30], [43299, 9], [43310, 45], [43388, 22], [43442, 30], [43599, 9], [43610, 45], [43688, 22], [43742, 29], [43899, 9], [43910, 45], [43988, 21], [44012, 3], [44042, 29], [44199, 8], [44210, 45], [44288, 21], [44312, 2], [44342, 29], [44499, 8], [44510, 45], [44588, 21], [44611, 3], [44641, 30], [44799, 7], [44810, 45], [44889, 20], [44911, 3], [44941, 30], [45098, 8], [45111, 44], [45189, 21], [45212, 1], [45240, 31], [45398, 7], [45412, 43], [45489, 21], [45512, 1], [45539, 32], [45698, 6], [45713, 42], [45789, 21], [45838, 34], [45998, 5], [46013, 42], [46089, 22], [46138, 34], [46298, 5], [46313, 42], [46389, 23], [46437, 36], [46598, 5], [46614, 41], [46689, 23], [46736, 38], [46898, 5], [46913, 42], [46989, 23], [47036, 37], [47198, 5], [47213, 42], [47289, 24], [47335, 26], [47369, 3], [47374, 5], [47382, 3], [47498, 5], [47513, 42], [47589, 24], [47634, 26], [47673, 12], [47797, 6], [47813, 42], [47889, 24], [47933, 26], [47973, 12], [48097, 6], [48112, 43], [48189, 25], [48233, 25], [48273, 13], [48397, 6], [48411, 44], [48490, 25], [48532, 26], [48574, 12], [48697, 7], [48710, 45], [48790, 26], [48831, 26], [48875, 11], [48997, 8], [49009, 46], [49090, 27], [49130, 27], [49175, 11], [49297, 58], [49390, 29], [49429, 28], [49476, 11], [49597, 58], [49690, 33], [49725, 33], [49776, 11], [49897, 58], [49990, 68], [50076, 11], [50197, 58], [50290, 69], [50376, 12], [50497, 58], [50590, 69], [50676, 12], [50797, 58], [50891, 69], [50976, 12], [51097, 57], [51191, 70], [51275, 13], [51397, 57], [51492, 71], [51574, 13], [51698, 55], [51794, 71], [51873, 14], [51999, 53], [52096, 91], [52299, 52], [52423, 63], [52601, 49], [52762, 23], [52902, 45], [53083, 1]], "point": [192, 147]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 25199], [25200, 299], [25500, 299], [25800, 298], [26100, 298], [26400, 298], [26700, 298], [27000, 297], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 292], [32400, 292], [32700, 292], [33000, 291], [33300, 291], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 5}, {"api_action": {"action": "PickupObject", "objectId": "Potato|-01.49|+01.40|+03.84"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [137, 67, 174, 109], "mask": [[19954, 1], [20250, 11], [20548, 15], [20846, 19], [21144, 23], [21443, 25], [21742, 27], [22042, 27], [22341, 29], [22640, 31], [22939, 33], [23239, 34], [23538, 35], [23838, 35], [24138, 35], [24438, 36], [24737, 37], [25037, 37], [25337, 38], [25637, 38], [25937, 38], [26237, 38], [26537, 38], [26837, 38], [27137, 38], [27437, 38], [27738, 37], [28038, 36], [28338, 36], [28638, 36], [28939, 35], [29239, 34], [29539, 34], [29840, 32], [30141, 31], [30441, 30], [30742, 29], [31043, 27], [31345, 23], [31646, 21], [31947, 19], [32250, 14], [32553, 8]], "point": [155, 87]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 13935], [13936, 299], [14236, 291], [14541, 284], [14845, 279], [15146, 277], [15448, 274], [15748, 273], [16049, 272], [16350, 270], [16650, 270], [16951, 269], [17251, 268], [17551, 268], [17852, 266], [18152, 266], [18453, 265], [18753, 264], [19053, 264], [19353, 264], [19653, 264], [19953, 264], [20253, 264], [20553, 264], [20853, 264], [21153, 264], [21453, 264], [21753, 265], [22053, 265], [22353, 265], [22652, 267], [22952, 267], [23252, 268], [23551, 269], [23851, 270], [24151, 271], [24450, 272], [24750, 273], [25049, 150], [25200, 124], [25349, 150], [25500, 124], [25648, 151], [25800, 125], [25947, 151], [26100, 126], [26247, 151], [26400, 127], [26546, 152], [26700, 128], [26845, 153], [27000, 131], [27142, 155], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 292], [32400, 292], [32700, 292], [33000, 291], [33300, 291], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 5}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Potato|-01.49|+01.40|+03.84", "placeStationary": true, "receptacleObjectId": "Sink|-01.70|+00.73|+03.83|SinkBasin"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [97, 119, 287, 177], "mask": [[35509, 39], [35592, 75], [35806, 44], [35890, 79], [36105, 47], [36189, 82], [36404, 7], [36413, 40], [36488, 84], [36703, 6], [36714, 39], [36787, 86], [37002, 7], [37014, 40], [37087, 87], [37302, 7], [37314, 40], [37387, 88], [37601, 8], [37614, 40], [37687, 88], [37901, 8], [37914, 41], [37987, 89], [38201, 8], [38213, 42], [38287, 89], [38501, 7], [38513, 42], [38587, 89], [38801, 7], [38813, 42], [38887, 89], [39101, 7], [39113, 42], [39187, 90], [39401, 7], [39413, 42], [39487, 90], [39701, 7], [39713, 42], [39787, 39], [39829, 48], [40000, 8], [40012, 43], [40087, 35], [40133, 45], [40300, 8], [40312, 43], [40387, 34], [40435, 43], [40600, 8], [40612, 43], [40687, 32], [40737, 40], [40900, 8], [40911, 44], [40987, 31], [41038, 38], [41200, 8], [41211, 44], [41287, 31], [41339, 36], [41500, 8], [41511, 44], [41588, 29], [41640, 34], [41800, 8], [41811, 44], [41888, 29], [41941, 33], [42100, 8], [42110, 45], [42188, 29], [42241, 32], [42400, 8], [42410, 45], [42488, 28], [42542, 31], [42699, 9], [42710, 45], [42788, 26], [42842, 31], [42999, 9], [43010, 45], [43088, 23], [43142, 30], [43299, 9], [43310, 26], [43337, 18], [43388, 22], [43442, 30], [43599, 9], [43610, 22], [43641, 14], [43688, 22], [43742, 29], [43899, 9], [43910, 20], [43943, 12], [43988, 21], [44012, 3], [44042, 29], [44199, 8], [44210, 19], [44244, 11], [44288, 21], [44312, 2], [44342, 29], [44499, 8], [44510, 18], [44545, 10], [44588, 21], [44611, 3], [44641, 30], [44799, 7], [44810, 16], [44846, 9], [44889, 20], [44911, 3], [44941, 30], [45098, 8], [45111, 15], [45146, 9], [45189, 21], [45212, 1], [45240, 31], [45398, 7], [45412, 13], [45447, 8], [45489, 21], [45512, 1], [45539, 32], [45698, 6], [45713, 12], [45747, 8], [45789, 21], [45838, 34], [45998, 5], [46013, 12], [46047, 8], [46089, 22], [46138, 34], [46298, 5], [46313, 11], [46348, 7], [46389, 23], [46437, 36], [46598, 5], [46614, 10], [46648, 7], [46689, 23], [46736, 38], [46898, 5], [46913, 11], [46948, 7], [46989, 23], [47036, 37], [47198, 5], [47213, 11], [47248, 7], [47289, 24], [47335, 26], [47369, 3], [47374, 5], [47382, 3], [47498, 5], [47513, 11], [47548, 7], [47589, 24], [47634, 26], [47673, 12], [47797, 6], [47813, 11], [47848, 7], [47889, 24], [47933, 26], [47973, 12], [48097, 6], [48112, 12], [48148, 7], [48189, 25], [48233, 25], [48273, 13], [48397, 6], [48411, 13], [48448, 7], [48490, 25], [48532, 26], [48574, 12], [48697, 7], [48710, 14], [48747, 8], [48790, 26], [48831, 26], [48875, 11], [48997, 8], [49009, 16], [49047, 8], [49090, 27], [49130, 27], [49175, 11], [49297, 28], [49347, 8], [49390, 29], [49429, 28], [49476, 11], [49597, 28], [49646, 9], [49690, 33], [49725, 33], [49776, 11], [49897, 29], [49945, 10], [49990, 68], [50076, 11], [50197, 30], [50245, 10], [50290, 69], [50376, 12], [50497, 31], [50544, 11], [50590, 69], [50676, 12], [50797, 32], [50842, 13], [50891, 69], [50976, 12], [51097, 34], [51140, 14], [51191, 70], [51275, 13], [51397, 38], [51437, 17], [51492, 71], [51574, 13], [51698, 55], [51794, 71], [51873, 14], [51999, 53], [52096, 91], [52299, 52], [52423, 63], [52601, 49], [52762, 23], [52902, 45], [53083, 1]], "point": [192, 147]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan24", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -0.5, "y": 0.9009992, "z": 2.25}, "object_poses": [{"objectName": "Potato_75837e5e", "position": {"x": -1.48663318, "y": 1.40254748, "z": 3.83608818}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Potato_75837e5e", "position": {"x": -1.053391, "y": 0.9436566, "z": 1.55565929}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_a0957e87", "position": {"x": -0.5249086, "y": 0.900185, "z": 0.9374151}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_a0957e87", "position": {"x": 0.7376116, "y": 0.690837443, "z": 0.9446635}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_af7bbed3", "position": {"x": -2.85124445, "y": 0.823142052, "z": 3.554316}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_8e927511", "position": {"x": 0.950185537, "y": 0.8380999, "z": 2.631965}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Spoon_ac730c84", "position": {"x": -1.90968871, "y": 0.6988694, "z": 3.7747}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Spoon_ac730c84", "position": {"x": -1.31763232, "y": 0.901750147, "z": 1.24653733}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_0cf79ab5", "position": {"x": 0.8395192, "y": 0.6919682, "z": 0.9446635}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_0cf79ab5", "position": {"x": -0.7891498, "y": 0.9011693, "z": 1.24653733}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_ace268e1", "position": {"x": 0.650935531, "y": 0.8592821, "z": 2.3718}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_45eb0ae6", "position": {"x": -1.309319, "y": 0.697119832, "z": 3.69178}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_45eb0ae6", "position": {"x": -1.138281, "y": 0.8227749, "z": 3.637175}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_9f68a721", "position": {"x": -0.5249086, "y": 0.900055349, "z": 1.24653721}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_f44f174d", "position": {"x": -2.5198946, "y": 0.993675232, "z": 2.33388638}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}, {"objectName": "PepperShaker_22dad048", "position": {"x": 0.650935531, "y": 0.819406152, "z": 2.631965}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_22dad048", "position": {"x": -2.54273272, "y": 1.84556532, "z": 1.80120742}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_656d9f06", "position": {"x": 0.7430601, "y": 0.129489064, "z": 2.53324127}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_0cf79ab5", "position": {"x": -1.18551159, "y": 0.9011693, "z": 1.45261872}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_6b465e10", "position": {"x": 0.827999353, "y": 0.8614967, "z": 2.44999862}, "rotation": {"x": -0.00039939495, "y": 9.775586e-05, "z": 0.0002930066}}, {"objectName": "Spatula_8e927511", "position": {"x": 1.09981048, "y": 0.8380999, "z": 0.8951105}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_d2e883cd", "position": {"x": -1.31763232, "y": 1.004906, "z": 1.45261872}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Potato_75837e5e", "position": {"x": -2.85124445, "y": 0.8661358, "z": 3.47989845}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_9f68a721", "position": {"x": -1.2937814, "y": 0.127723277, "z": 3.63610172}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_ace268e1", "position": {"x": -1.31763232, "y": 0.9368028, "z": 0.9374153}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_45eb0ae6", "position": {"x": -1.21623611, "y": 0.8227749, "z": 3.54598141}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_eb577cac", "position": {"x": 0.9133, "y": 0.850800037, "z": 1.7688}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_0c71376c", "position": {"x": 1.02499807, "y": 0.819406152, "z": 2.3718}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_b017884c", "position": {"x": -2.667, "y": 0.812, "z": 3.74}, "rotation": {"x": 0.0, "y": 137.145508, "z": 0.0}}, {"objectName": "PepperShaker_22dad048", "position": {"x": 0.859734058, "y": 0.121948123, "z": 0.9422834}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_af7bbed3", "position": {"x": -0.789149761, "y": 0.900662839, "z": 1.55565929}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Tomato_facc9b39", "position": {"x": -0.657029152, "y": 0.969194233, "z": 1.4526186}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_a0957e87", "position": {"x": 0.7705799, "y": 0.69149965, "z": 2.46021318}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6dc531a2", "position": {"x": -1.31763232, "y": 0.9763836, "z": 1.55565929}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_ac730c84", "position": {"x": -2.8682456, "y": 0.8242294, "z": 3.94783449}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_21fad42f", "position": {"x": -1.53855622, "y": 1.42068064, "z": 3.9672935}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Knife_b53567ce", "position": {"x": -1.457582, "y": 0.846180558, "z": 3.57769036}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_2e53b233", "position": {"x": -1.46801519, "y": 0.6924449, "z": 3.73324013}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_f44f174d", "position": {"x": -2.51989436, "y": 0.765420139, "z": 2.27782869}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}], "object_toggles": [], "random_seed": 363793710, "scene_num": 24}, "task_id": "trial_T20190908_212045_643714", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A2TUUIV61CR0C7_32Z9ZLUT1OBTI9CPK640G6CQSFOOHT", "high_descs": ["Turn left and walk to the front of the oven.", "Grab the egg to the right of the oven.", "Turn right to face the sink.", "Place the egg in the sink.", "Move to the center of the sinks.", "Remove the egg from the microwave.", "Move to the front of the left sink.", "Place the egg in the sink."], "task_desc": "Put the eggs from the counter and microwave in the sink.", "votes": [1, 1]}, {"assignment_id": "AFPMG8TLP1TND_3OHYZ19UGFWE8M4BE0U8ZKPMFLZOAL", "high_descs": ["walk straight then turn left then walk straight towards the oven", "pick up the potato on the counter top next to the oven", "turn around and walk straight then turn left at the sink", "put the potato down inside of the sink", "turn right then walk straight then turn left towards the sink", "open the microwave and take out the potato and close the microwave door", "turn left walk straight then turn right towards the sink and look down", "put the potato down inside the sink next to the other potato "], "task_desc": "clean up potato's and put them into the sink", "votes": [1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3WJ1OXY92D7BNUHV7J5C2YZM0FW8A1", "high_descs": ["Walk forward, then turn left and walk over to the oven.", "Pick up the potato off of the counter to the right of the oven.", "Turn around and walk forward, then turn left and walk over to the sink.", "Put the potato in the left sink.", "Turn right and walk forward, then turn left and look up at the microwave.", "Open the microwave and take the potato out of the microwave, then close the microwave.", "Turn left and walk forward, then turn right to face the counter.", "Put the potato in the left sink basin."], "task_desc": "Move two potatoes into the left sink.", "votes": [1, 1]}]}}