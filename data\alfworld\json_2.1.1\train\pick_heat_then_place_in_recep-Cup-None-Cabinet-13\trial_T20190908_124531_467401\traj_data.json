{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000030.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000031.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000032.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000033.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000034.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000243.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000244.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000245.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000246.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000247.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000248.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000249.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000254.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000255.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000256.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000258.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000259.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000260.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000261.png", "low_idx": 32}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-14|10|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-16.21647836, -16.21647836, 8.98556328, 8.98556328, 0.30596732, 0.30596732]], "coordinateReceptacleObjectId": ["Cabinet", [-15.84405708, -15.84405708, 8.47927284, 8.47927284, 1.555381416, 1.555381416]], "forceVisible": true, "objectId": "Cup|-04.05|+00.08|+02.25"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-11|10|2|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-12|10|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-16.21647836, -16.21647836, 8.98556328, 8.98556328, 0.30596732, 0.30596732]], "coordinateReceptacleObjectId": ["Cabinet", [-15.84405708, -15.84405708, 8.47927284, 8.47927284, 1.555381416, 1.555381416]], "forceVisible": true, "objectId": "Cup|-04.05|+00.08|+02.25", "receptacleObjectId": "Cabinet|-03.96|+00.39|+02.12"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-03.96|+00.39|+02.12"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [49, 159, 143, 211], "mask": [[47449, 91], [47749, 91], [48050, 90], [48350, 90], [48651, 89], [48952, 88], [49253, 87], [49553, 87], [49854, 86], [50155, 86], [50455, 86], [50756, 85], [51057, 84], [51358, 83], [51658, 83], [51959, 82], [52260, 81], [52560, 81], [52861, 80], [53162, 79], [53463, 78], [53763, 78], [54064, 77], [54365, 77], [54666, 76], [54966, 76], [55267, 75], [55568, 74], [55868, 74], [56169, 73], [56470, 72], [56771, 71], [57071, 71], [57372, 70], [57673, 69], [57973, 69], [58274, 68], [58575, 68], [58876, 67], [59176, 67], [59477, 66], [59778, 65], [60078, 65], [60379, 64], [60680, 63], [60981, 62], [61281, 62], [61582, 61], [61883, 60], [62183, 60], [62484, 59], [62785, 59], [63086, 58]], "point": [96, 184]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-04.05|+00.08|+02.25"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [95, 179, 115, 201], "mask": [[53502, 9], [53800, 12], [54099, 15], [54398, 16], [54697, 18], [54996, 20], [55296, 20], [55595, 21], [55895, 21], [56195, 21], [56495, 21], [56795, 21], [57095, 21], [57395, 21], [57695, 21], [57996, 19], [58297, 18], [58597, 18], [58899, 16], [59200, 14], [59501, 12], [59803, 9], [60105, 5]], "point": [105, 189]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-03.96|+00.39|+02.12"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [45, 159, 139, 273], "mask": [[47448, 2], [47452, 84], [47747, 4], [47753, 84], [48047, 5], [48054, 83], [48347, 6], [48354, 83], [48647, 6], [48655, 82], [48947, 7], [48956, 81], [49247, 8], [49256, 81], [49547, 8], [49557, 80], [49847, 9], [49858, 79], [50147, 10], [50158, 79], [50447, 11], [50459, 78], [50747, 11], [50760, 77], [51047, 12], [51061, 77], [51347, 13], [51361, 77], [51647, 13], [51662, 76], [51947, 14], [51963, 75], [52247, 15], [52263, 75], [52547, 15], [52564, 74], [52846, 17], [52865, 73], [53146, 18], [53165, 73], [53446, 19], [53466, 72], [53746, 19], [53767, 71], [54046, 20], [54067, 72], [54346, 21], [54368, 71], [54646, 21], [54669, 70], [54946, 22], [54969, 70], [55246, 23], [55270, 69], [55546, 23], [55571, 68], [55846, 24], [55872, 67], [56146, 25], [56172, 67], [56446, 26], [56473, 66], [56746, 26], [56774, 65], [57046, 27], [57074, 65], [57346, 28], [57375, 65], [57646, 28], [57676, 64], [57946, 29], [57976, 64], [58246, 30], [58277, 61], [58546, 30], [58578, 54], [58846, 31], [58878, 51], [59146, 32], [59179, 49], [59446, 33], [59480, 46], [59746, 33], [59781, 43], [60046, 34], [60081, 42], [60346, 35], [60382, 39], [60646, 35], [60683, 37], [60946, 36], [60983, 35], [61246, 37], [61284, 33], [61546, 38], [61585, 30], [61846, 38], [61885, 28], [62146, 39], [62186, 26], [62446, 40], [62746, 40], [63046, 41], [63346, 41], [63646, 41], [63946, 41], [64246, 41], [64546, 41], [64845, 42], [65145, 42], [65445, 42], [65745, 43], [66045, 43], [66345, 43], [66645, 43], [66945, 43], [67245, 43], [67545, 43], [67845, 43], [68145, 43], [68445, 43], [68745, 43], [69045, 43], [69345, 43], [69645, 43], [69945, 43], [70245, 43], [70545, 43], [70845, 43], [71145, 44], [71445, 44], [71745, 44], [72045, 44], [72345, 44], [72645, 44], [72945, 44], [73245, 44], [73545, 44], [73845, 44], [74145, 44], [74445, 44], [74745, 44], [75045, 44], [75345, 44], [75645, 44], [75945, 44], [76247, 42], [76549, 41], [76851, 39], [77154, 36], [77456, 34], [77758, 32], [78061, 29], [78363, 27], [78665, 25], [78967, 23], [79270, 20], [79572, 18], [79874, 16], [80176, 14], [80479, 11], [80781, 9], [81083, 7], [81385, 5], [81688, 2]], "point": [86, 215]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-04.05|+00.08|+02.25", "placeStationary": true, "receptacleObjectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 125], [14226, 138], [14400, 109], [14544, 120], [14700, 106], [14849, 115], [15000, 105], [15150, 114], [15300, 105], [15450, 114], [15600, 106], [15750, 114], [15900, 106], [16050, 114], [16200, 106], [16350, 114], [16500, 106], [16650, 114], [16800, 106], [16949, 114], [17100, 107], [17249, 114], [17400, 107], [17549, 114], [17700, 107], [17849, 114], [18000, 107], [18149, 114], [18300, 107], [18449, 114], [18600, 107], [18749, 114], [18900, 108], [19048, 115], [19200, 108], [19348, 115], [19500, 108], [19648, 114], [19800, 109], [19947, 115], [20100, 109], [20247, 115], [20400, 110], [20546, 116], [20700, 110], [20846, 116], [21000, 111], [21145, 117], [21300, 111], [21445, 117], [21600, 111], [21745, 117], [21900, 111], [22044, 118], [22200, 112], [22344, 117], [22500, 112], [22644, 117], [22800, 112], [22944, 117], [23100, 112], [23244, 117], [23400, 112], [23544, 117], [23700, 113], [23843, 118], [24000, 113], [24143, 118], [24300, 113], [24443, 118], [24600, 113], [24743, 118], [24900, 113], [25043, 118], [25200, 114], [25343, 117], [25500, 114], [25643, 117], [25800, 114], [25943, 117], [26100, 114], [26243, 117], [26400, 114], [26543, 117], [26700, 114], [26843, 117], [27000, 114], [27143, 117], [27300, 124], [27431, 129], [27600, 260], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [145, 70]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-04.05|+00.08|+02.25"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [105, 48, 149, 92], "mask": [[14225, 1], [14509, 35], [14806, 43], [15105, 45], [15405, 45], [15706, 44], [16006, 44], [16306, 44], [16606, 44], [16906, 43], [17207, 42], [17507, 42], [17807, 42], [18107, 42], [18407, 42], [18707, 42], [19008, 40], [19308, 40], [19608, 40], [19909, 38], [20209, 38], [20510, 36], [20810, 36], [21111, 34], [21411, 34], [21711, 34], [22011, 33], [22312, 32], [22612, 32], [22912, 32], [23212, 32], [23512, 32], [23813, 30], [24113, 30], [24413, 30], [24713, 30], [25013, 30], [25314, 29], [25614, 29], [25914, 29], [26214, 29], [26514, 29], [26814, 29], [27114, 29], [27424, 7]], "point": [127, 69]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-03.96|+00.39|+02.12"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [69, 65, 143, 140], "mask": [[19269, 73], [19569, 73], [19870, 72], [20170, 72], [20470, 72], [20771, 71], [21071, 71], [21371, 71], [21672, 70], [21972, 70], [22272, 70], [22573, 69], [22873, 69], [23174, 68], [23474, 68], [23774, 68], [24075, 67], [24375, 67], [24675, 68], [24976, 67], [25276, 67], [25576, 67], [25877, 66], [26177, 66], [26477, 66], [26778, 65], [27078, 65], [27378, 65], [27679, 64], [27979, 64], [28279, 64], [28580, 63], [28880, 63], [29180, 63], [29481, 62], [29781, 62], [30081, 62], [30382, 61], [30682, 61], [30982, 61], [31283, 60], [31583, 60], [31883, 60], [32184, 59], [32484, 59], [32785, 58], [33085, 58], [33385, 59], [33686, 58], [33986, 58], [34286, 58], [34587, 57], [34887, 57], [35187, 57], [35488, 56], [35788, 56], [36088, 56], [36389, 55], [36689, 55], [36989, 55], [37290, 54], [37590, 54], [37890, 54], [38191, 53], [38491, 53], [38791, 53], [39092, 52], [39392, 52], [39692, 52], [39993, 51], [40293, 51], [40593, 51], [40894, 50], [41194, 50], [41494, 50], [41795, 49]], "point": [106, 101]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-04.05|+00.08|+02.25", "placeStationary": true, "receptacleObjectId": "Cabinet|-03.96|+00.39|+02.12"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [68, 66, 141, 184], "mask": [[19568, 3], [19572, 67], [19868, 3], [19872, 67], [20168, 71], [20468, 4], [20473, 66], [20768, 4], [20773, 66], [21068, 71], [21368, 5], [21374, 65], [21668, 5], [21674, 65], [21968, 71], [22268, 6], [22275, 65], [22568, 6], [22575, 65], [22868, 72], [23168, 7], [23176, 64], [23468, 7], [23476, 64], [23768, 72], [24068, 8], [24077, 63], [24368, 8], [24377, 63], [24668, 72], [24968, 9], [24978, 62], [25268, 9], [25278, 62], [25569, 71], [25869, 9], [25879, 61], [26169, 9], [26179, 61], [26469, 71], [26769, 10], [26780, 60], [27069, 10], [27080, 60], [27369, 71], [27669, 11], [27681, 59], [27969, 11], [27981, 59], [28269, 71], [28569, 12], [28582, 59], [28869, 12], [28882, 59], [29169, 72], [29469, 13], [29483, 58], [29769, 13], [29783, 58], [30069, 72], [30369, 14], [30384, 57], [30669, 14], [30684, 57], [30969, 72], [31269, 15], [31285, 56], [31569, 72], [31869, 72], [32169, 16], [32186, 55], [32469, 72], [32769, 72], [33069, 17], [33087, 54], [33369, 72], [33669, 72], [33969, 18], [33988, 53], [34269, 72], [34569, 72], [34869, 19], [34889, 52], [35169, 73], [35469, 73], [35769, 20], [35790, 52], [36069, 73], [36369, 73], [36669, 21], [36691, 51], [36970, 72], [37270, 72], [37571, 20], [37592, 50], [37871, 71], [38172, 70], [38472, 20], [38493, 49], [38773, 69], [39073, 69], [39374, 19], [39394, 48], [39674, 68], [39974, 21], [39996, 46], [40275, 19], [40296, 46], [40575, 67], [40876, 66], [41176, 19], [41477, 19], [41777, 19], [42078, 18], [42378, 18], [42679, 17], [42979, 17], [43279, 17], [43580, 16], [43880, 16], [44181, 16], [44481, 16], [44782, 15], [45082, 15], [45383, 14], [45683, 14], [45984, 13], [46284, 13], [46584, 13], [46885, 12], [47185, 12], [47486, 11], [47786, 12], [48087, 11], [48387, 11], [48688, 10], [48988, 10], [49289, 9], [49589, 9], [49889, 9], [50190, 8], [50490, 8], [50791, 7], [51091, 7], [51392, 7], [51692, 7], [51993, 6], [52293, 6], [52594, 5], [52894, 5], [53194, 5], [53495, 4], [53795, 4], [54096, 3], [54396, 3], [54697, 3], [54997, 3]], "point": [104, 124]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-03.96|+00.39|+02.12"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [68, 66, 141, 184], "mask": [[19568, 3], [19572, 67], [19868, 3], [19872, 67], [20168, 71], [20468, 4], [20473, 66], [20768, 4], [20773, 66], [21068, 71], [21368, 5], [21374, 65], [21668, 5], [21674, 65], [21968, 71], [22268, 6], [22275, 65], [22568, 6], [22575, 65], [22868, 72], [23168, 7], [23176, 64], [23468, 7], [23476, 64], [23768, 72], [24068, 8], [24077, 63], [24368, 8], [24377, 63], [24668, 72], [24968, 9], [24978, 62], [25268, 9], [25278, 62], [25569, 71], [25869, 9], [25879, 61], [26169, 9], [26179, 61], [26469, 71], [26769, 10], [26780, 60], [27069, 10], [27080, 60], [27369, 71], [27669, 11], [27681, 59], [27969, 11], [27981, 59], [28269, 71], [28569, 12], [28582, 59], [28869, 12], [28882, 59], [29169, 72], [29469, 13], [29483, 58], [29769, 13], [29783, 58], [30069, 72], [30369, 14], [30384, 57], [30669, 14], [30684, 57], [30969, 72], [31269, 15], [31285, 56], [31569, 72], [31869, 72], [32169, 16], [32186, 55], [32469, 72], [32769, 72], [33069, 17], [33087, 40], [33134, 7], [33369, 56], [33436, 5], [33669, 54], [33737, 4], [33969, 18], [33988, 34], [34038, 3], [34269, 53], [34339, 2], [34569, 52], [34639, 2], [34869, 19], [34889, 32], [34939, 2], [35169, 52], [35239, 3], [35469, 52], [35539, 3], [35769, 20], [35790, 31], [35839, 3], [36069, 52], [36139, 3], [36369, 53], [36439, 3], [36669, 21], [36691, 31], [36738, 4], [36970, 53], [37038, 4], [37270, 54], [37337, 5], [37571, 20], [37592, 32], [37637, 5], [37871, 54], [37937, 5], [38172, 53], [38237, 5], [38472, 20], [38493, 32], [38537, 5], [38773, 53], [38836, 6], [39073, 54], [39135, 7], [39374, 19], [39394, 36], [39432, 10], [39674, 68], [39974, 21], [39996, 46], [40275, 19], [40296, 46], [40575, 67], [40876, 66], [41176, 19], [41477, 19], [41777, 19], [42078, 18], [42378, 18], [42679, 17], [42979, 17], [43279, 17], [43580, 16], [43880, 16], [44181, 16], [44481, 16], [44782, 15], [45082, 15], [45383, 14], [45683, 14], [45984, 13], [46284, 13], [46584, 13], [46885, 12], [47185, 12], [47486, 11], [47786, 12], [48087, 11], [48387, 11], [48688, 10], [48988, 10], [49289, 9], [49589, 9], [49889, 9], [50190, 8], [50490, 8], [50791, 7], [51091, 7], [51392, 7], [51692, 7], [51993, 6], [52293, 6], [52594, 5], [52894, 5], [53194, 5], [53495, 4], [53795, 4], [54096, 3], [54396, 3], [54697, 3], [54997, 3]], "point": [104, 124]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan13", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -2.75, "y": 0.8995012, "z": 2.5}, "object_poses": [{"objectName": "Pan_94f6c891", "position": {"x": -0.488323241, "y": 0.9247095, "z": 5.603}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pencil_282a316d", "position": {"x": -1.86012983, "y": 0.92379117, "z": 1.6489948}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -4.335503, "y": 0.923064232, "z": 5.418301}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "ButterKnife_8b572f1d", "position": {"x": -4.18449736, "y": 0.9235421, "z": 4.381836}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_04c898c4", "position": {"x": -2.469, "y": 0.9303, "z": 1.712}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Spoon_8612b3fc", "position": {"x": -1.94325483, "y": 0.9246294, "z": 1.9510051}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_8612b3fc", "position": {"x": -0.181592077, "y": 0.9262294, "z": 5.27366638}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_ebd9a920", "position": {"x": -0.411640465, "y": 0.9400999, "z": 5.27366638}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_ab4d2337", "position": {"x": -3.74827051, "y": 0.104547471, "z": 6.251528}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Plate_53fee662", "position": {"x": -0.301118851, "y": 1.5816828, "z": 3.40980458}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SoapBottle_56566bc4", "position": {"x": -2.109505, "y": 0.9232217, "z": 1.87550259}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9c9ac9d5", "position": {"x": -0.259895742, "y": 1.39299977, "z": 3.72674942}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Tomato_9c9ac9d5", "position": {"x": -0.3487285, "y": 1.12759972, "z": 3.97425079}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Mug_19433bed", "position": {"x": -1.905484, "y": 1.44823647, "z": 1.63855672}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_19433bed", "position": {"x": -0.4098, "y": 0.9556, "z": 6.55700064}, "rotation": {"x": 0.0, "y": 270.000153, "z": 0.0}}, {"objectName": "Apple_d89eec12", "position": {"x": -4.19639969, "y": 0.8444275, "z": 3.22241282}, "rotation": {"x": -1.44077641e-20, "y": 269.999939, "z": -1.40334191e-14}}, {"objectName": "Egg_ab4d2337", "position": {"x": -0.3423407, "y": 1.72169638, "z": 4.035727}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Apple_d89eec12", "position": {"x": -3.712, "y": 0.122178406, "z": 6.41615725}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_52085e6b", "position": {"x": -0.411640465, "y": 0.9548003, "z": 4.944333}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_9933ef2d", "position": {"x": -0.342341483, "y": 1.61432481, "z": 3.22872162}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Fork_1dc44411", "position": {"x": -1.86012983, "y": 0.924048543, "z": 2.02650762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_f227e8eb", "position": {"x": -0.3423393, "y": 1.33290422, "z": 3.97424984}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Plate_53fee662", "position": {"x": -0.348730356, "y": 1.07138276, "z": 3.35549927}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Lettuce_67328aa7", "position": {"x": -4.191, "y": 1.0039, "z": 2.538}, "rotation": {"x": 0.0, "y": 278.173157, "z": 0.0}}, {"objectName": "Bread_b10200c3", "position": {"x": -0.258274853, "y": 1.00465465, "z": 5.932334}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9c9ac9d5", "position": {"x": -0.383562565, "y": 1.39299977, "z": 3.603}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "WineBottle_eafa1419", "position": {"x": -0.334957659, "y": 0.926028669, "z": 4.944333}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_ebd9a920", "position": {"x": -1.94325483, "y": 0.938499868, "z": 1.6489948}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_aa4cec24", "position": {"x": -4.033492, "y": 1.02778542, "z": 4.640952}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_ba2c4270", "position": {"x": -4.05411959, "y": 0.07649183, "z": 2.24639082}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pencil_282a316d", "position": {"x": -3.74827051, "y": 0.06899914, "z": 6.375}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pan_94f6c891", "position": {"x": -2.45436978, "y": 0.9335794, "z": 2.0230875}, "rotation": {"x": 0.0, "y": 240.000244, "z": 0.0}}, {"objectName": "Pot_04c898c4", "position": {"x": -2.92400026, "y": 0.9303, "z": 1.712}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "SaltShaker_c6eeef1e", "position": {"x": -0.565006, "y": 0.92140615, "z": 4.944333}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_56566bc4", "position": {"x": -3.49410057, "y": 0.923221648, "z": 5.41275358}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PepperShaker_ddab876f", "position": {"x": -4.125074, "y": 0.7432669, "z": 4.660389}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "ButterKnife_8b572f1d", "position": {"x": -0.181592077, "y": 0.9251421, "z": 4.615}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -1.777005, "y": 0.923064232, "z": 1.6489948}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_8612b3fc", "position": {"x": -0.503100038, "y": 0.7479368, "z": 5.99718952}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_19433bed", "position": {"x": -4.1590867, "y": 0.7859571, "z": 3.34761643}, "rotation": {"x": -1.44077641e-20, "y": 269.999939, "z": -1.40334191e-14}}], "object_toggles": [], "random_seed": 1231323790, "scene_num": 13}, "task_id": "trial_T20190908_124531_467401", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A3PPLDHC3CG0YN_30BXRYBRP7O1NILRBSVAX0PCVDKWH2", "high_descs": ["Walk up to the counter straight ahead and look down at the cabinets. ", "Open the cabinet on the left side and pick up the cup. ", "Turn around and walk to the stove on the right, look up at the microwave. ", "Open the microwave door, place the cup inside the microwave, close the door, heat up the cup, open the microwave, take the cup out, and close the door. ", "Turn right and face the cabinets below the counter with the head of lettuce. ", "Open the cabinet on the left, place the cup inside the cabinet, and shut the door. "], "task_desc": "To heat a cup and place it in the cabinet. ", "votes": [1, 1]}, {"assignment_id": "A2A028LRDJB7ZB_3CPLWGV3MRQILIUULFGVO0EXJOWN90", "high_descs": ["Move forward  face the cabinet on your left", "open the cabinet on get the cup then close the cabinet", "Turn to your left then walk towards the microwave", "Open the microwave put in and out the cup then close the microwave", "Turn right then head to the same cabinet", "Open the cabinet and place back the cup then close the cabinet"], "task_desc": "Put the heated cup in the cabinet", "votes": [1, 1]}, {"assignment_id": "A17TKHT8FEVH0R_3MMN5BL1W2VQOYKES50LK8MBE7Y3MN", "high_descs": ["Move forward and face the cabinets at the bottom", "Grab the black cup from the bottom left cabinet", "Turn around and go to the microwave", "Heat the cup in the microwave and then take it out", "Turn right and face the cabinets at the bottom", "Put the cup in the bottom left cabinet"], "task_desc": "Put a heated cup in the cabinet", "votes": [1, 1]}]}}