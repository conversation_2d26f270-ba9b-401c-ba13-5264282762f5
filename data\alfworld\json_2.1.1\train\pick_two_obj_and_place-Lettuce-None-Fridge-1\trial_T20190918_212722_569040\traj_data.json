{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000091.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000092.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000093.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000094.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000095.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000096.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000097.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000098.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000099.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000103.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000104.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000105.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000106.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000107.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000108.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000109.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000110.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000111.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000112.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000243.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000244.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000245.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000246.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000247.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000248.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000249.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000283.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000284.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000285.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000286.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000287.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000288.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000289.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000290.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000291.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000292.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000293.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000294.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000295.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000296.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000297.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000298.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000299.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000300.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000301.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000302.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000303.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000304.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000305.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000306.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000307.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000308.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000309.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000310.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000311.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000312.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000313.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000314.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000315.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000316.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000317.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000318.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000319.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000320.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000321.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000322.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000323.png", "low_idx": 59}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Lettuce", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-1|5|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["lettuce"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Lettuce", [-1.3419616, -1.3419616, 3.0292868, 3.0292868, 4.7648902, 4.7648902]], "coordinateReceptacleObjectId": ["CounterTop", [-0.316, -0.316, -0.004, -0.004, 4.5884, 4.5884]], "forceVisible": true, "objectId": "Lettuce|-00.34|+01.19|+00.76"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-5|4|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["lettuce", "fridge"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Lettuce", [-1.3419616, -1.3419616, 3.0292868, 3.0292868, 4.7648902, 4.7648902]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-8.388, -8.388, 4.352, 4.352, 0.0, 0.0]], "forceVisible": true, "objectId": "Lettuce|-00.34|+01.19|+00.76", "receptacleObjectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-5|-3|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["lettuce"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Lettuce", [-7.09938572, -7.09938572, -2.7958604, -2.7958604, 3.96409, 3.96409]], "coordinateReceptacleObjectId": ["CounterTop", [-7.472, -7.472, -4.824, -4.824, 3.7876, 3.7876]], "forceVisible": true, "objectId": "Lettuce|-01.77|+00.99|-00.70"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-5|4|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["lettuce", "fridge"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Lettuce", [-7.09938572, -7.09938572, -2.7958604, -2.7958604, 3.96409, 3.96409]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-8.388, -8.388, 4.352, 4.352, 0.0, 0.0]], "forceVisible": true, "objectId": "Lettuce|-01.77|+00.99|-00.70", "receptacleObjectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Lettuce|-00.34|+01.19|+00.76"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [155, 81, 209, 165], "mask": [[24180, 2], [24478, 9], [24775, 18], [25073, 23], [25371, 27], [25669, 30], [25968, 32], [26266, 36], [26565, 38], [26864, 40], [27163, 42], [27462, 43], [27761, 45], [28061, 45], [28360, 47], [28660, 47], [28959, 49], [29259, 49], [29558, 51], [29858, 51], [30158, 51], [30458, 51], [30757, 53], [31057, 53], [31357, 53], [31657, 53], [31957, 53], [32257, 53], [32557, 53], [32857, 53], [33157, 53], [33457, 53], [33756, 54], [34056, 54], [34356, 54], [34656, 54], [34955, 55], [35255, 55], [35555, 55], [35855, 55], [36155, 55], [36455, 55], [36755, 55], [37056, 54], [37356, 54], [37656, 54], [37956, 54], [38256, 53], [38556, 53], [38857, 52], [39157, 52], [39457, 52], [39758, 51], [40058, 51], [40359, 50], [40659, 50], [40960, 48], [41260, 48], [41560, 48], [41861, 46], [42162, 45], [42462, 45], [42763, 43], [43064, 42], [43364, 41], [43665, 40], [43966, 38], [44267, 37], [44567, 37], [44868, 35], [45169, 34], [45470, 32], [45771, 31], [46072, 30], [46373, 29], [46674, 27], [46975, 26], [47277, 23], [47578, 22], [47879, 20], [48180, 19], [48481, 18], [48782, 16], [49083, 13], [49385, 10]], "point": [182, 122]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 222], "mask": [[0, 17100], [17101, 299], [17402, 298], [17707, 293], [18008, 292], [18308, 292], [18609, 291], [18909, 291], [19210, 290], [19511, 289], [19811, 289], [20112, 288], [20412, 288], [20713, 287], [21014, 286], [21314, 286], [21615, 285], [21915, 285], [22216, 284], [22517, 283], [22817, 283], [23118, 282], [23418, 282], [23719, 281], [24020, 280], [24320, 280], [24621, 279], [24921, 279], [25222, 278], [25523, 277], [25823, 277], [26124, 276], [26424, 276], [26725, 275], [27025, 275], [27326, 274], [27627, 273], [27927, 273], [28228, 272], [28528, 272], [28829, 271], [29130, 270], [29430, 270], [29731, 269], [30031, 269], [30332, 268], [30633, 267], [30933, 267], [31234, 266], [31534, 266], [31835, 265], [32136, 264], [32436, 264], [32737, 263], [33037, 263], [33338, 262], [33639, 261], [33939, 261], [34240, 260], [34540, 260], [34841, 259], [35142, 258], [35442, 258], [35743, 257], [36043, 257], [36344, 256], [36645, 255], [36945, 255], [37246, 254], [37546, 254], [37847, 253], [38147, 253], [38448, 252], [38749, 251], [39049, 251], [39350, 250], [39650, 250], [39951, 249], [40252, 248], [40552, 248], [40853, 247], [41153, 247], [41454, 246], [41755, 245], [42055, 245], [42356, 244], [42656, 244], [42957, 243], [43258, 242], [43558, 242], [43859, 241], [44159, 241], [44460, 239], [44761, 237], [45061, 236], [45362, 234], [45662, 233], [45963, 231], [46264, 229], [46564, 228], [46865, 226], [47165, 225], [47466, 223], [47767, 221], [48067, 220], [48368, 218], [48668, 217], [48969, 215], [49270, 213], [49570, 212], [49871, 210], [50171, 209], [50472, 207], [50772, 206], [51073, 204], [51374, 202], [51674, 201], [51975, 199], [52275, 198], [52576, 196], [52877, 194], [53177, 193], [53478, 191], [53778, 190], [54079, 189], [54380, 187], [54680, 186], [54981, 184], [55281, 183], [55582, 181], [55883, 179], [56183, 178], [56484, 61], [56554, 106], [56784, 59], [56856, 103], [57085, 56], [57158, 100], [57386, 54], [57459, 98], [57686, 53], [57760, 96], [57987, 51], [58061, 94], [58287, 50], [58362, 92], [58588, 49], [58662, 91], [58889, 47], [58963, 89], [59189, 46], [59264, 87], [59489, 46], [59565, 85], [59789, 45], [59865, 84], [60089, 44], [60166, 82], [60389, 44], [60467, 80], [60689, 43], [60767, 79], [60989, 43], [61068, 77], [61289, 42], [61369, 75], [61588, 42], [61670, 73], [61888, 42], [61970, 72], [62188, 41], [62271, 70], [62489, 39], [62572, 69], [62790, 38], [62872, 68], [63091, 36], [63173, 66], [63391, 35], [63474, 64], [63693, 33], [63774, 62], [63996, 29], [64075, 57], [64300, 25], [64375, 54], [64603, 21], [64676, 50], [64907, 16], [64977, 45], [65212, 11], [65277, 40], [65518, 4], [65578, 33], [65878, 27], [66179, 18], [66479, 9]], "point": [149, 110]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Lettuce|-00.34|+01.19|+00.76", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 16013], [16018, 4], [16080, 3], [16087, 17], [16117, 196], [16317, 4], [16380, 4], [16388, 16], [16417, 196], [16616, 5], [16680, 4], [16688, 15], [16718, 194], [16916, 5], [16981, 4], [16988, 15], [17018, 194], [17216, 5], [17281, 4], [17289, 13], [17318, 194], [17516, 5], [17581, 4], [17588, 14], [17618, 194], [17816, 5], [17881, 4], [17888, 14], [17918, 194], [18117, 4], [18181, 3], [18188, 14], [18218, 195], [18417, 4], [18480, 3], [18487, 16], [18518, 196], [18718, 4], [18780, 3], [18787, 16], [18817, 198], [19020, 2], [19080, 1], [19085, 18], [19117, 200], [19383, 21], [19417, 203], [19680, 24], [19717, 206], [19978, 27], [20016, 208], [20277, 29], [20316, 208], [20577, 30], [20615, 209], [20877, 32], [20913, 211], [21177, 248], [21476, 249], [21776, 249], [22076, 249], [22376, 250], [22676, 250], [22976, 250], [23276, 250], [23576, 250], [23876, 250], [24175, 251], [24475, 252], [24775, 252], [25074, 254], [25374, 254], [25673, 256], [25973, 257], [26272, 259], [26571, 261], [26870, 263], [27169, 266], [27467, 269], [27765, 273], [28063, 278], [28361, 284], [28656, 6143], [34800, 298], [35100, 297], [35400, 297], [35700, 296], [36000, 295], [36300, 294], [36600, 293], [36900, 293], [37200, 292], [37500, 291], [37800, 290], [38100, 289], [38400, 288], [38700, 288], [39000, 287], [39300, 286], [39600, 285], [39900, 284], [40200, 284], [40500, 283], [40800, 282], [41100, 281], [41400, 280], [41700, 280], [42000, 279], [42300, 278], [42600, 277], [42900, 276], [43200, 276], [43500, 275], [43800, 274], [44100, 273], [44400, 272], [44700, 272], [45000, 271], [45300, 270], [45600, 269], [45900, 268], [46200, 267], [46500, 267], [46800, 266], [47100, 265], [47400, 264], [47700, 263], [48000, 263], [48300, 262], [48600, 261], [48900, 260], [49200, 259], [49500, 259], [49800, 258], [50100, 257], [50400, 256], [50700, 255], [51000, 255], [51300, 254], [51600, 253], [51900, 252], [52200, 251], [52500, 251], [52800, 250], [53100, 249], [53400, 248], [53700, 247], [54000, 246], [54300, 246], [54600, 245], [54900, 244], [55200, 243], [55500, 242], [55800, 242], [56100, 241], [56400, 145], [56554, 86], [56700, 143], [56856, 83], [57000, 141], [57158, 81], [57300, 140], [57459, 80], [57600, 139], [57760, 79], [57900, 138], [58061, 78], [58200, 137], [58362, 77], [58500, 137], [58662, 78], [58800, 136], [58963, 77], [59100, 135], [59264, 76], [59400, 135], [59565, 75], [59700, 134], [59865, 75], [60000, 133], [60166, 74], [60300, 133], [60467, 74], [60600, 132], [60767, 74], [60900, 132], [61068, 73], [61200, 131], [61369, 72], [61500, 130], [61670, 71], [61800, 130], [61970, 72], [62100, 129], [62271, 70], [62400, 128], [62572, 69], [62700, 89], [62790, 38], [62872, 68], [63000, 89], [63091, 36], [63173, 66], [63300, 89], [63391, 35], [63474, 64], [63600, 88], [63693, 33], [63774, 62], [63900, 88], [63996, 29], [64075, 57], [64200, 88], [64300, 25], [64375, 54], [64500, 88], [64603, 21], [64676, 50], [64800, 88], [64907, 16], [64977, 45], [65100, 88], [65212, 11], [65277, 40], [65400, 88], [65518, 4], [65578, 33], [65700, 88], [65878, 27], [66000, 87], [66179, 18], [66300, 87], [66479, 9], [66600, 87], [66900, 87], [67200, 87], [67500, 87], [67800, 87], [68100, 87], [68400, 86], [68700, 86], [69000, 86], [69300, 86], [69600, 86], [69900, 86], [70200, 86], [70500, 86], [70800, 85], [71100, 85], [71400, 85], [71700, 85], [72000, 85], [72300, 85], [72600, 85], [72900, 85], [73200, 84], [73500, 84], [73800, 84], [74100, 84], [74400, 84], [74700, 84], [75000, 84], [75300, 84], [75600, 83], [75900, 83], [76200, 83], [76500, 83], [76800, 83], [77100, 83], [77400, 83], [77700, 82], [78000, 82], [78300, 82], [78600, 82], [78900, 82], [79200, 82], [79500, 82], [79800, 82], [80100, 81], [80400, 81], [80700, 81], [81000, 81], [81300, 81], [81600, 81], [81900, 81], [82200, 81], [82500, 80], [82800, 80], [83100, 80], [83400, 80], [83700, 80], [84000, 80], [84300, 80], [84600, 80], [84900, 79], [85200, 79], [85500, 79], [85800, 79], [86100, 79], [86400, 79], [86700, 79], [87000, 79], [87300, 78], [87600, 78], [87900, 78], [88200, 78], [88500, 78], [88800, 78], [89100, 78], [89400, 78], [89700, 77]], "point": [149, 149]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 16013], [16018, 4], [16080, 3], [16087, 17], [16117, 196], [16317, 4], [16380, 4], [16388, 16], [16417, 196], [16616, 5], [16680, 4], [16688, 15], [16718, 194], [16916, 5], [16981, 4], [16988, 15], [17018, 194], [17216, 5], [17281, 4], [17289, 13], [17318, 194], [17516, 5], [17581, 4], [17588, 14], [17618, 194], [17816, 5], [17881, 4], [17888, 14], [17918, 194], [18117, 4], [18181, 3], [18188, 14], [18218, 195], [18417, 4], [18480, 3], [18487, 16], [18518, 196], [18718, 4], [18780, 3], [18787, 16], [18817, 198], [19020, 2], [19080, 1], [19085, 18], [19117, 200], [19383, 21], [19417, 203], [19680, 24], [19717, 206], [19978, 27], [20016, 208], [20277, 29], [20316, 208], [20577, 30], [20615, 209], [20877, 32], [20913, 211], [21177, 248], [21476, 249], [21776, 249], [22076, 249], [22376, 250], [22676, 250], [22976, 250], [23276, 250], [23576, 250], [23876, 250], [24175, 251], [24475, 252], [24775, 252], [25074, 254], [25374, 254], [25673, 256], [25973, 257], [26272, 259], [26571, 261], [26870, 263], [27169, 266], [27467, 269], [27765, 273], [28064, 275], [28364, 275], [28643, 2], [28656, 3], [28664, 275], [28943, 3], [28950, 2], [28956, 3], [28964, 275], [29243, 3], [29250, 2], [29257, 2], [29264, 275], [29543, 3], [29550, 2], [29557, 2], [29564, 275], [29843, 3], [29850, 2], [29857, 2], [29864, 275], [30143, 3], [30150, 2], [30157, 2], [30164, 1774], [31961, 278], [32261, 278], [32561, 279], [32860, 280], [33160, 281], [33459, 282], [33759, 283], [34058, 285], [34358, 286], [34657, 142], [34800, 145], [34957, 141], [35100, 145], [35256, 141], [35400, 146], [35556, 141], [35700, 146], [35855, 141], [36000, 147], [36155, 140], [36300, 148], [36454, 140], [36600, 293], [36900, 293], [37200, 292], [37500, 291], [37800, 290], [38100, 289], [38400, 288], [38700, 288], [39000, 287], [39300, 286], [39600, 285], [39900, 284], [40200, 284], [40500, 283], [40800, 282], [41100, 281], [41400, 280], [41700, 280], [42000, 279], [42300, 278], [42600, 277], [42900, 276], [43200, 276], [43500, 275], [43800, 274], [44100, 273], [44400, 272], [44700, 272], [45000, 271], [45300, 270], [45600, 269], [45900, 268], [46200, 267], [46500, 267], [46800, 266], [47100, 265], [47400, 264], [47700, 263], [48000, 263], [48300, 262], [48600, 261], [48900, 260], [49200, 259], [49500, 259], [49800, 258], [50100, 257], [50400, 256], [50700, 255], [51000, 255], [51300, 254], [51600, 253], [51900, 252], [52200, 251], [52500, 251], [52800, 250], [53100, 249], [53400, 248], [53700, 247], [54000, 246], [54300, 246], [54600, 245], [54900, 244], [55200, 243], [55500, 242], [55800, 242], [56100, 241], [56400, 240], [56700, 239], [57000, 239], [57300, 239], [57600, 239], [57900, 239], [58200, 239], [58500, 240], [58800, 240], [59100, 240], [59400, 240], [59700, 240], [60000, 240], [60300, 241], [60600, 241], [60900, 241], [61200, 241], [61500, 241], [61800, 242], [62100, 241], [62400, 241], [62700, 89], [62790, 150], [63000, 89], [63091, 148], [63300, 89], [63391, 147], [63600, 88], [63693, 143], [63900, 88], [63996, 136], [64200, 88], [64300, 129], [64500, 88], [64603, 123], [64800, 88], [64907, 115], [65100, 88], [65212, 105], [65400, 88], [65518, 93], [65700, 88], [65824, 81], [66000, 87], [66132, 65], [66300, 87], [66442, 46], [66600, 87], [66900, 87], [67200, 87], [67500, 87], [67800, 87], [68100, 87], [68400, 86], [68700, 86], [69000, 86], [69300, 86], [69600, 86], [69900, 86], [70200, 86], [70500, 86], [70800, 85], [71100, 85], [71400, 85], [71700, 85], [72000, 85], [72300, 85], [72600, 85], [72900, 85], [73200, 84], [73500, 84], [73800, 84], [74100, 84], [74400, 84], [74700, 84], [75000, 84], [75300, 84], [75600, 83], [75900, 83], [76200, 83], [76500, 83], [76800, 83], [77100, 83], [77400, 83], [77700, 82], [78000, 82], [78300, 82], [78600, 82], [78900, 82], [79200, 82], [79500, 82], [79800, 82], [80100, 81], [80400, 81], [80700, 81], [81000, 81], [81300, 81], [81600, 81], [81900, 81], [82200, 81], [82500, 80], [82800, 80], [83100, 80], [83400, 80], [83700, 80], [84000, 80], [84300, 80], [84600, 80], [84900, 79], [85200, 79], [85500, 79], [85800, 79], [86100, 79], [86400, 79], [86700, 79], [87000, 79], [87300, 78], [87600, 78], [87900, 78], [88200, 78], [88500, 78], [88800, 78], [89100, 78], [89400, 78], [89700, 77]], "point": [149, 149]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Lettuce|-01.77|+00.99|-00.70"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [126, 146, 205, 194], "mask": [[43641, 15], [43939, 25], [44237, 30], [44535, 36], [44834, 40], [45133, 46], [45432, 51], [45731, 55], [46030, 58], [46330, 60], [46629, 63], [46929, 65], [47228, 68], [47528, 70], [47828, 72], [48127, 75], [48427, 77], [48727, 78], [49027, 78], [49327, 79], [49627, 79], [49926, 80], [50226, 80], [50526, 80], [50826, 80], [51126, 80], [51427, 79], [51727, 79], [52027, 78], [52327, 78], [52627, 76], [52927, 74], [53227, 72], [53528, 69], [53828, 67], [54128, 65], [54428, 64], [54728, 62], [55029, 60], [55330, 57], [55630, 55], [55931, 53], [56232, 49], [56533, 46], [56834, 42], [57136, 37], [57438, 31], [57740, 25], [58042, 14]], "point": [165, 169]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 222], "mask": [[0, 17100], [17101, 299], [17402, 298], [17707, 293], [18008, 292], [18308, 292], [18609, 291], [18909, 291], [19210, 290], [19511, 289], [19811, 289], [20112, 288], [20412, 288], [20713, 287], [21014, 286], [21314, 286], [21615, 285], [21915, 285], [22216, 284], [22517, 283], [22817, 283], [23118, 282], [23418, 282], [23719, 281], [24020, 280], [24320, 280], [24621, 279], [24921, 279], [25222, 278], [25523, 277], [25823, 277], [26124, 276], [26424, 276], [26725, 275], [27025, 275], [27326, 274], [27627, 273], [27927, 273], [28228, 272], [28528, 272], [28829, 271], [29130, 270], [29430, 270], [29731, 269], [30031, 269], [30332, 268], [30633, 267], [30933, 267], [31234, 266], [31534, 266], [31835, 265], [32136, 264], [32436, 264], [32737, 263], [33037, 263], [33338, 262], [33639, 261], [33939, 261], [34240, 260], [34540, 260], [34841, 259], [35142, 258], [35442, 258], [35743, 257], [36043, 257], [36344, 256], [36645, 255], [36945, 255], [37246, 254], [37546, 254], [37847, 253], [38147, 253], [38448, 252], [38749, 251], [39049, 251], [39350, 250], [39650, 250], [39951, 249], [40252, 248], [40552, 248], [40853, 247], [41153, 247], [41454, 246], [41755, 245], [42055, 245], [42356, 244], [42656, 244], [42957, 243], [43258, 242], [43558, 242], [43859, 241], [44159, 241], [44460, 239], [44761, 237], [45061, 236], [45362, 234], [45662, 233], [45963, 231], [46264, 229], [46564, 228], [46865, 226], [47165, 225], [47466, 223], [47767, 221], [48067, 220], [48368, 218], [48668, 217], [48969, 215], [49270, 213], [49570, 212], [49871, 210], [50171, 209], [50472, 207], [50772, 206], [51073, 204], [51374, 202], [51674, 201], [51975, 199], [52275, 198], [52576, 196], [52877, 194], [53177, 193], [53478, 191], [53778, 190], [54079, 189], [54380, 187], [54680, 186], [54981, 184], [55281, 183], [55582, 181], [55883, 179], [56183, 178], [56484, 61], [56554, 106], [56784, 59], [56856, 103], [57085, 56], [57158, 100], [57386, 54], [57459, 98], [57686, 53], [57760, 96], [57987, 51], [58061, 94], [58287, 50], [58362, 92], [58588, 49], [58662, 91], [58889, 47], [58963, 89], [59189, 46], [59264, 87], [59489, 46], [59565, 85], [59789, 45], [59865, 84], [60089, 44], [60166, 82], [60389, 44], [60467, 80], [60689, 43], [60767, 79], [60989, 43], [61068, 77], [61289, 42], [61369, 75], [61588, 42], [61670, 73], [61888, 42], [61970, 72], [62188, 41], [62271, 70], [62489, 39], [62572, 69], [62790, 38], [62872, 68], [63091, 36], [63173, 66], [63391, 35], [63474, 64], [63693, 33], [63774, 62], [63996, 29], [64075, 57], [64300, 25], [64375, 54], [64603, 21], [64676, 50], [64907, 16], [64977, 45], [65212, 11], [65277, 40], [65518, 4], [65578, 33], [65878, 27], [66179, 18], [66479, 9]], "point": [149, 110]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Lettuce|-01.77|+00.99|-00.70", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 16013], [16018, 4], [16080, 3], [16087, 17], [16117, 196], [16317, 4], [16380, 4], [16388, 16], [16417, 196], [16616, 5], [16680, 4], [16688, 15], [16718, 194], [16916, 5], [16981, 4], [16988, 15], [17018, 194], [17216, 5], [17281, 4], [17289, 13], [17318, 194], [17516, 5], [17581, 4], [17588, 14], [17618, 194], [17816, 5], [17881, 4], [17888, 14], [17918, 194], [18117, 4], [18181, 3], [18188, 14], [18218, 195], [18417, 4], [18480, 3], [18487, 16], [18518, 196], [18718, 4], [18780, 3], [18787, 16], [18817, 198], [19020, 2], [19080, 1], [19085, 18], [19117, 200], [19383, 21], [19417, 203], [19680, 24], [19717, 206], [19978, 27], [20016, 208], [20277, 29], [20316, 208], [20577, 30], [20615, 209], [20877, 32], [20913, 211], [21177, 248], [21476, 249], [21776, 249], [22076, 249], [22376, 250], [22676, 250], [22976, 250], [23276, 250], [23576, 250], [23876, 250], [24175, 251], [24475, 252], [24775, 252], [25074, 254], [25374, 254], [25673, 256], [25973, 257], [26272, 259], [26571, 261], [26870, 263], [27169, 266], [27467, 269], [27765, 273], [28064, 275], [28364, 275], [28643, 2], [28656, 3], [28664, 275], [28943, 3], [28950, 2], [28956, 3], [28964, 275], [29243, 3], [29250, 2], [29257, 2], [29264, 275], [29543, 3], [29550, 2], [29557, 2], [29564, 275], [29843, 3], [29850, 2], [29857, 2], [29864, 275], [30143, 3], [30150, 2], [30157, 2], [30164, 1774], [31961, 278], [32261, 278], [32561, 279], [32860, 280], [33160, 281], [33459, 282], [33759, 283], [34058, 285], [34358, 286], [34657, 142], [34800, 145], [34957, 141], [35100, 145], [35256, 141], [35400, 146], [35556, 141], [35700, 146], [35855, 141], [36000, 147], [36155, 140], [36300, 148], [36454, 140], [36600, 293], [36900, 293], [37200, 292], [37500, 291], [37800, 290], [38100, 289], [38400, 288], [38700, 288], [39000, 287], [39300, 286], [39600, 285], [39900, 284], [40200, 284], [40500, 283], [40800, 282], [41100, 281], [41400, 280], [41700, 280], [42000, 279], [42300, 278], [42600, 277], [42900, 276], [43200, 276], [43500, 275], [43800, 274], [44100, 273], [44400, 272], [44700, 272], [45000, 271], [45300, 270], [45600, 269], [45900, 268], [46200, 267], [46500, 267], [46800, 266], [47100, 265], [47400, 264], [47700, 263], [48000, 263], [48300, 262], [48600, 261], [48900, 260], [49200, 259], [49500, 259], [49800, 258], [50100, 257], [50400, 256], [50700, 255], [51000, 255], [51300, 254], [51600, 253], [51900, 252], [52200, 251], [52500, 251], [52800, 250], [53100, 249], [53400, 248], [53700, 247], [54000, 246], [54300, 246], [54600, 245], [54900, 244], [55200, 243], [55500, 242], [55800, 242], [56100, 241], [56400, 145], [56554, 86], [56700, 143], [56856, 83], [57000, 141], [57158, 81], [57300, 140], [57459, 80], [57600, 139], [57760, 79], [57900, 138], [58061, 78], [58200, 137], [58362, 77], [58500, 137], [58662, 78], [58800, 136], [58963, 77], [59100, 135], [59264, 76], [59400, 135], [59565, 75], [59700, 134], [59865, 75], [60000, 133], [60166, 74], [60300, 133], [60467, 74], [60600, 132], [60767, 74], [60900, 132], [61068, 73], [61200, 131], [61369, 72], [61500, 130], [61670, 71], [61800, 130], [61970, 72], [62100, 129], [62271, 70], [62400, 128], [62572, 69], [62700, 89], [62790, 38], [62872, 68], [63000, 89], [63091, 36], [63173, 66], [63300, 89], [63391, 35], [63474, 64], [63600, 88], [63693, 33], [63774, 62], [63900, 88], [63996, 29], [64075, 57], [64200, 88], [64300, 25], [64375, 54], [64500, 88], [64603, 21], [64676, 50], [64800, 88], [64907, 16], [64977, 45], [65100, 88], [65212, 11], [65277, 40], [65400, 88], [65518, 4], [65578, 33], [65700, 88], [65878, 27], [66000, 87], [66179, 18], [66300, 87], [66479, 9], [66600, 87], [66900, 87], [67200, 87], [67500, 87], [67800, 87], [68100, 87], [68400, 86], [68700, 86], [69000, 86], [69300, 86], [69600, 86], [69900, 86], [70200, 86], [70500, 86], [70800, 85], [71100, 85], [71400, 85], [71700, 85], [72000, 85], [72300, 85], [72600, 85], [72900, 85], [73200, 84], [73500, 84], [73800, 84], [74100, 84], [74400, 84], [74700, 84], [75000, 84], [75300, 84], [75600, 83], [75900, 83], [76200, 83], [76500, 83], [76800, 83], [77100, 83], [77400, 83], [77700, 82], [78000, 82], [78300, 82], [78600, 82], [78900, 82], [79200, 82], [79500, 82], [79800, 82], [80100, 81], [80400, 81], [80700, 81], [81000, 81], [81300, 81], [81600, 81], [81900, 81], [82200, 81], [82500, 80], [82800, 80], [83100, 80], [83400, 80], [83700, 80], [84000, 80], [84300, 80], [84600, 80], [84900, 79], [85200, 79], [85500, 79], [85800, 79], [86100, 79], [86400, 79], [86700, 79], [87000, 79], [87300, 78], [87600, 78], [87900, 78], [88200, 78], [88500, 78], [88800, 78], [89100, 78], [89400, 78], [89700, 77]], "point": [149, 149]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 16013], [16018, 4], [16080, 3], [16087, 17], [16117, 196], [16317, 4], [16380, 4], [16388, 16], [16417, 196], [16616, 5], [16680, 4], [16688, 15], [16718, 194], [16916, 5], [16981, 4], [16988, 15], [17018, 194], [17216, 5], [17281, 4], [17289, 13], [17318, 194], [17516, 5], [17581, 4], [17588, 14], [17618, 194], [17816, 5], [17881, 4], [17888, 14], [17918, 194], [18117, 4], [18181, 3], [18188, 14], [18218, 195], [18417, 4], [18480, 3], [18487, 16], [18518, 196], [18718, 4], [18780, 3], [18787, 16], [18817, 198], [19020, 2], [19080, 1], [19085, 18], [19117, 200], [19383, 21], [19417, 203], [19680, 24], [19717, 206], [19978, 27], [20016, 208], [20277, 29], [20316, 208], [20577, 30], [20615, 209], [20877, 32], [20913, 211], [21177, 248], [21476, 249], [21776, 249], [22076, 249], [22376, 250], [22676, 6], [22683, 3], [22687, 239], [22976, 3], [22983, 3], [22989, 237], [23276, 3], [23283, 3], [23290, 236], [23576, 3], [23583, 3], [23590, 2], [23594, 232], [23876, 3], [23883, 3], [23890, 2], [23895, 231], [24176, 3], [24183, 3], [24190, 3], [24196, 230], [24476, 3], [24483, 3], [24490, 3], [24497, 230], [24776, 3], [24784, 2], [24790, 3], [24797, 230], [25076, 3], [25084, 2], [25090, 3], [25097, 231], [25377, 2], [25384, 3], [25390, 3], [25397, 231], [25677, 2], [25684, 3], [25690, 3], [25697, 232], [25977, 2], [25984, 3], [25990, 3], [25997, 233], [26272, 1], [26277, 3], [26284, 3], [26291, 2], [26297, 234], [26571, 2], [26577, 3], [26584, 3], [26591, 2], [26597, 235], [26870, 3], [26877, 3], [26884, 3], [26891, 3], [26898, 235], [27169, 4], [27177, 3], [27184, 3], [27191, 3], [27198, 237], [27467, 6], [27477, 3], [27484, 3], [27491, 3], [27498, 238], [27765, 8], [27777, 3], [27784, 3], [27791, 3], [27798, 240], [28064, 5], [28070, 3], [28077, 3], [28085, 2], [28091, 3], [28098, 241], [28364, 5], [28370, 3], [28377, 3], [28385, 2], [28391, 3], [28398, 241], [28643, 2], [28656, 3], [28664, 5], [28670, 3], [28677, 3], [28685, 3], [28691, 3], [28698, 241], [28943, 3], [28950, 2], [28956, 3], [28964, 5], [28970, 3], [28977, 3], [28985, 3], [28991, 3], [28998, 241], [29243, 3], [29250, 2], [29257, 2], [29264, 9], [29277, 3], [29285, 3], [29292, 2], [29298, 241], [29543, 3], [29550, 2], [29557, 2], [29564, 9], [29577, 3], [29585, 3], [29592, 3], [29598, 241], [29843, 3], [29850, 2], [29857, 2], [29864, 6], [29871, 2], [29878, 2], [29885, 3], [29892, 3], [29898, 241], [30143, 3], [30150, 2], [30157, 2], [30164, 6], [30171, 3], [30178, 3], [30185, 3], [30192, 3], [30198, 1740], [31961, 11], [31997, 242], [32261, 12], [32296, 243], [32561, 12], [32596, 244], [32860, 14], [32896, 244], [33160, 15], [33195, 246], [33459, 16], [33495, 246], [33759, 17], [33794, 248], [34058, 19], [34094, 249], [34358, 19], [34393, 251], [34657, 21], [34693, 106], [34800, 145], [34957, 22], [34993, 105], [35100, 145], [35256, 24], [35292, 105], [35400, 146], [35556, 25], [35592, 105], [35700, 146], [35855, 27], [35891, 105], [36000, 147], [36155, 28], [36191, 104], [36300, 148], [36454, 30], [36489, 105], [36600, 293], [36900, 293], [37200, 292], [37500, 291], [37800, 290], [38100, 289], [38400, 288], [38700, 288], [39000, 287], [39300, 286], [39600, 285], [39900, 284], [40200, 284], [40500, 283], [40800, 282], [41100, 281], [41400, 280], [41700, 280], [42000, 279], [42300, 278], [42600, 277], [42900, 276], [43200, 276], [43500, 275], [43800, 274], [44100, 273], [44400, 272], [44700, 272], [45000, 271], [45300, 270], [45600, 269], [45900, 268], [46200, 267], [46500, 267], [46800, 266], [47100, 265], [47400, 264], [47700, 263], [48000, 263], [48300, 262], [48600, 261], [48900, 260], [49200, 259], [49500, 259], [49800, 258], [50100, 257], [50400, 256], [50700, 255], [51000, 255], [51300, 254], [51600, 253], [51900, 252], [52200, 251], [52500, 251], [52800, 250], [53100, 249], [53400, 248], [53700, 247], [54000, 246], [54300, 246], [54600, 245], [54900, 244], [55200, 243], [55500, 242], [55800, 242], [56100, 241], [56400, 240], [56700, 239], [57000, 239], [57300, 239], [57600, 239], [57900, 239], [58200, 239], [58500, 240], [58800, 240], [59100, 240], [59400, 240], [59700, 240], [60000, 240], [60300, 241], [60600, 241], [60900, 241], [61200, 241], [61500, 241], [61800, 242], [62100, 241], [62400, 241], [62700, 89], [62790, 150], [63000, 89], [63091, 148], [63300, 89], [63391, 147], [63600, 88], [63693, 143], [63900, 88], [63996, 136], [64200, 88], [64300, 129], [64500, 88], [64603, 123], [64800, 88], [64907, 115], [65100, 88], [65212, 105], [65400, 88], [65518, 93], [65700, 88], [65824, 81], [66000, 87], [66132, 65], [66300, 87], [66442, 46], [66600, 87], [66900, 87], [67200, 87], [67500, 87], [67800, 87], [68100, 87], [68400, 86], [68700, 86], [69000, 86], [69300, 86], [69600, 86], [69900, 86], [70200, 86], [70500, 86], [70800, 85], [71100, 85], [71400, 85], [71700, 85], [72000, 85], [72300, 85], [72600, 85], [72900, 85], [73200, 84], [73500, 84], [73800, 84], [74100, 84], [74400, 84], [74700, 84], [75000, 84], [75300, 84], [75600, 83], [75900, 83], [76200, 83], [76500, 83], [76800, 83], [77100, 83], [77400, 83], [77700, 82], [78000, 82], [78300, 82], [78600, 82], [78900, 82], [79200, 82], [79500, 82], [79800, 82], [80100, 81], [80400, 81], [80700, 81], [81000, 81], [81300, 81], [81600, 81], [81900, 81], [82200, 81], [82500, 80], [82800, 80], [83100, 80], [83400, 80], [83700, 80], [84000, 80], [84300, 80], [84600, 80], [84900, 79], [85200, 79], [85500, 79], [85800, 79], [86100, 79], [86400, 79], [86700, 79], [87000, 79], [87300, 78], [87600, 78], [87900, 78], [88200, 78], [88500, 78], [88800, 78], [89100, 78], [89400, 78], [89700, 77]], "point": [149, 149]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan1", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 0.0, "y": 0.9009995, "z": -1.25}, "object_poses": [{"objectName": "Potato_dc7b7f7e", "position": {"x": -2.08159924, "y": 1.53346038, "z": 1.26004815}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_dc7b7f7e", "position": {"x": -0.07077502, "y": 1.82098532, "z": -2.514165}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_01a9d33b", "position": {"x": 0.8363187, "y": 0.478232384, "z": -2.38039064}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_01a9d33b", "position": {"x": -1.60805786, "y": 0.475677431, "z": -0.100144826}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_ba789e3c", "position": {"x": -0.84807694, "y": 0.908318639, "z": -2.5602715}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_811f3b02", "position": {"x": 0.969296634, "y": 0.949768364, "z": -2.637633}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_811f3b02", "position": {"x": -0.473685682, "y": 1.15066838, "z": 0.7573217}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_0fe5b61f", "position": {"x": 0.393203825, "y": 0.141623616, "z": -2.23962021}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "SaltShaker_b714915a", "position": {"x": -1.10414863, "y": 0.133306861, "z": -2.55404758}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_816f44ce", "position": {"x": -0.473685682, "y": 1.10790622, "z": 0.5045478}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_816f44ce", "position": {"x": 1.58455455, "y": 0.8771372, "z": -2.59076715}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Lettuce_e166a32f", "position": {"x": -1.77484643, "y": 0.9910225, "z": -0.6989651}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a0b666be", "position": {"x": -0.473685682, "y": 1.16217768, "z": 0.251773834}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a0b666be", "position": {"x": -0.173900008, "y": 1.84107757, "z": -2.578435}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_1e920a81", "position": {"x": -1.8247478, "y": 0.493681282, "z": -0.293631434}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pot_9e34d9fc", "position": {"x": -0.0361, "y": 0.950499952, "z": -2.5762}, "rotation": {"x": 0.0, "y": 270.000061, "z": 0.0}}, {"objectName": "ButterKnife_f63cc56c", "position": {"x": -0.059099853, "y": 1.11164212, "z": -0.253773928}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f63cc56c", "position": {"x": 1.00866878, "y": 0.315583, "z": -2.38311172}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_7ed44c70", "position": {"x": -0.4652, "y": 0.950499952, "z": -2.372}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}, {"objectName": "Glassbottle_c5dfb511", "position": {"x": 1.68690813, "y": 0.8815478, "z": -2.53643131}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Glassbottle_c5dfb511", "position": {"x": -1.935, "y": 0.0487296022, "z": 1.93331647}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "WineBottle_c21bc5e7", "position": {"x": -1.96227539, "y": 0.0489416756, "z": 2.07684183}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "WineBottle_c21bc5e7", "position": {"x": -1.58601356, "y": 0.9123287, "z": -2.32982731}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_56e11318", "position": {"x": -0.9956643, "y": 0.9109642, "z": -2.25301242}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Book_f5814e4b", "position": {"x": 0.155, "y": 1.1, "z": 0.617}, "rotation": {"x": 0.0, "y": 315.826447, "z": 0.0}}, {"objectName": "Glassbottle_c5dfb511", "position": {"x": -1.96337986, "y": 0.7704729, "z": -1.41488659}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_c9d70d0f", "position": {"x": 0.3554865, "y": 1.13694966, "z": -0.253774}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_35d530e8", "position": {"x": 0.0790954158, "y": 1.18406522, "z": 0.251773834}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_89238ee6", "position": {"x": 0.355485976, "y": 1.11214864, "z": -0.0009999275}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_dc7b7f7e", "position": {"x": -2.12060976, "y": 0.8289984, "z": 1.2513144}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_0fe5b61f", "position": {"x": 1.7894665, "y": 0.5540393, "z": -2.66644788}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Kettle_7ed44c70", "position": {"x": -1.01363826, "y": 1.65734136, "z": -2.604735}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_e8d2711b", "position": {"x": 0.2172907, "y": 1.11120951, "z": -0.5065479}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Plate_ba789e3c", "position": {"x": -0.3354904, "y": 1.10851872, "z": -0.5065479}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_811f3b02", "position": {"x": 0.582687557, "y": 0.949768364, "z": -2.56081653}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_d1ae33eb", "position": {"x": 2.042472, "y": 0.5407073, "z": -2.49500537}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_30762d8a", "position": {"x": -2.20491076, "y": 1.53813827, "z": 1.26004815}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "CreditCard_01a9d33b", "position": {"x": 2.09632087, "y": 0.8778273, "z": -2.59077}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Statue_1a4cbefa", "position": {"x": 0.0790954158, "y": 1.1103971, "z": -0.7593218}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "WineBottle_c21bc5e7", "position": {"x": 1.7892617, "y": 0.8817599, "z": -2.50926352}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Pot_9e34d9fc", "position": {"x": -2.03700638, "y": 0.802778065, "z": 1.00317216}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_1e920a81", "position": {"x": -1.85282016, "y": 0.784756, "z": -1.76027608}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_2c38299b", "position": {"x": 0.8404269, "y": 1.01498532, "z": -2.40718341}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_77fad61b", "position": {"x": -1.45701456, "y": 1.65619934, "z": -2.53134537}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_5ffb3206", "position": {"x": -1.81492758, "y": 0.127596617, "z": 0.158000976}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_9960ee75", "position": {"x": -2.08159924, "y": 1.4995954, "z": 0.9250771}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_b714915a", "position": {"x": -1.6098721, "y": 0.150145382, "z": -0.342119217}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PepperShaker_816f44ce", "position": {"x": 1.99396777, "y": 0.8771374, "z": -2.59076929}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Lettuce_e166a32f", "position": {"x": -0.3354904, "y": 1.19122255, "z": 0.7573217}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f63cc56c", "position": {"x": 0.0790954158, "y": 1.11164212, "z": -0.00100004673}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a0b666be", "position": {"x": 0.969296634, "y": 0.961277664, "z": -2.40718341}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_56e11318", "position": {"x": 1.47836614, "y": 0.5526228, "z": -2.666446}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Spoon_cd066502", "position": {"x": -1.96337986, "y": 0.7708855, "z": -1.50123394}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Mug_b5db8038", "position": {"x": -1.7313, "y": 0.956599951, "z": -0.1689994}, "rotation": {"x": 0.0, "y": 89.99987, "z": 0.0}}], "object_toggles": [], "random_seed": 132597603, "scene_num": 1}, "task_id": "trial_T20190918_212722_569040", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A1O3TWBUDONVLO_33OOO72IVKCIET6HUVCYZPLXEQLCT5", "high_descs": ["Turn around and walk around the center table to face the lettuce on the other end.", "Pick up the lettuce on the center table.", "Turn right and walk forward to face the fridge.", "Place the lettuce in the fridge.", "Step to the left to face the lettuce on the counter.", "Pick up the lettuce on the counter.", "Step to the right to face the fridge.", "Place the lettuce in the fridge next to the first lettuce."], "task_desc": "Place two heads of lettuce in the fridge.", "votes": [1, 1]}, {"assignment_id": "ARB8SJAWXUWTD_3RU7GD8VPRK0TI26A0TE9KOIBQSSPG", "high_descs": ["Go to the kitchen island", "Pick up the lettuce", "Go to the fridge", "Put the lettuce in the fridge", "Go to the counter next to the sink", "Pick up the lettuce from the counter", "Go to the fridge", "Place the lettuce in the fridge"], "task_desc": "Move two heads of lettuce to a fridge", "votes": [1, 1]}, {"assignment_id": "A2A028LRDJB7ZB_3T3IWE1XG9EM8UG0ZDDTJUAEI6UTQ1", "high_descs": ["Turn right then go around the island stop at the other side of it", "Pick up the cabbage on the island", "Turn to your right then face the fridge on your right", "Open the  fridge put in the cabbage, close the fridge", "Turn left head to the counter then face the cabbage beside the sink", "Pick up the cabbage on the counter", "Turn right walk toward the fridge", "Open the fridge and put in the cabbage"], "task_desc": "Put two cabbage in the fridge", "votes": [1, 1]}]}}