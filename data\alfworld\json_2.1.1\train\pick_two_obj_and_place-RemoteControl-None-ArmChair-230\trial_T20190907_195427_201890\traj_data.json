{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000254.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000255.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000256.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000258.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000259.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000260.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000261.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000262.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000263.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000264.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000265.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000283.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000284.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000285.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000286.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000287.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000288.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000289.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000290.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000291.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000292.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000293.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000294.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000295.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000296.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000297.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000298.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000299.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000300.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000301.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000302.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000303.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000304.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000305.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000306.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000307.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000308.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000309.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000310.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000311.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000312.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000313.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000314.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000315.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000316.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000317.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000318.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000319.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000320.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000321.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000322.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000323.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000324.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000325.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000326.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000327.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000328.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000329.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000330.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000331.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000332.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000333.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000334.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000335.png", "low_idx": 62}, {"high_idx": 7, "image_name": "000000336.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000337.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000338.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000339.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000340.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000341.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000342.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000343.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000344.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000345.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000346.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000347.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000348.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000349.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000350.png", "low_idx": 63}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "RemoteControl", "parent_target": "ArmChair", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["coffeetable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-10|26|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["remotecontrol"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["RemoteControl", [-12.90855408, -12.90855408, 26.225048, 26.225048, 1.828936576, 1.828936576]], "coordinateReceptacleObjectId": ["CoffeeTable", [-12.948, -12.948, 26.396, 26.396, 0.04, 0.04]], "forceVisible": true, "objectId": "RemoteControl|-03.23|+00.46|+06.56"}}, {"discrete_action": {"action": "GotoLocation", "args": ["armchair"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-15|22|2|60"}}, {"discrete_action": {"action": "PutObject", "args": ["remotecontrol", "armchair"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["RemoteControl", [-12.90855408, -12.90855408, 26.225048, 26.225048, 1.828936576, 1.828936576]], "coordinateReceptacleObjectId": ["ArmChair", [-14.652, -14.652, 18.24, 18.24, 0.0037252904, 0.0037252904]], "forceVisible": true, "objectId": "RemoteControl|-03.23|+00.46|+06.56", "receptacleObjectId": "ArmChair|-03.66|+00.00|+04.56"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-17|11|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["remotecontrol"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["RemoteControl", [-13.73041724, -13.73041724, 10.7258148, 10.7258148, 3.1317364, 3.1317364]], "coordinateReceptacleObjectId": ["DiningTable", [-11.612, -11.612, 8.9, 8.9, 0.0572, 0.0572]], "forceVisible": true, "objectId": "RemoteControl|-03.43|+00.78|+02.68"}}, {"discrete_action": {"action": "GotoLocation", "args": ["armchair"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-15|22|2|60"}}, {"discrete_action": {"action": "PutObject", "args": ["remotecontrol", "armchair"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["RemoteControl", [-13.73041724, -13.73041724, 10.7258148, 10.7258148, 3.1317364, 3.1317364]], "coordinateReceptacleObjectId": ["ArmChair", [-14.652, -14.652, 18.24, 18.24, 0.0037252904, 0.0037252904]], "forceVisible": true, "objectId": "RemoteControl|-03.43|+00.78|+02.68", "receptacleObjectId": "ArmChair|-03.66|+00.00|+04.56"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "RemoteControl|-03.23|+00.46|+06.56"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [139, 131, 181, 143], "mask": [[39176, 2], [39444, 7], [39468, 12], [39741, 40], [40040, 41], [40339, 43], [40639, 43], [40939, 43], [41239, 43], [41539, 43], [41840, 42], [42141, 40], [42442, 39], [42771, 8]], "point": [160, 136]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "RemoteControl|-03.23|+00.46|+06.56", "placeStationary": true, "receptacleObjectId": "ArmChair|-03.66|+00.00|+04.56"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [46, 1, 212, 184], "mask": [[87, 88], [385, 92], [683, 96], [982, 98], [1281, 100], [1580, 101], [1879, 103], [2178, 105], [2478, 105], [2778, 106], [3077, 107], [3377, 108], [3677, 108], [3977, 108], [4277, 108], [4577, 108], [4877, 108], [5177, 108], [5477, 108], [5777, 108], [6077, 108], [6377, 108], [6677, 108], [6977, 108], [7277, 108], [7578, 107], [7878, 107], [8178, 107], [8478, 107], [8778, 107], [9079, 105], [9379, 105], [9679, 105], [9979, 105], [10280, 104], [10580, 104], [10880, 103], [11180, 103], [11481, 102], [11781, 102], [12081, 102], [12186, 4], [12375, 107], [12483, 11], [12672, 123], [12970, 127], [13269, 128], [13569, 129], [13868, 131], [14168, 131], [14467, 132], [14767, 132], [15067, 132], [15366, 134], [15666, 134], [15966, 134], [16265, 135], [16565, 135], [16865, 136], [17165, 136], [17464, 137], [17764, 137], [18064, 138], [18363, 139], [18663, 139], [18963, 139], [19262, 140], [19562, 141], [19862, 141], [20161, 142], [20461, 142], [20761, 143], [21060, 144], [21360, 144], [21660, 144], [21960, 144], [22259, 145], [22559, 146], [22859, 146], [23158, 147], [23458, 147], [23758, 147], [24058, 148], [24357, 149], [24657, 149], [24957, 149], [25257, 149], [25556, 151], [25856, 151], [26156, 151], [26456, 151], [26755, 152], [27055, 152], [27355, 153], [27655, 153], [27954, 154], [28254, 154], [28554, 154], [28853, 155], [29153, 156], [29453, 156], [29753, 156], [30053, 156], [30352, 157], [30652, 157], [30952, 158], [31252, 158], [31551, 159], [31851, 159], [32151, 159], [32451, 159], [32750, 161], [33050, 161], [33350, 161], [33650, 161], [33950, 161], [34249, 162], [34549, 163], [34849, 163], [35149, 163], [35448, 70], [35532, 80], [35748, 70], [35832, 80], [36048, 70], [36132, 80], [36348, 70], [36432, 81], [36647, 71], [36732, 81], [36947, 71], [37032, 81], [37247, 70], [37332, 81], [37547, 166], [37847, 166], [38146, 167], [38446, 167], [38746, 167], [39046, 167], [39347, 166], [39647, 165], [39948, 164], [40249, 162], [40549, 162], [40850, 160], [41151, 159], [41452, 157], [41752, 156], [42053, 155], [42354, 153], [42655, 152], [42955, 152], [43256, 150], [43557, 149], [43857, 148], [44158, 147], [44459, 145], [44759, 145], [45060, 144], [45361, 142], [45661, 142], [45962, 140], [46263, 139], [46563, 139], [46864, 137], [47165, 136], [47465, 135], [47766, 133], [48067, 132], [48368, 131], [48669, 130], [48969, 130], [49269, 130], [49569, 130], [49869, 130], [50169, 130], [50469, 130], [50770, 129], [51070, 128], [51370, 128], [51671, 126], [51972, 124], [52273, 122], [52576, 119], [52876, 119], [53176, 117], [53477, 113], [53781, 51], [53883, 5], [54086, 4], [54183, 5], [54386, 4], [54484, 4], [54686, 4], [54785, 2], [54987, 2]], "point": [129, 91]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "RemoteControl|-03.43|+00.78|+02.68"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [141, 141, 190, 154], "mask": [[42146, 3], [42443, 16], [42475, 10], [42742, 47], [43042, 48], [43341, 50], [43641, 50], [43941, 50], [44241, 50], [44541, 50], [44841, 50], [45141, 49], [45442, 47], [45743, 16], [45775, 11], [46045, 6]], "point": [165, 146]}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "RemoteControl|-03.43|+00.78|+02.68", "placeStationary": true, "receptacleObjectId": "ArmChair|-03.66|+00.00|+04.56"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [46, 1, 212, 184], "mask": [[87, 88], [385, 92], [683, 96], [982, 98], [1281, 100], [1580, 101], [1879, 103], [2178, 105], [2478, 105], [2778, 106], [3077, 107], [3377, 108], [3677, 108], [3977, 108], [4277, 108], [4577, 108], [4877, 108], [5177, 108], [5477, 108], [5777, 108], [6077, 108], [6377, 108], [6677, 108], [6977, 108], [7277, 108], [7578, 107], [7878, 107], [8178, 107], [8478, 107], [8778, 107], [9079, 105], [9379, 105], [9679, 105], [9979, 105], [10280, 104], [10580, 104], [10880, 103], [11180, 103], [11481, 102], [11781, 102], [12081, 102], [12186, 4], [12375, 107], [12483, 11], [12672, 123], [12970, 127], [13269, 128], [13569, 129], [13868, 131], [14168, 131], [14467, 132], [14767, 132], [15067, 132], [15366, 134], [15666, 134], [15966, 134], [16265, 135], [16565, 135], [16865, 136], [17165, 136], [17464, 137], [17764, 137], [18064, 138], [18363, 139], [18663, 139], [18963, 139], [19262, 140], [19562, 141], [19862, 141], [20161, 142], [20461, 142], [20761, 143], [21060, 144], [21360, 144], [21660, 144], [21960, 144], [22259, 145], [22559, 146], [22859, 146], [23158, 147], [23458, 147], [23758, 147], [24058, 148], [24357, 149], [24657, 149], [24957, 149], [25257, 149], [25556, 151], [25856, 151], [26156, 151], [26456, 151], [26755, 152], [27055, 152], [27355, 153], [27655, 153], [27954, 154], [28254, 154], [28554, 154], [28853, 155], [29153, 156], [29453, 156], [29753, 156], [30053, 156], [30352, 157], [30652, 157], [30952, 158], [31252, 158], [31551, 159], [31851, 159], [32151, 159], [32451, 159], [32750, 161], [33050, 161], [33350, 161], [33650, 161], [33950, 161], [34249, 162], [34549, 163], [34849, 163], [35149, 163], [35448, 70], [35532, 80], [35748, 70], [35832, 80], [36048, 70], [36132, 80], [36348, 70], [36432, 81], [36647, 71], [36732, 81], [36947, 71], [37032, 81], [37247, 70], [37332, 11], [37349, 64], [37547, 95], [37650, 63], [37847, 94], [37950, 63], [38146, 95], [38251, 62], [38446, 95], [38551, 62], [38746, 95], [38851, 62], [39046, 95], [39151, 62], [39347, 94], [39451, 62], [39647, 94], [39751, 61], [39948, 93], [40051, 61], [40249, 92], [40351, 60], [40549, 92], [40650, 61], [40850, 91], [40950, 60], [41151, 90], [41250, 60], [41452, 89], [41550, 59], [41752, 89], [41850, 58], [42053, 88], [42150, 58], [42354, 87], [42450, 57], [42655, 86], [42750, 57], [42955, 86], [43050, 57], [43256, 85], [43350, 56], [43557, 84], [43651, 55], [43857, 84], [43951, 54], [44158, 82], [44251, 54], [44459, 81], [44551, 53], [44759, 81], [44851, 53], [45060, 79], [45152, 52], [45361, 78], [45452, 51], [45661, 78], [45752, 51], [45962, 77], [46052, 50], [46263, 76], [46352, 50], [46563, 77], [46651, 51], [46864, 77], [46950, 51], [47165, 77], [47249, 52], [47465, 135], [47766, 133], [48067, 132], [48368, 131], [48669, 130], [48969, 130], [49269, 130], [49569, 130], [49869, 130], [50169, 130], [50469, 130], [50770, 129], [51070, 128], [51370, 128], [51671, 126], [51972, 124], [52273, 122], [52576, 119], [52876, 119], [53176, 117], [53477, 113], [53781, 51], [53883, 5], [54086, 4], [54183, 5], [54386, 4], [54484, 4], [54686, 4], [54785, 2], [54987, 2]], "point": [129, 91]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan230", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -3.0, "y": 0.9082557, "z": 5.5}, "object_poses": [{"objectName": "TissueBox_908cc737", "position": {"x": -2.324463, "y": 0.7833995, "z": 2.52654219}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Newspaper_a5054344", "position": {"x": -2.04742765, "y": 0.7810505, "z": 1.75198448}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Watch_724f65b7", "position": {"x": -3.1925, "y": 0.5337727, "z": 8.427632}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "KeyChain_5b0f81e2", "position": {"x": -1.547709, "y": 0.456587017, "z": 6.14456}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "KeyChain_5b0f81e2", "position": {"x": -3.005, "y": 0.535087049, "z": 8.534}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "RemoteControl_06e16364", "position": {"x": -3.43260431, "y": 0.7829341, "z": 2.6814537}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "RemoteControl_06e16364", "position": {"x": -3.22713852, "y": 0.457234144, "z": 6.556262}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Box_9dff4699", "position": {"x": -3.32743239, "y": 0.584661663, "z": 6.138971}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Box_9dff4699", "position": {"x": -1.85204864, "y": 0.5836617, "z": 7.03673935}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_1ce7ed17", "position": {"x": -1.77596378, "y": 0.453000069, "z": 5.54977369}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Box_9dff4699", "position": {"x": -2.04742765, "y": 0.910361648, "z": 2.06180763}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "KeyChain_5b0f81e2", "position": {"x": -2.651342, "y": 0.535087049, "z": 8.346501}, "rotation": {"x": 0.0, "y": 179.999847, "z": 0.0}}, {"objectName": "Plate_5e7db3f1", "position": {"x": -2.68879128, "y": 0.7889063, "z": 1.82050252}, "rotation": {"x": -0.00051901507, "y": 346.769623, "z": 0.0073584835}}, {"objectName": "Candle_baa7deba", "position": {"x": -3.15556884, "y": 0.7828239, "z": 2.52654219}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pillow_1c7e6d26", "position": {"x": -1.77596378, "y": 0.5167066, "z": 6.441953}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Candle_926dae3a", "position": {"x": -0.6246592, "y": 0.667834938, "z": 8.444735}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "TissueBox_908cc737", "position": {"x": -2.60149837, "y": 0.7833995, "z": 2.52654219}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_1ce7ed17", "position": {"x": -3.15556884, "y": 0.780179441, "z": 1.75198448}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Newspaper_a5054344", "position": {"x": -2.8785336, "y": 0.7815299, "z": 2.21671915}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_edffc522", "position": {"x": -3.60050154, "y": 0.329707026, "z": 4.58263445}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "RemoteControl_06e16364", "position": {"x": -3.70963955, "y": 0.7834135, "z": 2.52654219}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Boots_7fb33fbb", "position": {"x": -5.786989, "y": 0.007925332, "z": 4.817991}, "rotation": {"x": 0.09014016, "y": 167.028427, "z": 0.003741637}}, {"objectName": "Candle_6f5401e5", "position": {"x": -0.4101632, "y": 0.678357244, "z": 0.566388369}, "rotation": {"x": 0.0, "y": 329.437, "z": 0.0}}, {"objectName": "Statue_ed97bba2", "position": {"x": -2.71359587, "y": 0.902, "z": 2.29446149}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Watch_724f65b7", "position": {"x": -3.60865736, "y": 0.5337727, "z": 8.40899849}, "rotation": {"x": 0.0, "y": 179.999847, "z": 0.0}}, {"objectName": "Statue_1d7526da", "position": {"x": -3.15556884, "y": 0.791763961, "z": 2.37163067}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1959815256, "scene_num": 230}, "task_id": "trial_T20190907_195427_201890", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "AUTYWXILTACCR_31LM9EDVOO9Z65L5BJIRC88YL0BNJF", "high_descs": ["Turn around, then turn left, and turn around again to face the coffee table.", "Pick up the remote from the middle of the coffee table.", "Turn left, then turn right and walk until you get to the chair nearest the end of the rug.", "Place the remote on the chair.", "Walk around the chair to get to the dining table behind it.", "Pick up the remote to the left of the other remote on the table.", "Walk back to face the chair where you put the first remote.", "Place the remote to the left of the other on on the chair."], "task_desc": "Place two remotes on a chair.", "votes": [1, 1]}, {"assignment_id": "A1S1K7134S2VUC_3E13VNJ1NQC0XNUAOEZT30GV5OZI1G", "high_descs": ["Turn left, move to in front of the coffee table, near the plant.", "Pick up the remote on the coffee table.", "Turn left, move to in front of the right hand chair.", "Put the remote on the chair, right of the credit card.", "Turn right, move to the table, move to near the two remote controls.", "Pick up the remote on the table, nearest the dog statue, nearest the candle.", "Turn left, move to in front of the right hand chair.", "Put the remote on the chair, left of the other remote."], "task_desc": "Place two remote controls onto a chair.", "votes": [1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3M23Y66PO5OKYNY3ZW5O2YDZMYHS6I", "high_descs": ["Turn around and walk over to the couch, then turn left and start walking across the room, turn left to face the coffee table.", "Pick up the remote off of the coffee table.", "Turn left and walk over to the chair, the turn right ad walk towards the fireplace, turn left to face the chair closest to the fireplace.", "Put the remote on the seat of the chair in front of you.", "Turn right and walk forward, then hang a left and walk over to the end of the large wooden table.", "Pick up the remote that is closest to the silver statue on the table.", "Turn left and walk over the chair with the remote and credit card on its seat.", "Put the remote on the seat of the chair."], "task_desc": "Move two remotes to a chair seat.", "votes": [1, 1]}]}}