
(define (problem plan_trial_T20190908_071856_380211)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Candle_bar__minus_00_dot_42_bar__plus_01_dot_05_bar__minus_03_dot_95 - object
        Candle_bar__minus_00_dot_68_bar__plus_01_dot_05_bar__minus_03_dot_95 - object
        Cloth_bar__minus_02_dot_67_bar__plus_00_dot_08_bar__minus_00_dot_52 - object
        Cloth_bar__minus_02_dot_67_bar__plus_00_dot_08_bar__minus_01_dot_06 - object
        FloorLamp_bar__minus_02_dot_91_bar__plus_00_dot_99_bar__minus_00_dot_73 - object
        FloorLamp_bar__minus_02_dot_91_bar__plus_00_dot_99_bar__minus_01_dot_76 - object
        HandTowel_bar__minus_02_dot_59_bar__plus_01_dot_64_bar__minus_00_dot_05 - object
        LightSwitch_bar__minus_01_dot_33_bar__plus_01_dot_39_bar__plus_00_dot_00 - object
        Mirror_bar__minus_02_dot_95_bar__plus_01_dot_56_bar__minus_01_dot_19 - object
        Plunger_bar__minus_01_dot_07_bar__plus_00_dot_00_bar__minus_03_dot_53 - object
        ScrubBrush_bar__minus_01_dot_05_bar__plus_00_dot_00_bar__minus_03_dot_83 - object
        ShowerDoor_bar__minus_02_dot_20_bar__plus_01_dot_05_bar__minus_02_dot_50 - object
        ShowerGlass_bar__minus_02_dot_08_bar__plus_01_dot_48_bar__minus_03_dot_26 - object
        ShowerHead_bar__minus_02_dot_87_bar__plus_01_dot_22_bar__minus_03_dot_22 - object
        Sink_bar__minus_02_dot_91_bar__plus_00_dot_99_bar__minus_00_dot_73 - object
        Sink_bar__minus_02_dot_91_bar__plus_00_dot_99_bar__minus_01_dot_76 - object
        SoapBar_bar__minus_02_dot_67_bar__plus_00_dot_08_bar__minus_00_dot_36 - object
        SoapBottle_bar__minus_02_dot_87_bar__plus_00_dot_08_bar__minus_02_dot_03 - object
        SprayBottle_bar__minus_02_dot_61_bar__plus_01_dot_00_bar__minus_00_dot_28 - object
        SprayBottle_bar__minus_02_dot_68_bar__plus_01_dot_00_bar__minus_01_dot_32 - object
        ToiletPaper_bar__minus_02_dot_64_bar__plus_00_dot_07_bar__minus_01_dot_89 - object
        ToiletPaper_bar__minus_02_dot_82_bar__plus_00_dot_08_bar__minus_01_dot_60 - object
        Towel_bar__minus_01_dot_05_bar__plus_01_dot_54_bar__minus_02_dot_89 - object
        Window_bar__minus_00_dot_57_bar__plus_01_dot_73_bar__minus_04_dot_10 - object
        Cabinet_bar__minus_02_dot_50_bar__plus_00_dot_40_bar__minus_01_dot_24 - receptacle
        Cabinet_bar__minus_02_dot_50_bar__plus_00_dot_40_bar__minus_02_dot_21 - receptacle
        Cabinet_bar__minus_02_dot_51_bar__plus_00_dot_43_bar__minus_00_dot_29 - receptacle
        Cabinet_bar__minus_02_dot_51_bar__plus_00_dot_43_bar__minus_01_dot_26 - receptacle
        CounterTop_bar__minus_02_dot_75_bar__plus_00_dot_99_bar__minus_01_dot_24 - receptacle
        GarbageCan_bar__minus_01_dot_53_bar__plus_00_dot_00_bar__minus_00_dot_18 - receptacle
        HandTowelHolder_bar__minus_02_dot_59_bar__plus_01_dot_74_bar__minus_00_dot_01 - receptacle
        Sink_bar__minus_02_dot_91_bar__plus_00_dot_99_bar__minus_00_dot_73_bar_SinkBasin - receptacle
        Sink_bar__minus_02_dot_91_bar__plus_00_dot_99_bar__minus_01_dot_76_bar_SinkBasin - receptacle
        ToiletPaperHanger_bar__plus_00_dot_00_bar__plus_00_dot_98_bar__minus_03_dot_33 - receptacle
        Toilet_bar__minus_00_dot_55_bar__plus_00_dot_00_bar__minus_03_dot_53 - receptacle
        TowelHolder_bar__minus_01_dot_16_bar__plus_01_dot_52_bar__minus_02_dot_89 - receptacle
        loc_bar__minus_9_bar__minus_3_bar_3_bar_45 - location
        loc_bar__minus_2_bar__minus_11_bar_2_bar_0 - location
        loc_bar__minus_6_bar__minus_4_bar_3_bar_45 - location
        loc_bar__minus_2_bar__minus_11_bar_2_bar_60 - location
        loc_bar__minus_9_bar__minus_5_bar_3_bar_15 - location
        loc_bar__minus_2_bar__minus_11_bar_3_bar_15 - location
        loc_bar__minus_9_bar__minus_8_bar_3_bar_60 - location
        loc_bar__minus_5_bar__minus_2_bar_0_bar_30 - location
        loc_bar__minus_9_bar__minus_2_bar_3_bar_60 - location
        loc_bar__minus_9_bar__minus_4_bar_3_bar_60 - location
        loc_bar__minus_9_bar__minus_2_bar_0_bar__minus_15 - location
        loc_bar__minus_6_bar__minus_8_bar_3_bar_45 - location
        loc_bar__minus_9_bar__minus_8_bar_2_bar_45 - location
        loc_bar__minus_8_bar__minus_8_bar_2_bar_15 - location
        loc_bar__minus_9_bar__minus_7_bar_3_bar_45 - location
        loc_bar__minus_2_bar__minus_3_bar_3_bar_60 - location
        loc_bar__minus_6_bar__minus_6_bar_3_bar_45 - location
        loc_bar__minus_2_bar__minus_11_bar_2_bar_45 - location
        loc_bar__minus_2_bar__minus_4_bar_2_bar_30 - location
        )
    

(:init


        (receptacleType GarbageCan_bar__minus_01_dot_53_bar__plus_00_dot_00_bar__minus_00_dot_18 GarbageCanType)
        (receptacleType Toilet_bar__minus_00_dot_55_bar__plus_00_dot_00_bar__minus_03_dot_53 ToiletType)
        (receptacleType CounterTop_bar__minus_02_dot_75_bar__plus_00_dot_99_bar__minus_01_dot_24 CounterTopType)
        (receptacleType Cabinet_bar__minus_02_dot_50_bar__plus_00_dot_40_bar__minus_02_dot_21 CabinetType)
        (receptacleType Cabinet_bar__minus_02_dot_51_bar__plus_00_dot_43_bar__minus_00_dot_29 CabinetType)
        (receptacleType Sink_bar__minus_02_dot_91_bar__plus_00_dot_99_bar__minus_00_dot_73_bar_SinkBasin SinkBasinType)
        (receptacleType Cabinet_bar__minus_02_dot_50_bar__plus_00_dot_40_bar__minus_01_dot_24 CabinetType)
        (receptacleType TowelHolder_bar__minus_01_dot_16_bar__plus_01_dot_52_bar__minus_02_dot_89 TowelHolderType)
        (receptacleType Sink_bar__minus_02_dot_91_bar__plus_00_dot_99_bar__minus_01_dot_76_bar_SinkBasin SinkBasinType)
        (receptacleType Cabinet_bar__minus_02_dot_51_bar__plus_00_dot_43_bar__minus_01_dot_26 CabinetType)
        (receptacleType HandTowelHolder_bar__minus_02_dot_59_bar__plus_01_dot_74_bar__minus_00_dot_01 HandTowelHolderType)
        (receptacleType ToiletPaperHanger_bar__plus_00_dot_00_bar__plus_00_dot_98_bar__minus_03_dot_33 ToiletPaperHangerType)
        (objectType FloorLamp_bar__minus_02_dot_91_bar__plus_00_dot_99_bar__minus_01_dot_76 FloorLampType)
        (objectType ShowerDoor_bar__minus_02_dot_20_bar__plus_01_dot_05_bar__minus_02_dot_50 ShowerDoorType)
        (objectType SoapBar_bar__minus_02_dot_67_bar__plus_00_dot_08_bar__minus_00_dot_36 SoapBarType)
        (objectType SoapBottle_bar__minus_02_dot_87_bar__plus_00_dot_08_bar__minus_02_dot_03 SoapBottleType)
        (objectType HandTowel_bar__minus_02_dot_59_bar__plus_01_dot_64_bar__minus_00_dot_05 HandTowelType)
        (objectType Candle_bar__minus_00_dot_68_bar__plus_01_dot_05_bar__minus_03_dot_95 CandleType)
        (objectType ToiletPaper_bar__minus_02_dot_82_bar__plus_00_dot_08_bar__minus_01_dot_60 ToiletPaperType)
        (objectType Plunger_bar__minus_01_dot_07_bar__plus_00_dot_00_bar__minus_03_dot_53 PlungerType)
        (objectType SprayBottle_bar__minus_02_dot_61_bar__plus_01_dot_00_bar__minus_00_dot_28 SprayBottleType)
        (objectType FloorLamp_bar__minus_02_dot_91_bar__plus_00_dot_99_bar__minus_00_dot_73 FloorLampType)
        (objectType Sink_bar__minus_02_dot_91_bar__plus_00_dot_99_bar__minus_01_dot_76 SinkType)
        (objectType Sink_bar__minus_02_dot_91_bar__plus_00_dot_99_bar__minus_00_dot_73 SinkType)
        (objectType ScrubBrush_bar__minus_01_dot_05_bar__plus_00_dot_00_bar__minus_03_dot_83 ScrubBrushType)
        (objectType Towel_bar__minus_01_dot_05_bar__plus_01_dot_54_bar__minus_02_dot_89 TowelType)
        (objectType LightSwitch_bar__minus_01_dot_33_bar__plus_01_dot_39_bar__plus_00_dot_00 LightSwitchType)
        (objectType ShowerGlass_bar__minus_02_dot_08_bar__plus_01_dot_48_bar__minus_03_dot_26 ShowerGlassType)
        (objectType ToiletPaper_bar__minus_02_dot_64_bar__plus_00_dot_07_bar__minus_01_dot_89 ToiletPaperType)
        (objectType Cloth_bar__minus_02_dot_67_bar__plus_00_dot_08_bar__minus_00_dot_52 ClothType)
        (objectType Cloth_bar__minus_02_dot_67_bar__plus_00_dot_08_bar__minus_01_dot_06 ClothType)
        (objectType Candle_bar__minus_00_dot_42_bar__plus_01_dot_05_bar__minus_03_dot_95 CandleType)
        (objectType SprayBottle_bar__minus_02_dot_68_bar__plus_01_dot_00_bar__minus_01_dot_32 SprayBottleType)
        (objectType Mirror_bar__minus_02_dot_95_bar__plus_01_dot_56_bar__minus_01_dot_19 MirrorType)
        (objectType Window_bar__minus_00_dot_57_bar__plus_01_dot_73_bar__minus_04_dot_10 WindowType)
        (canContain GarbageCanType SoapBarType)
        (canContain GarbageCanType SprayBottleType)
        (canContain GarbageCanType ToiletPaperType)
        (canContain GarbageCanType SoapBottleType)
        (canContain GarbageCanType ClothType)
        (canContain GarbageCanType HandTowelType)
        (canContain ToiletType SoapBottleType)
        (canContain ToiletType HandTowelType)
        (canContain ToiletType ToiletPaperType)
        (canContain ToiletType ClothType)
        (canContain ToiletType CandleType)
        (canContain ToiletType SoapBarType)
        (canContain ToiletType SprayBottleType)
        (canContain CounterTopType CandleType)
        (canContain CounterTopType SoapBarType)
        (canContain CounterTopType SprayBottleType)
        (canContain CounterTopType ToiletPaperType)
        (canContain CounterTopType SoapBottleType)
        (canContain CounterTopType ClothType)
        (canContain CounterTopType HandTowelType)
        (canContain CabinetType CandleType)
        (canContain CabinetType SoapBarType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType ToiletPaperType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType ClothType)
        (canContain CabinetType HandTowelType)
        (canContain CabinetType PlungerType)
        (canContain CabinetType CandleType)
        (canContain CabinetType SoapBarType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType ToiletPaperType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType ClothType)
        (canContain CabinetType HandTowelType)
        (canContain CabinetType PlungerType)
        (canContain SinkBasinType SoapBarType)
        (canContain SinkBasinType ClothType)
        (canContain SinkBasinType HandTowelType)
        (canContain CabinetType CandleType)
        (canContain CabinetType SoapBarType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType ToiletPaperType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType ClothType)
        (canContain CabinetType HandTowelType)
        (canContain CabinetType PlungerType)
        (canContain TowelHolderType TowelType)
        (canContain SinkBasinType SoapBarType)
        (canContain SinkBasinType ClothType)
        (canContain SinkBasinType HandTowelType)
        (canContain CabinetType CandleType)
        (canContain CabinetType SoapBarType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType ToiletPaperType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType ClothType)
        (canContain CabinetType HandTowelType)
        (canContain CabinetType PlungerType)
        (canContain HandTowelHolderType HandTowelType)
        (canContain ToiletPaperHangerType ToiletPaperType)
        (pickupable SoapBar_bar__minus_02_dot_67_bar__plus_00_dot_08_bar__minus_00_dot_36)
        (pickupable SoapBottle_bar__minus_02_dot_87_bar__plus_00_dot_08_bar__minus_02_dot_03)
        (pickupable HandTowel_bar__minus_02_dot_59_bar__plus_01_dot_64_bar__minus_00_dot_05)
        (pickupable Candle_bar__minus_00_dot_68_bar__plus_01_dot_05_bar__minus_03_dot_95)
        (pickupable ToiletPaper_bar__minus_02_dot_82_bar__plus_00_dot_08_bar__minus_01_dot_60)
        (pickupable Plunger_bar__minus_01_dot_07_bar__plus_00_dot_00_bar__minus_03_dot_53)
        (pickupable SprayBottle_bar__minus_02_dot_61_bar__plus_01_dot_00_bar__minus_00_dot_28)
        (pickupable ScrubBrush_bar__minus_01_dot_05_bar__plus_00_dot_00_bar__minus_03_dot_83)
        (pickupable Towel_bar__minus_01_dot_05_bar__plus_01_dot_54_bar__minus_02_dot_89)
        (pickupable ToiletPaper_bar__minus_02_dot_64_bar__plus_00_dot_07_bar__minus_01_dot_89)
        (pickupable Cloth_bar__minus_02_dot_67_bar__plus_00_dot_08_bar__minus_00_dot_52)
        (pickupable Cloth_bar__minus_02_dot_67_bar__plus_00_dot_08_bar__minus_01_dot_06)
        (pickupable Candle_bar__minus_00_dot_42_bar__plus_01_dot_05_bar__minus_03_dot_95)
        (pickupable SprayBottle_bar__minus_02_dot_68_bar__plus_01_dot_00_bar__minus_01_dot_32)
        
        (openable Cabinet_bar__minus_02_dot_50_bar__plus_00_dot_40_bar__minus_02_dot_21)
        (openable Cabinet_bar__minus_02_dot_51_bar__plus_00_dot_43_bar__minus_00_dot_29)
        (openable Cabinet_bar__minus_02_dot_50_bar__plus_00_dot_40_bar__minus_01_dot_24)
        (openable Cabinet_bar__minus_02_dot_51_bar__plus_00_dot_43_bar__minus_01_dot_26)
        
        (atLocation agent1 loc_bar__minus_2_bar__minus_4_bar_2_bar_30)
        
        (cleanable SoapBar_bar__minus_02_dot_67_bar__plus_00_dot_08_bar__minus_00_dot_36)
        (cleanable Cloth_bar__minus_02_dot_67_bar__plus_00_dot_08_bar__minus_00_dot_52)
        (cleanable Cloth_bar__minus_02_dot_67_bar__plus_00_dot_08_bar__minus_01_dot_06)
        
        
        
        
        
        (toggleable FloorLamp_bar__minus_02_dot_91_bar__plus_00_dot_99_bar__minus_01_dot_76)
        (toggleable FloorLamp_bar__minus_02_dot_91_bar__plus_00_dot_99_bar__minus_00_dot_73)
        
        
        
        
        (inReceptacle SoapBottle_bar__minus_02_dot_87_bar__plus_00_dot_08_bar__minus_02_dot_03 Cabinet_bar__minus_02_dot_50_bar__plus_00_dot_40_bar__minus_02_dot_21)
        (inReceptacle ToiletPaper_bar__minus_02_dot_64_bar__plus_00_dot_07_bar__minus_01_dot_89 Cabinet_bar__minus_02_dot_50_bar__plus_00_dot_40_bar__minus_02_dot_21)
        (inReceptacle SprayBottle_bar__minus_02_dot_61_bar__plus_01_dot_00_bar__minus_00_dot_28 CounterTop_bar__minus_02_dot_75_bar__plus_00_dot_99_bar__minus_01_dot_24)
        (inReceptacle SprayBottle_bar__minus_02_dot_68_bar__plus_01_dot_00_bar__minus_01_dot_32 CounterTop_bar__minus_02_dot_75_bar__plus_00_dot_99_bar__minus_01_dot_24)
        (inReceptacle Cloth_bar__minus_02_dot_67_bar__plus_00_dot_08_bar__minus_01_dot_06 Cabinet_bar__minus_02_dot_50_bar__plus_00_dot_40_bar__minus_01_dot_24)
        (inReceptacle SoapBar_bar__minus_02_dot_67_bar__plus_00_dot_08_bar__minus_00_dot_36 Cabinet_bar__minus_02_dot_51_bar__plus_00_dot_43_bar__minus_00_dot_29)
        (inReceptacle Cloth_bar__minus_02_dot_67_bar__plus_00_dot_08_bar__minus_00_dot_52 Cabinet_bar__minus_02_dot_51_bar__plus_00_dot_43_bar__minus_00_dot_29)
        (inReceptacle ToiletPaper_bar__minus_02_dot_82_bar__plus_00_dot_08_bar__minus_01_dot_60 Cabinet_bar__minus_02_dot_51_bar__plus_00_dot_43_bar__minus_01_dot_26)
        (inReceptacle Towel_bar__minus_01_dot_05_bar__plus_01_dot_54_bar__minus_02_dot_89 TowelHolder_bar__minus_01_dot_16_bar__plus_01_dot_52_bar__minus_02_dot_89)
        (inReceptacle HandTowel_bar__minus_02_dot_59_bar__plus_01_dot_64_bar__minus_00_dot_05 HandTowelHolder_bar__minus_02_dot_59_bar__plus_01_dot_74_bar__minus_00_dot_01)
        (inReceptacle Candle_bar__minus_00_dot_68_bar__plus_01_dot_05_bar__minus_03_dot_95 Toilet_bar__minus_00_dot_55_bar__plus_00_dot_00_bar__minus_03_dot_53)
        (inReceptacle Candle_bar__minus_00_dot_42_bar__plus_01_dot_05_bar__minus_03_dot_95 Toilet_bar__minus_00_dot_55_bar__plus_00_dot_00_bar__minus_03_dot_53)
        
        
        (receptacleAtLocation Cabinet_bar__minus_02_dot_50_bar__plus_00_dot_40_bar__minus_01_dot_24 loc_bar__minus_6_bar__minus_6_bar_3_bar_45)
        (receptacleAtLocation Cabinet_bar__minus_02_dot_50_bar__plus_00_dot_40_bar__minus_02_dot_21 loc_bar__minus_6_bar__minus_6_bar_3_bar_45)
        (receptacleAtLocation Cabinet_bar__minus_02_dot_51_bar__plus_00_dot_43_bar__minus_00_dot_29 loc_bar__minus_6_bar__minus_4_bar_3_bar_45)
        (receptacleAtLocation Cabinet_bar__minus_02_dot_51_bar__plus_00_dot_43_bar__minus_01_dot_26 loc_bar__minus_6_bar__minus_8_bar_3_bar_45)
        (receptacleAtLocation CounterTop_bar__minus_02_dot_75_bar__plus_00_dot_99_bar__minus_01_dot_24 loc_bar__minus_9_bar__minus_4_bar_3_bar_60)
        (receptacleAtLocation GarbageCan_bar__minus_01_dot_53_bar__plus_00_dot_00_bar__minus_00_dot_18 loc_bar__minus_2_bar__minus_3_bar_3_bar_60)
        (receptacleAtLocation HandTowelHolder_bar__minus_02_dot_59_bar__plus_01_dot_74_bar__minus_00_dot_01 loc_bar__minus_9_bar__minus_2_bar_0_bar__minus_15)
        (receptacleAtLocation Sink_bar__minus_02_dot_91_bar__plus_00_dot_99_bar__minus_00_dot_73_bar_SinkBasin loc_bar__minus_9_bar__minus_2_bar_3_bar_60)
        (receptacleAtLocation Sink_bar__minus_02_dot_91_bar__plus_00_dot_99_bar__minus_01_dot_76_bar_SinkBasin loc_bar__minus_9_bar__minus_8_bar_3_bar_60)
        (receptacleAtLocation ToiletPaperHanger_bar__plus_00_dot_00_bar__plus_00_dot_98_bar__minus_03_dot_33 loc_bar__minus_2_bar__minus_11_bar_2_bar_45)
        (receptacleAtLocation Toilet_bar__minus_00_dot_55_bar__plus_00_dot_00_bar__minus_03_dot_53 loc_bar__minus_2_bar__minus_11_bar_2_bar_60)
        (receptacleAtLocation TowelHolder_bar__minus_01_dot_16_bar__plus_01_dot_52_bar__minus_02_dot_89 loc_bar__minus_2_bar__minus_11_bar_3_bar_15)
        (objectAtLocation SprayBottle_bar__minus_02_dot_68_bar__plus_01_dot_00_bar__minus_01_dot_32 loc_bar__minus_9_bar__minus_4_bar_3_bar_60)
        (objectAtLocation Cloth_bar__minus_02_dot_67_bar__plus_00_dot_08_bar__minus_01_dot_06 loc_bar__minus_6_bar__minus_6_bar_3_bar_45)
        (objectAtLocation Candle_bar__minus_00_dot_68_bar__plus_01_dot_05_bar__minus_03_dot_95 loc_bar__minus_2_bar__minus_11_bar_2_bar_60)
        (objectAtLocation ShowerDoor_bar__minus_02_dot_20_bar__plus_01_dot_05_bar__minus_02_dot_50 loc_bar__minus_9_bar__minus_8_bar_2_bar_45)
        (objectAtLocation Mirror_bar__minus_02_dot_95_bar__plus_01_dot_56_bar__minus_01_dot_19 loc_bar__minus_9_bar__minus_5_bar_3_bar_15)
        (objectAtLocation Sink_bar__minus_02_dot_91_bar__plus_00_dot_99_bar__minus_00_dot_73 loc_bar__minus_9_bar__minus_3_bar_3_bar_45)
        (objectAtLocation ShowerGlass_bar__minus_02_dot_08_bar__plus_01_dot_48_bar__minus_03_dot_26 loc_bar__minus_8_bar__minus_8_bar_2_bar_15)
        (objectAtLocation Sink_bar__minus_02_dot_91_bar__plus_00_dot_99_bar__minus_01_dot_76 loc_bar__minus_9_bar__minus_7_bar_3_bar_45)
        (objectAtLocation Towel_bar__minus_01_dot_05_bar__plus_01_dot_54_bar__minus_02_dot_89 loc_bar__minus_2_bar__minus_11_bar_3_bar_15)
        (objectAtLocation HandTowel_bar__minus_02_dot_59_bar__plus_01_dot_64_bar__minus_00_dot_05 loc_bar__minus_9_bar__minus_2_bar_0_bar__minus_15)
        (objectAtLocation SoapBar_bar__minus_02_dot_67_bar__plus_00_dot_08_bar__minus_00_dot_36 loc_bar__minus_6_bar__minus_4_bar_3_bar_45)
        (objectAtLocation ToiletPaper_bar__minus_02_dot_64_bar__plus_00_dot_07_bar__minus_01_dot_89 loc_bar__minus_6_bar__minus_6_bar_3_bar_45)
        (objectAtLocation ToiletPaper_bar__minus_02_dot_82_bar__plus_00_dot_08_bar__minus_01_dot_60 loc_bar__minus_6_bar__minus_8_bar_3_bar_45)
        (objectAtLocation SoapBottle_bar__minus_02_dot_87_bar__plus_00_dot_08_bar__minus_02_dot_03 loc_bar__minus_6_bar__minus_6_bar_3_bar_45)
        (objectAtLocation Window_bar__minus_00_dot_57_bar__plus_01_dot_73_bar__minus_04_dot_10 loc_bar__minus_2_bar__minus_11_bar_2_bar_0)
        (objectAtLocation LightSwitch_bar__minus_01_dot_33_bar__plus_01_dot_39_bar__plus_00_dot_00 loc_bar__minus_5_bar__minus_2_bar_0_bar_30)
        (objectAtLocation Candle_bar__minus_00_dot_42_bar__plus_01_dot_05_bar__minus_03_dot_95 loc_bar__minus_2_bar__minus_11_bar_2_bar_60)
        (objectAtLocation ScrubBrush_bar__minus_01_dot_05_bar__plus_00_dot_00_bar__minus_03_dot_83 loc_bar__minus_2_bar__minus_11_bar_2_bar_60)
        (objectAtLocation Plunger_bar__minus_01_dot_07_bar__plus_00_dot_00_bar__minus_03_dot_53 loc_bar__minus_2_bar__minus_11_bar_2_bar_60)
        (objectAtLocation Cloth_bar__minus_02_dot_67_bar__plus_00_dot_08_bar__minus_00_dot_52 loc_bar__minus_6_bar__minus_4_bar_3_bar_45)
        (objectAtLocation SprayBottle_bar__minus_02_dot_61_bar__plus_01_dot_00_bar__minus_00_dot_28 loc_bar__minus_9_bar__minus_4_bar_3_bar_60)
        (objectAtLocation FloorLamp_bar__minus_02_dot_91_bar__plus_00_dot_99_bar__minus_01_dot_76 loc_bar__minus_9_bar__minus_7_bar_3_bar_45)
        (objectAtLocation FloorLamp_bar__minus_02_dot_91_bar__plus_00_dot_99_bar__minus_00_dot_73 loc_bar__minus_9_bar__minus_3_bar_3_bar_45)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 SprayBottleType)
                                    (receptacleType ?r ToiletType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 SprayBottleType)
                                            (receptacleType ?r ToiletType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            