{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000243.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000244.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000245.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000283.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000284.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000285.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000286.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000287.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000288.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000289.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000290.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000291.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000292.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000293.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000294.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000295.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000296.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000297.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000298.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000299.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000300.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000301.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000302.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000303.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000304.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000305.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000306.png", "low_idx": 48}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Watch", "parent_target": "<PERSON><PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["shelf"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|8|-3|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["watch"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Watch", [8.6694784, 8.6694784, -5.71164896, -5.71164896, 4.17998648, 4.17998648]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [9.868, 9.868, -5.652, -5.652, 4.148, 4.148]], "forceVisible": true, "objectId": "Watch|+02.17|+01.04|-01.43"}}, {"discrete_action": {"action": "GotoLocation", "args": ["dresser"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-3|-3|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["watch", "dresser"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Watch", [8.6694784, 8.6694784, -5.71164896, -5.71164896, 4.17998648, 4.17998648]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON>", [-5.312, -5.312, -2.98, -2.98, 0.0044, 0.0044]], "forceVisible": true, "objectId": "Watch|+02.17|+01.04|-01.43", "receptacleObjectId": "<PERSON><PERSON><PERSON>|-01.33|+00.00|-00.75"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-3|-2|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["watch"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Watch", [-5.4007778, -5.4007778, -2.3203252, -2.3203252, 0.425715268, 0.425715268]], "coordinateReceptacleObjectId": ["Drawer", [-5.1436, -5.1436, -2.3224, -2.3224, 0.663599968, 0.663599968]], "forceVisible": true, "objectId": "Watch|-01.35|+00.11|-00.58"}}, {"discrete_action": {"action": "GotoLocation", "args": ["dresser"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-3|-3|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["watch", "dresser"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Watch", [-5.4007778, -5.4007778, -2.3203252, -2.3203252, 0.425715268, 0.425715268]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON>", [-5.312, -5.312, -2.98, -2.98, 0.0044, 0.0044]], "forceVisible": true, "objectId": "Watch|-01.35|+00.11|-00.58", "receptacleObjectId": "<PERSON><PERSON><PERSON>|-01.33|+00.00|-00.75"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Watch|+02.17|+01.04|-01.43"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [87, 111, 110, 124], "mask": [[33096, 6], [33395, 9], [33691, 17], [33988, 22], [34287, 23], [34587, 8], [34596, 8], [34607, 3], [34887, 3], [34897, 6], [34909, 2], [35187, 2], [35209, 2], [35508, 3], [35788, 2], [35806, 5], [36088, 5], [36094, 1], [36103, 7], [36389, 20], [36690, 17], [36994, 10]], "point": [98, 116]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Watch|+02.17|+01.04|-01.43", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON><PERSON>|-01.33|+00.00|-00.75"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 218], "mask": [[76, 41], [188, 37], [241, 59], [376, 41], [488, 37], [541, 59], [676, 42], [787, 38], [841, 59], [975, 44], [1086, 39], [1141, 59], [1275, 45], [1385, 40], [1441, 59], [1575, 46], [1684, 42], [1741, 59], [1875, 47], [1983, 43], [2041, 59], [2174, 48], [2282, 46], [2340, 60], [2474, 50], [2581, 52], [2636, 64], [2774, 51], [2880, 120], [3074, 52], [3179, 26], [3278, 22], [3373, 55], [3477, 27], [3580, 20], [3673, 56], [3775, 29], [3880, 20], [3973, 58], [4073, 31], [4181, 19], [4273, 59], [4372, 32], [4481, 19], [4572, 62], [4671, 33], [4781, 19], [4872, 63], [4969, 35], [5082, 18], [5172, 65], [5267, 38], [5382, 18], [5472, 68], [5565, 40], [5683, 17], [5771, 72], [5862, 43], [5983, 17], [6071, 78], [6156, 49], [6283, 17], [6369, 136], [6584, 16], [6669, 136], [6884, 16], [6969, 137], [7185, 16], [7269, 137], [7485, 16], [7568, 138], [7786, 16], [7868, 138], [8086, 220], [8386, 220], [8687, 220], [8987, 220], [9288, 219], [9588, 219], [9888, 219], [10188, 220], [10487, 222], [10711, 72], [10786, 12016], [22943, 14], [23099, 2], [23244, 12], [23399, 2], [23545, 11], [23699, 3], [23845, 11], [23999, 3], [24145, 11], [24298, 5], [24445, 11], [24598, 6], [24745, 11], [24897, 7], [25045, 11], [25196, 9], [25345, 11], [25496, 10], [25645, 11], [25795, 11], [25945, 11], [26094, 13], [26245, 11], [26394, 14], [26545, 11], [26693, 15], [26845, 11], [26992, 17], [27145, 11], [27292, 18], [27445, 11], [27591, 19], [27745, 11], [27890, 21], [28045, 11], [28190, 22], [28345, 11], [28489, 23], [28645, 11], [28788, 25], [28945, 11], [29088, 26], [29245, 11], [29387, 27], [29545, 11], [29686, 29], [29845, 10], [29986, 30], [30145, 10], [30285, 15], [30301, 15], [30445, 10], [30584, 16], [30602, 15], [30745, 10], [30884, 16], [30903, 15], [31045, 10], [31183, 17], [31203, 15], [31345, 10], [31482, 18], [31504, 15], [31645, 10], [31781, 19], [31805, 15], [31945, 10], [32081, 18], [32106, 14], [32245, 10], [32380, 18], [32406, 15], [32545, 10], [32679, 19], [32707, 15], [32845, 10], [32979, 18], [33008, 14], [33145, 10], [33278, 18], [33309, 14], [33445, 10], [33577, 18], [33609, 15], [33745, 10], [33877, 18], [33910, 14], [34045, 10], [34176, 18], [34211, 14], [34345, 10], [34475, 18], [34512, 14], [34645, 10], [34775, 17], [34812, 14], [34946, 9], [35074, 18], [35113, 14], [35246, 9], [35373, 18], [35414, 14], [35546, 9], [35673, 17], [35715, 13], [35846, 9], [35972, 17], [36015, 14], [36146, 9], [36271, 17], [36316, 14], [36446, 9], [36571, 17], [36617, 13], [36746, 9], [36870, 17], [36918, 13], [37046, 9], [37169, 17], [37218, 14], [37346, 9], [37469, 16], [37519, 13], [37646, 9], [37768, 17], [37820, 13], [37946, 9], [38067, 17], [38121, 13], [38246, 9], [38367, 16], [38421, 14], [38546, 9], [38666, 16], [38722, 13], [38846, 9], [38965, 16], [39023, 13], [39146, 9], [39265, 16], [39324, 13], [39446, 9], [39564, 16], [39624, 13], [39746, 9], [39863, 16], [39925, 13], [40046, 9], [40163, 15], [40226, 13], [40346, 9], [40462, 16], [40527, 12], [40646, 9], [40761, 16], [40827, 249], [41128, 247], [41429, 14], [41545, 10], [41658, 17], [41730, 13], [41846, 8], [41958, 16], [42030, 13], [42146, 8], [42258, 15], [42331, 12], [42446, 8], [42557, 15], [42632, 12], [42746, 8], [42856, 15], [42933, 12], [43046, 8], [43156, 15], [43233, 12], [43346, 8], [43455, 15], [43534, 12], [43646, 8], [43754, 15], [43835, 12], [43946, 8], [44054, 14], [44136, 11], [44246, 8], [44353, 15], [44436, 12], [44546, 8], [44652, 15], [44737, 12], [44846, 8], [44952, 14], [45038, 11], [45146, 8], [45251, 14], [45339, 11], [45446, 8], [45550, 15], [45639, 12], [45746, 8], [45850, 14], [45940, 11], [46046, 8], [46149, 14], [46241, 11], [46346, 8], [46448, 14], [46542, 11], [46646, 8], [46748, 13], [46842, 11], [46946, 8], [47047, 14], [47143, 11], [47247, 7], [47346, 14], [47444, 11], [47547, 7], [47646, 13], [47745, 10], [47847, 7], [47945, 13], [48045, 11], [48147, 7], [48244, 14], [48346, 11], [48447, 7], [48544, 13], [48647, 10], [48747, 7], [48843, 13], [48948, 10], [49047, 7], [49142, 13], [49248, 11], [49347, 7], [49442, 12], [49549, 10], [49647, 7], [49741, 13], [49850, 10], [49947, 7], [50040, 13], [50151, 10], [50247, 7], [50340, 12], [50451, 10], [50547, 7], [50639, 12], [50752, 10], [50847, 7], [50938, 13], [51053, 10], [51147, 7], [51238, 12], [51354, 9], [51447, 7], [51537, 12], [51654, 10], [51747, 6], [51836, 12], [51955, 10], [52047, 6], [52136, 12], [52256, 9], [52347, 6], [52435, 12], [52557, 9], [52647, 6], [52734, 12], [52857, 10], [52947, 7], [53033, 12], [53158, 186], [53459, 16], [53546, 9], [53627, 17], [53760, 10], [53847, 7], [53931, 12], [54060, 10], [54147, 7], [54231, 11], [54361, 9], [54447, 6], [54530, 11], [54662, 9], [54747, 6], [54830, 11], [54963, 9], [55047, 6], [55129, 11], [55263, 9], [55347, 6], [55428, 11], [55564, 9], [55647, 6], [55728, 10], [55865, 9], [55947, 6], [56027, 11], [56166, 8], [56247, 6], [56326, 11], [56466, 9], [56548, 5], [56626, 10], [56767, 9], [56848, 5], [56925, 10], [57068, 8], [57148, 5], [57224, 10], [57369, 8], [57448, 5], [57524, 10], [57669, 9], [57748, 5], [57823, 10], [57970, 8], [58048, 5], [58122, 10], [58271, 8], [58348, 5], [58422, 9], [58572, 8], [58648, 5], [58721, 10], [58872, 9], [58948, 5], [59020, 10], [59173, 8], [59248, 5], [59320, 9], [59474, 8], [59548, 5], [59619, 9], [59775, 8], [59848, 5], [59918, 9], [60075, 8], [60148, 5], [60217, 10], [60376, 8], [60447, 6], [60517, 9], [60677, 8], [60748, 5], [60816, 9], [60978, 7], [61048, 5], [61115, 9], [61278, 8], [61348, 5], [61415, 9], [61579, 8], [61648, 5], [61714, 9], [61880, 7], [61948, 5], [62013, 9], [62181, 7], [62248, 5], [62313, 8], [62481, 8], [62548, 5], [62612, 9], [62782, 7], [62848, 5], [62911, 9], [63083, 7], [63148, 5], [63211, 8], [63384, 7], [63448, 5], [63510, 8], [63684, 7], [63748, 5], [63809, 8], [63985, 7], [64048, 5], [64109, 8], [64286, 7], [64348, 5], [64408, 8], [64587, 6], [64707, 8], [64887, 7], [65007, 7], [65188, 10], [65304, 10]], "point": [149, 108]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-01.29|+00.17|-00.58"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [103, 203, 165, 217], "mask": [[60703, 63], [61003, 63], [61304, 61], [61604, 61], [61904, 61], [62205, 60], [62505, 60], [62806, 59], [63106, 59], [63407, 57], [63707, 57], [64008, 56], [64308, 56], [64609, 55], [64909, 55]], "point": [134, 209]}}, "high_idx": 5}, {"api_action": {"action": "PickupObject", "objectId": "Watch|-01.35|+00.11|-00.58"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [132, 215, 138, 229], "mask": [[64334, 2], [64633, 1], [65232, 1], [65238, 1], [65532, 1], [65832, 1], [66132, 1], [66432, 1], [66732, 1], [67032, 1], [67038, 1], [67332, 1], [67632, 1], [67638, 1], [68233, 1], [68534, 2]], "point": [132, 221]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-01.29|+00.17|-00.58"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [99, 203, 165, 255], "mask": [[60705, 59], [61005, 59], [61305, 59], [61605, 59], [61905, 59], [62205, 59], [62504, 60], [62804, 60], [63104, 60], [63404, 60], [63704, 60], [64004, 60], [64304, 60], [64604, 60], [64904, 60], [65204, 60], [65504, 60], [65803, 61], [66103, 61], [66403, 61], [66703, 61], [67003, 61], [67303, 61], [67603, 61], [67903, 61], [68203, 61], [68503, 61], [68803, 62], [69102, 63], [69402, 63], [69702, 63], [70002, 63], [70302, 63], [70602, 63], [70902, 63], [71202, 63], [71502, 63], [71802, 63], [72102, 44], [72157, 8], [72401, 37], [72461, 4], [72701, 34], [72764, 1], [73001, 32], [73299, 32], [73599, 30], [73899, 29], [73940, 13], [73957, 2], [74200, 27], [74236, 27], [74501, 25], [74534, 32], [74802, 23], [74832, 34], [75103, 21], [75130, 36], [75404, 20], [75429, 36], [75705, 18], [75728, 37], [76006, 17], [76027, 38], [76307, 15], [76326, 38]], "point": [132, 228]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Watch|-01.35|+00.11|-00.58", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON><PERSON>|-01.33|+00.00|-00.75"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 218], "mask": [[76, 41], [188, 37], [241, 59], [376, 41], [488, 37], [541, 59], [676, 42], [787, 38], [841, 59], [975, 44], [1086, 39], [1141, 59], [1275, 45], [1385, 40], [1441, 59], [1575, 46], [1684, 42], [1741, 59], [1875, 47], [1983, 43], [2041, 59], [2174, 48], [2282, 46], [2340, 60], [2474, 50], [2581, 52], [2636, 64], [2774, 51], [2880, 120], [3074, 52], [3179, 26], [3278, 22], [3373, 55], [3477, 27], [3580, 20], [3673, 56], [3775, 29], [3880, 20], [3973, 58], [4073, 31], [4181, 19], [4273, 59], [4372, 32], [4481, 19], [4572, 62], [4671, 33], [4781, 19], [4872, 63], [4969, 35], [5082, 18], [5172, 65], [5267, 38], [5382, 18], [5472, 68], [5565, 40], [5683, 17], [5771, 72], [5862, 43], [5983, 17], [6071, 78], [6156, 49], [6283, 17], [6369, 136], [6584, 16], [6669, 136], [6884, 16], [6969, 137], [7185, 16], [7269, 137], [7485, 16], [7568, 138], [7786, 16], [7868, 138], [8086, 64], [8157, 7], [8166, 140], [8386, 64], [8458, 6], [8466, 140], [8687, 64], [8765, 142], [8987, 65], [9064, 143], [9288, 65], [9363, 144], [9588, 66], [9663, 144], [9888, 68], [9962, 145], [10188, 220], [10487, 222], [10711, 72], [10786, 12016], [22943, 14], [23099, 2], [23244, 12], [23399, 2], [23545, 11], [23699, 3], [23845, 11], [23999, 3], [24145, 11], [24298, 5], [24445, 11], [24598, 6], [24745, 11], [24897, 7], [25045, 11], [25196, 9], [25345, 11], [25496, 10], [25645, 11], [25795, 11], [25945, 11], [26094, 13], [26245, 11], [26394, 14], [26545, 11], [26693, 15], [26845, 11], [26992, 17], [27145, 11], [27292, 18], [27445, 11], [27591, 19], [27745, 11], [27890, 21], [28045, 11], [28190, 22], [28345, 11], [28489, 23], [28645, 11], [28788, 25], [28945, 11], [29088, 26], [29245, 11], [29387, 27], [29545, 11], [29686, 29], [29845, 10], [29986, 30], [30145, 10], [30285, 15], [30301, 15], [30445, 10], [30584, 16], [30602, 15], [30745, 10], [30884, 16], [30903, 15], [31045, 10], [31183, 17], [31203, 15], [31345, 10], [31482, 18], [31504, 15], [31645, 10], [31781, 19], [31805, 15], [31945, 10], [32081, 18], [32106, 14], [32245, 10], [32380, 18], [32406, 15], [32545, 10], [32679, 19], [32707, 15], [32845, 10], [32979, 18], [33008, 14], [33145, 10], [33278, 18], [33309, 14], [33445, 10], [33577, 18], [33609, 15], [33745, 10], [33877, 18], [33910, 14], [34045, 10], [34176, 18], [34211, 14], [34345, 10], [34475, 18], [34512, 14], [34645, 10], [34775, 17], [34812, 14], [34946, 9], [35074, 18], [35113, 14], [35246, 9], [35373, 18], [35414, 14], [35546, 9], [35673, 17], [35715, 13], [35846, 9], [35972, 17], [36015, 14], [36146, 9], [36271, 17], [36316, 14], [36446, 9], [36571, 17], [36617, 13], [36746, 9], [36870, 17], [36918, 13], [37046, 9], [37169, 17], [37218, 14], [37346, 9], [37469, 16], [37519, 13], [37646, 9], [37768, 17], [37820, 13], [37946, 9], [38067, 17], [38121, 13], [38246, 9], [38367, 16], [38421, 14], [38546, 9], [38666, 16], [38722, 13], [38846, 9], [38965, 16], [39023, 13], [39146, 9], [39265, 16], [39324, 13], [39446, 9], [39564, 16], [39624, 13], [39746, 9], [39863, 16], [39925, 13], [40046, 9], [40163, 15], [40226, 13], [40346, 9], [40462, 16], [40527, 12], [40646, 9], [40761, 16], [40827, 249], [41128, 247], [41429, 14], [41545, 10], [41658, 17], [41730, 13], [41846, 8], [41958, 16], [42030, 13], [42146, 8], [42258, 15], [42331, 12], [42446, 8], [42557, 15], [42632, 12], [42746, 8], [42856, 15], [42933, 12], [43046, 8], [43156, 15], [43233, 12], [43346, 8], [43455, 15], [43534, 12], [43646, 8], [43754, 15], [43835, 12], [43946, 8], [44054, 14], [44136, 11], [44246, 8], [44353, 15], [44436, 12], [44546, 8], [44652, 15], [44737, 12], [44846, 8], [44952, 14], [45038, 11], [45146, 8], [45251, 14], [45339, 11], [45446, 8], [45550, 15], [45639, 12], [45746, 8], [45850, 14], [45940, 11], [46046, 8], [46149, 14], [46241, 11], [46346, 8], [46448, 14], [46542, 11], [46646, 8], [46748, 13], [46842, 11], [46946, 8], [47047, 14], [47143, 11], [47247, 7], [47346, 14], [47444, 11], [47547, 7], [47646, 13], [47745, 10], [47847, 7], [47945, 13], [48045, 11], [48147, 7], [48244, 14], [48346, 11], [48447, 7], [48544, 13], [48647, 10], [48747, 7], [48843, 13], [48948, 10], [49047, 7], [49142, 13], [49248, 11], [49347, 7], [49442, 12], [49549, 10], [49647, 7], [49741, 13], [49850, 10], [49947, 7], [50040, 13], [50151, 10], [50247, 7], [50340, 12], [50451, 10], [50547, 7], [50639, 12], [50752, 10], [50847, 7], [50938, 13], [51053, 10], [51147, 7], [51238, 12], [51354, 9], [51447, 7], [51537, 12], [51654, 10], [51747, 6], [51836, 12], [51955, 10], [52047, 6], [52136, 12], [52256, 9], [52347, 6], [52435, 12], [52557, 9], [52647, 6], [52734, 12], [52857, 10], [52947, 7], [53033, 12], [53158, 186], [53459, 16], [53546, 9], [53627, 17], [53760, 10], [53847, 7], [53931, 12], [54060, 10], [54147, 7], [54231, 11], [54361, 9], [54447, 6], [54530, 11], [54662, 9], [54747, 6], [54830, 11], [54963, 9], [55047, 6], [55129, 11], [55263, 9], [55347, 6], [55428, 11], [55564, 9], [55647, 6], [55728, 10], [55865, 9], [55947, 6], [56027, 11], [56166, 8], [56247, 6], [56326, 11], [56466, 9], [56548, 5], [56626, 10], [56767, 9], [56848, 5], [56925, 10], [57068, 8], [57148, 5], [57224, 10], [57369, 8], [57448, 5], [57524, 10], [57669, 9], [57748, 5], [57823, 10], [57970, 8], [58048, 5], [58122, 10], [58271, 8], [58348, 5], [58422, 9], [58572, 8], [58648, 5], [58721, 10], [58872, 9], [58948, 5], [59020, 10], [59173, 8], [59248, 5], [59320, 9], [59474, 8], [59548, 5], [59619, 9], [59775, 8], [59848, 5], [59918, 9], [60075, 8], [60148, 5], [60217, 10], [60376, 8], [60447, 6], [60517, 9], [60677, 8], [60748, 5], [60816, 9], [60978, 7], [61048, 5], [61115, 9], [61278, 8], [61348, 5], [61415, 9], [61579, 8], [61648, 5], [61714, 9], [61880, 7], [61948, 5], [62013, 9], [62181, 7], [62248, 5], [62313, 8], [62481, 8], [62548, 5], [62612, 9], [62782, 7], [62848, 5], [62911, 9], [63083, 7], [63148, 5], [63211, 8], [63384, 7], [63448, 5], [63510, 8], [63684, 7], [63748, 5], [63809, 8], [63985, 7], [64048, 5], [64109, 8], [64286, 7], [64348, 5], [64408, 8], [64587, 6], [64707, 8], [64887, 7], [65007, 7], [65188, 10], [65304, 10]], "point": [149, 108]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan301", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": 1.25, "y": 0.9009992, "z": 1.25}, "object_poses": [{"objectName": "Mug_2a940808", "position": {"x": 2.40391779, "y": 0.7928, "z": -1.24994731}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "TissueBox_00aeed40", "position": {"x": -1.29435253, "y": 0.677608252, "z": -0.543508649}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Watch_0f7be4f9", "position": {"x": 2.1673696, "y": 1.04499662, "z": -1.42791224}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Watch_0f7be4f9", "position": {"x": 1.96231079, "y": 1.04764462, "z": -1.40308774}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Watch_0f7be4f9", "position": {"x": -1.35019445, "y": 0.106428817, "z": -0.5800813}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CD_f25640ea", "position": {"x": -1.35019445, "y": 0.105762854, "z": -0.8818699}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CD_f25640ea", "position": {"x": 2.839, "y": 0.0398521759, "z": 0.18251498}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "CellPhone_b9003495", "position": {"x": 1.83983088, "y": 0.798104942, "z": -1.18569851}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_80d98dab", "position": {"x": 2.58702, "y": 1.04471993, "z": -1.39808774}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_80d98dab", "position": {"x": 1.651802, "y": 0.794796, "z": -1.12144959}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "AlarmClock_a68436a8", "position": {"x": 2.5919466, "y": 0.7965486, "z": -1.21782291}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Book_c3876352", "position": {"x": -0.902, "y": 0.5744, "z": 1.179}, "rotation": {"x": 0.0, "y": 332.922272, "z": 0.0}}, {"objectName": "Box_32219d9d", "position": {"x": 2.7342, "y": 0.195, "z": 0.89}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_acb78e08", "position": {"x": -0.2898885, "y": 0.561, "z": 0.846990347}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_b9003495", "position": {"x": -1.248423, "y": 1.23822176, "z": -0.5537876}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "BaseballBat_7de4b683", "position": {"x": 2.847, "y": 0.643, "z": 1.838}, "rotation": {"x": 0.0, "y": 0.0, "z": 335.1118}}, {"objectName": "BasketBall_7b5f2c88", "position": {"x": 1.01986575, "y": 0.330830961, "z": -0.9766739}, "rotation": {"x": 346.948547, "y": 295.819916, "z": 0.6563854}}, {"objectName": "TissueBox_00aeed40", "position": {"x": -1.29372144, "y": 0.384006232, "z": -0.614350438}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_80d98dab", "position": {"x": 2.85323119, "y": 0.5703723, "z": -1.21824765}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "AlarmClock_a68436a8", "position": {"x": 2.21588874, "y": 0.7965486, "z": -1.28207183}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pencil_89afc677", "position": {"x": 2.58602, "y": 0.81406486, "z": -1.38217545}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Boots_dc0ae5c1", "position": {"x": -1.27998614, "y": 0.0006508231, "z": -1.34300911}, "rotation": {"x": 359.9876, "y": 27.1430378, "z": 359.993652}}, {"objectName": "Pillow_419db2d8", "position": {"x": -0.634, "y": 0.63, "z": 1.6116}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "KeyChain_7bb65943", "position": {"x": -1.35281718, "y": 1.233747, "z": -0.5537876}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_600fd48d", "position": {"x": -1.31801915, "y": 1.23453724, "z": -0.745}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Watch_0f7be4f9", "position": {"x": 1.50911212, "y": 0.796085656, "z": -1.39515185}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CD_f25640ea", "position": {"x": 2.79416656, "y": 0.124976709, "z": -1.24647689}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_d94967db", "position": {"x": 2.21588874, "y": 0.8004528, "z": -1.12144959}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_2a940808", "position": {"x": 2.82123256, "y": 0.7887166, "z": -1.22375643}, "rotation": {"x": -0.000668735, "y": 2.97707666e-05, "z": 0.0007859358}}, {"objectName": "Statue_e0075340", "position": {"x": 1.823921, "y": 1.036511, "z": -1.419659}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1867495701, "scene_num": 301}, "task_id": "trial_T20190906_180154_425245", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A3JZLYQ606HJJR_351SEKWQS3YGL10DPMHGWVOK8UZDMP", "high_descs": ["Turn around and move to the white table across the room.", "Pick up the watch on the top shelf of the desk.", "Turn to the right and move across the room to the white dresser. ", "Lean the watch on the lamp on the dresser. ", "On the white dresser there are 8 drawers.  Open the bottom drawer on the right hand side.", "Pick up the watch from the drawer. ", "Close the drawer that the watch was in.", "Place the watch on top of the cell phone that is on top of the dresser. "], "task_desc": "Moving two watches from one place to another. ", "votes": [1, 1, 1]}, {"assignment_id": "A1ELPYAFO7MANS_3HFNH7HEMKVAI08WJ5P5JPBMVJHGQM", "high_descs": ["Turn around and walk to the desk.", "Pick up the watch on the left shelf.", "Turn right and walk to the dresser.", "Put the watch on the left side of the bowl.", "Look down at the drawers.", "Open up the bottom right drawer and take out the watch.", "Look up to the top of the dresser.", "Place the watch on top of the cell phone."], "task_desc": "Place two watches on a dresser.", "votes": [1, 1, 1]}, {"assignment_id": "A3PPLDHC3CG0YN_3CN4LGXD5053FM6YFE4YNMHM7ZB4Y5", "high_descs": ["Turn around and walk to the white desk. ", "Pick up the watch behind the alarm clock. ", "Turn right and walk to the white dresser. ", "Put the watch down on the top of the dresser in front of the lamp on the left. ", "Move to the right side of the dresser and look at the bottom drawer. ", "Open the bottom drawer on the right, take the watch out of the drawer, close the drawer. ", "Move to face the top of the dresser. ", "Put the watch down on the cell phone that's on the right side of the top of the dresser. "], "task_desc": "To move two watches to the top of a dresser. ", "votes": [1, 1]}, {"assignment_id": "A1ELPYAFO7MANS_3P59JYT76O1XVMSH1UYBMQS98I12TK", "high_descs": ["Turn around and veer right to the white desk with shelves.", "Pick up the left watch.", "Turn right and walk to the night stand at the foot of the bed.", "Place the watch on the night stand in front of the set of books.", "Look down at the drawers.", "Open the bottom right drawer and take out the watch.", "Look back up at the counter.", "Place the watch on top of the cell phone on the counter."], "task_desc": "Place to watches on top of a cabinet.", "votes": [1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3RKNTXVS3PF2J48VZ8QIK0ZF47XA4O", "high_descs": ["Turn right and walk towards the wall, then hang a right and walk up to the desk.", "Pick up the leftmost watch off of the white shelf behind the desk.", "Turn around and walk towards the door, then hang a left and walk up to the tall white dresser.", "Put the watch on top of the dresser in front of the lamp.", "Turn right and walk towards the bed, then turn left.", "Open the bottom right drawer of the dresser and take out the watch, then close the drawer.", "Turn left and walk forward, then turn right to face the dresser.", "Put the watch on top of the phone that is on the dresser."], "task_desc": "Move two watches to the top of the dresser.", "votes": [1, 1]}, {"assignment_id": "A3A0VPWPASCO9J_33F859I569U1EQ1IKTAX90SOOMLBH2", "high_descs": ["Turn right and walk across the room to the door, turn right again and walk across the room to the desk.", "Pick up the watch on the shelf directly above the alarm clock.", "Turn right walk past the bean bag chair and turn left, walk to the window, turn right, walk to the dresser.", "Set the watch down in front of the lamp.", "Turn right and walk to the foot of the bed and turn left to face the dresser.", "Open the bottom drawer on the right and take out the watch that's inside.", "Turn left and walk to the window and turn back right to face the dresser.", "Place the watch on top of the cell phone."], "task_desc": "Place two watches on top of the dresser.", "votes": [1, 1, 0]}]}}