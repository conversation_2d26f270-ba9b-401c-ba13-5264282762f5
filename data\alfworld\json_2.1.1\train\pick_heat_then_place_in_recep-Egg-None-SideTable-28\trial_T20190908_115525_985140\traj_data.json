{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000186.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000187.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000188.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000189.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000190.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000191.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000192.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000193.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000194.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000195.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000245.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000246.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000247.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000248.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000249.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000250.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000251.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000252.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000253.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000254.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000255.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000256.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000257.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000258.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000259.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000260.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000261.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000262.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000263.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000264.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000265.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000320.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000321.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000322.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000323.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000324.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000325.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000326.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000327.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000328.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000329.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000330.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000331.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000332.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000333.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000334.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000335.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000336.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000337.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000338.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000339.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000340.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000341.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000342.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000343.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000344.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000345.png", "low_idx": 67}, {"high_idx": 4, "image_name": "000000346.png", "low_idx": 67}, {"high_idx": 4, "image_name": "000000347.png", "low_idx": 68}, {"high_idx": 4, "image_name": "000000348.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000349.png", "low_idx": 69}, {"high_idx": 5, "image_name": "000000350.png", "low_idx": 69}, {"high_idx": 5, "image_name": "000000351.png", "low_idx": 69}, {"high_idx": 5, "image_name": "000000352.png", "low_idx": 69}, {"high_idx": 5, "image_name": "000000353.png", "low_idx": 69}, {"high_idx": 5, "image_name": "000000354.png", "low_idx": 69}, {"high_idx": 5, "image_name": "000000355.png", "low_idx": 69}, {"high_idx": 5, "image_name": "000000356.png", "low_idx": 69}, {"high_idx": 5, "image_name": "000000357.png", "low_idx": 69}, {"high_idx": 5, "image_name": "000000358.png", "low_idx": 69}, {"high_idx": 5, "image_name": "000000359.png", "low_idx": 69}, {"high_idx": 5, "image_name": "000000360.png", "low_idx": 69}, {"high_idx": 5, "image_name": "000000361.png", "low_idx": 69}, {"high_idx": 5, "image_name": "000000362.png", "low_idx": 69}, {"high_idx": 5, "image_name": "000000363.png", "low_idx": 69}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "SideTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-16|-4|0|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-16.97022248, -16.97022248, -1.722671748, -1.722671748, 2.9262672, 2.9262672]], "coordinateReceptacleObjectId": ["SideTable", [-16.284, -16.284, -1.236, -1.236, 2.904, 2.904]], "forceVisible": true, "objectId": "Egg|-04.24|+00.73|-00.43"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-8|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-15|-5|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "sidetable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-16.97022248, -16.97022248, -1.722671748, -1.722671748, 2.9262672, 2.9262672]], "coordinateReceptacleObjectId": ["SideTable", [-16.284, -16.284, -1.236, -1.236, 2.904, 2.904]], "forceVisible": true, "objectId": "Egg|-04.24|+00.73|-00.43", "receptacleObjectId": "SideTable|-04.07|+00.73|-00.31"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-04.24|+00.73|-00.43"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [80, 124, 96, 142], "mask": [[36985, 4], [37283, 9], [37582, 11], [37881, 13], [38181, 14], [38480, 16], [38780, 16], [39080, 17], [39380, 17], [39680, 17], [39980, 17], [40280, 17], [40580, 17], [40881, 16], [41181, 15], [41482, 14], [41783, 12], [42084, 10], [42386, 6]], "point": [88, 132]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-04.24|+00.73|-00.43", "placeStationary": true, "receptacleObjectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 8504], [8510, 286], [8819, 275], [9123, 270], [9424, 269], [9725, 267], [10026, 266], [10326, 266], [10627, 264], [10927, 264], [11228, 262], [11528, 262], [11829, 261], [12129, 54], [12184, 205], [12430, 53], [12484, 205], [12730, 42], [12793, 196], [13030, 41], [13094, 194], [13331, 39], [13395, 193], [13631, 38], [13696, 192], [13931, 38], [13996, 192], [14232, 36], [14297, 191], [14532, 36], [14597, 191], [14832, 35], [14897, 191], [15132, 35], [15198, 189], [15432, 35], [15498, 189], [15732, 34], [15798, 189], [16033, 33], [16098, 189], [16333, 33], [16398, 189], [16633, 33], [16699, 188], [16933, 33], [16999, 188], [17233, 32], [17298, 189], [17533, 32], [17598, 190], [17833, 32], [17898, 190], [18133, 32], [18198, 190], [18433, 32], [18498, 190], [18733, 33], [18798, 190], [19033, 33], [19097, 191], [19333, 33], [19397, 191], [19633, 33], [19697, 191], [19933, 34], [19997, 191], [20233, 34], [20296, 193], [20533, 34], [20596, 193], [20832, 36], [20896, 194], [21132, 36], [21195, 195], [21432, 36], [21495, 195], [21731, 38], [21794, 197], [22031, 39], [22094, 197], [22331, 39], [22393, 199], [22630, 41], [22693, 200], [22929, 42], [22992, 202], [23228, 44], [23291, 204], [23527, 45], [23590, 208], [23824, 49], [23889, 285], [24189, 295], [24487, 8256], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [149, 62]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 8504], [8510, 286], [8819, 275], [9123, 270], [9424, 269], [9725, 267], [10026, 266], [10326, 266], [10627, 264], [10927, 264], [11228, 262], [11528, 262], [11829, 261], [12129, 54], [12184, 205], [12430, 53], [12484, 205], [12730, 42], [12793, 196], [13030, 41], [13094, 194], [13331, 39], [13395, 193], [13631, 38], [13696, 192], [13931, 14], [13948, 21], [13996, 192], [14232, 11], [14250, 18], [14297, 191], [14532, 9], [14552, 16], [14597, 191], [14832, 8], [14853, 14], [14897, 191], [15132, 7], [15154, 13], [15198, 189], [15432, 6], [15455, 12], [15498, 189], [15732, 6], [15755, 11], [15798, 189], [16033, 4], [16056, 10], [16098, 189], [16333, 4], [16356, 10], [16398, 189], [16633, 3], [16657, 9], [16699, 188], [16933, 3], [16957, 9], [16999, 188], [17233, 3], [17258, 7], [17298, 189], [17533, 2], [17558, 7], [17598, 190], [17833, 2], [17858, 7], [17898, 190], [18133, 2], [18158, 7], [18198, 190], [18433, 2], [18459, 6], [18498, 190], [18733, 1], [18759, 7], [18798, 190], [19033, 1], [19059, 7], [19097, 191], [19333, 1], [19359, 7], [19397, 191], [19633, 1], [19659, 7], [19697, 191], [19933, 1], [19959, 8], [19997, 191], [20233, 1], [20259, 8], [20296, 193], [20533, 1], [20559, 8], [20596, 193], [20832, 2], [20859, 9], [20896, 194], [21132, 3], [21158, 10], [21195, 195], [21432, 3], [21458, 10], [21495, 195], [21731, 4], [21758, 11], [21794, 197], [22031, 5], [22057, 13], [22094, 197], [22331, 5], [22357, 13], [22393, 199], [22630, 7], [22656, 15], [22693, 200], [22929, 8], [22956, 15], [22992, 202], [23228, 10], [23255, 17], [23291, 204], [23527, 12], [23554, 18], [23590, 208], [23824, 16], [23853, 20], [23889, 252], [24152, 22], [24189, 254], [24450, 34], [24487, 8256], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [158, 60]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-04.24|+00.73|-00.43"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [134, 47, 158, 82], "mask": [[13945, 3], [14243, 7], [14541, 11], [14840, 13], [15139, 15], [15438, 17], [15738, 17], [16037, 19], [16337, 19], [16636, 21], [16936, 21], [17236, 22], [17535, 23], [17835, 23], [18135, 23], [18435, 24], [18734, 25], [19034, 25], [19334, 25], [19634, 25], [19934, 25], [20234, 25], [20534, 25], [20834, 25], [21135, 23], [21435, 23], [21735, 23], [22036, 21], [22336, 21], [22637, 19], [22937, 19], [23238, 17], [23539, 15], [23840, 13], [24141, 11], [24443, 7]], "point": [146, 63]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 8504], [8510, 286], [8819, 275], [9123, 270], [9424, 269], [9725, 267], [10026, 266], [10326, 266], [10627, 264], [10927, 264], [11228, 262], [11528, 262], [11829, 261], [12129, 54], [12184, 205], [12430, 53], [12484, 205], [12730, 42], [12793, 196], [13030, 41], [13094, 194], [13331, 39], [13395, 193], [13631, 38], [13696, 192], [13931, 38], [13996, 192], [14232, 36], [14297, 191], [14532, 36], [14597, 191], [14832, 35], [14897, 191], [15132, 35], [15198, 189], [15432, 35], [15498, 189], [15732, 34], [15798, 189], [16033, 33], [16098, 189], [16333, 33], [16398, 189], [16633, 33], [16699, 188], [16933, 33], [16999, 188], [17233, 32], [17298, 189], [17533, 32], [17598, 190], [17833, 32], [17898, 190], [18133, 32], [18198, 190], [18433, 32], [18498, 190], [18733, 33], [18798, 190], [19033, 33], [19097, 191], [19333, 33], [19397, 191], [19633, 33], [19697, 191], [19933, 34], [19997, 191], [20233, 34], [20296, 193], [20533, 34], [20596, 193], [20832, 36], [20896, 194], [21132, 36], [21195, 195], [21432, 36], [21495, 195], [21731, 38], [21794, 197], [22031, 39], [22094, 197], [22331, 39], [22393, 199], [22630, 41], [22693, 200], [22929, 42], [22992, 202], [23228, 44], [23291, 204], [23527, 45], [23590, 208], [23824, 49], [23889, 285], [24189, 295], [24487, 8256], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [149, 62]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-04.24|+00.73|-00.43", "placeStationary": true, "receptacleObjectId": "SideTable|-04.07|+00.73|-00.31"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [21, 115, 140, 251], "mask": [[34251, 66], [34334, 4], [34551, 67], [34633, 5], [34851, 67], [34933, 5], [35150, 68], [35232, 6], [35450, 69], [35531, 7], [35749, 71], [35831, 7], [36049, 72], [36130, 8], [36349, 74], [36429, 9], [36648, 32], [36689, 21], [36715, 23], [36948, 30], [36991, 17], [37017, 21], [37247, 30], [37292, 15], [37318, 3], [37324, 14], [37547, 29], [37593, 13], [37618, 3], [37624, 14], [37846, 30], [37894, 12], [37919, 2], [37924, 14], [38146, 29], [38194, 11], [38219, 1], [38224, 14], [38446, 29], [38495, 10], [38519, 1], [38525, 13], [38745, 29], [38795, 9], [38819, 1], [38825, 13], [39045, 29], [39096, 9], [39119, 1], [39123, 15], [39344, 30], [39396, 9], [39419, 1], [39423, 15], [39644, 30], [39696, 9], [39718, 1], [39723, 15], [39943, 31], [39996, 10], [40018, 1], [40023, 15], [40243, 32], [40296, 10], [40317, 2], [40323, 15], [40542, 33], [40596, 11], [40616, 3], [40622, 16], [40842, 33], [40895, 14], [40915, 4], [40922, 16], [41142, 34], [41195, 23], [41222, 17], [41441, 35], [41495, 23], [41521, 18], [41741, 36], [41794, 24], [41821, 18], [42040, 38], [42094, 23], [42121, 18], [42340, 39], [42393, 24], [42422, 17], [42639, 41], [42692, 25], [42722, 17], [42939, 42], [42991, 26], [43023, 16], [43239, 44], [43290, 27], [43323, 16], [43538, 47], [43588, 28], [43623, 16], [43838, 78], [43923, 16], [44137, 41], [44192, 24], [44223, 16], [44437, 40], [44492, 24], [44523, 16], [44736, 41], [44792, 23], [44822, 17], [45036, 40], [45093, 22], [45122, 17], [45335, 41], [45393, 22], [45422, 17], [45635, 41], [45692, 23], [45721, 18], [45935, 41], [45992, 23], [46021, 18], [46234, 41], [46292, 22], [46321, 18], [46534, 41], [46592, 22], [46621, 18], [46833, 42], [46892, 22], [46920, 19], [47133, 41], [47191, 23], [47220, 19], [47432, 42], [47491, 23], [47520, 19], [47732, 42], [47791, 23], [47819, 20], [48031, 43], [48091, 22], [48119, 21], [48331, 42], [48390, 23], [48419, 21], [48631, 42], [48690, 23], [48718, 22], [48930, 43], [48990, 23], [49018, 22], [49230, 43], [49290, 23], [49317, 23], [49529, 43], [49590, 23], [49617, 23], [49829, 43], [49889, 24], [49916, 24], [50128, 44], [50189, 11], [50203, 10], [50215, 25], [50428, 43], [50489, 1], [50497, 4], [50502, 11], [50514, 26], [50728, 23], [50792, 3], [50799, 41], [51027, 22], [51094, 3], [51100, 40], [51327, 22], [51368, 4], [51396, 3], [51401, 39], [51626, 24], [51657, 29], [51692, 2], [51697, 43], [51926, 61], [51993, 47], [52225, 115], [52525, 115], [52824, 116], [53124, 116], [53424, 116], [53723, 117], [54022, 118], [54322, 118], [54621, 119], [54922, 118], [55223, 118], [55523, 11], [55546, 95], [55824, 11], [55846, 95], [56124, 11], [56145, 96], [56425, 11], [56445, 96], [56725, 11], [56745, 92], [57026, 11], [57044, 93], [57326, 11], [57344, 93], [57627, 11], [57644, 93], [57928, 10], [57943, 94], [58228, 11], [58243, 94], [58529, 10], [58542, 95], [58829, 11], [58842, 95], [59130, 10], [59142, 95], [59430, 107], [59731, 106], [60032, 105], [60332, 105], [60633, 104], [60933, 104], [61234, 103], [61534, 102], [61835, 101], [62136, 9], [62158, 78], [62436, 10], [62457, 79], [62737, 9], [62757, 79], [63037, 10], [63057, 79], [63338, 9], [63356, 80], [63638, 10], [63656, 80], [63939, 10], [63956, 80], [64240, 9], [64256, 80], [64540, 10], [64555, 81], [64841, 9], [64855, 82], [65141, 10], [65155, 82], [65442, 9], [65454, 83], [65742, 10], [65754, 83], [66043, 9], [66054, 83], [66343, 10], [66354, 83], [66644, 93], [66945, 92], [67245, 92], [67546, 91], [67846, 91], [68147, 90], [68447, 90], [68748, 89], [69049, 8], [69131, 7], [69349, 9], [69431, 7], [69650, 8], [69731, 7], [69950, 9], [70032, 6], [70251, 8], [70332, 6], [70551, 9], [70632, 5], [70852, 8], [70932, 4], [71153, 8], [71232, 3], [71453, 8], [71532, 3], [71754, 8], [71832, 2], [72054, 8], [72132, 1], [72355, 8], [72432, 1], [72655, 8], [72956, 8], [73256, 9], [73557, 8], [73858, 8], [74158, 8], [74459, 7], [74759, 7], [75060, 5]], "point": [80, 182]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan28", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -1.25, "y": 0.900998235, "z": -0.25}, "object_poses": [{"objectName": "Potato_5e3edff7", "position": {"x": -3.94221377, "y": 0.7224759, "z": -0.248166}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_7e2c9e01", "position": {"x": -2.419992, "y": 0.09922422, "z": -3.538296}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_e676ac70", "position": {"x": -0.394597232, "y": 0.9413421, "z": -1.36285353}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_e676ac70", "position": {"x": -0.9638954, "y": 0.942342043, "z": -3.70289278}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Pot_c7418f98", "position": {"x": -3.92534733, "y": 0.761066, "z": -3.39901733}, "rotation": {"x": 0.0, "y": 244.125916, "z": 0.0}}, {"objectName": "Fork_938fe393", "position": {"x": -3.56550121, "y": 0.7606369, "z": -3.43287659}, "rotation": {"x": 0.0, "y": 64.1259155, "z": 0.0}}, {"objectName": "Fork_938fe393", "position": {"x": -4.06235027, "y": 0.691048563, "z": -0.491501927}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_7dcfa4cd", "position": {"x": -1.47139764, "y": 0.9434294, "z": -3.67361975}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_1d69618b", "position": {"x": -0.3156285, "y": 0.9562999, "z": -1.5381465}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_571582a1", "position": {"x": -4.24255562, "y": 0.7315668, "z": -0.430667937}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_41873c33", "position": {"x": -0.2012378, "y": 1.57615554, "z": -1.88466358}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Cup_41873c33", "position": {"x": -0.293651253, "y": 1.28730249, "z": -0.7623111}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Plate_ba5a8c1e", "position": {"x": -0.214446545, "y": 1.95197284, "z": -1.1088773}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_1b546504", "position": {"x": -2.76173234, "y": 0.9202391, "z": -0.462292016}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Knife_1b546504", "position": {"x": -2.96263933, "y": 0.9202391, "z": -0.376522064}, "rotation": {"x": 0.0, "y": 180.00032, "z": 0.0}}, {"objectName": "SoapBottle_0407be4c", "position": {"x": -3.32981253, "y": 0.7582775, "z": -3.421749}, "rotation": {"x": 0.0, "y": 109.125885, "z": 0.0}}, {"objectName": "Bowl_1f51c27f", "position": {"x": -0.376193523, "y": 1.28490734, "z": -0.413378}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Bowl_1f51c27f", "position": {"x": -3.16354656, "y": 0.892928839, "z": -0.376520932}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Bread_4d72833d", "position": {"x": -3.16354561, "y": 0.9760524, "z": -0.20498313}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Bread_4d72833d", "position": {"x": -3.56536078, "y": 0.9760524, "z": -0.290749818}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Lettuce_ec37c659", "position": {"x": -3.314754, "y": 0.8510728, "z": -3.24179721}, "rotation": {"x": 0.0, "y": 244.125916, "z": 0.0}}, {"objectName": "Lettuce_ec37c659", "position": {"x": -3.36445332, "y": 0.98560524, "z": -0.290750921}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Apple_4eadea0d", "position": {"x": -3.8821454, "y": 0.7519119, "z": -0.126498044}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_4eadea0d", "position": {"x": -4.06235027, "y": 0.7519119, "z": -0.309}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_0407be4c", "position": {"x": -0.5416963, "y": 0.0803504, "z": -1.5295893}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_c7418f98", "position": {"x": -0.2221, "y": 0.961999953, "z": -1.8457}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Fork_938fe393", "position": {"x": -0.7214115, "y": 0.8140154, "z": -3.49191141}, "rotation": {"x": 9.923132e-15, "y": 315.000031, "z": -9.92312e-15}}, {"objectName": "Cup_41873c33", "position": {"x": -2.560824, "y": 0.895324, "z": -0.2907554}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Tomato_b1760e7a", "position": {"x": -0.318482876, "y": 0.9930661, "z": -2.93781018}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Pan_d1250561", "position": {"x": -0.217, "y": 0.958, "z": -2.215}, "rotation": {"x": 0.0, "y": 44.9999161, "z": 0.0}}, {"objectName": "Plate_ba5a8c1e", "position": {"x": -0.3591778, "y": 1.50284219, "z": -0.9949338}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Bread_4d72833d", "position": {"x": -3.60372019, "y": 0.84152, "z": -3.65283442}, "rotation": {"x": 0.0, "y": 199.125946, "z": 0.0}}, {"objectName": "Spatula_1d69618b", "position": {"x": -3.58681631, "y": 0.775088251, "z": -2.841071}, "rotation": {"x": 0.0, "y": 199.125946, "z": 0.0}}, {"objectName": "Knife_1b546504", "position": {"x": -3.8821454, "y": 0.716118336, "z": -0.309}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SaltShaker_6f13b4f9", "position": {"x": -0.2535286, "y": 1.93414724, "z": -0.8963769}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_8ae9f292", "position": {"x": -1.15479994, "y": 0.749246538, "z": -3.476358}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_e676ac70", "position": {"x": -2.56082559, "y": 0.894662857, "z": -0.548061967}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Apple_4eadea0d", "position": {"x": -0.167795092, "y": 1.63686419, "z": -2.09674573}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "DishSponge_7e2c9e01", "position": {"x": -4.06235027, "y": 0.690860569, "z": -0.4306679}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5e3edff7", "position": {"x": -3.50831437, "y": 0.79206425, "z": -3.19675}, "rotation": {"x": 0.0, "y": 244.125916, "z": 0.0}}, {"objectName": "Egg_571582a1", "position": {"x": -2.761731, "y": 0.9356876, "z": -0.204985365}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Spoon_7dcfa4cd", "position": {"x": -0.740141332, "y": 0.8147884, "z": -3.58881855}, "rotation": {"x": 9.92312057e-15, "y": 45.0000343, "z": 9.923131e-15}}, {"objectName": "Lettuce_ec37c659", "position": {"x": -0.321308225, "y": 1.57969487, "z": -0.413377553}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Mug_f3c7368a", "position": {"x": -1.24855721, "y": 0.9373, "z": -3.439128}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_1f51c27f", "position": {"x": -0.3213089, "y": 1.48701847, "z": -0.646000147}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}], "object_toggles": [], "random_seed": 1310886628, "scene_num": 28}, "task_id": "trial_T20190908_115525_985140", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2A028LRDJB7ZB_3F6KKYWMNESUO87BXVLXAWXNA8HDNB", "high_descs": ["Walk straight then turn right head to the shelf on the left side of the table ", "Pick up the egg on the top of the shelf", "Turn to your right cross the room and head to the microwave", "Open the microwave and put in the egg then wait get it back and close the microwave", "Turn left walk forward then turn right on the shelf", "Put the egg on the shelf beside the sponge"], "task_desc": "Put the heated egg on the shelf", "votes": [1, 1]}, {"assignment_id": "A24RGLS3BRZ60J_3G5F9DBFOSOO8TLMLIQEBY6YO3EHV7", "high_descs": ["Turn to the left and then right to face the small black table on the right.", "Pick up the egg from the left side of the black table.", "Carry the egg and turn around. Turn left and go to the stove.", "Place the egg in the microwave and shut the door. Open the door and take out the egg, then shut the door.", "Carry the egg and turn around and then right to the black table.", "Place the egg on the black table."], "task_desc": "Heat an egg and place it on the table.", "votes": [1, 1]}, {"assignment_id": "A3PPRVK6XK6GP5_3TY7ZAOG5IBZHILEO65M3XW0MOO0KL", "high_descs": ["Turn left and step forward, then take a right and walk along the white table. At the wall turn right to face the black stand.", "Grab the brown egg from the left of the fork.", "Turn around and take a few steps toward the round table. Turn left and walk to the stove across the room.", "Open the microwave, place the egg in the center next to the apple, run the machine then open the door and remove the egg.", "Turn around and go back across the room, towards the door, turn right to face the black stand.", "Return the egg to the stand, to the left of the fork.."], "task_desc": "Heat an egg in the microwave.", "votes": [1, 1]}]}}