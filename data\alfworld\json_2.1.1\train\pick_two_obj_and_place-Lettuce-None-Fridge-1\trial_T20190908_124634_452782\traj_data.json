{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000187.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000188.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000189.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000190.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000191.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000192.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000193.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000194.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 54}, {"high_idx": 7, "image_name": "000000261.png", "low_idx": 55}, {"high_idx": 7, "image_name": "000000262.png", "low_idx": 55}, {"high_idx": 7, "image_name": "000000263.png", "low_idx": 55}, {"high_idx": 7, "image_name": "000000264.png", "low_idx": 55}, {"high_idx": 7, "image_name": "000000265.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000266.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000267.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000268.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000269.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000270.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000271.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000272.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000273.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000274.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000275.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000277.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000278.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000279.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000280.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000281.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000282.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 57}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Lettuce", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-5|-2|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["lettuce"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Lettuce", [-7.42509748, -7.42509748, -2.05200386, -2.05200386, 3.96409, 3.96409]], "coordinateReceptacleObjectId": ["CounterTop", [-7.472, -7.472, -4.824, -4.824, 3.7876, 3.7876]], "forceVisible": true, "objectId": "Lettuce|-01.86|+00.99|-00.51"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-5|4|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["lettuce", "fridge"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Lettuce", [-7.42509748, -7.42509748, -2.05200386, -2.05200386, 3.96409, 3.96409]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-8.388, -8.388, 4.352, 4.352, 0.0, 0.0]], "forceVisible": true, "objectId": "Lettuce|-01.86|+00.99|-00.51", "receptacleObjectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-5|-7|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["lettuce"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Lettuce", [-5.75370504, -5.75370504, -9.31930924, -9.31930924, 3.964089872, 3.964089872]], "coordinateReceptacleObjectId": ["CounterTop", [-7.472, -7.472, -4.824, -4.824, 3.7876, 3.7876]], "forceVisible": true, "objectId": "Lettuce|-01.44|+00.99|-02.33"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-5|4|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["lettuce", "fridge"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Lettuce", [-5.75370504, -5.75370504, -9.31930924, -9.31930924, 3.964089872, 3.964089872]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-8.388, -8.388, 4.352, 4.352, 0.0, 0.0]], "forceVisible": true, "objectId": "Lettuce|-01.44|+00.99|-02.33", "receptacleObjectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Lettuce|-01.86|+00.99|-00.51"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [108, 129, 182, 174], "mask": [[38522, 17], [38820, 26], [39118, 31], [39416, 36], [39715, 41], [40014, 46], [40313, 50], [40612, 54], [40911, 57], [41211, 58], [41510, 61], [41810, 63], [42110, 66], [42409, 69], [42709, 70], [43009, 72], [43308, 74], [43608, 74], [43908, 74], [44208, 75], [44508, 75], [44808, 75], [45108, 75], [45408, 75], [45708, 74], [46008, 74], [46309, 73], [46609, 72], [46909, 70], [47209, 69], [47509, 67], [47809, 65], [48110, 62], [48410, 60], [48710, 59], [49011, 56], [49311, 55], [49612, 52], [49913, 49], [50214, 46], [50515, 42], [50817, 38], [51118, 34], [51420, 28], [51722, 21], [52027, 6]], "point": [145, 150]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 222], "mask": [[0, 17100], [17101, 299], [17402, 298], [17707, 293], [18008, 292], [18308, 292], [18609, 291], [18909, 291], [19210, 290], [19511, 289], [19811, 289], [20112, 288], [20412, 288], [20713, 287], [21014, 286], [21314, 286], [21615, 285], [21915, 285], [22216, 284], [22517, 283], [22817, 283], [23118, 282], [23418, 282], [23719, 281], [24020, 280], [24320, 280], [24621, 279], [24921, 279], [25222, 278], [25523, 277], [25823, 277], [26124, 276], [26424, 276], [26725, 275], [27025, 275], [27326, 274], [27627, 273], [27927, 273], [28228, 272], [28528, 272], [28829, 271], [29130, 270], [29430, 270], [29731, 269], [30031, 269], [30332, 268], [30633, 267], [30933, 267], [31234, 266], [31534, 266], [31835, 265], [32136, 264], [32436, 264], [32737, 263], [33037, 263], [33338, 262], [33639, 261], [33939, 261], [34240, 260], [34540, 260], [34841, 259], [35142, 258], [35442, 258], [35743, 257], [36043, 257], [36344, 256], [36645, 255], [36945, 255], [37246, 254], [37546, 254], [37847, 253], [38147, 253], [38448, 252], [38749, 251], [39049, 251], [39350, 250], [39650, 250], [39951, 249], [40252, 248], [40552, 248], [40853, 247], [41153, 247], [41454, 246], [41755, 245], [42055, 245], [42356, 244], [42656, 244], [42957, 243], [43258, 242], [43558, 242], [43859, 241], [44159, 241], [44460, 239], [44761, 237], [45061, 236], [45362, 234], [45662, 233], [45963, 231], [46264, 229], [46564, 228], [46865, 226], [47165, 225], [47466, 223], [47767, 221], [48067, 220], [48368, 218], [48668, 217], [48969, 215], [49270, 213], [49570, 212], [49871, 210], [50171, 209], [50472, 207], [50772, 206], [51073, 204], [51374, 202], [51674, 201], [51975, 199], [52275, 198], [52576, 196], [52877, 194], [53177, 193], [53478, 191], [53778, 190], [54079, 189], [54380, 187], [54680, 186], [54981, 184], [55281, 183], [55582, 181], [55883, 179], [56183, 178], [56484, 61], [56554, 106], [56784, 59], [56856, 103], [57085, 56], [57158, 100], [57386, 54], [57459, 98], [57686, 53], [57760, 96], [57987, 51], [58061, 94], [58287, 50], [58362, 92], [58588, 49], [58662, 91], [58889, 47], [58963, 89], [59189, 46], [59264, 87], [59489, 46], [59565, 85], [59789, 45], [59865, 84], [60089, 44], [60166, 82], [60389, 44], [60467, 80], [60689, 43], [60767, 79], [60989, 43], [61068, 77], [61289, 42], [61369, 75], [61588, 42], [61670, 73], [61888, 42], [61970, 72], [62188, 41], [62271, 70], [62489, 39], [62572, 69], [62790, 38], [62872, 68], [63091, 36], [63173, 66], [63391, 35], [63474, 64], [63693, 33], [63774, 62], [63996, 29], [64075, 57], [64300, 25], [64375, 54], [64603, 21], [64676, 50], [64907, 16], [64977, 45], [65212, 11], [65277, 40], [65518, 4], [65578, 33], [65878, 27], [66179, 18], [66479, 9]], "point": [149, 110]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Lettuce|-01.86|+00.99|-00.51", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 16099], [16125, 2], [16130, 269], [16424, 3], [16430, 269], [16724, 2], [16730, 268], [17024, 1], [17029, 269], [17323, 1], [17328, 270], [17628, 270], [17927, 271], [18226, 271], [18525, 272], [18823, 274], [19122, 275], [19421, 276], [19721, 276], [20021, 276], [20320, 278], [20620, 278], [20920, 279], [21219, 281], [21518, 284], [21817, 287], [22115, 293], [22411, 5038], [27450, 2], [27455, 291], [27747, 1], [27750, 2], [27756, 290], [28050, 2], [28056, 286], [28343, 3], [28350, 2], [28356, 285], [28643, 3], [28650, 2], [28656, 285], [28943, 3], [28950, 2], [28956, 3], [28960, 280], [29243, 3], [29250, 2], [29257, 2], [29260, 280], [29543, 3], [29550, 2], [29557, 2], [29561, 279], [29843, 3], [29850, 2], [29857, 2], [29861, 279], [30143, 3], [30150, 2], [30157, 2], [30161, 1780], [31959, 283], [32258, 285], [32557, 287], [32856, 290], [33154, 1645], [34800, 298], [35100, 297], [35400, 297], [35700, 296], [36000, 295], [36300, 294], [36600, 293], [36900, 293], [37200, 292], [37500, 291], [37800, 290], [38100, 289], [38400, 288], [38700, 288], [39000, 287], [39300, 286], [39600, 285], [39900, 284], [40200, 284], [40500, 283], [40800, 282], [41100, 281], [41400, 280], [41700, 280], [42000, 279], [42300, 204], [42513, 65], [42600, 203], [42814, 63], [42900, 202], [43116, 60], [43200, 201], [43416, 60], [43500, 201], [43717, 58], [43800, 200], [44017, 57], [44100, 200], [44317, 56], [44400, 200], [44617, 55], [44700, 200], [44917, 55], [45000, 200], [45217, 54], [45300, 200], [45517, 53], [45600, 200], [45816, 53], [45900, 201], [46116, 52], [46200, 201], [46415, 52], [46500, 202], [46714, 53], [46800, 203], [47013, 53], [47100, 205], [47311, 54], [47400, 264], [47700, 263], [48000, 263], [48300, 262], [48600, 261], [48900, 260], [49200, 259], [49500, 259], [49800, 258], [50100, 257], [50400, 256], [50700, 255], [51000, 255], [51300, 254], [51600, 253], [51900, 252], [52200, 251], [52500, 251], [52800, 250], [53100, 249], [53400, 248], [53700, 247], [54000, 246], [54300, 246], [54600, 245], [54900, 244], [55200, 243], [55500, 242], [55800, 242], [56100, 241], [56400, 145], [56554, 86], [56700, 143], [56856, 83], [57000, 141], [57158, 81], [57300, 140], [57459, 80], [57600, 139], [57760, 79], [57900, 138], [58061, 78], [58200, 137], [58362, 77], [58500, 137], [58662, 78], [58800, 136], [58963, 77], [59100, 135], [59264, 76], [59400, 135], [59565, 75], [59700, 134], [59865, 75], [60000, 133], [60166, 74], [60300, 133], [60467, 74], [60600, 132], [60767, 74], [60900, 132], [61068, 73], [61200, 131], [61369, 72], [61500, 130], [61670, 71], [61800, 130], [61970, 72], [62100, 129], [62271, 70], [62400, 128], [62572, 69], [62700, 89], [62790, 38], [62872, 68], [63000, 89], [63091, 36], [63173, 66], [63300, 89], [63391, 35], [63474, 64], [63600, 88], [63693, 33], [63774, 62], [63900, 88], [63996, 29], [64075, 57], [64200, 88], [64300, 25], [64375, 54], [64500, 88], [64603, 21], [64676, 50], [64800, 88], [64907, 16], [64977, 45], [65100, 88], [65212, 11], [65277, 40], [65400, 88], [65518, 4], [65578, 33], [65700, 88], [65878, 27], [66000, 87], [66179, 18], [66300, 87], [66479, 9], [66600, 87], [66900, 87], [67200, 87], [67500, 87], [67800, 87], [68100, 87], [68400, 86], [68700, 86], [69000, 86], [69300, 86], [69600, 86], [69900, 86], [70200, 86], [70500, 86], [70800, 85], [71100, 85], [71400, 85], [71700, 85], [72000, 85], [72300, 85], [72600, 85], [72900, 85], [73200, 84], [73500, 84], [73800, 84], [74100, 84], [74400, 84], [74700, 84], [75000, 84], [75300, 84], [75600, 83], [75900, 83], [76200, 83], [76500, 83], [76800, 83], [77100, 83], [77400, 83], [77700, 82], [78000, 82], [78300, 82], [78600, 82], [78900, 82], [79200, 82], [79500, 82], [79800, 82], [80100, 81], [80400, 81], [80700, 81], [81000, 81], [81300, 81], [81600, 81], [81900, 81], [82200, 81], [82500, 80], [82800, 80], [83100, 80], [83400, 80], [83700, 80], [84000, 80], [84300, 80], [84600, 80], [84900, 79], [85200, 79], [85500, 79], [85800, 79], [86100, 79], [86400, 79], [86700, 79], [87000, 79], [87300, 78], [87600, 78], [87900, 78], [88200, 78], [88500, 78], [88800, 78], [89100, 78], [89400, 78], [89700, 77]], "point": [149, 149]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 16033], [16068, 31], [16125, 2], [16130, 203], [16367, 32], [16424, 3], [16430, 204], [16667, 32], [16724, 2], [16730, 204], [16967, 31], [17024, 1], [17029, 205], [17266, 32], [17323, 1], [17328, 206], [17566, 32], [17628, 206], [17865, 33], [17927, 207], [18165, 33], [18226, 208], [18465, 32], [18525, 210], [18764, 33], [18823, 212], [19064, 33], [19122, 213], [19364, 33], [19421, 215], [19663, 34], [19721, 215], [19963, 34], [20021, 216], [20262, 35], [20320, 217], [20562, 36], [20620, 218], [20861, 37], [20920, 219], [21161, 38], [21219, 220], [21460, 40], [21518, 222], [21759, 43], [21817, 224], [22059, 45], [22115, 227], [22358, 50], [22411, 232], [22658, 286], [22957, 288], [23257, 288], [23557, 289], [23856, 291], [24155, 294], [24454, 2995], [27450, 2], [27455, 291], [27747, 1], [27750, 2], [27756, 290], [28050, 2], [28056, 286], [28343, 3], [28350, 2], [28356, 285], [28643, 3], [28650, 2], [28656, 285], [28943, 3], [28950, 2], [28956, 3], [28960, 280], [29243, 3], [29250, 2], [29257, 2], [29260, 280], [29543, 3], [29550, 2], [29557, 2], [29561, 279], [29843, 3], [29850, 2], [29857, 2], [29861, 279], [30143, 3], [30150, 2], [30157, 2], [30161, 1780], [31959, 283], [32258, 285], [32557, 287], [32856, 290], [33154, 1645], [34800, 298], [35100, 297], [35400, 297], [35700, 296], [36000, 295], [36300, 294], [36600, 293], [36900, 293], [37200, 292], [37500, 291], [37800, 290], [38100, 289], [38400, 288], [38700, 288], [39000, 287], [39300, 286], [39600, 285], [39900, 284], [40200, 284], [40500, 283], [40800, 282], [41100, 281], [41400, 280], [41700, 280], [42000, 279], [42300, 204], [42513, 65], [42600, 203], [42814, 63], [42900, 202], [43116, 60], [43200, 201], [43416, 60], [43500, 201], [43717, 58], [43800, 200], [44017, 57], [44100, 200], [44317, 56], [44400, 200], [44617, 55], [44700, 200], [44917, 55], [45000, 200], [45217, 54], [45300, 200], [45517, 53], [45600, 200], [45816, 53], [45900, 201], [46116, 52], [46200, 201], [46415, 52], [46500, 202], [46714, 53], [46800, 203], [47013, 53], [47100, 205], [47311, 54], [47400, 264], [47700, 263], [48000, 263], [48300, 262], [48600, 261], [48900, 260], [49200, 259], [49500, 259], [49800, 258], [50100, 257], [50400, 256], [50700, 255], [51000, 255], [51300, 254], [51600, 253], [51900, 252], [52200, 251], [52500, 251], [52800, 250], [53100, 249], [53400, 248], [53700, 247], [54000, 246], [54300, 246], [54600, 245], [54900, 244], [55200, 243], [55500, 242], [55800, 242], [56100, 241], [56400, 240], [56700, 239], [57000, 239], [57300, 239], [57600, 239], [57900, 239], [58200, 239], [58500, 240], [58800, 240], [59100, 240], [59400, 240], [59700, 240], [60000, 240], [60300, 241], [60600, 241], [60900, 241], [61200, 241], [61500, 241], [61800, 242], [62100, 241], [62400, 241], [62700, 89], [62790, 150], [63000, 89], [63091, 148], [63300, 89], [63391, 147], [63600, 88], [63693, 143], [63900, 88], [63996, 136], [64200, 88], [64300, 129], [64500, 88], [64603, 123], [64800, 88], [64907, 115], [65100, 88], [65212, 105], [65400, 88], [65518, 93], [65700, 88], [65824, 81], [66000, 87], [66132, 65], [66300, 87], [66442, 46], [66600, 87], [66900, 87], [67200, 87], [67500, 87], [67800, 87], [68100, 87], [68400, 86], [68700, 86], [69000, 86], [69300, 86], [69600, 86], [69900, 86], [70200, 86], [70500, 86], [70800, 85], [71100, 85], [71400, 85], [71700, 85], [72000, 85], [72300, 85], [72600, 85], [72900, 85], [73200, 84], [73500, 84], [73800, 84], [74100, 84], [74400, 84], [74700, 84], [75000, 84], [75300, 84], [75600, 83], [75900, 83], [76200, 83], [76500, 83], [76800, 83], [77100, 83], [77400, 83], [77700, 82], [78000, 82], [78300, 82], [78600, 82], [78900, 82], [79200, 82], [79500, 82], [79800, 82], [80100, 81], [80400, 81], [80700, 81], [81000, 81], [81300, 81], [81600, 81], [81900, 81], [82200, 81], [82500, 80], [82800, 80], [83100, 80], [83400, 80], [83700, 80], [84000, 80], [84300, 80], [84600, 80], [84900, 79], [85200, 79], [85500, 79], [85800, 79], [86100, 79], [86400, 79], [86700, 79], [87000, 79], [87300, 78], [87600, 78], [87900, 78], [88200, 78], [88500, 78], [88800, 78], [89100, 78], [89400, 78], [89700, 77]], "point": [149, 149]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Lettuce|-01.44|+00.99|-02.33"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [184, 122, 227, 188], "mask": [[36502, 8], [36799, 14], [37096, 19], [37395, 22], [37693, 26], [37992, 28], [38291, 31], [38590, 33], [38890, 33], [39189, 35], [39488, 36], [39788, 37], [40087, 38], [40387, 39], [40686, 40], [40986, 40], [41286, 41], [41586, 41], [41886, 41], [42186, 41], [42485, 42], [42785, 42], [43085, 42], [43384, 44], [43684, 44], [43984, 44], [44284, 44], [44584, 44], [44884, 44], [45184, 44], [45484, 44], [45784, 44], [46085, 43], [46385, 43], [46685, 42], [46985, 42], [47286, 41], [47586, 41], [47886, 42], [48187, 41], [48487, 41], [48788, 40], [49088, 40], [49389, 39], [49689, 38], [49990, 37], [50291, 36], [50591, 36], [50892, 34], [51193, 33], [51493, 33], [51794, 31], [52095, 30], [52396, 29], [52696, 28], [52997, 27], [53299, 25], [53600, 23], [53901, 22], [54202, 21], [54503, 20], [54805, 17], [55106, 16], [55407, 14], [55708, 13], [56009, 11], [56310, 8]], "point": [205, 154]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 222], "mask": [[0, 17100], [17101, 299], [17402, 298], [17707, 293], [18008, 292], [18308, 292], [18609, 291], [18909, 291], [19210, 290], [19511, 289], [19811, 289], [20112, 288], [20412, 288], [20713, 287], [21014, 286], [21314, 286], [21615, 285], [21915, 285], [22216, 284], [22517, 283], [22817, 283], [23118, 282], [23418, 282], [23719, 281], [24020, 280], [24320, 280], [24621, 279], [24921, 279], [25222, 278], [25523, 277], [25823, 277], [26124, 276], [26424, 276], [26725, 275], [27025, 275], [27326, 274], [27627, 273], [27927, 273], [28228, 272], [28528, 272], [28829, 271], [29130, 270], [29430, 270], [29731, 269], [30031, 269], [30332, 268], [30633, 267], [30933, 267], [31234, 266], [31534, 266], [31835, 265], [32136, 264], [32436, 264], [32737, 263], [33037, 263], [33338, 262], [33639, 261], [33939, 261], [34240, 260], [34540, 260], [34841, 259], [35142, 258], [35442, 258], [35743, 257], [36043, 257], [36344, 256], [36645, 255], [36945, 255], [37246, 254], [37546, 254], [37847, 253], [38147, 253], [38448, 252], [38749, 251], [39049, 251], [39350, 250], [39650, 250], [39951, 249], [40252, 248], [40552, 248], [40853, 247], [41153, 247], [41454, 246], [41755, 245], [42055, 245], [42356, 244], [42656, 244], [42957, 243], [43258, 242], [43558, 242], [43859, 241], [44159, 241], [44460, 239], [44761, 237], [45061, 236], [45362, 234], [45662, 233], [45963, 231], [46264, 229], [46564, 228], [46865, 226], [47165, 225], [47466, 223], [47767, 221], [48067, 220], [48368, 218], [48668, 217], [48969, 215], [49270, 213], [49570, 212], [49871, 210], [50171, 209], [50472, 207], [50772, 206], [51073, 204], [51374, 202], [51674, 201], [51975, 199], [52275, 198], [52576, 196], [52877, 194], [53177, 193], [53478, 191], [53778, 190], [54079, 189], [54380, 187], [54680, 186], [54981, 184], [55281, 183], [55582, 181], [55883, 179], [56183, 178], [56484, 61], [56554, 106], [56784, 59], [56856, 103], [57085, 56], [57158, 100], [57386, 54], [57459, 98], [57686, 53], [57760, 96], [57987, 51], [58061, 94], [58287, 50], [58362, 92], [58588, 49], [58662, 91], [58889, 47], [58963, 89], [59189, 46], [59264, 87], [59489, 46], [59565, 85], [59789, 45], [59865, 84], [60089, 44], [60166, 82], [60389, 44], [60467, 80], [60689, 43], [60767, 79], [60989, 43], [61068, 77], [61289, 42], [61369, 75], [61588, 42], [61670, 73], [61888, 42], [61970, 72], [62188, 41], [62271, 70], [62489, 39], [62572, 69], [62790, 38], [62872, 68], [63091, 36], [63173, 66], [63391, 35], [63474, 64], [63693, 33], [63774, 62], [63996, 29], [64075, 57], [64300, 25], [64375, 54], [64603, 21], [64676, 50], [64907, 16], [64977, 45], [65212, 11], [65277, 40], [65518, 4], [65578, 33], [65878, 27], [66179, 18], [66479, 9]], "point": [149, 110]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Lettuce|-01.44|+00.99|-02.33", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 16033], [16068, 31], [16125, 2], [16130, 203], [16367, 32], [16424, 3], [16430, 204], [16667, 32], [16724, 2], [16730, 204], [16967, 31], [17024, 1], [17029, 205], [17266, 32], [17323, 1], [17328, 206], [17566, 32], [17628, 206], [17865, 33], [17927, 207], [18165, 33], [18226, 208], [18465, 32], [18525, 210], [18764, 33], [18823, 212], [19064, 33], [19122, 213], [19364, 33], [19421, 215], [19663, 34], [19721, 215], [19963, 34], [20021, 216], [20262, 35], [20320, 217], [20562, 36], [20620, 218], [20861, 37], [20920, 219], [21161, 38], [21219, 220], [21460, 40], [21518, 222], [21759, 43], [21817, 224], [22059, 45], [22115, 227], [22358, 50], [22411, 232], [22658, 286], [22957, 288], [23257, 288], [23557, 289], [23856, 291], [24155, 294], [24454, 2995], [27450, 2], [27455, 291], [27747, 1], [27750, 2], [27756, 290], [28050, 2], [28056, 286], [28343, 3], [28350, 2], [28356, 285], [28643, 3], [28650, 2], [28656, 285], [28943, 3], [28950, 2], [28956, 3], [28960, 280], [29243, 3], [29250, 2], [29257, 2], [29260, 280], [29543, 3], [29550, 2], [29557, 2], [29561, 279], [29843, 3], [29850, 2], [29857, 2], [29861, 279], [30143, 3], [30150, 2], [30157, 2], [30161, 1780], [31959, 283], [32258, 285], [32557, 287], [32856, 290], [33154, 1645], [34800, 298], [35100, 297], [35400, 297], [35700, 296], [36000, 295], [36300, 294], [36600, 293], [36900, 293], [37200, 292], [37500, 291], [37800, 290], [38100, 289], [38400, 288], [38700, 288], [39000, 287], [39300, 286], [39600, 285], [39900, 284], [40200, 284], [40500, 283], [40800, 282], [41100, 281], [41400, 280], [41700, 280], [42000, 279], [42300, 204], [42513, 65], [42600, 203], [42814, 63], [42900, 202], [43116, 60], [43200, 201], [43416, 60], [43500, 201], [43717, 58], [43800, 200], [44017, 57], [44100, 200], [44317, 56], [44400, 200], [44617, 55], [44700, 200], [44917, 55], [45000, 200], [45217, 54], [45300, 200], [45517, 53], [45600, 200], [45816, 53], [45900, 201], [46116, 52], [46200, 201], [46415, 52], [46500, 202], [46714, 53], [46800, 203], [47013, 53], [47100, 205], [47311, 54], [47400, 264], [47700, 263], [48000, 263], [48300, 262], [48600, 261], [48900, 260], [49200, 259], [49500, 259], [49800, 258], [50100, 257], [50400, 256], [50700, 255], [51000, 255], [51300, 254], [51600, 253], [51900, 252], [52200, 251], [52500, 251], [52800, 250], [53100, 249], [53400, 248], [53700, 247], [54000, 246], [54300, 246], [54600, 245], [54900, 244], [55200, 243], [55500, 242], [55800, 242], [56100, 241], [56400, 145], [56554, 86], [56700, 143], [56856, 83], [57000, 141], [57158, 81], [57300, 140], [57459, 80], [57600, 139], [57760, 79], [57900, 138], [58061, 78], [58200, 137], [58362, 77], [58500, 137], [58662, 78], [58800, 136], [58963, 77], [59100, 135], [59264, 76], [59400, 135], [59565, 75], [59700, 134], [59865, 75], [60000, 133], [60166, 74], [60300, 133], [60467, 74], [60600, 132], [60767, 74], [60900, 132], [61068, 73], [61200, 131], [61369, 72], [61500, 130], [61670, 71], [61800, 130], [61970, 72], [62100, 129], [62271, 70], [62400, 128], [62572, 69], [62700, 89], [62790, 38], [62872, 68], [63000, 89], [63091, 36], [63173, 66], [63300, 89], [63391, 35], [63474, 64], [63600, 88], [63693, 33], [63774, 62], [63900, 88], [63996, 29], [64075, 57], [64200, 88], [64300, 25], [64375, 54], [64500, 88], [64603, 21], [64676, 50], [64800, 88], [64907, 16], [64977, 45], [65100, 88], [65212, 11], [65277, 40], [65400, 88], [65518, 4], [65578, 33], [65700, 88], [65878, 27], [66000, 87], [66179, 18], [66300, 87], [66479, 9], [66600, 87], [66900, 87], [67200, 87], [67500, 87], [67800, 87], [68100, 87], [68400, 86], [68700, 86], [69000, 86], [69300, 86], [69600, 86], [69900, 86], [70200, 86], [70500, 86], [70800, 85], [71100, 85], [71400, 85], [71700, 85], [72000, 85], [72300, 85], [72600, 85], [72900, 85], [73200, 84], [73500, 84], [73800, 84], [74100, 84], [74400, 84], [74700, 84], [75000, 84], [75300, 84], [75600, 83], [75900, 83], [76200, 83], [76500, 83], [76800, 83], [77100, 83], [77400, 83], [77700, 82], [78000, 82], [78300, 82], [78600, 82], [78900, 82], [79200, 82], [79500, 82], [79800, 82], [80100, 81], [80400, 81], [80700, 81], [81000, 81], [81300, 81], [81600, 81], [81900, 81], [82200, 81], [82500, 80], [82800, 80], [83100, 80], [83400, 80], [83700, 80], [84000, 80], [84300, 80], [84600, 80], [84900, 79], [85200, 79], [85500, 79], [85800, 79], [86100, 79], [86400, 79], [86700, 79], [87000, 79], [87300, 78], [87600, 78], [87900, 78], [88200, 78], [88500, 78], [88800, 78], [89100, 78], [89400, 78], [89700, 77]], "point": [149, 149]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 16033], [16068, 31], [16125, 2], [16130, 203], [16367, 32], [16424, 3], [16430, 204], [16667, 32], [16724, 2], [16730, 204], [16967, 31], [17024, 1], [17029, 205], [17266, 32], [17323, 1], [17328, 206], [17566, 32], [17628, 206], [17865, 33], [17927, 207], [18165, 33], [18226, 208], [18465, 32], [18525, 210], [18764, 33], [18823, 212], [19064, 33], [19122, 213], [19364, 33], [19421, 215], [19663, 34], [19721, 215], [19963, 34], [20021, 216], [20262, 35], [20320, 217], [20562, 36], [20620, 218], [20861, 37], [20920, 219], [21161, 38], [21219, 220], [21460, 40], [21518, 222], [21759, 43], [21817, 224], [22059, 45], [22115, 227], [22358, 50], [22411, 232], [22658, 24], [22683, 3], [22687, 257], [22957, 22], [22983, 3], [22989, 256], [23257, 22], [23283, 3], [23290, 255], [23557, 22], [23583, 3], [23590, 2], [23594, 252], [23856, 19], [23876, 3], [23883, 3], [23890, 2], [23895, 252], [24155, 19], [24176, 3], [24183, 3], [24190, 3], [24196, 253], [24454, 19], [24476, 3], [24483, 3], [24490, 3], [24497, 276], [24776, 3], [24784, 2], [24790, 3], [24797, 276], [25076, 3], [25084, 2], [25090, 3], [25097, 276], [25377, 2], [25384, 3], [25390, 3], [25397, 276], [25677, 2], [25684, 3], [25690, 3], [25697, 276], [25977, 2], [25984, 3], [25990, 3], [25997, 276], [26277, 3], [26284, 3], [26291, 2], [26297, 276], [26577, 3], [26584, 3], [26591, 2], [26597, 276], [26877, 3], [26884, 3], [26891, 3], [26898, 275], [27177, 3], [27184, 3], [27191, 3], [27198, 251], [27450, 2], [27455, 18], [27477, 3], [27484, 3], [27491, 3], [27498, 248], [27747, 1], [27750, 2], [27756, 17], [27777, 3], [27784, 3], [27791, 3], [27798, 248], [28050, 2], [28056, 13], [28070, 3], [28077, 3], [28085, 2], [28091, 3], [28098, 244], [28343, 3], [28350, 2], [28356, 13], [28370, 3], [28377, 3], [28385, 2], [28391, 3], [28398, 243], [28643, 3], [28650, 2], [28656, 13], [28670, 3], [28677, 3], [28685, 3], [28691, 3], [28698, 243], [28943, 3], [28950, 2], [28956, 3], [28960, 9], [28970, 3], [28977, 3], [28985, 3], [28991, 3], [28998, 242], [29243, 3], [29250, 2], [29257, 2], [29260, 13], [29277, 3], [29285, 3], [29292, 2], [29298, 242], [29543, 3], [29550, 2], [29557, 2], [29561, 12], [29577, 3], [29585, 3], [29592, 3], [29598, 242], [29843, 3], [29850, 2], [29857, 2], [29861, 9], [29871, 2], [29878, 2], [29885, 3], [29892, 3], [29898, 242], [30143, 3], [30150, 2], [30157, 2], [30161, 9], [30171, 3], [30178, 3], [30185, 3], [30192, 3], [30198, 1743], [31959, 13], [31997, 245], [32258, 15], [32296, 247], [32557, 16], [32596, 248], [32856, 18], [32896, 250], [33154, 21], [33195, 280], [33495, 281], [33794, 283], [34094, 283], [34393, 285], [34693, 106], [34800, 179], [34993, 105], [35100, 180], [35292, 105], [35400, 181], [35592, 105], [35700, 182], [35891, 105], [36000, 183], [36191, 104], [36300, 184], [36489, 105], [36600, 293], [36900, 293], [37200, 292], [37500, 291], [37800, 290], [38100, 289], [38400, 288], [38700, 288], [39000, 287], [39300, 286], [39600, 285], [39900, 284], [40200, 284], [40500, 283], [40800, 282], [41100, 281], [41400, 280], [41700, 280], [42000, 279], [42300, 204], [42513, 65], [42600, 203], [42814, 63], [42900, 202], [43116, 60], [43200, 201], [43416, 60], [43500, 201], [43717, 58], [43800, 200], [44017, 57], [44100, 200], [44317, 56], [44400, 200], [44617, 55], [44700, 200], [44917, 55], [45000, 200], [45217, 54], [45300, 200], [45517, 53], [45600, 200], [45816, 53], [45900, 201], [46116, 52], [46200, 201], [46415, 52], [46500, 202], [46714, 53], [46800, 203], [47013, 53], [47100, 205], [47311, 54], [47400, 264], [47700, 263], [48000, 263], [48300, 262], [48600, 261], [48900, 260], [49200, 259], [49500, 259], [49800, 258], [50100, 257], [50400, 256], [50700, 255], [51000, 255], [51300, 254], [51600, 253], [51900, 252], [52200, 251], [52500, 251], [52800, 250], [53100, 249], [53400, 248], [53700, 247], [54000, 246], [54300, 246], [54600, 245], [54900, 244], [55200, 243], [55500, 242], [55800, 242], [56100, 241], [56400, 240], [56700, 239], [57000, 239], [57300, 239], [57600, 239], [57900, 239], [58200, 239], [58500, 240], [58800, 240], [59100, 240], [59400, 240], [59700, 240], [60000, 240], [60300, 241], [60600, 241], [60900, 241], [61200, 241], [61500, 241], [61800, 242], [62100, 241], [62400, 241], [62700, 89], [62790, 150], [63000, 89], [63091, 148], [63300, 89], [63391, 147], [63600, 88], [63693, 143], [63900, 88], [63996, 136], [64200, 88], [64300, 129], [64500, 88], [64603, 123], [64800, 88], [64907, 115], [65100, 88], [65212, 105], [65400, 88], [65518, 93], [65700, 88], [65824, 81], [66000, 87], [66132, 65], [66300, 87], [66442, 46], [66600, 87], [66900, 87], [67200, 87], [67500, 87], [67800, 87], [68100, 87], [68400, 86], [68700, 86], [69000, 86], [69300, 86], [69600, 86], [69900, 86], [70200, 86], [70500, 86], [70800, 85], [71100, 85], [71400, 85], [71700, 85], [72000, 85], [72300, 85], [72600, 85], [72900, 85], [73200, 84], [73500, 84], [73800, 84], [74100, 84], [74400, 84], [74700, 84], [75000, 84], [75300, 84], [75600, 83], [75900, 83], [76200, 83], [76500, 83], [76800, 83], [77100, 83], [77400, 83], [77700, 82], [78000, 82], [78300, 82], [78600, 82], [78900, 82], [79200, 82], [79500, 82], [79800, 82], [80100, 81], [80400, 81], [80700, 81], [81000, 81], [81300, 81], [81600, 81], [81900, 81], [82200, 81], [82500, 80], [82800, 80], [83100, 80], [83400, 80], [83700, 80], [84000, 80], [84300, 80], [84600, 80], [84900, 79], [85200, 79], [85500, 79], [85800, 79], [86100, 79], [86400, 79], [86700, 79], [87000, 79], [87300, 78], [87600, 78], [87900, 78], [88200, 78], [88500, 78], [88800, 78], [89100, 78], [89400, 78], [89700, 77]], "point": [149, 149]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan1", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -1.0, "y": 0.9009995, "z": 1.75}, "object_poses": [{"objectName": "Book_f5814e4b", "position": {"x": -0.3354904, "y": 1.11098993, "z": -0.7593218}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Statue_1a4cbefa", "position": {"x": -0.3354904, "y": 1.1103971, "z": 0.251773834}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_01a9d33b", "position": {"x": -1.86809945, "y": 0.7909137, "z": 0.243079767}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "CreditCard_01a9d33b", "position": {"x": 1.47836769, "y": 0.55005455, "z": -2.40757751}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Plate_ba789e3c", "position": {"x": 0.0790954158, "y": 1.10851872, "z": -0.253773928}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ba789e3c", "position": {"x": -2.0260036, "y": 1.30615294, "z": 0.9250779}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_77fad61b", "position": {"x": -2.163807, "y": 1.50085676, "z": 1.34379089}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_77fad61b", "position": {"x": 1.89316738, "y": 0.5509404, "z": -2.51112723}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Egg_30762d8a", "position": {"x": -1.96227539, "y": 0.0831761956, "z": 1.88547468}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Egg_30762d8a", "position": {"x": -0.225462511, "y": 1.82566321, "z": -2.61057}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_811f3b02", "position": {"x": -1.90772438, "y": 0.08708125, "z": 1.98115826}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bread_35d530e8", "position": {"x": -0.059099853, "y": 1.18406522, "z": 0.251773834}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_35d530e8", "position": {"x": -0.059099853, "y": 1.18406522, "z": -0.7593218}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e166a32f", "position": {"x": 0.2172907, "y": 1.19122255, "z": 0.251773834}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e166a32f", "position": {"x": -1.85627437, "y": 0.9910225, "z": -0.513000965}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e166a32f", "position": {"x": 0.2172907, "y": 1.19122255, "z": -0.5065479}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a0b666be", "position": {"x": -1.99919128, "y": 0.277290642, "z": 1.33402848}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_c9d70d0f", "position": {"x": -1.60975492, "y": 0.9367496, "z": -1.869518}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_c9d70d0f", "position": {"x": -0.84807694, "y": 0.9367496, "z": -2.406642}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_b5db8038", "position": {"x": -2.12060976, "y": 0.793513, "z": 1.2513144}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_b5db8038", "position": {"x": -2.01865983, "y": 0.764756143, "z": -1.24219179}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Spatula_1e920a81", "position": {"x": -1.82675266, "y": 0.1694812, "z": -0.0532928854}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Spatula_1e920a81", "position": {"x": 0.582687557, "y": 0.9256999, "z": -2.40718341}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_9e34d9fc", "position": {"x": -0.4652, "y": 0.950499952, "z": -2.372}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}, {"objectName": "ButterKnife_f63cc56c", "position": {"x": -1.77484679, "y": 0.911442041, "z": -2.47583175}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f63cc56c", "position": {"x": -0.84807694, "y": 0.911442041, "z": -2.63708615}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_c5dfb511", "position": {"x": 0.2172907, "y": 1.11231673, "z": -0.7593218}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_c21bc5e7", "position": {"x": 1.99396825, "y": 0.88176, "z": -2.50926471}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Bowl_9960ee75", "position": {"x": 1.47836614, "y": 0.5496789, "z": -2.666446}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Book_f5814e4b", "position": {"x": 0.155, "y": 1.1, "z": 0.617}, "rotation": {"x": 0.0, "y": 315.826447, "z": 0.0}}, {"objectName": "Glassbottle_c5dfb511", "position": {"x": 0.355485976, "y": 1.11231673, "z": -0.00100004673}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_c9d70d0f", "position": {"x": 0.8404273, "y": 0.93604964, "z": -2.25355077}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Bread_35d530e8", "position": {"x": -0.3354904, "y": 1.18406522, "z": 0.7573217}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_89238ee6", "position": {"x": 0.893931866, "y": 0.776772439, "z": -2.37714672}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_dc7b7f7e", "position": {"x": -0.122337513, "y": 1.82098532, "z": -2.514165}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_0fe5b61f", "position": {"x": 0.453817844, "y": 0.911680758, "z": -2.484}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_7ed44c70", "position": {"x": -1.80525851, "y": 0.137341261, "z": -1.62344623}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pan_e8d2711b", "position": {"x": 0.7211994, "y": 0.9, "z": -2.41542959}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ba789e3c", "position": {"x": 0.505984664, "y": 1.65691876, "z": -2.5966444}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_811f3b02", "position": {"x": -2.12153769, "y": 0.5795812, "z": 1.00317287}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Vase_d1ae33eb", "position": {"x": 2.042472, "y": 0.5407073, "z": -2.49500537}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_30762d8a", "position": {"x": -1.88044894, "y": 0.0831761956, "z": 2.07684183}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "CreditCard_01a9d33b", "position": {"x": -1.82734108, "y": 0.7911049, "z": -0.153644964}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Statue_1a4cbefa", "position": {"x": -0.197295129, "y": 1.1103971, "z": -0.253773928}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_c21bc5e7", "position": {"x": -1.96227539, "y": 0.0489416756, "z": 2.12468362}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pot_9e34d9fc", "position": {"x": -0.0361, "y": 0.950499952, "z": -2.5762}, "rotation": {"x": 0.0, "y": 270.000061, "z": 0.0}}, {"objectName": "Spatula_1e920a81", "position": {"x": -1.90809989, "y": 0.784756, "z": -1.24219179}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_2c38299b", "position": {"x": 1.48220122, "y": 0.985116363, "z": -2.59076643}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Cup_77fad61b", "position": {"x": -1.290839, "y": 0.909281731, "z": -2.406642}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_5ffb3206", "position": {"x": -0.840385556, "y": 1.653999, "z": -2.604735}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_9960ee75", "position": {"x": -0.9956643, "y": 0.9080204, "z": -2.5602715}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b714915a", "position": {"x": -1.51231253, "y": 1.65462375, "z": -2.53134537}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_816f44ce", "position": {"x": -1.937702, "y": 0.907706141, "z": -2.25621462}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e166a32f", "position": {"x": -1.43842626, "y": 0.991022468, "z": -2.32982731}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f63cc56c", "position": {"x": 0.582687557, "y": 0.9107421, "z": -2.637633}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a0b666be", "position": {"x": -1.58601356, "y": 0.9619776, "z": -2.713901}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_56e11318", "position": {"x": -1.69473386, "y": 0.478245616, "z": -0.100144833}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_cd066502", "position": {"x": -1.96337986, "y": 0.7708855, "z": -1.58758128}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_b5db8038", "position": {"x": -2.122703, "y": 1.497975, "z": 1.00881982}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1428044510, "scene_num": 1}, "task_id": "trial_T20190908_124634_452782", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A12HZGOZQD5YK7_3HVVDCPGTHJVHW7MQNUNWGDGHE1YTX", "high_descs": ["Turn right, go half way across the room and then turn right towards the coffee machine.", "Pick up the lettuce to the left of the coffee maker.", "Turn right and go to the fridge.", "Open the fridge, put the lettuce on the top shelf and close the fridge.", "Turn left and go across the room to the counter.", "Pick up the lettuce on the right side of the glass.", "Turn around and go back to the fridge.", "Open the fridge and put the lettuce on the second shelf on the right side of the tomato."], "task_desc": "Put two lettuce in the fridge.", "votes": [1, 1, 1]}, {"assignment_id": "A11LSO6D7BMY99_3J88R45B2JP8PZO54AZ18S5AQ5DXPC", "high_descs": ["go to the marble counter top on the right side of the room", "pick up the lettuce from the counter top", "go to the fridge to the left of the counter", "place the lettuce in the fridge on the shelf", "go to the counter next to the stove top", "pick up the lettuce from this counter top", "go back to the fridge where you put the other lettuce", "place this lettuce on the shelf in the fridge"], "task_desc": "Place two heads of lettuce in the fridge.", "votes": [1, 1, 1]}, {"assignment_id": "ARB8SJAWXUWTD_3STRJBFXOZI0XRI5M8TWN8Y5J2GKTI", "high_descs": ["Go to the counter left of the coffeemaker", "Pick up the lettuce", "Go to the fridge", "Put the lettuce in the fridge", "Go to the counter right of the stove", "Pick up the lettuce", "Go to the fridge", "Put the lettuce in the fridge"], "task_desc": "Move two heads of lettuce to the fridge", "votes": [1, 1, 0]}]}}