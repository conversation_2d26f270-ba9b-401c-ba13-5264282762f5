{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000091.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000092.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000093.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000094.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000095.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000096.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000097.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000098.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000099.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000100.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000101.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000102.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000103.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000104.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000105.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000106.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000107.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000108.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000109.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000110.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000111.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000112.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000113.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000114.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000115.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000116.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000117.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000118.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000119.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000120.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000121.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000122.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000123.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000124.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000125.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000126.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000127.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000128.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000129.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000130.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000131.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000132.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000133.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000135.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000136.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000137.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000138.png", "low_idx": 30}, {"high_idx": 1, "image_name": "000000139.png", "low_idx": 30}, {"high_idx": 1, "image_name": "000000140.png", "low_idx": 30}, {"high_idx": 1, "image_name": "000000141.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000300.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000301.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000302.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000303.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000304.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000305.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000306.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000307.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000308.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000309.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000310.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000311.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000312.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000313.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000314.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000315.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000316.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000317.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000318.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000319.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000320.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000321.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000322.png", "low_idx": 49}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON>g", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|1|-2|2|0"}}, {"discrete_action": {"action": "PickupObject", "args": ["mug"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON>g", [1.490400196, 1.490400196, -5.41439916, -5.41439916, 6.61618996, 6.61618996]], "coordinateReceptacleObjectId": ["Cabinet", [0.940000056, 0.940000056, -4.79, -4.79, 8.04, 8.04]], "forceVisible": true, "objectId": "Mug|+00.37|+01.65|-01.35"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|-2|2|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["mug"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [1.132, 1.132, -5.3248, -5.3248, 3.6003664, 3.6003664]], "forceVisible": true, "objectId": "Microwave|+00.28|+00.90|-01.33"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|1|-2|2|-30"}}, {"discrete_action": {"action": "PutObject", "args": ["mug", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON>g", [1.490400196, 1.490400196, -5.41439916, -5.41439916, 6.61618996, 6.61618996]], "coordinateReceptacleObjectId": ["Cabinet", [0.940000056, 0.940000056, -4.79, -4.79, 8.04, 8.04]], "forceVisible": true, "objectId": "Mug|+00.37|+01.65|-01.35", "receptacleObjectId": "Cabinet|+00.24|+02.01|-01.20"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|+00.24|+02.01|-01.20"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [4, 1, 155, 121], "mask": [[5, 151], [305, 151], [605, 151], [905, 151], [1205, 151], [1505, 151], [1805, 151], [2105, 151], [2405, 151], [2705, 151], [3005, 151], [3305, 151], [3605, 151], [3905, 151], [4205, 151], [4505, 151], [4805, 151], [5105, 151], [5405, 151], [5705, 151], [6005, 151], [6305, 151], [6605, 151], [6905, 151], [7205, 151], [7505, 151], [7805, 151], [8105, 151], [8405, 151], [8705, 151], [9005, 151], [9305, 151], [9605, 151], [9905, 151], [10205, 151], [10505, 151], [10805, 151], [11105, 151], [11405, 151], [11705, 151], [12005, 151], [12305, 151], [12605, 151], [12905, 151], [13205, 151], [13505, 151], [13805, 151], [14105, 151], [14405, 151], [14705, 151], [15005, 151], [15305, 151], [15605, 151], [15905, 151], [16205, 151], [16505, 151], [16805, 151], [17105, 151], [17405, 151], [17705, 151], [18005, 151], [18305, 151], [18605, 151], [18905, 151], [19205, 151], [19505, 151], [19805, 151], [20105, 151], [20405, 151], [20705, 151], [21005, 151], [21305, 151], [21605, 151], [21905, 151], [22205, 151], [22505, 151], [22805, 151], [23105, 151], [23405, 151], [23705, 151], [24005, 151], [24305, 151], [24605, 151], [24905, 151], [25205, 151], [25505, 151], [25805, 151], [26105, 151], [26405, 151], [26705, 151], [27005, 151], [27305, 151], [27605, 151], [27905, 151], [28205, 151], [28505, 151], [28805, 151], [29105, 151], [29405, 151], [29705, 151], [30005, 151], [30305, 151], [30605, 151], [30905, 151], [31205, 151], [31505, 151], [31805, 151], [32105, 151], [32405, 151], [32705, 151], [33004, 152], [33304, 152], [33604, 152], [33904, 152], [34204, 152], [34504, 152], [34804, 152], [35104, 152], [35405, 151], [35705, 151], [36005, 151]], "point": [79, 60]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Mug|+00.37|+01.65|-01.35"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [97, 93, 135, 121], "mask": [[27700, 22], [27998, 28], [28297, 30], [28597, 31], [28897, 36], [29197, 37], [29497, 32], [29530, 5], [29797, 31], [29832, 3], [30097, 31], [30132, 3], [30397, 31], [30433, 3], [30697, 31], [30733, 3], [30997, 31], [31033, 3], [31297, 31], [31333, 3], [31597, 31], [31633, 3], [31897, 31], [31933, 2], [32197, 31], [32233, 2], [32497, 31], [32532, 3], [32797, 31], [32832, 3], [33097, 31], [33132, 3], [33397, 31], [33431, 3], [33697, 31], [33731, 3], [33997, 31], [34030, 4], [34297, 31], [34329, 4], [34597, 35], [34897, 34], [35197, 33], [35497, 31], [35797, 31], [36097, 31]], "point": [116, 106]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|+00.24|+02.01|-01.20"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [8, 1, 173, 121], "mask": [[8, 153], [308, 153], [608, 153], [908, 153], [1208, 153], [1508, 153], [1808, 153], [2108, 153], [2408, 153], [2708, 153], [3008, 153], [3308, 153], [3608, 153], [3908, 153], [4208, 153], [4508, 153], [4808, 153], [5108, 153], [5408, 153], [5708, 153], [6008, 153], [6308, 153], [6608, 153], [6908, 153], [7208, 153], [7508, 153], [7808, 153], [8108, 153], [8408, 153], [8708, 153], [9008, 153], [9308, 153], [9608, 153], [9908, 153], [10208, 153], [10508, 153], [10808, 153], [11108, 153], [11408, 153], [11708, 153], [12008, 153], [12308, 153], [12608, 153], [12908, 153], [13208, 153], [13508, 153], [13808, 153], [14108, 153], [14408, 153], [14708, 153], [15008, 153], [15308, 155], [15608, 157], [15908, 158], [16208, 160], [16508, 162], [16808, 164], [17108, 166], [17408, 166], [17708, 166], [18008, 166], [18308, 166], [18608, 166], [18908, 166], [19208, 166], [19508, 166], [19808, 166], [20108, 166], [20408, 166], [20708, 166], [21008, 166], [21308, 166], [21608, 166], [21908, 166], [22208, 166], [22508, 166], [22808, 166], [23108, 165], [23408, 165], [23708, 165], [24008, 164], [24308, 164], [24608, 164], [24908, 164], [25208, 163], [25508, 163], [25808, 163], [26108, 162], [26408, 162], [26708, 162], [27008, 161], [27308, 161], [27608, 161], [27908, 160], [28208, 160], [28508, 160], [28808, 159], [29108, 159], [29408, 159], [29708, 158], [30008, 158], [30308, 158], [30608, 157], [30908, 157], [31208, 157], [31508, 156], [31808, 156], [32108, 156], [32408, 155], [32708, 155], [33008, 155], [33308, 154], [33608, 154], [33908, 154], [34208, 153], [34508, 153], [34808, 153], [35108, 153], [35408, 151], [35708, 149], [36008, 147]], "point": [90, 60]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|+00.37|+01.65|-01.35", "placeStationary": true, "receptacleObjectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 4, 252, 267], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15322, 229], [15620, 230], [15919, 231], [16219, 231], [16518, 232], [16817, 232], [17116, 233], [17415, 234], [17715, 234], [18014, 235], [18313, 236], [18612, 236], [18911, 237], [19211, 237], [19510, 237], [19809, 238], [20108, 239], [20407, 240], [20707, 239], [21006, 240], [21305, 241], [21604, 241], [21903, 242], [22203, 242], [22502, 243], [22801, 243], [23100, 244], [23400, 244], [23700, 243], [24000, 243], [24300, 243], [24600, 243], [24900, 242], [25200, 242], [25500, 242], [25800, 241], [26100, 241], [26400, 241], [26700, 241], [27000, 240], [27300, 240], [27600, 240], [27900, 240], [28200, 239], [28500, 239], [28800, 239], [29100, 238], [29400, 238], [29700, 238], [30000, 238], [30300, 237], [30600, 237], [30900, 237], [31200, 236], [31500, 236], [31800, 236], [32100, 236], [32400, 235], [32700, 235], [33000, 235], [33300, 234], [33600, 234], [33900, 234], [34200, 234], [34500, 233], [34800, 233], [35100, 233], [35400, 232], [35700, 232], [36000, 232], [36300, 232], [36600, 231], [36900, 231], [37200, 231], [37500, 230], [37800, 230], [38100, 230], [38400, 230], [38700, 229], [39000, 229], [39300, 229], [39600, 228], [39900, 228], [40200, 228], [40500, 228], [40800, 227], [41100, 227], [41400, 227], [41700, 226], [42000, 226], [42300, 225], [42600, 226], [42900, 225], [43200, 225], [43500, 225], [43800, 56], [43858, 166], [44100, 56], [44158, 166], [44400, 56], [44458, 166], [44700, 55], [44759, 165], [45000, 55], [45300, 54], [45600, 54], [45900, 54], [46200, 53], [46500, 53], [46800, 53], [47100, 52], [47400, 52], [47700, 52], [48000, 51], [48300, 51], [48600, 50], [48900, 50], [49200, 50], [49500, 49], [49800, 49], [50100, 49], [50400, 48], [50700, 48], [51000, 48], [51300, 47], [51600, 47], [51900, 46], [52200, 46], [52500, 46], [52800, 45], [53100, 45], [53400, 45], [53700, 44], [54000, 44], [54300, 43], [54600, 43], [54900, 43], [55200, 42], [55500, 42], [55800, 42], [56100, 41], [56400, 41], [56700, 41], [57000, 40], [57300, 40], [57600, 39], [57900, 39], [58200, 39], [58500, 38], [58800, 38], [59100, 38], [59400, 37], [59700, 37], [60000, 37], [60300, 36], [60600, 36], [60900, 35], [61200, 35], [61500, 35], [61800, 34], [62100, 34], [62400, 34], [62700, 33], [63000, 33], [63300, 33], [63600, 32], [63900, 32], [64200, 31], [64500, 31], [64800, 31], [65100, 30], [65400, 30], [65700, 30], [66000, 29], [66300, 29], [66600, 28], [66900, 28], [67200, 28], [67500, 27], [67800, 27], [68100, 27], [68400, 26], [68700, 26], [69000, 26], [69300, 25], [69600, 25], [69900, 24], [70200, 24], [70500, 24], [70800, 23], [71100, 23], [71400, 23], [71700, 22], [72000, 22], [72300, 22], [72600, 21], [72900, 21], [73200, 20], [73500, 20], [73800, 20], [74100, 19], [74400, 19], [74700, 19], [75000, 18], [75300, 18], [75600, 18], [75900, 17], [76200, 17], [76500, 16], [76800, 16], [77100, 16], [77400, 15], [77700, 17], [78000, 18], [78300, 19], [78600, 19], [78900, 14], [78915, 2], [79200, 13], [79500, 13], [79800, 12]], "point": [126, 134]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 4, 252, 267], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15322, 229], [15620, 230], [15919, 231], [16219, 231], [16518, 232], [16817, 232], [17116, 233], [17415, 234], [17715, 234], [18014, 235], [18313, 236], [18612, 236], [18911, 237], [19211, 237], [19510, 237], [19809, 238], [20108, 239], [20407, 240], [20707, 239], [21006, 240], [21305, 241], [21604, 241], [21903, 242], [22203, 242], [22502, 243], [22801, 243], [23100, 244], [23400, 244], [23700, 145], [23859, 84], [24000, 143], [24162, 81], [24300, 141], [24464, 79], [24600, 140], [24765, 78], [24900, 139], [25066, 76], [25200, 138], [25367, 75], [25500, 138], [25667, 75], [25800, 138], [25971, 70], [26100, 138], [26273, 68], [26400, 138], [26573, 68], [26700, 138], [26874, 67], [27000, 138], [27167, 4], [27174, 66], [27300, 138], [27467, 4], [27474, 66], [27600, 138], [27767, 4], [27774, 66], [27900, 138], [28067, 4], [28074, 66], [28200, 138], [28367, 4], [28374, 65], [28500, 138], [28667, 4], [28674, 65], [28800, 138], [28966, 5], [28973, 66], [29100, 138], [29266, 4], [29273, 65], [29400, 138], [29566, 4], [29573, 65], [29700, 138], [29866, 3], [29872, 66], [30000, 138], [30166, 2], [30172, 66], [30300, 138], [30466, 1], [30472, 65], [30600, 138], [30771, 66], [30900, 138], [31070, 67], [31200, 138], [31369, 67], [31500, 138], [31666, 70], [31800, 138], [31966, 70], [32100, 139], [32266, 70], [32400, 139], [32566, 69], [32700, 139], [32866, 69], [33000, 139], [33166, 69], [33300, 139], [33466, 68], [33600, 139], [33765, 69], [33900, 140], [34065, 69], [34200, 141], [34364, 70], [34500, 142], [34663, 70], [34800, 143], [34961, 72], [35100, 145], [35259, 74], [35400, 151], [35554, 78], [35700, 232], [36000, 232], [36300, 232], [36600, 231], [36900, 231], [37200, 231], [37500, 230], [37800, 230], [38100, 230], [38400, 230], [38700, 229], [39000, 229], [39300, 229], [39600, 228], [39900, 228], [40200, 228], [40500, 228], [40800, 227], [41100, 227], [41400, 227], [41700, 226], [42000, 226], [42300, 225], [42600, 226], [42900, 225], [43200, 225], [43500, 225], [43800, 56], [43858, 166], [44100, 56], [44158, 166], [44400, 56], [44458, 166], [44700, 55], [44759, 165], [45000, 55], [45300, 54], [45600, 54], [45900, 54], [46200, 53], [46500, 53], [46800, 53], [47100, 52], [47400, 52], [47700, 52], [48000, 51], [48300, 51], [48600, 50], [48900, 50], [49200, 50], [49500, 49], [49800, 49], [50100, 49], [50400, 48], [50700, 48], [51000, 48], [51300, 47], [51600, 47], [51900, 46], [52200, 46], [52500, 46], [52800, 45], [53100, 45], [53400, 45], [53700, 44], [54000, 44], [54300, 43], [54600, 43], [54900, 43], [55200, 42], [55500, 42], [55800, 42], [56100, 41], [56400, 41], [56700, 41], [57000, 40], [57300, 40], [57600, 39], [57900, 39], [58200, 39], [58500, 38], [58800, 38], [59100, 38], [59400, 37], [59700, 37], [60000, 37], [60300, 36], [60600, 36], [60900, 35], [61200, 35], [61500, 35], [61800, 34], [62100, 34], [62400, 34], [62700, 33], [63000, 33], [63300, 33], [63600, 32], [63900, 32], [64200, 31], [64500, 31], [64800, 31], [65100, 30], [65400, 30], [65700, 30], [66000, 29], [66300, 29], [66600, 28], [66900, 28], [67200, 28], [67500, 27], [67800, 27], [68100, 27], [68400, 26], [68700, 26], [69000, 26], [69300, 25], [69600, 25], [69900, 24], [70200, 24], [70500, 24], [70800, 23], [71100, 23], [71400, 23], [71700, 22], [72000, 22], [72300, 22], [72600, 21], [72900, 21], [73200, 20], [73500, 20], [73800, 20], [74100, 19], [74400, 19], [74700, 19], [75000, 18], [75300, 18], [75600, 18], [75900, 17], [76200, 17], [76500, 16], [76800, 16], [77100, 16], [77400, 15], [77700, 17], [78000, 18], [78300, 19], [78600, 19], [78900, 14], [78915, 2], [79200, 13], [79500, 13], [79800, 12]], "point": [126, 134]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [1.132, 1.132, -5.3248, -5.3248, 3.6003664, 3.6003664]], "forceVisible": true, "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [1.132, 1.132, -5.3248, -5.3248, 3.6003664, 3.6003664]], "forceVisible": true, "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Mug|+00.37|+01.65|-01.35"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [138, 80, 173, 119], "mask": [[23845, 14], [24143, 19], [24441, 23], [24740, 25], [25039, 27], [25338, 29], [25638, 29], [25938, 33], [26238, 35], [26538, 35], [26838, 36], [27138, 29], [27171, 3], [27438, 29], [27471, 3], [27738, 29], [27771, 3], [28038, 29], [28071, 3], [28338, 29], [28371, 3], [28638, 29], [28671, 3], [28938, 28], [28971, 2], [29238, 28], [29270, 3], [29538, 28], [29570, 3], [29838, 28], [29869, 3], [30138, 28], [30168, 4], [30438, 28], [30467, 5], [30738, 33], [31038, 32], [31338, 31], [31638, 28], [31938, 28], [32239, 27], [32539, 27], [32839, 27], [33139, 27], [33439, 27], [33739, 26], [34040, 25], [34341, 23], [34642, 21], [34943, 18], [35245, 14], [35551, 3]], "point": [155, 98]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 4, 252, 267], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15322, 229], [15620, 230], [15919, 231], [16219, 231], [16518, 232], [16817, 232], [17116, 233], [17415, 234], [17715, 234], [18014, 235], [18313, 236], [18612, 236], [18911, 237], [19211, 237], [19510, 237], [19809, 238], [20108, 239], [20407, 240], [20707, 239], [21006, 240], [21305, 241], [21604, 241], [21903, 242], [22203, 242], [22502, 243], [22801, 243], [23100, 244], [23400, 244], [23700, 243], [24000, 243], [24300, 243], [24600, 243], [24900, 242], [25200, 242], [25500, 242], [25800, 241], [26100, 241], [26400, 241], [26700, 241], [27000, 240], [27300, 240], [27600, 240], [27900, 240], [28200, 239], [28500, 239], [28800, 239], [29100, 238], [29400, 238], [29700, 238], [30000, 238], [30300, 237], [30600, 237], [30900, 237], [31200, 236], [31500, 236], [31800, 236], [32100, 236], [32400, 235], [32700, 235], [33000, 235], [33300, 234], [33600, 234], [33900, 234], [34200, 234], [34500, 233], [34800, 233], [35100, 233], [35400, 232], [35700, 232], [36000, 232], [36300, 232], [36600, 231], [36900, 231], [37200, 231], [37500, 230], [37800, 230], [38100, 230], [38400, 230], [38700, 229], [39000, 229], [39300, 229], [39600, 228], [39900, 228], [40200, 228], [40500, 228], [40800, 227], [41100, 227], [41400, 227], [41700, 226], [42000, 226], [42300, 225], [42600, 226], [42900, 225], [43200, 225], [43500, 225], [43800, 56], [43858, 166], [44100, 56], [44158, 166], [44400, 56], [44458, 166], [44700, 55], [44759, 165], [45000, 55], [45300, 54], [45600, 54], [45900, 54], [46200, 53], [46500, 53], [46800, 53], [47100, 52], [47400, 52], [47700, 52], [48000, 51], [48300, 51], [48600, 50], [48900, 50], [49200, 50], [49500, 49], [49800, 49], [50100, 49], [50400, 48], [50700, 48], [51000, 48], [51300, 47], [51600, 47], [51900, 46], [52200, 46], [52500, 46], [52800, 45], [53100, 45], [53400, 45], [53700, 44], [54000, 44], [54300, 43], [54600, 43], [54900, 43], [55200, 42], [55500, 42], [55800, 42], [56100, 41], [56400, 41], [56700, 41], [57000, 40], [57300, 40], [57600, 39], [57900, 39], [58200, 39], [58500, 38], [58800, 38], [59100, 38], [59400, 37], [59700, 37], [60000, 37], [60300, 36], [60600, 36], [60900, 35], [61200, 35], [61500, 35], [61800, 34], [62100, 34], [62400, 34], [62700, 33], [63000, 33], [63300, 33], [63600, 32], [63900, 32], [64200, 31], [64500, 31], [64800, 31], [65100, 30], [65400, 30], [65700, 30], [66000, 29], [66300, 29], [66600, 28], [66900, 28], [67200, 28], [67500, 27], [67800, 27], [68100, 27], [68400, 26], [68700, 26], [69000, 26], [69300, 25], [69600, 25], [69900, 24], [70200, 24], [70500, 24], [70800, 23], [71100, 23], [71400, 23], [71700, 22], [72000, 22], [72300, 22], [72600, 21], [72900, 21], [73200, 20], [73500, 20], [73800, 20], [74100, 19], [74400, 19], [74700, 19], [75000, 18], [75300, 18], [75600, 18], [75900, 17], [76200, 17], [76500, 16], [76800, 16], [77100, 16], [77400, 15], [77700, 17], [78000, 18], [78300, 19], [78600, 19], [78900, 14], [78915, 2], [79200, 13], [79500, 13], [79800, 12]], "point": [126, 134]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|+00.24|+02.01|-01.20"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 64, 154, 264], "mask": [[18949, 105], [19248, 106], [19548, 106], [19848, 106], [20148, 106], [20447, 107], [20747, 107], [21047, 107], [21347, 107], [21646, 108], [21946, 108], [22246, 108], [22545, 109], [22845, 109], [23145, 109], [23445, 109], [23744, 110], [24044, 110], [24344, 110], [24643, 111], [24943, 111], [25243, 111], [25543, 111], [25842, 112], [26142, 112], [26442, 112], [26741, 113], [27041, 113], [27341, 113], [27641, 113], [27940, 114], [28240, 114], [28540, 114], [28840, 114], [29139, 115], [29439, 115], [29739, 115], [30038, 116], [30338, 116], [30638, 116], [30938, 116], [31237, 117], [31537, 117], [31837, 117], [32136, 118], [32436, 118], [32736, 118], [33036, 118], [33335, 119], [33635, 119], [33935, 119], [34235, 119], [34534, 120], [34834, 120], [35134, 120], [35433, 121], [35733, 121], [36033, 122], [36333, 122], [36632, 123], [36932, 123], [37232, 123], [37531, 124], [37831, 124], [38131, 124], [38431, 124], [38730, 125], [39030, 125], [39330, 125], [39630, 125], [39929, 126], [40229, 126], [40529, 126], [40828, 127], [41128, 127], [41428, 127], [41728, 127], [42027, 128], [42327, 128], [42627, 128], [42926, 129], [43226, 129], [43526, 129], [43826, 129], [44125, 130], [44425, 130], [44725, 130], [45025, 130], [45324, 131], [45624, 131], [45924, 131], [46223, 132], [46523, 132], [46823, 132], [47123, 132], [47422, 133], [47722, 133], [48022, 133], [48321, 134], [48621, 134], [48921, 134], [49221, 134], [49520, 135], [49820, 135], [50120, 135], [50419, 136], [50719, 136], [51019, 136], [51319, 136], [51618, 137], [51918, 137], [52218, 137], [52518, 137], [52817, 138], [53117, 138], [53417, 138], [53716, 139], [54016, 139], [54316, 139], [54616, 139], [54915, 140], [55215, 140], [55515, 140], [55814, 141], [56114, 141], [56414, 141], [56714, 135], [56851, 4], [57013, 127], [57313, 124], [57613, 121], [57913, 118], [58212, 117], [58512, 116], [58812, 114], [59111, 113], [59411, 112], [59711, 111], [60011, 110], [60310, 110], [60610, 109], [60910, 108], [61209, 108], [61509, 107], [61809, 106], [62109, 105], [62408, 106], [62708, 105], [63008, 104], [63308, 104], [63607, 104], [63907, 104], [64207, 103], [64506, 104], [64806, 104], [65106, 103], [65406, 103], [65705, 93], [66005, 91], [66305, 89], [66604, 89], [66904, 88], [67204, 88], [67504, 87], [67803, 88], [68103, 88], [68403, 87], [68702, 88], [69002, 88], [69302, 87], [69602, 87], [69901, 88], [70201, 88], [70501, 88], [70600, 8], [70801, 88], [70901, 7], [71100, 89], [71202, 7], [71400, 90], [71503, 6], [71700, 90], [71805, 4], [72000, 90], [72107, 2], [72300, 90], [72409, 1], [72600, 91], [72900, 91], [73200, 92], [73500, 93], [73800, 93], [74100, 94], [74400, 95], [74700, 96], [75000, 97], [75300, 98], [75600, 99], [75900, 100], [76200, 102], [76500, 104], [76800, 107], [77100, 110], [77400, 114], [77700, 114], [78000, 114], [78300, 114], [78600, 115], [78900, 115]], "point": [77, 163]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|+00.37|+01.65|-01.35", "placeStationary": true, "receptacleObjectId": "Cabinet|+00.24|+02.01|-01.20"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 173, 264], "mask": [[151, 5], [451, 5], [751, 5], [1051, 5], [1351, 6], [1651, 6], [1951, 6], [2251, 6], [2551, 6], [2851, 6], [3151, 6], [3451, 6], [3751, 6], [4051, 6], [4351, 6], [4651, 6], [4951, 6], [5251, 6], [5551, 6], [5851, 6], [6151, 6], [6451, 6], [6751, 6], [7051, 6], [7351, 6], [7651, 6], [7951, 6], [8251, 6], [8551, 6], [8851, 6], [9151, 6], [9451, 6], [9751, 6], [10051, 6], [10351, 6], [10651, 6], [10951, 6], [11251, 6], [11551, 6], [11851, 6], [12151, 6], [12451, 6], [12751, 6], [13051, 6], [13351, 6], [13651, 6], [13951, 6], [14251, 6], [14551, 6], [14851, 6], [15151, 6], [15451, 7], [15751, 7], [16051, 7], [16351, 7], [16651, 7], [16951, 7], [17251, 7], [17551, 7], [17851, 7], [18151, 7], [18451, 7], [18751, 7], [19051, 7], [19351, 7], [19651, 7], [19850, 108], [20149, 109], [20449, 109], [20749, 109], [21049, 109], [21348, 110], [21648, 110], [21948, 110], [22248, 110], [22547, 111], [22847, 111], [23147, 111], [23446, 112], [23746, 112], [24046, 112], [24346, 112], [24645, 113], [24945, 113], [25245, 113], [25545, 113], [25844, 114], [26144, 114], [26444, 114], [26743, 115], [27043, 115], [27343, 115], [27643, 115], [27942, 116], [28242, 116], [28542, 116], [28842, 116], [29141, 117], [29441, 118], [29741, 118], [30040, 119], [30340, 119], [30640, 119], [30940, 119], [31239, 120], [31539, 120], [31839, 120], [32139, 120], [32438, 121], [32738, 121], [33038, 121], [33337, 122], [33637, 122], [33937, 122], [34237, 122], [34536, 123], [34836, 123], [35136, 123], [35436, 123], [35735, 124], [36035, 124], [36335, 124], [36634, 125], [36934, 125], [37234, 125], [37534, 125], [37833, 126], [38133, 126], [38433, 126], [38732, 127], [39032, 127], [39332, 127], [39632, 127], [39931, 128], [40231, 128], [40531, 128], [40831, 128], [41130, 129], [41430, 129], [41730, 129], [42029, 130], [42329, 130], [42629, 130], [42929, 130], [43228, 131], [43528, 132], [43828, 132], [44128, 132], [44427, 133], [44727, 133], [45027, 133], [45326, 134], [45626, 134], [45926, 134], [46226, 134], [46525, 135], [46825, 135], [47125, 135], [47425, 135], [47724, 136], [48024, 136], [48324, 136], [48623, 137], [48923, 137], [49223, 137], [49523, 137], [49822, 138], [50122, 138], [50422, 138], [50722, 138], [51021, 139], [51321, 139], [51621, 139], [51920, 140], [52220, 140], [52520, 140], [52820, 140], [53119, 141], [53419, 141], [53719, 141], [54019, 141], [54318, 142], [54618, 142], [54918, 142], [55217, 143], [55517, 143], [55817, 143], [56117, 143], [56416, 144], [56716, 133], [56851, 9], [57016, 124], [57152, 8], [57316, 121], [57452, 9], [57615, 119], [57752, 11], [57915, 116], [58052, 13], [58215, 114], [58352, 14], [58514, 114], [58652, 16], [58814, 112], [58952, 18], [59114, 110], [59252, 20], [59414, 109], [59552, 21], [59713, 109], [59852, 21], [60013, 108], [60152, 21], [60313, 107], [60452, 21], [60613, 106], [60752, 21], [60912, 106], [61052, 21], [61212, 105], [61352, 21], [61512, 104], [61652, 21], [61811, 104], [61952, 21], [62111, 103], [62252, 21], [62411, 103], [62552, 21], [62711, 102], [62852, 21], [63010, 102], [63152, 22], [63310, 102], [63452, 22], [63610, 101], [63752, 22], [63910, 101], [64052, 22], [64209, 101], [64352, 22], [64509, 101], [64652, 22], [64809, 101], [64952, 21], [65108, 101], [65252, 21], [65408, 101], [65552, 21], [65708, 90], [65852, 21], [66008, 88], [66152, 20], [66307, 87], [66452, 20], [66607, 86], [66752, 20], [66907, 85], [67052, 19], [67207, 85], [67352, 19], [67506, 85], [67652, 19], [67806, 85], [67952, 19], [68106, 85], [68252, 18], [68405, 85], [68552, 18], [68705, 85], [68852, 18], [69005, 85], [69152, 17], [69305, 84], [69452, 17], [69604, 85], [69752, 17], [69904, 85], [70052, 17], [70204, 85], [70352, 16], [70503, 86], [70600, 8], [70652, 16], [70803, 86], [70901, 7], [70952, 14], [71103, 86], [71202, 7], [71252, 12], [71403, 87], [71503, 6], [71552, 10], [71702, 88], [71805, 4], [71852, 7], [72002, 88], [72107, 2], [72302, 88], [72409, 1], [72602, 89], [72901, 90], [73201, 91], [73501, 92], [73800, 93], [74100, 94], [74400, 95], [74700, 96], [75000, 97], [75300, 98], [75600, 99], [75900, 100], [76200, 102], [76500, 104], [76800, 107], [77100, 110], [77400, 114], [77700, 114], [78000, 114], [78300, 114], [78600, 115], [78900, 115]], "point": [86, 131]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|+00.24|+02.01|-01.20"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 173, 264], "mask": [[151, 5], [451, 5], [751, 5], [1051, 5], [1351, 6], [1651, 6], [1951, 6], [2251, 6], [2551, 6], [2851, 6], [3151, 6], [3451, 6], [3751, 6], [4051, 6], [4351, 6], [4651, 6], [4951, 6], [5251, 6], [5551, 6], [5851, 6], [6151, 6], [6451, 6], [6751, 6], [7051, 6], [7351, 6], [7651, 6], [7951, 6], [8251, 6], [8551, 6], [8851, 6], [9151, 6], [9451, 6], [9751, 6], [10051, 6], [10351, 6], [10651, 6], [10951, 6], [11251, 6], [11551, 6], [11851, 6], [12151, 6], [12451, 6], [12751, 6], [13051, 6], [13351, 6], [13651, 6], [13951, 6], [14251, 6], [14551, 6], [14851, 6], [15151, 6], [15451, 7], [15751, 7], [16051, 7], [16351, 7], [16651, 7], [16951, 7], [17251, 7], [17551, 7], [17851, 7], [18151, 7], [18451, 7], [18751, 7], [19051, 7], [19351, 7], [19651, 7], [19850, 108], [20149, 109], [20449, 109], [20749, 109], [21049, 109], [21348, 110], [21648, 110], [21948, 110], [22248, 110], [22547, 111], [22847, 111], [23147, 111], [23446, 112], [23746, 112], [24046, 112], [24346, 112], [24645, 113], [24945, 113], [25245, 113], [25545, 113], [25844, 114], [26144, 114], [26444, 114], [26743, 115], [27043, 115], [27343, 115], [27643, 115], [27942, 116], [28242, 116], [28542, 116], [28842, 116], [29141, 117], [29441, 118], [29741, 118], [30040, 119], [30340, 119], [30640, 119], [30940, 119], [31239, 120], [31539, 120], [31839, 120], [32139, 120], [32438, 121], [32738, 121], [33038, 121], [33337, 122], [33637, 122], [33937, 122], [34237, 122], [34536, 123], [34836, 123], [35136, 123], [35436, 123], [35735, 124], [36035, 124], [36335, 124], [36634, 125], [36934, 125], [37234, 125], [37534, 125], [37833, 126], [38133, 126], [38433, 126], [38732, 127], [39032, 127], [39332, 127], [39632, 127], [39931, 128], [40231, 128], [40531, 128], [40831, 128], [41130, 129], [41430, 129], [41730, 129], [42029, 130], [42329, 130], [42629, 130], [42929, 130], [43228, 131], [43528, 132], [43828, 132], [44128, 132], [44427, 133], [44727, 133], [45027, 133], [45326, 134], [45626, 134], [45926, 134], [46226, 134], [46525, 135], [46825, 135], [47125, 135], [47425, 135], [47724, 136], [48024, 136], [48324, 136], [48623, 137], [48923, 137], [49223, 137], [49523, 137], [49822, 138], [50122, 138], [50422, 138], [50722, 138], [51021, 139], [51321, 139], [51621, 139], [51920, 140], [52220, 140], [52520, 140], [52820, 140], [53119, 141], [53419, 141], [53719, 141], [54019, 141], [54318, 142], [54618, 142], [54918, 142], [55217, 143], [55517, 143], [55817, 143], [56117, 143], [56416, 144], [56716, 144], [57016, 144], [57316, 145], [57615, 148], [57915, 150], [58215, 151], [58514, 154], [58814, 156], [59114, 158], [59414, 159], [59713, 160], [60013, 160], [60313, 160], [60613, 160], [60912, 161], [61212, 161], [61512, 161], [61811, 162], [62111, 162], [62411, 162], [62711, 162], [63010, 164], [63310, 164], [63610, 164], [63910, 164], [64209, 165], [64509, 165], [64809, 164], [65108, 165], [65408, 165], [65708, 165], [66008, 164], [66307, 165], [66607, 165], [66907, 164], [67207, 69], [67290, 81], [67506, 65], [67597, 74], [67806, 64], [67900, 71], [68106, 63], [68202, 68], [68405, 64], [68504, 66], [68705, 64], [68807, 63], [69005, 64], [69109, 60], [69305, 64], [69410, 59], [69604, 64], [69704, 2], [69711, 58], [69904, 64], [70003, 4], [70011, 58], [70204, 64], [70303, 5], [70311, 57], [70503, 65], [70603, 5], [70611, 57], [70803, 65], [70903, 5], [70911, 57], [71103, 65], [71203, 5], [71211, 57], [71403, 64], [71503, 5], [71511, 56], [71702, 65], [71803, 5], [71811, 56], [72002, 65], [72103, 5], [72111, 56], [72302, 65], [72403, 5], [72411, 55], [72602, 65], [72702, 5], [72711, 55], [72901, 66], [73002, 5], [73011, 55], [73201, 66], [73302, 5], [73310, 56], [73501, 65], [73602, 5], [73610, 55], [73800, 66], [73902, 4], [73910, 55], [74100, 66], [74202, 4], [74209, 56], [74400, 66], [74502, 3], [74509, 55], [74700, 66], [74802, 3], [74809, 55], [75000, 66], [75102, 2], [75108, 56], [75300, 65], [75402, 1], [75407, 57], [75600, 65], [75706, 57], [75900, 65], [76006, 57], [76200, 65], [76304, 59], [76500, 65], [76603, 59], [76800, 65], [76901, 61], [77100, 65], [77201, 61], [77400, 64], [77501, 61], [77700, 64], [77801, 60], [78000, 64], [78101, 60], [78300, 64], [78401, 58], [78600, 64], [78701, 55], [78900, 64], [79001, 54]], "point": [86, 131]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan22", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": 1.0, "y": 0.9009992, "z": 2.5}, "object_poses": [{"objectName": "Potato_5ea35e17", "position": {"x": -0.598039, "y": 0.9335, "z": -1.119659}, "rotation": {"x": 0.0, "y": 47.53002, "z": 0.0}}, {"objectName": "Potato_5ea35e17", "position": {"x": -0.598039, "y": 0.9335, "z": -1.119659}, "rotation": {"x": 0.0, "y": 47.53002, "z": 0.0}}, {"objectName": "CreditCard_35b71b93", "position": {"x": -2.60430479, "y": 0.908296, "z": -0.0629096553}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_35b71b93", "position": {"x": 0.997561, "y": 0.9082959, "z": -1.28244877}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_1cf3b23c", "position": {"x": 0.721354067, "y": 1.11274135, "z": 1.306659}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Pot_86f597c8", "position": {"x": -2.6225, "y": 0.9287, "z": -0.3964}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_4b9198b3", "position": {"x": 0.3594984, "y": 0.7486444, "z": -1.05214345}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_b9d165af", "position": {"x": -0.06949246, "y": 1.11224866, "z": 1.79089165}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Spatula_3b50a8bf", "position": {"x": -1.50506091, "y": 0.8666762, "z": -1.19472015}, "rotation": {"x": 0.0, "y": 270.0, "z": -1.40334183e-14}}, {"objectName": "Spatula_3b50a8bf", "position": {"x": -1.41871345, "y": 0.8666588, "z": -1.30527985}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334183e-14}}, {"objectName": "Cup_46773746", "position": {"x": -1.24601889, "y": 0.848235548, "z": -1.3605597}, "rotation": {"x": 1.40334183e-14, "y": 3.208973e-43, "z": 2.89269767e-27}}, {"objectName": "Cup_46773746", "position": {"x": -2.2369647, "y": 0.909979343, "z": -1.199}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ad96aa4f", "position": {"x": -0.0496140122, "y": 1.66433525, "z": -1.39770389}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ad96aa4f", "position": {"x": -2.01539421, "y": 1.66433525, "z": -1.36000526}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_1fa5d324", "position": {"x": -0.2391411, "y": 0.9343805, "z": -1.36589718}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_1fa5d324", "position": {"x": 0.997561, "y": 0.9343804, "z": -1.36589742}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_f59054f9", "position": {"x": -2.44529438, "y": 1.65877759, "z": -1.39770389}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_f59054f9", "position": {"x": -2.68743, "y": 0.9076062, "z": -0.147414446}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_4f98d325", "position": {"x": -1.38758254, "y": 1.65877759, "z": -1.473101}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_4f98d325", "position": {"x": -0.662034154, "y": 0.9076062, "z": -1.44934571}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_81a93477", "position": {"x": -2.74032164, "y": 0.432975948, "z": 1.013526}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_a99f93a6", "position": {"x": -1.0733242, "y": 0.903479, "z": -1.13944018}, "rotation": {"x": 1.40334183e-14, "y": 3.208973e-43, "z": 2.89269767e-27}}, {"objectName": "Apple_a99f93a6", "position": {"x": -2.81595325, "y": 0.0878257751, "z": 1.5369277}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_304c8d90", "position": {"x": -2.59, "y": 0.9, "z": -1.147}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a99f93a6", "position": {"x": -2.699162, "y": 0.0878257751, "z": 1.50188792}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_b9d165af", "position": {"x": 0.164701045, "y": 1.11224866, "z": 1.85760593}, "rotation": {"x": 0.0, "y": 135.000076, "z": 0.0}}, {"objectName": "Kettle_1cf3b23c", "position": {"x": -1.65596771, "y": 1.66351271, "z": -1.39770389}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_35b71b93", "position": {"x": -2.93680477, "y": 0.908296, "z": 0.02159518}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_81a93477", "position": {"x": 0.765533566, "y": 0.965819955, "z": -1.03210258}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ad96aa4f", "position": {"x": 0.6636004, "y": 1.1135639, "z": 0.902641356}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "SoapBottle_f346af22", "position": {"x": -0.748, "y": 0.8999999, "z": -1.291}, "rotation": {"x": 0.0, "y": 315.000031, "z": 0.0}}, {"objectName": "Spatula_3b50a8bf", "position": {"x": -0.4505876, "y": 0.92629987, "z": -1.11555147}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e2bf19ed", "position": {"x": -2.6608, "y": 0.8744, "z": 0.9831}, "rotation": {"x": 31.0465031, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_4f98d325", "position": {"x": -2.02590227, "y": 0.112172782, "z": -1.13122034}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9ffe4e75", "position": {"x": 0.203787982, "y": 1.15205479, "z": 1.05698335}, "rotation": {"x": 359.98233, "y": 0.00772271631, "z": 0.233230665}}, {"objectName": "PepperShaker_f59054f9", "position": {"x": 0.03262645, "y": 1.10800624, "z": 1.940316}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "ButterKnife_dc49bd4c", "position": {"x": -2.22248077, "y": 0.9, "z": -0.997339845}, "rotation": {"x": 0.0, "y": 323.977722, "z": 0.0}}, {"objectName": "Pot_86f597c8", "position": {"x": -2.80053949, "y": 1.47523439, "z": 0.852000058}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Potato_5ea35e17", "position": {"x": -0.598039, "y": 0.9335, "z": -1.119659}, "rotation": {"x": 0.0, "y": 47.53002, "z": 0.0}}, {"objectName": "DishSponge_9171dd8d", "position": {"x": -1.221, "y": 0.8416841, "z": -1.1264}, "rotation": {"x": 0.0, "y": 41.1644974, "z": 0.0}}, {"objectName": "Egg_e02c5e35", "position": {"x": 0.136268735, "y": 1.13652563, "z": 1.274665}, "rotation": {"x": 359.9644, "y": 2.94516053e-06, "z": 359.9811}}, {"objectName": "Spoon_4b9198b3", "position": {"x": 0.452051967, "y": 1.11282945, "z": 1.72988582}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Knife_1fa5d324", "position": {"x": -1.591408, "y": 0.8726366, "z": -1.19472015}, "rotation": {"x": 0.0, "y": 270.0, "z": -1.40334183e-14}}, {"objectName": "Cup_46773746", "position": {"x": -2.93680477, "y": 0.9099794, "z": -0.147414446}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f88244ed", "position": {"x": 0.372600049, "y": 1.65404749, "z": -1.35359979}, "rotation": {"x": 359.610046, "y": -3.675765e-05, "z": 0.00738341268}}, {"objectName": "Bowl_b7a8ccec", "position": {"x": -2.046209, "y": 0.9, "z": -1.16398358}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2577004842, "scene_num": 22}, "task_id": "trial_T20190908_073225_811379", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A24RGLS3BRZ60J_34QN5IT0T2871SXFJ9J2C637XG6809", "high_descs": ["Turn to the left, and go back to the microwave.", "Open the top left cabinet and take out the mug.", "Hold the mug and look down.", "Place the mug in the microwave below, then when finished, open the door and take out the mug.", "Hold the mug and look above again.", "Open the left side cabinet and place the mug inside, shut the door."], "task_desc": "Heat a mug and place it in the cabinet.", "votes": [1, 1, 1, 1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_39GXDJN2OWVNUTYVM67RXZTXRB4V87", "high_descs": ["Walk towards the wall, then hang a left an walk over to the microwave.", "Open the leftmost cabinet door above the microwave and remove the mug from inside the cabinet, then close the door.", "Look down at the microwave on the counter.", "Open the microwave and put the mug inside then turn on the microwave, after a couple seconds open the microwave and remove the now heated mug then close the door.", "Look up at the upper cabinets.", "Open the left upper cabinet door in front of you and put the heated mug inside, then close the door."], "task_desc": "Put a heated mug in a cabinet.", "votes": [1, 1, 1, 1, 1]}, {"assignment_id": "A38Z99XF4NDNH0_32UTUBMZ7JD6WRW2PIR1O1JY0OEBVQ", "high_descs": ["Move back then left to the cupboard on the left directly above the red microwave", "Open the cupboard and remove the white mug inside of the cupboard, close the cupboard", "Carry the white mug to face the red microwave", "Open the microwave, place the mug inside, turn it on then remove the mug and shut the door", "Move to face the upper cupboard on the left where the mug was", "Place the mug back in the cupboard and close the cupboard"], "task_desc": "Move a heated mug to the cupboard", "votes": [1, 1]}]}}