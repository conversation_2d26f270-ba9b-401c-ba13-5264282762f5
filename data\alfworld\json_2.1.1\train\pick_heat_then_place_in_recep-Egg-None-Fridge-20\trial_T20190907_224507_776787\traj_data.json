{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000103.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000104.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000105.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000289.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000290.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000291.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000292.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000293.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000294.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000295.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000296.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000297.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000298.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000299.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000300.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000301.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000302.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000303.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000304.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000305.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000306.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000307.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000308.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000309.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000310.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000311.png", "low_idx": 50}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-3|-4|2|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-4.29119112, -4.29119112, -8.4625368, -8.4625368, 3.868214, 3.868214]], "coordinateReceptacleObjectId": ["CounterTop", [0.932, 0.932, -8.008, -8.008, 3.796, 3.796]], "forceVisible": true, "objectId": "Egg|-01.07|+00.97|-02.12"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-1|-5|3|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-4.936, -4.936, -6.724, -6.724, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.23|+00.90|-01.68"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-3|-3|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-4.29119112, -4.29119112, -8.4625368, -8.4625368, 3.868214, 3.868214]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-5.98224832, -5.98224832, -2.812, -2.812, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|-01.07|+00.97|-02.12", "receptacleObjectId": "<PERSON><PERSON>|-01.50|+00.00|-00.70"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.07|+00.97|-02.12"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [207, 136, 223, 157], "mask": [[40714, 5], [41012, 9], [41311, 10], [41610, 12], [41910, 13], [42209, 14], [42509, 14], [42808, 16], [43108, 16], [43408, 16], [43707, 17], [44007, 17], [44307, 17], [44607, 17], [44908, 16], [45208, 15], [45508, 15], [45809, 13], [46109, 12], [46410, 10], [46712, 7], [47014, 2]], "point": [215, 145]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 85, 133, 186], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 134], [39300, 134], [39600, 134], [39900, 134], [40200, 134], [40500, 134], [40800, 134], [41100, 134], [41400, 134], [41700, 134], [42000, 134], [42300, 134], [42600, 134], [42900, 134], [43200, 134], [43500, 134], [43800, 134], [44100, 134], [44400, 134], [44700, 134], [45000, 134], [45300, 134], [45600, 134], [45900, 134], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54000, 118], [54300, 118], [54600, 117], [54900, 117], [55200, 117], [55577, 5]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.07|+00.97|-02.12", "placeStationary": true, "receptacleObjectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 85, 133, 185], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 63], [38165, 69], [38400, 60], [38468, 66], [38700, 58], [38770, 64], [39000, 57], [39071, 63], [39300, 56], [39372, 62], [39600, 56], [39672, 62], [39900, 55], [39973, 61], [40200, 9], [40216, 39], [40273, 61], [40500, 6], [40518, 36], [40573, 61], [40800, 3], [40819, 35], [40872, 62], [41100, 2], [41120, 34], [41172, 62], [41400, 1], [41421, 33], [41472, 62], [41721, 33], [41771, 63], [42021, 33], [42070, 64], [42321, 33], [42370, 64], [42621, 34], [42670, 64], [42921, 35], [42969, 65], [43220, 37], [43268, 66], [43519, 39], [43567, 67], [43818, 116], [44118, 116], [44417, 117], [44716, 118], [45015, 119], [45315, 119], [45600, 1], [45614, 120], [45900, 4], [45912, 122], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54087, 31], [54388, 30], [54688, 29], [54988, 29], [55288, 29]], "point": [68, 128]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 85, 133, 185], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 32], [37537, 97], [37800, 30], [37839, 95], [38100, 29], [38141, 22], [38165, 69], [38400, 28], [38442, 18], [38468, 66], [38700, 28], [38743, 15], [38770, 64], [39000, 27], [39043, 14], [39071, 63], [39300, 27], [39344, 12], [39372, 62], [39600, 27], [39645, 11], [39672, 62], [39900, 27], [39945, 10], [39973, 61], [40200, 9], [40216, 10], [40246, 9], [40273, 61], [40500, 6], [40518, 8], [40546, 8], [40573, 61], [40800, 3], [40819, 7], [40846, 8], [40872, 62], [41100, 2], [41120, 6], [41146, 8], [41172, 62], [41400, 1], [41421, 5], [41447, 7], [41472, 62], [41721, 6], [41747, 7], [41771, 63], [42021, 6], [42047, 7], [42070, 64], [42321, 6], [42346, 8], [42370, 64], [42621, 6], [42646, 9], [42670, 64], [42921, 7], [42946, 10], [42969, 65], [43220, 9], [43245, 12], [43268, 66], [43519, 10], [43545, 13], [43567, 67], [43818, 12], [43844, 90], [44118, 13], [44143, 91], [44417, 16], [44442, 92], [44716, 20], [44740, 94], [45015, 119], [45315, 119], [45600, 1], [45614, 120], [45900, 4], [45912, 122], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54087, 31], [54388, 30], [54688, 29], [54988, 29], [55288, 29]], "point": [68, 128]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-4.936, -4.936, -6.724, -6.724, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 85, 133, 186], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 134], [39300, 134], [39600, 134], [39900, 134], [40200, 134], [40500, 134], [40800, 134], [41100, 134], [41400, 134], [41700, 134], [42000, 134], [42300, 134], [42600, 134], [42900, 134], [43200, 134], [43500, 134], [43800, 134], [44100, 134], [44400, 134], [44700, 134], [45000, 134], [45300, 134], [45600, 134], [45900, 134], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54000, 118], [54300, 118], [54600, 117], [54900, 117], [55200, 117], [55577, 5]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-4.936, -4.936, -6.724, -6.724, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 85, 133, 186], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 134], [39300, 134], [39600, 134], [39900, 134], [40200, 134], [40500, 134], [40800, 134], [41100, 134], [41400, 134], [41700, 134], [42000, 134], [42300, 134], [42600, 134], [42900, 134], [43200, 134], [43500, 134], [43800, 134], [44100, 134], [44400, 134], [44700, 134], [45000, 134], [45300, 134], [45600, 134], [45900, 134], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54000, 118], [54300, 118], [54600, 117], [54900, 117], [55200, 117], [55577, 5]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 85, 133, 186], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 134], [39300, 134], [39600, 134], [39900, 134], [40200, 134], [40500, 134], [40800, 134], [41100, 134], [41400, 134], [41700, 134], [42000, 134], [42300, 134], [42600, 134], [42900, 134], [43200, 134], [43500, 134], [43800, 134], [44100, 134], [44400, 134], [44700, 134], [45000, 134], [45300, 134], [45600, 134], [45900, 134], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54000, 118], [54300, 118], [54600, 117], [54900, 117], [55200, 117], [55577, 5]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.07|+00.97|-02.12"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [26, 126, 46, 150], "mask": [[37532, 5], [37830, 9], [38129, 12], [38428, 14], [38728, 15], [39027, 16], [39327, 17], [39627, 18], [39927, 18], [40226, 20], [40526, 20], [40826, 20], [41126, 20], [41426, 21], [41727, 20], [42027, 20], [42327, 19], [42627, 19], [42928, 18], [43229, 16], [43529, 16], [43830, 14], [44131, 12], [44433, 9], [44736, 4]], "point": [36, 137]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 85, 133, 185], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 63], [38165, 69], [38400, 60], [38468, 66], [38700, 58], [38770, 64], [39000, 57], [39071, 63], [39300, 56], [39372, 62], [39600, 56], [39672, 62], [39900, 55], [39973, 61], [40200, 9], [40216, 39], [40273, 61], [40500, 6], [40518, 36], [40573, 61], [40800, 3], [40819, 35], [40872, 62], [41100, 2], [41120, 34], [41172, 62], [41400, 1], [41421, 33], [41472, 62], [41721, 33], [41771, 63], [42021, 33], [42070, 64], [42321, 33], [42370, 64], [42621, 34], [42670, 64], [42921, 35], [42969, 65], [43220, 37], [43268, 66], [43519, 39], [43567, 67], [43818, 116], [44118, 116], [44417, 117], [44716, 118], [45015, 119], [45315, 119], [45600, 1], [45614, 120], [45900, 4], [45912, 122], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54087, 31], [54388, 30], [54688, 29], [54988, 29], [55288, 29]], "point": [68, 128]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-01.50|+00.00|-00.70"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 235], "mask": [[0, 41700], [41701, 299], [42002, 298], [42303, 297], [42604, 296], [42905, 295], [43206, 294], [43507, 293], [43808, 292], [44109, 291], [44410, 290], [44710, 290], [45011, 289], [45312, 288], [45613, 287], [45914, 286], [46215, 285], [46516, 284], [46817, 283], [47118, 282], [47419, 281], [47720, 280], [48021, 279], [48322, 278], [48623, 277], [48923, 277], [49224, 276], [49525, 275], [49826, 274], [50127, 273], [50428, 272], [50729, 271], [51030, 270], [51331, 269], [51632, 268], [51933, 267], [52234, 265], [52535, 263], [52836, 261], [53136, 260], [53437, 257], [53738, 255], [54039, 253], [54340, 251], [54641, 249], [54942, 246], [55243, 244], [55544, 242], [55845, 240], [56146, 238], [56447, 235], [56748, 233], [57048, 232], [57349, 230], [57650, 228], [57951, 225], [58252, 223], [58553, 221], [58854, 219], [59155, 217], [59456, 214], [59757, 212], [60058, 210], [60359, 208], [60660, 206], [60961, 203], [61261, 202], [61562, 200], [61863, 198], [62164, 194], [62465, 189], [62766, 187], [63067, 185], [63368, 183], [63669, 181], [63970, 178], [64271, 176], [64572, 174], [64873, 172], [65174, 170], [65474, 169], [65775, 167], [66076, 164], [66377, 162], [66678, 160], [66979, 158], [67280, 156], [67582, 66], [67653, 81], [67888, 55], [67957, 71], [68193, 48], [68259, 64], [68499, 40], [68561, 56], [68805, 32], [68863, 48], [69111, 25], [69164, 41], [69417, 18], [69465, 34], [69723, 11], [69766, 26], [70032, 1], [70067, 16], [70368, 3]], "point": [149, 117]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.07|+00.97|-02.12", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-01.50|+00.00|-00.70"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 128], [209, 219], [509, 220], [809, 220], [1108, 221], [1408, 221], [1708, 222], [2007, 223], [2307, 224], [2607, 224], [2906, 226], [3206, 226], [3505, 228], [3805, 228], [4104, 230], [4403, 232], [4703, 232], [5002, 234], [5301, 236], [5600, 238], [5899, 240], [6199, 241], [6498, 243], [6797, 245], [7096, 248], [7395, 250], [7694, 252], [7993, 255], [8291, 258], [8590, 261], [8889, 264], [9187, 267], [9485, 273], [9783, 281], [10078, 33121], [43200, 298], [43500, 297], [43800, 296], [44100, 295], [44400, 295], [44700, 294], [45000, 293], [45300, 292], [45600, 291], [45900, 290], [46200, 289], [46500, 288], [46800, 287], [47100, 286], [47400, 285], [47700, 284], [48000, 283], [48300, 282], [48600, 281], [48900, 280], [49200, 279], [49500, 278], [49800, 277], [50100, 276], [50400, 275], [50700, 274], [51000, 274], [51300, 273], [51600, 272], [51900, 271], [52200, 270], [52500, 269], [52800, 268], [53100, 267], [53400, 266], [53700, 265], [54000, 264], [54300, 263], [54600, 262], [54900, 261], [55200, 260], [55500, 259], [55800, 258], [56100, 257], [56400, 256], [56700, 255], [57000, 254], [57300, 254], [57600, 253], [57900, 252], [58200, 251], [58500, 250], [58800, 249], [59100, 248], [59400, 247], [59700, 246], [60000, 245], [60300, 244], [60600, 243], [60900, 242], [61200, 241], [61500, 240], [61800, 239], [62100, 238], [62400, 237], [62700, 236], [63000, 235], [63300, 234], [63600, 233], [63900, 88], [64200, 88], [64500, 88], [64800, 88], [65100, 88], [65400, 88], [65700, 88], [66000, 88], [66300, 88], [66600, 88], [66900, 87], [67200, 87], [67500, 87], [67800, 87], [68100, 87], [68400, 87], [68700, 87], [69000, 87], [69300, 87], [69600, 86], [69900, 86], [70200, 86], [70500, 86], [70800, 86], [71100, 86], [71400, 86], [71700, 86], [72000, 85], [72300, 85], [72600, 85], [72900, 85], [73200, 85], [73500, 85], [73800, 85], [74100, 85], [74400, 84], [74700, 84], [75000, 84], [75300, 84], [75600, 84], [75900, 84], [76200, 84], [76500, 84], [76800, 84], [77100, 83], [77400, 83], [77700, 83], [78000, 83], [78300, 83], [78600, 83], [78900, 83], [79200, 83], [79500, 82], [79800, 82], [80100, 82], [80400, 82], [80700, 82], [81000, 82], [81300, 82], [81600, 82], [81900, 81], [82200, 81], [82500, 81], [82800, 81], [83100, 81], [83400, 81], [83700, 81], [84000, 81], [84300, 81], [84600, 80], [84900, 80], [85200, 80], [85500, 80], [85800, 80], [86100, 80], [86400, 80], [86700, 80], [87000, 79], [87300, 79], [87600, 79], [87900, 79], [88200, 79], [88500, 79], [88800, 79], [89100, 79], [89400, 79], [89700, 78]], "point": [149, 149]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-01.50|+00.00|-00.70"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 128], [209, 219], [509, 220], [809, 220], [1108, 221], [1408, 221], [1708, 222], [2007, 223], [2307, 224], [2607, 224], [2906, 226], [3206, 226], [3505, 228], [3805, 228], [4104, 230], [4403, 232], [4703, 232], [5002, 234], [5301, 236], [5600, 238], [5899, 240], [6199, 241], [6498, 243], [6797, 245], [7096, 248], [7395, 250], [7694, 252], [7993, 255], [8291, 258], [8590, 261], [8889, 264], [9187, 267], [9485, 273], [9783, 281], [10078, 15585], [25667, 293], [25969, 290], [26271, 286], [26572, 284], [26873, 283], [27173, 282], [27474, 280], [27774, 280], [28075, 278], [28375, 278], [28676, 277], [28976, 276], [29276, 276], [29576, 276], [29876, 276], [30176, 276], [30476, 276], [30776, 276], [31076, 277], [31376, 277], [31675, 278], [31975, 279], [32275, 279], [32574, 281], [32873, 282], [33172, 285], [33471, 287], [33770, 289], [34068, 9131], [43200, 298], [43500, 297], [43800, 296], [44100, 295], [44400, 295], [44700, 294], [45000, 293], [45300, 292], [45600, 291], [45900, 290], [46200, 289], [46500, 288], [46800, 287], [47100, 286], [47400, 285], [47700, 284], [48000, 283], [48300, 282], [48600, 281], [48900, 280], [49200, 279], [49500, 278], [49800, 277], [50100, 276], [50400, 275], [50700, 274], [51000, 274], [51300, 273], [51600, 272], [51900, 271], [52200, 270], [52500, 269], [52800, 268], [53100, 267], [53400, 266], [53700, 265], [54000, 264], [54300, 263], [54600, 262], [54900, 261], [55200, 260], [55500, 259], [55800, 258], [56100, 257], [56400, 256], [56700, 255], [57000, 254], [57300, 254], [57600, 253], [57900, 252], [58200, 251], [58500, 250], [58800, 249], [59100, 248], [59400, 247], [59700, 246], [60000, 245], [60300, 244], [60600, 243], [60900, 242], [61200, 241], [61500, 240], [61800, 239], [62100, 238], [62400, 237], [62700, 236], [63000, 235], [63300, 234], [63600, 233], [63900, 88], [64200, 88], [64500, 88], [64800, 88], [65100, 88], [65400, 88], [65700, 88], [66000, 88], [66300, 88], [66600, 88], [66900, 87], [67200, 87], [67500, 87], [67800, 87], [68100, 87], [68400, 87], [68700, 87], [69000, 87], [69300, 87], [69600, 86], [69900, 86], [70200, 86], [70500, 86], [70800, 86], [71100, 86], [71400, 86], [71700, 86], [72000, 85], [72300, 85], [72600, 85], [72900, 85], [73200, 85], [73500, 85], [73800, 85], [74100, 85], [74400, 84], [74700, 84], [75000, 84], [75300, 84], [75600, 84], [75900, 84], [76200, 84], [76500, 84], [76800, 84], [77100, 83], [77400, 83], [77700, 83], [78000, 83], [78300, 83], [78600, 83], [78900, 83], [79200, 83], [79500, 82], [79800, 82], [80100, 82], [80400, 82], [80700, 82], [81000, 82], [81300, 82], [81600, 82], [81900, 81], [82200, 81], [82500, 81], [82800, 81], [83100, 81], [83400, 81], [83700, 81], [84000, 81], [84300, 81], [84600, 80], [84900, 80], [85200, 80], [85500, 80], [85800, 80], [86100, 80], [86400, 80], [86700, 80], [87000, 79], [87300, 79], [87600, 79], [87900, 79], [88200, 79], [88500, 79], [88800, 79], [89100, 79], [89400, 79], [89700, 78]], "point": [149, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan20", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 1.0, "y": 0.9009992, "z": 1.75}, "object_poses": [{"objectName": "Potato_4e602a34", "position": {"x": -1.28486753, "y": 1.0447588, "z": -1.63809991}, "rotation": {"x": 0.0, "y": 89.99989, "z": 0.0}}, {"objectName": "Ladle_bd8f96dd", "position": {"x": 1.63150012, "y": 0.164887309, "z": -1.39772534}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}, {"objectName": "ButterKnife_8db17fc4", "position": {"x": 0.294566572, "y": 0.9269746, "z": 1.220139}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_33e0bb4d", "position": {"x": 0.689599156, "y": 0.9284999, "z": -2.002}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_33e0bb4d", "position": {"x": -0.0470661819, "y": 0.9456444, "z": 0.99143815}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_8bcb7893", "position": {"x": -1.07279778, "y": 0.9670535, "z": -2.1156342}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_8bcb7893", "position": {"x": -1.6488148, "y": 1.558238, "z": -0.392489284}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_5f9ea4fe", "position": {"x": -1.6105392, "y": 1.504903, "z": -0.9100078}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_d1e14b30", "position": {"x": 0.180688992, "y": 0.9340559, "z": 0.99143815}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d1e14b30", "position": {"x": 1.52849984, "y": 1.66340566, "z": -2.162786}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_416e93ae", "position": {"x": 0.180688992, "y": 0.9537251, "z": 0.534036636}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_416e93ae", "position": {"x": 0.06681141, "y": 0.9537251, "z": 0.7627374}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_de47916c", "position": {"x": -0.160943776, "y": 0.9787365, "z": 0.99143815}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_de47916c", "position": {"x": -0.137785524, "y": 0.816260159, "z": -1.93657041}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_11e2985b", "position": {"x": 0.294566572, "y": 0.929298162, "z": 0.30533576}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_11e2985b", "position": {"x": 1.82878232, "y": 0.9121536, "z": -1.79745913}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_725f4a28", "position": {"x": 0.522321761, "y": 0.9269507, "z": 0.076635}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_725f4a28", "position": {"x": 0.06681141, "y": 0.9269507, "z": 1.44883978}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_b4d69cc9", "position": {"x": -0.0470661819, "y": 0.972077966, "z": 0.30533576}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_0e2bb95a", "position": {"x": -0.617012143, "y": 0.106887877, "z": -1.87334752}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_792121da", "position": {"x": 1.91566885, "y": 0.157462955, "z": 0.121947385}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_4e602a34", "position": {"x": -1.21946859, "y": 1.0447588, "z": -1.8466866}, "rotation": {"x": 0.0, "y": 89.99989, "z": 0.0}}, {"objectName": "Bowl_980e4fc9", "position": {"x": -1.27728152, "y": 1.6562798, "z": -1.72999978}, "rotation": {"x": -1.40334208e-14, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_11e2985b", "position": {"x": 1.838592, "y": 1.65694022, "z": -1.11789453}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}, {"objectName": "Fork_a1e5049a", "position": {"x": 0.180688992, "y": 0.927481055, "z": 0.076635}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_bd8f96dd", "position": {"x": 0.444179654, "y": 1.70332849, "z": -2.16299129}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_b4d69cc9", "position": {"x": -1.49556208, "y": 1.22883, "z": -0.703}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Lettuce_aebda124", "position": {"x": 1.73830819, "y": 0.9946568, "z": -1.56350493}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_620f4eaa", "position": {"x": 0.522321761, "y": 0.9315732, "z": 0.99143815}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_33e0bb4d", "position": {"x": -0.11366187, "y": 0.9284998, "z": -1.71353471}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d1e14b30", "position": {"x": 1.56653309, "y": 0.556470037, "z": 2.53467369}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_725f4a28", "position": {"x": -0.9272271, "y": 0.909806132, "z": -2.151905}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_d7116d9f", "position": {"x": 1.5675, "y": 0.948799968, "z": -0.7576}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_5f9ea4fe", "position": {"x": 1.87379873, "y": 0.881049633, "z": 2.51076818}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_6ea32579", "position": {"x": -0.160943776, "y": 0.9232387, "z": 0.076635}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_bc4f8581", "position": {"x": 1.82486081, "y": 0.9453135, "z": -0.3461054}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8db17fc4", "position": {"x": 1.6181078, "y": 0.9135421, "z": -1.83489048}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_de47916c", "position": {"x": 0.522321761, "y": 0.9824485, "z": 0.30533576}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_c9c37476", "position": {"x": 1.25967884, "y": 0.8803952, "z": 2.4836}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_8bcb7893", "position": {"x": -1.495563, "y": 1.237238, "z": -0.3924888}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spoon_a85d92ad", "position": {"x": -0.06476809, "y": 0.765585542, "z": -2.0264}, "rotation": {"x": -1.40334191e-14, "y": 180.0, "z": 0.0}}, {"objectName": "Knife_416e93ae", "position": {"x": 0.06681141, "y": 0.9500131, "z": 1.220139}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_0e2bb95a", "position": {"x": 1.83363366, "y": 1.65263772, "z": -1.54437351}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}], "object_toggles": [], "random_seed": 453227515, "scene_num": 20}, "task_id": "trial_T20190907_224507_776787", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A23HZ18KTCK2DA_37TD41K0AKQ9E4749NIROY59IPHCST", "high_descs": ["Turn and go to the counter that is to the right of the sink.", "Pick up the egg that is on the counter to the right of the salt.", "With the egg in hand turn so you are facing the microwave that is to the right of you.", "Open the microwave door. Place the egg in the center of the microwave and shut the door.  Start the microwave when finished open the door and grab the egg.", "Go to the fridge with the egg in hand that is to your right.", "Open the fridge.  Place the egg in the center of the second shelf.  Close the door."], "task_desc": "Warm up an egg to put in the fridge.", "votes": [1, 1]}, {"assignment_id": "AD0NVUGLDYDYN_3HFNH7HEMKVAI08WJ5P5JPBNYTNQGR", "high_descs": ["turn right, walk to the counter by the sink", "grab the egg on the counter", "turn around, turn around to the microwave", "open the microwave, put the egg in,heat the egg, take the egg out", "walk to the right a little", "open the refrigerator, put the egg in"], "task_desc": "cook the egg in the microwave, put the egg in the refrigerator", "votes": [1, 1]}, {"assignment_id": "A1RLO9LNUJIW5S_3U5NZHP4LUJ3IK2S3YSY4KXE8X3PHN", "high_descs": ["Turn right and walk past the island to your right then when you get past it turn right and walk to the fridge then turn left and walk to the counter.", "Pick up the egg that's by the microwave.", "Position yourself so that you can use the microwave.", "Cook the egg in the microwave and then get it back out and close the door.", "Position yourself so that you can use the fridge now.", "Put the egg on the bottom shelf of the fridge and close the door."], "task_desc": "Put a microwaved egg in the fridge.", "votes": [1, 1]}]}}