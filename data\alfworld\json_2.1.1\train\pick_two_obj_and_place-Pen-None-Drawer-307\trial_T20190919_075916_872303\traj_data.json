{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000205.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000206.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000207.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000208.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000209.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000210.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000211.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000212.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000279.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000280.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000281.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000282.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000284.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000285.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000286.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000287.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000288.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000289.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000290.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000291.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000292.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000293.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000294.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000295.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000296.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000297.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000298.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000299.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000300.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000301.png", "low_idx": 39}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Pen", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|1|-7|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["pen"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Pen", [0.1704069376, 0.1704069376, -8.9477768, -8.9477768, 3.272648572, 3.272648572]], "coordinateReceptacleObjectId": ["SideTable", [0.984, 0.984, -9.524, -9.524, -0.012, -0.012]], "forceVisible": true, "objectId": "Pen|+00.04|+00.82|-02.24"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-5|-6|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["pen", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Pen", [0.1704069376, 0.1704069376, -8.9477768, -8.9477768, 3.272648572, 3.272648572]], "coordinateReceptacleObjectId": ["Drawer", [-5.626548, -5.626548, -9.2759, -9.2759, 2.9122844, 2.9122844]], "forceVisible": true, "objectId": "Pen|+00.04|+00.82|-02.24", "receptacleObjectId": "Drawer|-01.41|+00.73|-02.32"}}, {"discrete_action": {"action": "GotoLocation", "args": ["desk"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-6|-7|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["pen"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Pen", [-6.37938452, -6.37938452, -9.244, -9.244, 3.4707516, 3.4707516]], "coordinateReceptacleObjectId": ["Desk", [-3.4770208, -3.4770208, -9.744, -9.744, -0.02, -0.02]], "forceVisible": true, "objectId": "Pen|-01.59|+00.87|-02.31"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-5|-6|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["pen", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Pen", [-6.37938452, -6.37938452, -9.244, -9.244, 3.4707516, 3.4707516]], "coordinateReceptacleObjectId": ["Drawer", [-5.626548, -5.626548, -9.2759, -9.2759, 2.9122844, 2.9122844]], "forceVisible": true, "objectId": "Pen|-01.59|+00.87|-02.31", "receptacleObjectId": "Drawer|-01.41|+00.73|-02.32"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Pen|+00.04|+00.82|-02.24"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [206, 116, 213, 162], "mask": [[34706, 1], [35006, 1], [35306, 2], [35606, 2], [35906, 2], [36206, 2], [36506, 3], [36806, 3], [37106, 3], [37406, 3], [37707, 2], [38007, 2], [38307, 2], [38607, 3], [38907, 3], [39207, 3], [39507, 3], [39808, 2], [40108, 2], [40408, 2], [40708, 2], [41008, 3], [41308, 3], [41608, 3], [41908, 3], [42209, 2], [42509, 2], [42809, 2], [43109, 3], [43409, 3], [43709, 3], [44009, 3], [44310, 2], [44609, 3], [44909, 3], [45209, 4], [45509, 4], [45810, 3], [46110, 3], [46410, 3], [46710, 3], [47010, 3], [47310, 4], [47611, 2], [47912, 1], [48212, 1], [48512, 1]], "point": [209, 138]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-01.41|+00.73|-02.32"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [95, 171, 285, 198], "mask": [[51095, 191], [51395, 190], [51696, 189], [51996, 188], [52296, 188], [52596, 187], [52896, 186], [53197, 185], [53497, 184], [53797, 184], [54097, 183], [54398, 182], [54698, 181], [54998, 180], [55298, 180], [55598, 179], [55899, 178], [56199, 177], [56499, 177], [56799, 176], [57100, 174], [57400, 174], [57700, 173], [58000, 173], [58301, 171], [58601, 171], [58901, 170], [59201, 169]], "point": [190, 183]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Pen|+00.04|+00.82|-02.24", "placeStationary": true, "receptacleObjectId": "Drawer|-01.41|+00.73|-02.32"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [80, 171, 299, 267], "mask": [[51097, 189], [51397, 189], [51696, 191], [51996, 191], [52296, 192], [52596, 192], [52896, 192], [53195, 194], [53495, 194], [53795, 195], [54095, 195], [54395, 196], [54694, 197], [54994, 198], [55294, 198], [55594, 199], [55894, 199], [56193, 201], [56493, 201], [56793, 202], [57093, 202], [57393, 203], [57693, 203], [57992, 205], [58292, 205], [58592, 206], [58892, 206], [59192, 207], [59491, 208], [59791, 209], [60091, 209], [60391, 209], [60691, 209], [60990, 210], [61290, 49], [61352, 148], [61590, 49], [61653, 147], [61890, 49], [61953, 147], [62190, 49], [62253, 147], [62490, 49], [62553, 147], [62789, 50], [62853, 147], [63089, 50], [63153, 147], [63389, 50], [63453, 147], [63689, 50], [63753, 147], [63989, 50], [64053, 147], [64288, 51], [64353, 147], [64588, 51], [64653, 147], [64888, 50], [64953, 147], [65188, 50], [65253, 147], [65488, 50], [65553, 147], [65787, 51], [65853, 147], [66087, 51], [66153, 147], [66387, 51], [66453, 147], [66687, 51], [66753, 147], [66987, 51], [67053, 147], [67286, 52], [67353, 147], [67586, 52], [67653, 147], [67886, 52], [67953, 147], [68186, 52], [68253, 147], [68486, 52], [68553, 147], [68786, 52], [68853, 147], [69085, 53], [69153, 147], [69385, 53], [69453, 147], [69685, 53], [69753, 147], [69985, 53], [70053, 147], [70285, 53], [70353, 147], [70584, 54], [70653, 147], [70884, 216], [71184, 216], [71480, 220], [71780, 220], [72080, 220], [72380, 220], [72680, 220], [72981, 219], [73281, 219], [73581, 219], [73882, 218], [74182, 218], [74483, 217], [74783, 217], [75083, 217], [75384, 216], [75684, 216], [75985, 215], [76285, 215], [76586, 12], [76630, 170], [76886, 12], [76900, 200], [77186, 12], [77312, 88], [77487, 2], [77616, 84], [77787, 2], [77920, 80], [78088, 1], [78219, 81], [78388, 9], [78515, 85], [78688, 9], [78811, 89], [78989, 211], [79289, 211], [79590, 209], [79890, 208]], "point": [189, 218]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-01.41|+00.73|-02.32"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [80, 171, 299, 267], "mask": [[51097, 189], [51397, 189], [51696, 191], [51996, 191], [52296, 192], [52596, 192], [52896, 192], [53195, 194], [53495, 194], [53795, 195], [54095, 195], [54395, 196], [54694, 197], [54994, 198], [55294, 198], [55594, 199], [55894, 199], [56193, 201], [56493, 201], [56793, 202], [57093, 202], [57393, 203], [57693, 203], [57992, 205], [58292, 205], [58592, 206], [58892, 206], [59192, 207], [59491, 208], [59791, 209], [60091, 209], [60391, 209], [60691, 32], [60764, 136], [60990, 33], [61064, 136], [61290, 49], [61352, 1], [61364, 136], [61590, 49], [61653, 147], [61890, 49], [61953, 147], [62190, 49], [62253, 147], [62490, 49], [62553, 147], [62789, 50], [62853, 147], [63089, 50], [63153, 147], [63389, 50], [63453, 147], [63689, 50], [63753, 147], [63989, 50], [64053, 147], [64288, 51], [64353, 147], [64588, 51], [64653, 147], [64888, 50], [64953, 147], [65188, 50], [65253, 147], [65488, 50], [65553, 147], [65787, 51], [65853, 147], [66087, 51], [66153, 147], [66387, 51], [66453, 147], [66687, 51], [66753, 147], [66987, 51], [67053, 147], [67286, 52], [67353, 147], [67586, 52], [67653, 147], [67886, 52], [67953, 147], [68186, 52], [68253, 147], [68486, 52], [68553, 147], [68786, 52], [68853, 147], [69085, 53], [69153, 147], [69385, 53], [69453, 147], [69685, 53], [69753, 147], [69985, 53], [70053, 147], [70285, 53], [70353, 147], [70584, 54], [70653, 147], [70884, 216], [71184, 216], [71480, 220], [71780, 220], [72080, 220], [72380, 220], [72680, 220], [72981, 219], [73281, 219], [73581, 219], [73882, 218], [74182, 218], [74483, 217], [74783, 217], [75083, 217], [75384, 216], [75684, 216], [75985, 215], [76285, 215], [76586, 214], [76886, 214], [77186, 214], [77487, 213], [77787, 213], [78088, 212], [78388, 212], [78688, 212], [78989, 211], [79289, 211], [79590, 209], [79890, 208]], "point": [189, 218]}}, "high_idx": 3}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Pen|-01.59|+00.87|-02.31"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [149, 113, 202, 115], "mask": [[33749, 54], [34050, 50], [34386, 13]], "point": [175, 113]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-01.41|+00.73|-02.32"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [95, 171, 285, 198], "mask": [[51095, 191], [51395, 190], [51696, 189], [51996, 188], [52296, 188], [52596, 187], [52896, 186], [53197, 185], [53497, 184], [53797, 184], [54097, 183], [54398, 182], [54698, 181], [54998, 180], [55298, 180], [55598, 179], [55899, 178], [56199, 177], [56499, 177], [56799, 176], [57100, 174], [57400, 174], [57700, 173], [58000, 173], [58301, 171], [58601, 171], [58901, 170], [59201, 169]], "point": [190, 183]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Pen|-01.59|+00.87|-02.31", "placeStationary": true, "receptacleObjectId": "Drawer|-01.41|+00.73|-02.32"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [80, 171, 299, 267], "mask": [[51097, 189], [51397, 189], [51696, 191], [51996, 191], [52296, 192], [52596, 192], [52896, 192], [53195, 194], [53495, 194], [53795, 195], [54095, 195], [54395, 196], [54694, 197], [54994, 198], [55294, 198], [55594, 199], [55894, 199], [56193, 201], [56493, 201], [56793, 202], [57093, 202], [57393, 203], [57693, 203], [57992, 205], [58292, 205], [58592, 206], [58892, 206], [59192, 207], [59491, 208], [59791, 209], [60091, 209], [60391, 209], [60691, 209], [60990, 50], [61047, 153], [61290, 49], [61353, 147], [61590, 49], [61653, 147], [61890, 49], [61953, 147], [62190, 49], [62253, 147], [62490, 49], [62553, 147], [62789, 50], [62853, 147], [63089, 50], [63153, 147], [63389, 50], [63453, 147], [63689, 50], [63753, 147], [63989, 50], [64053, 147], [64288, 51], [64353, 147], [64588, 51], [64653, 147], [64888, 51], [64953, 147], [65188, 51], [65253, 147], [65488, 51], [65553, 147], [65787, 51], [65853, 147], [66087, 51], [66153, 147], [66387, 51], [66453, 147], [66687, 51], [66753, 147], [66987, 51], [67053, 147], [67286, 52], [67353, 147], [67586, 52], [67653, 147], [67886, 52], [67953, 147], [68186, 52], [68253, 147], [68486, 52], [68553, 147], [68786, 52], [68853, 147], [69085, 53], [69153, 147], [69385, 53], [69453, 147], [69685, 53], [69753, 147], [69985, 53], [70053, 147], [70285, 53], [70353, 147], [70584, 54], [70653, 147], [70884, 216], [71184, 216], [71480, 220], [71780, 220], [72080, 220], [72380, 220], [72680, 220], [72981, 219], [73281, 219], [73581, 219], [73882, 218], [74182, 218], [74483, 217], [74783, 217], [75083, 217], [75384, 216], [75684, 216], [75985, 215], [76285, 215], [76586, 12], [76630, 170], [76886, 12], [76900, 200], [77186, 12], [77312, 88], [77487, 2], [77616, 84], [77787, 2], [77920, 80], [78088, 1], [78219, 81], [78388, 9], [78515, 85], [78688, 9], [78811, 89], [78989, 211], [79289, 211], [79590, 209], [79890, 208]], "point": [189, 218]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-01.41|+00.73|-02.32"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [80, 171, 299, 267], "mask": [[51097, 189], [51397, 189], [51696, 191], [51996, 191], [52296, 192], [52596, 192], [52896, 192], [53195, 194], [53495, 194], [53795, 195], [54095, 195], [54395, 196], [54694, 197], [54994, 198], [55294, 198], [55594, 199], [55894, 199], [56193, 201], [56493, 201], [56793, 202], [57093, 202], [57393, 203], [57693, 203], [57992, 205], [58292, 33], [58364, 133], [58592, 30], [58667, 131], [58892, 61], [58964, 134], [59192, 207], [59491, 208], [59791, 209], [60091, 209], [60391, 209], [60691, 209], [60990, 50], [61048, 152], [61290, 49], [61353, 147], [61590, 49], [61653, 147], [61890, 49], [61953, 147], [62190, 49], [62253, 147], [62490, 49], [62553, 147], [62789, 50], [62853, 147], [63089, 50], [63153, 147], [63389, 50], [63453, 147], [63689, 50], [63753, 147], [63989, 50], [64053, 147], [64288, 51], [64353, 147], [64588, 51], [64653, 147], [64888, 51], [64953, 147], [65188, 51], [65253, 147], [65488, 50], [65553, 147], [65787, 51], [65853, 147], [66087, 51], [66153, 147], [66387, 51], [66453, 147], [66687, 51], [66753, 147], [66987, 51], [67053, 147], [67286, 52], [67353, 147], [67586, 52], [67653, 147], [67886, 52], [67953, 147], [68186, 52], [68253, 147], [68486, 52], [68553, 147], [68786, 52], [68853, 147], [69085, 53], [69153, 147], [69385, 53], [69453, 147], [69685, 53], [69753, 147], [69985, 53], [70053, 147], [70285, 53], [70353, 147], [70584, 54], [70653, 147], [70884, 216], [71184, 216], [71480, 220], [71780, 220], [72080, 220], [72380, 220], [72680, 220], [72981, 219], [73281, 219], [73581, 219], [73882, 218], [74182, 218], [74483, 217], [74783, 217], [75083, 217], [75384, 216], [75684, 216], [75985, 215], [76285, 215], [76586, 214], [76886, 214], [77186, 214], [77487, 213], [77787, 213], [78088, 212], [78388, 212], [78688, 212], [78989, 211], [79289, 211], [79590, 209], [79890, 208]], "point": [189, 218]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan307", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": 0.0, "y": 0.9009995, "z": -1.25}, "object_poses": [{"objectName": "Bowl_67240dec", "position": {"x": -0.990618467, "y": 0.8631568, "z": -2.3735}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_2830e300", "position": {"x": -1.59484613, "y": 0.8676879, "z": -2.311}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_2830e300", "position": {"x": 0.459425867, "y": 0.818162143, "z": -2.50782871}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pencil_6cfeefd2", "position": {"x": -0.5878, "y": 0.8655968, "z": -2.2485}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pencil_6cfeefd2", "position": {"x": 0.459425867, "y": 0.815596759, "z": -2.30466533}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_a5a087c0", "position": {"x": -1.31956244, "y": 0.0880175158, "z": -2.4013288}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_a5a087c0", "position": {"x": 1.494799, "y": 0.8238078, "z": -1.77626562}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Book_d16fbaa0", "position": {"x": -1.49371147, "y": 0.08777916, "z": -2.325591}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "RemoteControl_7edfc11d", "position": {"x": -1.232488, "y": 0.6538168, "z": -2.28772235}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "RemoteControl_7edfc11d", "position": {"x": -0.0616043061, "y": 0.8138728, "z": -2.37238646}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_c51d9cc5", "position": {"x": 0.241252989, "y": 0.772767067, "z": 1.49655974}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "AlarmClock_6316eacc", "position": {"x": 0.3552198, "y": 0.8126922, "z": -2.30466533}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "AlarmClock_6316eacc", "position": {"x": 1.46703279, "y": 0.824504, "z": -1.67319643}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Book_d16fbaa0", "position": {"x": -1.39343691, "y": 0.8617927, "z": -2.4985}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cloth_18622ea7", "position": {"x": 1.30397391, "y": 0.0, "z": -0.6962366}, "rotation": {"x": 0.0, "y": 303.21286, "z": 0.0}}, {"objectName": "Cloth_f66086cd", "position": {"x": 1.27369571, "y": 0.0, "z": -0.969285965}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_3bc3970e", "position": {"x": -1.19202769, "y": 0.861944556, "z": -2.436}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pencil_6cfeefd2", "position": {"x": 0.14680776, "y": 0.815596759, "z": -2.30466533}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_c51d9cc5", "position": {"x": 0.241252989, "y": 0.772767067, "z": 1.81939971}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "AlarmClock_6316eacc", "position": {"x": -0.0616043061, "y": 0.8126922, "z": -2.2369442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_a5a087c0", "position": {"x": -1.58078587, "y": 0.6521884, "z": -2.28772235}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "KeyChain_9a139d9d", "position": {"x": -0.9968543, "y": 1.36302161, "z": -2.468409}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "TennisRacket_feb6b631", "position": {"x": -1.704, "y": 0.345, "z": 1.743}, "rotation": {"x": 345.000366, "y": 90.000145, "z": 180.000046}}, {"objectName": "RemoteControl_7edfc11d", "position": {"x": -1.406637, "y": 0.3733847, "z": -2.28772235}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pillow_63f56a9a", "position": {"x": 1.073, "y": 0.876, "z": 1.326}, "rotation": {"x": 0.0, "y": 262.06308, "z": 0.0}}, {"objectName": "CD_c74391cb", "position": {"x": 1.52155089, "y": 0.817598641, "z": -1.72783685}, "rotation": {"x": -1.017339e-06, "y": -3.58584839e-05, "z": 1.37082361e-06}}, {"objectName": "CD_dac1567f", "position": {"x": 0.527577341, "y": 0.8067264, "z": -2.4859283}, "rotation": {"x": -1.99666388e-07, "y": -9.220754e-05, "z": -1.50412529e-06}}, {"objectName": "CD_1430725f", "position": {"x": -1.19202769, "y": 0.8616067, "z": -2.2485}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_2830e300", "position": {"x": 0.0426017344, "y": 0.818162143, "z": -2.2369442}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_67240dec", "position": {"x": -0.3863908, "y": 0.8631568, "z": -2.4985}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1316644550, "scene_num": 307}, "task_id": "trial_T20190919_075916_872303", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A36A780ODXBDEH_3PPTZCWALTBIU6PZ552B5IBV397ZQS", "high_descs": ["Turn around and go to the small desk with the lamp on it.", "Pick up the pen to the left of the clock nearest to you.", "Turn around, turn left and then face the large desk against the wall on the right.", "Open the top desk drawer and put the pen inside the drawer.", "Turn right and get closer to the large desk.", "Pick up the pen on this large desk.", "Back away from the desk to face the desk drawers.", "Place this pen inside the top desk drawer."], "task_desc": "Move the pens from the small desk and the large desk into the top large desk drawer.", "votes": [1, 1]}, {"assignment_id": "A1CY7IOJ9YH136_3VE8AYVF8PO6JLY3IZR4GZADXZK8FX", "high_descs": ["turn left, go to table to the left with two clocks", "pick up black pen to the left of clock on the right", "turn right, go to right of chair at desk to the left", "open drawer to the right of desk chair, place pen behind remote in drawer, close drawer", "look up to desk with CD", "pick up black pen from desk", "go to desk drawer to the right of chair", "open drawer to the right of desk chair, place pen behind remote in drawer, close drawer"], "task_desc": "place two black pens in desk drawer", "votes": [1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_3WS1NTTKE13O1WT0NVEPNK4VE0PF0W", "high_descs": ["turn left and walk to the small desk to the left of the bigger desk on the right", "grab a pencil off of the small desk", "move to the right side of the room to face the right side of the larger desk", "place the pencil inside the pullout drawer on the right side of the desk", "turn around to back up a bit from the desk", "grab the pencil off of the desk", "turn around to back up a bit further from the desk", "place the pencil inside of the same pullout drawer you put the other pencil in"], "task_desc": "place two pencils inside of the pullout drawer of the right side larger desk", "votes": [1, 1]}]}}