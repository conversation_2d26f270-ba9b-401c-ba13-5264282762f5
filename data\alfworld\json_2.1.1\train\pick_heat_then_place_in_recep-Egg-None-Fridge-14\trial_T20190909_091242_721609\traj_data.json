{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000091.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000092.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000093.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000094.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000095.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000096.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000097.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000098.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000099.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000100.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000101.png", "low_idx": 28}, {"high_idx": 0, "image_name": "000000102.png", "low_idx": 28}, {"high_idx": 0, "image_name": "000000103.png", "low_idx": 28}, {"high_idx": 0, "image_name": "000000104.png", "low_idx": 28}, {"high_idx": 0, "image_name": "000000105.png", "low_idx": 28}, {"high_idx": 0, "image_name": "000000106.png", "low_idx": 28}, {"high_idx": 0, "image_name": "000000107.png", "low_idx": 28}, {"high_idx": 0, "image_name": "000000108.png", "low_idx": 28}, {"high_idx": 0, "image_name": "000000109.png", "low_idx": 28}, {"high_idx": 0, "image_name": "000000110.png", "low_idx": 28}, {"high_idx": 0, "image_name": "000000111.png", "low_idx": 28}, {"high_idx": 0, "image_name": "000000112.png", "low_idx": 29}, {"high_idx": 0, "image_name": "000000113.png", "low_idx": 29}, {"high_idx": 0, "image_name": "000000114.png", "low_idx": 30}, {"high_idx": 0, "image_name": "000000115.png", "low_idx": 30}, {"high_idx": 0, "image_name": "000000116.png", "low_idx": 31}, {"high_idx": 0, "image_name": "000000117.png", "low_idx": 31}, {"high_idx": 0, "image_name": "000000118.png", "low_idx": 32}, {"high_idx": 0, "image_name": "000000119.png", "low_idx": 32}, {"high_idx": 1, "image_name": "000000120.png", "low_idx": 33}, {"high_idx": 1, "image_name": "000000121.png", "low_idx": 33}, {"high_idx": 1, "image_name": "000000122.png", "low_idx": 33}, {"high_idx": 1, "image_name": "000000123.png", "low_idx": 33}, {"high_idx": 1, "image_name": "000000124.png", "low_idx": 33}, {"high_idx": 1, "image_name": "000000125.png", "low_idx": 33}, {"high_idx": 1, "image_name": "000000126.png", "low_idx": 33}, {"high_idx": 1, "image_name": "000000127.png", "low_idx": 33}, {"high_idx": 1, "image_name": "000000128.png", "low_idx": 33}, {"high_idx": 1, "image_name": "000000129.png", "low_idx": 33}, {"high_idx": 1, "image_name": "000000130.png", "low_idx": 33}, {"high_idx": 1, "image_name": "000000131.png", "low_idx": 33}, {"high_idx": 1, "image_name": "000000132.png", "low_idx": 33}, {"high_idx": 1, "image_name": "000000133.png", "low_idx": 33}, {"high_idx": 1, "image_name": "000000134.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000272.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000273.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000274.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000275.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000276.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000277.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000278.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000279.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000280.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000281.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000282.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000283.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000284.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000285.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000286.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000287.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000288.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000289.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000290.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000291.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000292.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000293.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000294.png", "low_idx": 55}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|4|-3|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [4.12867736, 4.12867736, -6.61911916, -6.61911916, 3.182439804, 3.182439804]], "coordinateReceptacleObjectId": ["SinkBasin", [4.3652, 4.3652, -6.398, -6.398, 2.964, 2.964]], "forceVisible": true, "objectId": "Egg|+01.03|+00.80|-01.65"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|-3|1|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [7.008, 7.008, -3.376, -3.376, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.75|+00.90|-00.84"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-2|-3|2|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [4.12867736, 4.12867736, -6.61911916, -6.61911916, 3.182439804, 3.182439804]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-2.136, -2.136, -6.216, -6.216, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|+01.03|+00.80|-01.65", "receptacleObjectId": "<PERSON><PERSON>|-00.53|+00.00|-01.55"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+01.03|+00.80|-01.65"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [137, 126, 148, 140], "mask": [[37640, 5], [37939, 7], [38238, 9], [38538, 10], [38837, 11], [39137, 11], [39437, 12], [39737, 12], [40037, 12], [40337, 12], [40637, 11], [40938, 10], [41239, 8], [41540, 6], [41842, 2]], "point": [142, 132]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [55, 26, 299, 160], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20457, 243], [20757, 243], [21057, 243], [21358, 242], [21658, 242], [21958, 242], [22258, 242], [22559, 241], [22859, 241], [23159, 241], [23459, 241], [23760, 240], [24060, 240], [24360, 240], [24661, 239], [24961, 239], [25261, 239], [25561, 239], [25862, 238], [26162, 238], [26462, 238], [26762, 238], [27063, 237], [27363, 237], [27663, 237], [27964, 236], [28264, 236], [28564, 236], [28864, 236], [29165, 235], [29465, 234], [29765, 234], [30066, 232], [30366, 232], [30666, 231], [30966, 231], [31267, 229], [31567, 229], [31867, 228], [32167, 228], [32468, 226], [32768, 226], [33068, 225], [33369, 224], [33669, 223], [33969, 223], [34269, 223], [34570, 221], [34870, 221], [35170, 220], [35470, 220], [35771, 218], [36071, 218], [36371, 217], [36672, 216], [36972, 215], [37272, 215], [37572, 214], [37873, 213], [38173, 212], [38473, 212], [38773, 211], [39074, 210], [39374, 209], [39674, 209], [39975, 207], [40275, 207], [40575, 206], [40875, 206], [41176, 204], [41476, 204], [41776, 204], [42076, 203], [42377, 202], [42677, 201], [42977, 201], [43278, 199], [43578, 199], [43878, 198], [44178, 198], [44485, 189], [44786, 187], [45086, 187], [45386, 186], [45686, 186], [45987, 184], [46287, 184], [46587, 183], [46887, 183], [47188, 182], [47488, 181], [47789, 180]], "point": [177, 92]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+01.03|+00.80|-01.65", "placeStationary": true, "receptacleObjectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 26, 299, 300], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20456, 244], [20756, 244], [21055, 245], [21355, 245], [21654, 246], [21954, 246], [22253, 247], [22553, 247], [22852, 248], [23152, 248], [23451, 249], [23751, 249], [24050, 250], [24349, 251], [24649, 251], [24948, 137], [25093, 107], [25248, 135], [25395, 105], [25547, 135], [25696, 104], [25847, 134], [25997, 103], [26146, 134], [26298, 102], [26446, 134], [26599, 101], [26745, 134], [26900, 100], [27045, 134], [27200, 100], [27344, 134], [27501, 99], [27644, 134], [27802, 98], [27943, 135], [28103, 97], [28243, 135], [28403, 97], [28542, 135], [28703, 97], [28842, 135], [29003, 97], [29141, 136], [29304, 96], [29441, 136], [29604, 95], [29740, 136], [29904, 95], [30039, 137], [30204, 94], [30339, 138], [30503, 95], [30638, 139], [30803, 94], [30938, 139], [31103, 94], [31237, 141], [31403, 93], [31537, 141], [31703, 93], [31836, 143], [32002, 93], [32136, 143], [32302, 93], [32435, 146], [32602, 92], [32735, 147], [32901, 93], [33034, 149], [33200, 93], [33334, 151], [33499, 94], [33633, 153], [33798, 94], [33933, 155], [34096, 96], [34232, 260], [34532, 259], [34831, 260], [35131, 259], [35430, 260], [35730, 259], [36029, 260], [36328, 260], [36628, 260], [36927, 260], [37227, 260], [37526, 260], [37826, 260], [38125, 260], [38425, 260], [38724, 260], [39024, 260], [39323, 260], [39623, 260], [39922, 260], [40222, 260], [40521, 260], [40821, 260], [41120, 260], [41420, 260], [41719, 261], [42018, 261], [42318, 261], [42617, 261], [42917, 261], [43216, 261], [43516, 261], [43815, 261], [44115, 261], [44414, 260], [44714, 259], [45013, 260], [45313, 79], [45531, 41], [45612, 80], [45831, 41], [45912, 80], [46132, 39], [46211, 81], [46432, 39], [46511, 80], [46732, 38], [46810, 81], [47033, 37], [47110, 81], [47333, 37], [47409, 82], [47633, 36], [47708, 82], [47934, 35], [48008, 82], [48307, 83], [48607, 83], [48906, 84], [49206, 83], [49505, 84], [49805, 84], [50104, 85], [50404, 84], [50703, 85], [51003, 85], [51302, 86], [51602, 86], [51901, 86], [52201, 86], [52500, 87], [52800, 87], [53100, 86], [53400, 86], [53700, 86], [54000, 86], [54300, 86], [54600, 85], [54900, 85], [55200, 85], [55500, 85], [55800, 84], [56100, 84], [56400, 84], [56700, 84], [57000, 84], [57300, 83], [57600, 83], [57900, 83], [58200, 83], [58500, 83], [58800, 82], [59100, 82], [59400, 82], [59700, 82], [60000, 81], [60300, 81], [60600, 81], [60900, 81], [61200, 81], [61500, 80], [61800, 80], [62100, 80], [62400, 80], [62700, 79], [63000, 79], [63300, 79], [63600, 79], [63900, 79], [64200, 78], [64500, 78], [64800, 78], [65100, 78], [65400, 77], [65700, 77], [66000, 77], [66300, 77], [66600, 77], [66900, 76], [67200, 76], [67500, 76], [67800, 76], [68100, 75], [68400, 75], [68700, 75], [69000, 75], [69300, 75], [69600, 74], [69900, 74], [70200, 74], [70500, 74], [70800, 73], [71100, 73], [71400, 73], [71700, 73], [72000, 73], [72300, 72], [72600, 72], [72900, 72], [73200, 72], [73500, 71], [73800, 71], [74100, 71], [74400, 71], [74700, 71], [75000, 70], [75300, 70], [75600, 70], [75900, 70], [76200, 69], [76500, 69], [76800, 69], [77100, 69], [77400, 69], [77700, 68], [78000, 68], [78300, 68], [78600, 68], [78900, 67], [79200, 67], [79500, 67], [79800, 67], [80100, 67], [80400, 66], [80700, 66], [81000, 66], [81300, 66], [81600, 65], [81900, 65], [82200, 65], [82500, 65], [82800, 65], [83100, 64], [83402, 62], [83703, 61], [84004, 60], [84305, 58], [84606, 57], [84907, 56], [85208, 55], [85509, 54], [85811, 51], [86112, 50], [86413, 49], [86714, 48], [87015, 47], [87316, 45], [87617, 44], [87919, 42], [88220, 41], [88521, 39], [88822, 39], [89125, 38], [89429, 36], [89730, 36]], "point": [149, 150]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 26, 299, 300], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20456, 244], [20756, 244], [21055, 245], [21355, 245], [21654, 246], [21954, 246], [22253, 247], [22553, 247], [22852, 248], [23152, 248], [23451, 249], [23751, 249], [24050, 250], [24349, 251], [24649, 251], [24948, 137], [25093, 107], [25248, 135], [25395, 105], [25547, 135], [25696, 104], [25847, 134], [25997, 103], [26146, 134], [26298, 102], [26446, 134], [26599, 101], [26745, 134], [26900, 100], [27045, 134], [27200, 100], [27344, 134], [27501, 99], [27644, 134], [27802, 98], [27943, 135], [28103, 97], [28243, 135], [28403, 97], [28542, 135], [28703, 97], [28842, 135], [29003, 97], [29141, 136], [29304, 96], [29441, 136], [29604, 95], [29740, 136], [29904, 95], [30039, 137], [30204, 94], [30339, 138], [30503, 95], [30638, 139], [30803, 94], [30938, 139], [31103, 94], [31237, 141], [31403, 93], [31537, 141], [31703, 93], [31836, 143], [32002, 93], [32136, 104], [32246, 33], [32302, 93], [32435, 104], [32548, 33], [32602, 92], [32735, 103], [32849, 33], [32901, 93], [33034, 103], [33149, 34], [33200, 93], [33334, 103], [33450, 35], [33499, 94], [33633, 103], [33751, 35], [33798, 94], [33933, 103], [34051, 37], [34096, 96], [34232, 104], [34351, 141], [34532, 103], [34652, 139], [34831, 104], [34952, 139], [35131, 104], [35252, 138], [35430, 105], [35552, 138], [35730, 105], [35852, 137], [36029, 106], [36152, 137], [36328, 108], [36452, 136], [36628, 108], [36751, 137], [36927, 109], [37051, 136], [37227, 110], [37350, 137], [37526, 112], [37649, 137], [37826, 113], [37949, 137], [38125, 115], [38247, 138], [38425, 260], [38724, 260], [39024, 260], [39323, 260], [39623, 260], [39922, 260], [40222, 260], [40521, 260], [40821, 260], [41120, 260], [41420, 260], [41719, 261], [42018, 261], [42318, 261], [42617, 261], [42917, 261], [43216, 261], [43516, 261], [43815, 261], [44115, 261], [44414, 260], [44714, 259], [45013, 260], [45313, 79], [45531, 41], [45612, 80], [45831, 41], [45912, 80], [46132, 39], [46211, 81], [46432, 39], [46511, 80], [46732, 38], [46810, 81], [47033, 37], [47110, 81], [47333, 37], [47409, 82], [47633, 36], [47708, 82], [47934, 35], [48008, 82], [48307, 83], [48607, 83], [48906, 84], [49206, 83], [49505, 84], [49805, 84], [50104, 85], [50404, 84], [50703, 85], [51003, 85], [51302, 86], [51602, 86], [51901, 86], [52201, 86], [52500, 87], [52800, 87], [53100, 86], [53400, 86], [53700, 86], [54000, 86], [54300, 86], [54600, 85], [54900, 85], [55200, 85], [55500, 85], [55800, 84], [56100, 84], [56400, 84], [56700, 84], [57000, 84], [57300, 83], [57600, 83], [57900, 83], [58200, 83], [58500, 83], [58800, 82], [59100, 82], [59400, 82], [59700, 82], [60000, 81], [60300, 81], [60600, 81], [60900, 81], [61200, 81], [61500, 80], [61800, 80], [62100, 80], [62400, 80], [62700, 79], [63000, 79], [63300, 79], [63600, 79], [63900, 79], [64200, 78], [64500, 78], [64800, 78], [65100, 78], [65400, 77], [65700, 77], [66000, 77], [66300, 77], [66600, 77], [66900, 76], [67200, 76], [67500, 76], [67800, 76], [68100, 75], [68400, 75], [68700, 75], [69000, 75], [69300, 75], [69600, 74], [69900, 74], [70200, 74], [70500, 74], [70800, 73], [71100, 73], [71400, 73], [71700, 73], [72000, 73], [72300, 72], [72600, 72], [72900, 72], [73200, 72], [73500, 71], [73800, 71], [74100, 71], [74400, 71], [74700, 71], [75000, 70], [75300, 70], [75600, 70], [75900, 70], [76200, 69], [76500, 69], [76800, 69], [77100, 69], [77400, 69], [77700, 68], [78000, 68], [78300, 68], [78600, 68], [78900, 67], [79200, 67], [79500, 67], [79800, 67], [80100, 67], [80400, 66], [80700, 66], [81000, 66], [81300, 66], [81600, 65], [81900, 65], [82200, 65], [82500, 65], [82800, 65], [83100, 64], [83402, 62], [83703, 61], [84004, 60], [84305, 58], [84606, 57], [84907, 56], [85208, 55], [85509, 54], [85811, 51], [86112, 50], [86413, 49], [86714, 48], [87015, 47], [87316, 45], [87617, 44], [87919, 42], [88220, 41], [88521, 39], [88822, 39], [89125, 38], [89429, 36], [89730, 36]], "point": [149, 150]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [7.008, 7.008, -3.376, -3.376, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [55, 26, 299, 160], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20457, 243], [20757, 243], [21057, 243], [21358, 242], [21658, 242], [21958, 242], [22258, 242], [22559, 241], [22859, 241], [23159, 241], [23459, 241], [23760, 240], [24060, 240], [24360, 240], [24661, 239], [24961, 239], [25261, 239], [25561, 239], [25862, 238], [26162, 238], [26462, 238], [26762, 238], [27063, 237], [27363, 237], [27663, 237], [27964, 236], [28264, 236], [28564, 236], [28864, 236], [29165, 235], [29465, 234], [29765, 234], [30066, 232], [30366, 232], [30666, 231], [30966, 231], [31267, 229], [31567, 229], [31867, 228], [32167, 228], [32468, 226], [32768, 226], [33068, 225], [33369, 224], [33669, 223], [33969, 223], [34269, 223], [34570, 221], [34870, 221], [35170, 220], [35470, 220], [35771, 218], [36071, 218], [36371, 217], [36672, 216], [36972, 215], [37272, 215], [37572, 214], [37873, 213], [38173, 212], [38473, 212], [38773, 211], [39074, 210], [39374, 209], [39674, 209], [39975, 207], [40275, 207], [40575, 206], [40875, 206], [41176, 204], [41476, 204], [41776, 204], [42076, 203], [42377, 202], [42677, 201], [42977, 201], [43278, 199], [43578, 199], [43878, 198], [44178, 198], [44485, 189], [44786, 187], [45086, 187], [45386, 186], [45686, 186], [45987, 184], [46287, 184], [46587, 183], [46887, 183], [47188, 182], [47488, 181], [47789, 180]], "point": [177, 92]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [7.008, 7.008, -3.376, -3.376, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [55, 26, 299, 160], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20457, 243], [20757, 243], [21057, 243], [21358, 242], [21658, 242], [21958, 242], [22258, 242], [22559, 241], [22859, 241], [23159, 241], [23459, 241], [23760, 240], [24060, 240], [24360, 240], [24661, 239], [24961, 239], [25261, 239], [25561, 239], [25862, 238], [26162, 238], [26462, 238], [26762, 238], [27063, 237], [27363, 237], [27663, 237], [27964, 236], [28264, 236], [28564, 236], [28864, 236], [29165, 235], [29465, 234], [29765, 234], [30066, 232], [30366, 232], [30666, 231], [30966, 231], [31267, 229], [31567, 229], [31867, 228], [32167, 228], [32468, 226], [32768, 226], [33068, 225], [33369, 224], [33669, 223], [33969, 223], [34269, 223], [34570, 221], [34870, 221], [35170, 220], [35470, 220], [35771, 218], [36071, 218], [36371, 217], [36672, 216], [36972, 215], [37272, 215], [37572, 214], [37873, 213], [38173, 212], [38473, 212], [38773, 211], [39074, 210], [39374, 209], [39674, 209], [39975, 207], [40275, 207], [40575, 206], [40875, 206], [41176, 204], [41476, 204], [41776, 204], [42076, 203], [42377, 202], [42677, 201], [42977, 201], [43278, 199], [43578, 199], [43878, 198], [44178, 198], [44485, 189], [44786, 187], [45086, 187], [45386, 186], [45686, 186], [45987, 184], [46287, 184], [46587, 183], [46887, 183], [47188, 182], [47488, 181], [47789, 180]], "point": [177, 92]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [55, 26, 299, 160], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20457, 243], [20757, 243], [21057, 243], [21358, 242], [21658, 242], [21958, 242], [22258, 242], [22559, 241], [22859, 241], [23159, 241], [23459, 241], [23760, 240], [24060, 240], [24360, 240], [24661, 239], [24961, 239], [25261, 239], [25561, 239], [25862, 238], [26162, 238], [26462, 238], [26762, 238], [27063, 237], [27363, 237], [27663, 237], [27964, 236], [28264, 236], [28564, 236], [28864, 236], [29165, 235], [29465, 234], [29765, 234], [30066, 232], [30366, 232], [30666, 231], [30966, 231], [31267, 229], [31567, 229], [31867, 228], [32167, 228], [32468, 226], [32768, 226], [33068, 225], [33369, 224], [33669, 223], [33969, 223], [34269, 223], [34570, 221], [34870, 221], [35170, 220], [35470, 220], [35771, 218], [36071, 218], [36371, 217], [36672, 216], [36972, 215], [37272, 215], [37572, 214], [37873, 213], [38173, 212], [38473, 212], [38773, 211], [39074, 210], [39374, 209], [39674, 209], [39975, 207], [40275, 207], [40575, 206], [40875, 206], [41176, 204], [41476, 204], [41776, 204], [42076, 203], [42377, 202], [42677, 201], [42977, 201], [43278, 199], [43578, 199], [43878, 198], [44178, 198], [44485, 189], [44786, 187], [45086, 187], [45386, 186], [45686, 186], [45987, 184], [46287, 184], [46587, 183], [46887, 183], [47188, 182], [47488, 181], [47789, 180]], "point": [177, 92]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+01.03|+00.80|-01.65"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [135, 108, 151, 128], "mask": [[32240, 6], [32539, 9], [32838, 11], [33137, 12], [33437, 13], [33736, 15], [34036, 15], [34336, 15], [34635, 17], [34935, 17], [35235, 17], [35535, 17], [35835, 17], [36135, 17], [36436, 16], [36736, 15], [37036, 15], [37337, 13], [37638, 11], [37939, 10], [38240, 7]], "point": [143, 117]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 26, 299, 300], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20456, 244], [20756, 244], [21055, 245], [21355, 245], [21654, 246], [21954, 246], [22253, 247], [22553, 247], [22852, 248], [23152, 248], [23451, 249], [23751, 249], [24050, 250], [24349, 251], [24649, 251], [24948, 137], [25093, 107], [25248, 135], [25395, 105], [25547, 135], [25696, 104], [25847, 134], [25997, 103], [26146, 134], [26298, 102], [26446, 134], [26599, 101], [26745, 134], [26900, 100], [27045, 134], [27200, 100], [27344, 134], [27501, 99], [27644, 134], [27802, 98], [27943, 135], [28103, 97], [28243, 135], [28403, 97], [28542, 135], [28703, 97], [28842, 135], [29003, 97], [29141, 136], [29304, 96], [29441, 136], [29604, 95], [29740, 136], [29904, 95], [30039, 137], [30204, 94], [30339, 138], [30503, 95], [30638, 139], [30803, 94], [30938, 139], [31103, 94], [31237, 141], [31403, 93], [31537, 141], [31703, 93], [31836, 143], [32002, 93], [32136, 143], [32302, 93], [32435, 146], [32602, 92], [32735, 147], [32901, 93], [33034, 149], [33200, 93], [33334, 151], [33499, 94], [33633, 153], [33798, 94], [33933, 155], [34096, 96], [34232, 260], [34532, 259], [34831, 260], [35131, 259], [35430, 260], [35730, 259], [36029, 260], [36328, 260], [36628, 260], [36927, 260], [37227, 260], [37526, 260], [37826, 260], [38125, 260], [38425, 260], [38724, 260], [39024, 260], [39323, 260], [39623, 260], [39922, 260], [40222, 260], [40521, 260], [40821, 260], [41120, 260], [41420, 260], [41719, 261], [42018, 261], [42318, 261], [42617, 261], [42917, 261], [43216, 261], [43516, 261], [43815, 261], [44115, 261], [44414, 260], [44714, 259], [45013, 260], [45313, 79], [45531, 41], [45612, 80], [45831, 41], [45912, 80], [46132, 39], [46211, 81], [46432, 39], [46511, 80], [46732, 38], [46810, 81], [47033, 37], [47110, 81], [47333, 37], [47409, 82], [47633, 36], [47708, 82], [47934, 35], [48008, 82], [48307, 83], [48607, 83], [48906, 84], [49206, 83], [49505, 84], [49805, 84], [50104, 85], [50404, 84], [50703, 85], [51003, 85], [51302, 86], [51602, 86], [51901, 86], [52201, 86], [52500, 87], [52800, 87], [53100, 86], [53400, 86], [53700, 86], [54000, 86], [54300, 86], [54600, 85], [54900, 85], [55200, 85], [55500, 85], [55800, 84], [56100, 84], [56400, 84], [56700, 84], [57000, 84], [57300, 83], [57600, 83], [57900, 83], [58200, 83], [58500, 83], [58800, 82], [59100, 82], [59400, 82], [59700, 82], [60000, 81], [60300, 81], [60600, 81], [60900, 81], [61200, 81], [61500, 80], [61800, 80], [62100, 80], [62400, 80], [62700, 79], [63000, 79], [63300, 79], [63600, 79], [63900, 79], [64200, 78], [64500, 78], [64800, 78], [65100, 78], [65400, 77], [65700, 77], [66000, 77], [66300, 77], [66600, 77], [66900, 76], [67200, 76], [67500, 76], [67800, 76], [68100, 75], [68400, 75], [68700, 75], [69000, 75], [69300, 75], [69600, 74], [69900, 74], [70200, 74], [70500, 74], [70800, 73], [71100, 73], [71400, 73], [71700, 73], [72000, 73], [72300, 72], [72600, 72], [72900, 72], [73200, 72], [73500, 71], [73800, 71], [74100, 71], [74400, 71], [74700, 71], [75000, 70], [75300, 70], [75600, 70], [75900, 70], [76200, 69], [76500, 69], [76800, 69], [77100, 69], [77400, 69], [77700, 68], [78000, 68], [78300, 68], [78600, 68], [78900, 67], [79200, 67], [79500, 67], [79800, 67], [80100, 67], [80400, 66], [80700, 66], [81000, 66], [81300, 66], [81600, 65], [81900, 65], [82200, 65], [82500, 65], [82800, 65], [83100, 64], [83402, 62], [83703, 61], [84004, 60], [84305, 58], [84606, 57], [84907, 56], [85208, 55], [85509, 54], [85811, 51], [86112, 50], [86413, 49], [86714, 48], [87015, 47], [87316, 45], [87617, 44], [87919, 42], [88220, 41], [88521, 39], [88822, 39], [89125, 38], [89429, 36], [89730, 36]], "point": [149, 150]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-00.53|+00.00|-01.55"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 225], "mask": [[0, 41700], [41701, 299], [42002, 298], [42302, 298], [42603, 297], [42904, 296], [43205, 295], [43506, 294], [43807, 293], [44108, 292], [44409, 291], [44710, 290], [45011, 289], [45312, 288], [45613, 287], [45914, 286], [46215, 285], [46515, 285], [46816, 284], [47117, 283], [47418, 282], [47719, 281], [48020, 280], [48321, 279], [48622, 278], [48923, 276], [49224, 274], [49525, 272], [49826, 270], [50127, 268], [50428, 266], [50729, 264], [51029, 263], [51330, 260], [51631, 258], [51932, 256], [52233, 254], [52534, 252], [52835, 250], [53136, 248], [53437, 246], [53738, 244], [54039, 242], [54340, 240], [54641, 237], [54942, 235], [55242, 234], [55543, 232], [55844, 230], [56145, 228], [56446, 226], [56747, 224], [57048, 222], [57349, 220], [57650, 218], [57951, 215], [58252, 213], [58553, 211], [58854, 209], [59155, 207], [59456, 205], [59756, 204], [60057, 202], [60358, 200], [60659, 198], [60960, 195], [61261, 193], [61562, 191], [61863, 189], [62164, 187], [62465, 185], [62766, 183], [63067, 181], [63368, 179], [63669, 177], [63969, 176], [64270, 173], [64571, 171], [64872, 169], [65173, 167], [65474, 165], [65775, 163], [66076, 161], [66377, 159], [66678, 157], [66979, 155], [67284, 143]], "point": [149, 112]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+01.03|+00.80|-01.65", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-00.53|+00.00|-01.55"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 12525], [12534, 289], [12836, 285], [13138, 282], [13439, 280], [13740, 278], [14041, 251], [14296, 21], [14341, 250], [14597, 20], [14642, 248], [14898, 18], [14942, 247], [15198, 17], [15243, 245], [15499, 16], [15543, 245], [15799, 16], [15843, 244], [16099, 15], [16143, 244], [16399, 15], [16443, 244], [16699, 15], [16743, 244], [16999, 14], [17043, 244], [17299, 14], [17343, 244], [17599, 14], [17642, 245], [17898, 15], [17942, 246], [18198, 15], [18241, 247], [18497, 16], [18541, 248], [18796, 18], [18840, 251], [19093, 21], [19139, 275], [19439, 276], [19738, 278], [20037, 279], [20336, 281], [20634, 284], [20933, 287], [21232, 290], [21530, 10736], [32278, 288], [32586, 280], [32907, 259], [33207, 259], [33507, 259], [33808, 258], [34108, 258], [34408, 258], [34708, 258], [35009, 257], [35309, 256], [35609, 256], [35909, 256], [36210, 255], [36510, 255], [36810, 255], [37110, 255], [37410, 255], [37711, 254], [38011, 254], [38311, 254], [38611, 254], [38911, 253], [39211, 253], [39511, 253], [39811, 253], [40111, 254], [40411, 254], [40711, 254], [41011, 254], [41311, 255], [41611, 255], [41911, 255], [42211, 255], [42511, 256], [42810, 262], [43105, 273], [43399, 19530], [62930, 299], [63230, 299], [63530, 299], [63830, 300], [64131, 299], [64431, 299], [64731, 299], [65031, 299], [65331, 299], [65631, 600], [66232, 298], [66532, 296], [66832, 152], [66985, 142], [67132, 295], [67432, 152], [67732, 152], [68033, 150], [68333, 150], [68633, 150], [68933, 150], [69233, 150], [69533, 150], [69833, 150], [70134, 149], [70434, 148], [70734, 148], [71034, 148], [71334, 148], [71634, 148], [71935, 147], [72235, 147], [72535, 147], [72835, 146], [73135, 146], [73435, 146], [73736, 145], [74036, 145], [74336, 145], [74636, 145], [74936, 145], [75236, 144], [75536, 144], [75837, 143], [76137, 143], [76437, 143], [76737, 143], [77037, 143], [77337, 143], [77638, 141], [77938, 141], [78238, 141], [78538, 141], [78838, 141], [79138, 141], [79438, 141], [79739, 140], [80039, 139], [80339, 139], [80639, 139], [80939, 139], [81239, 139], [81540, 138], [81840, 138], [82140, 138], [82440, 137], [82740, 137], [83040, 137], [83341, 136], [83641, 136], [83941, 136], [84241, 136], [84541, 136], [84841, 135], [85141, 135], [85442, 134], [85742, 133], [86042, 130], [86342, 130], [86642, 130], [86942, 129], [87243, 128], [87543, 128], [87843, 128], [88143, 128], [88443, 128], [88743, 57], [88809, 61], [89044, 56], [89149, 20], [89344, 56], [89644, 56], [89944, 56]], "point": [149, 149]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-00.53|+00.00|-01.55"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 6418], [6420, 295], [6722, 292], [7024, 289], [7325, 288], [7626, 286], [7926, 286], [8227, 284], [8527, 284], [8828, 283], [9128, 283], [9429, 281], [9729, 281], [10029, 281], [10329, 281], [10630, 280], [10930, 281], [11230, 281], [11530, 281], [11829, 282], [12129, 283], [12429, 96], [12534, 178], [12729, 94], [12836, 177], [13028, 93], [13138, 176], [13328, 92], [13439, 175], [13627, 92], [13740, 176], [13926, 92], [14041, 176], [14224, 68], [14296, 21], [14341, 250], [14597, 20], [14642, 248], [14898, 18], [14942, 247], [15198, 17], [15243, 245], [15499, 16], [15543, 245], [15799, 16], [15843, 244], [16099, 15], [16143, 244], [16399, 15], [16443, 244], [16699, 15], [16743, 244], [16999, 14], [17043, 244], [17299, 14], [17343, 244], [17599, 14], [17642, 245], [17898, 15], [17942, 246], [18198, 15], [18241, 247], [18497, 16], [18541, 248], [18796, 18], [18840, 251], [19093, 21], [19139, 275], [19439, 276], [19738, 278], [20037, 279], [20336, 281], [20634, 284], [20933, 287], [21232, 290], [21530, 10736], [32278, 288], [32586, 280], [32907, 259], [33207, 259], [33507, 259], [33808, 258], [34108, 258], [34408, 258], [34708, 258], [35009, 257], [35309, 256], [35609, 256], [35909, 256], [36210, 255], [36510, 255], [36810, 255], [37110, 255], [37410, 255], [37711, 254], [38011, 254], [38311, 254], [38611, 254], [38911, 253], [39211, 253], [39511, 253], [39811, 253], [40111, 254], [40411, 254], [40711, 254], [41011, 254], [41311, 255], [41611, 255], [41911, 255], [42211, 255], [42511, 256], [42810, 262], [43105, 273], [43399, 19530], [62930, 299], [63230, 299], [63530, 299], [63830, 300], [64131, 299], [64431, 299], [64731, 299], [65031, 299], [65331, 299], [65631, 600], [66232, 298], [66532, 296], [66832, 152], [66985, 142], [67132, 295], [67432, 152], [67732, 152], [68033, 150], [68333, 150], [68633, 150], [68933, 150], [69233, 150], [69533, 150], [69833, 150], [70134, 149], [70434, 148], [70734, 148], [71034, 148], [71334, 148], [71634, 148], [71935, 147], [72235, 147], [72535, 147], [72835, 146], [73135, 146], [73435, 146], [73736, 145], [74036, 145], [74336, 145], [74636, 145], [74936, 145], [75236, 144], [75536, 144], [75837, 143], [76137, 143], [76437, 143], [76737, 143], [77037, 143], [77337, 143], [77638, 141], [77938, 141], [78238, 141], [78538, 141], [78838, 141], [79138, 141], [79438, 141], [79739, 140], [80039, 139], [80339, 139], [80639, 139], [80939, 139], [81239, 139], [81540, 138], [81840, 138], [82140, 138], [82440, 137], [82740, 137], [83040, 137], [83341, 136], [83641, 136], [83941, 136], [84241, 136], [84541, 136], [84841, 135], [85141, 135], [85442, 134], [85742, 133], [86042, 130], [86342, 130], [86642, 130], [86942, 129], [87243, 128], [87543, 128], [87843, 128], [88143, 128], [88443, 128], [88743, 57], [88809, 61], [89044, 56], [89149, 20], [89344, 56], [89644, 56], [89944, 56]], "point": [149, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan14", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 1.5, "y": 0.9009992, "z": 3.0}, "object_poses": [{"objectName": "Pan_7013969f", "position": {"x": 1.5433, "y": 0.9098, "z": -0.3203}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Potato_6d0d5c3c", "position": {"x": 0.9262093, "y": 0.9514022, "z": -1.24763918}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Ladle_4b0a72cf", "position": {"x": 1.72622263, "y": 0.955562353, "z": -1.54592526}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "DishSponge_00484768", "position": {"x": 0.220124, "y": 0.910164237, "z": 1.18092012}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "DishSponge_00484768", "position": {"x": 1.68454289, "y": 0.9101642, "z": 0.363397419}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pot_bb5e3511", "position": {"x": 1.807, "y": 0.9098, "z": 0.075}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Fork_192d20c7", "position": {"x": 0.9890007, "y": 0.911148548, "z": 0.827641368}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spatula_83c41bbf", "position": {"x": 1.86598945, "y": 0.9261998, "z": -1.288871}, "rotation": {"x": 0.0, "y": -0.000160509444, "z": 0.0}}, {"objectName": "Spatula_83c41bbf", "position": {"x": 1.60700154, "y": 0.9255998, "z": 0.6226909}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "Egg_85efe221", "position": {"x": -0.6929716, "y": 0.785807133, "z": -1.69955325}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_85efe221", "position": {"x": 1.03216934, "y": 0.795609951, "z": -1.65477979}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_5ecc05ec", "position": {"x": 1.91716433, "y": 0.908884645, "z": 0.415256858}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_e8bf9352", "position": {"x": -0.6929714, "y": 0.4621566, "z": -1.54134119}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_e8bf9352", "position": {"x": 1.75787616, "y": 0.9122848, "z": 0.827643633}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_fd471042", "position": {"x": 1.73962343, "y": 0.06836966, "z": 1.684}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_817a54e2", "position": {"x": 1.65633833, "y": 0.9750553, "z": -1.3745563}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Tomato_817a54e2", "position": {"x": -0.8387238, "y": 0.810365558, "z": -1.66452444}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b3498f3e", "position": {"x": 1.77508092, "y": 2.13104033, "z": 1.19849527}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b3498f3e", "position": {"x": 1.73332167, "y": 1.64890611, "z": -0.449611485}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_8f8a19bf", "position": {"x": 1.5015831, "y": 0.9874766, "z": 1.18092394}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_e8bf9352", "position": {"x": 1.50158429, "y": 0.9122848, "z": 0.827642858}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_fd471042", "position": {"x": 1.84167039, "y": 1.65207028, "z": -0.449611485}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_5ecc05ec", "position": {"x": -0.790139735, "y": 1.3871752, "z": -1.66452444}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_85efe221", "position": {"x": 0.22012496, "y": 0.9457633, "z": 0.915959358}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Fork_192d20c7", "position": {"x": 0.455367029, "y": 0.9117485, "z": -1.79368055}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "Ladle_4b0a72cf", "position": {"x": 0.220124245, "y": 0.954962432, "z": 1.09259987}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "Apple_5b80d804", "position": {"x": 0.4764157, "y": 0.967907, "z": 1.26924109}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_b3498f3e", "position": {"x": 1.86598969, "y": 0.906199932, "z": -1.37455559}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pan_7013969f", "position": {"x": 1.28159308, "y": 0.900000036, "z": 1.19070256}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_817a54e2", "position": {"x": -0.8387238, "y": 1.74685693, "z": -1.59446669}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_83c41bbf", "position": {"x": 1.72622287, "y": 0.9261998, "z": -1.6316098}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "Potato_6d0d5c3c", "position": {"x": 1.7711072, "y": 1.055655, "z": -0.8880987}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Knife_06a240e2", "position": {"x": 1.6070025, "y": 0.943037152, "z": 0.311538458}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "ButterKnife_38537d23", "position": {"x": 1.762083, "y": 0.910642, "z": 0.5189738}, "rotation": {"x": 0.0, "y": -0.000160509444, "z": 0.0}}, {"objectName": "Bread_8f8a19bf", "position": {"x": 1.757875, "y": 0.9874766, "z": 1.18092465}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Lettuce_e61d6e1d", "position": {"x": 0.8565267, "y": 0.989360154, "z": 1.11928868}, "rotation": {"x": 0.303996265, "y": 270.002136, "z": 2.38245058}}, {"objectName": "PepperShaker_3caefad1", "position": {"x": 0.2201252, "y": 0.9056, "z": 0.8276391}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pot_bb5e3511", "position": {"x": 1.54872215, "y": 0.9098, "z": 0.075}, "rotation": {"x": 0.0, "y": 179.999741, "z": 0.0}}, {"objectName": "DishSponge_00484768", "position": {"x": 0.73270905, "y": 0.910164237, "z": 0.739320338}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spoon_89592edf", "position": {"x": 1.86599016, "y": 0.9123293, "z": -1.5459249}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_7266264e", "position": {"x": 1.8058, "y": 1.63408828, "z": -1.37889993}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f9995e03", "position": {"x": 0.476416826, "y": 0.9056, "z": 0.915960133}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}], "object_toggles": [], "random_seed": 1629363550, "scene_num": 14}, "task_id": "trial_T20190909_091242_721609", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A3F7G1FSFWQPLE_3IOEN3P9SAASPSL7ILFL7JASQ1P16V", "high_descs": ["Turn right and take a step forward, then turn right and walk towards the door, hang a left and walk towards the fridge, then walk over to the kitchen sink.", "Pick up the egg out of the sink.", "Turn left to face the microwave.", "Open the microwave and put the egg inside then close the door and turn on the microwave, after a couple seconds open the microwave back up and take out the now heated egg then close the microwave.", "Walk over to the fridge that is behind you.", "Open the fridge and put the heated egg inside then close the fridge."], "task_desc": "Put a heated egg in the fridge.", "votes": [1, 1, 1]}, {"assignment_id": "AM2KK02JXXW48_3LYA37P8ITE015OOMM2BNESJD95KB9", "high_descs": ["Turn around, move toward the door, go past the counter on the left, to the sink on the left.", "Pick up the egg in the sink. ", "Bring the egg to the microwave on the left. ", "Heat the egg in the microwave. ", "Bring the the heated egg to the fridge behind you on the left. ", "Put the heated egg on the top shelf on the left side of the fridge. "], "task_desc": "Put a heated egg on the top shelf on the left side of the fridge. ", "votes": [1, 1, 1]}, {"assignment_id": "A17TKHT8FEVH0R_3Q5ZZ9ZEVR6EHO5ECNEVUNJ08DV58G", "high_descs": ["Turn around and go to the sink", "Grab the egg out of the sink", "Turn left and look at the microwave", "Heat the egg in the microwave and then take it out", "Turn around and go to the fridge", "Put the egg in the fridge"], "task_desc": "Putting a hot egg in the microwave", "votes": [1, 1, 0]}]}}