{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000087.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000088.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000089.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000090.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000091.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000092.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000093.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000094.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000191.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000192.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000193.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000194.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000195.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000196.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000197.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000198.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000199.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000200.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000201.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000202.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000203.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000204.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000205.png", "low_idx": 35}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "CounterTop", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|0|-3|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-0.426448076, -0.426448076, -0.761204244, -0.761204244, 3.929890156, 3.929890156]], "coordinateReceptacleObjectId": ["CounterTop", [-0.06, -0.06, 2.024, 2.024, 3.8444, 3.8444]], "forceVisible": true, "objectId": "Egg|-00.11|+00.98|-00.19"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|-3|1|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [7.732, 7.732, -3.068, -3.068, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.93|+00.90|-00.77"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|3|2|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "countertop"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-0.426448076, -0.426448076, -0.761204244, -0.761204244, 3.929890156, 3.929890156]], "coordinateReceptacleObjectId": ["CounterTop", [-0.06, -0.06, 2.024, 2.024, 3.8444, 3.8444]], "forceVisible": true, "objectId": "Egg|-00.11|+00.98|-00.19", "receptacleObjectId": "CounterTop|-00.02|+00.96|+00.51"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.11|+00.98|-00.19"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [103, 145, 129, 176], "mask": [[43314, 4], [43611, 10], [43909, 13], [44208, 16], [44507, 18], [44806, 20], [45105, 21], [45405, 22], [45704, 24], [46004, 24], [46303, 26], [46603, 26], [46903, 26], [47203, 27], [47503, 27], [47803, 27], [48103, 27], [48403, 27], [48703, 27], [49003, 27], [49303, 27], [49603, 27], [49904, 25], [50204, 25], [50505, 23], [50805, 23], [51106, 21], [51407, 19], [51708, 17], [52009, 15], [52311, 11], [52614, 5]], "point": [116, 159]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [55, 9, 255, 124], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11160, 190], [11460, 191], [11758, 195], [12057, 197], [12356, 199], [12656, 199], [12955, 201], [13255, 201], [13555, 201], [13855, 201], [14155, 201], [14455, 200], [14756, 199], [15056, 199], [15356, 199], [15656, 198], [15957, 197], [16257, 197], [16557, 196], [16858, 195], [17158, 195], [17458, 195], [17758, 194], [18059, 193], [18359, 193], [18659, 192], [18959, 192], [19260, 191], [19560, 190], [19860, 190], [20160, 190], [20461, 189], [20761, 188], [21061, 188], [21361, 188], [21662, 186], [21962, 186], [22262, 186], [22563, 185], [22863, 184], [23163, 184], [23463, 184], [23764, 182], [24064, 182], [24364, 182], [24664, 181], [24965, 180], [25265, 180], [25565, 180], [25865, 179], [26166, 178], [26466, 178], [26766, 177], [27066, 177], [27367, 176], [27667, 176], [27967, 175], [28267, 175], [28568, 174], [28868, 173], [29168, 173], [29469, 172], [29769, 172], [30069, 171], [30369, 171], [30670, 170], [30970, 169], [31270, 169], [31570, 169], [31871, 167], [32171, 167], [32471, 167], [32771, 167], [33072, 165], [33372, 165], [33672, 165], [33972, 164], [34273, 163], [34573, 163], [34873, 163], [35174, 161], [35474, 161], [35774, 161], [36074, 160], [36375, 159], [36675, 159], [36976, 156]], "point": [155, 65]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.11|+00.98|-00.19", "placeStationary": true, "receptacleObjectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 9, 255, 228], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11149, 201], [11448, 203], [11747, 206], [12046, 208], [12345, 210], [12645, 210], [12944, 212], [13243, 213], [13543, 213], [13842, 214], [14141, 215], [14441, 214], [14740, 215], [15039, 216], [15339, 117], [15474, 81], [15638, 116], [15776, 78], [15937, 116], [16077, 77], [16237, 116], [16378, 76], [16536, 116], [16678, 75], [16835, 118], [16978, 75], [17134, 119], [17278, 75], [17434, 73], [17518, 35], [17578, 75], [17733, 71], [17821, 32], [17878, 74], [18032, 70], [18122, 31], [18178, 74], [18332, 69], [18423, 30], [18477, 75], [18631, 69], [18723, 30], [18777, 74], [18930, 70], [19023, 30], [19077, 74], [19230, 70], [19326, 27], [19377, 74], [19529, 71], [19628, 25], [19677, 73], [19828, 72], [19923, 2], [19928, 25], [19976, 74], [20128, 73], [20223, 3], [20228, 26], [20276, 74], [20427, 74], [20523, 4], [20528, 26], [20576, 74], [20726, 75], [20823, 4], [20829, 25], [20876, 73], [21025, 76], [21123, 4], [21129, 25], [21176, 73], [21325, 76], [21423, 4], [21428, 26], [21476, 73], [21624, 78], [21723, 3], [21728, 26], [21775, 73], [21923, 79], [22023, 3], [22028, 26], [22075, 73], [22223, 79], [22323, 3], [22328, 26], [22375, 73], [22522, 80], [22623, 3], [22628, 26], [22675, 73], [22821, 81], [22923, 2], [22927, 27], [22975, 72], [23121, 81], [23223, 2], [23227, 27], [23275, 72], [23420, 83], [23523, 1], [23526, 29], [23574, 73], [23719, 84], [23823, 1], [23826, 29], [23874, 72], [24019, 84], [24125, 30], [24174, 72], [24318, 85], [24424, 31], [24474, 72], [24617, 86], [24723, 32], [24774, 71], [24916, 88], [25024, 31], [25073, 72], [25216, 88], [25324, 32], [25373, 72], [25515, 89], [25624, 32], [25673, 72], [25814, 90], [25923, 34], [25972, 72], [26114, 91], [26223, 39], [26268, 76], [26413, 92], [26522, 41], [26566, 78], [26712, 94], [26821, 122], [27012, 96], [27118, 125], [27311, 232], [27610, 233], [27910, 232], [28209, 233], [28508, 234], [28807, 234], [29107, 234], [29406, 235], [29705, 236], [30005, 235], [30304, 236], [30603, 237], [30903, 236], [31202, 237], [31501, 238], [31801, 237], [32100, 238], [32400, 238], [32700, 238], [33000, 237], [33300, 237], [33600, 237], [33900, 236], [34200, 236], [34500, 236], [34800, 236], [35100, 235], [35400, 235], [35700, 79], [35902, 33], [36000, 79], [36202, 32], [36300, 78], [36502, 32], [36600, 78], [36802, 32], [36900, 78], [37103, 29], [37200, 77], [37500, 77], [37800, 77], [38100, 77], [38400, 76], [38700, 76], [39000, 76], [39300, 75], [39600, 75], [39900, 75], [40200, 74], [40500, 74], [40800, 74], [41100, 73], [41400, 73], [41700, 73], [42000, 73], [42300, 72], [42600, 72], [42900, 72], [43200, 71], [43500, 71], [43800, 71], [44100, 70], [44400, 70], [44700, 70], [45000, 69], [45300, 69], [45600, 69], [45900, 68], [46200, 68], [46500, 68], [46800, 68], [47100, 67], [47400, 67], [47700, 67], [48000, 66], [48300, 66], [48600, 66], [48900, 65], [49200, 65], [49500, 65], [49800, 64], [50100, 64], [50400, 64], [50700, 64], [51000, 63], [51300, 63], [51600, 63], [51900, 62], [52200, 62], [52500, 62], [52800, 61], [53100, 61], [53400, 61], [53700, 60], [54000, 60], [54301, 59], [54601, 59], [54902, 57], [55203, 56], [55503, 56], [55804, 54], [56105, 53], [56405, 53], [56706, 51], [57007, 50], [57307, 50], [57608, 48], [57909, 47], [58209, 47], [58510, 45], [58811, 44], [59111, 44], [59412, 43], [59713, 41], [60013, 41], [60314, 40], [60615, 38], [60915, 38], [61216, 37], [61517, 35], [61817, 35], [62118, 34], [62418, 33], [62719, 32], [63020, 31], [63320, 31], [63621, 29], [63922, 28], [64222, 28], [64522, 27], [64821, 28], [65122, 27], [65423, 25], [65723, 26], [66024, 27], [66325, 26], [66625, 27], [66926, 21], [66948, 3], [67227, 20], [67527, 19], [67828, 18], [68129, 17]], "point": [127, 117]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 9, 255, 228], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11149, 201], [11448, 203], [11747, 206], [12046, 208], [12345, 210], [12645, 210], [12944, 212], [13243, 213], [13543, 213], [13842, 214], [14141, 215], [14441, 214], [14740, 215], [15039, 216], [15339, 117], [15474, 81], [15638, 116], [15776, 78], [15937, 116], [16077, 77], [16237, 116], [16378, 76], [16536, 116], [16678, 75], [16835, 118], [16978, 75], [17134, 119], [17278, 75], [17434, 73], [17518, 35], [17578, 75], [17733, 71], [17821, 32], [17878, 74], [18032, 70], [18122, 31], [18178, 74], [18332, 69], [18423, 30], [18477, 75], [18631, 69], [18723, 30], [18777, 74], [18930, 70], [19023, 30], [19077, 74], [19230, 70], [19326, 27], [19377, 74], [19529, 71], [19628, 6], [19641, 12], [19677, 73], [19828, 72], [19923, 2], [19928, 5], [19943, 10], [19976, 74], [20128, 73], [20223, 3], [20228, 3], [20244, 10], [20276, 74], [20427, 74], [20523, 4], [20528, 3], [20545, 9], [20576, 74], [20726, 75], [20823, 4], [20829, 1], [20846, 8], [20876, 73], [21025, 76], [21123, 4], [21146, 8], [21176, 73], [21325, 76], [21423, 4], [21428, 1], [21447, 7], [21476, 73], [21624, 78], [21723, 3], [21747, 7], [21775, 73], [21923, 79], [22023, 3], [22048, 6], [22075, 73], [22223, 79], [22323, 3], [22348, 6], [22375, 73], [22522, 80], [22623, 3], [22649, 5], [22675, 73], [22821, 81], [22923, 2], [22949, 5], [22975, 72], [23121, 81], [23223, 2], [23249, 5], [23275, 72], [23420, 83], [23523, 1], [23526, 1], [23549, 6], [23574, 73], [23719, 84], [23823, 1], [23826, 1], [23849, 6], [23874, 72], [24019, 84], [24125, 2], [24149, 6], [24174, 72], [24318, 85], [24424, 3], [24449, 6], [24474, 72], [24617, 86], [24723, 4], [24749, 6], [24774, 71], [24916, 88], [25024, 3], [25049, 6], [25073, 72], [25216, 88], [25324, 3], [25349, 7], [25373, 72], [25515, 89], [25624, 4], [25649, 7], [25673, 72], [25814, 90], [25923, 5], [25948, 9], [25972, 72], [26114, 91], [26223, 6], [26248, 14], [26268, 76], [26413, 92], [26522, 7], [26547, 16], [26566, 78], [26712, 94], [26821, 9], [26847, 96], [27012, 96], [27118, 13], [27146, 97], [27311, 121], [27445, 98], [27610, 123], [27743, 100], [27910, 126], [28041, 101], [28209, 233], [28508, 234], [28807, 234], [29107, 234], [29406, 235], [29705, 236], [30005, 235], [30304, 236], [30603, 237], [30903, 236], [31202, 237], [31501, 238], [31801, 237], [32100, 238], [32400, 238], [32700, 238], [33000, 237], [33300, 237], [33600, 237], [33900, 236], [34200, 236], [34500, 236], [34800, 236], [35100, 235], [35400, 235], [35700, 79], [35902, 33], [36000, 79], [36202, 32], [36300, 78], [36502, 32], [36600, 78], [36802, 32], [36900, 78], [37103, 29], [37200, 77], [37500, 77], [37800, 77], [38100, 77], [38400, 76], [38700, 76], [39000, 76], [39300, 75], [39600, 75], [39900, 75], [40200, 74], [40500, 74], [40800, 74], [41100, 73], [41400, 73], [41700, 73], [42000, 73], [42300, 72], [42600, 72], [42900, 72], [43200, 71], [43500, 71], [43800, 71], [44100, 70], [44400, 70], [44700, 70], [45000, 69], [45300, 69], [45600, 69], [45900, 68], [46200, 68], [46500, 68], [46800, 68], [47100, 67], [47400, 67], [47700, 67], [48000, 66], [48300, 66], [48600, 66], [48900, 65], [49200, 65], [49500, 65], [49800, 64], [50100, 64], [50400, 64], [50700, 64], [51000, 63], [51300, 63], [51600, 63], [51900, 62], [52200, 62], [52500, 62], [52800, 61], [53100, 61], [53400, 61], [53700, 60], [54000, 60], [54301, 59], [54601, 59], [54902, 57], [55203, 56], [55503, 56], [55804, 54], [56105, 53], [56405, 53], [56706, 51], [57007, 50], [57307, 50], [57608, 48], [57909, 47], [58209, 47], [58510, 45], [58811, 44], [59111, 44], [59412, 43], [59713, 41], [60013, 41], [60314, 40], [60615, 38], [60915, 38], [61216, 37], [61517, 35], [61817, 35], [62118, 34], [62418, 33], [62719, 32], [63020, 31], [63320, 31], [63621, 29], [63922, 28], [64222, 28], [64522, 27], [64821, 28], [65122, 27], [65423, 25], [65723, 26], [66024, 27], [66325, 26], [66625, 27], [66926, 21], [66948, 3], [67227, 20], [67527, 19], [67828, 18], [68129, 17]], "point": [127, 117]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [7.732, 7.732, -3.068, -3.068, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [55, 9, 255, 124], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11160, 190], [11460, 191], [11758, 195], [12057, 197], [12356, 199], [12656, 199], [12955, 201], [13255, 201], [13555, 201], [13855, 201], [14155, 201], [14455, 200], [14756, 199], [15056, 199], [15356, 199], [15656, 198], [15957, 197], [16257, 197], [16557, 196], [16858, 195], [17158, 195], [17458, 195], [17758, 194], [18059, 193], [18359, 193], [18659, 192], [18959, 192], [19260, 191], [19560, 190], [19860, 190], [20160, 190], [20461, 189], [20761, 188], [21061, 188], [21361, 188], [21662, 186], [21962, 186], [22262, 186], [22563, 185], [22863, 184], [23163, 184], [23463, 184], [23764, 182], [24064, 182], [24364, 182], [24664, 181], [24965, 180], [25265, 180], [25565, 180], [25865, 179], [26166, 178], [26466, 178], [26766, 177], [27066, 177], [27367, 176], [27667, 176], [27967, 175], [28267, 175], [28568, 174], [28868, 173], [29168, 173], [29469, 172], [29769, 172], [30069, 171], [30369, 171], [30670, 170], [30970, 169], [31270, 169], [31570, 169], [31871, 167], [32171, 167], [32471, 167], [32771, 167], [33072, 165], [33372, 165], [33672, 165], [33972, 164], [34273, 163], [34573, 163], [34873, 163], [35174, 161], [35474, 161], [35774, 161], [36074, 160], [36375, 159], [36675, 159], [36976, 156]], "point": [155, 65]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [7.732, 7.732, -3.068, -3.068, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [55, 9, 255, 124], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11160, 190], [11460, 191], [11758, 195], [12057, 197], [12356, 199], [12656, 199], [12955, 201], [13255, 201], [13555, 201], [13855, 201], [14155, 201], [14455, 200], [14756, 199], [15056, 199], [15356, 199], [15656, 198], [15957, 197], [16257, 197], [16557, 196], [16858, 195], [17158, 195], [17458, 195], [17758, 194], [18059, 193], [18359, 193], [18659, 192], [18959, 192], [19260, 191], [19560, 190], [19860, 190], [20160, 190], [20461, 189], [20761, 188], [21061, 188], [21361, 188], [21662, 186], [21962, 186], [22262, 186], [22563, 185], [22863, 184], [23163, 184], [23463, 184], [23764, 182], [24064, 182], [24364, 182], [24664, 181], [24965, 180], [25265, 180], [25565, 180], [25865, 179], [26166, 178], [26466, 178], [26766, 177], [27066, 177], [27367, 176], [27667, 176], [27967, 175], [28267, 175], [28568, 174], [28868, 173], [29168, 173], [29469, 172], [29769, 172], [30069, 171], [30369, 171], [30670, 170], [30970, 169], [31270, 169], [31570, 169], [31871, 167], [32171, 167], [32471, 167], [32771, 167], [33072, 165], [33372, 165], [33672, 165], [33972, 164], [34273, 163], [34573, 163], [34873, 163], [35174, 161], [35474, 161], [35774, 161], [36074, 160], [36375, 159], [36675, 159], [36976, 156]], "point": [155, 65]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [55, 9, 255, 124], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11160, 190], [11460, 191], [11758, 195], [12057, 197], [12356, 199], [12656, 199], [12955, 201], [13255, 201], [13555, 201], [13855, 201], [14155, 201], [14455, 200], [14756, 199], [15056, 199], [15356, 199], [15656, 198], [15957, 197], [16257, 197], [16557, 196], [16858, 195], [17158, 195], [17458, 195], [17758, 194], [18059, 193], [18359, 193], [18659, 192], [18959, 192], [19260, 191], [19560, 190], [19860, 190], [20160, 190], [20461, 189], [20761, 188], [21061, 188], [21361, 188], [21662, 186], [21962, 186], [22262, 186], [22563, 185], [22863, 184], [23163, 184], [23463, 184], [23764, 182], [24064, 182], [24364, 182], [24664, 181], [24965, 180], [25265, 180], [25565, 180], [25865, 179], [26166, 178], [26466, 178], [26766, 177], [27066, 177], [27367, 176], [27667, 176], [27967, 175], [28267, 175], [28568, 174], [28868, 173], [29168, 173], [29469, 172], [29769, 172], [30069, 171], [30369, 171], [30670, 170], [30970, 169], [31270, 169], [31570, 169], [31871, 167], [32171, 167], [32471, 167], [32771, 167], [33072, 165], [33372, 165], [33672, 165], [33972, 164], [34273, 163], [34573, 163], [34873, 163], [35174, 161], [35474, 161], [35774, 161], [36074, 160], [36375, 159], [36675, 159], [36976, 156]], "point": [155, 65]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.11|+00.98|-00.19"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [127, 66, 148, 94], "mask": [[19634, 7], [19933, 10], [20231, 13], [20531, 14], [20830, 16], [21129, 17], [21429, 18], [21728, 19], [22028, 20], [22328, 20], [22627, 22], [22927, 22], [23227, 22], [23527, 22], [23827, 22], [24127, 22], [24427, 22], [24727, 22], [25027, 22], [25327, 22], [25628, 21], [25928, 20], [26229, 19], [26529, 18], [26830, 17], [27131, 15], [27432, 13], [27733, 10], [28036, 5]], "point": [137, 79]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 9, 255, 228], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11149, 201], [11448, 203], [11747, 206], [12046, 208], [12345, 210], [12645, 210], [12944, 212], [13243, 213], [13543, 213], [13842, 214], [14141, 215], [14441, 214], [14740, 215], [15039, 216], [15339, 117], [15474, 81], [15638, 116], [15776, 78], [15937, 116], [16077, 77], [16237, 116], [16378, 76], [16536, 116], [16678, 75], [16835, 118], [16978, 75], [17134, 119], [17278, 75], [17434, 73], [17518, 35], [17578, 75], [17733, 71], [17821, 32], [17878, 74], [18032, 70], [18122, 31], [18178, 74], [18332, 69], [18423, 30], [18477, 75], [18631, 69], [18723, 30], [18777, 74], [18930, 70], [19023, 30], [19077, 74], [19230, 70], [19326, 27], [19377, 74], [19529, 71], [19628, 25], [19677, 73], [19828, 72], [19923, 2], [19928, 25], [19976, 74], [20128, 73], [20223, 3], [20228, 26], [20276, 74], [20427, 74], [20523, 4], [20528, 26], [20576, 74], [20726, 75], [20823, 4], [20829, 25], [20876, 73], [21025, 76], [21123, 4], [21129, 25], [21176, 73], [21325, 76], [21423, 4], [21428, 26], [21476, 73], [21624, 78], [21723, 3], [21728, 26], [21775, 73], [21923, 79], [22023, 3], [22028, 26], [22075, 73], [22223, 79], [22323, 3], [22328, 26], [22375, 73], [22522, 80], [22623, 3], [22628, 26], [22675, 73], [22821, 81], [22923, 2], [22927, 27], [22975, 72], [23121, 81], [23223, 2], [23227, 27], [23275, 72], [23420, 83], [23523, 1], [23526, 29], [23574, 73], [23719, 84], [23823, 1], [23826, 29], [23874, 72], [24019, 84], [24125, 30], [24174, 72], [24318, 85], [24424, 31], [24474, 72], [24617, 86], [24723, 32], [24774, 71], [24916, 88], [25024, 31], [25073, 72], [25216, 88], [25324, 32], [25373, 72], [25515, 89], [25624, 32], [25673, 72], [25814, 90], [25923, 34], [25972, 72], [26114, 91], [26223, 39], [26268, 76], [26413, 92], [26522, 41], [26566, 78], [26712, 94], [26821, 122], [27012, 96], [27118, 125], [27311, 232], [27610, 233], [27910, 232], [28209, 233], [28508, 234], [28807, 234], [29107, 234], [29406, 235], [29705, 236], [30005, 235], [30304, 236], [30603, 237], [30903, 236], [31202, 237], [31501, 238], [31801, 237], [32100, 238], [32400, 238], [32700, 238], [33000, 237], [33300, 237], [33600, 237], [33900, 236], [34200, 236], [34500, 236], [34800, 236], [35100, 235], [35400, 235], [35700, 79], [35902, 33], [36000, 79], [36202, 32], [36300, 78], [36502, 32], [36600, 78], [36802, 32], [36900, 78], [37103, 29], [37200, 77], [37500, 77], [37800, 77], [38100, 77], [38400, 76], [38700, 76], [39000, 76], [39300, 75], [39600, 75], [39900, 75], [40200, 74], [40500, 74], [40800, 74], [41100, 73], [41400, 73], [41700, 73], [42000, 73], [42300, 72], [42600, 72], [42900, 72], [43200, 71], [43500, 71], [43800, 71], [44100, 70], [44400, 70], [44700, 70], [45000, 69], [45300, 69], [45600, 69], [45900, 68], [46200, 68], [46500, 68], [46800, 68], [47100, 67], [47400, 67], [47700, 67], [48000, 66], [48300, 66], [48600, 66], [48900, 65], [49200, 65], [49500, 65], [49800, 64], [50100, 64], [50400, 64], [50700, 64], [51000, 63], [51300, 63], [51600, 63], [51900, 62], [52200, 62], [52500, 62], [52800, 61], [53100, 61], [53400, 61], [53700, 60], [54000, 60], [54301, 59], [54601, 59], [54902, 57], [55203, 56], [55503, 56], [55804, 54], [56105, 53], [56405, 53], [56706, 51], [57007, 50], [57307, 50], [57608, 48], [57909, 47], [58209, 47], [58510, 45], [58811, 44], [59111, 44], [59412, 43], [59713, 41], [60013, 41], [60314, 40], [60615, 38], [60915, 38], [61216, 37], [61517, 35], [61817, 35], [62118, 34], [62418, 33], [62719, 32], [63020, 31], [63320, 31], [63621, 29], [63922, 28], [64222, 28], [64522, 27], [64821, 28], [65122, 27], [65423, 25], [65723, 26], [66024, 27], [66325, 26], [66625, 27], [66926, 21], [66948, 3], [67227, 20], [67527, 19], [67828, 18], [68129, 17]], "point": [127, 117]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.11|+00.98|-00.19", "placeStationary": true, "receptacleObjectId": "CounterTop|-00.02|+00.96|+00.51"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 78, 299, 238], "mask": [[23100, 7], [23169, 2], [23232, 174], [23533, 171], [23833, 171], [24133, 170], [24434, 168], [24734, 168], [25034, 168], [25334, 167], [25634, 167], [25934, 167], [26234, 167], [26534, 78], [26614, 86], [26833, 78], [26915, 85], [27133, 78], [27215, 86], [27433, 77], [27515, 86], [27733, 77], [27816, 86], [28033, 77], [28116, 86], [28333, 76], [28416, 86], [28633, 76], [28717, 86], [28933, 76], [29017, 86], [29233, 76], [29317, 86], [29533, 76], [29617, 87], [29833, 76], [29918, 86], [30133, 76], [30218, 87], [30433, 76], [30518, 88], [30732, 77], [30818, 88], [31032, 77], [31118, 88], [31331, 78], [31419, 88], [31630, 79], [31719, 88], [31929, 80], [32019, 88], [32146, 8], [32224, 85], [32319, 87], [32447, 7], [32519, 90], [32619, 86], [32748, 6], [32820, 89], [32919, 85], [33049, 5], [33120, 89], [33220, 83], [33350, 4], [33420, 89], [33520, 82], [33650, 4], [33721, 88], [33820, 81], [33951, 3], [34021, 88], [34120, 81], [34252, 1], [34321, 89], [34420, 80], [34552, 1], [34621, 95], [34721, 79], [34921, 95], [35021, 79], [35221, 95], [35321, 79], [35522, 94], [35621, 79], [35822, 94], [35921, 79], [36122, 94], [36221, 79], [36422, 94], [36522, 78], [36722, 94], [36822, 78], [37022, 94], [37122, 78], [37321, 95], [37422, 78], [37621, 95], [37722, 78], [37921, 95], [38023, 77], [38220, 97], [38323, 46], [38371, 29], [38520, 97], [38623, 45], [38673, 27], [38820, 97], [38923, 45], [38973, 27], [39119, 98], [39223, 46], [39274, 26], [39419, 96], [39524, 45], [39575, 25], [39655, 1], [39718, 97], [39824, 46], [39875, 25], [39954, 2], [40018, 98], [40124, 47], [40176, 24], [40254, 3], [40317, 100], [40424, 47], [40476, 24], [40553, 4], [40616, 103], [40723, 49], [40776, 24], [40852, 6], [40916, 157], [41077, 23], [41151, 8], [41215, 158], [41377, 24], [41450, 10], [41514, 160], [41678, 23], [41749, 12], [41812, 163], [41978, 23], [42048, 13], [42111, 164], [42279, 23], [42346, 16], [42410, 166], [42579, 23], [42645, 19], [42708, 169], [42880, 23], [42943, 22], [43007, 171], [43180, 24], [43241, 25], [43305, 173], [43481, 24], [43539, 30], [43603, 176], [43781, 26], [43837, 34], [43900, 109], [44014, 6], [44026, 54], [44082, 27], [44133, 41], [44197, 108], [44330, 50], [44383, 29], [44429, 49], [44493, 110], [44632, 49], [44683, 37], [44721, 181], [44934, 48], [44984, 215], [45237, 45], [45284, 212], [45541, 42], [45585, 209], [45844, 39], [45885, 208], [46145, 38], [46186, 206], [46447, 37], [46486, 205], [46748, 36], [46787, 204], [47049, 34], [47087, 203], [47350, 33], [47388, 202], [47651, 32], [47689, 200], [47952, 30], [47990, 198], [48254, 28], [48291, 196], [48555, 27], [48592, 195], [48856, 27], [48892, 8], [48931, 155], [49158, 26], [49193, 7], [49231, 154], [49459, 25], [49486, 1], [49488, 1], [49491, 1], [49493, 7], [49532, 153], [49759, 26], [49787, 1], [49789, 1], [49792, 1], [49794, 6], [49832, 31], [49884, 101], [50060, 26], [50087, 2], [50090, 1], [50092, 2], [50095, 5], [50133, 15], [50200, 85], [50361, 26], [50388, 1], [50391, 1], [50393, 1], [50396, 4], [50432, 5], [50508, 77], [50661, 27], [50689, 1], [50692, 1], [50694, 1], [50696, 4], [50812, 73], [50962, 27], [50990, 1], [50993, 1], [50995, 1], [50997, 3], [51113, 72], [51262, 28], [51291, 1], [51293, 2], [51296, 1], [51298, 2], [51415, 71], [51562, 29], [51592, 1], [51594, 2], [51597, 1], [51599, 1], [51716, 70], [51862, 38], [52016, 70], [52162, 38], [52317, 69], [52463, 37], [52617, 69], [52764, 36], [52918, 68], [53064, 36], [53218, 68], [53365, 35], [53518, 68], [53665, 35], [53818, 68], [53966, 34], [54118, 68], [54266, 34], [54419, 68], [54566, 34], [54719, 68], [54866, 34], [55019, 69], [55166, 51], [55319, 69], [55466, 51], [55619, 71], [55765, 51], [55919, 72], [56065, 51], [56219, 73], [56364, 51], [56519, 74], [56663, 52], [56819, 75], [56963, 52], [57119, 75], [57263, 51], [57419, 76], [57563, 51], [57719, 77], [57863, 51], [58019, 77], [58163, 51], [58319, 78], [58462, 51], [58619, 79], [58762, 51], [58919, 80], [59061, 52], [59219, 82], [59360, 53], [59519, 83], [59659, 54], [59819, 85], [59958, 55], [60119, 88], [60255, 58], [60419, 91], [60553, 61], [60718, 94], [60851, 64], [61018, 96], [61150, 65], [61318, 98], [61449, 66], [61618, 99], [61748, 67], [61918, 103], [62032, 1], [62045, 70], [62218, 198], [62518, 198], [62817, 200], [63117, 200], [63417, 201], [63717, 202], [64017, 203], [64316, 205], [64615, 207], [64914, 209], [65214, 210], [65513, 212], [65809, 219], [66103, 231], [66337, 1908], [68255, 287], [68558, 281], [68861, 276], [69163, 273], [69464, 270], [69766, 267], [70067, 265], [70368, 263], [70669, 261], [70970, 259], [71271, 129]], "point": [149, 157]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan2", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": 0.75, "y": 0.9009992, "z": 1.0}, "object_poses": [{"objectName": "Potato_5e728867", "position": {"x": -1.7504822, "y": 0.586915851, "z": 0.2956789}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_5e728867", "position": {"x": -0.08891332, "y": 0.7843743, "z": -1.32494032}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_ec6bb035", "position": {"x": 1.713, "y": 0.503753066, "z": 0.5310277}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_233c7df0", "position": {"x": 0.000411391258, "y": 0.9266413, "z": 0.0417993069}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_e1c57c26", "position": {"x": 1.34167874, "y": 1.67920291, "z": -1.65244007}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_e1c57c26", "position": {"x": -0.744763732, "y": 1.67920291, "z": -1.65244007}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_c80d57de", "position": {"x": 0.1074348, "y": 0.926148534, "z": 0.9702008}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_1bc23cfd", "position": {"x": 1.9753, "y": 0.938299954, "z": 0.8192}, "rotation": {"x": 0.0, "y": 180.000214, "z": 0.0}}, {"objectName": "Pot_1bc23cfd", "position": {"x": 0.000411391258, "y": 0.929685533, "z": 0.273899674}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7f5e18ec", "position": {"x": -0.106612019, "y": 0.982472539, "z": 1.20230114}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7f5e18ec", "position": {"x": -0.106612019, "y": 0.982472539, "z": -0.190301061}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7f5e18ec", "position": {"x": -1.81868351, "y": 0.0991948545, "z": 1.28883886}, "rotation": {"x": 0.0, "y": 269.999939, "z": 0.0}}, {"objectName": "Cup_4e364eea", "position": {"x": 1.9564, "y": 1.00455368, "z": -0.808987558}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_4e364eea", "position": {"x": 2.03011179, "y": 0.9096306, "z": -1.41596878}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d852c2bd", "position": {"x": 0.214458212, "y": 0.92430824, "z": 0.7381004}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_e0496240", "position": {"x": -0.106612019, "y": 0.9475927, "z": 0.7381004}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_e0496240", "position": {"x": 1.90011775, "y": 0.933492661, "z": 1.40879107}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_7ce82db0", "position": {"x": 0.138286144, "y": 0.116321623, "z": -1.53428245}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_7ce82db0", "position": {"x": -1.727078, "y": 0.9150344, "z": -0.9641914}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_448791c6", "position": {"x": -1.80318046, "y": 1.72807324, "z": -0.269365072}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_586649ce", "position": {"x": 1.55363727, "y": 0.907806158, "z": 1.25821185}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_14add4df", "position": {"x": 0.889933, "y": 0.9078062, "z": -1.2219063}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_14add4df", "position": {"x": -1.2440033, "y": 0.109772086, "z": -1.42369986}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_48e00ad2", "position": {"x": 0.321481615, "y": 1.00617969, "z": 0.273899674}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_48e00ad2", "position": {"x": -0.21363543, "y": 1.00617969, "z": 0.0417993069}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_aa2e533f", "position": {"x": -1.80318046, "y": 1.545918, "z": 0.0131571312}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_b045e42b", "position": {"x": 1.3554858, "y": 0.9554, "z": -1.36239815}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_7ce82db0", "position": {"x": 2.00143743, "y": 1.66291428, "z": -1.0534749}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_4e364eea", "position": {"x": -0.193877369, "y": 0.112982452, "z": -1.222}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_1bc23cfd", "position": {"x": -0.8440597, "y": 0.9155855, "z": -1.513}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_c80d57de", "position": {"x": -0.175260648, "y": 0.761128, "z": -1.49077988}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Ladle_ec6bb035", "position": {"x": 1.19580007, "y": 0.1534797, "z": -1.47949946}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_233c7df0", "position": {"x": -1.88043761, "y": 1.66302109, "z": -0.980649233}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_7f5e18ec", "position": {"x": -1.46391475, "y": 0.9683725, "z": -1.01392}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d852c2bd", "position": {"x": -0.3059555, "y": 0.11356014, "z": -1.472}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_6d10ab8d", "position": {"x": 1.765, "y": 0.938299954, "z": 0.3846}, "rotation": {"x": 0.0, "y": 180.000214, "z": 0.0}}, {"objectName": "Lettuce_aa2e533f", "position": {"x": 0.170128584, "y": 0.8372748, "z": -1.49077988}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_de267f63", "position": {"x": 1.71401417, "y": 0.7783038, "z": 1.36581}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_487a7950", "position": {"x": -1.78322244, "y": 0.8925956, "z": -0.08101642}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_e0496240", "position": {"x": 1.802076, "y": 0.9334927, "z": -1.2219063}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_586649ce", "position": {"x": 1.66067827, "y": 0.691707, "z": 0.41386044}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_14add4df", "position": {"x": -1.114, "y": 1.67594481, "z": -1.57705986}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_6cb12d41", "position": {"x": 0.661897242, "y": 0.909427, "z": -1.31893754}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_4f8c70c0", "position": {"x": 1.64025736, "y": 0.911542058, "z": 1.25821185}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_48e00ad2", "position": {"x": -0.21363543, "y": 1.00617969, "z": 0.273899674}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_448791c6", "position": {"x": -0.08891334, "y": 0.8156184, "z": -1.49077988}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5e728867", "position": {"x": 1.57404029, "y": 0.935231745, "z": -1.31893754}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_e1c57c26", "position": {"x": 0.214458212, "y": 0.9251642, "z": 0.0417993069}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_bfeb74cb", "position": {"x": -0.638020456, "y": 0.912629366, "z": -1.513}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_b045e42b", "position": {"x": 1.95639992, "y": 1.00142312, "z": -0.5996125}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bowl_253fa632", "position": {"x": 1.65311718, "y": 0.9099406, "z": 0.103804827}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 85661502, "scene_num": 2}, "task_id": "trial_T20190908_092533_792300", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AJQGWGESKQT4Y_39PAAFCODPRED2CHLC73PH9MZX9TVT", "high_descs": ["Go forward and then go right and turn to the right to face the counter with the egg on it.", "Pick the egg up from the counter.", "Turn to the right and stand in front of the microwave.", "Put the egg in the microwave and close the door and then open the door and pick up the egg again.", "Turn around and face the counter behind.", "Put the egg down on the counter to the right of the bread."], "task_desc": "Put a heated egg on the counter.", "votes": [1, 1, 1, 1, 1]}, {"assignment_id": "A2871R3LEPWMMK_3M0NZ3JDP4PT1K61LDE877G475F5Z0", "high_descs": ["Cross the room, hang a right at the silver dishwasher and turn right at the sink to face the side of the island counter.", "Pick up the egg in front of the loaf of bread on the counter.", "Turn right and walk to the microwave on the counter ahead.", "Put the egg inside the microwave between the two mugs, heat it for a few seconds, remove the heated egg and close the door.", "Turn left and hang a left at the cook top range on the right to reach the island counter.", "Put the heated egg to the right of the loaf of bread on the counter."], "task_desc": "Place a heated egg on a counter.", "votes": [1, 1, 1, 1, 1]}, {"assignment_id": "AKW57KYG90X61_3C5W7UE9CIH24WRSCU79ROBFLDBMX4", "high_descs": ["move right to the table", "pick up an egg from the table", "move right to the microwave", "place the egg to cook and take it out", "turn around and head to the center table", "place the egg on the center table"], "task_desc": "place a cooked egg on the center table", "votes": [1, 1, 0]}]}}