{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000034.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000047.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000048.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000049.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 3, "image_name": "000000058.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000059.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000060.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000061.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000062.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000063.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000064.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000065.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000066.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000067.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000068.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000071.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000072.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000073.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000074.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000075.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000076.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000077.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000078.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000079.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000081.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000084.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000085.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000086.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000087.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000088.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000089.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000090.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000092.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000095.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000096.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000097.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000098.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000099.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000100.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000101.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000102.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000103.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000105.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000108.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000109.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000110.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000111.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000112.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000113.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000114.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000115.png", "low_idx": 15}, {"high_idx": 5, "image_name": "000000116.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000117.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000118.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000119.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000120.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000121.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000122.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000123.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000124.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000125.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000126.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000127.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000128.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000129.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000130.png", "low_idx": 16}, {"high_idx": 6, "image_name": "000000131.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000132.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000133.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000134.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000135.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000136.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000137.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000138.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000139.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000140.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000141.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000142.png", "low_idx": 18}, {"high_idx": 6, "image_name": "000000143.png", "low_idx": 18}, {"high_idx": 6, "image_name": "000000144.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000145.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000146.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000147.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000148.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000149.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000150.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000151.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000152.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000153.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000154.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000155.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000156.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000157.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000158.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000159.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000160.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000161.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000162.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000163.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000164.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000165.png", "low_idx": 20}, {"high_idx": 7, "image_name": "000000166.png", "low_idx": 21}, {"high_idx": 7, "image_name": "000000167.png", "low_idx": 21}, {"high_idx": 7, "image_name": "000000168.png", "low_idx": 21}, {"high_idx": 7, "image_name": "000000169.png", "low_idx": 21}, {"high_idx": 7, "image_name": "000000170.png", "low_idx": 22}, {"high_idx": 7, "image_name": "000000171.png", "low_idx": 22}, {"high_idx": 7, "image_name": "000000172.png", "low_idx": 22}, {"high_idx": 7, "image_name": "000000173.png", "low_idx": 22}, {"high_idx": 7, "image_name": "000000174.png", "low_idx": 22}, {"high_idx": 7, "image_name": "000000175.png", "low_idx": 22}, {"high_idx": 7, "image_name": "000000176.png", "low_idx": 22}, {"high_idx": 7, "image_name": "000000177.png", "low_idx": 22}, {"high_idx": 7, "image_name": "000000178.png", "low_idx": 22}, {"high_idx": 7, "image_name": "000000179.png", "low_idx": 22}, {"high_idx": 7, "image_name": "000000180.png", "low_idx": 22}, {"high_idx": 7, "image_name": "000000181.png", "low_idx": 22}, {"high_idx": 7, "image_name": "000000182.png", "low_idx": 22}, {"high_idx": 7, "image_name": "000000183.png", "low_idx": 22}, {"high_idx": 7, "image_name": "000000184.png", "low_idx": 22}, {"high_idx": 7, "image_name": "000000185.png", "low_idx": 23}, {"high_idx": 7, "image_name": "000000186.png", "low_idx": 23}, {"high_idx": 7, "image_name": "000000187.png", "low_idx": 23}, {"high_idx": 7, "image_name": "000000188.png", "low_idx": 23}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "ToiletPaper", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["dresser"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|1|13|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [1.212646244, 1.212646244, 15.31869888, 15.31869888, 4.63944864, 4.63944864]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON>", [0.82, 0.82, 15.092, 15.092, 3.236, 3.236]], "forceVisible": true, "objectId": "ToiletPaper|+00.30|+01.16|+03.83"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|13|0|60"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [1.212646244, 1.212646244, 15.31869888, 15.31869888, 4.63944864, 4.63944864]], "coordinateReceptacleObjectId": ["Drawer", [0.82, 0.82, 15.168, 15.168, 3.7637912, 3.7637912]], "forceVisible": true, "objectId": "ToiletPaper|+00.30|+01.16|+03.83", "receptacleObjectId": "Drawer|+00.21|+00.94|+03.79"}}, {"discrete_action": {"action": "GotoLocation", "args": ["dresser"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|2|13|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [1.997938872, 1.997938872, 15.06043148, 15.06043148, 4.63944864, 4.63944864]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON>", [0.82, 0.82, 15.092, 15.092, 3.236, 3.236]], "forceVisible": true, "objectId": "ToiletPaper|+00.50|+01.16|+03.77"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|1|13|0|60"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [1.997938872, 1.997938872, 15.06043148, 15.06043148, 4.63944864, 4.63944864]], "coordinateReceptacleObjectId": ["Drawer", [0.82, 0.82, 15.168, 15.168, 3.7637912, 3.7637912]], "forceVisible": true, "objectId": "ToiletPaper|+00.50|+01.16|+03.77", "receptacleObjectId": "Drawer|+00.21|+00.94|+03.79"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|+00.30|+01.16|+03.83"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [163, 64, 178, 112], "mask": [[19068, 8], [19366, 12], [19665, 14], [19965, 14], [20265, 14], [20565, 14], [20865, 14], [21165, 14], [21465, 14], [21765, 14], [22065, 14], [22365, 14], [22665, 14], [22965, 14], [23265, 13], [23565, 13], [23865, 13], [24165, 13], [24465, 13], [24764, 14], [25064, 14], [25364, 14], [25664, 14], [25964, 14], [26264, 14], [26564, 13], [26864, 13], [27164, 13], [27464, 13], [27764, 13], [28064, 13], [28364, 13], [28664, 13], [28964, 13], [29264, 13], [29564, 13], [29864, 13], [30164, 12], [30464, 12], [30764, 12], [31064, 12], [31364, 12], [31663, 13], [31963, 13], [32263, 13], [32563, 13], [32864, 12], [33165, 9], [33468, 4]], "point": [170, 87]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|+00.21|+00.94|+03.79"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 92, 264, 148], "mask": [[27302, 260], [27601, 262], [27900, 264], [28200, 264], [28500, 265], [28800, 265], [29100, 265], [29400, 264], [29701, 263], [30001, 262], [30302, 260], [30603, 259], [30904, 257], [31204, 257], [31505, 255], [31806, 254], [32107, 252], [32407, 251], [32708, 250], [33009, 248], [33310, 247], [33610, 246], [33911, 245], [34212, 243], [34513, 242], [34813, 241], [35114, 239], [35415, 238], [35716, 236], [36016, 236], [36317, 234], [36618, 233], [36919, 231], [37219, 230], [37520, 229], [37821, 227], [38122, 226], [38422, 225], [38723, 224], [39024, 222], [39325, 220], [39625, 220], [39926, 218], [40227, 217], [40527, 216], [40828, 215], [41129, 213], [41430, 211], [41730, 211], [42031, 209], [42332, 208], [42633, 206], [42933, 206], [43234, 204], [43535, 203], [43836, 201], [44137, 198]], "point": [132, 119]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|+00.30|+01.16|+03.83", "placeStationary": true, "receptacleObjectId": "Drawer|+00.21|+00.94|+03.79"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 92, 284, 204], "mask": [[27311, 243], [27610, 244], [27910, 245], [28210, 245], [28509, 246], [28809, 247], [29108, 248], [29408, 248], [29708, 248], [30007, 250], [30307, 250], [30607, 250], [30906, 251], [31206, 252], [31506, 252], [31805, 253], [32105, 253], [32405, 254], [32704, 255], [33004, 255], [33303, 257], [33603, 257], [33903, 257], [34202, 258], [34502, 259], [34802, 259], [35101, 260], [35401, 260], [35701, 261], [36000, 262], [36300, 262], [36600, 262], [36900, 263], [37200, 263], [37500, 263], [37800, 264], [38100, 264], [38400, 264], [38700, 264], [39000, 265], [39300, 265], [39600, 265], [39900, 265], [40200, 266], [40500, 266], [40800, 266], [41100, 266], [41400, 267], [41700, 267], [42000, 267], [42300, 268], [42600, 268], [42900, 268], [43200, 268], [43500, 269], [43800, 269], [44100, 269], [44400, 269], [44700, 270], [45000, 270], [45300, 270], [45600, 270], [45900, 271], [46200, 271], [46500, 271], [46800, 272], [47100, 272], [47400, 281], [47700, 282], [48000, 284], [48300, 284], [48600, 284], [48900, 284], [49200, 285], [49500, 285], [49800, 284], [50100, 283], [50400, 282], [50700, 281], [51000, 280], [51300, 279], [51600, 278], [51900, 277], [52200, 276], [52500, 275], [52800, 274], [53100, 273], [53400, 272], [53700, 271], [54000, 270], [54300, 269], [54600, 268], [54900, 267], [55200, 266], [55500, 265], [55800, 264], [56101, 262], [56402, 260], [56703, 258], [57005, 255], [57306, 253], [57607, 251], [57909, 248], [58210, 246], [58511, 244], [58813, 241], [59114, 239], [59415, 237], [59717, 234], [60018, 232], [60319, 230], [60621, 129], [60752, 96], [60922, 123], [61055, 92]], "point": [142, 147]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|+00.21|+00.94|+03.79"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 92, 284, 204], "mask": [[27311, 243], [27610, 244], [27910, 245], [28210, 245], [28509, 246], [28809, 247], [29108, 248], [29408, 248], [29708, 248], [30007, 250], [30307, 250], [30607, 250], [30906, 251], [31206, 252], [31506, 252], [31805, 253], [32105, 253], [32405, 254], [32704, 255], [33004, 255], [33303, 257], [33603, 257], [33903, 257], [34202, 258], [34502, 259], [34802, 259], [35101, 260], [35401, 260], [35701, 261], [36000, 262], [36300, 262], [36600, 262], [36900, 263], [37200, 211], [37415, 48], [37500, 209], [37717, 46], [37800, 208], [38018, 46], [38100, 207], [38319, 45], [38400, 207], [38620, 44], [38700, 207], [38920, 44], [39000, 206], [39220, 45], [39300, 206], [39520, 45], [39600, 206], [39820, 45], [39900, 205], [40119, 46], [40200, 205], [40419, 47], [40500, 205], [40718, 48], [40800, 204], [41018, 48], [41100, 204], [41317, 49], [41400, 204], [41617, 50], [41700, 203], [41917, 50], [42000, 203], [42216, 51], [42300, 203], [42516, 52], [42600, 202], [42815, 53], [42900, 202], [43115, 53], [43200, 202], [43415, 53], [43500, 201], [43714, 55], [43800, 201], [44014, 55], [44100, 201], [44313, 56], [44400, 200], [44613, 56], [44700, 200], [44912, 58], [45000, 200], [45212, 58], [45300, 200], [45512, 58], [45600, 200], [45811, 59], [45900, 271], [46200, 271], [46500, 271], [46800, 272], [47100, 272], [47400, 281], [47700, 282], [48000, 284], [48300, 284], [48600, 284], [48900, 284], [49200, 285], [49500, 285], [49800, 284], [50100, 283], [50400, 282], [50700, 281], [51000, 280], [51300, 279], [51600, 278], [51900, 277], [52200, 276], [52500, 275], [52800, 274], [53100, 273], [53400, 272], [53700, 271], [54000, 270], [54300, 269], [54600, 268], [54900, 267], [55200, 266], [55500, 265], [55800, 264], [56101, 262], [56402, 260], [56703, 258], [57005, 255], [57306, 253], [57607, 251], [57909, 248], [58210, 246], [58511, 244], [58813, 241], [59114, 239], [59415, 237], [59717, 234], [60018, 232], [60319, 230], [60621, 227], [60922, 225]], "point": [142, 147]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|+00.50|+01.16|+03.77"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [126, 69, 171, 135], "mask": [[20549, 4], [20840, 18], [21137, 26], [21435, 30], [21733, 34], [22031, 37], [22329, 40], [22628, 43], [22928, 44], [23228, 44], [23528, 44], [23827, 45], [24127, 45], [24427, 45], [24726, 46], [25027, 45], [25327, 45], [25627, 45], [25927, 45], [26227, 45], [26527, 45], [26827, 45], [27127, 45], [27428, 43], [27728, 43], [28028, 43], [28328, 43], [28628, 43], [28928, 43], [29228, 43], [29528, 43], [29828, 43], [30128, 43], [30428, 43], [30729, 42], [31029, 42], [31329, 42], [31629, 42], [31929, 41], [32229, 41], [32529, 41], [32829, 41], [33129, 41], [33429, 41], [33729, 41], [34029, 41], [34329, 41], [34629, 41], [34929, 41], [35230, 40], [35530, 40], [35829, 41], [36129, 41], [36429, 40], [36729, 40], [37029, 40], [37329, 40], [37630, 39], [37931, 38], [38232, 36], [38533, 34], [38834, 32], [39134, 31], [39435, 28], [39738, 24], [40041, 19], [40345, 9]], "point": [148, 101]}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|+00.21|+00.94|+03.79"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 92, 264, 148], "mask": [[27302, 260], [27601, 262], [27900, 264], [28200, 264], [28500, 265], [28800, 265], [29100, 265], [29400, 264], [29701, 263], [30001, 262], [30302, 260], [30603, 259], [30904, 257], [31204, 257], [31505, 255], [31806, 254], [32107, 252], [32407, 251], [32708, 250], [33009, 248], [33310, 247], [33610, 246], [33911, 245], [34212, 243], [34513, 242], [34813, 241], [35114, 239], [35415, 238], [35716, 236], [36016, 236], [36317, 234], [36618, 233], [36919, 231], [37219, 230], [37520, 229], [37821, 227], [38122, 226], [38422, 225], [38723, 224], [39024, 222], [39325, 220], [39625, 220], [39926, 218], [40227, 217], [40527, 216], [40828, 215], [41129, 213], [41430, 211], [41730, 211], [42031, 209], [42332, 208], [42633, 206], [42933, 206], [43234, 204], [43535, 203], [43836, 201], [44137, 198]], "point": [132, 119]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|+00.50|+01.16|+03.77", "placeStationary": true, "receptacleObjectId": "Drawer|+00.21|+00.94|+03.79"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 92, 284, 204], "mask": [[27311, 243], [27610, 244], [27910, 245], [28210, 245], [28509, 246], [28809, 247], [29108, 248], [29408, 248], [29708, 248], [30007, 250], [30307, 250], [30607, 250], [30906, 251], [31206, 252], [31506, 252], [31805, 253], [32105, 253], [32405, 254], [32704, 255], [33004, 255], [33303, 257], [33603, 257], [33903, 257], [34202, 258], [34502, 259], [34802, 259], [35101, 260], [35401, 260], [35701, 261], [36000, 262], [36300, 262], [36600, 262], [36900, 263], [37200, 211], [37415, 48], [37500, 209], [37717, 46], [37800, 208], [38018, 46], [38100, 207], [38319, 45], [38400, 207], [38620, 44], [38700, 207], [38920, 44], [39000, 206], [39220, 45], [39300, 206], [39520, 45], [39600, 206], [39820, 45], [39900, 205], [40119, 46], [40200, 205], [40419, 47], [40500, 205], [40718, 48], [40800, 204], [41018, 48], [41100, 204], [41317, 49], [41400, 204], [41617, 50], [41700, 203], [41917, 50], [42000, 203], [42216, 51], [42300, 203], [42516, 52], [42600, 202], [42815, 53], [42900, 202], [43115, 53], [43200, 202], [43415, 53], [43500, 201], [43714, 55], [43800, 201], [44014, 55], [44100, 201], [44313, 56], [44400, 200], [44613, 56], [44700, 200], [44912, 58], [45000, 200], [45212, 58], [45300, 200], [45512, 58], [45600, 200], [45811, 59], [45900, 271], [46200, 271], [46500, 271], [46800, 272], [47100, 272], [47400, 281], [47700, 282], [48000, 284], [48300, 284], [48600, 284], [48900, 284], [49200, 285], [49500, 285], [49800, 284], [50100, 283], [50400, 282], [50700, 281], [51000, 280], [51300, 279], [51600, 278], [51900, 277], [52200, 276], [52500, 275], [52800, 274], [53100, 273], [53400, 272], [53700, 271], [54000, 151], [54155, 115], [54300, 145], [54458, 111], [54600, 140], [54761, 107], [54900, 134], [55064, 103], [55200, 129], [55367, 99], [55500, 128], [55670, 95], [55800, 126], [55973, 91], [56101, 124], [56276, 87], [56402, 122], [56578, 84], [56703, 120], [56879, 82], [57005, 117], [57180, 80], [57306, 115], [57480, 79], [57607, 112], [57781, 77], [57909, 109], [58082, 75], [58210, 107], [58383, 73], [58511, 105], [58684, 71], [58813, 102], [58985, 69], [59114, 100], [59285, 68], [59415, 97], [59586, 66], [59717, 94], [59887, 64], [60018, 92], [60188, 62], [60319, 91], [60489, 60], [60621, 88], [60790, 58], [60922, 87], [61090, 57]], "point": [142, 147]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|+00.21|+00.94|+03.79"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 92, 284, 204], "mask": [[27311, 243], [27610, 244], [27910, 245], [28210, 245], [28509, 246], [28809, 247], [29108, 248], [29408, 248], [29708, 248], [30007, 250], [30307, 250], [30607, 250], [30906, 251], [31206, 252], [31506, 252], [31805, 253], [32105, 253], [32405, 254], [32704, 255], [33004, 255], [33303, 257], [33603, 257], [33903, 257], [34202, 159], [34362, 98], [34502, 153], [34665, 96], [34802, 148], [34968, 93], [35101, 148], [35271, 90], [35401, 147], [35573, 88], [35701, 146], [35873, 89], [36000, 146], [36174, 88], [36300, 145], [36475, 87], [36600, 144], [36776, 86], [36900, 143], [37076, 87], [37200, 142], [37377, 34], [37415, 48], [37500, 142], [37678, 31], [37717, 46], [37800, 142], [37979, 29], [38018, 46], [38100, 142], [38279, 28], [38319, 45], [38400, 142], [38578, 29], [38620, 44], [38700, 142], [38878, 29], [38920, 44], [39000, 142], [39178, 28], [39220, 45], [39300, 141], [39478, 28], [39520, 45], [39600, 141], [39778, 28], [39820, 45], [39900, 141], [40078, 27], [40119, 46], [40200, 141], [40378, 27], [40419, 47], [40500, 141], [40678, 27], [40718, 48], [40800, 141], [40978, 26], [41018, 48], [41100, 142], [41278, 26], [41317, 49], [41400, 142], [41578, 26], [41617, 50], [41700, 142], [41878, 25], [41917, 50], [42000, 142], [42177, 26], [42216, 51], [42300, 142], [42477, 26], [42516, 52], [42600, 142], [42777, 25], [42815, 53], [42900, 142], [43077, 25], [43115, 53], [43200, 142], [43377, 25], [43415, 53], [43500, 142], [43677, 24], [43714, 55], [43800, 142], [43976, 25], [44014, 55], [44100, 142], [44276, 25], [44313, 56], [44400, 142], [44576, 24], [44613, 56], [44700, 142], [44876, 24], [44912, 58], [45000, 142], [45176, 24], [45212, 58], [45300, 142], [45475, 25], [45512, 58], [45600, 142], [45775, 25], [45811, 59], [45900, 271], [46200, 271], [46500, 271], [46800, 272], [47100, 272], [47400, 281], [47700, 282], [48000, 284], [48300, 284], [48600, 284], [48900, 284], [49200, 285], [49500, 285], [49800, 284], [50100, 283], [50400, 282], [50700, 281], [51000, 280], [51300, 279], [51600, 278], [51900, 277], [52200, 276], [52500, 275], [52800, 274], [53100, 273], [53400, 272], [53700, 271], [54000, 270], [54300, 269], [54600, 268], [54900, 267], [55200, 266], [55500, 265], [55800, 264], [56101, 262], [56402, 260], [56703, 258], [57005, 255], [57306, 253], [57607, 251], [57909, 248], [58210, 246], [58511, 244], [58813, 241], [59114, 239], [59415, 237], [59717, 234], [60018, 232], [60319, 230], [60621, 227], [60922, 225]], "point": [141, 147]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan413", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -0.5, "y": 0.900691152, "z": 2.75}, "object_poses": [{"objectName": "SprayBottle_775697e0", "position": {"x": -1.79516649, "y": 0.0182126034, "z": 2.22}, "rotation": {"x": 0.0, "y": 6.83018834e-06, "z": 0.0}}, {"objectName": "SoapBottle_9a0dcc9c", "position": {"x": -0.5007916, "y": 0.44772163, "z": 0.126883268}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_d73e7170", "position": {"x": 0.499484718, "y": 1.15986216, "z": 3.76510787}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Towel_ff9c436a", "position": {"x": -0.07, "y": 1.284, "z": 0.1069}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "HandTowel_a322ac82", "position": {"x": -1.731, "y": 1.333, "z": 3.9617}, "rotation": {"x": 0.0, "y": -4.78113252e-05, "z": 0.0}}, {"objectName": "SoapBar_84c384be", "position": {"x": 0.204999983, "y": 0.35175547, "z": 3.79201841}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_d73e7170", "position": {"x": -1.70791709, "y": 0.0645169, "z": 2.60160971}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_40a19bb4", "position": {"x": 0.303161561, "y": 1.15986216, "z": 3.82967472}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cloth_27e5b947", "position": {"x": 0.106838413, "y": 1.16367412, "z": 3.86195827}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_775697e0", "position": {"x": 0.40132314, "y": 1.16040409, "z": 3.76510787}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Candle_7b917d2e", "position": {"x": 0.06032799, "y": 0.87825, "z": 3.81576252}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ScrubBrush_c9aeeb01", "position": {"x": -1.66445529, "y": -0.00030824542, "z": 3.89782381}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plunger_fd5a7685", "position": {"x": -1.88882077, "y": -0.0007712394, "z": 3.88542414}, "rotation": {"x": -0.00118193, "y": 0.0004413876, "z": 0.000780660135}}, {"objectName": "SoapBottle_9a0dcc9c", "position": {"x": -0.457497776, "y": 0.44772163, "z": 0.236972123}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1964688043, "scene_num": 413}, "task_id": "trial_T20190908_004827_769411", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A6U2C66WQ7QQN_35LDD5557DLOZ76NH33308JBVAQMKO", "high_descs": ["Head left to the white dresser", "Take the empty toilet paper tube from on top", "Open the top drawer of the dresser", "Put the toilet paper tube in the drawer", "Close the drawer to the dresser", "Take the full roll of toilet paper from on top of the dresser", "Open the top drawer of the dresser, put the toilet paper in the drawer", "Close the drawer to the dresser"], "task_desc": "Put the roll of toilet paper and empty roll in the drawer", "votes": [1, 1, 1]}, {"assignment_id": "A3PGS1Q1XAY79I_3SKEMFQBZ6M9EI4JN2TKVQIGB4D8KW", "high_descs": ["Walk in the direction of the door, hang a left towards the white cabinet.\n ", "Pick up the toilet paper tube from the top of the cabinet. \n \n ", "Locate the top drawer of the cabinet.", "Open the top drawer and place the tube on the right side of the drawer, close the drawer.", "Move to the right front of the cabinet.", "Pick up the toilet paper roll from the top of the cabinet.", "Move left to the center front of the cabinet.", "Open the top drawer and place the toilet paper roll in the drawer to the left of the toilet paper tube."], "task_desc": "Move a toilet paper tube and roll from the top of the cabinet to the top drawer", "votes": [1, 1, 1]}, {"assignment_id": "A1ELPYAFO7MANS_3VBEN272MNGMVGN9B9EAEPQOGHASGK", "high_descs": ["Turn left and walk to the dresser.", "Pick up the empty toilet paper roll on top of the dresser.", "Look down.", "Place the toilet paper roll in the drawer, on the right side.", "Look up at the dresser again.", "Pick up the full toilet paper roll on top of the dresser.", "Look down.", "Place the toilet paper roll to the left of the empty toilet paper roll in the top drawer."], "task_desc": "Place two toilet paper rolls in a drawer.", "votes": [1, 1, 0]}]}}