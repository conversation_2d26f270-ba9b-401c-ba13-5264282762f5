{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000079.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000080.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000081.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000082.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000083.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000084.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000085.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000087.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000090.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000091.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000092.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000093.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000094.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000095.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000096.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000099.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000100.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000101.png", "low_idx": 15}, {"high_idx": 5, "image_name": "000000102.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000103.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000104.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000105.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000106.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000107.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000110.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000111.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000112.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000113.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000114.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000115.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000116.png", "low_idx": 16}, {"high_idx": 6, "image_name": "000000117.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000118.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000121.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000122.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000123.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000124.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000125.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000126.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000127.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000128.png", "low_idx": 18}, {"high_idx": 6, "image_name": "000000129.png", "low_idx": 18}, {"high_idx": 6, "image_name": "000000130.png", "low_idx": 18}, {"high_idx": 6, "image_name": "000000131.png", "low_idx": 18}, {"high_idx": 6, "image_name": "000000132.png", "low_idx": 18}, {"high_idx": 6, "image_name": "000000133.png", "low_idx": 18}, {"high_idx": 6, "image_name": "000000134.png", "low_idx": 18}, {"high_idx": 6, "image_name": "000000135.png", "low_idx": 18}, {"high_idx": 6, "image_name": "000000136.png", "low_idx": 18}, {"high_idx": 6, "image_name": "000000137.png", "low_idx": 18}, {"high_idx": 6, "image_name": "000000138.png", "low_idx": 18}, {"high_idx": 6, "image_name": "000000139.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000140.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000141.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000142.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000143.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000144.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000145.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000146.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000147.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000148.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000149.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000150.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000151.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000152.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000153.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000154.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000155.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000156.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000157.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000158.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000159.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000160.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000161.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000162.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000163.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000164.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000165.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000166.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000167.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000168.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000169.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000170.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000171.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000172.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000173.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000174.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000175.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000176.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000177.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000178.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000179.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000180.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000181.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000186.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000187.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000188.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000189.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000190.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000191.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000192.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000193.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000194.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000195.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000196.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000197.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000198.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000199.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000200.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000201.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000202.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000203.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000204.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000205.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000206.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000207.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000208.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000209.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000210.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000211.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000212.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000213.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000214.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000215.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000216.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000217.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000218.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000219.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000220.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000221.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000222.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000223.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000224.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000225.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000226.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000227.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000228.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000229.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000230.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000231.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000232.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000233.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000234.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000235.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000236.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000237.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000238.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000239.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000240.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000241.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000242.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000243.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000244.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000245.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000246.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000247.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000248.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000249.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000250.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000251.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000252.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000253.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000254.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000255.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000256.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000257.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000258.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000259.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000260.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000261.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000262.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000263.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000264.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000265.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000266.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000267.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000268.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000269.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000270.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000271.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000272.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000273.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000274.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000275.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000276.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000277.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000278.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000279.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000280.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000281.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000282.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000283.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000284.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000285.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000286.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000287.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000288.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000289.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000290.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000291.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000292.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000293.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000294.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000295.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000296.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000297.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000298.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000299.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000300.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000301.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000302.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000303.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000304.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000305.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000306.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000307.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000308.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000309.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000310.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000311.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000312.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000313.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000314.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000315.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000316.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000317.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000318.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000319.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000320.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000321.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000322.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000323.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000324.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000325.png", "low_idx": 51}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "GarbageCan", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|3|2|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["butterknife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ButterKnife", [5.777236, 5.777236, 2.826878, 2.826878, 3.6461684, 3.6461684]], "coordinateReceptacleObjectId": ["CounterTop", [5.1, 5.1, 0.26, 0.26, 3.788, 3.788]], "forceVisible": true, "objectId": "ButterKnife|+01.44|+00.91|+00.71"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|3|2|1|60"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [4.513736, 4.513736, 2.826878, 2.826878, 3.8996596, 3.8996596]], "forceVisible": true, "objectId": "Bread|+01.13|+00.97|+00.71"}}, {"discrete_action": {"action": "PutObject", "args": ["butterknife", "countertop"]}, "high_idx": 4, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ButterKnife", [5.777236, 5.777236, 2.826878, 2.826878, 3.6461684, 3.6461684]], "coordinateReceptacleObjectId": ["CounterTop", [5.1, 5.1, 0.26, 0.26, 3.788, 3.788]], "forceVisible": true, "objectId": "ButterKnife|+01.44|+00.91|+00.71", "receptacleObjectId": "CounterTop|+01.28|+00.95|+00.07"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [4.513736, 4.513736, 2.826878, 2.826878, 3.8996596, 3.8996596]], "coordinateReceptacleObjectId": ["CounterTop", [5.1, 5.1, 0.26, 0.26, 3.788, 3.788]], "forceVisible": true, "objectId": "Bread|+01.13|+00.97|+00.71|BreadSliced_8"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-2|0|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|3|6|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "garbagecan"]}, "high_idx": 9, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [4.513736, 4.513736, 2.826878, 2.826878, 3.8996596, 3.8996596]], "coordinateReceptacleObjectId": ["GarbageCan", [5.18714428, 5.18714428, 5.8631954, 5.8631954, 0.032000004, 0.032000004]], "forceVisible": true, "objectId": "Bread|+01.13|+00.97|+00.71|BreadSliced_8", "receptacleObjectId": "GarbageCan|+01.30|+00.01|+01.47"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 10, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ButterKnife|+01.44|+00.91|+00.71"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [86, 123, 101, 173], "mask": [[36699, 1], [36998, 3], [37298, 3], [37598, 4], [37897, 5], [38197, 5], [38497, 5], [38797, 5], [39096, 6], [39396, 6], [39696, 6], [39996, 6], [40295, 6], [40595, 6], [40895, 6], [41195, 5], [41494, 6], [41794, 5], [42094, 5], [42394, 4], [42694, 4], [42993, 4], [43293, 4], [43593, 4], [43893, 3], [44193, 3], [44492, 4], [44792, 3], [45092, 3], [45391, 4], [45691, 4], [45991, 3], [46290, 4], [46590, 4], [46890, 4], [47189, 5], [47489, 5], [47789, 5], [48088, 6], [48388, 6], [48688, 6], [48987, 6], [49287, 6], [49587, 6], [49887, 6], [50186, 7], [50486, 6], [50786, 6], [51086, 6], [51386, 5], [51686, 4]], "point": [93, 147]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|+01.13|+00.97|+00.71"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [19, 117, 128, 178], "mask": [[34852, 44], [35142, 73], [35438, 81], [35735, 87], [36034, 89], [36332, 93], [36631, 95], [36930, 96], [37229, 98], [37528, 99], [37828, 100], [38127, 101], [38427, 102], [38726, 103], [39026, 103], [39325, 104], [39625, 104], [39925, 104], [40224, 105], [40524, 105], [40824, 105], [41123, 106], [41423, 106], [41723, 106], [42023, 106], [42323, 106], [42622, 106], [42922, 106], [43222, 106], [43522, 106], [43821, 107], [44121, 107], [44421, 107], [44721, 107], [45021, 107], [45321, 107], [45620, 108], [45920, 108], [46220, 108], [46520, 108], [46820, 108], [47120, 108], [47419, 109], [47719, 109], [48019, 109], [48319, 109], [48619, 109], [48919, 109], [49220, 107], [49520, 107], [49821, 106], [50122, 105], [50422, 105], [50722, 105], [51023, 104], [51324, 103], [51624, 103], [51925, 102], [52226, 101], [52527, 98], [52828, 95], [53149, 23], [53181, 18]], "point": [73, 146]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ButterKnife|+01.44|+00.91|+00.71", "placeStationary": true, "receptacleObjectId": "CounterTop|+01.28|+00.95|+00.07"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 52, 299, 204], "mask": [[15332, 268], [15633, 267], [15933, 267], [16233, 267], [16533, 267], [16832, 268], [17132, 268], [17432, 268], [17731, 269], [18031, 269], [18331, 269], [18630, 270], [18930, 270], [19230, 270], [19529, 271], [19829, 140], [19973, 127], [20129, 131], [20283, 76], [20365, 35], [20428, 127], [20588, 64], [20673, 27], [20728, 122], [20893, 56], [20977, 23], [21028, 118], [21198, 48], [21280, 20], [21327, 118], [21498, 46], [21583, 17], [21627, 117], [21799, 44], [21885, 15], [21927, 117], [22100, 41], [22187, 13], [22226, 117], [22400, 40], [22489, 11], [22526, 117], [22701, 38], [22791, 9], [22826, 116], [23001, 37], [23092, 8], [23125, 117], [23302, 35], [23394, 6], [23425, 117], [23602, 34], [23695, 5], [23725, 116], [23903, 32], [23996, 4], [24024, 117], [24204, 31], [24298, 2], [24324, 116], [24504, 30], [24599, 1], [24624, 116], [24804, 30], [24923, 117], [25105, 28], [25223, 117], [25405, 28], [25523, 116], [25706, 27], [25822, 117], [26006, 27], [26122, 117], [26306, 26], [26422, 117], [26606, 26], [26721, 118], [26907, 25], [27021, 118], [27207, 25], [27321, 118], [27507, 25], [27620, 119], [27807, 25], [27920, 119], [28107, 26], [28220, 119], [28407, 26], [28519, 120], [28707, 26], [28819, 120], [29007, 26], [29119, 120], [29307, 26], [29418, 122], [29607, 26], [29718, 122], [29907, 26], [30018, 122], [30206, 27], [30317, 124], [30506, 27], [30617, 124], [30806, 27], [30917, 124], [31106, 28], [31216, 126], [31405, 29], [31516, 126], [31705, 29], [31816, 126], [32005, 29], [32115, 128], [32305, 29], [32415, 128], [32604, 30], [32715, 129], [32904, 30], [33014, 130], [33204, 30], [33314, 130], [33504, 30], [33614, 131], [33803, 32], [33913, 135], [34100, 35], [34213, 139], [34396, 39], [34513, 144], [34692, 44], [34812, 49], [34872, 4], [34884, 1], [34891, 71], [34987, 50], [35112, 35], [35222, 47], [35279, 58], [35412, 29], [35527, 111], [35711, 26], [35828, 2], [35831, 107], [35999, 1], [36011, 24], [36134, 105], [36297, 3], [36311, 22], [36436, 104], [36595, 5], [36610, 22], [36737, 104], [36893, 7], [36910, 21], [37037, 1], [37039, 103], [37191, 9], [37210, 20], [37340, 104], [37489, 11], [37509, 20], [37641, 104], [37787, 13], [37809, 19], [37941, 105], [38085, 15], [38109, 19], [38241, 107], [38383, 17], [38408, 19], [38542, 108], [38680, 20], [38708, 19], [38842, 110], [38978, 22], [39008, 18], [39142, 113], [39276, 24], [39307, 19], [39442, 116], [39573, 27], [39607, 19], [39742, 158], [39907, 18], [40043, 157], [40207, 18], [40343, 157], [40506, 18], [40643, 157], [40806, 18], [40943, 157], [41106, 18], [41243, 157], [41405, 19], [41543, 157], [41705, 19], [41843, 157], [42005, 18], [42143, 101], [42248, 52], [42304, 19], [42443, 90], [42550, 38], [42594, 6], [42604, 19], [42743, 83], [42851, 35], [42897, 3], [42903, 20], [43043, 82], [43151, 34], [43199, 1], [43203, 19], [43343, 81], [43451, 33], [43502, 20], [43643, 81], [43751, 32], [43800, 22], [43943, 81], [44052, 29], [44100, 22], [44243, 80], [44352, 29], [44400, 22], [44543, 19], [44588, 35], [44652, 28], [44700, 21], [44843, 7], [44952, 27], [45000, 21], [45143, 7], [45252, 26], [45300, 21], [45443, 7], [45488, 9], [45553, 24], [45600, 21], [45742, 8], [45773, 46], [45853, 24], [45900, 21], [46042, 82], [46153, 23], [46200, 21], [46342, 82], [46453, 22], [46500, 20], [46642, 83], [46754, 20], [46800, 20], [46942, 83], [47054, 20], [47100, 20], [47242, 84], [47353, 20], [47400, 20], [47542, 85], [47652, 20], [47700, 20], [47842, 86], [47941, 31], [48000, 20], [48142, 129], [48300, 20], [48442, 129], [48600, 20], [48742, 128], [48900, 20], [49042, 128], [49200, 20], [49342, 128], [49500, 20], [49642, 129], [49800, 21], [49942, 129], [50100, 22], [50242, 129], [50400, 22], [50527, 1], [50542, 129], [50700, 23], [50827, 1], [50841, 131], [51000, 23], [51141, 131], [51300, 24], [51441, 131], [51600, 25], [51741, 131], [51900, 25], [52041, 132], [52200, 26], [52341, 132], [52500, 27], [52619, 1], [52628, 1], [52636, 1], [52640, 133], [52800, 28], [52910, 1], [52919, 1], [52928, 1], [52936, 137], [53100, 31], [53139, 1], [53174, 1], [53178, 3], [53183, 1], [53192, 1], [53201, 1], [53210, 1], [53218, 2], [53223, 151], [53400, 274], [53700, 274], [54000, 274], [54300, 275], [54600, 275], [54899, 276], [55198, 277], [55496, 280], [55794, 282], [56093, 284], [56391, 287], [56689, 290], [56987, 4213]], "point": [149, 127]}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Bread|+01.13|+00.97|+00.71|BreadSliced_8"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [136, 124, 142, 176], "mask": [[37038, 1], [37337, 3], [37637, 4], [37937, 4], [38237, 4], [38537, 5], [38837, 5], [39137, 5], [39437, 5], [39737, 5], [40037, 6], [40337, 6], [40637, 6], [40937, 6], [41237, 6], [41536, 7], [41836, 7], [42136, 7], [42436, 7], [42736, 7], [43036, 7], [43336, 7], [43636, 7], [43936, 7], [44236, 7], [44536, 7], [44836, 7], [45136, 7], [45436, 7], [45736, 6], [46036, 6], [46336, 6], [46636, 6], [46936, 6], [47236, 6], [47536, 6], [47836, 6], [48136, 6], [48436, 6], [48736, 6], [49036, 6], [49336, 6], [49637, 5], [49937, 5], [50237, 5], [50537, 5], [50837, 4], [51137, 4], [51437, 4], [51737, 4], [52037, 4], [52337, 4], [52637, 3]], "point": [139, 149]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|+01.13|+00.97|+00.71|BreadSliced_8", "placeStationary": true, "receptacleObjectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 236], [20744, 236], [21044, 236], [21343, 236], [21643, 236], [21942, 237], [22241, 237], [22541, 237], [22840, 237], [23140, 237], [23439, 238], [23739, 237], [24038, 238], [24337, 239], [24637, 238], [24936, 239], [25236, 238], [25535, 239], [25834, 240], [26134, 239], [26433, 240], [26733, 239], [27032, 240], [27331, 241], [27631, 240], [27930, 241], [28230, 241], [28529, 241], [28829, 241], [29128, 241], [29427, 242], [29727, 242], [30026, 242], [30326, 242], [30625, 243], [30924, 243], [31224, 243], [31523, 243], [31823, 243], [32122, 244], [32421, 244], [32721, 244], [33020, 244], [33320, 244], [33619, 245], [33918, 245], [34218, 245], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 236], [20744, 236], [21044, 236], [21343, 236], [21643, 236], [21942, 237], [22241, 237], [22541, 237], [22840, 237], [23140, 237], [23439, 238], [23739, 237], [24038, 238], [24337, 239], [24637, 238], [24936, 239], [25236, 238], [25535, 239], [25834, 240], [26134, 239], [26433, 240], [26733, 239], [27032, 240], [27331, 241], [27631, 240], [27930, 111], [28057, 114], [28230, 105], [28362, 109], [28529, 103], [28665, 105], [28829, 102], [28967, 103], [29128, 102], [29268, 101], [29427, 102], [29568, 101], [29727, 102], [29868, 101], [30026, 103], [30168, 100], [30326, 103], [30468, 100], [30625, 104], [30768, 100], [30924, 104], [31069, 98], [31224, 104], [31370, 97], [31523, 104], [31670, 96], [31823, 104], [31970, 96], [32122, 105], [32270, 96], [32421, 106], [32570, 95], [32721, 106], [32870, 95], [33020, 107], [33170, 94], [33320, 107], [33470, 94], [33619, 110], [33769, 95], [33918, 113], [34068, 95], [34218, 245], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 7}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 7}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 7}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 7}, {"api_action": {"action": "PickupObject", "objectId": "Bread|+01.13|+00.97|+00.71|BreadSliced_8"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [127, 94, 169, 114], "mask": [[28041, 16], [28335, 27], [28632, 33], [28931, 36], [29230, 38], [29529, 39], [29829, 39], [30129, 39], [30429, 39], [30729, 39], [31028, 41], [31328, 42], [31627, 43], [31927, 43], [32227, 43], [32527, 43], [32827, 43], [33127, 43], [33427, 43], [33729, 40], [34031, 37]], "point": [148, 103]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 236], [20744, 236], [21044, 236], [21343, 236], [21643, 236], [21942, 237], [22241, 237], [22541, 237], [22840, 237], [23140, 237], [23439, 238], [23739, 237], [24038, 238], [24337, 239], [24637, 238], [24936, 239], [25236, 238], [25535, 239], [25834, 240], [26134, 239], [26433, 240], [26733, 239], [27032, 240], [27331, 241], [27631, 240], [27930, 241], [28230, 241], [28529, 241], [28829, 241], [29128, 241], [29427, 242], [29727, 242], [30026, 242], [30326, 242], [30625, 243], [30924, 243], [31224, 243], [31523, 243], [31823, 243], [32122, 244], [32421, 244], [32721, 244], [33020, 244], [33320, 244], [33619, 245], [33918, 245], [34218, 245], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 7}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|+01.13|+00.97|+00.71|BreadSliced_8", "placeStationary": true, "receptacleObjectId": "GarbageCan|+01.30|+00.01|+01.47"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [108, 104, 206, 243], "mask": [[31046, 43], [31340, 51], [31638, 55], [31935, 59], [32233, 61], [32532, 63], [32831, 64], [33130, 65], [33429, 67], [33728, 68], [34027, 69], [34325, 72], [34624, 73], [34923, 74], [35223, 74], [35522, 75], [35822, 75], [36121, 77], [36420, 78], [36720, 78], [37019, 79], [37319, 79], [37618, 80], [37918, 80], [38217, 81], [38516, 83], [38816, 83], [39115, 84], [39415, 84], [39715, 84], [40014, 85], [40314, 85], [40614, 85], [40913, 86], [41213, 87], [41513, 87], [41812, 88], [42112, 88], [42411, 89], [42711, 89], [43011, 89], [43310, 90], [43610, 91], [43910, 91], [44210, 91], [44510, 91], [44810, 91], [45110, 91], [45410, 91], [45710, 91], [46010, 91], [46309, 93], [46609, 93], [46909, 93], [47209, 93], [47509, 93], [47809, 93], [48109, 93], [48409, 93], [48709, 93], [49009, 94], [49309, 94], [49609, 94], [49908, 95], [50208, 95], [50508, 95], [50808, 95], [51108, 95], [51409, 94], [51709, 95], [52009, 95], [52309, 95], [52609, 95], [52909, 95], [53210, 94], [53510, 94], [53810, 94], [54110, 94], [54410, 95], [54710, 95], [55010, 95], [55311, 94], [55611, 94], [55911, 94], [56212, 93], [56512, 93], [56812, 94], [57113, 93], [57413, 48], [57463, 43], [57713, 48], [57763, 43], [58014, 46], [58063, 43], [58314, 46], [58363, 43], [58614, 46], [58663, 43], [58915, 45], [58963, 43], [59215, 45], [59263, 43], [59516, 43], [59563, 43], [59816, 42], [59863, 44], [60116, 42], [60163, 44], [60417, 41], [60464, 43], [60718, 40], [60764, 43], [61019, 39], [61063, 44], [61319, 39], [61363, 44], [61620, 38], [61663, 44], [61921, 37], [61963, 44], [62221, 37], [62263, 44], [62522, 85], [62822, 84], [63122, 84], [63423, 83], [63723, 83], [64023, 82], [64324, 81], [64624, 81], [64924, 81], [65225, 79], [65525, 78], [65825, 77], [66126, 75], [66426, 74], [66726, 73], [67027, 68], [67327, 68], [67628, 66], [67928, 66], [68228, 65], [68529, 64], [68830, 62], [69131, 60], [69431, 60], [69732, 58], [70033, 57], [70334, 55], [70634, 54], [70935, 53], [71236, 51], [71538, 49], [71840, 46], [72142, 43], [72472, 12], [72781, 2]], "point": [157, 172]}}, "high_idx": 9}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan17", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -0.25, "y": 0.908999562, "z": 1.75}, "object_poses": [{"objectName": "SprayBottle_4f8d0eee", "position": {"x": 1.0494653, "y": 0.9109041, "z": 0.1391859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_4f8d0eee", "position": {"x": 1.20740271, "y": 0.9109041, "z": -0.4283477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_e3d6b27e", "position": {"x": -1.15602934, "y": 0.77432996, "z": -0.136830419}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "CellPhone_e3d6b27e", "position": {"x": -0.91988194, "y": 0.747337937, "z": 2.459}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_4b57442b", "position": {"x": -1.29051554, "y": 0.7484642, "z": 2.83014464}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_4b57442b", "position": {"x": -0.144537389, "y": 0.7581204, "z": -0.533726156}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": -1.383174, "y": 0.74966836, "z": 2.08785558}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_6b7178a8", "position": {"x": -1.47583234, "y": 0.7498287, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_1d6d1e51", "position": {"x": 1.284095, "y": 0.8874294, "z": 2.80043745}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_9f8771c2", "position": {"x": -1.19785714, "y": 0.749448538, "z": 1.90228331}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_9f8771c2", "position": {"x": -1.10519874, "y": 0.7501748, "z": 2.459}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": 1.128434, "y": 0.9264999, "z": 0.4229527}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": -0.6586952, "y": 0.9270999, "z": -0.430464923}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_92502562", "position": {"x": 0.5029, "y": 0.9393, "z": -0.7406}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_572193ee", "position": {"x": -1.19416761, "y": 0.3408087, "z": 0.6739378}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_572193ee", "position": {"x": -1.26391447, "y": 1.63620532, "z": 0.7816506}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": 1.36534023, "y": 0.9123855, "z": 0.4229527}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_e0c21b7c", "position": {"x": -0.91988194, "y": 0.7502286, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_7e041f70", "position": {"x": -1.3209753, "y": 1.635398, "z": 1.08649826}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_7e041f70", "position": {"x": 1.28637147, "y": 0.90994066, "z": 0.1391859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_74101882", "position": {"x": -1.01254034, "y": 0.813041151, "z": 2.08785558}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_74101882", "position": {"x": -1.29051554, "y": 0.813041151, "z": 2.459}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b6eaf348", "position": {"x": -1.383174, "y": 0.7452062, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c297f602", "position": {"x": -1.0601548, "y": 0.907099962, "z": -0.732326269}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_7daf87a0", "position": {"x": -1.09273458, "y": 0.8113616, "z": 0.778625548}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_7daf87a0", "position": {"x": -0.0203592032, "y": 0.818760157, "z": -0.533726156}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_74101882", "position": {"x": 1.128434, "y": 0.9749149, "z": 0.7067195}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_6018293c", "position": {"x": -1.14979517, "y": 0.7518743, "z": 0.9880004}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Fork_9f8771c2", "position": {"x": 1.20097, "y": 0.886848569, "z": 2.261375}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_c40d8d1f", "position": {"x": -1.357, "y": 0.7396263, "z": 3.024}, "rotation": {"x": 0.0, "y": 240.000229, "z": 0.0}}, {"objectName": "Tomato_57296338", "position": {"x": -1.513145, "y": 0.961828768, "z": -0.477608353}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": 1.03472006, "y": 0.8871855, "z": 2.692625}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_e3d6b27e", "position": {"x": 1.450345, "y": 0.8840117, "z": 2.58481264}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6438ff8b", "position": {"x": 1.20097, "y": 0.957268536, "z": 2.477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_92502562", "position": {"x": 0.802, "y": 0.9393, "z": -0.7406}, "rotation": {"x": 0.0, "y": 89.9998245, "z": 0.0}}, {"objectName": "SoapBottle_e0c21b7c", "position": {"x": -1.28665, "y": 0.913428545, "z": -0.392702371}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_6b7178a8", "position": {"x": -0.129671916, "y": 0.11756748, "z": -0.5338761}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": -0.968678534, "y": 0.9270999, "z": -0.430464923}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_7e041f70", "position": {"x": -1.26391578, "y": 1.41969812, "z": 1.197375}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_7daf87a0", "position": {"x": 1.444309, "y": 0.971704, "z": -0.4283477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_572193ee", "position": {"x": -1.47583234, "y": 0.7481479, "z": 1.90228331}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_268c1c72", "position": {"x": -0.813686848, "y": 0.9340927, "z": -0.748511732}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_1ede3fc6", "position": {"x": -1.47583234, "y": 0.783066452, "z": 2.459}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_8429d040", "position": {"x": -1.01254034, "y": 0.74593246, "z": 2.273428}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b6eaf348", "position": {"x": -1.32594109, "y": 1.65203, "z": 0.222244546}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_edec52fd", "position": {"x": -0.91988194, "y": 0.794557154, "z": 2.83014464}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": 1.444309, "y": 0.9115421, "z": 0.7067195}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_4b57442b", "position": {"x": -1.43365359, "y": 0.911664248, "z": -0.907535136}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_a0065d6c", "position": {"x": -1.10519874, "y": 0.749235749, "z": 3.015717}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_1d6d1e51", "position": {"x": 1.36722, "y": 0.8874294, "z": 2.261375}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_4f8d0eee", "position": {"x": 1.18936872, "y": 0.08565579, "z": 1.44079888}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c297f602", "position": {"x": 1.21572745, "y": 0.943699956, "z": -0.633415341}, "rotation": {"x": 0.0, "y": 320.3145, "z": 0.0}}], "object_toggles": [], "random_seed": 3191886622, "scene_num": 17}, "task_id": "trial_T20190906_205908_665995", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A002160837SWJFPIAI7L7_37KGEN7NJ67VV10GMB03DTZ5TB2PPQ", "high_descs": ["Turn left then turn left again to face the counter.", "Pick up the knife from the top left of the counter.", "Look down at the bread.", "Use the knife to slice half the bread into 8 slices.", "Leave the knife on top of the bread.", "Grab one slice of the bread.", "Turn around and walk to the microwave.", "Open the microwave and place the bread slice inside. Heat it for 2 seconds and take it out.", "Close the microwave, turn left then turn right to face the trash can.", "Put the bread slice into the trashcan."], "task_desc": "Place a slice of warm bread into the trashcan.", "votes": [1, 1]}, {"assignment_id": "A10LHOXLUO2WBA_3DOCMVPBTQVVPVCQQHAJ5FPFTP5NNH", "high_descs": ["\nmoves left towards the sink and then right in front of the bread", "picks up the knife close to the bread", "moves backwards", "slice the bread\n", "uses the knife to slice the bread\n", "picks up a slice of the bread", " then left and right and walks towards the microwave opens it up", "turns right twice,put the bread in the microwave for a while and take it out", "turns right moves foward and right and looks down on the trash", "and drops the bread slice in it"], "task_desc": "the robot slices the bread takes a piece to the microwave  heat it put it in the trash", "votes": [1, 1]}, {"assignment_id": "A2BLQ1GVEHJR8T_3X66WABAJZZIBZ2ULUD96HICNSU3G3", "high_descs": ["Turn around to your left, then go to the counter with the single loaf of bread on it.", "Move the knife that is behind the loaf of bread to the counter in front of the bread.", "Pick up the knife.", "Slice the bread with the knife.", "Lay the knife down on the bread loaf.", "Take the slice from the right hand end of the bread.", "Carry the slice of bread to the microwave behind you.", "Heat the slice of bread in the microwave", "Turn around to your right and carry the bread slice to the red wastebasket behind you.", "Throw the cooked bread into the wastebasket."], "task_desc": "Throw a slice of cooked bread away into a red wastebasket.", "votes": [1, 1]}]}}