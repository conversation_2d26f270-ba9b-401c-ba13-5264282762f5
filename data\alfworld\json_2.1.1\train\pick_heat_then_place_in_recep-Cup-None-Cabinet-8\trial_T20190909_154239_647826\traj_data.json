{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000243.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000244.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000245.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000246.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000247.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000248.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000249.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000254.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000255.png", "low_idx": 40}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|0|-4|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-0.25512892, -0.25512892, -6.8313546, -6.8313546, 3.133461476, 3.133461476]], "coordinateReceptacleObjectId": ["SinkBasin", [-0.25512844, -0.25512844, -6.85248328, -6.85248328, 3.0634928, 3.0634928]], "forceVisible": true, "objectId": "Cup|-00.06|+00.78|-01.71"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|2|0|1|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [5.688, 5.688, 0.092, 0.092, 4.6, 4.6]], "forceVisible": true, "objectId": "Microwave|+01.42|+01.15|+00.02"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|2|-2|2|60"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-0.25512892, -0.25512892, -6.8313546, -6.8313546, 3.133461476, 3.133461476]], "coordinateReceptacleObjectId": ["Cabinet", [-0.8108, -0.8108, -5.5664, -5.5664, 1.6076, 1.6076]], "forceVisible": true, "objectId": "Cup|-00.06|+00.78|-01.71", "receptacleObjectId": "Cabinet|-00.20|+00.40|-01.39"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-00.06|+00.78|-01.71"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [153, 130, 180, 172], "mask": [[38863, 7], [39160, 14], [39458, 18], [39757, 20], [40056, 22], [40355, 24], [40655, 25], [40954, 26], [41254, 26], [41554, 26], [41853, 28], [42153, 28], [42453, 28], [42753, 28], [43053, 28], [43353, 28], [43653, 28], [43953, 28], [44253, 28], [44553, 28], [44854, 26], [45154, 26], [45454, 25], [45755, 24], [46055, 23], [46356, 22], [46657, 20], [46957, 19], [47257, 18], [47557, 18], [47857, 18], [48157, 18], [48457, 18], [48756, 19], [49056, 19], [49356, 19], [49656, 19], [49957, 18], [50257, 17], [50558, 16], [50859, 14], [51161, 10], [51463, 6]], "point": [166, 150]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-00.06|+00.78|-01.71", "placeStationary": true, "receptacleObjectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 24, 252, 209], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9622, 226], [9918, 233], [10214, 239], [10510, 243], [10806, 247], [11102, 251], [11400, 253], [11700, 252], [12000, 252], [12300, 252], [12600, 252], [12900, 252], [13200, 251], [13500, 251], [13800, 251], [14100, 251], [14400, 251], [14700, 251], [15000, 250], [15300, 250], [15600, 250], [15900, 250], [16200, 250], [16500, 249], [16800, 249], [17100, 249], [17400, 249], [17700, 249], [18000, 248], [18300, 248], [18600, 248], [18900, 248], [19200, 248], [19500, 248], [19800, 247], [20100, 98], [20199, 148], [20400, 96], [20501, 146], [20700, 92], [20806, 141], [21000, 90], [21107, 140], [21300, 90], [21409, 137], [21600, 89], [21710, 136], [21900, 88], [22011, 135], [22200, 87], [22311, 135], [22500, 87], [22612, 134], [22800, 87], [22912, 134], [23100, 86], [23213, 132], [23400, 86], [23513, 132], [23700, 86], [23813, 132], [24000, 86], [24113, 132], [24300, 86], [24413, 132], [24600, 86], [24713, 131], [24900, 86], [25013, 131], [25200, 87], [25313, 131], [25500, 87], [25613, 131], [25800, 87], [25913, 131], [26100, 88], [26212, 132], [26400, 88], [26512, 131], [26700, 89], [26811, 132], [27000, 89], [27111, 132], [27300, 90], [27411, 132], [27600, 91], [27710, 133], [27900, 91], [28009, 133], [28200, 93], [28307, 135], [28500, 96], [28605, 137], [28800, 242], [29100, 242], [29400, 242], [29700, 241], [30000, 241], [30300, 241], [30600, 241], [30900, 241], [31200, 240], [31500, 240], [31800, 240], [32100, 240], [32400, 240], [32700, 240], [33000, 239], [33300, 239], [33600, 239], [33900, 239], [34200, 239], [34500, 238], [34800, 238], [35100, 238], [35400, 238], [35700, 238], [36000, 238], [36300, 237], [36600, 237], [36900, 237], [37200, 237], [37500, 237], [37800, 236], [38100, 236], [38400, 236], [38700, 236], [39000, 236], [39300, 236], [39600, 55], [39677, 6], [39798, 37], [39900, 54], [39977, 4], [40098, 37], [40200, 54], [40399, 36], [40500, 53], [40800, 52], [41100, 52], [41400, 51], [41700, 50], [42000, 49], [42300, 49], [42600, 48], [42900, 47], [43200, 47], [43500, 46], [43800, 45], [44100, 44], [44400, 44], [44700, 43], [45000, 42], [45300, 41], [45600, 41], [45900, 40], [46200, 39], [46500, 39], [46800, 38], [47100, 37], [47400, 36], [47700, 36], [48000, 35], [48300, 34], [48600, 34], [48900, 33], [49200, 32], [49500, 31], [49800, 31], [50100, 30], [50400, 29], [50700, 29], [51000, 28], [51300, 27], [51600, 26], [51900, 26], [52200, 25], [52500, 24], [52800, 24], [53100, 23], [53400, 22], [53700, 21], [54000, 21], [54300, 20], [54600, 19], [54900, 19], [55200, 18], [55500, 17], [55800, 16], [56100, 16], [56400, 15], [56700, 14], [57000, 14], [57300, 13], [57600, 12], [57900, 11], [58200, 11], [58500, 10], [58800, 9], [59100, 9], [59400, 8], [59700, 7], [60000, 6], [60300, 6], [60600, 5], [60900, 4], [61200, 4], [61500, 3], [61800, 2], [62100, 1], [62400, 1]], "point": [126, 115]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 24, 252, 209], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9622, 226], [9918, 233], [10214, 239], [10510, 243], [10806, 247], [11102, 251], [11400, 253], [11700, 252], [12000, 252], [12300, 252], [12600, 252], [12900, 252], [13200, 251], [13500, 251], [13800, 251], [14100, 251], [14400, 251], [14700, 251], [15000, 250], [15300, 250], [15600, 250], [15900, 250], [16200, 250], [16500, 146], [16667, 82], [16800, 143], [16970, 79], [17100, 142], [17271, 78], [17400, 142], [17571, 78], [17700, 141], [17872, 77], [18000, 141], [18172, 76], [18300, 141], [18472, 76], [18600, 141], [18772, 76], [18900, 141], [19072, 76], [19200, 140], [19373, 75], [19500, 140], [19673, 75], [19800, 140], [19973, 74], [20100, 98], [20199, 41], [20273, 74], [20400, 96], [20501, 39], [20573, 74], [20700, 92], [20806, 34], [20872, 75], [21000, 90], [21107, 33], [21172, 75], [21300, 90], [21409, 31], [21472, 74], [21600, 89], [21710, 31], [21772, 74], [21900, 88], [22011, 30], [22072, 74], [22200, 87], [22311, 30], [22371, 75], [22500, 87], [22612, 30], [22671, 75], [22800, 87], [22912, 30], [22971, 75], [23100, 86], [23213, 29], [23271, 74], [23400, 86], [23513, 30], [23570, 75], [23700, 86], [23813, 30], [23870, 75], [24000, 86], [24113, 31], [24169, 76], [24300, 86], [24413, 31], [24469, 76], [24600, 86], [24713, 32], [24768, 76], [24900, 86], [25013, 32], [25068, 76], [25200, 87], [25313, 32], [25367, 77], [25500, 87], [25613, 33], [25667, 77], [25800, 87], [25913, 33], [25967, 77], [26100, 88], [26212, 34], [26267, 77], [26400, 88], [26512, 34], [26566, 77], [26700, 89], [26811, 35], [26866, 77], [27000, 89], [27111, 35], [27166, 77], [27300, 90], [27411, 35], [27466, 77], [27600, 91], [27710, 36], [27766, 77], [27900, 91], [28009, 37], [28066, 76], [28200, 93], [28307, 39], [28366, 76], [28500, 96], [28605, 41], [28666, 76], [28800, 146], [28967, 75], [29100, 145], [29267, 75], [29400, 145], [29567, 75], [29700, 145], [29867, 74], [30000, 145], [30167, 74], [30300, 145], [30467, 74], [30600, 145], [30767, 74], [30900, 147], [31066, 75], [31200, 149], [31363, 77], [31500, 240], [31800, 240], [32100, 240], [32400, 240], [32700, 240], [33000, 239], [33300, 239], [33600, 239], [33900, 239], [34200, 239], [34500, 238], [34800, 238], [35100, 238], [35400, 238], [35700, 238], [36000, 238], [36300, 237], [36600, 237], [36900, 237], [37200, 237], [37500, 237], [37800, 236], [38100, 236], [38400, 236], [38700, 236], [39000, 236], [39300, 236], [39600, 55], [39677, 6], [39798, 37], [39900, 54], [39977, 4], [40098, 37], [40200, 54], [40399, 36], [40500, 53], [40800, 52], [41100, 52], [41400, 51], [41700, 50], [42000, 49], [42300, 49], [42600, 48], [42900, 47], [43200, 47], [43500, 46], [43800, 45], [44100, 44], [44400, 44], [44700, 43], [45000, 42], [45300, 41], [45600, 41], [45900, 40], [46200, 39], [46500, 39], [46800, 38], [47100, 37], [47400, 36], [47700, 36], [48000, 35], [48300, 34], [48600, 34], [48900, 33], [49200, 32], [49500, 31], [49800, 31], [50100, 30], [50400, 29], [50700, 29], [51000, 28], [51300, 27], [51600, 26], [51900, 26], [52200, 25], [52500, 24], [52800, 24], [53100, 23], [53400, 22], [53700, 21], [54000, 21], [54300, 20], [54600, 19], [54900, 19], [55200, 18], [55500, 17], [55800, 16], [56100, 16], [56400, 15], [56700, 14], [57000, 14], [57300, 13], [57600, 12], [57900, 11], [58200, 11], [58500, 10], [58800, 9], [59100, 9], [59400, 8], [59700, 7], [60000, 6], [60300, 6], [60600, 5], [60900, 4], [61200, 4], [61500, 3], [61800, 2], [62100, 1], [62400, 1]], "point": [126, 115]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [5.688, 5.688, 0.092, 0.092, 4.6, 4.6]], "forceVisible": true, "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [5.688, 5.688, 0.092, 0.092, 4.6, 4.6]], "forceVisible": true, "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-00.06|+00.78|-01.71"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [140, 56, 172, 105], "mask": [[16646, 21], [16943, 27], [17242, 29], [17542, 29], [17841, 31], [18141, 31], [18441, 31], [18741, 31], [19041, 31], [19340, 33], [19640, 33], [19940, 33], [20240, 33], [20540, 33], [20840, 32], [21140, 32], [21440, 32], [21741, 31], [22041, 31], [22341, 30], [22642, 29], [22942, 29], [23242, 29], [23543, 27], [23843, 27], [24144, 25], [24444, 25], [24745, 23], [25045, 23], [25345, 22], [25646, 21], [25946, 21], [26246, 21], [26546, 20], [26846, 20], [27146, 20], [27446, 20], [27746, 20], [28046, 20], [28346, 20], [28646, 20], [28946, 21], [29245, 22], [29545, 22], [29845, 22], [30145, 22], [30445, 22], [30745, 22], [31047, 19], [31349, 14]], "point": [156, 79]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 24, 252, 209], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9622, 226], [9918, 233], [10214, 239], [10510, 243], [10806, 247], [11102, 251], [11400, 253], [11700, 252], [12000, 252], [12300, 252], [12600, 252], [12900, 252], [13200, 251], [13500, 251], [13800, 251], [14100, 251], [14400, 251], [14700, 251], [15000, 250], [15300, 250], [15600, 250], [15900, 250], [16200, 250], [16500, 249], [16800, 249], [17100, 249], [17400, 249], [17700, 249], [18000, 248], [18300, 248], [18600, 248], [18900, 248], [19200, 248], [19500, 248], [19800, 247], [20100, 98], [20199, 148], [20400, 96], [20501, 146], [20700, 92], [20806, 141], [21000, 90], [21107, 140], [21300, 90], [21409, 137], [21600, 89], [21710, 136], [21900, 88], [22011, 135], [22200, 87], [22311, 135], [22500, 87], [22612, 134], [22800, 87], [22912, 134], [23100, 86], [23213, 132], [23400, 86], [23513, 132], [23700, 86], [23813, 132], [24000, 86], [24113, 132], [24300, 86], [24413, 132], [24600, 86], [24713, 131], [24900, 86], [25013, 131], [25200, 87], [25313, 131], [25500, 87], [25613, 131], [25800, 87], [25913, 131], [26100, 88], [26212, 132], [26400, 88], [26512, 131], [26700, 89], [26811, 132], [27000, 89], [27111, 132], [27300, 90], [27411, 132], [27600, 91], [27710, 133], [27900, 91], [28009, 133], [28200, 93], [28307, 135], [28500, 96], [28605, 137], [28800, 242], [29100, 242], [29400, 242], [29700, 241], [30000, 241], [30300, 241], [30600, 241], [30900, 241], [31200, 240], [31500, 240], [31800, 240], [32100, 240], [32400, 240], [32700, 240], [33000, 239], [33300, 239], [33600, 239], [33900, 239], [34200, 239], [34500, 238], [34800, 238], [35100, 238], [35400, 238], [35700, 238], [36000, 238], [36300, 237], [36600, 237], [36900, 237], [37200, 237], [37500, 237], [37800, 236], [38100, 236], [38400, 236], [38700, 236], [39000, 236], [39300, 236], [39600, 55], [39677, 6], [39798, 37], [39900, 54], [39977, 4], [40098, 37], [40200, 54], [40399, 36], [40500, 53], [40800, 52], [41100, 52], [41400, 51], [41700, 50], [42000, 49], [42300, 49], [42600, 48], [42900, 47], [43200, 47], [43500, 46], [43800, 45], [44100, 44], [44400, 44], [44700, 43], [45000, 42], [45300, 41], [45600, 41], [45900, 40], [46200, 39], [46500, 39], [46800, 38], [47100, 37], [47400, 36], [47700, 36], [48000, 35], [48300, 34], [48600, 34], [48900, 33], [49200, 32], [49500, 31], [49800, 31], [50100, 30], [50400, 29], [50700, 29], [51000, 28], [51300, 27], [51600, 26], [51900, 26], [52200, 25], [52500, 24], [52800, 24], [53100, 23], [53400, 22], [53700, 21], [54000, 21], [54300, 20], [54600, 19], [54900, 19], [55200, 18], [55500, 17], [55800, 16], [56100, 16], [56400, 15], [56700, 14], [57000, 14], [57300, 13], [57600, 12], [57900, 11], [58200, 11], [58500, 10], [58800, 9], [59100, 9], [59400, 8], [59700, 7], [60000, 6], [60300, 6], [60600, 5], [60900, 4], [61200, 4], [61500, 3], [61800, 2], [62100, 1], [62400, 1]], "point": [126, 115]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-00.20|+00.40|-01.39"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [181, 79, 299, 147], "mask": [[23594, 106], [23894, 106], [24194, 106], [24494, 106], [24793, 107], [25093, 106], [25393, 105], [25693, 105], [25993, 104], [26292, 104], [26592, 104], [26892, 103], [27192, 102], [27492, 102], [27791, 102], [28091, 101], [28391, 101], [28691, 100], [28991, 99], [29290, 100], [29590, 99], [29890, 98], [30190, 97], [30490, 97], [30789, 97], [31089, 96], [31389, 96], [31689, 95], [31989, 94], [32288, 95], [32588, 94], [32888, 93], [33188, 93], [33488, 92], [33787, 92], [34087, 92], [34387, 91], [34687, 90], [34987, 89], [35286, 90], [35586, 89], [35886, 88], [36186, 88], [36486, 87], [36785, 87], [37085, 87], [37385, 86], [37685, 85], [37985, 85], [38284, 85], [38584, 84], [38884, 83], [39184, 83], [39484, 82], [39783, 82], [40083, 82], [40383, 81], [40683, 80], [40983, 80], [41282, 80], [41582, 79], [41882, 79], [42182, 78], [42482, 77], [42781, 78], [43081, 77], [43381, 76], [43681, 75], [43981, 75]], "point": [240, 112]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-00.06|+00.78|-01.71", "placeStationary": true, "receptacleObjectId": "Cabinet|-00.20|+00.40|-01.39"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [177, 79, 298, 196], "mask": [[23590, 109], [23890, 108], [24189, 109], [24489, 108], [24789, 107], [25089, 107], [25388, 107], [25688, 106], [25988, 106], [26288, 105], [26587, 105], [26887, 105], [27187, 104], [27487, 103], [27787, 103], [28086, 103], [28386, 102], [28686, 102], [28986, 101], [29286, 100], [29586, 100], [29885, 100], [30185, 99], [30485, 99], [30785, 98], [31085, 97], [31385, 97], [31684, 97], [31984, 96], [32284, 96], [32584, 95], [32884, 94], [33183, 95], [33483, 94], [33783, 93], [34083, 93], [34383, 92], [34683, 91], [34982, 92], [35282, 91], [35582, 90], [35882, 89], [36182, 89], [36482, 88], [36781, 88], [37081, 88], [37381, 87], [37681, 86], [37981, 86], [38280, 86], [38580, 85], [38880, 85], [39180, 84], [39480, 83], [39780, 83], [40079, 83], [40379, 82], [40679, 82], [40979, 81], [41279, 80], [41579, 80], [41878, 80], [42178, 79], [42478, 79], [42778, 78], [43078, 77], [43377, 25], [43677, 25], [43977, 25], [44277, 26], [44577, 26], [44877, 26], [45177, 26], [45478, 25], [45778, 25], [46078, 25], [46378, 25], [46678, 25], [46978, 26], [47278, 26], [47578, 26], [47878, 26], [48178, 26], [48478, 26], [48779, 25], [49081, 23], [49382, 23], [49683, 22], [49984, 21], [50285, 20], [50586, 19], [50887, 18], [51188, 17], [51488, 16], [51789, 15], [52089, 14], [52390, 13], [52690, 13], [52991, 11], [53291, 11], [53591, 10], [53891, 10], [54191, 10], [54491, 9], [54792, 8], [55092, 7], [55392, 7], [55692, 6], [55992, 6], [56293, 5], [56593, 4], [56893, 4], [57193, 3], [57493, 3], [57793, 2], [58093, 2], [58393, 2], [58693, 1]], "point": [237, 136]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-00.20|+00.40|-01.39"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [177, 79, 298, 215], "mask": [[23590, 109], [23890, 108], [24189, 109], [24489, 108], [24789, 107], [25089, 107], [25388, 107], [25688, 106], [25988, 106], [26288, 105], [26587, 105], [26887, 105], [27187, 104], [27487, 103], [27787, 103], [28086, 103], [28386, 102], [28686, 102], [28986, 101], [29286, 100], [29586, 100], [29885, 100], [30185, 99], [30485, 99], [30785, 98], [31085, 97], [31385, 97], [31684, 97], [31984, 96], [32284, 96], [32584, 95], [32884, 94], [33183, 95], [33483, 94], [33783, 26], [33814, 62], [34083, 24], [34116, 60], [34383, 23], [34418, 57], [34683, 22], [34718, 56], [34982, 22], [35019, 55], [35282, 21], [35319, 54], [35582, 21], [35620, 52], [35882, 21], [35920, 51], [36182, 21], [36220, 51], [36482, 20], [36520, 50], [36781, 21], [36820, 49], [37081, 22], [37119, 50], [37381, 22], [37419, 49], [37681, 22], [37718, 49], [37981, 23], [38018, 49], [38280, 23], [38317, 49], [38580, 23], [38616, 49], [38880, 22], [38914, 51], [39180, 22], [39214, 50], [39480, 22], [39513, 50], [39780, 22], [39813, 50], [40079, 24], [40113, 49], [40379, 25], [40412, 49], [40679, 26], [40711, 50], [40979, 81], [41279, 80], [41579, 80], [41878, 80], [42178, 79], [42478, 79], [42778, 78], [43078, 77], [43377, 25], [43677, 25], [43977, 25], [44277, 26], [44577, 26], [44877, 26], [45177, 26], [45478, 25], [45778, 25], [46078, 25], [46378, 25], [46678, 25], [46978, 26], [47278, 26], [47578, 26], [47878, 26], [48178, 26], [48478, 26], [48778, 26], [49078, 26], [49378, 27], [49678, 27], [49978, 27], [50278, 27], [50579, 26], [50879, 26], [51179, 26], [51479, 25], [51779, 25], [52079, 24], [52379, 24], [52679, 24], [52979, 23], [53279, 23], [53579, 22], [53879, 22], [54179, 22], [54479, 21], [54779, 21], [55079, 20], [55380, 19], [55680, 18], [55980, 18], [56280, 18], [56580, 17], [56880, 17], [57180, 16], [57480, 16], [57780, 15], [58080, 15], [58380, 15], [58680, 14], [58980, 14], [59280, 13], [59580, 13], [59880, 12], [60181, 11], [60481, 11], [60781, 10], [61081, 10], [61381, 9], [61681, 9], [61981, 9], [62281, 8], [62581, 8], [62882, 6], [63182, 6], [63482, 5], [63782, 5], [64082, 5], [64382, 4]], "point": [237, 143]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan8", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -1.25, "y": 0.9009992, "z": 0.5}, "object_poses": [{"objectName": "SprayBottle_abf5c390", "position": {"x": -2.32376337, "y": 0.909904063, "z": 0.118500233}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pen_73e168e3", "position": {"x": -0.6279957, "y": 0.913952768, "z": -1.44882691}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_238538e2", "position": {"x": 0.970767558, "y": 1.69993734, "z": -1.839}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_e75d4607", "position": {"x": -1.431, "y": 0.949799955, "z": -1.81}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_7cca838f", "position": {"x": -1.8091464, "y": 0.9105421, "z": -1.62075019}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_7aaf3771", "position": {"x": -1.835049, "y": 0.911048532, "z": 0.3772499}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_7aaf3771", "position": {"x": 0.949094832, "y": 0.773711, "z": -1.013817}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_6f7c42a7", "position": {"x": 0.251858473, "y": 0.9206794, "z": -1.60713387}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_6f7c42a7", "position": {"x": -0.316887051, "y": 0.774390936, "z": -1.56547654}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_1d30af71", "position": {"x": -1.9629277, "y": 0.925499856, "z": -1.44825029}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_e08d0eeb", "position": {"x": 1.37599969, "y": 1.39679658, "z": 1.97600007}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Cup_bfcb0336", "position": {"x": -0.6855, "y": 0.112243295, "z": -1.53563261}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_c69c5340", "position": {"x": 1.494119, "y": 1.16210353, "z": -1.00233626}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_441a8bda", "position": {"x": 1.459939, "y": 0.254656553, "z": 1.02203965}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_441a8bda", "position": {"x": 1.323865, "y": 1.76034415, "z": 1.90114331}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Bowl_a0ece05a", "position": {"x": -2.20158482, "y": 0.911050856, "z": 0.118500233}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_a2768593", "position": {"x": -0.603324533, "y": 1.65057492, "z": -1.91353726}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_a2768593", "position": {"x": 1.37525809, "y": 1.1572001, "z": -1.00233626}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_9b638b9f", "position": {"x": 1.093845, "y": 0.989441156, "z": -0.309145331}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_49b86ad0", "position": {"x": 0.217776522, "y": 0.9145501, "z": -1.7080245}, "rotation": {"x": 0.0, "y": 6.83018925e-06, "z": 0.0}}, {"objectName": "Apple_b66a32b5", "position": {"x": 1.375999, "y": 1.40215027, "z": 1.726}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Apple_b66a32b5", "position": {"x": -1.88603711, "y": 0.94518286, "z": -1.87949979}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_441a8bda", "position": {"x": 1.34153056, "y": 0.254656553, "z": 1.08}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_b308514b", "position": {"x": -2.445942, "y": 0.9112167, "z": 0.0322503448}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_af290bda", "position": {"x": 1.45449877, "y": 1.19190037, "z": -0.659025431}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_9b9c5535", "position": {"x": 0.5284804, "y": 0.117729604, "z": -1.63311362}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_7aaf3771", "position": {"x": 1.41487837, "y": 1.16274869, "z": -1.34564734}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_238538e2", "position": {"x": 1.34259534, "y": 1.7004267, "z": -1.34299994}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_1cf505ed", "position": {"x": 1.41487837, "y": 1.20399642, "z": 0.3709073}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_0f08525d", "position": {"x": 1.42646933, "y": 1.43663275, "z": 2.226}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Kettle_e75d4607", "position": {"x": -1.431, "y": 0.949799955, "z": -1.5257}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_73e168e3", "position": {"x": 0.8524788, "y": 0.913952768, "z": -1.53404093}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_a2768593", "position": {"x": -0.7787359, "y": 0.906299949, "z": -1.96010935}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_bfcb0336", "position": {"x": -0.06378223, "y": 0.783365369, "z": -1.70783865}, "rotation": {"x": 1.40334191e-14, "y": 270.0, "z": 0.0}}, {"objectName": "Egg_e08d0eeb", "position": {"x": 0.251858443, "y": 0.9488791, "z": -1.80891538}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_1d30af71", "position": {"x": -2.07940626, "y": 0.925499856, "z": 0.463499784}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_a0ece05a", "position": {"x": -0.6279957, "y": 0.9118508, "z": -1.78968191}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_abf5c390", "position": {"x": -0.406642735, "y": 0.116157413, "z": -1.48949575}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_c0048524", "position": {"x": -1.0811, "y": 0.949799955, "z": -1.81}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_c69c5340", "position": {"x": -0.4018854, "y": 0.9112034, "z": -1.70446813}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pot_316d55f0", "position": {"x": 0.9673953, "y": 0.9, "z": 0.249277115}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_7cca838f", "position": {"x": -2.32376337, "y": 0.9105421, "z": 0.291}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1de14ecd", "position": {"x": -0.6855, "y": 0.106764436, "z": -1.4384979}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_b7024f32", "position": {"x": 1.03643036, "y": 0.7745388, "z": -0.6656355}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_9b638b9f", "position": {"x": 1.45449877, "y": 1.24034131, "z": -1.68895817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_b66a32b5", "position": {"x": 1.47354031, "y": 1.30013561, "z": 0.188763976}, "rotation": {"x": 0.0, "y": 270.000153, "z": 0.0}}, {"objectName": "Spoon_6f7c42a7", "position": {"x": 0.8524788, "y": 0.912429333, "z": -1.53404093}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_49b86ad0", "position": {"x": 0.149612635, "y": 0.9145499, "z": -1.80891526}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}], "object_toggles": [], "random_seed": 455252523, "scene_num": 8}, "task_id": "trial_T20190909_154239_647826", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1DMXEJGJY02E1_3137ONMDKJMLLXIRAEU5P8M9FKREGJ", "high_descs": ["Turn around and go to the sink.", "Pick up the glass from the sink.", "Turn around and go to the microwave.", "Heat the glass and then pick up the glass again.", "Turn right and face the sink.", "Put the glass in the cabinet under the sink."], "task_desc": "Put a heated glass in the cabinet under the sink.", "votes": [1, 1, 1]}, {"assignment_id": "A3R19ZA45J8915_3HOSI13XH25DH7U0CTIDD51R2GJDDO", "high_descs": ["Turn around and walk over to the kitchen sink. ", "Pick up the cup from the right sink basin. ", "Turn around and walk over to the microwave that is now on your right. ", "Place the cup on the plate in the microwave and heat it before pulling the cup back out. ", "Turn to the right and walk over to the kitchen sink. ", "Open the cabinet that is directly below the left hand side of the sink and place the cup inside. "], "task_desc": "Place a heated cup into a cabinet. ", "votes": [1, 1, 1]}, {"assignment_id": "A36DK84J5YJ942_3LQ8PUHQFO9B1YFOQ32EVNW2MDFHIY", "high_descs": ["Move to the sink to the right of the fridge", "pick up a cup from the sink", "move to the microwave to the left of the sink", "heat the cup in the microwave", "move to the cabinet to the left of the sink", "put the cup in the cabinet"], "task_desc": "Put a heated cup in the cabinet under the sink.", "votes": [0, 1, 1]}]}}