{"pddl_domain": ";; Specification in PDDL of the Alfred domain\n;; Intended to be used with Fast Downward which supports PDDL 2.2 level 1 plus the :action-costs requirement from PDDL 3.1.\n\n(define (domain alfred)\n (:requirements\n    :adl\n    :action-costs\n    :typing\n )\n (:types\n  agent\n  location\n  receptacle\n  object\n  rtype\n  otype\n  )\n\n\n (:predicates\n    (atLocation ?a - agent ?l - location)                     ; true if the agent is at the location\n    (receptacleAtLocation ?r - receptacle ?l - location)      ; true if the receptacle is at the location (constant)\n    (objectAtLocation ?o - object ?l - location)              ; true if the object is at the location\n    (openable ?r - receptacle)                                ; true if a receptacle is openable\n    (opened ?r - receptacle)                                  ; true if a receptacle is opened\n    (inReceptacle ?o - object ?r - receptacle)                ; object ?o is in receptacle ?r\n    (isReceptacleObject ?o - object)                          ; true if the object can have things put inside it\n    (inReceptacleObject ?innerObject - object ?outerObject - object)                ; object ?innerObject is inside object ?outerObject\n    (isReceptacleObjectFull ?o - object)                      ; true if the receptacle object contains something\n    (wasInReceptacle ?o - object ?r - receptacle)             ; object ?o was or is in receptacle ?r now or some time in the past\n    (checked ?r - receptacle)                                 ; whether the receptacle has been looked inside/visited\n    (examined ?l - location)                                  ; TODO\n    (receptacleType ?r - receptacle ?t - rtype)               ; the type of receptacle (Cabinet vs Cabinet|01|2...)\n    (canContain ?rt - rtype ?ot - otype)                      ; true if receptacle can hold object\n    (objectType ?o - object ?t - otype)                       ; the type of object (Apple vs Apple|01|2...)\n    (holds ?a - agent ?o - object)                            ; object ?o is held by agent ?a\n    (holdsAny ?a - agent)                                     ; agent ?a holds an object\n    (holdsAnyReceptacleObject ?a - agent)                        ; agent ?a holds a receptacle object\n    (full ?r - receptacle)                                    ; true if the receptacle has no remaining space\n    (isClean ?o - object)                                     ; true if the object has been clean in sink\n    (cleanable ?o - object)                                   ; true if the object can be placed in a sink\n    (isHot ?o - object)                                       ; true if the object has been heated up\n    (heatable ?o - object)                                    ; true if the object can be heated up in a microwave\n    (isCool ?o - object)                                      ; true if the object has been cooled\n    (coolable ?o - object)                                    ; true if the object can be cooled in the fridge\n    (pickupable ?o - object)                                   ; true if the object can be picked up\n    (moveable ?o - object)                                      ; true if the object can be moved\n    (toggleable ?o - object)                                  ; true if the object can be turned on/off\n    (isOn ?o - object)                                        ; true if the object is on\n    (isToggled ?o - object)                                   ; true if the object has been toggled\n    (sliceable ?o - object)                                   ; true if the object can be sliced\n    (isSliced ?o - object)                                    ; true if the object is sliced\n )\n\n  (:functions\n    (distance ?from ?to)\n    (total-cost) - number\n   )\n\n;; All actions are specified such that the final arguments are the ones used\n;; for performing actions in Unity.\n\n\n(:action look\n    :parameters (?a - agent ?l - location)\n    :precondition\n        (and\n            (atLocation ?a ?l)\n        )\n    :effect\n        (and\n            (checked ?l)\n        )\n)\n\n(:action inventory\n    :parameters (?a - agent)\n    :precondition\n        ()\n    :effect\n        (and\n            (checked ?a)\n        )\n)\n\n(:action examineReceptacle\n    :parameters (?a - agent ?r - receptacle)\n    :precondition\n        (and\n            (exists (?l - location)\n                (and\n                    (atLocation ?a ?l)\n                    (receptacleAtLocation ?r ?l)\n                )\n            )\n        )\n    :effect\n        (and\n            (checked ?r)\n        )\n)\n\n(:action examineObject\n    :parameters (?a - agent ?o - object)\n    :precondition\n        (or\n            ;(exists (?l - location)\n            ;    (and\n            ;        (atLocation ?a ?l)\n            ;        (objectAtLocation ?o ?l)\n            ;    )\n            ;)\n            (exists (?l - location, ?r - receptacle)\n                (and\n                    (atLocation ?a ?l)\n                    (receptacleAtLocation ?r ?l)\n                    ; (objectAtLocation ?o ?l)\n                    (inReceptacle ?o ?r)\n                    (or (not (openable ?r)) (opened ?r))  ; receptacle is opened if it is openable.\n              )\n            )\n            (holds ?a ?o)\n        )\n    :effect\n        (and\n            (checked ?o)\n        )\n)\n\n;; agent goes to receptacle\n (:action GotoLocation\n    :parameters (?a - agent ?lStart - location ?lEnd - location ?r - receptacle)\n    :precondition (and\n                    (atLocation ?a ?lStart)\n                    (receptacleAtLocation ?r ?lEnd)\n                    ;(exists (?r - receptacle) (receptacleAtLocation ?r ?lEnd))\n                  )\n    :effect (and\n                (not (atLocation ?a ?lStart))\n                (atLocation ?a ?lEnd)\n                ; (forall (?r - receptacle)\n                ;     (when (and (receptacleAtLocation ?r ?lEnd)\n                ;                (or (not (openable ?r)) (opened ?r)))\n                ;         (checked ?r)\n                ;     )\n                ; )\n                ; (increase (total-cost) (distance ?lStart ?lEnd))\n                (increase (total-cost) 1)\n            )\n )\n\n;; agent opens receptacle\n (:action OpenObject\n    :parameters (?a - agent ?l - location ?r - receptacle)\n    :precondition (and\n            (openable ?r)\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (not (opened ?r))\n            )\n    :effect (and\n                (opened ?r)\n                (checked ?r)\n                (increase (total-cost) 1)\n            )\n )\n;; agent closes receptacle\n (:action CloseObject\n    :parameters (?a - agent ?l - location ?r - receptacle)\n    :precondition (and\n            (openable ?r)\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (opened ?r)\n            )\n    :effect (and\n                (not (opened ?r))\n                (increase (total-cost) 1)\n            )\n\n )\n\n ;; agent picks up object from a receptacle\n (:action PickupObject\n    :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n    :precondition\n        (and\n            (pickupable ?o)\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            ; (objectAtLocation ?o ?l)\n            (inReceptacle ?o ?r)\n            (not (holdsAny ?a))  ; agent's hands are empty.\n            ;(not (holdsAnyReceptacleObject ?a))\n            (or (not (openable ?r)) (opened ?r))  ; receptacle is opened if it is openable.\n            ;(not (isReceptacleObject ?o))\n        )\n    :effect\n        (and\n            (not (inReceptacle ?o ?r))\n            (holds ?a ?o)\n            (holdsAny ?a)\n            (not (objectAtLocation ?o ?l))\n            ;(not (full ?r))\n            (increase (total-cost) 1)\n        )\n )\n\n\n; ;; agent picks up object from a receptacle\n; (:action PickupObjectFromReceptacleObject\n;    :parameters (?a - agent ?l - location ?o - object ?outerR - object ?r - receptacle)\n;    :precondition\n;        (and\n;            (atLocation ?a ?l)\n;            (receptacleAtLocation ?r ?l)\n;            (inReceptacle ?o ?r)\n;            (pickupable ?o)\n;            (not (holdsAny ?a))  ; agent's hands are empty.\n;            (not (holdsAnyReceptacleObject ?a))\n;            (or (not (openable ?r)) (opened ?r))  ; receptacle is opened if it is openable.\n;            (not (isReceptacleObject ?o))\n;            (isReceptacleObject ?outerR)\n;            (inReceptacleObject ?o ?outerR)\n;        )\n;    :effect\n;        (and\n;            (not (inReceptacle ?o ?r))\n;            (holds ?a ?o)\n;            (holdsAny ?a)\n;            (not (objectAtLocation ?o ?l))\n;            (not (full ?r))\n;            (increase (total-cost) 1)\n;\n;            (not (inReceptacleObject ?o ?outerR))\n;            (not (isReceptacleObjectFull ?outerR))\n;        )\n; )\n;\n; ;; agent picks up object from a receptacle\n; (:action PickupEmptyReceptacleObject\n;    :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n;    :precondition\n;        (and\n;            (atLocation ?a ?l)\n;            (receptacleAtLocation ?r ?l)\n;            ; (objectAtLocation ?o ?l)\n;            (inReceptacle ?o ?r)\n;            (pickupable ?o)\n;            (not (holdsAny ?a))  ; agent's hands are empty.\n;            (not (holdsAnyReceptacleObject ?a))\n;            (or (not (openable ?r)) (opened ?r))  ; receptacle is opened if it is openable.\n;            (isReceptacleObject ?o)\n;            (not (isReceptacleObjectFull ?o))\n;        )\n;    :effect\n;        (and\n;            (not (inReceptacle ?o ?r))\n;            (holds ?a ?o)\n;            (holdsAny ?a)\n;            (not (objectAtLocation ?o ?l))\n;            (not (full ?r))\n;            (increase (total-cost) 1)\n;            (holdsAnyReceptacleObject ?a)\n;        )\n; )\n;\n; ;; agent picks up object from a receptacle\n; (:action PickupFullReceptacleObject\n;    :parameters (?a - agent ?l - location ?o - object ?outerR - object ?r - receptacle)\n;    :precondition\n;        (and\n;            (atLocation ?a ?l)\n;            (receptacleAtLocation ?r ?l)\n;            (inReceptacle ?outerR ?r)\n;            (pickupable ?outerR)\n;            (not (holdsAny ?a))  ; agent's hands are empty.\n;            (not (holdsAnyReceptacleObject ?a))\n;            (or (not (openable ?r)) (opened ?r))  ; receptacle is opened if it is openable.\n;            (not (isReceptacleObject ?o))\n;            (isReceptacleObject ?outerR)\n;            (inReceptacleObject ?o ?outerR)\n;        )\n;    :effect\n;        (and\n;            (not (inReceptacle ?o ?r))\n;            (not (inReceptacle ?outerR ?r))\n;            (holds ?a ?outerR)\n;            (holdsAny ?a)\n;            (not (objectAtLocation ?o ?l))\n;            (not (objectAtLocation ?outerR ?l))\n;            (not (full ?r))\n;            (increase (total-cost) 1)\n;            (holdsAnyReceptacleObject ?a)\n;        )\n; )\n\n\n;; agent puts down an object\n (:action PutObject\n    :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n    :precondition (and\n            (holds ?a ?o)\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (or (not (openable ?r)) (opened ?r))    ; receptacle is opened if it is openable\n            ;(not (full ?r))\n            (objectType ?o ?ot)\n            (receptacleType ?r ?rt)\n            (canContain ?rt ?ot)\n            ;(not (holdsAnyReceptacleObject ?a))\n            )\n    :effect (and\n                (inReceptacle ?o ?r)\n                (objectAtLocation ?o ?l)\n                ;(full ?r)\n                (not (holds ?a ?o))\n                (not (holdsAny ?a))\n                (increase (total-cost) 1)\n            )\n )\n\n;;; agent puts down an object\n; (:action PutObjectInReceptacleObject\n;    :parameters (?a - agent ?l - location ?ot - otype ?o - object ?outerO - object ?outerR - receptacle)\n;    :precondition (and\n;            (atLocation ?a ?l)\n;            (objectAtLocation ?outerO ?l)\n;            (isReceptacleObject ?outerO)\n;            (not (isReceptacleObject ?o))\n;            (objectType ?o ?ot)\n;            (holds ?a ?o)\n;            (not (holdsAnyReceptacleObject ?a))\n;            (inReceptacle ?outerO ?outerR)\n;            (not (isReceptacleObjectFull ?outerO))\n;            )\n;    :effect (and\n;                (inReceptacleObject ?o ?outerO)\n;                (inReceptacle ?o ?outerR)\n;                (not (holds ?a ?o))\n;                (not (holdsAny ?a))\n;                (objectAtLocation ?o ?l)\n;                (isReceptacleObjectFull ?outerO)\n;                (increase (total-cost) 1)\n;            )\n; )\n;\n;;; agent puts down an object\n; (:action PutEmptyReceptacleObjectinReceptacle\n;    :parameters (?a - agent ?l - location ?ot - otype ?o - object ?r - receptacle)\n;    :precondition (and\n;            (atLocation ?a ?l)\n;            (receptacleAtLocation ?r ?l)\n;            (or (not (openable ?r)) (opened ?r))    ; receptacle is opened if it is openable\n;            (not (full ?r))\n;            (objectType ?o ?ot)\n;            (holds ?a ?o)\n;            (holdsAnyReceptacleObject ?a)\n;            (isReceptacleObject ?o)\n;            (not (isReceptacleObjectFull ?o))\n;            )\n;    :effect (and\n;                (inReceptacle ?o ?r)\n;                (objectAtLocation ?o ?l)\n;                (full ?r)\n;                (not (holds ?a ?o))\n;                (not (holdsAny ?a))\n;                (not (holdsAnyReceptacleObject ?a))\n;                (increase (total-cost) 1)\n;            )\n; )\n;\n;;; agent puts down a receptacle object in a receptacle\n; (:action PutFullReceptacleObjectInReceptacle\n;    :parameters (?a - agent ?l - location ?ot - otype ?innerO - object ?outerO - object ?r - receptacle) ; ?rt - rtype)\n;    :precondition (and\n;            (atLocation ?a ?l)\n;            (receptacleAtLocation ?r ?l)\n;            (objectType ?outerO ?ot)\n;            (holds ?a ?outerO)\n;            (holdsAnyReceptacleObject ?a)\n;            (isReceptacleObject ?outerO)\n;            (isReceptacleObjectFull ?outerO)\n;            (inReceptacleObject ?innerO ?outerO)\n;            )\n;    :effect (and\n;                (not (holdsAny ?a))\n;                (not (holdsAnyReceptacleObject ?a))\n;                (objectAtLocation ?outerO ?l)\n;                (objectAtLocation ?innerO ?l)\n;                (inReceptacle ?outerO ?r)\n;                (inReceptacle ?innerO ?r)\n;                (not (holds ?a ?outerO))\n;                (increase (total-cost) 1)\n;            )\n; )\n\n;; agent cleans some object\n (:action CleanObject\n    :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n    :precondition (and\n            (cleanable ?o)\n            (or\n                (receptacleType ?r SinkType)\n                (receptacleType ?r SinkBasinType)\n            )\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (holds ?a ?o)\n            )\n    :effect (and\n                (increase (total-cost) 5)\n                (isClean ?o)\n            )\n )\n\n\n;; agent heats-up some object\n (:action HeatObject\n    :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n    :precondition (and\n            (heatable ?o)\n            (or\n                (receptacleType ?r MicrowaveType)\n            )\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (holds ?a ?o)\n            )\n    :effect (and\n                (increase (total-cost) 5)\n                (isHot ?o)\n                (not (isCool ?o))\n            )\n )\n\n;; agent cools some object\n (:action CoolObject\n    :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n    :precondition (and\n            (coolable ?o)\n            (or\n                (receptacleType ?r FridgeType)\n            )\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (holds ?a ?o)\n            )\n    :effect (and\n                (increase (total-cost) 5)\n                (isCool ?o)\n                (not (isHot ?o))\n            )\n )\n\n\n;; agent toggle object\n (:action ToggleObject\n    :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n    :precondition (and\n            (toggleable ?o)\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (inReceptacle ?o ?r)\n            )\n    :effect (and\n                (increase (total-cost) 5)\n                (when (isOn ?o)\n                    (not (isOn ?o)))\n                (when (not (isOn ?o))\n                    (isOn ?o))\n                (isToggled ?o)\n            )\n )\n\n\n;; agent slices some object with a knife\n (:action SliceObject\n    :parameters (?a - agent ?l - location ?co - object ?ko - object)\n    :precondition\n            (and\n                (sliceable ?co)\n                (or\n                    (objectType ?ko KnifeType)\n                    (objectType ?ko ButterKnifeType)\n                )\n                (atLocation ?a ?l)\n                (objectAtLocation ?co ?l)\n                (holds ?a ?ko)\n            )\n    :effect (and\n                (increase (total-cost) 5)\n                (isSliced ?co)\n            )\n )\n\n\n(:action help\n    :parameters (?a - agent)\n    :precondition\n        ()\n    :effect\n        (and\n            (checked ?a)\n        )\n)\n)\n", "grammar": "grammar :: \"\"\"\n    {\n        \"intro\": [\n            {\n                \"rhs\": \"-= Welcome to TextWorld, ALFRED! =-\\n\\n#look.feedback#\\n\\n#task#\"\n            }\n        ],\n\n        \"notImplemented\": [\n            {\n                \"rhs\": \"TODO\"\n            }\n        ],\n\n        \"task\": [\n            {\n                \"rhs\": \"Your task is to: heat some cup and put it in cabinet.\"\n            }\n        ],\n\n        \"GotoLocation.feedback\": [\n            {\n                \"rhs\": \"You arrive at {r.name}. #examineReceptacle.feedback#\"\n            }\n        ],\n\n        \"OpenObject.feedback\": [\n            {\n                \"rhs\": \"You open the {r.name}. #examineReceptacle.feedback#\"\n            }\n        ],\n\n        \"CloseObject.feedback\": [\n            {\n                \"rhs\": \"You close the {r.name}.\"\n            }\n        ],\n\n        \"PickupObject.feedback\": [\n            {\n                \"rhs\": \"You pick up the {o.name} from the {r.name}.\"\n            }\n        ],\n\n        \"PickupObjectFromReceptacleObject.feedback\": [\n            {\n                \"rhs\": \"PickupObjectFromReceptacleObject: You pick up the {o.name}.\"\n            }\n        ],\n\n        \"PickupEmptyReceptacleObject.feedback\": [\n            {\n                \"rhs\": \"PickupEmptyReceptacleObject: You pick up the {o.name}.\"\n            }\n        ],\n\n        \"PickupFullReceptacleObject.feedback\": [\n            {\n                \"rhs\": \"PickupFullReceptacleObject: You pick up the {outerr.name}.\"\n            }\n        ],\n\n        \"PutObject.feedback\": [\n            {\n                \"rhs\": \"You move the {o.name} to the {r.name}.\"\n            }\n        ],\n\n        \"PutObjectInReceptacleObject.feedback\": [\n            {\n                \"rhs\": \"PutObjectInReceptacleObject: You put the {o.name} in the {outero.name}.\"\n            }\n        ],\n\n        \"PutEmptyReceptacleObjectinReceptacle.feedback\": [\n            {\n                \"rhs\": \"PutEmptyReceptacleObjectinReceptacle: You put the {o.name} in the {r.name}.\"\n            }\n        ],\n\n        \"PutFullReceptacleObjectInReceptacle.feedback\": [\n            {\n                \"rhs\": \"PutFullReceptacleObjectInReceptacle: You put the {outero.name} in the {r.name}.\"\n            }\n        ],\n\n        \"inventory.feedback\": [\n            {\n                \"condition\": \"holdsany(a:agent)\",\n                \"rhs\": \"You are carrying: [{o.indefinite + ' ' + o.name | holds(a:agent, o:object)}].\"\n            },\n            {\n                \"rhs\": \"You are not carrying anything.\"\n            }\n        ],\n\n        \"examineReceptacle.feedback\": [\n            {\n                \"condition\": \"openable(r:receptacle) & opened(r:receptacle)\",\n                \"rhs\": \"The {r.name} is open. In it, you see [{o.indefinite + ' ' + o.name | inreceptacle(o:object, r:receptacle)}].\"\n            },\n            {\n                \"condition\": \"openable(r:receptacle)\",\n                \"rhs\": \"The {r.name} is closed.\"\n            },\n            {\n                \"rhs\": \"On the {r.name}, you see [{o.indefinite + ' ' + o.name | inreceptacle(o:object, r:receptacle)}].\"\n            }\n        ],\n\n        \"examineObject.feedback\": [\n            {\n                \"condition\": \"isreceptacleobject(o:object)\",\n                \"rhs\": \"This is a normal {o.name}. In it, you see [{o2.indefinite + ' ' + o2.name | inreceptacleobject(o2:object, o:object)}].\"\n            },\n            {\n                \"condition\": \"isclean(o:object) & ishot(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a hot and clean sliced {o.name}.\"\n            },\n            {\n                \"condition\": \"isclean(o:object) & iscool(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a cool and clean sliced {o.name}.\"\n            },\n            {\n                \"condition\": \"isclean(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a clean sliced {o.name}.\"\n            },\n            {\n                \"condition\": \"ishot(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a hot sliced {o.name}.\"\n            },\n            {\n                \"condition\": \"iscool(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a cool sliced {o.name}.\"\n            },\n            {\n                \"condition\": \"isclean(o:object) & ishot(o:object)\",\n                \"rhs\": \"This is a hot and clean {o.name}.\"\n            },\n            {\n                \"condition\": \"isclean(o:object) & iscool(o:object)\",\n                \"rhs\": \"This is a cool and clean {o.name}.\"\n            },\n            {\n                \"condition\": \"ishot(o:object)\",\n                \"rhs\": \"This is a hot {o.name}.\"\n            },\n            {\n                \"condition\": \"isclean(o:object)\",\n                \"rhs\": \"This is a clean {o.name}.\"\n            },\n            {\n                \"condition\": \"iscool(o:object)\",\n                \"rhs\": \"This is a cold {o.name}.\"\n            },\n            {\n                \"condition\": \"toggleable(o:object) & istoggled(o:object)\",\n                \"rhs\": \"This {o.name} is on.\"\n            },\n            {\n                \"condition\": \"toggleable(o:object) & not_istoggled(o:object)\",\n                \"rhs\": \"This {o.name} is off.\"\n            },\n            {\n                \"condition\": \"sliceable(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a sliced {o.name}.\"\n            },\n            {\n                \"rhs\": \"There's nothing special about {o.name}.\"\n            }\n        ]\n    }\n\"\"\";\n\naction GotoLocation {\n    template :: \"go to [{r.name | receptacleatlocation(r:receptacle, lend:location)}]\";\n    feedback :: \"#GotoLocation.feedback#\";\n}\n\naction OpenObject {\n    template :: \"open {r}\";\n    feedback :: \"#OpenObject.feedback#\";\n}\n\naction CloseObject {\n    template :: \"close {r}\";\n    feedback :: \"#CloseObject.feedback#\";\n}\n\naction PickupObject {\n    template :: \"take {o} from {r}\";\n    feedback :: \"#PickupObject.feedback#\";\n}\n\naction PickupObjectFromReceptacleObject {\n    template :: \"take {o} from {r}\";\n    feedback :: \"#PickupObjectFromReceptacleObject.feedback#\";\n}\n\naction PickupEmptyReceptacleObject {\n    template :: \"take {o} from {r}\";\n    feedback :: \"#PickupEmptyReceptacleObject.feedback#\";\n}\n\naction PickupFullReceptacleObject {\n    template :: \"take {outerr} from {r}\";\n    feedback :: \"#PickupFullReceptacleObject.feedback#\";\n}\n\naction PutObject {\n    template :: \"move {o} to {r}\";\n    feedback :: \"#PutObject.feedback#\";\n}\n\naction PutObjectInReceptacleObject {\n    template :: \"put {o} into {outero}\";\n    feedback :: \"#PutObjectInReceptacleObject.feedback#\";\n}\n\naction PutEmptyReceptacleObjectinReceptacle {\n    template :: \"move {o} to {r}\";\n    feedback :: \"#PutEmptyReceptacleObjectinReceptacle.feedback#\";\n}\n\naction PutFullReceptacleObjectInReceptacle {\n    template :: \"put {outero} in {r}\";\n    feedback :: \"#PutFullReceptacleObjectInReceptacle.feedback#\";\n}\n\naction inventory {\n    template :: \"inventory\";\n    feedback :: \"#inventory.feedback#\";\n}\n\naction examineReceptacle {\n    template :: \"examine {r}\";\n    feedback :: \"#examineReceptacle.feedback#\";\n}\n\naction examineObject {\n    template :: \"examine {o}\";\n    feedback :: \"#examineObject.feedback#\";\n}\n\naction ToggleObject {\n    template :: \"use {o}\";\n    feedback :: \"#toggleObject.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"toggleObject.feedback\": [\n                {\n                    \"condition\": \"toggleable(o:object) & istoggled(o:object)\",\n                    \"rhs\": \"You turn on the {o.name}.\"\n                },\n                {\n                    \"condition\": \"toggleable(o:object)\",\n                    \"rhs\": \"You turn off the {o.name}.\"\n                },\n                {\n                    \"rhs\": \"You don't see any switch on the {o.name}.\"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction HeatObject {\n    template :: \"heat {o} with {r}\";\n    feedback :: \"#heatObject.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"heatObject.feedback\": [\n                {\n                    \"rhs\": \"You heat the {o.name} using the {r.name}.\"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction CleanObject {\n    template :: \"clean {o} with {r}\";\n    feedback :: \"#cleanObject.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"cleanObject.feedback\": [\n                {\n                    \"rhs\": \"You clean the {o.name} using the {r.name}.\"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction CoolObject {\n    template :: \"cool {o} with {r}\";\n    feedback :: \"#coolObject.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"coolObject.feedback\": [\n                {\n                    \"rhs\": \"You cool the {o.name} using the {r.name}.\"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction SliceObject {\n    template :: \"slice {co} with {ko}\";\n    feedback :: \"#sliceObject.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"sliceObject.feedback\": [\n                {\n                    \"rhs\": \"You sliced the {co.name} with the {ko.name}.\"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction look {\n    template :: \"look\";\n    feedback :: \"#look.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"look.feedback\": [\n                {\n                   \"condition\": \"atlocation(a:agent, l:location) & receptacleatlocation(r:receptacle, l:location)\",\n                   \"rhs\": \"#look-variations#. Next to it, you see #list_objects_on_the_floor#.\"\n                },\n                {\n                    \"rhs\": \"You are in the middle of a room. Looking quickly around you, you see #list_appliances#.\"\n                }\n            ],\n\n            \"look-variations\": [\n                {\n                    \"rhs\": \"You are facing the [{r.name | atlocation(a:agent, l:location) & receptacleatlocation(r:receptacle, l:location)}]\"\n                }\n            ],\n\n            \"list_objects_on_the_floor\": [\n                {\n                    \"condition\": \"atlocation(a:agent, l:location) & objectatlocation(o:object, l:location) & receptacleatlocation(r:receptacle, l:location) & not_inreceptacle(o:object, r:receptacle)\",\n                    \"rhs\": \"[{#overview(o)# | atlocation(a:agent, l:location) & objectatlocation(o:object, l:location) & receptacleatlocation(r:receptacle, l:location) & not_inreceptacle(o:object, r:receptacle)}]\"\n                },\n                {\n                    \"rhs\": \"nothing\"\n                }\n            ],\n\n            \"list_appliances\": [\n                {\n                    \"condition\": \"receptacleatlocation(r:receptacle, l:location)\",\n                    \"rhs\": \"[{#overview(r)# | receptacleatlocation(r:receptacle, l:location)}]\"\n                },\n                {\n                    \"rhs\": \"nothing\"\n                }\n            ],\n\n            \"overview(o, r)\": [\n                {\n                    \"rhs\": \"{o.indefinite} {o.name} (in/on the {r.name})}\"\n                }\n            ],\n\n            \"overview(o)\": [\n                {\n                    \"rhs\": \"{o.indefinite} {o.name}\"\n                }\n            ],\n            \"overview(r)\": [\n                {\n                    \"rhs\": \"{r.indefinite} {r.name or r.id}\"\n                }\n            ],\n            \"overview_with_state(r)\": [\n                {\n                    \"rhs\": \"{r.indefinite} {r.name or r.id}#overview_state(r)#\"\n                }\n            ],\n            \"overview_state(r)\": [\n                {\n                    \"condition\": \"openable(r:receptacle) & opened(r:receptacle)\",\n                    \"rhs\": \" (it is open)\"\n                },\n                {\n                    \"condition\": \"openable(r:receptacle)\",\n                    \"rhs\": \" (it is closed)\"\n                },\n                {\n                    \"rhs\": \"\"\n                }\n            ],\n\n            \"list_empty\": [\n                {\n                    \"rhs\": \"nothing\"\n                }\n            ],\n            \"list_separator\": [\n                {\n                    \"rhs\": \", \"\n                }\n            ],\n            \"list_last_separator\": [\n                {\n                    \"rhs\": \", and \"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction help {\n    template :: \"help\";\n    feedback :: \"\nAvailable commands:\n  look:                             look around your current location\n  inventory:                        check your current inventory\n  go to (receptacle):               move to a receptacle\n  open (receptacle):                open a receptacle\n  close (receptacle):               close a receptacle\n  take (object) from (receptacle):  take an object from a receptacle\n  move (object) to (receptacle):  place an object in or on a receptacle\n  examine (something):              examine a receptacle or an object\n  use (object):                     use an object\n  heat (object) with (receptacle):  heat an object using a receptacle\n  clean (object) with (receptacle): clean an object using a receptacle\n  cool (object) with (receptacle):  cool an object using a receptacle\n  slice (object) with (object):     slice an object using a sharp object\n\";\n}\n", "pddl_problem": "\n(define (problem plan_trial_T20190907_173342_454704)\n(:domain alfred)\n(:objects\nagent1 - agent\n<PERSON><PERSON><PERSON><PERSON> - object\n        HousePlant - object\n        Candle - object\n        SprayBottle - object\n        Bowl - object\n        Window - object\n        CD - object\n        Egg - object\n        Glassbottle - object\n        Sink - object\n        Pillow - object\n        Spoon - object\n        SoapBottle - object\n        TeddyBear - object\n        ButterKnife - object\n        Cup - object\n        Plate - object\n        RemoteControl - object\n        Tomato - object\n        Statue - object\n        HandTowel - object\n        Knife - object\n        StoveKnob - object\n        LightSwitch - object\n        Pen - object\n        Painting - object\n        DishSponge - object\n        Vase - object\n        Mug - object\n        ToiletPaper - object\n        Box - object\n        Mirror - object\n        Ladle - object\n        Fork - object\n        Blinds - object\n        Footstool - object\n        KeyChain - object\n        Cloth - object\n        Laptop - object\n        TissueBox - object\n        PepperShaker - object\n        FloorLamp - object\n        WateringCan - object\n        Apple - object\n        Pan - object\n        PaperTowel - object\n        PaperTowelRoll - object\n        Newspaper - object\n        Television - object\n        Chair - object\n        CellPhone - object\n        CreditCard - object\n        Lettuce - object\n        BasketBall - object\n        Potato - object\n        Curtains - object\n        Boots - object\n        Pencil - object\n        AlarmClock - object\n        ToiletPaperRoll - object\n        Spatula - object\n        Book - object\n        Bread - object\n        SoapBar - object\n        Watch - object\n        DeskLamp - object\n        Kettle - object\n        Pot - object\n        ScrubBrush - object\n        WineBottle - object\n        ShowerDoor - object\n        Bathtub - object\n        LaundryHamperLid - object\n        ShowerGlass - object\n        Poster - object\n        TennisRacket - object\n        BaseballBat - object\n        Towel - object\n        Plunger - object\nSaltShakerType - otype\n        HousePlantType - otype\n        CandleType - otype\n        SprayBottleType - otype\n        BowlType - otype\n        WindowType - otype\n        CDType - otype\n        EggType - otype\n        GlassbottleType - otype\n        SinkType - otype\n        PillowType - otype\n        SpoonType - otype\n        SoapBottleType - otype\n        TeddyBearType - otype\n        ButterKnifeType - otype\n        CupType - otype\n        PlateType - otype\n        RemoteControlType - otype\n        TomatoType - otype\n        StatueType - otype\n        HandTowelType - otype\n        KnifeType - otype\n        StoveKnobType - otype\n        LightSwitchType - otype\n        PenType - otype\n        PaintingType - otype\n        DishSpongeType - otype\n        VaseType - otype\n        MugType - otype\n        ToiletPaperType - otype\n        BoxType - otype\n        MirrorType - otype\n        LadleType - otype\n        ForkType - otype\n        BlindsType - otype\n        FootstoolType - otype\n        KeyChainType - otype\n        ClothType - otype\n        LaptopType - otype\n        TissueBoxType - otype\n        PepperShakerType - otype\n        FloorLampType - otype\n        WateringCanType - otype\n        AppleType - otype\n        PanType - otype\n        PaperTowelType - otype\n        PaperTowelRollType - otype\n        NewspaperType - otype\n        TelevisionType - otype\n        ChairType - otype\n        CellPhoneType - otype\n        CreditCardType - otype\n        LettuceType - otype\n        BasketBallType - otype\n        PotatoType - otype\n        CurtainsType - otype\n        BootsType - otype\n        PencilType - otype\n        AlarmClockType - otype\n        ToiletPaperRollType - otype\n        SpatulaType - otype\n        BookType - otype\n        BreadType - otype\n        SoapBarType - otype\n        WatchType - otype\n        DeskLampType - otype\n        KettleType - otype\n        PotType - otype\n        ScrubBrushType - otype\n        WineBottleType - otype\n        ShowerDoorType - otype\n        BathtubType - otype\n        LaundryHamperLidType - otype\n        ShowerGlassType - otype\n        PosterType - otype\n        TennisRacketType - otype\n        BaseballBatType - otype\n        TowelType - otype\n        PlungerType - otype\nSafeType - rtype\n        DrawerType - rtype\n        CoffeeMachineType - rtype\n        HandTowelHolderType - rtype\n        SinkBasinType - rtype\n        DresserType - rtype\n        LaundryHamperType - rtype\n        TVStandType - rtype\n        BathtubBasinType - rtype\n        CabinetType - rtype\n        FridgeType - rtype\n        DeskType - rtype\n        ToiletType - rtype\n        CartType - rtype\n        SideTableType - rtype\n        SofaType - rtype\n        CoffeeTableType - rtype\n        DiningTableType - rtype\n        CounterTopType - rtype\n        GarbageCanType - rtype\n        ArmChairType - rtype\n        ShelfType - rtype\n        MicrowaveType - rtype\n        ToasterType - rtype\n        BedType - rtype\n        PaintingHangerType - rtype\n        TowelHolderType - rtype\n        ToiletPaperHangerType - rtype\n        StoveBurnerType - rtype\n        OttomanType - rtype\n\n\n        Apple_bar__plus_00_dot_28_bar__plus_01_dot_15_bar__plus_01_dot_58 - object\n        Bowl_bar__minus_01_dot_57_bar__plus_00_dot_12_bar__minus_01_dot_07 - object\n        Bowl_bar__minus_02_dot_86_bar__plus_00_dot_38_bar__plus_00_dot_69 - object\n        Bowl_bar__minus_02_dot_86_bar__plus_00_dot_82_bar__plus_00_dot_77 - object\n        Bread_bar__minus_02_dot_74_bar__plus_01_dot_53_bar__plus_00_dot_93 - object\n        ButterKnife_bar__minus_02_dot_22_bar__plus_00_dot_90_bar__minus_01_dot_00 - object\n        Chair_bar__plus_01_dot_07_bar__plus_00_dot_01_bar__plus_01_dot_56 - object\n        Chair_bar__plus_01_dot_56_bar__plus_00_dot_01_bar__plus_01_dot_36 - object\n        CreditCard_bar__plus_01_dot_00_bar__plus_00_dot_90_bar__minus_00_dot_98 - object\n        Cup_bar__minus_01_dot_12_bar__plus_00_dot_84_bar__minus_01_dot_24 - object\n        DishSponge_bar__minus_00_dot_34_bar__plus_00_dot_91_bar__minus_01_dot_37 - object\n        DishSponge_bar__minus_01_dot_42_bar__plus_00_dot_85_bar__minus_01_dot_25 - object\n        DishSponge_bar__minus_02_dot_54_bar__plus_00_dot_91_bar__minus_01_dot_37 - object\n        Egg_bar__plus_00_dot_35_bar__plus_01_dot_04_bar__minus_01_dot_25 - object\n        Egg_bar__minus_02_dot_70_bar__plus_00_dot_07_bar__plus_01_dot_57 - object\n        Egg_bar__minus_02_dot_76_bar__plus_00_dot_07_bar__plus_01_dot_57 - object\n        Faucet_bar__minus_01_dot_39_bar__plus_01_dot_14_bar__minus_01_dot_40 - object\n        Fork_bar__minus_01_dot_51_bar__plus_00_dot_85_bar__minus_01_dot_31 - object\n        Fork_bar__minus_01_dot_91_bar__plus_00_dot_75_bar__minus_01_dot_09 - object\n        Kettle_bar__minus_02_dot_62_bar__plus_00_dot_92_bar__minus_00_dot_72 - object\n        Knife_bar__plus_00_dot_06_bar__plus_01_dot_11_bar__plus_01_dot_59 - object\n        Lettuce_bar__minus_02_dot_66_bar__plus_00_dot_87_bar__plus_00_dot_98 - object\n        LightSwitch_bar__minus_01_dot_17_bar__plus_01_dot_31_bar__plus_03_dot_00 - object\n        Mug_bar__plus_00_dot_24_bar__plus_01_dot_00_bar__minus_01_dot_38 - object\n        Mug_bar__plus_00_dot_40_bar__plus_01_dot_00_bar__minus_01_dot_41 - object\n        Mug_bar__minus_02_dot_44_bar__plus_00_dot_91_bar__minus_00_dot_06 - object\n        Pan_bar__minus_02_dot_59_bar__plus_00_dot_90_bar__minus_01_dot_15 - object\n        PepperShaker_bar__minus_00_dot_06_bar__plus_01_dot_11_bar__plus_01_dot_87 - object\n        PepperShaker_bar__minus_00_dot_15_bar__plus_01_dot_11_bar__plus_01_dot_41 - object\n        PepperShaker_bar__minus_01_dot_12_bar__plus_00_dot_11_bar__minus_01_dot_07 - object\n        Plate_bar__plus_01_dot_00_bar__plus_00_dot_91_bar__minus_01_dot_20 - object\n        Plate_bar__minus_02_dot_24_bar__plus_00_dot_91_bar__minus_01_dot_20 - object\n        Potato_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_01_dot_12 - object\n        Pot_bar__minus_02_dot_82_bar__plus_00_dot_93_bar__minus_00_dot_40 - object\n        SaltShaker_bar__minus_00_dot_66_bar__plus_01_dot_66_bar__minus_01_dot_32 - object\n        SaltShaker_bar__minus_01_dot_00_bar__plus_00_dot_11_bar__minus_00_dot_99 - object\n        Sink_bar__minus_01_dot_33_bar__plus_00_dot_92_bar__minus_01_dot_23 - object\n        SoapBottle_bar__minus_00_dot_75_bar__plus_00_dot_90_bar__minus_01_dot_29 - object\n        Spatula_bar__minus_02_dot_55_bar__plus_00_dot_92_bar__plus_00_dot_01 - object\n        Spoon_bar__plus_00_dot_61_bar__plus_00_dot_91_bar__minus_01_dot_28 - object\n        Spoon_bar__plus_00_dot_92_bar__plus_00_dot_91_bar__minus_00_dot_95 - object\n        Spoon_bar__minus_02_dot_59_bar__plus_00_dot_75_bar__plus_00_dot_16 - object\n        StoveKnob_bar__minus_02_dot_43_bar__plus_00_dot_86_bar__minus_00_dot_32 - object\n        StoveKnob_bar__minus_02_dot_43_bar__plus_00_dot_86_bar__minus_00_dot_48 - object\n        StoveKnob_bar__minus_02_dot_43_bar__plus_00_dot_86_bar__minus_00_dot_62 - object\n        StoveKnob_bar__minus_02_dot_43_bar__plus_00_dot_86_bar__minus_00_dot_78 - object\n        Tomato_bar__plus_00_dot_55_bar__plus_01_dot_16_bar__plus_01_dot_48 - object\n        Tomato_bar__minus_02_dot_73_bar__plus_00_dot_09_bar__plus_01_dot_43 - object\n        Window_bar__plus_00_dot_32_bar__plus_01_dot_45_bar__plus_03_dot_09 - object\n        Cabinet_bar__plus_00_dot_17_bar__plus_02_dot_01_bar__minus_01_dot_20 - receptacle\n        Cabinet_bar__plus_00_dot_24_bar__plus_02_dot_01_bar__minus_01_dot_20 - receptacle\n        Cabinet_bar__plus_00_dot_48_bar__plus_00_dot_39_bar__minus_00_dot_90 - receptacle\n        Cabinet_bar__plus_01_dot_12_bar__plus_02_dot_01_bar__minus_01_dot_20 - receptacle\n        Cabinet_bar__plus_01_dot_13_bar__plus_00_dot_47_bar__minus_00_dot_90 - receptacle\n        Cabinet_bar__minus_00_dot_77_bar__plus_02_dot_01_bar__minus_01_dot_20 - receptacle\n        Cabinet_bar__minus_00_dot_82_bar__plus_00_dot_47_bar__minus_00_dot_91 - receptacle\n        Cabinet_bar__minus_00_dot_84_bar__plus_02_dot_01_bar__minus_01_dot_20 - receptacle\n        Cabinet_bar__minus_01_dot_77_bar__plus_02_dot_01_bar__minus_01_dot_20 - receptacle\n        Cabinet_bar__minus_01_dot_80_bar__plus_00_dot_47_bar__minus_00_dot_91 - receptacle\n        Cabinet_bar__minus_01_dot_84_bar__plus_02_dot_01_bar__minus_01_dot_20 - receptacle\n        Cabinet_bar__minus_01_dot_85_bar__plus_00_dot_39_bar__minus_00_dot_90 - receptacle\n        Cabinet_bar__minus_02_dot_39_bar__plus_00_dot_39_bar__plus_00_dot_38 - receptacle\n        Cabinet_bar__minus_02_dot_63_bar__plus_02_dot_01_bar__minus_01_dot_20 - receptacle\n        Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_01_bar__plus_00_dot_36 - receptacle\n        Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_01_bar__minus_00_dot_95 - receptacle\n        Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_08_bar__minus_00_dot_23 - receptacle\n        Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_08_bar__minus_00_dot_88 - receptacle\n        Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_20_bar__plus_00_dot_43 - receptacle\n        Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_20_bar__plus_01_dot_67 - receptacle\n        CoffeeMachine_bar__plus_01_dot_14_bar__plus_01_dot_10_bar__plus_00_dot_30 - receptacle\n        CounterTop_bar__plus_00_dot_07_bar__plus_00_dot_95_bar__minus_01_dot_20 - receptacle\n        CounterTop_bar__plus_00_dot_91_bar__plus_01_dot_15_bar__plus_00_dot_79 - receptacle\n        CounterTop_bar__minus_02_dot_70_bar__plus_00_dot_95_bar__plus_00_dot_11 - receptacle\n        Drawer_bar__plus_00_dot_26_bar__plus_00_dot_78_bar__minus_01_dot_08 - receptacle\n        Drawer_bar__minus_02_dot_03_bar__plus_00_dot_78_bar__minus_01_dot_08 - receptacle\n        Drawer_bar__minus_02_dot_57_bar__plus_00_dot_78_bar__plus_00_dot_10 - receptacle\n        Fridge_bar__minus_02_dot_86_bar__plus_00_dot_00_bar__plus_00_dot_85 - receptacle\n        GarbageCan_bar__minus_02_dot_76_bar__plus_00_dot_00_bar__plus_01_dot_47 - receptacle\n        Microwave_bar__plus_00_dot_28_bar__plus_00_dot_90_bar__minus_01_dot_33 - receptacle\n        Sink_bar__minus_01_dot_33_bar__plus_00_dot_92_bar__minus_01_dot_23_bar_SinkBasin - receptacle\n        StoveBurner_bar__minus_02_dot_62_bar__plus_00_dot_89_bar__minus_00_dot_40 - receptacle\n        StoveBurner_bar__minus_02_dot_62_bar__plus_00_dot_89_bar__minus_00_dot_71 - receptacle\n        StoveBurner_bar__minus_02_dot_82_bar__plus_00_dot_89_bar__minus_00_dot_40 - receptacle\n        StoveBurner_bar__minus_02_dot_82_bar__plus_00_dot_89_bar__minus_00_dot_71 - receptacle\n        Toaster_bar__minus_02_dot_82_bar__plus_00_dot_90_bar__plus_00_dot_25 - receptacle\n        loc_bar_2_bar_0_bar_2_bar_45 - location\n        loc_bar_6_bar_7_bar_2_bar_60 - location\n        loc_bar__minus_6_bar__minus_1_bar_3_bar_45 - location\n        loc_bar__minus_5_bar_10_bar_0_bar_30 - location\n        loc_bar__minus_8_bar__minus_1_bar_2_bar_60 - location\n        loc_bar__minus_6_bar__minus_2_bar_2_bar__minus_30 - location\n        loc_bar__minus_8_bar_0_bar_2_bar_45 - location\n        loc_bar__minus_8_bar__minus_2_bar_2_bar_60 - location\n        loc_bar__minus_8_bar_0_bar_3_bar_45 - location\n        loc_bar_2_bar__minus_2_bar_2_bar__minus_30 - location\n        loc_bar__minus_8_bar__minus_2_bar_3_bar__minus_30 - location\n        loc_bar__minus_4_bar__minus_2_bar_2_bar__minus_30 - location\n        loc_bar__minus_8_bar__minus_2_bar_2_bar__minus_30 - location\n        loc_bar__minus_8_bar_0_bar_3_bar__minus_30 - location\n        loc_bar_0_bar__minus_2_bar_2_bar__minus_30 - location\n        loc_bar__minus_8_bar_1_bar_3_bar_45 - location\n        loc_bar__minus_5_bar__minus_2_bar_2_bar_45 - location\n        loc_bar_4_bar_8_bar_2_bar_60 - location\n        loc_bar__minus_2_bar_0_bar_2_bar_45 - location\n        loc_bar__minus_8_bar__minus_1_bar_2_bar__minus_15 - location\n        loc_bar__minus_7_bar_3_bar_3_bar_60 - location\n        loc_bar_2_bar_0_bar_2_bar_60 - location\n        loc_bar__minus_8_bar__minus_2_bar_3_bar_60 - location\n        loc_bar__minus_8_bar__minus_1_bar_3_bar_60 - location\n        loc_bar__minus_6_bar_7_bar_3_bar__minus_30 - location\n        loc_bar__minus_6_bar__minus_2_bar_3_bar__minus_15 - location\n        loc_bar__minus_3_bar__minus_2_bar_2_bar__minus_30 - location\n        loc_bar__minus_6_bar_1_bar_3_bar_60 - location\n        loc_bar__minus_6_bar_1_bar_3_bar__minus_30 - location\n        loc_bar_1_bar__minus_2_bar_2_bar_45 - location\n        loc_bar__minus_8_bar_6_bar_3_bar_60 - location\n        loc_bar_2_bar_0_bar_1_bar_45 - location\n        loc_bar__minus_1_bar_0_bar_2_bar_60 - location\n        loc_bar__minus_8_bar__minus_2_bar_3_bar_45 - location\n        loc_bar_1_bar_10_bar_0_bar_15 - location\n        loc_bar_2_bar__minus_2_bar_2_bar_45 - location\n        loc_bar__minus_2_bar_6_bar_1_bar_15 - location\n        loc_bar__minus_6_bar_4_bar_2_bar_30 - location\n        )\n    \n\n(:init\n\n\n        (receptacleType Cabinet_bar__minus_00_dot_77_bar__plus_02_dot_01_bar__minus_01_dot_20 CabinetType)\n        (receptacleType StoveBurner_bar__minus_02_dot_82_bar__plus_00_dot_89_bar__minus_00_dot_40 StoveBurnerType)\n        (receptacleType CounterTop_bar__plus_00_dot_91_bar__plus_01_dot_15_bar__plus_00_dot_79 CounterTopType)\n        (receptacleType Cabinet_bar__minus_01_dot_80_bar__plus_00_dot_47_bar__minus_00_dot_91 CabinetType)\n        (receptacleType Toaster_bar__minus_02_dot_82_bar__plus_00_dot_90_bar__plus_00_dot_25 ToasterType)\n        (receptacleType CounterTop_bar__plus_00_dot_07_bar__plus_00_dot_95_bar__minus_01_dot_20 CounterTopType)\n        (receptacleType Microwave_bar__plus_00_dot_28_bar__plus_00_dot_90_bar__minus_01_dot_33 MicrowaveType)\n        (receptacleType Cabinet_bar__minus_02_dot_39_bar__plus_00_dot_39_bar__plus_00_dot_38 CabinetType)\n        (receptacleType Cabinet_bar__plus_01_dot_13_bar__plus_00_dot_47_bar__minus_00_dot_90 CabinetType)\n        (receptacleType Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_01_bar__plus_00_dot_36 CabinetType)\n        (receptacleType Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_08_bar__minus_00_dot_88 CabinetType)\n        (receptacleType Drawer_bar__plus_00_dot_26_bar__plus_00_dot_78_bar__minus_01_dot_08 DrawerType)\n        (receptacleType CounterTop_bar__minus_02_dot_70_bar__plus_00_dot_95_bar__plus_00_dot_11 CounterTopType)\n        (receptacleType GarbageCan_bar__minus_02_dot_76_bar__plus_00_dot_00_bar__plus_01_dot_47 GarbageCanType)\n        (receptacleType Cabinet_bar__minus_02_dot_63_bar__plus_02_dot_01_bar__minus_01_dot_20 CabinetType)\n        (receptacleType StoveBurner_bar__minus_02_dot_62_bar__plus_00_dot_89_bar__minus_00_dot_40 StoveBurnerType)\n        (receptacleType Cabinet_bar__minus_00_dot_84_bar__plus_02_dot_01_bar__minus_01_dot_20 CabinetType)\n        (receptacleType Cabinet_bar__plus_00_dot_48_bar__plus_00_dot_39_bar__minus_00_dot_90 CabinetType)\n        (receptacleType Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_20_bar__plus_01_dot_67 CabinetType)\n        (receptacleType Drawer_bar__minus_02_dot_03_bar__plus_00_dot_78_bar__minus_01_dot_08 DrawerType)\n        (receptacleType Cabinet_bar__minus_01_dot_84_bar__plus_02_dot_01_bar__minus_01_dot_20 CabinetType)\n        (receptacleType Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_08_bar__minus_00_dot_23 CabinetType)\n        (receptacleType StoveBurner_bar__minus_02_dot_82_bar__plus_00_dot_89_bar__minus_00_dot_71 StoveBurnerType)\n        (receptacleType Cabinet_bar__plus_01_dot_12_bar__plus_02_dot_01_bar__minus_01_dot_20 CabinetType)\n        (receptacleType Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_01_bar__minus_00_dot_95 CabinetType)\n        (receptacleType Cabinet_bar__plus_00_dot_17_bar__plus_02_dot_01_bar__minus_01_dot_20 CabinetType)\n        (receptacleType CoffeeMachine_bar__plus_01_dot_14_bar__plus_01_dot_10_bar__plus_00_dot_30 CoffeeMachineType)\n        (receptacleType Cabinet_bar__plus_00_dot_24_bar__plus_02_dot_01_bar__minus_01_dot_20 CabinetType)\n        (receptacleType Fridge_bar__minus_02_dot_86_bar__plus_00_dot_00_bar__plus_00_dot_85 FridgeType)\n        (receptacleType StoveBurner_bar__minus_02_dot_62_bar__plus_00_dot_89_bar__minus_00_dot_71 StoveBurnerType)\n        (receptacleType Cabinet_bar__minus_01_dot_77_bar__plus_02_dot_01_bar__minus_01_dot_20 CabinetType)\n        (receptacleType Sink_bar__minus_01_dot_33_bar__plus_00_dot_92_bar__minus_01_dot_23_bar_SinkBasin SinkBasinType)\n        (receptacleType Cabinet_bar__minus_00_dot_82_bar__plus_00_dot_47_bar__minus_00_dot_91 CabinetType)\n        (receptacleType Cabinet_bar__minus_01_dot_85_bar__plus_00_dot_39_bar__minus_00_dot_90 CabinetType)\n        (receptacleType Drawer_bar__minus_02_dot_57_bar__plus_00_dot_78_bar__plus_00_dot_10 DrawerType)\n        (receptacleType Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_20_bar__plus_00_dot_43 CabinetType)\n        (objectType Kettle_bar__minus_02_dot_62_bar__plus_00_dot_92_bar__minus_00_dot_72 KettleType)\n        (objectType Fork_bar__minus_01_dot_51_bar__plus_00_dot_85_bar__minus_01_dot_31 ForkType)\n        (objectType Fork_bar__minus_01_dot_91_bar__plus_00_dot_75_bar__minus_01_dot_09 ForkType)\n        (objectType Tomato_bar__plus_00_dot_55_bar__plus_01_dot_16_bar__plus_01_dot_48 TomatoType)\n        (objectType PepperShaker_bar__minus_00_dot_15_bar__plus_01_dot_11_bar__plus_01_dot_41 PepperShakerType)\n        (objectType Bowl_bar__minus_01_dot_57_bar__plus_00_dot_12_bar__minus_01_dot_07 BowlType)\n        (objectType PepperShaker_bar__minus_01_dot_12_bar__plus_00_dot_11_bar__minus_01_dot_07 PepperShakerType)\n        (objectType LightSwitch_bar__minus_01_dot_17_bar__plus_01_dot_31_bar__plus_03_dot_00 LightSwitchType)\n        (objectType Spoon_bar__minus_02_dot_59_bar__plus_00_dot_75_bar__plus_00_dot_16 SpoonType)\n        (objectType Window_bar__plus_00_dot_32_bar__plus_01_dot_45_bar__plus_03_dot_09 WindowType)\n        (objectType Plate_bar__minus_02_dot_24_bar__plus_00_dot_91_bar__minus_01_dot_20 PlateType)\n        (objectType Apple_bar__plus_00_dot_28_bar__plus_01_dot_15_bar__plus_01_dot_58 AppleType)\n        (objectType Bowl_bar__minus_02_dot_86_bar__plus_00_dot_38_bar__plus_00_dot_69 BowlType)\n        (objectType PepperShaker_bar__minus_00_dot_06_bar__plus_01_dot_11_bar__plus_01_dot_87 PepperShakerType)\n        (objectType StoveKnob_bar__minus_02_dot_43_bar__plus_00_dot_86_bar__minus_00_dot_48 StoveKnobType)\n        (objectType Egg_bar__minus_02_dot_70_bar__plus_00_dot_07_bar__plus_01_dot_57 EggType)\n        (objectType CreditCard_bar__plus_01_dot_00_bar__plus_00_dot_90_bar__minus_00_dot_98 CreditCardType)\n        (objectType Spoon_bar__plus_00_dot_92_bar__plus_00_dot_91_bar__minus_00_dot_95 SpoonType)\n        (objectType DishSponge_bar__minus_02_dot_54_bar__plus_00_dot_91_bar__minus_01_dot_37 DishSpongeType)\n        (objectType Knife_bar__plus_00_dot_06_bar__plus_01_dot_11_bar__plus_01_dot_59 KnifeType)\n        (objectType DishSponge_bar__minus_01_dot_42_bar__plus_00_dot_85_bar__minus_01_dot_25 DishSpongeType)\n        (objectType StoveKnob_bar__minus_02_dot_43_bar__plus_00_dot_86_bar__minus_00_dot_78 StoveKnobType)\n        (objectType SoapBottle_bar__minus_00_dot_75_bar__plus_00_dot_90_bar__minus_01_dot_29 SoapBottleType)\n        (objectType Mug_bar__minus_02_dot_44_bar__plus_00_dot_91_bar__minus_00_dot_06 MugType)\n        (objectType Egg_bar__plus_00_dot_35_bar__plus_01_dot_04_bar__minus_01_dot_25 EggType)\n        (objectType Sink_bar__minus_01_dot_33_bar__plus_00_dot_92_bar__minus_01_dot_23 SinkType)\n        (objectType Chair_bar__plus_01_dot_07_bar__plus_00_dot_01_bar__plus_01_dot_56 ChairType)\n        (objectType Lettuce_bar__minus_02_dot_66_bar__plus_00_dot_87_bar__plus_00_dot_98 LettuceType)\n        (objectType Potato_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_01_dot_12 PotatoType)\n        (objectType Mug_bar__plus_00_dot_40_bar__plus_01_dot_00_bar__minus_01_dot_41 MugType)\n        (objectType Spoon_bar__plus_00_dot_61_bar__plus_00_dot_91_bar__minus_01_dot_28 SpoonType)\n        (objectType Bread_bar__minus_02_dot_74_bar__plus_01_dot_53_bar__plus_00_dot_93 BreadType)\n        (objectType Plate_bar__plus_01_dot_00_bar__plus_00_dot_91_bar__minus_01_dot_20 PlateType)\n        (objectType DishSponge_bar__minus_00_dot_34_bar__plus_00_dot_91_bar__minus_01_dot_37 DishSpongeType)\n        (objectType StoveKnob_bar__minus_02_dot_43_bar__plus_00_dot_86_bar__minus_00_dot_62 StoveKnobType)\n        (objectType Spatula_bar__minus_02_dot_55_bar__plus_00_dot_92_bar__plus_00_dot_01 SpatulaType)\n        (objectType Pot_bar__minus_02_dot_82_bar__plus_00_dot_93_bar__minus_00_dot_40 PotType)\n        (objectType StoveKnob_bar__minus_02_dot_43_bar__plus_00_dot_86_bar__minus_00_dot_32 StoveKnobType)\n        (objectType Tomato_bar__minus_02_dot_73_bar__plus_00_dot_09_bar__plus_01_dot_43 TomatoType)\n        (objectType SaltShaker_bar__minus_00_dot_66_bar__plus_01_dot_66_bar__minus_01_dot_32 SaltShakerType)\n        (objectType Mug_bar__plus_00_dot_24_bar__plus_01_dot_00_bar__minus_01_dot_38 MugType)\n        (objectType Egg_bar__minus_02_dot_76_bar__plus_00_dot_07_bar__plus_01_dot_57 EggType)\n        (objectType Chair_bar__plus_01_dot_56_bar__plus_00_dot_01_bar__plus_01_dot_36 ChairType)\n        (objectType Bowl_bar__minus_02_dot_86_bar__plus_00_dot_82_bar__plus_00_dot_77 BowlType)\n        (objectType Pan_bar__minus_02_dot_59_bar__plus_00_dot_90_bar__minus_01_dot_15 PanType)\n        (objectType Cup_bar__minus_01_dot_12_bar__plus_00_dot_84_bar__minus_01_dot_24 CupType)\n        (objectType ButterKnife_bar__minus_02_dot_22_bar__plus_00_dot_90_bar__minus_01_dot_00 ButterKnifeType)\n        (objectType SaltShaker_bar__minus_01_dot_00_bar__plus_00_dot_11_bar__minus_00_dot_99 SaltShakerType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType KettleType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain StoveBurnerType KettleType)\n        (canContain StoveBurnerType PotType)\n        (canContain StoveBurnerType PanType)\n        (canContain CounterTopType SaltShakerType)\n        (canContain CounterTopType BreadType)\n        (canContain CounterTopType DishSpongeType)\n        (canContain CounterTopType BowlType)\n        (canContain CounterTopType KettleType)\n        (canContain CounterTopType PotType)\n        (canContain CounterTopType MugType)\n        (canContain CounterTopType EggType)\n        (canContain CounterTopType ForkType)\n        (canContain CounterTopType CreditCardType)\n        (canContain CounterTopType SpoonType)\n        (canContain CounterTopType SoapBottleType)\n        (canContain CounterTopType LettuceType)\n        (canContain CounterTopType PotatoType)\n        (canContain CounterTopType ButterKnifeType)\n        (canContain CounterTopType CupType)\n        (canContain CounterTopType PlateType)\n        (canContain CounterTopType PepperShakerType)\n        (canContain CounterTopType TomatoType)\n        (canContain CounterTopType KnifeType)\n        (canContain CounterTopType AppleType)\n        (canContain CounterTopType PanType)\n        (canContain CounterTopType SpatulaType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType KettleType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CounterTopType SaltShakerType)\n        (canContain CounterTopType BreadType)\n        (canContain CounterTopType DishSpongeType)\n        (canContain CounterTopType BowlType)\n        (canContain CounterTopType KettleType)\n        (canContain CounterTopType PotType)\n        (canContain CounterTopType MugType)\n        (canContain CounterTopType EggType)\n        (canContain CounterTopType ForkType)\n        (canContain CounterTopType CreditCardType)\n        (canContain CounterTopType SpoonType)\n        (canContain CounterTopType SoapBottleType)\n        (canContain CounterTopType LettuceType)\n        (canContain CounterTopType PotatoType)\n        (canContain CounterTopType ButterKnifeType)\n        (canContain CounterTopType CupType)\n        (canContain CounterTopType PlateType)\n        (canContain CounterTopType PepperShakerType)\n        (canContain CounterTopType TomatoType)\n        (canContain CounterTopType KnifeType)\n        (canContain CounterTopType AppleType)\n        (canContain CounterTopType PanType)\n        (canContain CounterTopType SpatulaType)\n        (canContain MicrowaveType MugType)\n        (canContain MicrowaveType PotatoType)\n        (canContain MicrowaveType EggType)\n        (canContain MicrowaveType AppleType)\n        (canContain MicrowaveType TomatoType)\n        (canContain MicrowaveType BreadType)\n        (canContain MicrowaveType CupType)\n        (canContain MicrowaveType PlateType)\n        (canContain MicrowaveType BowlType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType KettleType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType KettleType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType KettleType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType KettleType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain DrawerType SaltShakerType)\n        (canContain DrawerType DishSpongeType)\n        (canContain DrawerType ForkType)\n        (canContain DrawerType CreditCardType)\n        (canContain DrawerType SpoonType)\n        (canContain DrawerType SoapBottleType)\n        (canContain DrawerType ButterKnifeType)\n        (canContain DrawerType PepperShakerType)\n        (canContain DrawerType KnifeType)\n        (canContain DrawerType SpatulaType)\n        (canContain CounterTopType SaltShakerType)\n        (canContain CounterTopType BreadType)\n        (canContain CounterTopType DishSpongeType)\n        (canContain CounterTopType BowlType)\n        (canContain CounterTopType KettleType)\n        (canContain CounterTopType PotType)\n        (canContain CounterTopType MugType)\n        (canContain CounterTopType EggType)\n        (canContain CounterTopType ForkType)\n        (canContain CounterTopType CreditCardType)\n        (canContain CounterTopType SpoonType)\n        (canContain CounterTopType SoapBottleType)\n        (canContain CounterTopType LettuceType)\n        (canContain CounterTopType PotatoType)\n        (canContain CounterTopType ButterKnifeType)\n        (canContain CounterTopType CupType)\n        (canContain CounterTopType PlateType)\n        (canContain CounterTopType PepperShakerType)\n        (canContain CounterTopType TomatoType)\n        (canContain CounterTopType KnifeType)\n        (canContain CounterTopType AppleType)\n        (canContain CounterTopType PanType)\n        (canContain CounterTopType SpatulaType)\n        (canContain GarbageCanType BreadType)\n        (canContain GarbageCanType DishSpongeType)\n        (canContain GarbageCanType EggType)\n        (canContain GarbageCanType SoapBottleType)\n        (canContain GarbageCanType LettuceType)\n        (canContain GarbageCanType PotatoType)\n        (canContain GarbageCanType TomatoType)\n        (canContain GarbageCanType AppleType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType KettleType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain StoveBurnerType KettleType)\n        (canContain StoveBurnerType PotType)\n        (canContain StoveBurnerType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType KettleType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType KettleType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType KettleType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain DrawerType SaltShakerType)\n        (canContain DrawerType DishSpongeType)\n        (canContain DrawerType ForkType)\n        (canContain DrawerType CreditCardType)\n        (canContain DrawerType SpoonType)\n        (canContain DrawerType SoapBottleType)\n        (canContain DrawerType ButterKnifeType)\n        (canContain DrawerType PepperShakerType)\n        (canContain DrawerType KnifeType)\n        (canContain DrawerType SpatulaType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType KettleType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType KettleType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain StoveBurnerType KettleType)\n        (canContain StoveBurnerType PotType)\n        (canContain StoveBurnerType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType KettleType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType KettleType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType KettleType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CoffeeMachineType MugType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType KettleType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain FridgeType BreadType)\n        (canContain FridgeType BowlType)\n        (canContain FridgeType PotType)\n        (canContain FridgeType MugType)\n        (canContain FridgeType EggType)\n        (canContain FridgeType LettuceType)\n        (canContain FridgeType PotatoType)\n        (canContain FridgeType CupType)\n        (canContain FridgeType PlateType)\n        (canContain FridgeType TomatoType)\n        (canContain FridgeType AppleType)\n        (canContain FridgeType PanType)\n        (canContain StoveBurnerType KettleType)\n        (canContain StoveBurnerType PotType)\n        (canContain StoveBurnerType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType KettleType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain SinkBasinType DishSpongeType)\n        (canContain SinkBasinType BowlType)\n        (canContain SinkBasinType KettleType)\n        (canContain SinkBasinType PotType)\n        (canContain SinkBasinType MugType)\n        (canContain SinkBasinType EggType)\n        (canContain SinkBasinType ForkType)\n        (canContain SinkBasinType SpoonType)\n        (canContain SinkBasinType LettuceType)\n        (canContain SinkBasinType PotatoType)\n        (canContain SinkBasinType ButterKnifeType)\n        (canContain SinkBasinType CupType)\n        (canContain SinkBasinType PlateType)\n        (canContain SinkBasinType TomatoType)\n        (canContain SinkBasinType KnifeType)\n        (canContain SinkBasinType AppleType)\n        (canContain SinkBasinType PanType)\n        (canContain SinkBasinType SpatulaType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType KettleType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType KettleType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain DrawerType SaltShakerType)\n        (canContain DrawerType DishSpongeType)\n        (canContain DrawerType ForkType)\n        (canContain DrawerType CreditCardType)\n        (canContain DrawerType SpoonType)\n        (canContain DrawerType SoapBottleType)\n        (canContain DrawerType ButterKnifeType)\n        (canContain DrawerType PepperShakerType)\n        (canContain DrawerType KnifeType)\n        (canContain DrawerType SpatulaType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType KettleType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (pickupable Kettle_bar__minus_02_dot_62_bar__plus_00_dot_92_bar__minus_00_dot_72)\n        (pickupable Fork_bar__minus_01_dot_51_bar__plus_00_dot_85_bar__minus_01_dot_31)\n        (pickupable Fork_bar__minus_01_dot_91_bar__plus_00_dot_75_bar__minus_01_dot_09)\n        (pickupable Tomato_bar__plus_00_dot_55_bar__plus_01_dot_16_bar__plus_01_dot_48)\n        (pickupable PepperShaker_bar__minus_00_dot_15_bar__plus_01_dot_11_bar__plus_01_dot_41)\n        (pickupable Bowl_bar__minus_01_dot_57_bar__plus_00_dot_12_bar__minus_01_dot_07)\n        (pickupable PepperShaker_bar__minus_01_dot_12_bar__plus_00_dot_11_bar__minus_01_dot_07)\n        (pickupable Spoon_bar__minus_02_dot_59_bar__plus_00_dot_75_bar__plus_00_dot_16)\n        (pickupable Plate_bar__minus_02_dot_24_bar__plus_00_dot_91_bar__minus_01_dot_20)\n        (pickupable Apple_bar__plus_00_dot_28_bar__plus_01_dot_15_bar__plus_01_dot_58)\n        (pickupable Bowl_bar__minus_02_dot_86_bar__plus_00_dot_38_bar__plus_00_dot_69)\n        (pickupable PepperShaker_bar__minus_00_dot_06_bar__plus_01_dot_11_bar__plus_01_dot_87)\n        (pickupable Egg_bar__minus_02_dot_70_bar__plus_00_dot_07_bar__plus_01_dot_57)\n        (pickupable CreditCard_bar__plus_01_dot_00_bar__plus_00_dot_90_bar__minus_00_dot_98)\n        (pickupable Spoon_bar__plus_00_dot_92_bar__plus_00_dot_91_bar__minus_00_dot_95)\n        (pickupable DishSponge_bar__minus_02_dot_54_bar__plus_00_dot_91_bar__minus_01_dot_37)\n        (pickupable Knife_bar__plus_00_dot_06_bar__plus_01_dot_11_bar__plus_01_dot_59)\n        (pickupable DishSponge_bar__minus_01_dot_42_bar__plus_00_dot_85_bar__minus_01_dot_25)\n        (pickupable SoapBottle_bar__minus_00_dot_75_bar__plus_00_dot_90_bar__minus_01_dot_29)\n        (pickupable Mug_bar__minus_02_dot_44_bar__plus_00_dot_91_bar__minus_00_dot_06)\n        (pickupable Egg_bar__plus_00_dot_35_bar__plus_01_dot_04_bar__minus_01_dot_25)\n        (pickupable Lettuce_bar__minus_02_dot_66_bar__plus_00_dot_87_bar__plus_00_dot_98)\n        (pickupable Potato_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_01_dot_12)\n        (pickupable Mug_bar__plus_00_dot_40_bar__plus_01_dot_00_bar__minus_01_dot_41)\n        (pickupable Spoon_bar__plus_00_dot_61_bar__plus_00_dot_91_bar__minus_01_dot_28)\n        (pickupable Bread_bar__minus_02_dot_74_bar__plus_01_dot_53_bar__plus_00_dot_93)\n        (pickupable Plate_bar__plus_01_dot_00_bar__plus_00_dot_91_bar__minus_01_dot_20)\n        (pickupable DishSponge_bar__minus_00_dot_34_bar__plus_00_dot_91_bar__minus_01_dot_37)\n        (pickupable Spatula_bar__minus_02_dot_55_bar__plus_00_dot_92_bar__plus_00_dot_01)\n        (pickupable Pot_bar__minus_02_dot_82_bar__plus_00_dot_93_bar__minus_00_dot_40)\n        (pickupable Tomato_bar__minus_02_dot_73_bar__plus_00_dot_09_bar__plus_01_dot_43)\n        (pickupable SaltShaker_bar__minus_00_dot_66_bar__plus_01_dot_66_bar__minus_01_dot_32)\n        (pickupable Mug_bar__plus_00_dot_24_bar__plus_01_dot_00_bar__minus_01_dot_38)\n        (pickupable Egg_bar__minus_02_dot_76_bar__plus_00_dot_07_bar__plus_01_dot_57)\n        (pickupable Bowl_bar__minus_02_dot_86_bar__plus_00_dot_82_bar__plus_00_dot_77)\n        (pickupable Pan_bar__minus_02_dot_59_bar__plus_00_dot_90_bar__minus_01_dot_15)\n        (pickupable Cup_bar__minus_01_dot_12_bar__plus_00_dot_84_bar__minus_01_dot_24)\n        (pickupable ButterKnife_bar__minus_02_dot_22_bar__plus_00_dot_90_bar__minus_01_dot_00)\n        (pickupable SaltShaker_bar__minus_01_dot_00_bar__plus_00_dot_11_bar__minus_00_dot_99)\n        (isReceptacleObject Bowl_bar__minus_01_dot_57_bar__plus_00_dot_12_bar__minus_01_dot_07)\n        (isReceptacleObject Plate_bar__minus_02_dot_24_bar__plus_00_dot_91_bar__minus_01_dot_20)\n        (isReceptacleObject Bowl_bar__minus_02_dot_86_bar__plus_00_dot_38_bar__plus_00_dot_69)\n        (isReceptacleObject Mug_bar__minus_02_dot_44_bar__plus_00_dot_91_bar__minus_00_dot_06)\n        (isReceptacleObject Mug_bar__plus_00_dot_40_bar__plus_01_dot_00_bar__minus_01_dot_41)\n        (isReceptacleObject Plate_bar__plus_01_dot_00_bar__plus_00_dot_91_bar__minus_01_dot_20)\n        (isReceptacleObject Pot_bar__minus_02_dot_82_bar__plus_00_dot_93_bar__minus_00_dot_40)\n        (isReceptacleObject Mug_bar__plus_00_dot_24_bar__plus_01_dot_00_bar__minus_01_dot_38)\n        (isReceptacleObject Bowl_bar__minus_02_dot_86_bar__plus_00_dot_82_bar__plus_00_dot_77)\n        (isReceptacleObject Pan_bar__minus_02_dot_59_bar__plus_00_dot_90_bar__minus_01_dot_15)\n        (isReceptacleObject Cup_bar__minus_01_dot_12_bar__plus_00_dot_84_bar__minus_01_dot_24)\n        (openable Cabinet_bar__minus_00_dot_77_bar__plus_02_dot_01_bar__minus_01_dot_20)\n        (openable Cabinet_bar__minus_01_dot_80_bar__plus_00_dot_47_bar__minus_00_dot_91)\n        (openable Microwave_bar__plus_00_dot_28_bar__plus_00_dot_90_bar__minus_01_dot_33)\n        (openable Cabinet_bar__minus_02_dot_39_bar__plus_00_dot_39_bar__plus_00_dot_38)\n        (openable Cabinet_bar__plus_01_dot_13_bar__plus_00_dot_47_bar__minus_00_dot_90)\n        (openable Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_01_bar__plus_00_dot_36)\n        (openable Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_08_bar__minus_00_dot_88)\n        (openable Drawer_bar__plus_00_dot_26_bar__plus_00_dot_78_bar__minus_01_dot_08)\n        (openable Cabinet_bar__minus_02_dot_63_bar__plus_02_dot_01_bar__minus_01_dot_20)\n        (openable Cabinet_bar__minus_00_dot_84_bar__plus_02_dot_01_bar__minus_01_dot_20)\n        (openable Cabinet_bar__plus_00_dot_48_bar__plus_00_dot_39_bar__minus_00_dot_90)\n        (openable Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_20_bar__plus_01_dot_67)\n        (openable Cabinet_bar__minus_01_dot_84_bar__plus_02_dot_01_bar__minus_01_dot_20)\n        (openable Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_08_bar__minus_00_dot_23)\n        (openable Cabinet_bar__plus_01_dot_12_bar__plus_02_dot_01_bar__minus_01_dot_20)\n        (openable Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_01_bar__minus_00_dot_95)\n        (openable Cabinet_bar__plus_00_dot_17_bar__plus_02_dot_01_bar__minus_01_dot_20)\n        (openable Cabinet_bar__plus_00_dot_24_bar__plus_02_dot_01_bar__minus_01_dot_20)\n        (openable Fridge_bar__minus_02_dot_86_bar__plus_00_dot_00_bar__plus_00_dot_85)\n        (openable Cabinet_bar__minus_01_dot_77_bar__plus_02_dot_01_bar__minus_01_dot_20)\n        (openable Cabinet_bar__minus_00_dot_82_bar__plus_00_dot_47_bar__minus_00_dot_91)\n        (openable Cabinet_bar__minus_01_dot_85_bar__plus_00_dot_39_bar__minus_00_dot_90)\n        (openable Drawer_bar__minus_02_dot_57_bar__plus_00_dot_78_bar__plus_00_dot_10)\n        (openable Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_20_bar__plus_00_dot_43)\n        \n        (atLocation agent1 loc_bar__minus_6_bar_4_bar_2_bar_30)\n        \n        (cleanable Kettle_bar__minus_02_dot_62_bar__plus_00_dot_92_bar__minus_00_dot_72)\n        (cleanable Fork_bar__minus_01_dot_51_bar__plus_00_dot_85_bar__minus_01_dot_31)\n        (cleanable Fork_bar__minus_01_dot_91_bar__plus_00_dot_75_bar__minus_01_dot_09)\n        (cleanable Tomato_bar__plus_00_dot_55_bar__plus_01_dot_16_bar__plus_01_dot_48)\n        (cleanable Bowl_bar__minus_01_dot_57_bar__plus_00_dot_12_bar__minus_01_dot_07)\n        (cleanable Spoon_bar__minus_02_dot_59_bar__plus_00_dot_75_bar__plus_00_dot_16)\n        (cleanable Plate_bar__minus_02_dot_24_bar__plus_00_dot_91_bar__minus_01_dot_20)\n        (cleanable Apple_bar__plus_00_dot_28_bar__plus_01_dot_15_bar__plus_01_dot_58)\n        (cleanable Bowl_bar__minus_02_dot_86_bar__plus_00_dot_38_bar__plus_00_dot_69)\n        (cleanable Egg_bar__minus_02_dot_70_bar__plus_00_dot_07_bar__plus_01_dot_57)\n        (cleanable Spoon_bar__plus_00_dot_92_bar__plus_00_dot_91_bar__minus_00_dot_95)\n        (cleanable DishSponge_bar__minus_02_dot_54_bar__plus_00_dot_91_bar__minus_01_dot_37)\n        (cleanable Knife_bar__plus_00_dot_06_bar__plus_01_dot_11_bar__plus_01_dot_59)\n        (cleanable DishSponge_bar__minus_01_dot_42_bar__plus_00_dot_85_bar__minus_01_dot_25)\n        (cleanable Mug_bar__minus_02_dot_44_bar__plus_00_dot_91_bar__minus_00_dot_06)\n        (cleanable Egg_bar__plus_00_dot_35_bar__plus_01_dot_04_bar__minus_01_dot_25)\n        (cleanable Lettuce_bar__minus_02_dot_66_bar__plus_00_dot_87_bar__plus_00_dot_98)\n        (cleanable Potato_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_01_dot_12)\n        (cleanable Mug_bar__plus_00_dot_40_bar__plus_01_dot_00_bar__minus_01_dot_41)\n        (cleanable Spoon_bar__plus_00_dot_61_bar__plus_00_dot_91_bar__minus_01_dot_28)\n        (cleanable Plate_bar__plus_01_dot_00_bar__plus_00_dot_91_bar__minus_01_dot_20)\n        (cleanable DishSponge_bar__minus_00_dot_34_bar__plus_00_dot_91_bar__minus_01_dot_37)\n        (cleanable Spatula_bar__minus_02_dot_55_bar__plus_00_dot_92_bar__plus_00_dot_01)\n        (cleanable Pot_bar__minus_02_dot_82_bar__plus_00_dot_93_bar__minus_00_dot_40)\n        (cleanable Tomato_bar__minus_02_dot_73_bar__plus_00_dot_09_bar__plus_01_dot_43)\n        (cleanable Mug_bar__plus_00_dot_24_bar__plus_01_dot_00_bar__minus_01_dot_38)\n        (cleanable Egg_bar__minus_02_dot_76_bar__plus_00_dot_07_bar__plus_01_dot_57)\n        (cleanable Bowl_bar__minus_02_dot_86_bar__plus_00_dot_82_bar__plus_00_dot_77)\n        (cleanable Pan_bar__minus_02_dot_59_bar__plus_00_dot_90_bar__minus_01_dot_15)\n        (cleanable Cup_bar__minus_01_dot_12_bar__plus_00_dot_84_bar__minus_01_dot_24)\n        (cleanable ButterKnife_bar__minus_02_dot_22_bar__plus_00_dot_90_bar__minus_01_dot_00)\n        \n        (heatable Tomato_bar__plus_00_dot_55_bar__plus_01_dot_16_bar__plus_01_dot_48)\n        (heatable Plate_bar__minus_02_dot_24_bar__plus_00_dot_91_bar__minus_01_dot_20)\n        (heatable Apple_bar__plus_00_dot_28_bar__plus_01_dot_15_bar__plus_01_dot_58)\n        (heatable Egg_bar__minus_02_dot_70_bar__plus_00_dot_07_bar__plus_01_dot_57)\n        (heatable Mug_bar__minus_02_dot_44_bar__plus_00_dot_91_bar__minus_00_dot_06)\n        (heatable Egg_bar__plus_00_dot_35_bar__plus_01_dot_04_bar__minus_01_dot_25)\n        (heatable Potato_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_01_dot_12)\n        (heatable Mug_bar__plus_00_dot_40_bar__plus_01_dot_00_bar__minus_01_dot_41)\n        (heatable Bread_bar__minus_02_dot_74_bar__plus_01_dot_53_bar__plus_00_dot_93)\n        (heatable Plate_bar__plus_01_dot_00_bar__plus_00_dot_91_bar__minus_01_dot_20)\n        (heatable Tomato_bar__minus_02_dot_73_bar__plus_00_dot_09_bar__plus_01_dot_43)\n        (heatable Mug_bar__plus_00_dot_24_bar__plus_01_dot_00_bar__minus_01_dot_38)\n        (heatable Egg_bar__minus_02_dot_76_bar__plus_00_dot_07_bar__plus_01_dot_57)\n        (heatable Cup_bar__minus_01_dot_12_bar__plus_00_dot_84_bar__minus_01_dot_24)\n        (coolable Tomato_bar__plus_00_dot_55_bar__plus_01_dot_16_bar__plus_01_dot_48)\n        (coolable Bowl_bar__minus_01_dot_57_bar__plus_00_dot_12_bar__minus_01_dot_07)\n        (coolable Plate_bar__minus_02_dot_24_bar__plus_00_dot_91_bar__minus_01_dot_20)\n        (coolable Apple_bar__plus_00_dot_28_bar__plus_01_dot_15_bar__plus_01_dot_58)\n        (coolable Bowl_bar__minus_02_dot_86_bar__plus_00_dot_38_bar__plus_00_dot_69)\n        (coolable Egg_bar__minus_02_dot_70_bar__plus_00_dot_07_bar__plus_01_dot_57)\n        (coolable Mug_bar__minus_02_dot_44_bar__plus_00_dot_91_bar__minus_00_dot_06)\n        (coolable Egg_bar__plus_00_dot_35_bar__plus_01_dot_04_bar__minus_01_dot_25)\n        (coolable Lettuce_bar__minus_02_dot_66_bar__plus_00_dot_87_bar__plus_00_dot_98)\n        (coolable Potato_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_01_dot_12)\n        (coolable Mug_bar__plus_00_dot_40_bar__plus_01_dot_00_bar__minus_01_dot_41)\n        (coolable Bread_bar__minus_02_dot_74_bar__plus_01_dot_53_bar__plus_00_dot_93)\n        (coolable Plate_bar__plus_01_dot_00_bar__plus_00_dot_91_bar__minus_01_dot_20)\n        (coolable Pot_bar__minus_02_dot_82_bar__plus_00_dot_93_bar__minus_00_dot_40)\n        (coolable Tomato_bar__minus_02_dot_73_bar__plus_00_dot_09_bar__plus_01_dot_43)\n        (coolable Mug_bar__plus_00_dot_24_bar__plus_01_dot_00_bar__minus_01_dot_38)\n        (coolable Egg_bar__minus_02_dot_76_bar__plus_00_dot_07_bar__plus_01_dot_57)\n        (coolable Bowl_bar__minus_02_dot_86_bar__plus_00_dot_82_bar__plus_00_dot_77)\n        (coolable Pan_bar__minus_02_dot_59_bar__plus_00_dot_90_bar__minus_01_dot_15)\n        (coolable Cup_bar__minus_01_dot_12_bar__plus_00_dot_84_bar__minus_01_dot_24)\n        \n        \n        \n        \n        \n        (sliceable Tomato_bar__plus_00_dot_55_bar__plus_01_dot_16_bar__plus_01_dot_48)\n        (sliceable Apple_bar__plus_00_dot_28_bar__plus_01_dot_15_bar__plus_01_dot_58)\n        (sliceable Egg_bar__minus_02_dot_70_bar__plus_00_dot_07_bar__plus_01_dot_57)\n        (sliceable Egg_bar__plus_00_dot_35_bar__plus_01_dot_04_bar__minus_01_dot_25)\n        (sliceable Lettuce_bar__minus_02_dot_66_bar__plus_00_dot_87_bar__plus_00_dot_98)\n        (sliceable Potato_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_01_dot_12)\n        (sliceable Bread_bar__minus_02_dot_74_bar__plus_01_dot_53_bar__plus_00_dot_93)\n        (sliceable Tomato_bar__minus_02_dot_73_bar__plus_00_dot_09_bar__plus_01_dot_43)\n        (sliceable Egg_bar__minus_02_dot_76_bar__plus_00_dot_07_bar__plus_01_dot_57)\n        \n        (inReceptacle SaltShaker_bar__minus_01_dot_00_bar__plus_00_dot_11_bar__minus_00_dot_99 Cabinet_bar__minus_00_dot_82_bar__plus_00_dot_47_bar__minus_00_dot_91)\n        (inReceptacle PepperShaker_bar__minus_01_dot_12_bar__plus_00_dot_11_bar__minus_01_dot_07 Cabinet_bar__minus_00_dot_82_bar__plus_00_dot_47_bar__minus_00_dot_91)\n        (inReceptacle Bowl_bar__minus_01_dot_57_bar__plus_00_dot_12_bar__minus_01_dot_07 Cabinet_bar__minus_01_dot_80_bar__plus_00_dot_47_bar__minus_00_dot_91)\n        (inReceptacle SaltShaker_bar__minus_00_dot_66_bar__plus_01_dot_66_bar__minus_01_dot_32 Cabinet_bar__minus_00_dot_77_bar__plus_02_dot_01_bar__minus_01_dot_20)\n        (inReceptacle Kettle_bar__minus_02_dot_62_bar__plus_00_dot_92_bar__minus_00_dot_72 StoveBurner_bar__minus_02_dot_62_bar__plus_00_dot_89_bar__minus_00_dot_71)\n        (inReceptacle Spatula_bar__minus_02_dot_55_bar__plus_00_dot_92_bar__plus_00_dot_01 CounterTop_bar__minus_02_dot_70_bar__plus_00_dot_95_bar__plus_00_dot_11)\n        (inReceptacle Mug_bar__minus_02_dot_44_bar__plus_00_dot_91_bar__minus_00_dot_06 CounterTop_bar__minus_02_dot_70_bar__plus_00_dot_95_bar__plus_00_dot_11)\n        (inReceptacle Knife_bar__plus_00_dot_06_bar__plus_01_dot_11_bar__plus_01_dot_59 CounterTop_bar__plus_00_dot_91_bar__plus_01_dot_15_bar__plus_00_dot_79)\n        (inReceptacle Tomato_bar__plus_00_dot_55_bar__plus_01_dot_16_bar__plus_01_dot_48 CounterTop_bar__plus_00_dot_91_bar__plus_01_dot_15_bar__plus_00_dot_79)\n        (inReceptacle PepperShaker_bar__minus_00_dot_15_bar__plus_01_dot_11_bar__plus_01_dot_41 CounterTop_bar__plus_00_dot_91_bar__plus_01_dot_15_bar__plus_00_dot_79)\n        (inReceptacle Apple_bar__plus_00_dot_28_bar__plus_01_dot_15_bar__plus_01_dot_58 CounterTop_bar__plus_00_dot_91_bar__plus_01_dot_15_bar__plus_00_dot_79)\n        (inReceptacle PepperShaker_bar__minus_00_dot_06_bar__plus_01_dot_11_bar__plus_01_dot_87 CounterTop_bar__plus_00_dot_91_bar__plus_01_dot_15_bar__plus_00_dot_79)\n        (inReceptacle Pot_bar__minus_02_dot_82_bar__plus_00_dot_93_bar__minus_00_dot_40 StoveBurner_bar__minus_02_dot_62_bar__plus_00_dot_89_bar__minus_00_dot_40)\n        (inReceptacle Spoon_bar__minus_02_dot_59_bar__plus_00_dot_75_bar__plus_00_dot_16 Drawer_bar__minus_02_dot_57_bar__plus_00_dot_78_bar__plus_00_dot_10)\n        (inReceptacle Pot_bar__minus_02_dot_82_bar__plus_00_dot_93_bar__minus_00_dot_40 StoveBurner_bar__minus_02_dot_82_bar__plus_00_dot_89_bar__minus_00_dot_40)\n        (inReceptacle Spoon_bar__plus_00_dot_92_bar__plus_00_dot_91_bar__minus_00_dot_95 CounterTop_bar__plus_00_dot_07_bar__plus_00_dot_95_bar__minus_01_dot_20)\n        (inReceptacle Plate_bar__plus_01_dot_00_bar__plus_00_dot_91_bar__minus_01_dot_20 CounterTop_bar__plus_00_dot_07_bar__plus_00_dot_95_bar__minus_01_dot_20)\n        (inReceptacle DishSponge_bar__minus_00_dot_34_bar__plus_00_dot_91_bar__minus_01_dot_37 CounterTop_bar__plus_00_dot_07_bar__plus_00_dot_95_bar__minus_01_dot_20)\n        (inReceptacle DishSponge_bar__minus_02_dot_54_bar__plus_00_dot_91_bar__minus_01_dot_37 CounterTop_bar__plus_00_dot_07_bar__plus_00_dot_95_bar__minus_01_dot_20)\n        (inReceptacle SoapBottle_bar__minus_00_dot_75_bar__plus_00_dot_90_bar__minus_01_dot_29 CounterTop_bar__plus_00_dot_07_bar__plus_00_dot_95_bar__minus_01_dot_20)\n        (inReceptacle Pan_bar__minus_02_dot_59_bar__plus_00_dot_90_bar__minus_01_dot_15 CounterTop_bar__plus_00_dot_07_bar__plus_00_dot_95_bar__minus_01_dot_20)\n        (inReceptacle Plate_bar__minus_02_dot_24_bar__plus_00_dot_91_bar__minus_01_dot_20 CounterTop_bar__plus_00_dot_07_bar__plus_00_dot_95_bar__minus_01_dot_20)\n        (inReceptacle Potato_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_01_dot_12 CounterTop_bar__plus_00_dot_07_bar__plus_00_dot_95_bar__minus_01_dot_20)\n        (inReceptacle Spoon_bar__plus_00_dot_61_bar__plus_00_dot_91_bar__minus_01_dot_28 CounterTop_bar__plus_00_dot_07_bar__plus_00_dot_95_bar__minus_01_dot_20)\n        (inReceptacle ButterKnife_bar__minus_02_dot_22_bar__plus_00_dot_90_bar__minus_01_dot_00 CounterTop_bar__plus_00_dot_07_bar__plus_00_dot_95_bar__minus_01_dot_20)\n        (inReceptacle CreditCard_bar__plus_01_dot_00_bar__plus_00_dot_90_bar__minus_00_dot_98 CounterTop_bar__plus_00_dot_07_bar__plus_00_dot_95_bar__minus_01_dot_20)\n        (inReceptacle DishSponge_bar__minus_01_dot_42_bar__plus_00_dot_85_bar__minus_01_dot_25 Sink_bar__minus_01_dot_33_bar__plus_00_dot_92_bar__minus_01_dot_23_bar_SinkBasin)\n        (inReceptacle Fork_bar__minus_01_dot_51_bar__plus_00_dot_85_bar__minus_01_dot_31 Sink_bar__minus_01_dot_33_bar__plus_00_dot_92_bar__minus_01_dot_23_bar_SinkBasin)\n        (inReceptacle Cup_bar__minus_01_dot_12_bar__plus_00_dot_84_bar__minus_01_dot_24 Sink_bar__minus_01_dot_33_bar__plus_00_dot_92_bar__minus_01_dot_23_bar_SinkBasin)\n        (inReceptacle Egg_bar__minus_02_dot_70_bar__plus_00_dot_07_bar__plus_01_dot_57 GarbageCan_bar__minus_02_dot_76_bar__plus_00_dot_00_bar__plus_01_dot_47)\n        (inReceptacle Tomato_bar__minus_02_dot_73_bar__plus_00_dot_09_bar__plus_01_dot_43 GarbageCan_bar__minus_02_dot_76_bar__plus_00_dot_00_bar__plus_01_dot_47)\n        (inReceptacle Egg_bar__minus_02_dot_76_bar__plus_00_dot_07_bar__plus_01_dot_57 GarbageCan_bar__minus_02_dot_76_bar__plus_00_dot_00_bar__plus_01_dot_47)\n        (inReceptacle Bowl_bar__minus_02_dot_86_bar__plus_00_dot_38_bar__plus_00_dot_69 Fridge_bar__minus_02_dot_86_bar__plus_00_dot_00_bar__plus_00_dot_85)\n        (inReceptacle Bread_bar__minus_02_dot_74_bar__plus_01_dot_53_bar__plus_00_dot_93 Fridge_bar__minus_02_dot_86_bar__plus_00_dot_00_bar__plus_00_dot_85)\n        (inReceptacle Bowl_bar__minus_02_dot_86_bar__plus_00_dot_82_bar__plus_00_dot_77 Fridge_bar__minus_02_dot_86_bar__plus_00_dot_00_bar__plus_00_dot_85)\n        (inReceptacle Lettuce_bar__minus_02_dot_66_bar__plus_00_dot_87_bar__plus_00_dot_98 Fridge_bar__minus_02_dot_86_bar__plus_00_dot_00_bar__plus_00_dot_85)\n        (inReceptacle Mug_bar__plus_00_dot_40_bar__plus_01_dot_00_bar__minus_01_dot_41 Microwave_bar__plus_00_dot_28_bar__plus_00_dot_90_bar__minus_01_dot_33)\n        (inReceptacle Mug_bar__plus_00_dot_24_bar__plus_01_dot_00_bar__minus_01_dot_38 Microwave_bar__plus_00_dot_28_bar__plus_00_dot_90_bar__minus_01_dot_33)\n        (inReceptacle Egg_bar__plus_00_dot_35_bar__plus_01_dot_04_bar__minus_01_dot_25 Microwave_bar__plus_00_dot_28_bar__plus_00_dot_90_bar__minus_01_dot_33)\n        \n        \n        (receptacleAtLocation Cabinet_bar__plus_00_dot_17_bar__plus_02_dot_01_bar__minus_01_dot_20 loc_bar_0_bar__minus_2_bar_2_bar__minus_30)\n        (receptacleAtLocation Cabinet_bar__plus_00_dot_24_bar__plus_02_dot_01_bar__minus_01_dot_20 loc_bar_2_bar__minus_2_bar_2_bar__minus_30)\n        (receptacleAtLocation Cabinet_bar__plus_00_dot_48_bar__plus_00_dot_39_bar__minus_00_dot_90 loc_bar__minus_1_bar_0_bar_2_bar_60)\n        (receptacleAtLocation Cabinet_bar__plus_01_dot_12_bar__plus_02_dot_01_bar__minus_01_dot_20 loc_bar_2_bar__minus_2_bar_2_bar__minus_30)\n        (receptacleAtLocation Cabinet_bar__plus_01_dot_13_bar__plus_00_dot_47_bar__minus_00_dot_90 loc_bar_2_bar_0_bar_2_bar_60)\n        (receptacleAtLocation Cabinet_bar__minus_00_dot_77_bar__plus_02_dot_01_bar__minus_01_dot_20 loc_bar__minus_3_bar__minus_2_bar_2_bar__minus_30)\n        (receptacleAtLocation Cabinet_bar__minus_00_dot_82_bar__plus_00_dot_47_bar__minus_00_dot_91 loc_bar__minus_2_bar_0_bar_2_bar_45)\n        (receptacleAtLocation Cabinet_bar__minus_00_dot_84_bar__plus_02_dot_01_bar__minus_01_dot_20 loc_bar__minus_4_bar__minus_2_bar_2_bar__minus_30)\n        (receptacleAtLocation Cabinet_bar__minus_01_dot_77_bar__plus_02_dot_01_bar__minus_01_dot_20 loc_bar__minus_6_bar__minus_2_bar_2_bar__minus_30)\n        (receptacleAtLocation Cabinet_bar__minus_01_dot_80_bar__plus_00_dot_47_bar__minus_00_dot_91 loc_bar__minus_8_bar_0_bar_2_bar_45)\n        (receptacleAtLocation Cabinet_bar__minus_01_dot_84_bar__plus_02_dot_01_bar__minus_01_dot_20 loc_bar__minus_8_bar__minus_2_bar_2_bar__minus_30)\n        (receptacleAtLocation Cabinet_bar__minus_01_dot_85_bar__plus_00_dot_39_bar__minus_00_dot_90 loc_bar__minus_8_bar__minus_1_bar_2_bar_60)\n        (receptacleAtLocation Cabinet_bar__minus_02_dot_39_bar__plus_00_dot_39_bar__plus_00_dot_38 loc_bar__minus_6_bar_1_bar_3_bar_60)\n        (receptacleAtLocation Cabinet_bar__minus_02_dot_63_bar__plus_02_dot_01_bar__minus_01_dot_20 loc_bar__minus_8_bar__minus_1_bar_2_bar__minus_15)\n        (receptacleAtLocation Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_01_bar__plus_00_dot_36 loc_bar__minus_8_bar_0_bar_3_bar__minus_30)\n        (receptacleAtLocation Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_01_bar__minus_00_dot_95 loc_bar__minus_6_bar__minus_2_bar_3_bar__minus_15)\n        (receptacleAtLocation Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_08_bar__minus_00_dot_23 loc_bar__minus_8_bar__minus_2_bar_3_bar__minus_30)\n        (receptacleAtLocation Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_08_bar__minus_00_dot_88 loc_bar__minus_8_bar__minus_2_bar_3_bar__minus_30)\n        (receptacleAtLocation Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_20_bar__plus_00_dot_43 loc_bar__minus_6_bar_1_bar_3_bar__minus_30)\n        (receptacleAtLocation Cabinet_bar__minus_02_dot_65_bar__plus_02_dot_20_bar__plus_01_dot_67 loc_bar__minus_6_bar_7_bar_3_bar__minus_30)\n        (receptacleAtLocation CoffeeMachine_bar__plus_01_dot_14_bar__plus_01_dot_10_bar__plus_00_dot_30 loc_bar_2_bar_0_bar_1_bar_45)\n        (receptacleAtLocation CounterTop_bar__plus_00_dot_07_bar__plus_00_dot_95_bar__minus_01_dot_20 loc_bar_2_bar__minus_2_bar_2_bar_45)\n        (receptacleAtLocation CounterTop_bar__plus_00_dot_91_bar__plus_01_dot_15_bar__plus_00_dot_79 loc_bar__minus_2_bar_6_bar_1_bar_15)\n        (receptacleAtLocation CounterTop_bar__minus_02_dot_70_bar__plus_00_dot_95_bar__plus_00_dot_11 loc_bar__minus_8_bar_0_bar_3_bar_45)\n        (receptacleAtLocation Drawer_bar__plus_00_dot_26_bar__plus_00_dot_78_bar__minus_01_dot_08 loc_bar_2_bar_0_bar_2_bar_45)\n        (receptacleAtLocation Drawer_bar__minus_02_dot_03_bar__plus_00_dot_78_bar__minus_01_dot_08 loc_bar__minus_8_bar__minus_2_bar_2_bar_60)\n        (receptacleAtLocation Drawer_bar__minus_02_dot_57_bar__plus_00_dot_78_bar__plus_00_dot_10 loc_bar__minus_6_bar__minus_1_bar_3_bar_45)\n        (receptacleAtLocation Fridge_bar__minus_02_dot_86_bar__plus_00_dot_00_bar__plus_00_dot_85 loc_bar__minus_7_bar_3_bar_3_bar_60)\n        (receptacleAtLocation GarbageCan_bar__minus_02_dot_76_bar__plus_00_dot_00_bar__plus_01_dot_47 loc_bar__minus_8_bar_6_bar_3_bar_60)\n        (receptacleAtLocation Microwave_bar__plus_00_dot_28_bar__plus_00_dot_90_bar__minus_01_dot_33 loc_bar_1_bar__minus_2_bar_2_bar_45)\n        (receptacleAtLocation Sink_bar__minus_01_dot_33_bar__plus_00_dot_92_bar__minus_01_dot_23_bar_SinkBasin loc_bar__minus_5_bar__minus_2_bar_2_bar_45)\n        (receptacleAtLocation StoveBurner_bar__minus_02_dot_62_bar__plus_00_dot_89_bar__minus_00_dot_40 loc_bar__minus_8_bar__minus_2_bar_3_bar_45)\n        (receptacleAtLocation StoveBurner_bar__minus_02_dot_62_bar__plus_00_dot_89_bar__minus_00_dot_71 loc_bar__minus_8_bar__minus_2_bar_3_bar_45)\n        (receptacleAtLocation StoveBurner_bar__minus_02_dot_82_bar__plus_00_dot_89_bar__minus_00_dot_40 loc_bar__minus_8_bar__minus_2_bar_3_bar_45)\n        (receptacleAtLocation StoveBurner_bar__minus_02_dot_82_bar__plus_00_dot_89_bar__minus_00_dot_71 loc_bar__minus_8_bar__minus_2_bar_3_bar_45)\n        (receptacleAtLocation Toaster_bar__minus_02_dot_82_bar__plus_00_dot_90_bar__plus_00_dot_25 loc_bar__minus_8_bar_1_bar_3_bar_45)\n        (objectAtLocation Bowl_bar__minus_02_dot_86_bar__plus_00_dot_82_bar__plus_00_dot_77 loc_bar__minus_7_bar_3_bar_3_bar_60)\n        (objectAtLocation Mug_bar__plus_00_dot_40_bar__plus_01_dot_00_bar__minus_01_dot_41 loc_bar_1_bar__minus_2_bar_2_bar_45)\n        (objectAtLocation Spoon_bar__plus_00_dot_92_bar__plus_00_dot_91_bar__minus_00_dot_95 loc_bar_2_bar__minus_2_bar_2_bar_45)\n        (objectAtLocation Egg_bar__plus_00_dot_35_bar__plus_01_dot_04_bar__minus_01_dot_25 loc_bar_1_bar__minus_2_bar_2_bar_45)\n        (objectAtLocation DishSponge_bar__minus_01_dot_42_bar__plus_00_dot_85_bar__minus_01_dot_25 loc_bar__minus_5_bar__minus_2_bar_2_bar_45)\n        (objectAtLocation Potato_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_01_dot_12 loc_bar_2_bar__minus_2_bar_2_bar_45)\n        (objectAtLocation PepperShaker_bar__minus_00_dot_15_bar__plus_01_dot_11_bar__plus_01_dot_41 loc_bar__minus_2_bar_6_bar_1_bar_15)\n        (objectAtLocation Tomato_bar__minus_02_dot_73_bar__plus_00_dot_09_bar__plus_01_dot_43 loc_bar__minus_8_bar_6_bar_3_bar_60)\n        (objectAtLocation SaltShaker_bar__minus_01_dot_00_bar__plus_00_dot_11_bar__minus_00_dot_99 loc_bar__minus_2_bar_0_bar_2_bar_45)\n        (objectAtLocation Plate_bar__minus_02_dot_24_bar__plus_00_dot_91_bar__minus_01_dot_20 loc_bar_2_bar__minus_2_bar_2_bar_45)\n        (objectAtLocation Fork_bar__minus_01_dot_51_bar__plus_00_dot_85_bar__minus_01_dot_31 loc_bar__minus_5_bar__minus_2_bar_2_bar_45)\n        (objectAtLocation Mug_bar__minus_02_dot_44_bar__plus_00_dot_91_bar__minus_00_dot_06 loc_bar__minus_8_bar_0_bar_3_bar_45)\n        (objectAtLocation PepperShaker_bar__minus_00_dot_06_bar__plus_01_dot_11_bar__plus_01_dot_87 loc_bar__minus_2_bar_6_bar_1_bar_15)\n        (objectAtLocation Bowl_bar__minus_01_dot_57_bar__plus_00_dot_12_bar__minus_01_dot_07 loc_bar__minus_8_bar_0_bar_2_bar_45)\n        (objectAtLocation Egg_bar__minus_02_dot_76_bar__plus_00_dot_07_bar__plus_01_dot_57 loc_bar__minus_8_bar_6_bar_3_bar_60)\n        (objectAtLocation Spoon_bar__plus_00_dot_61_bar__plus_00_dot_91_bar__minus_01_dot_28 loc_bar_2_bar__minus_2_bar_2_bar_45)\n        (objectAtLocation DishSponge_bar__minus_02_dot_54_bar__plus_00_dot_91_bar__minus_01_dot_37 loc_bar_2_bar__minus_2_bar_2_bar_45)\n        (objectAtLocation Sink_bar__minus_01_dot_33_bar__plus_00_dot_92_bar__minus_01_dot_23 loc_bar__minus_5_bar__minus_2_bar_2_bar_45)\n        (objectAtLocation Chair_bar__plus_01_dot_07_bar__plus_00_dot_01_bar__plus_01_dot_56 loc_bar_4_bar_8_bar_2_bar_60)\n        (objectAtLocation Chair_bar__plus_01_dot_56_bar__plus_00_dot_01_bar__plus_01_dot_36 loc_bar_6_bar_7_bar_2_bar_60)\n        (objectAtLocation Pan_bar__minus_02_dot_59_bar__plus_00_dot_90_bar__minus_01_dot_15 loc_bar_2_bar__minus_2_bar_2_bar_45)\n        (objectAtLocation Apple_bar__plus_00_dot_28_bar__plus_01_dot_15_bar__plus_01_dot_58 loc_bar__minus_2_bar_6_bar_1_bar_15)\n        (objectAtLocation LightSwitch_bar__minus_01_dot_17_bar__plus_01_dot_31_bar__plus_03_dot_00 loc_bar__minus_5_bar_10_bar_0_bar_30)\n        (objectAtLocation StoveKnob_bar__minus_02_dot_43_bar__plus_00_dot_86_bar__minus_00_dot_62 loc_bar__minus_8_bar__minus_2_bar_3_bar_60)\n        (objectAtLocation StoveKnob_bar__minus_02_dot_43_bar__plus_00_dot_86_bar__minus_00_dot_48 loc_bar__minus_8_bar__minus_2_bar_3_bar_60)\n        (objectAtLocation StoveKnob_bar__minus_02_dot_43_bar__plus_00_dot_86_bar__minus_00_dot_78 loc_bar__minus_8_bar__minus_2_bar_3_bar_60)\n        (objectAtLocation StoveKnob_bar__minus_02_dot_43_bar__plus_00_dot_86_bar__minus_00_dot_32 loc_bar__minus_8_bar__minus_1_bar_3_bar_60)\n        (objectAtLocation Fork_bar__minus_01_dot_91_bar__plus_00_dot_75_bar__minus_01_dot_09 loc_bar__minus_8_bar__minus_2_bar_2_bar_60)\n        (objectAtLocation Kettle_bar__minus_02_dot_62_bar__plus_00_dot_92_bar__minus_00_dot_72 loc_bar__minus_8_bar__minus_2_bar_3_bar_45)\n        (objectAtLocation CreditCard_bar__plus_01_dot_00_bar__plus_00_dot_90_bar__minus_00_dot_98 loc_bar_2_bar__minus_2_bar_2_bar_45)\n        (objectAtLocation Bread_bar__minus_02_dot_74_bar__plus_01_dot_53_bar__plus_00_dot_93 loc_bar__minus_7_bar_3_bar_3_bar_60)\n        (objectAtLocation Plate_bar__plus_01_dot_00_bar__plus_00_dot_91_bar__minus_01_dot_20 loc_bar_2_bar__minus_2_bar_2_bar_45)\n        (objectAtLocation SoapBottle_bar__minus_00_dot_75_bar__plus_00_dot_90_bar__minus_01_dot_29 loc_bar_2_bar__minus_2_bar_2_bar_45)\n        (objectAtLocation Window_bar__plus_00_dot_32_bar__plus_01_dot_45_bar__plus_03_dot_09 loc_bar_1_bar_10_bar_0_bar_15)\n        (objectAtLocation Spatula_bar__minus_02_dot_55_bar__plus_00_dot_92_bar__plus_00_dot_01 loc_bar__minus_8_bar_0_bar_3_bar_45)\n        (objectAtLocation Lettuce_bar__minus_02_dot_66_bar__plus_00_dot_87_bar__plus_00_dot_98 loc_bar__minus_7_bar_3_bar_3_bar_60)\n        (objectAtLocation SaltShaker_bar__minus_00_dot_66_bar__plus_01_dot_66_bar__minus_01_dot_32 loc_bar__minus_3_bar__minus_2_bar_2_bar__minus_30)\n        (objectAtLocation Tomato_bar__plus_00_dot_55_bar__plus_01_dot_16_bar__plus_01_dot_48 loc_bar__minus_2_bar_6_bar_1_bar_15)\n        (objectAtLocation PepperShaker_bar__minus_01_dot_12_bar__plus_00_dot_11_bar__minus_01_dot_07 loc_bar__minus_2_bar_0_bar_2_bar_45)\n        (objectAtLocation ButterKnife_bar__minus_02_dot_22_bar__plus_00_dot_90_bar__minus_01_dot_00 loc_bar_2_bar__minus_2_bar_2_bar_45)\n        (objectAtLocation Pot_bar__minus_02_dot_82_bar__plus_00_dot_93_bar__minus_00_dot_40 loc_bar__minus_8_bar__minus_2_bar_3_bar_45)\n        (objectAtLocation Potato_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_01_dot_12 loc_bar_2_bar__minus_2_bar_2_bar_45)\n        (objectAtLocation DishSponge_bar__minus_00_dot_34_bar__plus_00_dot_91_bar__minus_01_dot_37 loc_bar_2_bar__minus_2_bar_2_bar_45)\n        (objectAtLocation Egg_bar__minus_02_dot_70_bar__plus_00_dot_07_bar__plus_01_dot_57 loc_bar__minus_8_bar_6_bar_3_bar_60)\n        (objectAtLocation Spoon_bar__minus_02_dot_59_bar__plus_00_dot_75_bar__plus_00_dot_16 loc_bar__minus_6_bar__minus_1_bar_3_bar_45)\n        (objectAtLocation Knife_bar__plus_00_dot_06_bar__plus_01_dot_11_bar__plus_01_dot_59 loc_bar__minus_2_bar_6_bar_1_bar_15)\n        (objectAtLocation Cup_bar__minus_01_dot_12_bar__plus_00_dot_84_bar__minus_01_dot_24 loc_bar__minus_5_bar__minus_2_bar_2_bar_45)\n        (objectAtLocation Mug_bar__plus_00_dot_24_bar__plus_01_dot_00_bar__minus_01_dot_38 loc_bar_1_bar__minus_2_bar_2_bar_45)\n        (objectAtLocation Bowl_bar__minus_02_dot_86_bar__plus_00_dot_38_bar__plus_00_dot_69 loc_bar__minus_7_bar_3_bar_3_bar_60)\n        )\n    \n\n        (:goal\n            (and\n                (exists (?r - receptacle)\n                    (exists (?o - object)\n                        (and\n                            (heatable ?o)\n                            (objectType ?o CupType)\n                            (receptacleType ?r CabinetType)\n                            (isHot ?o)\n                            (inReceptacle ?o ?r)\n                        )\n                    )\n                )\n            )\n        )\n    )\n    ", "solvable": true, "walkthrough": ["go to sinkbasin 1", "take cup 1 from sinkbasin 1", "go to microwave 1", "heat cup 1 with microwave 1", "go to cabinet 5", "open cabinet 5", "move cup 1 to cabinet 5"]}