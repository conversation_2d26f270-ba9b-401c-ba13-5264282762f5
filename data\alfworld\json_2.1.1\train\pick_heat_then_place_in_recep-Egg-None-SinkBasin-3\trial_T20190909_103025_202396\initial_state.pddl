
(define (problem plan_trial_T20190909_103025_202396)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Apple_bar__minus_01_dot_09_bar__plus_01_dot_37_bar__minus_03_dot_31 - object
        Apple_bar__minus_01_dot_66_bar__plus_00_dot_33_bar__plus_02_dot_28 - object
        Apple_bar__minus_01_dot_95_bar__plus_01_dot_10_bar__minus_00_dot_59 - object
        Bowl_bar__plus_00_dot_97_bar__plus_01_dot_52_bar__plus_01_dot_66 - object
        Bowl_bar__minus_01_dot_67_bar__plus_00_dot_34_bar__minus_02_dot_16 - object
        Bowl_bar__minus_01_dot_75_bar__plus_00_dot_34_bar__plus_00_dot_41 - object
        Bread_bar__plus_00_dot_49_bar__plus_01_dot_40_bar__minus_03_dot_12 - object
        Bread_bar__plus_00_dot_90_bar__plus_01_dot_40_bar__minus_02_dot_79 - object
        Chair_bar__plus_00_dot_60_bar__plus_00_dot_95_bar__minus_01_dot_09 - object
        Cup_bar__minus_01_dot_40_bar__plus_01_dot_32_bar__minus_02_dot_20 - object
        Cup_bar__minus_02_dot_18_bar__plus_01_dot_32_bar__minus_02_dot_20 - object
        DishSponge_bar__plus_00_dot_90_bar__plus_00_dot_46_bar__plus_00_dot_71 - object
        Egg_bar__plus_01_dot_02_bar__plus_01_dot_72_bar__plus_01_dot_72 - object
        Faucet_bar__minus_02_dot_06_bar__plus_01_dot_36_bar__minus_00_dot_73 - object
        Fork_bar__minus_01_dot_40_bar__plus_01_dot_32_bar__plus_02_dot_07 - object
        Fork_bar__minus_01_dot_53_bar__plus_01_dot_32_bar__minus_02_dot_46 - object
        HousePlant_bar__minus_02_dot_03_bar__plus_01_dot_31_bar__minus_00_dot_03 - object
        Kettle_bar__plus_00_dot_77_bar__plus_01_dot_32_bar__minus_00_dot_94 - object
        Knife_bar__minus_01_dot_31_bar__plus_01_dot_35_bar__minus_02_dot_87 - object
        Knife_bar__minus_01_dot_83_bar__plus_01_dot_36_bar__minus_00_dot_98 - object
        Lettuce_bar__minus_01_dot_89_bar__plus_01_dot_40_bar__minus_01_dot_09 - object
        LightSwitch_bar__plus_01_dot_39_bar__plus_01_dot_57_bar__minus_00_dot_66 - object
        Mug_bar__minus_02_dot_09_bar__plus_01_dot_33_bar__minus_00_dot_98 - object
        Pan_bar__minus_00_dot_63_bar__plus_01_dot_33_bar__minus_03_dot_30 - object
        Pan_bar__minus_02_dot_05_bar__plus_01_dot_32_bar__minus_02_dot_98 - object
        PaperTowelRoll_bar__minus_01_dot_35_bar__plus_01_dot_43_bar__minus_01_dot_50 - object
        PepperShaker_bar__plus_01_dot_03_bar__plus_01_dot_20_bar__plus_01_dot_22 - object
        PepperShaker_bar__plus_01_dot_19_bar__plus_01_dot_20_bar__plus_01_dot_22 - object
        PepperShaker_bar__minus_01_dot_92_bar__plus_01_dot_32_bar__minus_01_dot_68 - object
        Plate_bar__plus_00_dot_82_bar__plus_00_dot_35_bar__minus_01_dot_96 - object
        Plate_bar__plus_00_dot_90_bar__plus_00_dot_35_bar__minus_02_dot_27 - object
        Plate_bar__minus_01_dot_53_bar__plus_01_dot_32_bar__minus_01_dot_94 - object
        Potato_bar__plus_00_dot_98_bar__plus_01_dot_80_bar__plus_02_dot_23 - object
        Potato_bar__plus_01_dot_06_bar__plus_01_dot_80_bar__plus_01_dot_99 - object
        Potato_bar__minus_01_dot_83_bar__plus_01_dot_10_bar__minus_00_dot_50 - object
        Pot_bar__minus_00_dot_13_bar__plus_01_dot_33_bar__minus_02_dot_97 - object
        SaltShaker_bar__plus_00_dot_64_bar__plus_01_dot_32_bar__minus_02_dot_05 - object
        Sink_bar__minus_01_dot_99_bar__plus_01_dot_14_bar__minus_00_dot_98 - object
        SoapBottle_bar__plus_00_dot_56_bar__plus_01_dot_32_bar__minus_03_dot_34 - object
        SoapBottle_bar__minus_01_dot_35_bar__plus_01_dot_32_bar__minus_01_dot_04 - object
        Spatula_bar__plus_00_dot_77_bar__plus_01_dot_34_bar__minus_02_dot_42 - object
        Spatula_bar__minus_01_dot_17_bar__plus_01_dot_34_bar__minus_03_dot_31 - object
        Spoon_bar__plus_00_dot_28_bar__plus_01_dot_32_bar__minus_02_dot_90 - object
        Spoon_bar__plus_00_dot_86_bar__plus_01_dot_21_bar__plus_01_dot_22 - object
        Spoon_bar__minus_01_dot_79_bar__plus_01_dot_32_bar__plus_01_dot_42 - object
        StoveKnob_bar__minus_00_dot_19_bar__plus_01_dot_34_bar__minus_02_dot_74 - object
        StoveKnob_bar__minus_00_dot_34_bar__plus_01_dot_34_bar__minus_02_dot_74 - object
        StoveKnob_bar__minus_00_dot_57_bar__plus_01_dot_34_bar__minus_02_dot_74 - object
        StoveKnob_bar__minus_00_dot_71_bar__plus_01_dot_34_bar__minus_02_dot_74 - object
        Tomato_bar__plus_01_dot_02_bar__plus_01_dot_56_bar__plus_01_dot_48 - object
        Tomato_bar__minus_01_dot_52_bar__plus_01_dot_36_bar__minus_03_dot_20 - object
        Window_bar__minus_02_dot_57_bar__plus_01_dot_80_bar__minus_00_dot_82 - object
        WineBottle_bar__plus_00_dot_28_bar__plus_01_dot_32_bar__minus_03_dot_01 - object
        WineBottle_bar__minus_01_dot_63_bar__plus_00_dot_28_bar__plus_02_dot_11 - object
        WineBottle_bar__minus_01_dot_82_bar__plus_00_dot_34_bar__plus_00_dot_21 - object
        Cabinet_bar__plus_00_dot_58_bar__plus_00_dot_78_bar__minus_02_dot_05 - receptacle
        Cabinet_bar__minus_01_dot_46_bar__plus_00_dot_78_bar__plus_00_dot_47 - receptacle
        Cabinet_bar__minus_01_dot_46_bar__plus_00_dot_78_bar__plus_01_dot_31 - receptacle
        Cabinet_bar__minus_01_dot_46_bar__plus_00_dot_78_bar__minus_02_dot_00 - receptacle
        CoffeeMachine_bar__plus_01_dot_13_bar__plus_01_dot_31_bar__minus_01_dot_43 - receptacle
        CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18 - receptacle
        Drawer_bar__plus_00_dot_71_bar__plus_00_dot_85_bar__plus_01_dot_02 - receptacle
        Drawer_bar__plus_00_dot_71_bar__plus_01_dot_06_bar__plus_01_dot_02 - receptacle
        Drawer_bar__plus_00_dot_72_bar__plus_00_dot_59_bar__plus_00_dot_68 - receptacle
        Drawer_bar__plus_00_dot_72_bar__plus_00_dot_59_bar__plus_01_dot_02 - receptacle
        Drawer_bar__plus_00_dot_72_bar__plus_00_dot_84_bar__plus_00_dot_68 - receptacle
        Drawer_bar__plus_00_dot_72_bar__plus_01_dot_06_bar__plus_00_dot_68 - receptacle
        Drawer_bar__minus_01_dot_61_bar__plus_00_dot_68_bar__minus_00_dot_43 - receptacle
        Drawer_bar__minus_01_dot_61_bar__plus_00_dot_68_bar__minus_01_dot_22 - receptacle
        Fridge_bar__plus_01_dot_01_bar__plus_00_dot_23_bar__plus_01_dot_92 - receptacle
        GarbageCan_bar__minus_01_dot_63_bar__plus_00_dot_22_bar__plus_02_dot_19 - receptacle
        Microwave_bar__plus_01_dot_16_bar__plus_01_dot_19_bar__plus_00_dot_85 - receptacle
        SideTable_bar__plus_01_dot_02_bar__plus_01_dot_24_bar__plus_00_dot_87 - receptacle
        Sink_bar__minus_01_dot_99_bar__plus_01_dot_14_bar__minus_00_dot_98_bar_SinkBasin - receptacle
        StoveBurner_bar__minus_00_dot_13_bar__plus_01_dot_33_bar__minus_02_dot_97 - receptacle
        StoveBurner_bar__minus_00_dot_24_bar__plus_01_dot_33_bar__minus_03_dot_30 - receptacle
        StoveBurner_bar__minus_00_dot_63_bar__plus_01_dot_33_bar__minus_03_dot_30 - receptacle
        StoveBurner_bar__minus_00_dot_75_bar__plus_01_dot_33_bar__minus_02_dot_97 - receptacle
        Toaster_bar__minus_01_dot_89_bar__plus_01_dot_31_bar__minus_02_dot_51 - receptacle
        loc_bar__minus_4_bar__minus_2_bar_3_bar_60 - location
        loc_bar__minus_4_bar__minus_5_bar_3_bar_60 - location
        loc_bar_0_bar_1_bar_1_bar_60 - location
        loc_bar__minus_1_bar_0_bar_1_bar_60 - location
        loc_bar__minus_2_bar_7_bar_3_bar_45 - location
        loc_bar_0_bar_3_bar_1_bar_45 - location
        loc_bar__minus_2_bar__minus_6_bar_3_bar_45 - location
        loc_bar__minus_4_bar__minus_3_bar_3_bar_0 - location
        loc_bar__minus_3_bar__minus_9_bar_2_bar_45 - location
        loc_bar__minus_4_bar__minus_9_bar_3_bar_30 - location
        loc_bar_1_bar_3_bar_1_bar_30 - location
        loc_bar__minus_1_bar__minus_9_bar_2_bar_30 - location
        loc_bar__minus_1_bar__minus_9_bar_2_bar_45 - location
        loc_bar__minus_2_bar__minus_9_bar_2_bar_45 - location
        loc_bar_1_bar_4_bar_1_bar_45 - location
        loc_bar__minus_3_bar__minus_9_bar_2_bar_30 - location
        loc_bar_3_bar__minus_1_bar_1_bar_30 - location
        loc_bar_0_bar_6_bar_1_bar_60 - location
        loc_bar__minus_4_bar_5_bar_0_bar_60 - location
        loc_bar__minus_2_bar_0_bar_3_bar_45 - location
        loc_bar__minus_4_bar__minus_4_bar_3_bar_30 - location
        loc_bar__minus_1_bar_2_bar_1_bar_60 - location
        loc_bar_0_bar_1_bar_1_bar_45 - location
        loc_bar_0_bar_8_bar_1_bar_60 - location
        loc_bar__minus_1_bar__minus_9_bar_1_bar_60 - location
        loc_bar_0_bar__minus_4_bar_1_bar_60 - location
        loc_bar_0_bar__minus_6_bar_1_bar_30 - location
        loc_bar__minus_4_bar__minus_3_bar_3_bar_45 - location
        loc_bar__minus_4_bar_5_bar_3_bar_30 - location
        loc_bar_1_bar_4_bar_1_bar_30 - location
        )
    

(:init


        (receptacleType Drawer_bar__plus_00_dot_71_bar__plus_01_dot_06_bar__plus_01_dot_02 DrawerType)
        (receptacleType Toaster_bar__minus_01_dot_89_bar__plus_01_dot_31_bar__minus_02_dot_51 ToasterType)
        (receptacleType SideTable_bar__plus_01_dot_02_bar__plus_01_dot_24_bar__plus_00_dot_87 SideTableType)
        (receptacleType Cabinet_bar__minus_01_dot_46_bar__plus_00_dot_78_bar__plus_00_dot_47 CabinetType)
        (receptacleType Cabinet_bar__plus_00_dot_58_bar__plus_00_dot_78_bar__minus_02_dot_05 CabinetType)
        (receptacleType GarbageCan_bar__minus_01_dot_63_bar__plus_00_dot_22_bar__plus_02_dot_19 GarbageCanType)
        (receptacleType StoveBurner_bar__minus_00_dot_24_bar__plus_01_dot_33_bar__minus_03_dot_30 StoveBurnerType)
        (receptacleType StoveBurner_bar__minus_00_dot_75_bar__plus_01_dot_33_bar__minus_02_dot_97 StoveBurnerType)
        (receptacleType Microwave_bar__plus_01_dot_16_bar__plus_01_dot_19_bar__plus_00_dot_85 MicrowaveType)
        (receptacleType StoveBurner_bar__minus_00_dot_63_bar__plus_01_dot_33_bar__minus_03_dot_30 StoveBurnerType)
        (receptacleType Drawer_bar__minus_01_dot_61_bar__plus_00_dot_68_bar__minus_01_dot_22 DrawerType)
        (receptacleType Fridge_bar__plus_01_dot_01_bar__plus_00_dot_23_bar__plus_01_dot_92 FridgeType)
        (receptacleType Drawer_bar__minus_01_dot_61_bar__plus_00_dot_68_bar__minus_00_dot_43 DrawerType)
        (receptacleType Cabinet_bar__minus_01_dot_46_bar__plus_00_dot_78_bar__minus_02_dot_00 CabinetType)
        (receptacleType Drawer_bar__plus_00_dot_71_bar__plus_00_dot_85_bar__plus_01_dot_02 DrawerType)
        (receptacleType Cabinet_bar__minus_01_dot_46_bar__plus_00_dot_78_bar__plus_01_dot_31 CabinetType)
        (receptacleType Drawer_bar__plus_00_dot_72_bar__plus_00_dot_59_bar__plus_00_dot_68 DrawerType)
        (receptacleType Drawer_bar__plus_00_dot_72_bar__plus_00_dot_59_bar__plus_01_dot_02 DrawerType)
        (receptacleType CoffeeMachine_bar__plus_01_dot_13_bar__plus_01_dot_31_bar__minus_01_dot_43 CoffeeMachineType)
        (receptacleType StoveBurner_bar__minus_00_dot_13_bar__plus_01_dot_33_bar__minus_02_dot_97 StoveBurnerType)
        (receptacleType CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18 CounterTopType)
        (receptacleType Drawer_bar__plus_00_dot_72_bar__plus_00_dot_84_bar__plus_00_dot_68 DrawerType)
        (receptacleType Drawer_bar__plus_00_dot_72_bar__plus_01_dot_06_bar__plus_00_dot_68 DrawerType)
        (receptacleType Sink_bar__minus_01_dot_99_bar__plus_01_dot_14_bar__minus_00_dot_98_bar_SinkBasin SinkBasinType)
        (objectType StoveKnob_bar__minus_00_dot_57_bar__plus_01_dot_34_bar__minus_02_dot_74 StoveKnobType)
        (objectType Tomato_bar__minus_01_dot_52_bar__plus_01_dot_36_bar__minus_03_dot_20 TomatoType)
        (objectType Potato_bar__minus_01_dot_83_bar__plus_01_dot_10_bar__minus_00_dot_50 PotatoType)
        (objectType Potato_bar__plus_00_dot_98_bar__plus_01_dot_80_bar__plus_02_dot_23 PotatoType)
        (objectType DishSponge_bar__plus_00_dot_90_bar__plus_00_dot_46_bar__plus_00_dot_71 DishSpongeType)
        (objectType Pot_bar__minus_00_dot_13_bar__plus_01_dot_33_bar__minus_02_dot_97 PotType)
        (objectType WineBottle_bar__plus_00_dot_28_bar__plus_01_dot_32_bar__minus_03_dot_01 WineBottleType)
        (objectType Fork_bar__minus_01_dot_40_bar__plus_01_dot_32_bar__plus_02_dot_07 ForkType)
        (objectType Lettuce_bar__minus_01_dot_89_bar__plus_01_dot_40_bar__minus_01_dot_09 LettuceType)
        (objectType Kettle_bar__plus_00_dot_77_bar__plus_01_dot_32_bar__minus_00_dot_94 KettleType)
        (objectType Apple_bar__minus_01_dot_09_bar__plus_01_dot_37_bar__minus_03_dot_31 AppleType)
        (objectType Sink_bar__minus_01_dot_99_bar__plus_01_dot_14_bar__minus_00_dot_98 SinkType)
        (objectType Window_bar__minus_02_dot_57_bar__plus_01_dot_80_bar__minus_00_dot_82 WindowType)
        (objectType StoveKnob_bar__minus_00_dot_71_bar__plus_01_dot_34_bar__minus_02_dot_74 StoveKnobType)
        (objectType StoveKnob_bar__minus_00_dot_34_bar__plus_01_dot_34_bar__minus_02_dot_74 StoveKnobType)
        (objectType PepperShaker_bar__minus_01_dot_92_bar__plus_01_dot_32_bar__minus_01_dot_68 PepperShakerType)
        (objectType PepperShaker_bar__plus_01_dot_19_bar__plus_01_dot_20_bar__plus_01_dot_22 PepperShakerType)
        (objectType Plate_bar__minus_01_dot_53_bar__plus_01_dot_32_bar__minus_01_dot_94 PlateType)
        (objectType Fork_bar__minus_01_dot_53_bar__plus_01_dot_32_bar__minus_02_dot_46 ForkType)
        (objectType Tomato_bar__plus_01_dot_02_bar__plus_01_dot_56_bar__plus_01_dot_48 TomatoType)
        (objectType Plate_bar__plus_00_dot_90_bar__plus_00_dot_35_bar__minus_02_dot_27 PlateType)
        (objectType Bread_bar__plus_00_dot_90_bar__plus_01_dot_40_bar__minus_02_dot_79 BreadType)
        (objectType Potato_bar__plus_01_dot_06_bar__plus_01_dot_80_bar__plus_01_dot_99 PotatoType)
        (objectType StoveKnob_bar__minus_00_dot_19_bar__plus_01_dot_34_bar__minus_02_dot_74 StoveKnobType)
        (objectType Knife_bar__minus_01_dot_83_bar__plus_01_dot_36_bar__minus_00_dot_98 KnifeType)
        (objectType SaltShaker_bar__plus_00_dot_64_bar__plus_01_dot_32_bar__minus_02_dot_05 SaltShakerType)
        (objectType PaperTowelRoll_bar__minus_01_dot_35_bar__plus_01_dot_43_bar__minus_01_dot_50 PaperTowelRollType)
        (objectType Plate_bar__plus_00_dot_82_bar__plus_00_dot_35_bar__minus_01_dot_96 PlateType)
        (objectType Pan_bar__minus_02_dot_05_bar__plus_01_dot_32_bar__minus_02_dot_98 PanType)
        (objectType HousePlant_bar__minus_02_dot_03_bar__plus_01_dot_31_bar__minus_00_dot_03 HousePlantType)
        (objectType Chair_bar__plus_00_dot_60_bar__plus_00_dot_95_bar__minus_01_dot_09 ChairType)
        (objectType PepperShaker_bar__plus_01_dot_03_bar__plus_01_dot_20_bar__plus_01_dot_22 PepperShakerType)
        (objectType Spatula_bar__minus_01_dot_17_bar__plus_01_dot_34_bar__minus_03_dot_31 SpatulaType)
        (objectType WineBottle_bar__minus_01_dot_82_bar__plus_00_dot_34_bar__plus_00_dot_21 WineBottleType)
        (objectType Bowl_bar__minus_01_dot_67_bar__plus_00_dot_34_bar__minus_02_dot_16 BowlType)
        (objectType Mug_bar__minus_02_dot_09_bar__plus_01_dot_33_bar__minus_00_dot_98 MugType)
        (objectType Apple_bar__minus_01_dot_66_bar__plus_00_dot_33_bar__plus_02_dot_28 AppleType)
        (objectType Cup_bar__minus_02_dot_18_bar__plus_01_dot_32_bar__minus_02_dot_20 CupType)
        (objectType Spoon_bar__minus_01_dot_79_bar__plus_01_dot_32_bar__plus_01_dot_42 SpoonType)
        (objectType Pan_bar__minus_00_dot_63_bar__plus_01_dot_33_bar__minus_03_dot_30 PanType)
        (objectType SoapBottle_bar__plus_00_dot_56_bar__plus_01_dot_32_bar__minus_03_dot_34 SoapBottleType)
        (objectType SoapBottle_bar__minus_01_dot_35_bar__plus_01_dot_32_bar__minus_01_dot_04 SoapBottleType)
        (objectType Knife_bar__minus_01_dot_31_bar__plus_01_dot_35_bar__minus_02_dot_87 KnifeType)
        (objectType LightSwitch_bar__plus_01_dot_39_bar__plus_01_dot_57_bar__minus_00_dot_66 LightSwitchType)
        (objectType Spoon_bar__plus_00_dot_86_bar__plus_01_dot_21_bar__plus_01_dot_22 SpoonType)
        (objectType Bowl_bar__minus_01_dot_75_bar__plus_00_dot_34_bar__plus_00_dot_41 BowlType)
        (objectType Spatula_bar__plus_00_dot_77_bar__plus_01_dot_34_bar__minus_02_dot_42 SpatulaType)
        (objectType Egg_bar__plus_01_dot_02_bar__plus_01_dot_72_bar__plus_01_dot_72 EggType)
        (objectType Bowl_bar__plus_00_dot_97_bar__plus_01_dot_52_bar__plus_01_dot_66 BowlType)
        (objectType WineBottle_bar__minus_01_dot_63_bar__plus_00_dot_28_bar__plus_02_dot_11 WineBottleType)
        (objectType Cup_bar__minus_01_dot_40_bar__plus_01_dot_32_bar__minus_02_dot_20 CupType)
        (objectType Spoon_bar__plus_00_dot_28_bar__plus_01_dot_32_bar__minus_02_dot_90 SpoonType)
        (objectType Bread_bar__plus_00_dot_49_bar__plus_01_dot_40_bar__minus_03_dot_12 BreadType)
        (objectType Apple_bar__minus_01_dot_95_bar__plus_01_dot_10_bar__minus_00_dot_59 AppleType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain SideTableType SaltShakerType)
        (canContain SideTableType BreadType)
        (canContain SideTableType DishSpongeType)
        (canContain SideTableType BowlType)
        (canContain SideTableType KettleType)
        (canContain SideTableType PotType)
        (canContain SideTableType WineBottleType)
        (canContain SideTableType MugType)
        (canContain SideTableType EggType)
        (canContain SideTableType ForkType)
        (canContain SideTableType SpoonType)
        (canContain SideTableType SoapBottleType)
        (canContain SideTableType LettuceType)
        (canContain SideTableType PotatoType)
        (canContain SideTableType CupType)
        (canContain SideTableType PlateType)
        (canContain SideTableType PepperShakerType)
        (canContain SideTableType TomatoType)
        (canContain SideTableType KnifeType)
        (canContain SideTableType AppleType)
        (canContain SideTableType PanType)
        (canContain SideTableType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain GarbageCanType BreadType)
        (canContain GarbageCanType DishSpongeType)
        (canContain GarbageCanType WineBottleType)
        (canContain GarbageCanType EggType)
        (canContain GarbageCanType SoapBottleType)
        (canContain GarbageCanType LettuceType)
        (canContain GarbageCanType PotatoType)
        (canContain GarbageCanType TomatoType)
        (canContain GarbageCanType AppleType)
        (canContain StoveBurnerType KettleType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain StoveBurnerType KettleType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain MicrowaveType MugType)
        (canContain MicrowaveType PotatoType)
        (canContain MicrowaveType EggType)
        (canContain MicrowaveType AppleType)
        (canContain MicrowaveType TomatoType)
        (canContain MicrowaveType BreadType)
        (canContain MicrowaveType CupType)
        (canContain MicrowaveType PlateType)
        (canContain MicrowaveType BowlType)
        (canContain StoveBurnerType KettleType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain FridgeType BreadType)
        (canContain FridgeType BowlType)
        (canContain FridgeType PotType)
        (canContain FridgeType WineBottleType)
        (canContain FridgeType MugType)
        (canContain FridgeType EggType)
        (canContain FridgeType LettuceType)
        (canContain FridgeType PotatoType)
        (canContain FridgeType CupType)
        (canContain FridgeType PlateType)
        (canContain FridgeType TomatoType)
        (canContain FridgeType AppleType)
        (canContain FridgeType PanType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain CoffeeMachineType MugType)
        (canContain StoveBurnerType KettleType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CounterTopType SaltShakerType)
        (canContain CounterTopType BreadType)
        (canContain CounterTopType DishSpongeType)
        (canContain CounterTopType BowlType)
        (canContain CounterTopType KettleType)
        (canContain CounterTopType PotType)
        (canContain CounterTopType WineBottleType)
        (canContain CounterTopType MugType)
        (canContain CounterTopType EggType)
        (canContain CounterTopType ForkType)
        (canContain CounterTopType SpoonType)
        (canContain CounterTopType SoapBottleType)
        (canContain CounterTopType LettuceType)
        (canContain CounterTopType PotatoType)
        (canContain CounterTopType CupType)
        (canContain CounterTopType PlateType)
        (canContain CounterTopType PepperShakerType)
        (canContain CounterTopType TomatoType)
        (canContain CounterTopType KnifeType)
        (canContain CounterTopType AppleType)
        (canContain CounterTopType PanType)
        (canContain CounterTopType SpatulaType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain SinkBasinType DishSpongeType)
        (canContain SinkBasinType BowlType)
        (canContain SinkBasinType KettleType)
        (canContain SinkBasinType PotType)
        (canContain SinkBasinType MugType)
        (canContain SinkBasinType EggType)
        (canContain SinkBasinType ForkType)
        (canContain SinkBasinType SpoonType)
        (canContain SinkBasinType LettuceType)
        (canContain SinkBasinType PotatoType)
        (canContain SinkBasinType CupType)
        (canContain SinkBasinType PlateType)
        (canContain SinkBasinType TomatoType)
        (canContain SinkBasinType KnifeType)
        (canContain SinkBasinType AppleType)
        (canContain SinkBasinType PanType)
        (canContain SinkBasinType SpatulaType)
        (pickupable Tomato_bar__minus_01_dot_52_bar__plus_01_dot_36_bar__minus_03_dot_20)
        (pickupable Potato_bar__minus_01_dot_83_bar__plus_01_dot_10_bar__minus_00_dot_50)
        (pickupable Potato_bar__plus_00_dot_98_bar__plus_01_dot_80_bar__plus_02_dot_23)
        (pickupable DishSponge_bar__plus_00_dot_90_bar__plus_00_dot_46_bar__plus_00_dot_71)
        (pickupable Pot_bar__minus_00_dot_13_bar__plus_01_dot_33_bar__minus_02_dot_97)
        (pickupable WineBottle_bar__plus_00_dot_28_bar__plus_01_dot_32_bar__minus_03_dot_01)
        (pickupable Fork_bar__minus_01_dot_40_bar__plus_01_dot_32_bar__plus_02_dot_07)
        (pickupable Lettuce_bar__minus_01_dot_89_bar__plus_01_dot_40_bar__minus_01_dot_09)
        (pickupable Kettle_bar__plus_00_dot_77_bar__plus_01_dot_32_bar__minus_00_dot_94)
        (pickupable Apple_bar__minus_01_dot_09_bar__plus_01_dot_37_bar__minus_03_dot_31)
        (pickupable PepperShaker_bar__minus_01_dot_92_bar__plus_01_dot_32_bar__minus_01_dot_68)
        (pickupable PepperShaker_bar__plus_01_dot_19_bar__plus_01_dot_20_bar__plus_01_dot_22)
        (pickupable Plate_bar__minus_01_dot_53_bar__plus_01_dot_32_bar__minus_01_dot_94)
        (pickupable Fork_bar__minus_01_dot_53_bar__plus_01_dot_32_bar__minus_02_dot_46)
        (pickupable Tomato_bar__plus_01_dot_02_bar__plus_01_dot_56_bar__plus_01_dot_48)
        (pickupable Plate_bar__plus_00_dot_90_bar__plus_00_dot_35_bar__minus_02_dot_27)
        (pickupable Bread_bar__plus_00_dot_90_bar__plus_01_dot_40_bar__minus_02_dot_79)
        (pickupable Potato_bar__plus_01_dot_06_bar__plus_01_dot_80_bar__plus_01_dot_99)
        (pickupable Knife_bar__minus_01_dot_83_bar__plus_01_dot_36_bar__minus_00_dot_98)
        (pickupable SaltShaker_bar__plus_00_dot_64_bar__plus_01_dot_32_bar__minus_02_dot_05)
        (pickupable PaperTowelRoll_bar__minus_01_dot_35_bar__plus_01_dot_43_bar__minus_01_dot_50)
        (pickupable Plate_bar__plus_00_dot_82_bar__plus_00_dot_35_bar__minus_01_dot_96)
        (pickupable Pan_bar__minus_02_dot_05_bar__plus_01_dot_32_bar__minus_02_dot_98)
        (pickupable PepperShaker_bar__plus_01_dot_03_bar__plus_01_dot_20_bar__plus_01_dot_22)
        (pickupable Spatula_bar__minus_01_dot_17_bar__plus_01_dot_34_bar__minus_03_dot_31)
        (pickupable WineBottle_bar__minus_01_dot_82_bar__plus_00_dot_34_bar__plus_00_dot_21)
        (pickupable Bowl_bar__minus_01_dot_67_bar__plus_00_dot_34_bar__minus_02_dot_16)
        (pickupable Mug_bar__minus_02_dot_09_bar__plus_01_dot_33_bar__minus_00_dot_98)
        (pickupable Apple_bar__minus_01_dot_66_bar__plus_00_dot_33_bar__plus_02_dot_28)
        (pickupable Cup_bar__minus_02_dot_18_bar__plus_01_dot_32_bar__minus_02_dot_20)
        (pickupable Spoon_bar__minus_01_dot_79_bar__plus_01_dot_32_bar__plus_01_dot_42)
        (pickupable Pan_bar__minus_00_dot_63_bar__plus_01_dot_33_bar__minus_03_dot_30)
        (pickupable SoapBottle_bar__plus_00_dot_56_bar__plus_01_dot_32_bar__minus_03_dot_34)
        (pickupable SoapBottle_bar__minus_01_dot_35_bar__plus_01_dot_32_bar__minus_01_dot_04)
        (pickupable Knife_bar__minus_01_dot_31_bar__plus_01_dot_35_bar__minus_02_dot_87)
        (pickupable Spoon_bar__plus_00_dot_86_bar__plus_01_dot_21_bar__plus_01_dot_22)
        (pickupable Bowl_bar__minus_01_dot_75_bar__plus_00_dot_34_bar__plus_00_dot_41)
        (pickupable Spatula_bar__plus_00_dot_77_bar__plus_01_dot_34_bar__minus_02_dot_42)
        (pickupable Egg_bar__plus_01_dot_02_bar__plus_01_dot_72_bar__plus_01_dot_72)
        (pickupable Bowl_bar__plus_00_dot_97_bar__plus_01_dot_52_bar__plus_01_dot_66)
        (pickupable WineBottle_bar__minus_01_dot_63_bar__plus_00_dot_28_bar__plus_02_dot_11)
        (pickupable Cup_bar__minus_01_dot_40_bar__plus_01_dot_32_bar__minus_02_dot_20)
        (pickupable Spoon_bar__plus_00_dot_28_bar__plus_01_dot_32_bar__minus_02_dot_90)
        (pickupable Bread_bar__plus_00_dot_49_bar__plus_01_dot_40_bar__minus_03_dot_12)
        (pickupable Apple_bar__minus_01_dot_95_bar__plus_01_dot_10_bar__minus_00_dot_59)
        (isReceptacleObject Pot_bar__minus_00_dot_13_bar__plus_01_dot_33_bar__minus_02_dot_97)
        (isReceptacleObject Plate_bar__minus_01_dot_53_bar__plus_01_dot_32_bar__minus_01_dot_94)
        (isReceptacleObject Plate_bar__plus_00_dot_90_bar__plus_00_dot_35_bar__minus_02_dot_27)
        (isReceptacleObject Plate_bar__plus_00_dot_82_bar__plus_00_dot_35_bar__minus_01_dot_96)
        (isReceptacleObject Pan_bar__minus_02_dot_05_bar__plus_01_dot_32_bar__minus_02_dot_98)
        (isReceptacleObject Bowl_bar__minus_01_dot_67_bar__plus_00_dot_34_bar__minus_02_dot_16)
        (isReceptacleObject Mug_bar__minus_02_dot_09_bar__plus_01_dot_33_bar__minus_00_dot_98)
        (isReceptacleObject Cup_bar__minus_02_dot_18_bar__plus_01_dot_32_bar__minus_02_dot_20)
        (isReceptacleObject Pan_bar__minus_00_dot_63_bar__plus_01_dot_33_bar__minus_03_dot_30)
        (isReceptacleObject Bowl_bar__minus_01_dot_75_bar__plus_00_dot_34_bar__plus_00_dot_41)
        (isReceptacleObject Bowl_bar__plus_00_dot_97_bar__plus_01_dot_52_bar__plus_01_dot_66)
        (isReceptacleObject Cup_bar__minus_01_dot_40_bar__plus_01_dot_32_bar__minus_02_dot_20)
        (openable Drawer_bar__plus_00_dot_71_bar__plus_01_dot_06_bar__plus_01_dot_02)
        (openable Cabinet_bar__minus_01_dot_46_bar__plus_00_dot_78_bar__plus_00_dot_47)
        (openable Cabinet_bar__plus_00_dot_58_bar__plus_00_dot_78_bar__minus_02_dot_05)
        (openable Microwave_bar__plus_01_dot_16_bar__plus_01_dot_19_bar__plus_00_dot_85)
        (openable Fridge_bar__plus_01_dot_01_bar__plus_00_dot_23_bar__plus_01_dot_92)
        (openable Cabinet_bar__minus_01_dot_46_bar__plus_00_dot_78_bar__minus_02_dot_00)
        (openable Drawer_bar__plus_00_dot_71_bar__plus_00_dot_85_bar__plus_01_dot_02)
        (openable Cabinet_bar__minus_01_dot_46_bar__plus_00_dot_78_bar__plus_01_dot_31)
        (openable Drawer_bar__plus_00_dot_72_bar__plus_00_dot_59_bar__plus_00_dot_68)
        (openable Drawer_bar__plus_00_dot_72_bar__plus_00_dot_59_bar__plus_01_dot_02)
        (openable Drawer_bar__plus_00_dot_72_bar__plus_00_dot_84_bar__plus_00_dot_68)
        (openable Drawer_bar__plus_00_dot_72_bar__plus_01_dot_06_bar__plus_00_dot_68)
        
        (atLocation agent1 loc_bar_1_bar_4_bar_1_bar_30)
        
        (cleanable Tomato_bar__minus_01_dot_52_bar__plus_01_dot_36_bar__minus_03_dot_20)
        (cleanable Potato_bar__minus_01_dot_83_bar__plus_01_dot_10_bar__minus_00_dot_50)
        (cleanable Potato_bar__plus_00_dot_98_bar__plus_01_dot_80_bar__plus_02_dot_23)
        (cleanable DishSponge_bar__plus_00_dot_90_bar__plus_00_dot_46_bar__plus_00_dot_71)
        (cleanable Pot_bar__minus_00_dot_13_bar__plus_01_dot_33_bar__minus_02_dot_97)
        (cleanable Fork_bar__minus_01_dot_40_bar__plus_01_dot_32_bar__plus_02_dot_07)
        (cleanable Lettuce_bar__minus_01_dot_89_bar__plus_01_dot_40_bar__minus_01_dot_09)
        (cleanable Kettle_bar__plus_00_dot_77_bar__plus_01_dot_32_bar__minus_00_dot_94)
        (cleanable Apple_bar__minus_01_dot_09_bar__plus_01_dot_37_bar__minus_03_dot_31)
        (cleanable Plate_bar__minus_01_dot_53_bar__plus_01_dot_32_bar__minus_01_dot_94)
        (cleanable Fork_bar__minus_01_dot_53_bar__plus_01_dot_32_bar__minus_02_dot_46)
        (cleanable Tomato_bar__plus_01_dot_02_bar__plus_01_dot_56_bar__plus_01_dot_48)
        (cleanable Plate_bar__plus_00_dot_90_bar__plus_00_dot_35_bar__minus_02_dot_27)
        (cleanable Potato_bar__plus_01_dot_06_bar__plus_01_dot_80_bar__plus_01_dot_99)
        (cleanable Knife_bar__minus_01_dot_83_bar__plus_01_dot_36_bar__minus_00_dot_98)
        (cleanable Plate_bar__plus_00_dot_82_bar__plus_00_dot_35_bar__minus_01_dot_96)
        (cleanable Pan_bar__minus_02_dot_05_bar__plus_01_dot_32_bar__minus_02_dot_98)
        (cleanable Spatula_bar__minus_01_dot_17_bar__plus_01_dot_34_bar__minus_03_dot_31)
        (cleanable Bowl_bar__minus_01_dot_67_bar__plus_00_dot_34_bar__minus_02_dot_16)
        (cleanable Mug_bar__minus_02_dot_09_bar__plus_01_dot_33_bar__minus_00_dot_98)
        (cleanable Apple_bar__minus_01_dot_66_bar__plus_00_dot_33_bar__plus_02_dot_28)
        (cleanable Cup_bar__minus_02_dot_18_bar__plus_01_dot_32_bar__minus_02_dot_20)
        (cleanable Spoon_bar__minus_01_dot_79_bar__plus_01_dot_32_bar__plus_01_dot_42)
        (cleanable Pan_bar__minus_00_dot_63_bar__plus_01_dot_33_bar__minus_03_dot_30)
        (cleanable Knife_bar__minus_01_dot_31_bar__plus_01_dot_35_bar__minus_02_dot_87)
        (cleanable Spoon_bar__plus_00_dot_86_bar__plus_01_dot_21_bar__plus_01_dot_22)
        (cleanable Bowl_bar__minus_01_dot_75_bar__plus_00_dot_34_bar__plus_00_dot_41)
        (cleanable Spatula_bar__plus_00_dot_77_bar__plus_01_dot_34_bar__minus_02_dot_42)
        (cleanable Egg_bar__plus_01_dot_02_bar__plus_01_dot_72_bar__plus_01_dot_72)
        (cleanable Bowl_bar__plus_00_dot_97_bar__plus_01_dot_52_bar__plus_01_dot_66)
        (cleanable Cup_bar__minus_01_dot_40_bar__plus_01_dot_32_bar__minus_02_dot_20)
        (cleanable Spoon_bar__plus_00_dot_28_bar__plus_01_dot_32_bar__minus_02_dot_90)
        (cleanable Apple_bar__minus_01_dot_95_bar__plus_01_dot_10_bar__minus_00_dot_59)
        
        (heatable Tomato_bar__minus_01_dot_52_bar__plus_01_dot_36_bar__minus_03_dot_20)
        (heatable Potato_bar__minus_01_dot_83_bar__plus_01_dot_10_bar__minus_00_dot_50)
        (heatable Potato_bar__plus_00_dot_98_bar__plus_01_dot_80_bar__plus_02_dot_23)
        (heatable Apple_bar__minus_01_dot_09_bar__plus_01_dot_37_bar__minus_03_dot_31)
        (heatable Plate_bar__minus_01_dot_53_bar__plus_01_dot_32_bar__minus_01_dot_94)
        (heatable Tomato_bar__plus_01_dot_02_bar__plus_01_dot_56_bar__plus_01_dot_48)
        (heatable Plate_bar__plus_00_dot_90_bar__plus_00_dot_35_bar__minus_02_dot_27)
        (heatable Bread_bar__plus_00_dot_90_bar__plus_01_dot_40_bar__minus_02_dot_79)
        (heatable Potato_bar__plus_01_dot_06_bar__plus_01_dot_80_bar__plus_01_dot_99)
        (heatable Plate_bar__plus_00_dot_82_bar__plus_00_dot_35_bar__minus_01_dot_96)
        (heatable Mug_bar__minus_02_dot_09_bar__plus_01_dot_33_bar__minus_00_dot_98)
        (heatable Apple_bar__minus_01_dot_66_bar__plus_00_dot_33_bar__plus_02_dot_28)
        (heatable Cup_bar__minus_02_dot_18_bar__plus_01_dot_32_bar__minus_02_dot_20)
        (heatable Egg_bar__plus_01_dot_02_bar__plus_01_dot_72_bar__plus_01_dot_72)
        (heatable Cup_bar__minus_01_dot_40_bar__plus_01_dot_32_bar__minus_02_dot_20)
        (heatable Bread_bar__plus_00_dot_49_bar__plus_01_dot_40_bar__minus_03_dot_12)
        (heatable Apple_bar__minus_01_dot_95_bar__plus_01_dot_10_bar__minus_00_dot_59)
        (coolable Tomato_bar__minus_01_dot_52_bar__plus_01_dot_36_bar__minus_03_dot_20)
        (coolable Potato_bar__minus_01_dot_83_bar__plus_01_dot_10_bar__minus_00_dot_50)
        (coolable Potato_bar__plus_00_dot_98_bar__plus_01_dot_80_bar__plus_02_dot_23)
        (coolable Pot_bar__minus_00_dot_13_bar__plus_01_dot_33_bar__minus_02_dot_97)
        (coolable WineBottle_bar__plus_00_dot_28_bar__plus_01_dot_32_bar__minus_03_dot_01)
        (coolable Lettuce_bar__minus_01_dot_89_bar__plus_01_dot_40_bar__minus_01_dot_09)
        (coolable Apple_bar__minus_01_dot_09_bar__plus_01_dot_37_bar__minus_03_dot_31)
        (coolable Plate_bar__minus_01_dot_53_bar__plus_01_dot_32_bar__minus_01_dot_94)
        (coolable Tomato_bar__plus_01_dot_02_bar__plus_01_dot_56_bar__plus_01_dot_48)
        (coolable Plate_bar__plus_00_dot_90_bar__plus_00_dot_35_bar__minus_02_dot_27)
        (coolable Bread_bar__plus_00_dot_90_bar__plus_01_dot_40_bar__minus_02_dot_79)
        (coolable Potato_bar__plus_01_dot_06_bar__plus_01_dot_80_bar__plus_01_dot_99)
        (coolable Plate_bar__plus_00_dot_82_bar__plus_00_dot_35_bar__minus_01_dot_96)
        (coolable Pan_bar__minus_02_dot_05_bar__plus_01_dot_32_bar__minus_02_dot_98)
        (coolable WineBottle_bar__minus_01_dot_82_bar__plus_00_dot_34_bar__plus_00_dot_21)
        (coolable Bowl_bar__minus_01_dot_67_bar__plus_00_dot_34_bar__minus_02_dot_16)
        (coolable Mug_bar__minus_02_dot_09_bar__plus_01_dot_33_bar__minus_00_dot_98)
        (coolable Apple_bar__minus_01_dot_66_bar__plus_00_dot_33_bar__plus_02_dot_28)
        (coolable Cup_bar__minus_02_dot_18_bar__plus_01_dot_32_bar__minus_02_dot_20)
        (coolable Pan_bar__minus_00_dot_63_bar__plus_01_dot_33_bar__minus_03_dot_30)
        (coolable Bowl_bar__minus_01_dot_75_bar__plus_00_dot_34_bar__plus_00_dot_41)
        (coolable Egg_bar__plus_01_dot_02_bar__plus_01_dot_72_bar__plus_01_dot_72)
        (coolable Bowl_bar__plus_00_dot_97_bar__plus_01_dot_52_bar__plus_01_dot_66)
        (coolable WineBottle_bar__minus_01_dot_63_bar__plus_00_dot_28_bar__plus_02_dot_11)
        (coolable Cup_bar__minus_01_dot_40_bar__plus_01_dot_32_bar__minus_02_dot_20)
        (coolable Bread_bar__plus_00_dot_49_bar__plus_01_dot_40_bar__minus_03_dot_12)
        (coolable Apple_bar__minus_01_dot_95_bar__plus_01_dot_10_bar__minus_00_dot_59)
        
        (isCool Egg_bar__plus_01_dot_02_bar__plus_01_dot_72_bar__plus_01_dot_72)
        
        
        
        (sliceable Tomato_bar__minus_01_dot_52_bar__plus_01_dot_36_bar__minus_03_dot_20)
        (sliceable Potato_bar__minus_01_dot_83_bar__plus_01_dot_10_bar__minus_00_dot_50)
        (sliceable Potato_bar__plus_00_dot_98_bar__plus_01_dot_80_bar__plus_02_dot_23)
        (sliceable Lettuce_bar__minus_01_dot_89_bar__plus_01_dot_40_bar__minus_01_dot_09)
        (sliceable Apple_bar__minus_01_dot_09_bar__plus_01_dot_37_bar__minus_03_dot_31)
        (sliceable Tomato_bar__plus_01_dot_02_bar__plus_01_dot_56_bar__plus_01_dot_48)
        (sliceable Bread_bar__plus_00_dot_90_bar__plus_01_dot_40_bar__minus_02_dot_79)
        (sliceable Potato_bar__plus_01_dot_06_bar__plus_01_dot_80_bar__plus_01_dot_99)
        (sliceable Apple_bar__minus_01_dot_66_bar__plus_00_dot_33_bar__plus_02_dot_28)
        (sliceable Egg_bar__plus_01_dot_02_bar__plus_01_dot_72_bar__plus_01_dot_72)
        (sliceable Bread_bar__plus_00_dot_49_bar__plus_01_dot_40_bar__minus_03_dot_12)
        (sliceable Apple_bar__minus_01_dot_95_bar__plus_01_dot_10_bar__minus_00_dot_59)
        
        (inReceptacle Bowl_bar__minus_01_dot_67_bar__plus_00_dot_34_bar__minus_02_dot_16 Cabinet_bar__minus_01_dot_46_bar__plus_00_dot_78_bar__minus_02_dot_00)
        (inReceptacle Pan_bar__minus_00_dot_63_bar__plus_01_dot_33_bar__minus_03_dot_30 StoveBurner_bar__minus_00_dot_63_bar__plus_01_dot_33_bar__minus_03_dot_30)
        (inReceptacle Bowl_bar__minus_01_dot_75_bar__plus_00_dot_34_bar__plus_00_dot_41 Cabinet_bar__minus_01_dot_46_bar__plus_00_dot_78_bar__plus_00_dot_47)
        (inReceptacle WineBottle_bar__minus_01_dot_82_bar__plus_00_dot_34_bar__plus_00_dot_21 Cabinet_bar__minus_01_dot_46_bar__plus_00_dot_78_bar__plus_00_dot_47)
        (inReceptacle Plate_bar__plus_00_dot_82_bar__plus_00_dot_35_bar__minus_01_dot_96 Cabinet_bar__plus_00_dot_58_bar__plus_00_dot_78_bar__minus_02_dot_05)
        (inReceptacle Plate_bar__plus_00_dot_90_bar__plus_00_dot_35_bar__minus_02_dot_27 Cabinet_bar__plus_00_dot_58_bar__plus_00_dot_78_bar__minus_02_dot_05)
        (inReceptacle Tomato_bar__minus_01_dot_52_bar__plus_01_dot_36_bar__minus_03_dot_20 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle WineBottle_bar__plus_00_dot_28_bar__plus_01_dot_32_bar__minus_03_dot_01 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle Fork_bar__minus_01_dot_40_bar__plus_01_dot_32_bar__plus_02_dot_07 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle Kettle_bar__plus_00_dot_77_bar__plus_01_dot_32_bar__minus_00_dot_94 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle Apple_bar__minus_01_dot_09_bar__plus_01_dot_37_bar__minus_03_dot_31 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle PepperShaker_bar__minus_01_dot_92_bar__plus_01_dot_32_bar__minus_01_dot_68 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle Plate_bar__minus_01_dot_53_bar__plus_01_dot_32_bar__minus_01_dot_94 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle Fork_bar__minus_01_dot_53_bar__plus_01_dot_32_bar__minus_02_dot_46 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle Bread_bar__plus_00_dot_90_bar__plus_01_dot_40_bar__minus_02_dot_79 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle SaltShaker_bar__plus_00_dot_64_bar__plus_01_dot_32_bar__minus_02_dot_05 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle PaperTowelRoll_bar__minus_01_dot_35_bar__plus_01_dot_43_bar__minus_01_dot_50 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle HousePlant_bar__minus_02_dot_03_bar__plus_01_dot_31_bar__minus_00_dot_03 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle Pan_bar__minus_02_dot_05_bar__plus_01_dot_32_bar__minus_02_dot_98 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle Spatula_bar__minus_01_dot_17_bar__plus_01_dot_34_bar__minus_03_dot_31 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle Cup_bar__minus_02_dot_18_bar__plus_01_dot_32_bar__minus_02_dot_20 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle Spoon_bar__minus_01_dot_79_bar__plus_01_dot_32_bar__plus_01_dot_42 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle SoapBottle_bar__plus_00_dot_56_bar__plus_01_dot_32_bar__minus_03_dot_34 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle SoapBottle_bar__minus_01_dot_35_bar__plus_01_dot_32_bar__minus_01_dot_04 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle Knife_bar__minus_01_dot_31_bar__plus_01_dot_35_bar__minus_02_dot_87 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle Spatula_bar__plus_00_dot_77_bar__plus_01_dot_34_bar__minus_02_dot_42 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle Cup_bar__minus_01_dot_40_bar__plus_01_dot_32_bar__minus_02_dot_20 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle Spoon_bar__plus_00_dot_28_bar__plus_01_dot_32_bar__minus_02_dot_90 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle Bread_bar__plus_00_dot_49_bar__plus_01_dot_40_bar__minus_03_dot_12 CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18)
        (inReceptacle Pan_bar__minus_00_dot_63_bar__plus_01_dot_33_bar__minus_03_dot_30 StoveBurner_bar__minus_00_dot_75_bar__plus_01_dot_33_bar__minus_02_dot_97)
        (inReceptacle PepperShaker_bar__plus_01_dot_19_bar__plus_01_dot_20_bar__plus_01_dot_22 SideTable_bar__plus_01_dot_02_bar__plus_01_dot_24_bar__plus_00_dot_87)
        (inReceptacle Spoon_bar__plus_00_dot_86_bar__plus_01_dot_21_bar__plus_01_dot_22 SideTable_bar__plus_01_dot_02_bar__plus_01_dot_24_bar__plus_00_dot_87)
        (inReceptacle PepperShaker_bar__plus_01_dot_03_bar__plus_01_dot_20_bar__plus_01_dot_22 SideTable_bar__plus_01_dot_02_bar__plus_01_dot_24_bar__plus_00_dot_87)
        (inReceptacle DishSponge_bar__plus_00_dot_90_bar__plus_00_dot_46_bar__plus_00_dot_71 Drawer_bar__plus_00_dot_72_bar__plus_00_dot_59_bar__plus_00_dot_68)
        (inReceptacle Pot_bar__minus_00_dot_13_bar__plus_01_dot_33_bar__minus_02_dot_97 StoveBurner_bar__minus_00_dot_13_bar__plus_01_dot_33_bar__minus_02_dot_97)
        (inReceptacle Egg_bar__plus_01_dot_02_bar__plus_01_dot_72_bar__plus_01_dot_72 Fridge_bar__plus_01_dot_01_bar__plus_00_dot_23_bar__plus_01_dot_92)
        (inReceptacle Bowl_bar__plus_00_dot_97_bar__plus_01_dot_52_bar__plus_01_dot_66 Fridge_bar__plus_01_dot_01_bar__plus_00_dot_23_bar__plus_01_dot_92)
        (inReceptacle Potato_bar__plus_00_dot_98_bar__plus_01_dot_80_bar__plus_02_dot_23 Fridge_bar__plus_01_dot_01_bar__plus_00_dot_23_bar__plus_01_dot_92)
        (inReceptacle Tomato_bar__plus_01_dot_02_bar__plus_01_dot_56_bar__plus_01_dot_48 Fridge_bar__plus_01_dot_01_bar__plus_00_dot_23_bar__plus_01_dot_92)
        (inReceptacle Potato_bar__plus_01_dot_06_bar__plus_01_dot_80_bar__plus_01_dot_99 Fridge_bar__plus_01_dot_01_bar__plus_00_dot_23_bar__plus_01_dot_92)
        (inReceptacle WineBottle_bar__minus_01_dot_63_bar__plus_00_dot_28_bar__plus_02_dot_11 GarbageCan_bar__minus_01_dot_63_bar__plus_00_dot_22_bar__plus_02_dot_19)
        (inReceptacle Apple_bar__minus_01_dot_66_bar__plus_00_dot_33_bar__plus_02_dot_28 GarbageCan_bar__minus_01_dot_63_bar__plus_00_dot_22_bar__plus_02_dot_19)
        (inReceptacle Apple_bar__minus_01_dot_95_bar__plus_01_dot_10_bar__minus_00_dot_59 Sink_bar__minus_01_dot_99_bar__plus_01_dot_14_bar__minus_00_dot_98_bar_SinkBasin)
        (inReceptacle Potato_bar__minus_01_dot_83_bar__plus_01_dot_10_bar__minus_00_dot_50 Sink_bar__minus_01_dot_99_bar__plus_01_dot_14_bar__minus_00_dot_98_bar_SinkBasin)
        
        
        (receptacleAtLocation Cabinet_bar__plus_00_dot_58_bar__plus_00_dot_78_bar__minus_02_dot_05 loc_bar__minus_1_bar__minus_9_bar_1_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_46_bar__plus_00_dot_78_bar__plus_00_dot_47 loc_bar__minus_2_bar_0_bar_3_bar_45)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_46_bar__plus_00_dot_78_bar__plus_01_dot_31 loc_bar__minus_2_bar_7_bar_3_bar_45)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_46_bar__plus_00_dot_78_bar__minus_02_dot_00 loc_bar__minus_2_bar__minus_6_bar_3_bar_45)
        (receptacleAtLocation CoffeeMachine_bar__plus_01_dot_13_bar__plus_01_dot_31_bar__minus_01_dot_43 loc_bar_0_bar__minus_6_bar_1_bar_30)
        (receptacleAtLocation CounterTop_bar__minus_01_dot_81_bar__plus_01_dot_36_bar__plus_01_dot_18 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (receptacleAtLocation Drawer_bar__plus_00_dot_71_bar__plus_00_dot_85_bar__plus_01_dot_02 loc_bar_0_bar_6_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_71_bar__plus_01_dot_06_bar__plus_01_dot_02 loc_bar_0_bar_3_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__plus_00_dot_72_bar__plus_00_dot_59_bar__plus_00_dot_68 loc_bar__minus_1_bar_0_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_72_bar__plus_00_dot_59_bar__plus_01_dot_02 loc_bar__minus_1_bar_2_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_72_bar__plus_00_dot_84_bar__plus_00_dot_68 loc_bar_0_bar_1_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_72_bar__plus_01_dot_06_bar__plus_00_dot_68 loc_bar_0_bar_1_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_01_dot_61_bar__plus_00_dot_68_bar__minus_00_dot_43 loc_bar__minus_4_bar__minus_2_bar_3_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_61_bar__plus_00_dot_68_bar__minus_01_dot_22 loc_bar__minus_4_bar__minus_5_bar_3_bar_60)
        (receptacleAtLocation Fridge_bar__plus_01_dot_01_bar__plus_00_dot_23_bar__plus_01_dot_92 loc_bar_0_bar_8_bar_1_bar_60)
        (receptacleAtLocation GarbageCan_bar__minus_01_dot_63_bar__plus_00_dot_22_bar__plus_02_dot_19 loc_bar__minus_4_bar_5_bar_0_bar_60)
        (receptacleAtLocation Microwave_bar__plus_01_dot_16_bar__plus_01_dot_19_bar__plus_00_dot_85 loc_bar_1_bar_3_bar_1_bar_30)
        (receptacleAtLocation SideTable_bar__plus_01_dot_02_bar__plus_01_dot_24_bar__plus_00_dot_87 loc_bar_1_bar_4_bar_1_bar_45)
        (receptacleAtLocation Sink_bar__minus_01_dot_99_bar__plus_01_dot_14_bar__minus_00_dot_98_bar_SinkBasin loc_bar__minus_4_bar__minus_3_bar_3_bar_45)
        (receptacleAtLocation StoveBurner_bar__minus_00_dot_13_bar__plus_01_dot_33_bar__minus_02_dot_97 loc_bar__minus_1_bar__minus_9_bar_2_bar_30)
        (receptacleAtLocation StoveBurner_bar__minus_00_dot_24_bar__plus_01_dot_33_bar__minus_03_dot_30 loc_bar__minus_1_bar__minus_9_bar_2_bar_30)
        (receptacleAtLocation StoveBurner_bar__minus_00_dot_63_bar__plus_01_dot_33_bar__minus_03_dot_30 loc_bar__minus_3_bar__minus_9_bar_2_bar_30)
        (receptacleAtLocation StoveBurner_bar__minus_00_dot_75_bar__plus_01_dot_33_bar__minus_02_dot_97 loc_bar__minus_3_bar__minus_9_bar_2_bar_30)
        (receptacleAtLocation Toaster_bar__minus_01_dot_89_bar__plus_01_dot_31_bar__minus_02_dot_51 loc_bar__minus_4_bar__minus_9_bar_3_bar_30)
        (objectAtLocation Apple_bar__minus_01_dot_66_bar__plus_00_dot_33_bar__plus_02_dot_28 loc_bar__minus_4_bar_5_bar_0_bar_60)
        (objectAtLocation Spoon_bar__minus_01_dot_79_bar__plus_01_dot_32_bar__plus_01_dot_42 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation PepperShaker_bar__minus_01_dot_92_bar__plus_01_dot_32_bar__minus_01_dot_68 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation Plate_bar__minus_01_dot_53_bar__plus_01_dot_32_bar__minus_01_dot_94 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation Tomato_bar__plus_01_dot_02_bar__plus_01_dot_56_bar__plus_01_dot_48 loc_bar_0_bar_8_bar_1_bar_60)
        (objectAtLocation Spatula_bar__minus_01_dot_17_bar__plus_01_dot_34_bar__minus_03_dot_31 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation WineBottle_bar__plus_00_dot_28_bar__plus_01_dot_32_bar__minus_03_dot_01 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation Pan_bar__minus_02_dot_05_bar__plus_01_dot_32_bar__minus_02_dot_98 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation Cup_bar__minus_01_dot_40_bar__plus_01_dot_32_bar__minus_02_dot_20 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation Fork_bar__minus_01_dot_40_bar__plus_01_dot_32_bar__plus_02_dot_07 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation SoapBottle_bar__plus_00_dot_56_bar__plus_01_dot_32_bar__minus_03_dot_34 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation Potato_bar__plus_01_dot_06_bar__plus_01_dot_80_bar__plus_01_dot_99 loc_bar_0_bar_8_bar_1_bar_60)
        (objectAtLocation Bowl_bar__plus_00_dot_97_bar__plus_01_dot_52_bar__plus_01_dot_66 loc_bar_0_bar_8_bar_1_bar_60)
        (objectAtLocation Knife_bar__minus_01_dot_31_bar__plus_01_dot_35_bar__minus_02_dot_87 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation Bread_bar__plus_00_dot_49_bar__plus_01_dot_40_bar__minus_03_dot_12 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation Apple_bar__minus_01_dot_09_bar__plus_01_dot_37_bar__minus_03_dot_31 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation PepperShaker_bar__plus_01_dot_03_bar__plus_01_dot_20_bar__plus_01_dot_22 loc_bar_1_bar_4_bar_1_bar_45)
        (objectAtLocation Bowl_bar__minus_01_dot_75_bar__plus_00_dot_34_bar__plus_00_dot_41 loc_bar__minus_2_bar_0_bar_3_bar_45)
        (objectAtLocation Plate_bar__plus_00_dot_82_bar__plus_00_dot_35_bar__minus_01_dot_96 loc_bar__minus_1_bar__minus_9_bar_1_bar_60)
        (objectAtLocation Spoon_bar__plus_00_dot_86_bar__plus_01_dot_21_bar__plus_01_dot_22 loc_bar_1_bar_4_bar_1_bar_45)
        (objectAtLocation WineBottle_bar__minus_01_dot_63_bar__plus_00_dot_28_bar__plus_02_dot_11 loc_bar__minus_4_bar_5_bar_0_bar_60)
        (objectAtLocation Potato_bar__minus_01_dot_83_bar__plus_01_dot_10_bar__minus_00_dot_50 loc_bar__minus_4_bar__minus_3_bar_3_bar_45)
        (objectAtLocation Window_bar__minus_02_dot_57_bar__plus_01_dot_80_bar__minus_00_dot_82 loc_bar__minus_4_bar__minus_3_bar_3_bar_0)
        (objectAtLocation Sink_bar__minus_01_dot_99_bar__plus_01_dot_14_bar__minus_00_dot_98 loc_bar__minus_4_bar__minus_4_bar_3_bar_30)
        (objectAtLocation Bread_bar__plus_00_dot_90_bar__plus_01_dot_40_bar__minus_02_dot_79 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation Pot_bar__minus_00_dot_13_bar__plus_01_dot_33_bar__minus_02_dot_97 loc_bar__minus_1_bar__minus_9_bar_2_bar_30)
        (objectAtLocation Knife_bar__minus_01_dot_83_bar__plus_01_dot_36_bar__minus_00_dot_98 loc_bar__minus_4_bar__minus_3_bar_3_bar_45)
        (objectAtLocation Bowl_bar__minus_01_dot_67_bar__plus_00_dot_34_bar__minus_02_dot_16 loc_bar__minus_2_bar__minus_6_bar_3_bar_45)
        (objectAtLocation Potato_bar__plus_00_dot_98_bar__plus_01_dot_80_bar__plus_02_dot_23 loc_bar_0_bar_8_bar_1_bar_60)
        (objectAtLocation SoapBottle_bar__minus_01_dot_35_bar__plus_01_dot_32_bar__minus_01_dot_04 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation StoveKnob_bar__minus_00_dot_19_bar__plus_01_dot_34_bar__minus_02_dot_74 loc_bar__minus_1_bar__minus_9_bar_2_bar_45)
        (objectAtLocation StoveKnob_bar__minus_00_dot_34_bar__plus_01_dot_34_bar__minus_02_dot_74 loc_bar__minus_1_bar__minus_9_bar_2_bar_45)
        (objectAtLocation StoveKnob_bar__minus_00_dot_57_bar__plus_01_dot_34_bar__minus_02_dot_74 loc_bar__minus_2_bar__minus_9_bar_2_bar_45)
        (objectAtLocation StoveKnob_bar__minus_00_dot_71_bar__plus_01_dot_34_bar__minus_02_dot_74 loc_bar__minus_3_bar__minus_9_bar_2_bar_45)
        (objectAtLocation Fork_bar__minus_01_dot_53_bar__plus_01_dot_32_bar__minus_02_dot_46 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation Cup_bar__minus_02_dot_18_bar__plus_01_dot_32_bar__minus_02_dot_20 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation Kettle_bar__plus_00_dot_77_bar__plus_01_dot_32_bar__minus_00_dot_94 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation Egg_bar__plus_01_dot_02_bar__plus_01_dot_72_bar__plus_01_dot_72 loc_bar_0_bar_8_bar_1_bar_60)
        (objectAtLocation LightSwitch_bar__plus_01_dot_39_bar__plus_01_dot_57_bar__minus_00_dot_66 loc_bar_3_bar__minus_1_bar_1_bar_30)
        (objectAtLocation Pan_bar__minus_00_dot_63_bar__plus_01_dot_33_bar__minus_03_dot_30 loc_bar__minus_3_bar__minus_9_bar_2_bar_30)
        (objectAtLocation SaltShaker_bar__plus_00_dot_64_bar__plus_01_dot_32_bar__minus_02_dot_05 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation WineBottle_bar__minus_01_dot_82_bar__plus_00_dot_34_bar__plus_00_dot_21 loc_bar__minus_2_bar_0_bar_3_bar_45)
        (objectAtLocation Lettuce_bar__minus_01_dot_89_bar__plus_01_dot_40_bar__minus_01_dot_09 loc_bar__minus_4_bar__minus_3_bar_3_bar_45)
        (objectAtLocation HousePlant_bar__minus_02_dot_03_bar__plus_01_dot_31_bar__minus_00_dot_03 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation Spatula_bar__plus_00_dot_77_bar__plus_01_dot_34_bar__minus_02_dot_42 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation PaperTowelRoll_bar__minus_01_dot_35_bar__plus_01_dot_43_bar__minus_01_dot_50 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation Tomato_bar__minus_01_dot_52_bar__plus_01_dot_36_bar__minus_03_dot_20 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation Plate_bar__plus_00_dot_90_bar__plus_00_dot_35_bar__minus_02_dot_27 loc_bar__minus_1_bar__minus_9_bar_1_bar_60)
        (objectAtLocation PepperShaker_bar__plus_01_dot_19_bar__plus_01_dot_20_bar__plus_01_dot_22 loc_bar_1_bar_4_bar_1_bar_45)
        (objectAtLocation Chair_bar__plus_00_dot_60_bar__plus_00_dot_95_bar__minus_01_dot_09 loc_bar_0_bar__minus_4_bar_1_bar_60)
        (objectAtLocation DishSponge_bar__plus_00_dot_90_bar__plus_00_dot_46_bar__plus_00_dot_71 loc_bar__minus_1_bar_0_bar_1_bar_60)
        (objectAtLocation Spoon_bar__plus_00_dot_28_bar__plus_01_dot_32_bar__minus_02_dot_90 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation Apple_bar__minus_01_dot_95_bar__plus_01_dot_10_bar__minus_00_dot_59 loc_bar__minus_4_bar__minus_3_bar_3_bar_45)
        (objectAtLocation Mug_bar__minus_02_dot_09_bar__plus_01_dot_33_bar__minus_00_dot_98 loc_bar__minus_4_bar__minus_3_bar_3_bar_45)
        )
    

        (:goal
            (and
                (exists (?r - receptacle)
                    (exists (?o - object)
                        (and
                            (heatable ?o)
                            (objectType ?o EggType)
                            (receptacleType ?r SinkBasinType)
                            (isHot ?o)
                            (inReceptacle ?o ?r)
                        )
                    )
                )
            )
        )
    )
    