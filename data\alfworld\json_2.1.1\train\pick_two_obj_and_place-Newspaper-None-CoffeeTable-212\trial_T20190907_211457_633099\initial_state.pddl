
(define (problem plan_trial_T20190907_211457_633099)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Boots_bar__plus_04_dot_01_bar__plus_00_dot_00_bar__plus_01_dot_71 - object
        Box_bar__minus_00_dot_27_bar__plus_00_dot_57_bar__plus_01_dot_81 - object
        CreditCard_bar__plus_02_dot_01_bar__plus_00_dot_07_bar__minus_00_dot_62 - object
        CreditCard_bar__plus_02_dot_80_bar__plus_00_dot_32_bar__plus_01_dot_58 - object
        CreditCard_bar__minus_00_dot_40_bar__plus_00_dot_84_bar__minus_00_dot_77 - object
        FloorLamp_bar__plus_03_dot_61_bar__plus_00_dot_00_bar__plus_02_dot_16 - object
        HousePlant_bar__plus_00_dot_39_bar__plus_00_dot_83_bar__minus_00_dot_73 - object
        KeyChain_bar__plus_01_dot_17_bar__plus_00_dot_34_bar__plus_01_dot_60 - object
        KeyChain_bar__plus_01_dot_89_bar__plus_00_dot_47_bar__plus_00_dot_27 - object
        KeyChain_bar__minus_00_dot_07_bar__plus_00_dot_84_bar__minus_00_dot_87 - object
        Laptop_bar__plus_01_dot_81_bar__plus_00_dot_47_bar__plus_00_dot_50 - object
        LightSwitch_bar__minus_01_dot_40_bar__plus_01_dot_29_bar__plus_01_dot_84 - object
        Newspaper_bar__plus_02_dot_58_bar__plus_00_dot_32_bar__plus_01_dot_73 - object
        Newspaper_bar__minus_00_dot_27_bar__plus_00_dot_32_bar__plus_01_dot_88 - object
        Painting_bar__plus_04_dot_07_bar__plus_01_dot_95_bar__plus_00_dot_85 - object
        Pencil_bar__plus_03_dot_87_bar__plus_00_dot_07_bar__minus_00_dot_46 - object
        Pen_bar__plus_03_dot_80_bar__plus_00_dot_77_bar__plus_00_dot_58 - object
        Pen_bar__plus_03_dot_97_bar__plus_00_dot_88_bar__plus_01_dot_08 - object
        Pen_bar__minus_00_dot_29_bar__plus_00_dot_46_bar__minus_00_dot_73 - object
        Pillow_bar__plus_00_dot_65_bar__plus_00_dot_40_bar__plus_01_dot_71 - object
        RemoteControl_bar__plus_01_dot_17_bar__plus_00_dot_34_bar__plus_01_dot_82 - object
        Statue_bar__plus_00_dot_04_bar__plus_00_dot_85_bar__minus_00_dot_82 - object
        Statue_bar__minus_00_dot_53_bar__plus_00_dot_44_bar__minus_00_dot_74 - object
        Television_bar__plus_01_dot_90_bar__plus_01_dot_32_bar__minus_00_dot_84 - object
        TissueBox_bar__plus_01_dot_47_bar__plus_00_dot_48_bar__plus_00_dot_51 - object
        WateringCan_bar__plus_01_dot_62_bar__plus_00_dot_06_bar__minus_00_dot_70 - object
        Window_bar__plus_00_dot_02_bar__plus_02_dot_07_bar__plus_02_dot_49 - object
        Window_bar__plus_01_dot_57_bar__plus_02_dot_07_bar__plus_02_dot_49 - object
        ArmChair_bar__plus_02_dot_66_bar__plus_00_dot_00_bar__plus_01_dot_86 - receptacle
        ArmChair_bar__minus_00_dot_27_bar__plus_00_dot_00_bar__plus_01_dot_87 - receptacle
        CoffeeTable_bar__plus_01_dot_59_bar_00_dot_00_bar__plus_00_dot_45 - receptacle
        Drawer_bar__plus_03_dot_87_bar__plus_00_dot_78_bar__plus_00_dot_87 - receptacle
        GarbageCan_bar__plus_03_dot_83_bar__plus_00_dot_00_bar__minus_00_dot_50 - receptacle
        Shelf_bar__plus_01_dot_89_bar__plus_00_dot_13_bar__minus_00_dot_73 - receptacle
        Shelf_bar__plus_01_dot_89_bar__plus_00_dot_51_bar__minus_00_dot_73 - receptacle
        Shelf_bar__minus_00_dot_31_bar__plus_00_dot_13_bar__minus_00_dot_73 - receptacle
        Shelf_bar__minus_00_dot_31_bar__plus_00_dot_52_bar__minus_00_dot_73 - receptacle
        SideTable_bar__plus_03_dot_95_bar__plus_00_dot_00_bar__plus_00_dot_87 - receptacle
        Sofa_bar__plus_01_dot_17_bar__plus_00_dot_00_bar__plus_01_dot_87 - receptacle
        TVStand_bar__plus_01_dot_91_bar__plus_00_dot_04_bar__minus_00_dot_77 - receptacle
        TVStand_bar__minus_00_dot_29_bar__plus_00_dot_04_bar__minus_00_dot_77 - receptacle
        loc_bar__minus_2_bar_3_bar_0_bar_60 - location
        loc_bar_2_bar_0_bar_2_bar_45 - location
        loc_bar_13_bar__minus_1_bar_3_bar_45 - location
        loc_bar_14_bar_5_bar_1_bar_60 - location
        loc_bar_13_bar_3_bar_1_bar_60 - location
        loc_bar_0_bar_4_bar_0_bar__minus_15 - location
        loc_bar__minus_5_bar__minus_2_bar_1_bar_60 - location
        loc_bar_6_bar_4_bar_0_bar__minus_15 - location
        loc_bar_10_bar_2_bar_2_bar_45 - location
        loc_bar_5_bar_4_bar_0_bar_60 - location
        loc_bar_14_bar_3_bar_1_bar__minus_30 - location
        loc_bar_14_bar_5_bar_0_bar_60 - location
        loc_bar_10_bar_1_bar_2_bar_45 - location
        loc_bar_2_bar_0_bar_1_bar_60 - location
        loc_bar_10_bar_3_bar_0_bar_60 - location
        loc_bar_13_bar__minus_1_bar_1_bar_60 - location
        loc_bar__minus_2_bar_0_bar_2_bar_60 - location
        loc_bar__minus_3_bar_4_bar_0_bar_30 - location
        loc_bar_1_bar_2_bar_2_bar_45 - location
        loc_bar_5_bar_4_bar_3_bar_30 - location
        )
    

(:init


        (receptacleType TVStand_bar__plus_01_dot_91_bar__plus_00_dot_04_bar__minus_00_dot_77 TVStandType)
        (receptacleType ArmChair_bar__plus_02_dot_66_bar__plus_00_dot_00_bar__plus_01_dot_86 ArmChairType)
        (receptacleType Shelf_bar__plus_01_dot_89_bar__plus_00_dot_51_bar__minus_00_dot_73 ShelfType)
        (receptacleType Shelf_bar__minus_00_dot_31_bar__plus_00_dot_13_bar__minus_00_dot_73 ShelfType)
        (receptacleType SideTable_bar__plus_03_dot_95_bar__plus_00_dot_00_bar__plus_00_dot_87 SideTableType)
        (receptacleType CoffeeTable_bar__plus_01_dot_59_bar_00_dot_00_bar__plus_00_dot_45 CoffeeTableType)
        (receptacleType Sofa_bar__plus_01_dot_17_bar__plus_00_dot_00_bar__plus_01_dot_87 SofaType)
        (receptacleType Shelf_bar__minus_00_dot_31_bar__plus_00_dot_52_bar__minus_00_dot_73 ShelfType)
        (receptacleType ArmChair_bar__minus_00_dot_27_bar__plus_00_dot_00_bar__plus_01_dot_87 ArmChairType)
        (receptacleType Drawer_bar__plus_03_dot_87_bar__plus_00_dot_78_bar__plus_00_dot_87 DrawerType)
        (receptacleType TVStand_bar__minus_00_dot_29_bar__plus_00_dot_04_bar__minus_00_dot_77 TVStandType)
        (receptacleType GarbageCan_bar__plus_03_dot_83_bar__plus_00_dot_00_bar__minus_00_dot_50 GarbageCanType)
        (receptacleType Shelf_bar__plus_01_dot_89_bar__plus_00_dot_13_bar__minus_00_dot_73 ShelfType)
        (objectType HousePlant_bar__plus_00_dot_39_bar__plus_00_dot_83_bar__minus_00_dot_73 HousePlantType)
        (objectType Pencil_bar__plus_03_dot_87_bar__plus_00_dot_07_bar__minus_00_dot_46 PencilType)
        (objectType Pillow_bar__plus_00_dot_65_bar__plus_00_dot_40_bar__plus_01_dot_71 PillowType)
        (objectType Newspaper_bar__minus_00_dot_27_bar__plus_00_dot_32_bar__plus_01_dot_88 NewspaperType)
        (objectType Laptop_bar__plus_01_dot_81_bar__plus_00_dot_47_bar__plus_00_dot_50 LaptopType)
        (objectType Window_bar__plus_00_dot_02_bar__plus_02_dot_07_bar__plus_02_dot_49 WindowType)
        (objectType Pen_bar__plus_03_dot_80_bar__plus_00_dot_77_bar__plus_00_dot_58 PenType)
        (objectType CreditCard_bar__plus_02_dot_01_bar__plus_00_dot_07_bar__minus_00_dot_62 CreditCardType)
        (objectType Newspaper_bar__plus_02_dot_58_bar__plus_00_dot_32_bar__plus_01_dot_73 NewspaperType)
        (objectType TissueBox_bar__plus_01_dot_47_bar__plus_00_dot_48_bar__plus_00_dot_51 TissueBoxType)
        (objectType RemoteControl_bar__plus_01_dot_17_bar__plus_00_dot_34_bar__plus_01_dot_82 RemoteControlType)
        (objectType KeyChain_bar__minus_00_dot_07_bar__plus_00_dot_84_bar__minus_00_dot_87 KeyChainType)
        (objectType CreditCard_bar__minus_00_dot_40_bar__plus_00_dot_84_bar__minus_00_dot_77 CreditCardType)
        (objectType LightSwitch_bar__minus_01_dot_40_bar__plus_01_dot_29_bar__plus_01_dot_84 LightSwitchType)
        (objectType Box_bar__minus_00_dot_27_bar__plus_00_dot_57_bar__plus_01_dot_81 BoxType)
        (objectType KeyChain_bar__plus_01_dot_17_bar__plus_00_dot_34_bar__plus_01_dot_60 KeyChainType)
        (objectType Statue_bar__plus_00_dot_04_bar__plus_00_dot_85_bar__minus_00_dot_82 StatueType)
        (objectType Pen_bar__plus_03_dot_97_bar__plus_00_dot_88_bar__plus_01_dot_08 PenType)
        (objectType Boots_bar__plus_04_dot_01_bar__plus_00_dot_00_bar__plus_01_dot_71 BootsType)
        (objectType Painting_bar__plus_04_dot_07_bar__plus_01_dot_95_bar__plus_00_dot_85 PaintingType)
        (objectType Statue_bar__minus_00_dot_53_bar__plus_00_dot_44_bar__minus_00_dot_74 StatueType)
        (objectType Television_bar__plus_01_dot_90_bar__plus_01_dot_32_bar__minus_00_dot_84 TelevisionType)
        (objectType CreditCard_bar__plus_02_dot_80_bar__plus_00_dot_32_bar__plus_01_dot_58 CreditCardType)
        (objectType KeyChain_bar__plus_01_dot_89_bar__plus_00_dot_47_bar__plus_00_dot_27 KeyChainType)
        (objectType WateringCan_bar__plus_01_dot_62_bar__plus_00_dot_06_bar__minus_00_dot_70 WateringCanType)
        (objectType FloorLamp_bar__plus_03_dot_61_bar__plus_00_dot_00_bar__plus_02_dot_16 FloorLampType)
        (objectType Pen_bar__minus_00_dot_29_bar__plus_00_dot_46_bar__minus_00_dot_73 PenType)
        (objectType Window_bar__plus_01_dot_57_bar__plus_02_dot_07_bar__plus_02_dot_49 WindowType)
        (canContain TVStandType TissueBoxType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType NewspaperType)
        (canContain ArmChairType CreditCardType)
        (canContain ShelfType PenType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType TissueBoxType)
        (canContain ShelfType StatueType)
        (canContain ShelfType WateringCanType)
        (canContain ShelfType PenType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType TissueBoxType)
        (canContain ShelfType StatueType)
        (canContain ShelfType WateringCanType)
        (canContain SideTableType PenType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType TissueBoxType)
        (canContain SideTableType StatueType)
        (canContain SideTableType WateringCanType)
        (canContain CoffeeTableType PenType)
        (canContain CoffeeTableType NewspaperType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType PencilType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType TissueBoxType)
        (canContain CoffeeTableType StatueType)
        (canContain CoffeeTableType WateringCanType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType NewspaperType)
        (canContain SofaType CreditCardType)
        (canContain ShelfType PenType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType TissueBoxType)
        (canContain ShelfType StatueType)
        (canContain ShelfType WateringCanType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType NewspaperType)
        (canContain ArmChairType CreditCardType)
        (canContain DrawerType PenType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType TissueBoxType)
        (canContain DrawerType WateringCanType)
        (canContain TVStandType TissueBoxType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType NewspaperType)
        (canContain GarbageCanType PencilType)
        (canContain GarbageCanType TissueBoxType)
        (canContain ShelfType PenType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType TissueBoxType)
        (canContain ShelfType StatueType)
        (canContain ShelfType WateringCanType)
        (pickupable Pencil_bar__plus_03_dot_87_bar__plus_00_dot_07_bar__minus_00_dot_46)
        (pickupable Pillow_bar__plus_00_dot_65_bar__plus_00_dot_40_bar__plus_01_dot_71)
        (pickupable Newspaper_bar__minus_00_dot_27_bar__plus_00_dot_32_bar__plus_01_dot_88)
        (pickupable Laptop_bar__plus_01_dot_81_bar__plus_00_dot_47_bar__plus_00_dot_50)
        (pickupable Pen_bar__plus_03_dot_80_bar__plus_00_dot_77_bar__plus_00_dot_58)
        (pickupable CreditCard_bar__plus_02_dot_01_bar__plus_00_dot_07_bar__minus_00_dot_62)
        (pickupable Newspaper_bar__plus_02_dot_58_bar__plus_00_dot_32_bar__plus_01_dot_73)
        (pickupable TissueBox_bar__plus_01_dot_47_bar__plus_00_dot_48_bar__plus_00_dot_51)
        (pickupable RemoteControl_bar__plus_01_dot_17_bar__plus_00_dot_34_bar__plus_01_dot_82)
        (pickupable KeyChain_bar__minus_00_dot_07_bar__plus_00_dot_84_bar__minus_00_dot_87)
        (pickupable CreditCard_bar__minus_00_dot_40_bar__plus_00_dot_84_bar__minus_00_dot_77)
        (pickupable Box_bar__minus_00_dot_27_bar__plus_00_dot_57_bar__plus_01_dot_81)
        (pickupable KeyChain_bar__plus_01_dot_17_bar__plus_00_dot_34_bar__plus_01_dot_60)
        (pickupable Statue_bar__plus_00_dot_04_bar__plus_00_dot_85_bar__minus_00_dot_82)
        (pickupable Pen_bar__plus_03_dot_97_bar__plus_00_dot_88_bar__plus_01_dot_08)
        (pickupable Boots_bar__plus_04_dot_01_bar__plus_00_dot_00_bar__plus_01_dot_71)
        (pickupable Statue_bar__minus_00_dot_53_bar__plus_00_dot_44_bar__minus_00_dot_74)
        (pickupable CreditCard_bar__plus_02_dot_80_bar__plus_00_dot_32_bar__plus_01_dot_58)
        (pickupable KeyChain_bar__plus_01_dot_89_bar__plus_00_dot_47_bar__plus_00_dot_27)
        (pickupable WateringCan_bar__plus_01_dot_62_bar__plus_00_dot_06_bar__minus_00_dot_70)
        (pickupable Pen_bar__minus_00_dot_29_bar__plus_00_dot_46_bar__minus_00_dot_73)
        (isReceptacleObject Box_bar__minus_00_dot_27_bar__plus_00_dot_57_bar__plus_01_dot_81)
        (openable Drawer_bar__plus_03_dot_87_bar__plus_00_dot_78_bar__plus_00_dot_87)
        
        (atLocation agent1 loc_bar_5_bar_4_bar_3_bar_30)
        
        
        
        
        
        
        
        (toggleable FloorLamp_bar__plus_03_dot_61_bar__plus_00_dot_00_bar__plus_02_dot_16)
        
        
        
        
        (inReceptacle CreditCard_bar__minus_00_dot_40_bar__plus_00_dot_84_bar__minus_00_dot_77 TVStand_bar__minus_00_dot_29_bar__plus_00_dot_04_bar__minus_00_dot_77)
        (inReceptacle KeyChain_bar__minus_00_dot_07_bar__plus_00_dot_84_bar__minus_00_dot_87 TVStand_bar__minus_00_dot_29_bar__plus_00_dot_04_bar__minus_00_dot_77)
        (inReceptacle Statue_bar__plus_00_dot_04_bar__plus_00_dot_85_bar__minus_00_dot_82 TVStand_bar__minus_00_dot_29_bar__plus_00_dot_04_bar__minus_00_dot_77)
        (inReceptacle WateringCan_bar__plus_01_dot_62_bar__plus_00_dot_06_bar__minus_00_dot_70 Shelf_bar__plus_01_dot_89_bar__plus_00_dot_13_bar__minus_00_dot_73)
        (inReceptacle CreditCard_bar__plus_02_dot_01_bar__plus_00_dot_07_bar__minus_00_dot_62 Shelf_bar__plus_01_dot_89_bar__plus_00_dot_13_bar__minus_00_dot_73)
        (inReceptacle Newspaper_bar__plus_02_dot_58_bar__plus_00_dot_32_bar__plus_01_dot_73 ArmChair_bar__plus_02_dot_66_bar__plus_00_dot_00_bar__plus_01_dot_86)
        (inReceptacle CreditCard_bar__plus_02_dot_80_bar__plus_00_dot_32_bar__plus_01_dot_58 ArmChair_bar__plus_02_dot_66_bar__plus_00_dot_00_bar__plus_01_dot_86)
        (inReceptacle Pen_bar__plus_03_dot_80_bar__plus_00_dot_77_bar__plus_00_dot_58 Drawer_bar__plus_03_dot_87_bar__plus_00_dot_78_bar__plus_00_dot_87)
        (inReceptacle TissueBox_bar__plus_01_dot_47_bar__plus_00_dot_48_bar__plus_00_dot_51 CoffeeTable_bar__plus_01_dot_59_bar_00_dot_00_bar__plus_00_dot_45)
        (inReceptacle Laptop_bar__plus_01_dot_81_bar__plus_00_dot_47_bar__plus_00_dot_50 CoffeeTable_bar__plus_01_dot_59_bar_00_dot_00_bar__plus_00_dot_45)
        (inReceptacle KeyChain_bar__plus_01_dot_89_bar__plus_00_dot_47_bar__plus_00_dot_27 CoffeeTable_bar__plus_01_dot_59_bar_00_dot_00_bar__plus_00_dot_45)
        (inReceptacle Pillow_bar__plus_00_dot_65_bar__plus_00_dot_40_bar__plus_01_dot_71 Sofa_bar__plus_01_dot_17_bar__plus_00_dot_00_bar__plus_01_dot_87)
        (inReceptacle RemoteControl_bar__plus_01_dot_17_bar__plus_00_dot_34_bar__plus_01_dot_82 Sofa_bar__plus_01_dot_17_bar__plus_00_dot_00_bar__plus_01_dot_87)
        (inReceptacle Pen_bar__minus_00_dot_29_bar__plus_00_dot_46_bar__minus_00_dot_73 Shelf_bar__minus_00_dot_31_bar__plus_00_dot_52_bar__minus_00_dot_73)
        (inReceptacle Statue_bar__minus_00_dot_53_bar__plus_00_dot_44_bar__minus_00_dot_74 Shelf_bar__minus_00_dot_31_bar__plus_00_dot_52_bar__minus_00_dot_73)
        (inReceptacle Box_bar__minus_00_dot_27_bar__plus_00_dot_57_bar__plus_01_dot_81 ArmChair_bar__minus_00_dot_27_bar__plus_00_dot_00_bar__plus_01_dot_87)
        (inReceptacle Newspaper_bar__minus_00_dot_27_bar__plus_00_dot_32_bar__plus_01_dot_88 ArmChair_bar__minus_00_dot_27_bar__plus_00_dot_00_bar__plus_01_dot_87)
        (inReceptacle Pen_bar__plus_03_dot_97_bar__plus_00_dot_88_bar__plus_01_dot_08 SideTable_bar__plus_03_dot_95_bar__plus_00_dot_00_bar__plus_00_dot_87)
        (inReceptacle Television_bar__plus_01_dot_90_bar__plus_01_dot_32_bar__minus_00_dot_84 TVStand_bar__plus_01_dot_91_bar__plus_00_dot_04_bar__minus_00_dot_77)
        (inReceptacle Pencil_bar__plus_03_dot_87_bar__plus_00_dot_07_bar__minus_00_dot_46 GarbageCan_bar__plus_03_dot_83_bar__plus_00_dot_00_bar__minus_00_dot_50)
        
        
        (receptacleAtLocation ArmChair_bar__plus_02_dot_66_bar__plus_00_dot_00_bar__plus_01_dot_86 loc_bar_10_bar_3_bar_0_bar_60)
        (receptacleAtLocation ArmChair_bar__minus_00_dot_27_bar__plus_00_dot_00_bar__plus_01_dot_87 loc_bar__minus_2_bar_3_bar_0_bar_60)
        (receptacleAtLocation CoffeeTable_bar__plus_01_dot_59_bar_00_dot_00_bar__plus_00_dot_45 loc_bar_2_bar_0_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_03_dot_87_bar__plus_00_dot_78_bar__plus_00_dot_87 loc_bar_13_bar_3_bar_1_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_03_dot_83_bar__plus_00_dot_00_bar__minus_00_dot_50 loc_bar_13_bar__minus_1_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_89_bar__plus_00_dot_13_bar__minus_00_dot_73 loc_bar_10_bar_2_bar_2_bar_45)
        (receptacleAtLocation Shelf_bar__plus_01_dot_89_bar__plus_00_dot_51_bar__minus_00_dot_73 loc_bar_10_bar_1_bar_2_bar_45)
        (receptacleAtLocation Shelf_bar__minus_00_dot_31_bar__plus_00_dot_13_bar__minus_00_dot_73 loc_bar_1_bar_2_bar_2_bar_45)
        (receptacleAtLocation Shelf_bar__minus_00_dot_31_bar__plus_00_dot_52_bar__minus_00_dot_73 loc_bar__minus_2_bar_0_bar_2_bar_60)
        (receptacleAtLocation SideTable_bar__plus_03_dot_95_bar__plus_00_dot_00_bar__plus_00_dot_87 loc_bar_13_bar_3_bar_1_bar_60)
        (receptacleAtLocation Sofa_bar__plus_01_dot_17_bar__plus_00_dot_00_bar__plus_01_dot_87 loc_bar_5_bar_4_bar_0_bar_60)
        (receptacleAtLocation TVStand_bar__plus_01_dot_91_bar__plus_00_dot_04_bar__minus_00_dot_77 loc_bar_13_bar__minus_1_bar_3_bar_45)
        (receptacleAtLocation TVStand_bar__minus_00_dot_29_bar__plus_00_dot_04_bar__minus_00_dot_77 loc_bar__minus_5_bar__minus_2_bar_1_bar_60)
        (objectAtLocation CreditCard_bar__plus_02_dot_80_bar__plus_00_dot_32_bar__plus_01_dot_58 loc_bar_10_bar_3_bar_0_bar_60)
        (objectAtLocation Pen_bar__minus_00_dot_29_bar__plus_00_dot_46_bar__minus_00_dot_73 loc_bar__minus_2_bar_0_bar_2_bar_60)
        (objectAtLocation KeyChain_bar__minus_00_dot_07_bar__plus_00_dot_84_bar__minus_00_dot_87 loc_bar__minus_5_bar__minus_2_bar_1_bar_60)
        (objectAtLocation Newspaper_bar__minus_00_dot_27_bar__plus_00_dot_32_bar__plus_01_dot_88 loc_bar__minus_2_bar_3_bar_0_bar_60)
        (objectAtLocation CreditCard_bar__plus_02_dot_01_bar__plus_00_dot_07_bar__minus_00_dot_62 loc_bar_10_bar_2_bar_2_bar_45)
        (objectAtLocation KeyChain_bar__plus_01_dot_89_bar__plus_00_dot_47_bar__plus_00_dot_27 loc_bar_2_bar_0_bar_1_bar_60)
        (objectAtLocation Pen_bar__plus_03_dot_80_bar__plus_00_dot_77_bar__plus_00_dot_58 loc_bar_13_bar_3_bar_1_bar_60)
        (objectAtLocation Box_bar__minus_00_dot_27_bar__plus_00_dot_57_bar__plus_01_dot_81 loc_bar__minus_2_bar_3_bar_0_bar_60)
        (objectAtLocation FloorLamp_bar__plus_03_dot_61_bar__plus_00_dot_00_bar__plus_02_dot_16 loc_bar_14_bar_5_bar_0_bar_60)
        (objectAtLocation Newspaper_bar__plus_02_dot_58_bar__plus_00_dot_32_bar__plus_01_dot_73 loc_bar_10_bar_3_bar_0_bar_60)
        (objectAtLocation Laptop_bar__plus_01_dot_81_bar__plus_00_dot_47_bar__plus_00_dot_50 loc_bar_2_bar_0_bar_1_bar_60)
        (objectAtLocation WateringCan_bar__plus_01_dot_62_bar__plus_00_dot_06_bar__minus_00_dot_70 loc_bar_10_bar_2_bar_2_bar_45)
        (objectAtLocation TissueBox_bar__plus_01_dot_47_bar__plus_00_dot_48_bar__plus_00_dot_51 loc_bar_2_bar_0_bar_1_bar_60)
        (objectAtLocation KeyChain_bar__plus_01_dot_17_bar__plus_00_dot_34_bar__plus_01_dot_60 loc_bar_5_bar_4_bar_0_bar_60)
        (objectAtLocation Pen_bar__plus_03_dot_97_bar__plus_00_dot_88_bar__plus_01_dot_08 loc_bar_13_bar_3_bar_1_bar_60)
        (objectAtLocation Boots_bar__plus_04_dot_01_bar__plus_00_dot_00_bar__plus_01_dot_71 loc_bar_14_bar_5_bar_1_bar_60)
        (objectAtLocation Television_bar__plus_01_dot_90_bar__plus_01_dot_32_bar__minus_00_dot_84 loc_bar_13_bar__minus_1_bar_3_bar_45)
        (objectAtLocation Pencil_bar__plus_03_dot_87_bar__plus_00_dot_07_bar__minus_00_dot_46 loc_bar_13_bar__minus_1_bar_1_bar_60)
        (objectAtLocation RemoteControl_bar__plus_01_dot_17_bar__plus_00_dot_34_bar__plus_01_dot_82 loc_bar_5_bar_4_bar_0_bar_60)
        (objectAtLocation Painting_bar__plus_04_dot_07_bar__plus_01_dot_95_bar__plus_00_dot_85 loc_bar_14_bar_3_bar_1_bar__minus_30)
        (objectAtLocation CreditCard_bar__minus_00_dot_40_bar__plus_00_dot_84_bar__minus_00_dot_77 loc_bar__minus_5_bar__minus_2_bar_1_bar_60)
        (objectAtLocation HousePlant_bar__plus_00_dot_39_bar__plus_00_dot_83_bar__minus_00_dot_73 loc_bar_2_bar_0_bar_2_bar_45)
        (objectAtLocation Window_bar__plus_00_dot_02_bar__plus_02_dot_07_bar__plus_02_dot_49 loc_bar_0_bar_4_bar_0_bar__minus_15)
        (objectAtLocation Window_bar__plus_01_dot_57_bar__plus_02_dot_07_bar__plus_02_dot_49 loc_bar_6_bar_4_bar_0_bar__minus_15)
        (objectAtLocation Pillow_bar__plus_00_dot_65_bar__plus_00_dot_40_bar__plus_01_dot_71 loc_bar_5_bar_4_bar_0_bar_60)
        (objectAtLocation LightSwitch_bar__minus_01_dot_40_bar__plus_01_dot_29_bar__plus_01_dot_84 loc_bar__minus_3_bar_4_bar_0_bar_30)
        (objectAtLocation Statue_bar__minus_00_dot_53_bar__plus_00_dot_44_bar__minus_00_dot_74 loc_bar__minus_2_bar_0_bar_2_bar_60)
        (objectAtLocation Statue_bar__plus_00_dot_04_bar__plus_00_dot_85_bar__minus_00_dot_82 loc_bar__minus_5_bar__minus_2_bar_1_bar_60)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 NewspaperType)
                                    (receptacleType ?r CoffeeTableType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 NewspaperType)
                                            (receptacleType ?r CoffeeTableType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            