
(define (problem plan_trial_T20190918_175910_161432)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Apple_bar__plus_00_dot_96_bar__plus_00_dot_82_bar__minus_01_dot_49 - object
        Apple_bar__plus_01_dot_73_bar__plus_00_dot_97_bar__minus_01_dot_55 - object
        Bowl_bar__minus_00_dot_69_bar__plus_01_dot_39_bar__minus_01_dot_59 - object
        Bread_bar__plus_00_dot_99_bar__plus_00_dot_99_bar__plus_00_dot_74 - object
        ButterKnife_bar__plus_00_dot_30_bar__plus_00_dot_90_bar__minus_01_dot_30 - object
        Chair_bar__plus_00_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_72 - object
        Chair_bar__plus_01_dot_09_bar__plus_00_dot_00_bar__plus_01_dot_74 - object
        Cup_bar__plus_00_dot_48_bar__plus_00_dot_91_bar__plus_01_dot_18 - object
        DishSponge_bar__plus_01_dot_03_bar__plus_00_dot_76_bar__minus_01_dot_71 - object
        DishSponge_bar__plus_01_dot_74_bar__plus_00_dot_07_bar__plus_01_dot_54 - object
        Egg_bar__plus_01_dot_80_bar__plus_00_dot_11_bar__plus_01_dot_83 - object
        Egg_bar__plus_01_dot_94_bar__plus_00_dot_95_bar__minus_01_dot_37 - object
        Faucet_bar__plus_01_dot_11_bar__plus_01_dot_14_bar__minus_01_dot_76 - object
        Fork_bar__plus_01_dot_61_bar__plus_00_dot_91_bar__plus_00_dot_57 - object
        Fork_bar__plus_01_dot_68_bar__plus_00_dot_91_bar__plus_00_dot_52 - object
        Knife_bar__plus_00_dot_22_bar__plus_00_dot_94_bar__plus_00_dot_83 - object
        Knife_bar__plus_01_dot_14_bar__plus_00_dot_76_bar__minus_01_dot_58 - object
        Knife_bar__plus_01_dot_73_bar__plus_00_dot_94_bar__minus_01_dot_46 - object
        Ladle_bar__plus_01_dot_76_bar__plus_00_dot_95_bar__plus_01_dot_18 - object
        Lettuce_bar__plus_00_dot_86_bar__plus_00_dot_99_bar__plus_01_dot_12 - object
        LightSwitch_bar__plus_02_dot_00_bar__plus_01_dot_26_bar__plus_03_dot_09 - object
        Mug_bar__plus_01_dot_87_bar__plus_00_dot_91_bar__minus_01_dot_80 - object
        Mug_bar__plus_01_dot_92_bar__plus_00_dot_91_bar__plus_00_dot_57 - object
        Pan_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32 - object
        Pan_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32 - object
        PepperShaker_bar__plus_01_dot_59_bar__plus_00_dot_91_bar__minus_01_dot_29 - object
        PepperShaker_bar__plus_01_dot_73_bar__plus_01_dot_65_bar__minus_00_dot_45 - object
        PepperShaker_bar__plus_01_dot_76_bar__plus_00_dot_91_bar__plus_01_dot_27 - object
        Plate_bar__plus_01_dot_80_bar__plus_01_dot_65_bar_00_dot_00 - object
        Plate_bar__plus_01_dot_81_bar__plus_01_dot_63_bar__minus_00_dot_90 - object
        Potato_bar__plus_01_dot_80_bar__plus_01_dot_06_bar__minus_00_dot_89 - object
        Potato_bar__minus_00_dot_69_bar__plus_00_dot_79_bar__minus_01_dot_59 - object
        Pot_bar__plus_01_dot_53_bar__plus_00_dot_91_bar__plus_00_dot_06 - object
        SaltShaker_bar__plus_01_dot_73_bar__plus_02_dot_13_bar__plus_00_dot_88 - object
        SaltShaker_bar__plus_01_dot_81_bar__plus_01_dot_64_bar__minus_01_dot_77 - object
        SaltShaker_bar__plus_01_dot_84_bar__plus_02_dot_13_bar__plus_01_dot_24 - object
        Sink_bar__plus_01_dot_10_bar__plus_00_dot_91_bar__minus_01_dot_60 - object
        SoapBottle_bar__plus_01_dot_87_bar__plus_00_dot_91_bar__minus_01_dot_29 - object
        Spatula_bar__plus_00_dot_73_bar__plus_00_dot_93_bar__plus_00_dot_92 - object
        Spatula_bar__plus_01_dot_52_bar__plus_00_dot_93_bar__minus_01_dot_46 - object
        Spoon_bar__plus_00_dot_73_bar__plus_00_dot_91_bar__plus_00_dot_83 - object
        Spoon_bar__plus_01_dot_25_bar__plus_00_dot_91_bar__plus_01_dot_00 - object
        StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__plus_00_dot_09 - object
        StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__minus_00_dot_05 - object
        StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__minus_00_dot_18 - object
        StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__minus_00_dot_32 - object
        Tomato_bar__plus_00_dot_36_bar__plus_00_dot_98_bar__minus_01_dot_26 - object
        Window_bar__plus_00_dot_78_bar__plus_01_dot_74_bar__minus_01_dot_89 - object
        Window_bar__minus_00_dot_37_bar__plus_01_dot_59_bar__plus_03_dot_35 - object
        Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__plus_00_dot_67 - receptacle
        Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_00_dot_61 - receptacle
        Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_01_dot_21 - receptacle
        Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_01_dot_89 - receptacle
        Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_06_bar__plus_00_dot_19 - receptacle
        Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_06_bar__minus_00_dot_59 - receptacle
        Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_30_bar__plus_00_dot_69 - receptacle
        Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_30_bar__plus_01_dot_31 - receptacle
        CoffeeMachine_bar__plus_00_dot_50_bar__plus_00_dot_90_bar__minus_01_dot_60 - receptacle
        CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02 - receptacle
        CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52 - receptacle
        Fridge_bar__minus_00_dot_53_bar__plus_00_dot_00_bar__minus_01_dot_55 - receptacle
        GarbageCan_bar__plus_01_dot_77_bar__plus_00_dot_00_bar__plus_01_dot_69 - receptacle
        Microwave_bar__plus_01_dot_75_bar__plus_00_dot_90_bar__minus_00_dot_84 - receptacle
        Sink_bar__plus_01_dot_10_bar__plus_00_dot_91_bar__minus_01_dot_60_bar_SinkBasin - receptacle
        StoveBurner_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32 - receptacle
        StoveBurner_bar__plus_01_dot_55_bar__plus_00_dot_91_bar__plus_00_dot_08 - receptacle
        StoveBurner_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__plus_00_dot_08 - receptacle
        StoveBurner_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32 - receptacle
        Toaster_bar__plus_00_dot_13_bar__plus_00_dot_90_bar__minus_01_dot_59 - receptacle
        loc_bar_4_bar__minus_3_bar_1_bar__minus_30 - location
        loc_bar_4_bar_9_bar_2_bar_60 - location
        loc_bar_4_bar__minus_3_bar_1_bar_45 - location
        loc_bar_4_bar_0_bar_1_bar_45 - location
        loc_bar_6_bar_9_bar_2_bar_60 - location
        loc_bar_2_bar_0_bar_1_bar__minus_15 - location
        loc_bar_1_bar__minus_3_bar_2_bar_45 - location
        loc_bar_4_bar_1_bar_0_bar_45 - location
        loc_bar_6_bar_9_bar_2_bar__minus_30 - location
        loc_bar_4_bar__minus_3_bar_2_bar__minus_15 - location
        loc_bar_4_bar_0_bar_1_bar__minus_30 - location
        loc_bar_0_bar_7_bar_1_bar_60 - location
        loc_bar_3_bar__minus_3_bar_1_bar__minus_30 - location
        loc_bar_4_bar_0_bar_1_bar_60 - location
        loc_bar_4_bar__minus_3_bar_2_bar_45 - location
        loc_bar__minus_1_bar_11_bar_0_bar_0 - location
        loc_bar_4_bar__minus_1_bar_1_bar_60 - location
        loc_bar__minus_2_bar__minus_2_bar_2_bar_60 - location
        loc_bar_4_bar_1_bar_1_bar__minus_30 - location
        loc_bar_3_bar__minus_3_bar_2_bar_0 - location
        loc_bar_6_bar_11_bar_1_bar_45 - location
        loc_bar_4_bar__minus_1_bar_1_bar_45 - location
        loc_bar_2_bar__minus_3_bar_2_bar_45 - location
        loc_bar__minus_6_bar_8_bar_0_bar_30 - location
        )
    

(:init


        (receptacleType StoveBurner_bar__plus_01_dot_55_bar__plus_00_dot_91_bar__plus_00_dot_08 StoveBurnerType)
        (receptacleType Fridge_bar__minus_00_dot_53_bar__plus_00_dot_00_bar__minus_01_dot_55 FridgeType)
        (receptacleType GarbageCan_bar__plus_01_dot_77_bar__plus_00_dot_00_bar__plus_01_dot_69 GarbageCanType)
        (receptacleType CoffeeMachine_bar__plus_00_dot_50_bar__plus_00_dot_90_bar__minus_01_dot_60 CoffeeMachineType)
        (receptacleType Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_30_bar__plus_00_dot_69 CabinetType)
        (receptacleType StoveBurner_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32 StoveBurnerType)
        (receptacleType Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_30_bar__plus_01_dot_31 CabinetType)
        (receptacleType Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_06_bar__plus_00_dot_19 CabinetType)
        (receptacleType Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_01_dot_21 CabinetType)
        (receptacleType Sink_bar__plus_01_dot_10_bar__plus_00_dot_91_bar__minus_01_dot_60_bar_SinkBasin SinkBasinType)
        (receptacleType Microwave_bar__plus_01_dot_75_bar__plus_00_dot_90_bar__minus_00_dot_84 MicrowaveType)
        (receptacleType CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02 CounterTopType)
        (receptacleType Toaster_bar__plus_00_dot_13_bar__plus_00_dot_90_bar__minus_01_dot_59 ToasterType)
        (receptacleType Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_06_bar__minus_00_dot_59 CabinetType)
        (receptacleType StoveBurner_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__plus_00_dot_08 StoveBurnerType)
        (receptacleType Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__plus_00_dot_67 CabinetType)
        (receptacleType Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_00_dot_61 CabinetType)
        (receptacleType CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52 CounterTopType)
        (receptacleType StoveBurner_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32 StoveBurnerType)
        (receptacleType Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_01_dot_89 CabinetType)
        (objectType Bowl_bar__minus_00_dot_69_bar__plus_01_dot_39_bar__minus_01_dot_59 BowlType)
        (objectType Chair_bar__plus_00_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_72 ChairType)
        (objectType LightSwitch_bar__plus_02_dot_00_bar__plus_01_dot_26_bar__plus_03_dot_09 LightSwitchType)
        (objectType Knife_bar__plus_00_dot_22_bar__plus_00_dot_94_bar__plus_00_dot_83 KnifeType)
        (objectType Potato_bar__minus_00_dot_69_bar__plus_00_dot_79_bar__minus_01_dot_59 PotatoType)
        (objectType Sink_bar__plus_01_dot_10_bar__plus_00_dot_91_bar__minus_01_dot_60 SinkType)
        (objectType Pot_bar__plus_01_dot_53_bar__plus_00_dot_91_bar__plus_00_dot_06 PotType)
        (objectType Pan_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32 PanType)
        (objectType StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__plus_00_dot_09 StoveKnobType)
        (objectType Fork_bar__plus_01_dot_68_bar__plus_00_dot_91_bar__plus_00_dot_52 ForkType)
        (objectType Egg_bar__plus_01_dot_94_bar__plus_00_dot_95_bar__minus_01_dot_37 EggType)
        (objectType Tomato_bar__plus_00_dot_36_bar__plus_00_dot_98_bar__minus_01_dot_26 TomatoType)
        (objectType SaltShaker_bar__plus_01_dot_81_bar__plus_01_dot_64_bar__minus_01_dot_77 SaltShakerType)
        (objectType Spatula_bar__plus_01_dot_52_bar__plus_00_dot_93_bar__minus_01_dot_46 SpatulaType)
        (objectType PepperShaker_bar__plus_01_dot_73_bar__plus_01_dot_65_bar__minus_00_dot_45 PepperShakerType)
        (objectType Spoon_bar__plus_00_dot_73_bar__plus_00_dot_91_bar__plus_00_dot_83 SpoonType)
        (objectType Plate_bar__plus_01_dot_80_bar__plus_01_dot_65_bar_00_dot_00 PlateType)
        (objectType PepperShaker_bar__plus_01_dot_59_bar__plus_00_dot_91_bar__minus_01_dot_29 PepperShakerType)
        (objectType Fork_bar__plus_01_dot_61_bar__plus_00_dot_91_bar__plus_00_dot_57 ForkType)
        (objectType Apple_bar__plus_00_dot_96_bar__plus_00_dot_82_bar__minus_01_dot_49 AppleType)
        (objectType Window_bar__minus_00_dot_37_bar__plus_01_dot_59_bar__plus_03_dot_35 WindowType)
        (objectType StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__minus_00_dot_05 StoveKnobType)
        (objectType Mug_bar__plus_01_dot_87_bar__plus_00_dot_91_bar__minus_01_dot_80 MugType)
        (objectType ButterKnife_bar__plus_00_dot_30_bar__plus_00_dot_90_bar__minus_01_dot_30 ButterKnifeType)
        (objectType SaltShaker_bar__plus_01_dot_84_bar__plus_02_dot_13_bar__plus_01_dot_24 SaltShakerType)
        (objectType Ladle_bar__plus_01_dot_76_bar__plus_00_dot_95_bar__plus_01_dot_18 LadleType)
        (objectType DishSponge_bar__plus_01_dot_74_bar__plus_00_dot_07_bar__plus_01_dot_54 DishSpongeType)
        (objectType Spatula_bar__plus_00_dot_73_bar__plus_00_dot_93_bar__plus_00_dot_92 SpatulaType)
        (objectType StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__minus_00_dot_18 StoveKnobType)
        (objectType Bread_bar__plus_00_dot_99_bar__plus_00_dot_99_bar__plus_00_dot_74 BreadType)
        (objectType Cup_bar__plus_00_dot_48_bar__plus_00_dot_91_bar__plus_01_dot_18 CupType)
        (objectType DishSponge_bar__plus_01_dot_03_bar__plus_00_dot_76_bar__minus_01_dot_71 DishSpongeType)
        (objectType Window_bar__plus_00_dot_78_bar__plus_01_dot_74_bar__minus_01_dot_89 WindowType)
        (objectType Plate_bar__plus_01_dot_81_bar__plus_01_dot_63_bar__minus_00_dot_90 PlateType)
        (objectType Lettuce_bar__plus_00_dot_86_bar__plus_00_dot_99_bar__plus_01_dot_12 LettuceType)
        (objectType Chair_bar__plus_01_dot_09_bar__plus_00_dot_00_bar__plus_01_dot_74 ChairType)
        (objectType Knife_bar__plus_01_dot_73_bar__plus_00_dot_94_bar__minus_01_dot_46 KnifeType)
        (objectType Knife_bar__plus_01_dot_14_bar__plus_00_dot_76_bar__minus_01_dot_58 KnifeType)
        (objectType Egg_bar__plus_01_dot_80_bar__plus_00_dot_11_bar__plus_01_dot_83 EggType)
        (objectType PepperShaker_bar__plus_01_dot_76_bar__plus_00_dot_91_bar__plus_01_dot_27 PepperShakerType)
        (objectType Mug_bar__plus_01_dot_92_bar__plus_00_dot_91_bar__plus_00_dot_57 MugType)
        (objectType Potato_bar__plus_01_dot_80_bar__plus_01_dot_06_bar__minus_00_dot_89 PotatoType)
        (objectType Apple_bar__plus_01_dot_73_bar__plus_00_dot_97_bar__minus_01_dot_55 AppleType)
        (objectType Spoon_bar__plus_01_dot_25_bar__plus_00_dot_91_bar__plus_01_dot_00 SpoonType)
        (objectType SoapBottle_bar__plus_01_dot_87_bar__plus_00_dot_91_bar__minus_01_dot_29 SoapBottleType)
        (objectType Pan_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32 PanType)
        (objectType SaltShaker_bar__plus_01_dot_73_bar__plus_02_dot_13_bar__plus_00_dot_88 SaltShakerType)
        (objectType StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__minus_00_dot_32 StoveKnobType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain FridgeType BreadType)
        (canContain FridgeType BowlType)
        (canContain FridgeType PotType)
        (canContain FridgeType MugType)
        (canContain FridgeType EggType)
        (canContain FridgeType LettuceType)
        (canContain FridgeType PotatoType)
        (canContain FridgeType CupType)
        (canContain FridgeType PlateType)
        (canContain FridgeType TomatoType)
        (canContain FridgeType AppleType)
        (canContain FridgeType PanType)
        (canContain GarbageCanType BreadType)
        (canContain GarbageCanType DishSpongeType)
        (canContain GarbageCanType EggType)
        (canContain GarbageCanType SoapBottleType)
        (canContain GarbageCanType LettuceType)
        (canContain GarbageCanType PotatoType)
        (canContain GarbageCanType TomatoType)
        (canContain GarbageCanType AppleType)
        (canContain CoffeeMachineType MugType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain SinkBasinType DishSpongeType)
        (canContain SinkBasinType BowlType)
        (canContain SinkBasinType PotType)
        (canContain SinkBasinType MugType)
        (canContain SinkBasinType EggType)
        (canContain SinkBasinType LadleType)
        (canContain SinkBasinType ForkType)
        (canContain SinkBasinType SpoonType)
        (canContain SinkBasinType LettuceType)
        (canContain SinkBasinType PotatoType)
        (canContain SinkBasinType ButterKnifeType)
        (canContain SinkBasinType CupType)
        (canContain SinkBasinType PlateType)
        (canContain SinkBasinType TomatoType)
        (canContain SinkBasinType KnifeType)
        (canContain SinkBasinType AppleType)
        (canContain SinkBasinType PanType)
        (canContain SinkBasinType SpatulaType)
        (canContain MicrowaveType MugType)
        (canContain MicrowaveType PotatoType)
        (canContain MicrowaveType EggType)
        (canContain MicrowaveType AppleType)
        (canContain MicrowaveType TomatoType)
        (canContain MicrowaveType BreadType)
        (canContain MicrowaveType CupType)
        (canContain MicrowaveType PlateType)
        (canContain MicrowaveType BowlType)
        (canContain CounterTopType SaltShakerType)
        (canContain CounterTopType BreadType)
        (canContain CounterTopType DishSpongeType)
        (canContain CounterTopType BowlType)
        (canContain CounterTopType PotType)
        (canContain CounterTopType MugType)
        (canContain CounterTopType EggType)
        (canContain CounterTopType LadleType)
        (canContain CounterTopType ForkType)
        (canContain CounterTopType SpoonType)
        (canContain CounterTopType SoapBottleType)
        (canContain CounterTopType LettuceType)
        (canContain CounterTopType PotatoType)
        (canContain CounterTopType ButterKnifeType)
        (canContain CounterTopType CupType)
        (canContain CounterTopType PlateType)
        (canContain CounterTopType PepperShakerType)
        (canContain CounterTopType TomatoType)
        (canContain CounterTopType KnifeType)
        (canContain CounterTopType AppleType)
        (canContain CounterTopType PanType)
        (canContain CounterTopType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CounterTopType SaltShakerType)
        (canContain CounterTopType BreadType)
        (canContain CounterTopType DishSpongeType)
        (canContain CounterTopType BowlType)
        (canContain CounterTopType PotType)
        (canContain CounterTopType MugType)
        (canContain CounterTopType EggType)
        (canContain CounterTopType LadleType)
        (canContain CounterTopType ForkType)
        (canContain CounterTopType SpoonType)
        (canContain CounterTopType SoapBottleType)
        (canContain CounterTopType LettuceType)
        (canContain CounterTopType PotatoType)
        (canContain CounterTopType ButterKnifeType)
        (canContain CounterTopType CupType)
        (canContain CounterTopType PlateType)
        (canContain CounterTopType PepperShakerType)
        (canContain CounterTopType TomatoType)
        (canContain CounterTopType KnifeType)
        (canContain CounterTopType AppleType)
        (canContain CounterTopType PanType)
        (canContain CounterTopType SpatulaType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (pickupable Bowl_bar__minus_00_dot_69_bar__plus_01_dot_39_bar__minus_01_dot_59)
        (pickupable Knife_bar__plus_00_dot_22_bar__plus_00_dot_94_bar__plus_00_dot_83)
        (pickupable Potato_bar__minus_00_dot_69_bar__plus_00_dot_79_bar__minus_01_dot_59)
        (pickupable Pot_bar__plus_01_dot_53_bar__plus_00_dot_91_bar__plus_00_dot_06)
        (pickupable Pan_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32)
        (pickupable Fork_bar__plus_01_dot_68_bar__plus_00_dot_91_bar__plus_00_dot_52)
        (pickupable Egg_bar__plus_01_dot_94_bar__plus_00_dot_95_bar__minus_01_dot_37)
        (pickupable Tomato_bar__plus_00_dot_36_bar__plus_00_dot_98_bar__minus_01_dot_26)
        (pickupable SaltShaker_bar__plus_01_dot_81_bar__plus_01_dot_64_bar__minus_01_dot_77)
        (pickupable Spatula_bar__plus_01_dot_52_bar__plus_00_dot_93_bar__minus_01_dot_46)
        (pickupable PepperShaker_bar__plus_01_dot_73_bar__plus_01_dot_65_bar__minus_00_dot_45)
        (pickupable Spoon_bar__plus_00_dot_73_bar__plus_00_dot_91_bar__plus_00_dot_83)
        (pickupable Plate_bar__plus_01_dot_80_bar__plus_01_dot_65_bar_00_dot_00)
        (pickupable PepperShaker_bar__plus_01_dot_59_bar__plus_00_dot_91_bar__minus_01_dot_29)
        (pickupable Fork_bar__plus_01_dot_61_bar__plus_00_dot_91_bar__plus_00_dot_57)
        (pickupable Apple_bar__plus_00_dot_96_bar__plus_00_dot_82_bar__minus_01_dot_49)
        (pickupable Mug_bar__plus_01_dot_87_bar__plus_00_dot_91_bar__minus_01_dot_80)
        (pickupable ButterKnife_bar__plus_00_dot_30_bar__plus_00_dot_90_bar__minus_01_dot_30)
        (pickupable SaltShaker_bar__plus_01_dot_84_bar__plus_02_dot_13_bar__plus_01_dot_24)
        (pickupable Ladle_bar__plus_01_dot_76_bar__plus_00_dot_95_bar__plus_01_dot_18)
        (pickupable DishSponge_bar__plus_01_dot_74_bar__plus_00_dot_07_bar__plus_01_dot_54)
        (pickupable Spatula_bar__plus_00_dot_73_bar__plus_00_dot_93_bar__plus_00_dot_92)
        (pickupable Bread_bar__plus_00_dot_99_bar__plus_00_dot_99_bar__plus_00_dot_74)
        (pickupable Cup_bar__plus_00_dot_48_bar__plus_00_dot_91_bar__plus_01_dot_18)
        (pickupable DishSponge_bar__plus_01_dot_03_bar__plus_00_dot_76_bar__minus_01_dot_71)
        (pickupable Plate_bar__plus_01_dot_81_bar__plus_01_dot_63_bar__minus_00_dot_90)
        (pickupable Lettuce_bar__plus_00_dot_86_bar__plus_00_dot_99_bar__plus_01_dot_12)
        (pickupable Knife_bar__plus_01_dot_73_bar__plus_00_dot_94_bar__minus_01_dot_46)
        (pickupable Knife_bar__plus_01_dot_14_bar__plus_00_dot_76_bar__minus_01_dot_58)
        (pickupable Egg_bar__plus_01_dot_80_bar__plus_00_dot_11_bar__plus_01_dot_83)
        (pickupable PepperShaker_bar__plus_01_dot_76_bar__plus_00_dot_91_bar__plus_01_dot_27)
        (pickupable Mug_bar__plus_01_dot_92_bar__plus_00_dot_91_bar__plus_00_dot_57)
        (pickupable Potato_bar__plus_01_dot_80_bar__plus_01_dot_06_bar__minus_00_dot_89)
        (pickupable Apple_bar__plus_01_dot_73_bar__plus_00_dot_97_bar__minus_01_dot_55)
        (pickupable Spoon_bar__plus_01_dot_25_bar__plus_00_dot_91_bar__plus_01_dot_00)
        (pickupable SoapBottle_bar__plus_01_dot_87_bar__plus_00_dot_91_bar__minus_01_dot_29)
        (pickupable Pan_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32)
        (pickupable SaltShaker_bar__plus_01_dot_73_bar__plus_02_dot_13_bar__plus_00_dot_88)
        (isReceptacleObject Bowl_bar__minus_00_dot_69_bar__plus_01_dot_39_bar__minus_01_dot_59)
        (isReceptacleObject Pot_bar__plus_01_dot_53_bar__plus_00_dot_91_bar__plus_00_dot_06)
        (isReceptacleObject Pan_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32)
        (isReceptacleObject Plate_bar__plus_01_dot_80_bar__plus_01_dot_65_bar_00_dot_00)
        (isReceptacleObject Mug_bar__plus_01_dot_87_bar__plus_00_dot_91_bar__minus_01_dot_80)
        (isReceptacleObject Cup_bar__plus_00_dot_48_bar__plus_00_dot_91_bar__plus_01_dot_18)
        (isReceptacleObject Plate_bar__plus_01_dot_81_bar__plus_01_dot_63_bar__minus_00_dot_90)
        (isReceptacleObject Mug_bar__plus_01_dot_92_bar__plus_00_dot_91_bar__plus_00_dot_57)
        (isReceptacleObject Pan_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32)
        (openable Fridge_bar__minus_00_dot_53_bar__plus_00_dot_00_bar__minus_01_dot_55)
        (openable Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_06_bar__plus_00_dot_19)
        (openable Microwave_bar__plus_01_dot_75_bar__plus_00_dot_90_bar__minus_00_dot_84)
        (openable Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_06_bar__minus_00_dot_59)
        (openable Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__plus_00_dot_67)
        (openable Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_00_dot_61)
        
        (atLocation agent1 loc_bar__minus_6_bar_8_bar_0_bar_30)
        
        (cleanable Bowl_bar__minus_00_dot_69_bar__plus_01_dot_39_bar__minus_01_dot_59)
        (cleanable Knife_bar__plus_00_dot_22_bar__plus_00_dot_94_bar__plus_00_dot_83)
        (cleanable Potato_bar__minus_00_dot_69_bar__plus_00_dot_79_bar__minus_01_dot_59)
        (cleanable Pot_bar__plus_01_dot_53_bar__plus_00_dot_91_bar__plus_00_dot_06)
        (cleanable Pan_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32)
        (cleanable Fork_bar__plus_01_dot_68_bar__plus_00_dot_91_bar__plus_00_dot_52)
        (cleanable Egg_bar__plus_01_dot_94_bar__plus_00_dot_95_bar__minus_01_dot_37)
        (cleanable Tomato_bar__plus_00_dot_36_bar__plus_00_dot_98_bar__minus_01_dot_26)
        (cleanable Spatula_bar__plus_01_dot_52_bar__plus_00_dot_93_bar__minus_01_dot_46)
        (cleanable Spoon_bar__plus_00_dot_73_bar__plus_00_dot_91_bar__plus_00_dot_83)
        (cleanable Plate_bar__plus_01_dot_80_bar__plus_01_dot_65_bar_00_dot_00)
        (cleanable Fork_bar__plus_01_dot_61_bar__plus_00_dot_91_bar__plus_00_dot_57)
        (cleanable Apple_bar__plus_00_dot_96_bar__plus_00_dot_82_bar__minus_01_dot_49)
        (cleanable Mug_bar__plus_01_dot_87_bar__plus_00_dot_91_bar__minus_01_dot_80)
        (cleanable ButterKnife_bar__plus_00_dot_30_bar__plus_00_dot_90_bar__minus_01_dot_30)
        (cleanable Ladle_bar__plus_01_dot_76_bar__plus_00_dot_95_bar__plus_01_dot_18)
        (cleanable DishSponge_bar__plus_01_dot_74_bar__plus_00_dot_07_bar__plus_01_dot_54)
        (cleanable Spatula_bar__plus_00_dot_73_bar__plus_00_dot_93_bar__plus_00_dot_92)
        (cleanable Cup_bar__plus_00_dot_48_bar__plus_00_dot_91_bar__plus_01_dot_18)
        (cleanable DishSponge_bar__plus_01_dot_03_bar__plus_00_dot_76_bar__minus_01_dot_71)
        (cleanable Plate_bar__plus_01_dot_81_bar__plus_01_dot_63_bar__minus_00_dot_90)
        (cleanable Lettuce_bar__plus_00_dot_86_bar__plus_00_dot_99_bar__plus_01_dot_12)
        (cleanable Knife_bar__plus_01_dot_73_bar__plus_00_dot_94_bar__minus_01_dot_46)
        (cleanable Knife_bar__plus_01_dot_14_bar__plus_00_dot_76_bar__minus_01_dot_58)
        (cleanable Egg_bar__plus_01_dot_80_bar__plus_00_dot_11_bar__plus_01_dot_83)
        (cleanable Mug_bar__plus_01_dot_92_bar__plus_00_dot_91_bar__plus_00_dot_57)
        (cleanable Potato_bar__plus_01_dot_80_bar__plus_01_dot_06_bar__minus_00_dot_89)
        (cleanable Apple_bar__plus_01_dot_73_bar__plus_00_dot_97_bar__minus_01_dot_55)
        (cleanable Spoon_bar__plus_01_dot_25_bar__plus_00_dot_91_bar__plus_01_dot_00)
        (cleanable Pan_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32)
        
        (heatable Potato_bar__minus_00_dot_69_bar__plus_00_dot_79_bar__minus_01_dot_59)
        (heatable Egg_bar__plus_01_dot_94_bar__plus_00_dot_95_bar__minus_01_dot_37)
        (heatable Tomato_bar__plus_00_dot_36_bar__plus_00_dot_98_bar__minus_01_dot_26)
        (heatable Plate_bar__plus_01_dot_80_bar__plus_01_dot_65_bar_00_dot_00)
        (heatable Apple_bar__plus_00_dot_96_bar__plus_00_dot_82_bar__minus_01_dot_49)
        (heatable Mug_bar__plus_01_dot_87_bar__plus_00_dot_91_bar__minus_01_dot_80)
        (heatable Bread_bar__plus_00_dot_99_bar__plus_00_dot_99_bar__plus_00_dot_74)
        (heatable Cup_bar__plus_00_dot_48_bar__plus_00_dot_91_bar__plus_01_dot_18)
        (heatable Plate_bar__plus_01_dot_81_bar__plus_01_dot_63_bar__minus_00_dot_90)
        (heatable Egg_bar__plus_01_dot_80_bar__plus_00_dot_11_bar__plus_01_dot_83)
        (heatable Mug_bar__plus_01_dot_92_bar__plus_00_dot_91_bar__plus_00_dot_57)
        (heatable Potato_bar__plus_01_dot_80_bar__plus_01_dot_06_bar__minus_00_dot_89)
        (heatable Apple_bar__plus_01_dot_73_bar__plus_00_dot_97_bar__minus_01_dot_55)
        (coolable Bowl_bar__minus_00_dot_69_bar__plus_01_dot_39_bar__minus_01_dot_59)
        (coolable Potato_bar__minus_00_dot_69_bar__plus_00_dot_79_bar__minus_01_dot_59)
        (coolable Pot_bar__plus_01_dot_53_bar__plus_00_dot_91_bar__plus_00_dot_06)
        (coolable Pan_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32)
        (coolable Egg_bar__plus_01_dot_94_bar__plus_00_dot_95_bar__minus_01_dot_37)
        (coolable Tomato_bar__plus_00_dot_36_bar__plus_00_dot_98_bar__minus_01_dot_26)
        (coolable Plate_bar__plus_01_dot_80_bar__plus_01_dot_65_bar_00_dot_00)
        (coolable Apple_bar__plus_00_dot_96_bar__plus_00_dot_82_bar__minus_01_dot_49)
        (coolable Mug_bar__plus_01_dot_87_bar__plus_00_dot_91_bar__minus_01_dot_80)
        (coolable Bread_bar__plus_00_dot_99_bar__plus_00_dot_99_bar__plus_00_dot_74)
        (coolable Cup_bar__plus_00_dot_48_bar__plus_00_dot_91_bar__plus_01_dot_18)
        (coolable Plate_bar__plus_01_dot_81_bar__plus_01_dot_63_bar__minus_00_dot_90)
        (coolable Lettuce_bar__plus_00_dot_86_bar__plus_00_dot_99_bar__plus_01_dot_12)
        (coolable Egg_bar__plus_01_dot_80_bar__plus_00_dot_11_bar__plus_01_dot_83)
        (coolable Mug_bar__plus_01_dot_92_bar__plus_00_dot_91_bar__plus_00_dot_57)
        (coolable Potato_bar__plus_01_dot_80_bar__plus_01_dot_06_bar__minus_00_dot_89)
        (coolable Apple_bar__plus_01_dot_73_bar__plus_00_dot_97_bar__minus_01_dot_55)
        (coolable Pan_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32)
        
        
        
        
        
        (sliceable Potato_bar__minus_00_dot_69_bar__plus_00_dot_79_bar__minus_01_dot_59)
        (sliceable Egg_bar__plus_01_dot_94_bar__plus_00_dot_95_bar__minus_01_dot_37)
        (sliceable Tomato_bar__plus_00_dot_36_bar__plus_00_dot_98_bar__minus_01_dot_26)
        (sliceable Apple_bar__plus_00_dot_96_bar__plus_00_dot_82_bar__minus_01_dot_49)
        (sliceable Bread_bar__plus_00_dot_99_bar__plus_00_dot_99_bar__plus_00_dot_74)
        (sliceable Lettuce_bar__plus_00_dot_86_bar__plus_00_dot_99_bar__plus_01_dot_12)
        (sliceable Egg_bar__plus_01_dot_80_bar__plus_00_dot_11_bar__plus_01_dot_83)
        (sliceable Potato_bar__plus_01_dot_80_bar__plus_01_dot_06_bar__minus_00_dot_89)
        (sliceable Apple_bar__plus_01_dot_73_bar__plus_00_dot_97_bar__minus_01_dot_55)
        
        (inReceptacle Fork_bar__plus_01_dot_68_bar__plus_00_dot_91_bar__plus_00_dot_52 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle Spatula_bar__plus_00_dot_73_bar__plus_00_dot_93_bar__plus_00_dot_92 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle Mug_bar__plus_01_dot_92_bar__plus_00_dot_91_bar__plus_00_dot_57 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle Bread_bar__plus_00_dot_99_bar__plus_00_dot_99_bar__plus_00_dot_74 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle Cup_bar__plus_00_dot_48_bar__plus_00_dot_91_bar__plus_01_dot_18 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle Spoon_bar__plus_00_dot_73_bar__plus_00_dot_91_bar__plus_00_dot_83 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle Knife_bar__plus_00_dot_22_bar__plus_00_dot_94_bar__plus_00_dot_83 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle Spoon_bar__plus_01_dot_25_bar__plus_00_dot_91_bar__plus_01_dot_00 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle Lettuce_bar__plus_00_dot_86_bar__plus_00_dot_99_bar__plus_01_dot_12 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle Fork_bar__plus_01_dot_61_bar__plus_00_dot_91_bar__plus_00_dot_57 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle Ladle_bar__plus_01_dot_76_bar__plus_00_dot_95_bar__plus_01_dot_18 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle PepperShaker_bar__plus_01_dot_76_bar__plus_00_dot_91_bar__plus_01_dot_27 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle Pan_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32 StoveBurner_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32)
        (inReceptacle Pan_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32 StoveBurner_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32)
        (inReceptacle Egg_bar__plus_01_dot_94_bar__plus_00_dot_95_bar__minus_01_dot_37 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52)
        (inReceptacle Tomato_bar__plus_00_dot_36_bar__plus_00_dot_98_bar__minus_01_dot_26 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52)
        (inReceptacle Spatula_bar__plus_01_dot_52_bar__plus_00_dot_93_bar__minus_01_dot_46 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52)
        (inReceptacle Apple_bar__plus_01_dot_73_bar__plus_00_dot_97_bar__minus_01_dot_55 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52)
        (inReceptacle PepperShaker_bar__plus_01_dot_59_bar__plus_00_dot_91_bar__minus_01_dot_29 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52)
        (inReceptacle Knife_bar__plus_01_dot_73_bar__plus_00_dot_94_bar__minus_01_dot_46 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52)
        (inReceptacle SoapBottle_bar__plus_01_dot_87_bar__plus_00_dot_91_bar__minus_01_dot_29 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52)
        (inReceptacle Mug_bar__plus_01_dot_87_bar__plus_00_dot_91_bar__minus_01_dot_80 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52)
        (inReceptacle ButterKnife_bar__plus_00_dot_30_bar__plus_00_dot_90_bar__minus_01_dot_30 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52)
        (inReceptacle Pot_bar__plus_01_dot_53_bar__plus_00_dot_91_bar__plus_00_dot_06 StoveBurner_bar__plus_01_dot_55_bar__plus_00_dot_91_bar__plus_00_dot_08)
        (inReceptacle SaltShaker_bar__plus_01_dot_84_bar__plus_02_dot_13_bar__plus_01_dot_24 Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_30_bar__plus_01_dot_31)
        (inReceptacle SaltShaker_bar__plus_01_dot_73_bar__plus_02_dot_13_bar__plus_00_dot_88 Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_30_bar__plus_00_dot_69)
        (inReceptacle Plate_bar__plus_01_dot_80_bar__plus_01_dot_65_bar_00_dot_00 Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_06_bar__plus_00_dot_19)
        (inReceptacle PepperShaker_bar__plus_01_dot_73_bar__plus_01_dot_65_bar__minus_00_dot_45 Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_06_bar__minus_00_dot_59)
        (inReceptacle Plate_bar__plus_01_dot_81_bar__plus_01_dot_63_bar__minus_00_dot_90 Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_00_dot_61)
        (inReceptacle SaltShaker_bar__plus_01_dot_81_bar__plus_01_dot_64_bar__minus_01_dot_77 Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_01_dot_89)
        (inReceptacle DishSponge_bar__plus_01_dot_74_bar__plus_00_dot_07_bar__plus_01_dot_54 GarbageCan_bar__plus_01_dot_77_bar__plus_00_dot_00_bar__plus_01_dot_69)
        (inReceptacle Egg_bar__plus_01_dot_80_bar__plus_00_dot_11_bar__plus_01_dot_83 GarbageCan_bar__plus_01_dot_77_bar__plus_00_dot_00_bar__plus_01_dot_69)
        (inReceptacle DishSponge_bar__plus_01_dot_03_bar__plus_00_dot_76_bar__minus_01_dot_71 Sink_bar__plus_01_dot_10_bar__plus_00_dot_91_bar__minus_01_dot_60_bar_SinkBasin)
        (inReceptacle Apple_bar__plus_00_dot_96_bar__plus_00_dot_82_bar__minus_01_dot_49 Sink_bar__plus_01_dot_10_bar__plus_00_dot_91_bar__minus_01_dot_60_bar_SinkBasin)
        (inReceptacle Knife_bar__plus_01_dot_14_bar__plus_00_dot_76_bar__minus_01_dot_58 Sink_bar__plus_01_dot_10_bar__plus_00_dot_91_bar__minus_01_dot_60_bar_SinkBasin)
        (inReceptacle Potato_bar__plus_01_dot_80_bar__plus_01_dot_06_bar__minus_00_dot_89 Microwave_bar__plus_01_dot_75_bar__plus_00_dot_90_bar__minus_00_dot_84)
        (inReceptacle Potato_bar__minus_00_dot_69_bar__plus_00_dot_79_bar__minus_01_dot_59 Fridge_bar__minus_00_dot_53_bar__plus_00_dot_00_bar__minus_01_dot_55)
        (inReceptacle Bowl_bar__minus_00_dot_69_bar__plus_01_dot_39_bar__minus_01_dot_59 Fridge_bar__minus_00_dot_53_bar__plus_00_dot_00_bar__minus_01_dot_55)
        
        
        (receptacleAtLocation Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__plus_00_dot_67 loc_bar_4_bar_1_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_00_dot_61 loc_bar_3_bar__minus_3_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_01_dot_21 loc_bar_4_bar__minus_3_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_01_dot_89 loc_bar_4_bar__minus_3_bar_2_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_06_bar__plus_00_dot_19 loc_bar_4_bar_0_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_06_bar__minus_00_dot_59 loc_bar_2_bar_0_bar_1_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_30_bar__plus_00_dot_69 loc_bar_4_bar_1_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_30_bar__plus_01_dot_31 loc_bar_6_bar_9_bar_2_bar__minus_30)
        (receptacleAtLocation CoffeeMachine_bar__plus_00_dot_50_bar__plus_00_dot_90_bar__minus_01_dot_60 loc_bar_2_bar__minus_3_bar_2_bar_45)
        (receptacleAtLocation CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02 loc_bar_4_bar_1_bar_0_bar_45)
        (receptacleAtLocation CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52 loc_bar_2_bar__minus_3_bar_2_bar_45)
        (receptacleAtLocation Fridge_bar__minus_00_dot_53_bar__plus_00_dot_00_bar__minus_01_dot_55 loc_bar__minus_2_bar__minus_2_bar_2_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_01_dot_77_bar__plus_00_dot_00_bar__plus_01_dot_69 loc_bar_6_bar_9_bar_2_bar_60)
        (receptacleAtLocation Microwave_bar__plus_01_dot_75_bar__plus_00_dot_90_bar__minus_00_dot_84 loc_bar_4_bar__minus_3_bar_1_bar_45)
        (receptacleAtLocation Sink_bar__plus_01_dot_10_bar__plus_00_dot_91_bar__minus_01_dot_60_bar_SinkBasin loc_bar_4_bar__minus_3_bar_2_bar_45)
        (receptacleAtLocation StoveBurner_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32 loc_bar_4_bar__minus_1_bar_1_bar_60)
        (receptacleAtLocation StoveBurner_bar__plus_01_dot_55_bar__plus_00_dot_91_bar__plus_00_dot_08 loc_bar_4_bar_0_bar_1_bar_60)
        (receptacleAtLocation StoveBurner_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__plus_00_dot_08 loc_bar_4_bar_0_bar_1_bar_45)
        (receptacleAtLocation StoveBurner_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32 loc_bar_4_bar__minus_1_bar_1_bar_45)
        (receptacleAtLocation Toaster_bar__plus_00_dot_13_bar__plus_00_dot_90_bar__minus_01_dot_59 loc_bar_1_bar__minus_3_bar_2_bar_45)
        (objectAtLocation Mug_bar__plus_01_dot_92_bar__plus_00_dot_91_bar__plus_00_dot_57 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation Spoon_bar__plus_00_dot_73_bar__plus_00_dot_91_bar__plus_00_dot_83 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation DishSponge_bar__plus_01_dot_74_bar__plus_00_dot_07_bar__plus_01_dot_54 loc_bar_6_bar_9_bar_2_bar_60)
        (objectAtLocation PepperShaker_bar__plus_01_dot_76_bar__plus_00_dot_91_bar__plus_01_dot_27 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation Knife_bar__plus_01_dot_14_bar__plus_00_dot_76_bar__minus_01_dot_58 loc_bar_4_bar__minus_3_bar_2_bar_45)
        (objectAtLocation Potato_bar__plus_01_dot_80_bar__plus_01_dot_06_bar__minus_00_dot_89 loc_bar_4_bar__minus_3_bar_1_bar_45)
        (objectAtLocation Spatula_bar__plus_00_dot_73_bar__plus_00_dot_93_bar__plus_00_dot_92 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation Pan_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32 loc_bar_4_bar__minus_1_bar_1_bar_45)
        (objectAtLocation SaltShaker_bar__plus_01_dot_73_bar__plus_02_dot_13_bar__plus_00_dot_88 loc_bar_4_bar_1_bar_1_bar__minus_30)
        (objectAtLocation Apple_bar__plus_01_dot_73_bar__plus_00_dot_97_bar__minus_01_dot_55 loc_bar_2_bar__minus_3_bar_2_bar_45)
        (objectAtLocation Fork_bar__plus_01_dot_61_bar__plus_00_dot_91_bar__plus_00_dot_57 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation Egg_bar__plus_01_dot_80_bar__plus_00_dot_11_bar__plus_01_dot_83 loc_bar_6_bar_9_bar_2_bar_60)
        (objectAtLocation Plate_bar__plus_01_dot_80_bar__plus_01_dot_65_bar_00_dot_00 loc_bar_4_bar_0_bar_1_bar__minus_30)
        (objectAtLocation PepperShaker_bar__plus_01_dot_59_bar__plus_00_dot_91_bar__minus_01_dot_29 loc_bar_2_bar__minus_3_bar_2_bar_45)
        (objectAtLocation SaltShaker_bar__plus_01_dot_84_bar__plus_02_dot_13_bar__plus_01_dot_24 loc_bar_6_bar_9_bar_2_bar__minus_30)
        (objectAtLocation Knife_bar__plus_00_dot_22_bar__plus_00_dot_94_bar__plus_00_dot_83 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation Chair_bar__plus_00_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_72 loc_bar_0_bar_7_bar_1_bar_60)
        (objectAtLocation Chair_bar__plus_01_dot_09_bar__plus_00_dot_00_bar__plus_01_dot_74 loc_bar_4_bar_9_bar_2_bar_60)
        (objectAtLocation Sink_bar__plus_01_dot_10_bar__plus_00_dot_91_bar__minus_01_dot_60 loc_bar_4_bar__minus_3_bar_2_bar_45)
        (objectAtLocation Plate_bar__plus_01_dot_81_bar__plus_01_dot_63_bar__minus_00_dot_90 loc_bar_3_bar__minus_3_bar_1_bar__minus_30)
        (objectAtLocation SoapBottle_bar__plus_01_dot_87_bar__plus_00_dot_91_bar__minus_01_dot_29 loc_bar_2_bar__minus_3_bar_2_bar_45)
        (objectAtLocation Cup_bar__plus_00_dot_48_bar__plus_00_dot_91_bar__plus_01_dot_18 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation Egg_bar__plus_01_dot_94_bar__plus_00_dot_95_bar__minus_01_dot_37 loc_bar_2_bar__minus_3_bar_2_bar_45)
        (objectAtLocation Fork_bar__plus_01_dot_68_bar__plus_00_dot_91_bar__plus_00_dot_52 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation Ladle_bar__plus_01_dot_76_bar__plus_00_dot_95_bar__plus_01_dot_18 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation Apple_bar__plus_00_dot_96_bar__plus_00_dot_82_bar__minus_01_dot_49 loc_bar_4_bar__minus_3_bar_2_bar_45)
        (objectAtLocation SaltShaker_bar__plus_01_dot_81_bar__plus_01_dot_64_bar__minus_01_dot_77 loc_bar_4_bar__minus_3_bar_2_bar__minus_15)
        (objectAtLocation Pan_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32 loc_bar_4_bar__minus_1_bar_1_bar_60)
        (objectAtLocation Tomato_bar__plus_00_dot_36_bar__plus_00_dot_98_bar__minus_01_dot_26 loc_bar_2_bar__minus_3_bar_2_bar_45)
        (objectAtLocation Window_bar__minus_00_dot_37_bar__plus_01_dot_59_bar__plus_03_dot_35 loc_bar__minus_1_bar_11_bar_0_bar_0)
        (objectAtLocation Window_bar__plus_00_dot_78_bar__plus_01_dot_74_bar__minus_01_dot_89 loc_bar_3_bar__minus_3_bar_2_bar_0)
        (objectAtLocation Spatula_bar__plus_01_dot_52_bar__plus_00_dot_93_bar__minus_01_dot_46 loc_bar_2_bar__minus_3_bar_2_bar_45)
        (objectAtLocation Potato_bar__minus_00_dot_69_bar__plus_00_dot_79_bar__minus_01_dot_59 loc_bar__minus_2_bar__minus_2_bar_2_bar_60)
        (objectAtLocation Knife_bar__plus_01_dot_73_bar__plus_00_dot_94_bar__minus_01_dot_46 loc_bar_2_bar__minus_3_bar_2_bar_45)
        (objectAtLocation ButterKnife_bar__plus_00_dot_30_bar__plus_00_dot_90_bar__minus_01_dot_30 loc_bar_2_bar__minus_3_bar_2_bar_45)
        (objectAtLocation StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__plus_00_dot_09 loc_bar_4_bar_0_bar_1_bar_60)
        (objectAtLocation StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__minus_00_dot_18 loc_bar_4_bar__minus_1_bar_1_bar_60)
        (objectAtLocation StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__minus_00_dot_32 loc_bar_4_bar__minus_1_bar_1_bar_60)
        (objectAtLocation StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__minus_00_dot_05 loc_bar_4_bar_0_bar_1_bar_60)
        (objectAtLocation Bread_bar__plus_00_dot_99_bar__plus_00_dot_99_bar__plus_00_dot_74 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation Lettuce_bar__plus_00_dot_86_bar__plus_00_dot_99_bar__plus_01_dot_12 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation PepperShaker_bar__plus_01_dot_73_bar__plus_01_dot_65_bar__minus_00_dot_45 loc_bar_2_bar_0_bar_1_bar__minus_15)
        (objectAtLocation Pot_bar__plus_01_dot_53_bar__plus_00_dot_91_bar__plus_00_dot_06 loc_bar_4_bar_0_bar_1_bar_60)
        (objectAtLocation LightSwitch_bar__plus_02_dot_00_bar__plus_01_dot_26_bar__plus_03_dot_09 loc_bar_6_bar_11_bar_1_bar_45)
        (objectAtLocation DishSponge_bar__plus_01_dot_03_bar__plus_00_dot_76_bar__minus_01_dot_71 loc_bar_4_bar__minus_3_bar_2_bar_45)
        (objectAtLocation Spoon_bar__plus_01_dot_25_bar__plus_00_dot_91_bar__plus_01_dot_00 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation Bowl_bar__minus_00_dot_69_bar__plus_01_dot_39_bar__minus_01_dot_59 loc_bar__minus_2_bar__minus_2_bar_2_bar_60)
        (objectAtLocation Mug_bar__plus_01_dot_87_bar__plus_00_dot_91_bar__minus_01_dot_80 loc_bar_2_bar__minus_3_bar_2_bar_45)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 PanType)
                                    (receptacleType ?r CounterTopType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 PanType)
                                            (receptacleType ?r CounterTopType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            