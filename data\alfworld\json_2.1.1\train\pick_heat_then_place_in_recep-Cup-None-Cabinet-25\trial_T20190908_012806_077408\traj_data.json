{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 7}, {"high_idx": 3, "image_name": "000000074.png", "low_idx": 8}, {"high_idx": 3, "image_name": "000000075.png", "low_idx": 8}, {"high_idx": 3, "image_name": "000000076.png", "low_idx": 8}, {"high_idx": 3, "image_name": "000000077.png", "low_idx": 8}, {"high_idx": 3, "image_name": "000000078.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000079.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000080.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000081.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000082.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000083.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000084.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000085.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000086.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000087.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000088.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000089.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000090.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000091.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000092.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000093.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000094.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 19}, {"high_idx": 5, "image_name": "000000188.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000189.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000190.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000191.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000192.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000193.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000194.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000195.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000196.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000197.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000198.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000199.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000200.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000201.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000202.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000203.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000204.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000205.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000206.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000207.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000208.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000209.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000210.png", "low_idx": 22}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-8|4|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-10.94338132, -10.94338132, 3.812643052, 3.812643052, 3.295752524, 3.295752524]], "coordinateReceptacleObjectId": ["CounterTop", [-6.232, -6.232, 1.112, 1.112, 3.4364, 3.4364]], "forceVisible": true, "objectId": "Cup|-02.74|+00.82|+00.95"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-8|5|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-11.18, -11.18, 5.396, 5.396, 3.2488436, 3.2488436]], "forceVisible": true, "objectId": "Microwave|-02.80|+00.81|+01.35"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-8|5|3|-15"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-10.94338132, -10.94338132, 3.812643052, 3.812643052, 3.295752524, 3.295752524]], "coordinateReceptacleObjectId": ["Cabinet", [-11.1271496, -11.1271496, 3.301936628, 3.301936628, 7.19391156, 7.19391156]], "forceVisible": true, "objectId": "Cup|-02.74|+00.82|+00.95", "receptacleObjectId": "Cabinet|-02.78|+01.80|+00.83"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-02.74|+00.82|+00.95"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [118, 112, 155, 161], "mask": [[33434, 7], [33729, 17], [34027, 21], [34324, 27], [34623, 29], [34922, 31], [35221, 33], [35520, 35], [35820, 35], [36119, 36], [36419, 37], [36718, 38], [37018, 38], [37318, 37], [37619, 36], [37919, 36], [38219, 36], [38519, 35], [38820, 34], [39120, 34], [39421, 33], [39721, 32], [40021, 32], [40322, 31], [40622, 30], [40923, 29], [41223, 29], [41523, 29], [41824, 27], [42124, 27], [42424, 27], [42725, 25], [43026, 23], [43327, 21], [43628, 19], [43930, 16], [44230, 17], [44529, 19], [44828, 21], [45128, 21], [45428, 21], [45727, 23], [46027, 22], [46328, 21], [46628, 21], [46928, 20], [47229, 18], [47530, 16], [47832, 12], [48135, 6]], "point": [136, 135]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [76, 34, 295, 174], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25877, 217], [26177, 216], [26478, 215], [26778, 214], [27078, 214], [27378, 213], [27679, 212], [27979, 212], [28279, 211], [28579, 211], [28879, 210], [29179, 210], [29479, 210], [29779, 210], [30079, 210], [30379, 210], [30679, 209], [30979, 209], [31279, 209], [31579, 209], [31879, 209], [32179, 209], [32479, 209], [32779, 209], [33079, 208], [33380, 207], [33680, 206], [33980, 206], [34280, 205], [34581, 204], [34881, 204], [35181, 203], [35481, 203], [35781, 202], [36082, 201], [36382, 200], [36682, 200], [36982, 199], [37283, 198], [37583, 197], [37883, 197], [38183, 196], [38484, 195], [38784, 195], [39084, 194], [39384, 194], [39685, 192], [39985, 192], [40285, 191], [40585, 191], [40885, 190], [41186, 189], [41486, 188], [41786, 188], [42086, 188], [42387, 186], [42687, 186], [42987, 185], [43287, 185], [43588, 183], [43888, 183], [44188, 182], [44488, 182], [44789, 180], [45089, 60], [45151, 118], [45389, 53], [45458, 110], [45689, 46], [45765, 103], [45989, 39], [46072, 96], [46290, 36], [46374, 93], [46590, 34], [46676, 91], [46890, 32], [46978, 88], [47190, 30], [47280, 86], [47491, 26], [47583, 82], [47791, 24], [47885, 80], [48091, 22], [48187, 77], [48391, 20], [48489, 75], [48692, 17], [48791, 72], [48992, 16], [49092, 71], [49292, 15], [49393, 70], [49592, 14], [49694, 68], [49893, 12], [49995, 67], [50193, 11], [50296, 65], [50496, 6], [50598, 60], [50796, 5], [50899, 58], [51096, 4], [51200, 57], [51396, 3], [51501, 55], [51697, 1], [51802, 54], [52103, 52]], "point": [185, 103]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-02.74|+00.82|+00.95", "placeStationary": true, "receptacleObjectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [2, 34, 295, 294], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25870, 224], [26170, 223], [26469, 224], [26768, 224], [27068, 224], [27367, 224], [27666, 225], [27965, 226], [28265, 225], [28564, 226], [28864, 225], [29163, 226], [29463, 226], [29763, 226], [30062, 63], [30135, 154], [30362, 60], [30437, 152], [30661, 59], [30740, 148], [30961, 58], [31041, 147], [31260, 58], [31342, 146], [31560, 57], [31643, 145], [31860, 56], [31944, 144], [32159, 57], [32244, 144], [32459, 56], [32545, 143], [32758, 57], [32846, 142], [33058, 57], [33146, 141], [33358, 56], [33446, 141], [33657, 57], [33747, 139], [33957, 57], [34047, 139], [34256, 58], [34347, 138], [34556, 58], [34647, 138], [34855, 59], [34947, 138], [35155, 59], [35247, 137], [35455, 59], [35547, 137], [35754, 61], [35847, 136], [36054, 61], [36146, 137], [36353, 62], [36446, 136], [36653, 62], [36746, 136], [36953, 63], [37045, 55], [37104, 77], [37252, 64], [37345, 53], [37406, 75], [37552, 65], [37644, 53], [37706, 74], [37851, 66], [37944, 53], [38007, 73], [38151, 67], [38243, 53], [38307, 72], [38450, 68], [38543, 53], [38608, 71], [38750, 69], [38842, 54], [38908, 71], [39050, 70], [39141, 54], [39208, 70], [39349, 72], [39440, 56], [39507, 71], [39649, 73], [39739, 57], [39807, 70], [39948, 75], [40038, 58], [40107, 70], [40248, 76], [40337, 60], [40406, 70], [40548, 78], [40636, 62], [40705, 71], [40847, 81], [40934, 66], [41002, 73], [41147, 228], [41446, 228], [41746, 228], [42045, 229], [42345, 228], [42645, 228], [42944, 228], [43244, 228], [43543, 228], [43843, 228], [44143, 227], [44442, 228], [44742, 227], [45041, 108], [45151, 118], [45341, 101], [45458, 110], [45640, 95], [45765, 103], [45940, 88], [46072, 96], [46240, 86], [46374, 93], [46539, 85], [46676, 91], [46839, 83], [46978, 88], [47138, 82], [47280, 86], [47438, 79], [47583, 82], [47738, 77], [47885, 80], [48037, 76], [48187, 77], [48337, 74], [48489, 75], [48636, 73], [48791, 72], [48936, 72], [49092, 71], [49235, 72], [49393, 70], [49535, 71], [49694, 68], [49835, 70], [49995, 67], [50134, 70], [50296, 65], [50434, 68], [50598, 60], [50733, 68], [50899, 58], [51033, 67], [51200, 57], [51333, 66], [51501, 55], [51632, 64], [51697, 1], [51802, 54], [51932, 64], [52103, 52], [52231, 65], [52531, 64], [52830, 64], [53130, 63], [53430, 62], [53729, 63], [54029, 63], [54328, 63], [54628, 63], [54927, 63], [55227, 63], [55527, 62], [55826, 63], [56126, 62], [56425, 63], [56725, 62], [57025, 62], [57324, 63], [57624, 62], [57923, 63], [58223, 62], [58522, 63], [58822, 62], [59122, 62], [59421, 63], [59721, 63], [60020, 64], [60320, 64], [60620, 64], [60919, 65], [61219, 65], [61518, 66], [61818, 66], [62117, 67], [62417, 67], [62717, 67], [63016, 68], [63316, 68], [63615, 69], [63915, 69], [64215, 69], [64514, 70], [64814, 70], [65113, 71], [65413, 71], [65712, 72], [66012, 72], [66312, 73], [66611, 75], [66911, 75], [67210, 77], [67510, 76], [67810, 76], [68109, 77], [68409, 77], [68708, 78], [69008, 77], [69307, 78], [69607, 78], [69907, 78], [70206, 79], [70506, 79], [70805, 79], [71105, 79], [71405, 79], [71704, 80], [72004, 80], [72303, 80], [72603, 80], [72902, 81], [73202, 81], [73503, 80], [73804, 78], [74105, 77], [74405, 77], [74706, 76], [75007, 75], [75308, 74], [75609, 72], [75910, 71], [76211, 70], [76512, 69], [76813, 68], [77113, 67], [77414, 66], [77715, 65], [78016, 64], [78317, 63], [78618, 61], [78919, 60], [79220, 59], [79521, 58], [79822, 57], [80122, 56], [80423, 55], [80724, 54], [81025, 53], [81326, 52], [81627, 51], [81928, 49], [82229, 48], [82530, 47], [82831, 46], [83131, 46], [83432, 44], [83733, 43], [84034, 42], [84335, 41], [84636, 40], [84937, 38], [85238, 37], [85539, 36], [85839, 36], [86140, 38], [86441, 39], [86742, 39], [87043, 31], [87076, 5], [87345, 29], [87377, 2], [87649, 25], [87953, 20]], "point": [148, 150]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [2, 34, 295, 294], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25870, 224], [26170, 223], [26469, 224], [26768, 224], [27068, 224], [27367, 224], [27666, 225], [27965, 226], [28265, 225], [28564, 226], [28864, 225], [29163, 226], [29463, 94], [29594, 95], [29763, 94], [29894, 95], [30062, 63], [30135, 22], [30193, 96], [30362, 60], [30437, 20], [30493, 96], [30661, 59], [30740, 18], [30792, 96], [30961, 58], [31041, 17], [31092, 96], [31260, 58], [31342, 16], [31392, 96], [31560, 57], [31643, 15], [31691, 97], [31860, 56], [31944, 14], [31991, 97], [32159, 57], [32244, 15], [32290, 98], [32459, 56], [32545, 14], [32590, 98], [32758, 57], [32846, 13], [32890, 98], [33058, 57], [33146, 13], [33189, 98], [33358, 56], [33446, 14], [33489, 98], [33657, 57], [33747, 13], [33788, 98], [33957, 57], [34047, 13], [34088, 98], [34256, 58], [34347, 14], [34387, 98], [34556, 58], [34647, 15], [34687, 98], [34855, 59], [34947, 16], [34986, 99], [35155, 59], [35247, 17], [35285, 99], [35455, 59], [35547, 18], [35583, 101], [35754, 61], [35847, 20], [35881, 102], [36054, 61], [36146, 21], [36179, 104], [36353, 62], [36446, 19], [36479, 103], [36653, 62], [36746, 18], [36781, 101], [36953, 63], [37045, 18], [37082, 18], [37104, 77], [37252, 64], [37345, 17], [37383, 15], [37406, 75], [37552, 65], [37644, 17], [37683, 14], [37706, 74], [37851, 66], [37944, 17], [37984, 13], [38007, 73], [38151, 67], [38243, 18], [38284, 12], [38307, 72], [38450, 68], [38543, 18], [38584, 12], [38608, 71], [38750, 69], [38842, 20], [38884, 12], [38908, 71], [39050, 70], [39141, 21], [39184, 11], [39208, 70], [39349, 72], [39440, 23], [39483, 13], [39507, 71], [39649, 73], [39739, 25], [39782, 14], [39807, 70], [39948, 75], [40038, 28], [40080, 16], [40107, 70], [40248, 76], [40337, 31], [40378, 19], [40406, 70], [40548, 78], [40636, 62], [40705, 71], [40847, 81], [40934, 66], [41002, 73], [41147, 228], [41446, 228], [41746, 228], [42045, 229], [42345, 228], [42645, 228], [42944, 228], [43244, 228], [43543, 228], [43843, 228], [44143, 227], [44442, 228], [44742, 227], [45041, 228], [45341, 227], [45640, 228], [45940, 228], [46240, 227], [46539, 228], [46839, 227], [47138, 228], [47438, 227], [47738, 227], [48037, 227], [48337, 227], [48636, 227], [48936, 227], [49235, 228], [49535, 227], [49835, 227], [50134, 227], [50434, 224], [50733, 224], [51033, 224], [51333, 223], [51632, 64], [51697, 159], [51932, 64], [51997, 158], [52231, 65], [52531, 65], [52830, 66], [53130, 66], [53430, 65], [53729, 66], [54029, 66], [54328, 67], [54628, 67], [54927, 67], [55227, 67], [55527, 67], [55826, 68], [56126, 68], [56425, 68], [56725, 68], [57025, 68], [57324, 69], [57624, 69], [57923, 70], [58223, 69], [58522, 70], [58822, 70], [59122, 70], [59421, 71], [59721, 70], [60020, 71], [60320, 71], [60620, 71], [60919, 72], [61219, 71], [61518, 72], [61818, 72], [62117, 73], [62417, 73], [62717, 72], [63016, 73], [63316, 73], [63615, 74], [63915, 74], [64215, 74], [64514, 74], [64814, 74], [65113, 75], [65413, 75], [65712, 76], [66012, 75], [66312, 75], [66611, 76], [66911, 76], [67210, 77], [67510, 76], [67810, 76], [68109, 77], [68409, 77], [68708, 78], [69008, 77], [69307, 78], [69607, 78], [69907, 78], [70206, 79], [70506, 79], [70805, 79], [71105, 79], [71405, 79], [71704, 80], [72004, 80], [72303, 80], [72603, 80], [72902, 81], [73202, 81], [73503, 80], [73804, 78], [74105, 77], [74405, 77], [74706, 76], [75007, 75], [75308, 74], [75609, 72], [75910, 71], [76211, 70], [76512, 69], [76813, 68], [77113, 67], [77414, 66], [77715, 65], [78016, 64], [78317, 63], [78618, 61], [78919, 60], [79220, 59], [79521, 58], [79822, 57], [80122, 56], [80423, 55], [80724, 54], [81025, 53], [81326, 52], [81627, 51], [81928, 49], [82229, 48], [82530, 47], [82831, 46], [83131, 46], [83432, 44], [83733, 43], [84034, 42], [84335, 41], [84636, 40], [84937, 38], [85238, 37], [85539, 36], [85839, 36], [86140, 38], [86441, 39], [86742, 39], [87043, 31], [87076, 5], [87345, 29], [87377, 2], [87649, 25], [87953, 20]], "point": [148, 163]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-11.18, -11.18, 5.396, 5.396, 3.2488436, 3.2488436]], "forceVisible": true, "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [76, 34, 295, 174], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25877, 217], [26177, 216], [26478, 215], [26778, 214], [27078, 214], [27378, 213], [27679, 212], [27979, 212], [28279, 211], [28579, 211], [28879, 210], [29179, 210], [29479, 210], [29779, 210], [30079, 210], [30379, 210], [30679, 209], [30979, 209], [31279, 209], [31579, 209], [31879, 209], [32179, 209], [32479, 209], [32779, 209], [33079, 208], [33380, 207], [33680, 206], [33980, 206], [34280, 205], [34581, 204], [34881, 204], [35181, 203], [35481, 203], [35781, 202], [36082, 201], [36382, 200], [36682, 200], [36982, 199], [37283, 198], [37583, 197], [37883, 197], [38183, 196], [38484, 195], [38784, 195], [39084, 194], [39384, 194], [39685, 192], [39985, 192], [40285, 191], [40585, 191], [40885, 190], [41186, 189], [41486, 188], [41786, 188], [42086, 188], [42387, 186], [42687, 186], [42987, 185], [43287, 185], [43588, 183], [43888, 183], [44188, 182], [44488, 182], [44789, 180], [45089, 180], [45389, 179], [45689, 179], [45989, 179], [46290, 177], [46590, 177], [46890, 176], [47190, 176], [47491, 174], [47791, 174], [48091, 173], [48391, 173], [48692, 171], [48992, 171], [49292, 171], [49592, 170], [49893, 169], [50193, 168], [50496, 162], [50796, 161], [51096, 161], [51396, 160], [51697, 159], [51997, 158]], "point": [185, 103]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-11.18, -11.18, 5.396, 5.396, 3.2488436, 3.2488436]], "forceVisible": true, "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [76, 34, 295, 174], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25877, 217], [26177, 216], [26478, 215], [26778, 214], [27078, 214], [27378, 213], [27679, 212], [27979, 212], [28279, 211], [28579, 211], [28879, 210], [29179, 210], [29479, 210], [29779, 210], [30079, 210], [30379, 210], [30679, 209], [30979, 209], [31279, 209], [31579, 209], [31879, 209], [32179, 209], [32479, 209], [32779, 209], [33079, 208], [33380, 207], [33680, 206], [33980, 206], [34280, 205], [34581, 204], [34881, 204], [35181, 203], [35481, 203], [35781, 202], [36082, 201], [36382, 200], [36682, 200], [36982, 199], [37283, 198], [37583, 197], [37883, 197], [38183, 196], [38484, 195], [38784, 195], [39084, 194], [39384, 194], [39685, 192], [39985, 192], [40285, 191], [40585, 191], [40885, 190], [41186, 189], [41486, 188], [41786, 188], [42086, 188], [42387, 186], [42687, 186], [42987, 185], [43287, 185], [43588, 183], [43888, 183], [44188, 182], [44488, 182], [44789, 180], [45089, 180], [45389, 179], [45689, 179], [45989, 179], [46290, 177], [46590, 177], [46890, 176], [47190, 176], [47491, 174], [47791, 174], [48091, 173], [48391, 173], [48692, 171], [48992, 171], [49292, 171], [49592, 170], [49893, 169], [50193, 168], [50496, 162], [50796, 161], [51096, 161], [51396, 160], [51697, 159], [51997, 158]], "point": [185, 103]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [76, 34, 295, 174], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25877, 217], [26177, 216], [26478, 215], [26778, 214], [27078, 214], [27378, 213], [27679, 212], [27979, 212], [28279, 211], [28579, 211], [28879, 210], [29179, 210], [29479, 210], [29779, 210], [30079, 210], [30379, 210], [30679, 209], [30979, 209], [31279, 209], [31579, 209], [31879, 209], [32179, 209], [32479, 209], [32779, 209], [33079, 208], [33380, 207], [33680, 206], [33980, 206], [34280, 205], [34581, 204], [34881, 204], [35181, 203], [35481, 203], [35781, 202], [36082, 201], [36382, 200], [36682, 200], [36982, 199], [37283, 198], [37583, 197], [37883, 197], [38183, 196], [38484, 195], [38784, 195], [39084, 194], [39384, 194], [39685, 192], [39985, 192], [40285, 191], [40585, 191], [40885, 190], [41186, 189], [41486, 188], [41786, 188], [42086, 188], [42387, 186], [42687, 186], [42987, 185], [43287, 185], [43588, 183], [43888, 183], [44188, 182], [44488, 182], [44789, 180], [45089, 180], [45389, 179], [45689, 179], [45989, 179], [46290, 177], [46590, 177], [46890, 176], [47190, 176], [47491, 174], [47791, 174], [48091, 173], [48391, 173], [48692, 171], [48992, 171], [49292, 171], [49592, 170], [49893, 169], [50193, 168], [50496, 162], [50796, 161], [51096, 161], [51396, 160], [51697, 159], [51997, 158]], "point": [185, 103]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-02.74|+00.82|+00.95"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [157, 99, 193, 135], "mask": [[29557, 37], [29857, 37], [30157, 36], [30457, 36], [30758, 34], [31058, 34], [31358, 34], [31658, 33], [31958, 33], [32259, 31], [32559, 31], [32859, 31], [33159, 30], [33460, 29], [33760, 28], [34060, 28], [34361, 26], [34662, 25], [34963, 23], [35264, 21], [35565, 18], [35867, 14], [36167, 12], [36465, 14], [36764, 17], [37063, 19], [37362, 21], [37661, 22], [37961, 23], [38261, 23], [38561, 23], [38862, 22], [39162, 22], [39463, 20], [39764, 18], [40066, 14], [40368, 10]], "point": [175, 116]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [2, 34, 295, 294], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25870, 224], [26170, 223], [26469, 224], [26768, 224], [27068, 224], [27367, 224], [27666, 225], [27965, 226], [28265, 225], [28564, 226], [28864, 225], [29163, 226], [29463, 226], [29763, 226], [30062, 63], [30135, 154], [30362, 60], [30437, 152], [30661, 59], [30740, 148], [30961, 58], [31041, 147], [31260, 58], [31342, 146], [31560, 57], [31643, 145], [31860, 56], [31944, 144], [32159, 57], [32244, 144], [32459, 56], [32545, 143], [32758, 57], [32846, 142], [33058, 57], [33146, 141], [33358, 56], [33446, 141], [33657, 57], [33747, 139], [33957, 57], [34047, 139], [34256, 58], [34347, 138], [34556, 58], [34647, 138], [34855, 59], [34947, 138], [35155, 59], [35247, 137], [35455, 59], [35547, 137], [35754, 61], [35847, 136], [36054, 61], [36146, 137], [36353, 62], [36446, 136], [36653, 62], [36746, 136], [36953, 63], [37045, 55], [37104, 77], [37252, 64], [37345, 53], [37406, 75], [37552, 65], [37644, 53], [37706, 74], [37851, 66], [37944, 53], [38007, 73], [38151, 67], [38243, 53], [38307, 72], [38450, 68], [38543, 53], [38608, 71], [38750, 69], [38842, 54], [38908, 71], [39050, 70], [39141, 54], [39208, 70], [39349, 72], [39440, 56], [39507, 71], [39649, 73], [39739, 57], [39807, 70], [39948, 75], [40038, 58], [40107, 70], [40248, 76], [40337, 60], [40406, 70], [40548, 78], [40636, 62], [40705, 71], [40847, 81], [40934, 66], [41002, 73], [41147, 228], [41446, 228], [41746, 228], [42045, 229], [42345, 228], [42645, 228], [42944, 228], [43244, 228], [43543, 228], [43843, 228], [44143, 227], [44442, 228], [44742, 227], [45041, 108], [45151, 118], [45341, 101], [45458, 110], [45640, 95], [45765, 103], [45940, 88], [46072, 96], [46240, 86], [46374, 93], [46539, 85], [46676, 91], [46839, 83], [46978, 88], [47138, 82], [47280, 86], [47438, 79], [47583, 82], [47738, 77], [47885, 80], [48037, 76], [48187, 77], [48337, 74], [48489, 75], [48636, 73], [48791, 72], [48936, 72], [49092, 71], [49235, 72], [49393, 70], [49535, 71], [49694, 68], [49835, 70], [49995, 67], [50134, 70], [50296, 65], [50434, 68], [50598, 60], [50733, 68], [50899, 58], [51033, 67], [51200, 57], [51333, 66], [51501, 55], [51632, 64], [51697, 1], [51802, 54], [51932, 64], [52103, 52], [52231, 65], [52531, 64], [52830, 64], [53130, 63], [53430, 62], [53729, 63], [54029, 63], [54328, 63], [54628, 63], [54927, 63], [55227, 63], [55527, 62], [55826, 63], [56126, 62], [56425, 63], [56725, 62], [57025, 62], [57324, 63], [57624, 62], [57923, 63], [58223, 62], [58522, 63], [58822, 62], [59122, 62], [59421, 63], [59721, 63], [60020, 64], [60320, 64], [60620, 64], [60919, 65], [61219, 65], [61518, 66], [61818, 66], [62117, 67], [62417, 67], [62717, 67], [63016, 68], [63316, 68], [63615, 69], [63915, 69], [64215, 69], [64514, 70], [64814, 70], [65113, 71], [65413, 71], [65712, 72], [66012, 72], [66312, 73], [66611, 75], [66911, 75], [67210, 77], [67510, 76], [67810, 76], [68109, 77], [68409, 77], [68708, 78], [69008, 77], [69307, 78], [69607, 78], [69907, 78], [70206, 79], [70506, 79], [70805, 79], [71105, 79], [71405, 79], [71704, 80], [72004, 80], [72303, 80], [72603, 80], [72902, 81], [73202, 81], [73503, 80], [73804, 78], [74105, 77], [74405, 77], [74706, 76], [75007, 75], [75308, 74], [75609, 72], [75910, 71], [76211, 70], [76512, 69], [76813, 68], [77113, 67], [77414, 66], [77715, 65], [78016, 64], [78317, 63], [78618, 61], [78919, 60], [79220, 59], [79521, 58], [79822, 57], [80122, 56], [80423, 55], [80724, 54], [81025, 53], [81326, 52], [81627, 51], [81928, 49], [82229, 48], [82530, 47], [82831, 46], [83131, 46], [83432, 44], [83733, 43], [84034, 42], [84335, 41], [84636, 40], [84937, 38], [85238, 37], [85539, 36], [85839, 36], [86140, 38], [86441, 39], [86742, 39], [87043, 31], [87076, 5], [87345, 29], [87377, 2], [87649, 25], [87953, 20]], "point": [148, 150]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-02.78|+01.80|+00.83"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 42, 299, 279], "mask": [[12329, 103], [12433, 146], [12628, 251], [12928, 251], [13228, 251], [13528, 252], [13828, 252], [14128, 252], [14428, 252], [14727, 253], [15027, 253], [15327, 253], [15627, 254], [15927, 254], [16227, 254], [16527, 254], [16826, 255], [17126, 255], [17426, 256], [17726, 256], [18026, 256], [18326, 256], [18626, 256], [18925, 257], [19225, 257], [19525, 258], [19825, 258], [20125, 258], [20425, 258], [20725, 258], [21024, 259], [21324, 259], [21624, 260], [21924, 260], [22224, 260], [22524, 260], [22824, 260], [23123, 261], [23423, 262], [23723, 262], [24023, 262], [24323, 262], [24623, 262], [24923, 262], [25222, 263], [25522, 264], [25822, 264], [26122, 264], [26422, 264], [26722, 264], [27022, 264], [27321, 265], [27621, 266], [27921, 266], [28221, 266], [28521, 266], [28821, 266], [29121, 266], [29420, 268], [29720, 268], [30020, 268], [30320, 268], [30620, 268], [30920, 268], [31220, 268], [31519, 270], [31819, 270], [32119, 270], [32419, 270], [32719, 270], [33019, 270], [33319, 270], [33618, 272], [33918, 272], [34218, 272], [34518, 272], [34818, 272], [35118, 272], [35418, 273], [35717, 274], [36017, 274], [36317, 274], [36617, 274], [36917, 274], [37217, 274], [37517, 275], [37816, 276], [38116, 276], [38416, 276], [38716, 276], [39016, 276], [39316, 276], [39616, 277], [39916, 277], [40215, 278], [40515, 278], [40815, 278], [41115, 278], [41415, 279], [41715, 279], [42015, 279], [42314, 280], [42614, 280], [42914, 280], [43214, 280], [43514, 281], [43814, 281], [44114, 281], [44413, 282], [44713, 282], [45013, 136], [45151, 144], [45313, 129], [45458, 137], [45613, 122], [45765, 131], [45913, 115], [46072, 124], [46213, 113], [46374, 122], [46512, 112], [46676, 120], [46812, 110], [46978, 118], [47112, 108], [47280, 116], [47412, 105], [47583, 114], [47712, 103], [47885, 112], [48012, 101], [48187, 110], [48312, 99], [48489, 108], [48611, 98], [48791, 106], [48911, 97], [49092, 105], [49211, 96], [49393, 104], [49511, 95], [49694, 104], [49811, 94], [49995, 103], [50111, 93], [50296, 102], [50411, 91], [50598, 100], [50710, 91], [50899, 99], [51010, 90], [51200, 98], [51310, 89], [51501, 97], [51610, 88], [51802, 97], [51910, 87], [52103, 96], [52210, 86], [52404, 95], [52510, 85], [52705, 94], [52809, 85], [53006, 93], [53109, 84], [53307, 92], [53409, 83], [53608, 91], [53709, 83], [53908, 92], [54009, 83], [54208, 92], [54309, 82], [54509, 91], [54609, 82], [54809, 91], [54908, 82], [55110, 90], [55208, 82], [55410, 90], [55508, 81], [55711, 89], [55808, 81], [56011, 89], [56108, 80], [56312, 88], [56408, 80], [56612, 88], [56708, 79], [56913, 87], [57007, 80], [57213, 87], [57307, 80], [57513, 87], [57607, 79], [57814, 86], [57907, 79], [58114, 86], [58207, 78], [58415, 85], [58507, 78], [58715, 85], [58807, 77], [59016, 84], [59106, 78], [59316, 84], [59406, 78], [59616, 84], [59706, 78], [59916, 84], [60006, 78], [60216, 84], [60306, 78], [60516, 84], [60606, 78], [60816, 84], [60906, 78], [61116, 84], [61205, 79], [61416, 84], [61505, 79], [61716, 84], [61805, 79], [62016, 84], [62105, 79], [62316, 84], [62405, 79], [62616, 84], [62705, 79], [62916, 84], [63005, 79], [63216, 84], [63305, 79], [63516, 84], [63604, 80], [63816, 84], [63904, 80], [64116, 84], [64204, 80], [64416, 84], [64504, 80], [64716, 84], [64804, 80], [65016, 84], [65104, 80], [65316, 84], [65404, 80], [65616, 84], [65703, 81], [65916, 84], [66003, 81], [66216, 84], [66303, 82], [66515, 85], [66603, 83], [66814, 86], [66903, 83], [67114, 86], [67203, 84], [67413, 87], [67503, 84], [67713, 87], [67802, 86], [68012, 88], [68102, 86], [68312, 88], [68402, 87], [68611, 89], [68702, 87], [68911, 89], [69002, 88], [69210, 90], [69302, 88], [69510, 90], [69602, 89], [69809, 91], [69901, 91], [70108, 92], [70201, 91], [70408, 92], [70501, 92], [70707, 93], [70801, 92], [71007, 93], [71101, 93], [71306, 94], [71401, 93], [71606, 94], [71701, 94], [71905, 190], [72205, 191], [72504, 193], [72803, 194], [73103, 195], [73402, 197], [73701, 199], [74000, 201], [74299, 203], [74598, 205], [74897, 207], [75196, 209], [75495, 211], [75794, 213], [76093, 215], [76392, 217], [76691, 219], [76990, 221], [77289, 223], [77588, 225], [77887, 227], [78186, 229], [78485, 231], [78784, 233], [79083, 235], [79382, 238], [79680, 240], [79980, 240], [80280, 240], [80580, 240], [80880, 240], [81180, 240], [81480, 241], [81779, 243], [82078, 244], [82378, 245], [82677, 246], [82977, 247], [83276, 248], [83576, 124]], "point": [148, 150]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-02.74|+00.82|+00.95", "placeStationary": true, "receptacleObjectId": "Cabinet|-02.78|+01.80|+00.83"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 275], "mask": [[0, 2], [300, 3], [600, 4], [900, 4], [1200, 5], [1500, 6], [1800, 7], [2100, 7], [2400, 8], [2700, 9], [3000, 9], [3300, 10], [3599, 12], [3898, 13], [4197, 15], [4497, 16], [4796, 17], [5095, 19], [5395, 20], [5694, 21], [5993, 23], [6292, 25], [6592, 25], [6891, 27], [7190, 29], [7490, 29], [7789, 31], [8088, 33], [8387, 34], [8687, 35], [8986, 37], [9285, 38], [9585, 39], [9884, 41], [10183, 42], [10482, 44], [10782, 45], [11081, 46], [11380, 48], [11680, 49], [11979, 50], [12278, 52], [12577, 53], [12877, 53], [12933, 243], [13177, 53], [13233, 242], [13478, 52], [13533, 242], [13778, 52], [13833, 241], [14078, 52], [14133, 240], [14378, 51], [14432, 241], [14678, 51], [14732, 240], [14978, 51], [15032, 239], [15278, 51], [15332, 238], [15579, 50], [15632, 238], [15879, 50], [15932, 237], [16179, 50], [16232, 236], [16479, 49], [16532, 236], [16779, 49], [16831, 236], [17079, 49], [17131, 235], [17379, 49], [17431, 234], [17680, 48], [17731, 234], [17980, 48], [18031, 233], [18280, 48], [18331, 232], [18580, 47], [18631, 232], [18880, 47], [18930, 232], [19180, 47], [19230, 231], [19481, 46], [19530, 230], [19781, 46], [19830, 230], [20081, 46], [20130, 229], [20381, 46], [20430, 228], [20681, 45], [20730, 227], [20981, 45], [21029, 228], [21281, 45], [21329, 227], [21582, 44], [21629, 227], [21882, 44], [21929, 227], [22182, 44], [22229, 227], [22482, 44], [22529, 227], [22782, 43], [22829, 227], [23082, 43], [23382, 43], [23683, 42], [23983, 42], [24283, 42], [24583, 42], [24628, 254], [24883, 42], [24928, 253], [25183, 41], [25228, 252], [25483, 41], [25527, 252], [25784, 40], [25827, 251], [26084, 40], [26127, 250], [26384, 40], [26427, 249], [26684, 40], [26727, 248], [26984, 40], [27027, 247], [27284, 39], [27327, 246], [27585, 38], [27627, 245], [27885, 38], [27926, 245], [28185, 38], [28226, 244], [28485, 38], [28526, 243], [28785, 38], [28826, 242], [29085, 38], [29126, 241], [29385, 37], [29426, 240], [29686, 36], [29726, 239], [29986, 36], [30025, 239], [30286, 36], [30325, 238], [30586, 36], [30625, 237], [30886, 36], [30925, 236], [31186, 36], [31225, 235], [31486, 35], [31525, 235], [31787, 34], [31825, 235], [32087, 34], [32124, 236], [32387, 34], [32424, 236], [32687, 34], [32724, 236], [32987, 34], [33024, 236], [33287, 34], [33324, 236], [33587, 33], [33624, 236], [33888, 32], [33924, 237], [34188, 32], [34224, 237], [34488, 32], [34523, 238], [34788, 32], [34823, 238], [35088, 32], [35123, 238], [35388, 32], [35423, 238], [35688, 32], [35723, 238], [35989, 30], [36023, 238], [36289, 30], [36323, 239], [36589, 30], [36622, 240], [36889, 30], [36922, 240], [37189, 30], [37222, 240], [37489, 30], [37522, 240], [37790, 29], [37822, 240], [38090, 28], [38122, 240], [38390, 28], [38422, 240], [38690, 28], [38721, 241], [38990, 28], [39021, 242], [39290, 28], [39321, 242], [39590, 28], [39621, 242], [39891, 27], [39921, 242], [40191, 26], [40221, 242], [40491, 26], [40521, 242], [40791, 26], [40821, 242], [41091, 26], [41120, 243], [41391, 26], [41420, 244], [41691, 26], [41720, 244], [41992, 25], [42020, 244], [42292, 24], [42320, 244], [42592, 24], [42620, 244], [42892, 24], [42920, 244], [43192, 24], [43219, 245], [43492, 24], [43519, 245], [43792, 24], [43819, 245], [44093, 23], [44119, 246], [44393, 22], [44419, 246], [44693, 22], [44719, 246], [44993, 22], [45019, 130], [45151, 114], [45293, 22], [45318, 124], [45458, 107], [45593, 22], [45618, 117], [45765, 100], [45894, 21], [45918, 110], [46072, 93], [46194, 21], [46218, 108], [46374, 91], [46494, 21], [46518, 106], [46676, 90], [46794, 20], [46818, 104], [46978, 88], [47094, 20], [47118, 102], [47280, 86], [47394, 20], [47418, 99], [47583, 83], [47694, 20], [47717, 98], [47885, 81], [47995, 19], [48017, 96], [48187, 79], [48295, 19], [48317, 94], [48489, 77], [48595, 19], [48617, 92], [48791, 75], [48895, 18], [48917, 91], [49092, 74], [49195, 18], [49217, 90], [49393, 74], [49495, 18], [49517, 89], [49694, 73], [49795, 18], [49816, 89], [49995, 72], [50096, 17], [50116, 88], [50296, 71], [50396, 17], [50416, 86], [50598, 69], [50696, 17], [50716, 85], [50899, 68], [50996, 16], [51016, 84], [51200, 67], [51296, 16], [51316, 83], [51501, 66], [51596, 16], [51616, 82], [51802, 66], [51896, 16], [51916, 81], [52103, 65], [52197, 15], [52215, 81], [52404, 64], [52497, 15], [52515, 80], [52705, 63], [52797, 15], [52815, 79], [53006, 62], [53097, 14], [53115, 78], [53307, 61], [53397, 14], [53415, 77], [53608, 60], [53697, 14], [53715, 77], [53908, 60], [53997, 14], [54015, 77], [54208, 61], [54298, 13], [54314, 77], [54509, 60], [54598, 13], [54614, 77], [54809, 60], [54898, 13], [54914, 76], [55110, 59], [55198, 12], [55214, 76], [55410, 59], [55498, 12], [55514, 75], [55711, 58], [55798, 12], [55814, 75], [56011, 58], [56099, 11], [56114, 74], [56312, 57], [56399, 11], [56413, 75], [56612, 57], [56699, 11], [56713, 74], [56913, 57], [56999, 11], [57013, 74], [57213, 57], [57299, 11], [57313, 74], [57513, 57], [57599, 10], [57613, 73], [57814, 56], [57899, 10], [57913, 73], [58114, 56], [58200, 9], [58213, 72], [58415, 55], [58500, 9], [58513, 72], [58715, 55], [58800, 9], [58812, 72], [59016, 54], [59100, 9], [59112, 72], [59316, 55], [59400, 9], [59412, 72], [59616, 55], [59700, 8], [59712, 72], [59916, 55], [60000, 8], [60012, 72], [60216, 55], [60300, 8], [60312, 72], [60516, 55], [60600, 8], [60612, 72], [60816, 55], [60900, 8], [60911, 73], [61116, 55], [61200, 8], [61211, 73], [61416, 55], [61500, 8], [61511, 73], [61716, 55], [61800, 7], [61811, 73], [62016, 56], [62100, 7], [62111, 73], [62316, 56], [62400, 7], [62411, 73], [62616, 56], [62700, 7], [62711, 73], [62916, 56], [63000, 7], [63010, 74], [63216, 56], [63300, 7], [63310, 74], [63516, 56], [63600, 7], [63610, 74], [63816, 56], [63900, 6], [63910, 74], [64116, 56], [64200, 6], [64210, 74], [64416, 57], [64500, 6], [64510, 74], [64716, 57], [64800, 6], [64810, 74], [65016, 57], [65100, 6], [65110, 74], [65316, 57], [65400, 6], [65409, 75], [65616, 57], [65700, 6], [65709, 75], [65916, 57], [66000, 5], [66009, 75], [66216, 57], [66300, 5], [66309, 76], [66515, 58], [66600, 5], [66609, 77], [66814, 59], [66900, 5], [66909, 77], [67114, 60], [67200, 5], [67209, 78], [67413, 61], [67500, 5], [67508, 79], [67713, 61], [67800, 5], [67808, 80], [68012, 62], [68100, 5], [68108, 80], [68312, 62], [68400, 4], [68408, 81], [68611, 63], [68700, 4], [68708, 81], [68911, 63], [69000, 4], [69008, 82], [69210, 64], [69300, 4], [69308, 82], [69510, 65], [69600, 4], [69607, 84], [69809, 66], [69900, 4], [69907, 85], [70108, 67], [70200, 4], [70207, 85], [70408, 67], [70500, 3], [70507, 86], [70707, 68], [70800, 3], [70807, 86], [71007, 68], [71100, 3], [71107, 87], [71306, 69], [71400, 3], [71407, 87], [71606, 69], [71700, 3], [71707, 88], [71905, 70], [72000, 3], [72006, 89], [72205, 71], [72300, 3], [72306, 90], [72504, 72], [72600, 2], [72606, 91], [72803, 73], [72900, 2], [72906, 91], [73103, 73], [73200, 2], [73206, 92], [73402, 74], [73500, 2], [73506, 93], [73701, 75], [73800, 2], [73806, 94], [74000, 76], [74100, 2], [74105, 96], [74299, 77], [74400, 2], [74405, 97], [74598, 79], [74700, 1], [74705, 98], [74897, 80], [75000, 1], [75005, 99], [75196, 81], [75300, 1], [75305, 100], [75495, 82], [75600, 1], [75605, 101], [75794, 83], [75900, 1], [75905, 102], [76093, 84], [76200, 1], [76205, 103], [76392, 85], [76500, 1], [76504, 105], [76691, 86], [76804, 106], [76990, 88], [77104, 107], [77289, 89], [77404, 108], [77588, 90], [77704, 109], [77887, 91], [78004, 110], [78186, 92], [78304, 111], [78485, 93], [78603, 113], [78784, 94], [78903, 114], [79083, 95], [79203, 115], [79382, 98], [79503, 117], [79680, 102], [79803, 117], [79980, 105], [80103, 117], [80280, 108], [80403, 117], [80580, 111], [80702, 118], [80880, 114], [81002, 118], [81180, 117], [81302, 118], [81480, 120], [81602, 119], [81779, 121], [81902, 120], [82078, 122], [82202, 120], [82378, 122]], "point": [149, 137]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-02.78|+01.80|+00.83"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 275], "mask": [[0, 2], [300, 3], [600, 4], [900, 4], [1200, 5], [1500, 6], [1800, 7], [2100, 7], [2400, 8], [2700, 9], [3000, 9], [3300, 10], [3599, 12], [3898, 13], [4197, 15], [4497, 16], [4796, 17], [5095, 19], [5395, 20], [5694, 21], [5993, 23], [6292, 25], [6592, 25], [6891, 27], [7190, 29], [7490, 29], [7789, 31], [8088, 33], [8387, 34], [8687, 35], [8986, 37], [9285, 38], [9585, 39], [9884, 41], [10183, 42], [10482, 44], [10782, 45], [11081, 46], [11380, 48], [11680, 49], [11979, 50], [12278, 52], [12577, 53], [12877, 53], [12933, 243], [13177, 53], [13233, 242], [13478, 52], [13533, 242], [13778, 52], [13833, 241], [14078, 52], [14133, 240], [14378, 51], [14432, 241], [14678, 51], [14732, 240], [14978, 51], [15032, 239], [15278, 51], [15332, 238], [15579, 50], [15632, 238], [15879, 50], [15932, 237], [16179, 50], [16232, 236], [16479, 49], [16532, 236], [16779, 49], [16831, 236], [17079, 49], [17131, 235], [17379, 49], [17431, 234], [17680, 48], [17731, 234], [17980, 48], [18031, 233], [18280, 48], [18331, 232], [18580, 47], [18631, 232], [18880, 47], [18930, 232], [19180, 47], [19230, 231], [19481, 46], [19530, 230], [19781, 46], [19830, 230], [20081, 46], [20130, 229], [20381, 46], [20430, 228], [20681, 45], [20730, 227], [20981, 45], [21029, 228], [21281, 45], [21329, 227], [21582, 44], [21629, 227], [21882, 44], [21929, 227], [22182, 44], [22229, 227], [22482, 44], [22529, 227], [22782, 43], [22829, 227], [23082, 43], [23382, 43], [23683, 42], [23983, 42], [24283, 42], [24583, 42], [24628, 254], [24883, 42], [24928, 253], [25183, 41], [25228, 252], [25483, 41], [25527, 252], [25784, 40], [25827, 251], [26084, 40], [26127, 250], [26384, 40], [26427, 249], [26684, 40], [26727, 248], [26984, 40], [27027, 247], [27284, 39], [27327, 246], [27585, 38], [27627, 245], [27885, 38], [27926, 245], [28185, 38], [28226, 244], [28485, 38], [28526, 243], [28785, 38], [28826, 242], [29085, 38], [29126, 241], [29385, 37], [29426, 240], [29686, 36], [29726, 239], [29986, 36], [30025, 239], [30286, 36], [30325, 238], [30586, 36], [30625, 237], [30886, 36], [30925, 236], [31186, 36], [31225, 235], [31486, 35], [31525, 235], [31787, 34], [31825, 235], [32087, 34], [32124, 236], [32387, 34], [32424, 236], [32687, 34], [32724, 236], [32987, 34], [33024, 236], [33287, 34], [33324, 236], [33587, 33], [33624, 236], [33888, 32], [33924, 237], [34188, 32], [34224, 237], [34488, 32], [34523, 238], [34788, 32], [34823, 238], [35088, 32], [35123, 238], [35388, 32], [35423, 238], [35688, 32], [35723, 238], [35989, 30], [36023, 238], [36289, 30], [36323, 239], [36589, 30], [36622, 240], [36889, 30], [36922, 240], [37189, 30], [37222, 240], [37489, 30], [37522, 240], [37790, 29], [37822, 240], [38090, 28], [38122, 240], [38390, 28], [38422, 240], [38690, 28], [38721, 241], [38990, 28], [39021, 242], [39290, 28], [39321, 242], [39590, 28], [39621, 242], [39891, 27], [39921, 242], [40191, 26], [40221, 242], [40491, 26], [40521, 242], [40791, 26], [40821, 242], [41091, 26], [41120, 243], [41391, 26], [41420, 244], [41691, 26], [41720, 244], [41992, 25], [42020, 244], [42292, 24], [42320, 244], [42592, 24], [42620, 244], [42892, 24], [42920, 244], [43192, 24], [43219, 245], [43492, 24], [43519, 245], [43792, 24], [43819, 245], [44093, 23], [44119, 246], [44393, 22], [44419, 246], [44693, 22], [44719, 246], [44993, 22], [45019, 246], [45293, 22], [45318, 247], [45593, 22], [45618, 247], [45894, 21], [45918, 247], [46194, 21], [46218, 247], [46494, 21], [46518, 248], [46794, 20], [46818, 248], [47094, 20], [47118, 248], [47394, 20], [47418, 248], [47694, 20], [47717, 249], [47995, 19], [48017, 249], [48295, 19], [48317, 249], [48595, 19], [48617, 249], [48895, 18], [48917, 249], [49195, 18], [49217, 250], [49495, 18], [49517, 250], [49795, 18], [49816, 251], [50096, 17], [50116, 251], [50396, 17], [50416, 251], [50696, 17], [50716, 251], [50996, 16], [51016, 251], [51296, 16], [51316, 251], [51596, 16], [51616, 252], [51896, 16], [51916, 252], [52197, 15], [52215, 253], [52497, 15], [52515, 253], [52797, 15], [52815, 253], [53097, 14], [53115, 253], [53397, 14], [53415, 253], [53697, 14], [53715, 253], [53997, 14], [54015, 254], [54298, 13], [54314, 255], [54598, 13], [54614, 255], [54898, 13], [54914, 255], [55198, 12], [55214, 255], [55498, 12], [55514, 255], [55798, 12], [55814, 255], [56099, 11], [56114, 255], [56399, 11], [56413, 256], [56699, 11], [56713, 257], [56999, 11], [57013, 257], [57299, 11], [57313, 257], [57599, 10], [57613, 257], [57899, 10], [57913, 257], [58200, 9], [58213, 257], [58500, 9], [58513, 257], [58800, 9], [58812, 258], [59100, 9], [59112, 259], [59400, 9], [59412, 259], [59700, 8], [59712, 259], [60000, 8], [60012, 259], [60300, 8], [60312, 259], [60600, 8], [60612, 259], [60900, 8], [60911, 260], [61200, 8], [61211, 260], [61500, 8], [61511, 260], [61800, 7], [61811, 261], [62100, 7], [62111, 261], [62400, 7], [62411, 261], [62700, 7], [62711, 261], [63000, 7], [63010, 262], [63300, 7], [63310, 262], [63600, 7], [63610, 262], [63900, 6], [63910, 262], [64200, 6], [64210, 157], [64408, 65], [64500, 6], [64510, 157], [64708, 65], [64800, 6], [64810, 157], [65008, 65], [65100, 6], [65110, 157], [65308, 65], [65400, 6], [65409, 158], [65608, 65], [65700, 6], [65709, 158], [65907, 66], [66000, 5], [66009, 159], [66207, 66], [66300, 5], [66309, 159], [66507, 66], [66600, 5], [66609, 159], [66807, 66], [66900, 5], [66909, 159], [67107, 67], [67200, 5], [67209, 159], [67407, 67], [67500, 5], [67508, 161], [67707, 67], [67800, 5], [67808, 161], [68006, 68], [68100, 5], [68108, 161], [68306, 68], [68400, 4], [68408, 161], [68606, 68], [68700, 4], [68708, 161], [68906, 68], [69000, 4], [69008, 162], [69206, 68], [69300, 4], [69308, 162], [69506, 69], [69600, 4], [69607, 163], [69806, 69], [69900, 4], [69907, 163], [70106, 69], [70200, 4], [70207, 164], [70405, 70], [70500, 3], [70507, 164], [70705, 70], [70800, 3], [70807, 164], [71005, 70], [71100, 3], [71107, 164], [71305, 70], [71400, 3], [71407, 164], [71605, 70], [71700, 3], [71707, 165], [71905, 70], [72000, 3], [72006, 166], [72204, 72], [72300, 3], [72306, 167], [72503, 73], [72600, 2], [72606, 168], [72803, 73], [72900, 2], [72906, 169], [73102, 74], [73200, 2], [73206, 171], [73400, 76], [73500, 2], [73506, 173], [73698, 78], [73800, 2], [73806, 175], [73996, 80], [74100, 2], [74105, 180], [74292, 84], [74400, 2], [74405, 179], [74593, 84], [74700, 1], [74705, 178], [74894, 83], [75000, 1], [75005, 177], [75195, 82], [75300, 1], [75305, 177], [75495, 82], [75600, 1], [75605, 177], [75795, 82], [75900, 1], [75905, 177], [76096, 81], [76200, 1], [76205, 176], [76396, 81], [76500, 1], [76504, 178], [76696, 81], [76804, 178], [76996, 82], [77104, 178], [77295, 83], [77404, 179], [77595, 83], [77704, 179], [77894, 84], [78004, 180], [78194, 84], [78304, 182], [78492, 86], [78603, 182], [78792, 86], [78903, 175], [79097, 81], [79203, 172], [79402, 78], [79503, 172], [79703, 79], [79803, 172], [80003, 82], [80103, 175], [80302, 86], [80403, 182], [80597, 94], [80702, 292], [81002, 295], [81302, 298], [81602, 298], [81902, 298], [82202, 298]], "point": [149, 137]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan25", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -2.0, "y": 0.9009992, "z": 1.25}, "object_poses": [{"objectName": "Potato_a3c14b3a", "position": {"x": -2.94762182, "y": 1.182501, "z": 1.9578644}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "DishSponge_eb848da8", "position": {"x": -2.724535, "y": 0.8231642, "z": 0.278}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_eb848da8", "position": {"x": -0.362648815, "y": 0.05606586, "z": 2.57297325}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_d1baac51", "position": {"x": -2.211642, "y": 0.823642, "z": 0.528825939}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_df0734fa", "position": {"x": -0.8170933, "y": 0.8247293, "z": 0.352121234}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_df0734fa", "position": {"x": -2.5815177, "y": 0.8247293, "z": 0.7895261}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_29dab58d", "position": {"x": -2.837208, "y": 1.14841235, "z": 2.39759}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Knife_298dd93a", "position": {"x": -1.15182948, "y": 0.849218249, "z": 0.278}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_298dd93a", "position": {"x": -2.375244, "y": 0.8492183, "z": 0.4262423}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_57da2f86", "position": {"x": -2.67158484, "y": 1.13871777, "z": 2.13375545}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_0dd786e0", "position": {"x": -0.2959527, "y": 0.129467249, "z": 2.67462254}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_0dd786e0", "position": {"x": -2.74435759, "y": 0.9944069, "z": 1.17865109}, "rotation": {"x": 0.0, "y": 269.999878, "z": 0.0}}, {"objectName": "Apple_0dd786e0", "position": {"x": -2.78199935, "y": 1.5185833, "z": 1.957865}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Fork_2c8fe53a", "position": {"x": -2.49167442, "y": 0.824148536, "z": 0.20387885}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_3943cbe8", "position": {"x": -2.00053287, "y": 0.169670582, "z": 0.4016961}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_e0764688", "position": {"x": -0.2442, "y": 0.8297, "z": 1.684}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_3a4a4d0d", "position": {"x": -2.905465, "y": 1.43355191, "z": 0.7089031}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_ccf3345d", "position": {"x": -0.531748533, "y": 0.8226794, "z": 2.09645438}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_2ee69558", "position": {"x": -2.72679329, "y": 0.8743741, "z": 2.30964565}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Potato_a3c14b3a", "position": {"x": -0.238357425, "y": 0.861, "z": 1.33212233}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_69e12ad1", "position": {"x": -2.81204367, "y": 1.43003523, "z": 0.265812725}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Plate_29dab58d", "position": {"x": -1.407882, "y": 0.130002677, "z": 0.362672359}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_77ce7117", "position": {"x": -0.341, "y": 0.696, "z": 1.194}, "rotation": {"x": 0.0, "y": 45.0000267, "z": 0.0}}, {"objectName": "Knife_298dd93a", "position": {"x": -2.66281223, "y": 0.819159746, "z": 0.557993948}, "rotation": {"x": 89.94766, "y": 300.034546, "z": 0.0}}, {"objectName": "Egg_68224885", "position": {"x": -2.744358, "y": 0.94571954, "z": 1.44387424}, "rotation": {"x": 0.0, "y": 89.99987, "z": 0.0}}, {"objectName": "Cup_61785f6c", "position": {"x": -2.73584533, "y": 0.823938131, "z": 0.953160763}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_d1baac51", "position": {"x": -1.87690437, "y": 0.823642, "z": 0.5084426}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b2f1312c", "position": {"x": -1.8365, "y": 0.7415, "z": 0.238453716}, "rotation": {"x": 0.0, "y": 55.91092, "z": 0.0}}, {"objectName": "PepperShaker_407d7c3b", "position": {"x": -2.504354, "y": 0.8185999, "z": 0.707708836}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_eb848da8", "position": {"x": -1.10540831, "y": 0.6980382, "z": 0.410965}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_db4f73f8", "position": {"x": -0.979999542, "y": 0.8554756, "z": 0.249999985}, "rotation": {"x": 1.80723919e-06, "y": 29.9999123, "z": 3.46336333e-06}}, {"objectName": "Spoon_df0734fa", "position": {"x": -1.87690437, "y": 0.8247293, "z": 0.51863426}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_57da2f86", "position": {"x": -2.83720732, "y": 1.13871777, "z": 2.133755}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_4c742e05", "position": {"x": -0.397978365, "y": 0.115337931, "z": 0.9141403}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 88041003, "scene_num": 25}, "task_id": "trial_T20190908_012806_077408", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A3F7G1FSFWQPLE_3OJSZ2ATDVD2WLEX6AX8F1333V157C", "high_descs": ["Turn left and take a step forward, then turn right to face the counter.", "Pick up the green cup off of the counter.", "Turn right and take a step forward, then turn left to face the microwave.", "Open the microwave and put the green cup inside in between the apple and egg, microwave the cup for a couple seconds then take the cup out of the microwave and close the microwave door.", "Look up at the upper cabinets.", "Open the cabinet above the microwave and put the heated green cup inside."], "task_desc": "Put a heated green cup in the upper cabinets.", "votes": [1, 1, 1]}, {"assignment_id": "A20FCMWP43CVIU_3634BBTX0RBRN2OIVDEES2KP643IFE", "high_descs": ["move to the left to face the counter top to the left of the microwave", "pick up the green cup from the counter top", "move to the left to face the microwave", "open microwave oven door, put apple inside, close door, cook apple, open door, pick up apple, close door", "look up to face cabinet directly above microwave", "open cabinet doors, put cup on bottom of cabinet, close doors"], "task_desc": "put heated cup into cabinet", "votes": [1, 0, 1]}, {"assignment_id": "A2IU5TX5S0FA1C_3JW0YLFXRWXBZJUIAJSTXHFXGIBWWH", "high_descs": ["Turn left, one step, turn right to face counter.", "Pick up green glass next to microwave.", "Turn right one step, turn left to face microwave.", "Place green glass in microwave.", "Take green glass out of microwave.", "Place green glass in cupboard over microwave."], "task_desc": "Place green glass in cupboard.", "votes": [0, 1, 1]}]}}