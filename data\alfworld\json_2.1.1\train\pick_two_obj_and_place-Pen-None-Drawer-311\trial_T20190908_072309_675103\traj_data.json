{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000283.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000284.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000285.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000286.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000287.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000288.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000289.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000290.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000291.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000292.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000293.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000294.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000295.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000296.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000297.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000298.png", "low_idx": 64}, {"high_idx": 7, "image_name": "000000299.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000300.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000301.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000302.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000303.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000304.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000305.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000306.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000307.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000308.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000309.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000310.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000311.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000312.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000313.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000314.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000315.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000316.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000317.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000318.png", "low_idx": 67}, {"high_idx": 7, "image_name": "000000319.png", "low_idx": 67}, {"high_idx": 7, "image_name": "000000320.png", "low_idx": 67}, {"high_idx": 7, "image_name": "000000321.png", "low_idx": 67}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Pen", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|7|13|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["pen"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Pen", [9.46939468, 9.46939468, 13.97877504, 13.97877504, 3.255920648, 3.255920648]], "coordinateReceptacleObjectId": ["DiningTable", [10.304288, 10.304288, 12.21562, 12.21562, 0.009562092, 0.009562092]], "forceVisible": true, "objectId": "Pen|+02.37|+00.81|+03.49"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|6|3|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["pen", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Pen", [9.46939468, 9.46939468, 13.97877504, 13.97877504, 3.255920648, 3.255920648]], "coordinateReceptacleObjectId": ["Drawer", [10.212, 10.212, 2.496, 2.496, 2.2896004, 2.2896004]], "forceVisible": true, "objectId": "Pen|+02.37|+00.81|+03.49", "receptacleObjectId": "Drawer|+02.55|+00.57|+00.62"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|7|13|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["pen"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Pen", [9.88684176, 9.88684176, 13.09719752, 13.09719752, 3.255920648, 3.255920648]], "coordinateReceptacleObjectId": ["DiningTable", [10.304288, 10.304288, 12.21562, 12.21562, 0.009562092, 0.009562092]], "forceVisible": true, "objectId": "Pen|+02.47|+00.81|+03.27"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|6|3|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["pen", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Pen", [9.88684176, 9.88684176, 13.09719752, 13.09719752, 3.255920648, 3.255920648]], "coordinateReceptacleObjectId": ["Drawer", [10.212, 10.212, 2.496, 2.496, 2.2896004, 2.2896004]], "forceVisible": true, "objectId": "Pen|+02.47|+00.81|+03.27", "receptacleObjectId": "Drawer|+02.55|+00.57|+00.62"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Pen|+02.37|+00.81|+03.49"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [81, 92, 88, 131], "mask": [[27388, 1], [27688, 1], [27987, 2], [28287, 2], [28587, 2], [28887, 2], [29186, 3], [29486, 3], [29786, 2], [30086, 2], [30386, 2], [30686, 2], [30986, 2], [31285, 3], [31585, 3], [31885, 2], [32185, 2], [32485, 2], [32785, 2], [33084, 3], [33384, 3], [33684, 2], [33984, 2], [34284, 2], [34584, 2], [34883, 3], [35183, 3], [35482, 4], [35782, 3], [36082, 3], [36382, 3], [36682, 3], [36982, 3], [37282, 3], [37581, 3], [37881, 3], [38181, 3], [38482, 2], [38782, 1], [39082, 1]], "point": [84, 110]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|+02.55|+00.57|+00.62"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [79, 142, 276, 193], "mask": [[42381, 193], [42680, 195], [42979, 197], [43279, 197], [43579, 198], [43879, 198], [44179, 197], [44480, 196], [44780, 195], [45080, 195], [45380, 194], [45681, 193], [45981, 192], [46281, 192], [46582, 190], [46882, 190], [47182, 189], [47482, 189], [47783, 187], [48083, 187], [48383, 186], [48683, 186], [48984, 184], [49284, 184], [49584, 183], [49885, 182], [50185, 181], [50485, 181], [50785, 181], [51086, 179], [51386, 179], [51686, 178], [51986, 178], [52287, 176], [52587, 176], [52887, 175], [53187, 175], [53488, 173], [53788, 173], [54088, 172], [54389, 171], [54689, 170], [54989, 170], [55289, 169], [55590, 168], [55890, 167], [56190, 167], [56490, 166], [56791, 165], [57091, 164], [57391, 164], [57692, 162]], "point": [177, 166]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Pen|+02.37|+00.81|+03.49", "placeStationary": true, "receptacleObjectId": "Drawer|+02.55|+00.57|+00.62"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [59, 142, 299, 260], "mask": [[42387, 181], [42686, 182], [42986, 182], [43286, 183], [43586, 183], [43885, 185], [44185, 185], [44485, 186], [44785, 186], [45084, 188], [45384, 188], [45684, 189], [45984, 189], [46283, 191], [46583, 191], [46883, 192], [47183, 192], [47482, 194], [47782, 194], [48082, 194], [48382, 195], [48681, 196], [48981, 197], [49281, 197], [49581, 198], [49880, 199], [50180, 200], [50480, 200], [50780, 201], [51079, 202], [51379, 203], [51679, 203], [51979, 204], [52278, 205], [52578, 205], [52878, 206], [53178, 206], [53477, 208], [53777, 208], [54077, 209], [54377, 209], [54676, 211], [54976, 211], [55276, 212], [55576, 212], [55875, 214], [56175, 214], [56475, 215], [56775, 215], [57074, 216], [57374, 217], [57674, 217], [57974, 218], [58273, 219], [58573, 220], [58873, 220], [59173, 221], [59472, 222], [59772, 223], [60072, 223], [60372, 224], [60671, 225], [60971, 226], [61271, 226], [61571, 226], [61870, 228], [62170, 228], [62464, 236], [62762, 238], [63061, 239], [63361, 239], [63660, 240], [63960, 240], [64260, 240], [64559, 241], [64859, 241], [65160, 240], [65460, 240], [65761, 239], [66061, 239], [66362, 238], [66662, 238], [66963, 237], [67263, 237], [67564, 236], [67864, 236], [68165, 235], [68465, 235], [68765, 235], [69066, 234], [69366, 234], [69667, 232], [69967, 231], [70268, 229], [70568, 229], [70869, 227], [71169, 226], [71470, 224], [71770, 223], [72071, 221], [72371, 220], [72672, 219], [72972, 218], [73273, 216], [73573, 215], [73873, 214], [74174, 212], [74474, 212], [74775, 210], [75075, 209], [75376, 207], [75676, 206], [75977, 204], [76277, 204], [76578, 20], [76630, 150], [76878, 20], [76900, 179], [77179, 19], [77312, 66], [77479, 10], [77616, 61], [77780, 9], [77920, 56]], "point": [179, 200]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|+02.55|+00.57|+00.62"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [59, 142, 299, 260], "mask": [[42387, 181], [42686, 182], [42986, 182], [43286, 183], [43586, 183], [43885, 185], [44185, 185], [44485, 186], [44785, 186], [45084, 188], [45384, 188], [45684, 189], [45984, 189], [46283, 191], [46583, 191], [46883, 192], [47183, 192], [47482, 194], [47782, 194], [48082, 194], [48382, 195], [48681, 196], [48981, 197], [49281, 197], [49581, 198], [49880, 199], [50180, 200], [50480, 200], [50780, 201], [51079, 202], [51379, 203], [51679, 203], [51979, 204], [52278, 205], [52578, 205], [52878, 206], [53178, 206], [53477, 208], [53777, 208], [54077, 209], [54377, 209], [54676, 211], [54976, 211], [55276, 42], [55319, 169], [55576, 42], [55619, 169], [55875, 43], [55919, 170], [56175, 43], [56219, 170], [56475, 43], [56519, 171], [56775, 42], [56819, 171], [57074, 43], [57119, 171], [57374, 43], [57419, 172], [57674, 43], [57719, 172], [57974, 43], [58019, 173], [58273, 44], [58319, 173], [58573, 44], [58619, 174], [58873, 44], [58918, 175], [59173, 44], [59218, 176], [59472, 45], [59518, 176], [59772, 44], [59818, 177], [60072, 44], [60118, 177], [60372, 44], [60418, 178], [60671, 45], [60718, 178], [60971, 226], [61271, 226], [61571, 226], [61870, 228], [62170, 228], [62464, 236], [62762, 238], [63061, 239], [63361, 239], [63660, 240], [63960, 240], [64260, 240], [64559, 241], [64859, 241], [65160, 240], [65460, 240], [65761, 239], [66061, 239], [66362, 238], [66662, 238], [66963, 237], [67263, 237], [67564, 236], [67864, 236], [68165, 235], [68465, 235], [68765, 235], [69066, 234], [69366, 234], [69667, 232], [69967, 231], [70268, 229], [70568, 229], [70869, 227], [71169, 226], [71470, 224], [71770, 223], [72071, 221], [72371, 220], [72672, 219], [72972, 218], [73273, 216], [73573, 215], [73873, 214], [74174, 212], [74474, 212], [74775, 210], [75075, 209], [75376, 207], [75676, 206], [75977, 204], [76277, 204], [76578, 202], [76878, 201], [77179, 199], [77479, 198], [77780, 196]], "point": [179, 200]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Pen|+02.47|+00.81|+03.27"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [142, 144, 145, 177], "mask": [[43044, 1], [43344, 1], [43643, 3], [43943, 3], [44243, 2], [44543, 2], [44843, 2], [45143, 2], [45443, 2], [45743, 2], [46043, 2], [46343, 2], [46643, 2], [46943, 2], [47243, 2], [47543, 2], [47843, 2], [48143, 2], [48443, 2], [48743, 2], [49043, 2], [49343, 2], [49642, 3], [49942, 3], [50242, 3], [50542, 3], [50842, 3], [51142, 3], [51442, 3], [51742, 3], [52042, 3], [52342, 3], [52643, 1], [52943, 1]], "point": [143, 159]}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|+02.55|+00.57|+00.62"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [79, 142, 276, 193], "mask": [[42381, 193], [42680, 195], [42979, 197], [43279, 197], [43579, 198], [43879, 198], [44179, 197], [44480, 196], [44780, 195], [45080, 195], [45380, 194], [45681, 193], [45981, 192], [46281, 192], [46582, 190], [46882, 190], [47182, 189], [47482, 189], [47783, 187], [48083, 187], [48383, 186], [48683, 186], [48984, 184], [49284, 184], [49584, 183], [49885, 182], [50185, 181], [50485, 181], [50785, 181], [51086, 179], [51386, 179], [51686, 178], [51986, 178], [52287, 176], [52587, 176], [52887, 175], [53187, 175], [53488, 173], [53788, 173], [54088, 172], [54389, 171], [54689, 170], [54989, 170], [55289, 169], [55590, 168], [55890, 167], [56190, 167], [56490, 166], [56791, 165], [57091, 164], [57391, 164], [57692, 162]], "point": [177, 166]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Pen|+02.47|+00.81|+03.27", "placeStationary": true, "receptacleObjectId": "Drawer|+02.55|+00.57|+00.62"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [59, 142, 299, 260], "mask": [[42387, 181], [42686, 182], [42986, 182], [43286, 183], [43586, 183], [43885, 185], [44185, 185], [44485, 186], [44785, 186], [45084, 188], [45384, 188], [45684, 189], [45984, 189], [46283, 191], [46583, 191], [46883, 192], [47183, 192], [47482, 194], [47782, 194], [48082, 194], [48382, 195], [48681, 196], [48981, 197], [49281, 197], [49581, 198], [49880, 199], [50180, 200], [50480, 200], [50780, 201], [51079, 202], [51379, 203], [51679, 203], [51979, 204], [52278, 205], [52578, 205], [52878, 206], [53178, 206], [53477, 208], [53777, 208], [54077, 209], [54377, 209], [54676, 211], [54976, 211], [55276, 42], [55319, 169], [55576, 42], [55619, 169], [55875, 43], [55919, 170], [56175, 43], [56219, 170], [56475, 43], [56519, 171], [56775, 42], [56819, 171], [57074, 43], [57119, 171], [57374, 43], [57419, 172], [57674, 43], [57719, 172], [57974, 43], [58019, 173], [58273, 44], [58319, 173], [58573, 44], [58619, 174], [58873, 44], [58918, 175], [59173, 44], [59218, 176], [59472, 45], [59518, 176], [59772, 44], [59818, 177], [60072, 44], [60118, 177], [60372, 44], [60418, 178], [60671, 45], [60718, 178], [60971, 226], [61271, 226], [61571, 226], [61870, 228], [62170, 228], [62464, 236], [62762, 238], [63061, 239], [63361, 239], [63660, 240], [63960, 240], [64260, 240], [64559, 241], [64859, 241], [65160, 240], [65460, 240], [65761, 239], [66061, 239], [66362, 238], [66662, 238], [66963, 237], [67263, 237], [67564, 236], [67864, 236], [68165, 235], [68465, 235], [68765, 235], [69066, 234], [69366, 234], [69667, 232], [69967, 231], [70268, 229], [70568, 229], [70869, 227], [71169, 226], [71470, 224], [71770, 223], [72071, 221], [72371, 220], [72672, 219], [72972, 218], [73273, 216], [73573, 215], [73873, 214], [74174, 212], [74474, 212], [74775, 210], [75075, 209], [75376, 207], [75676, 206], [75977, 204], [76277, 204], [76578, 20], [76630, 150], [76878, 20], [76930, 149], [77179, 19], [77312, 66], [77479, 10], [77617, 60], [77780, 9], [77920, 56]], "point": [179, 200]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|+02.55|+00.57|+00.62"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [59, 142, 299, 260], "mask": [[42387, 181], [42686, 182], [42986, 182], [43286, 183], [43586, 183], [43885, 185], [44185, 185], [44485, 186], [44785, 186], [45084, 188], [45384, 188], [45684, 189], [45984, 189], [46283, 191], [46583, 191], [46883, 192], [47183, 192], [47482, 194], [47782, 194], [48082, 194], [48382, 195], [48681, 196], [48981, 197], [49281, 197], [49581, 198], [49880, 199], [50180, 200], [50480, 200], [50780, 201], [51079, 202], [51379, 203], [51679, 203], [51979, 204], [52278, 205], [52578, 205], [52878, 206], [53178, 35], [53214, 170], [53477, 36], [53515, 170], [53777, 36], [53815, 170], [54077, 36], [54115, 171], [54377, 37], [54415, 171], [54676, 38], [54716, 171], [54976, 38], [55016, 171], [55276, 38], [55316, 2], [55319, 169], [55576, 38], [55616, 2], [55619, 169], [55875, 39], [55916, 2], [55919, 170], [56175, 39], [56216, 2], [56219, 170], [56475, 40], [56516, 2], [56519, 171], [56775, 40], [56819, 171], [57074, 41], [57119, 171], [57374, 41], [57419, 172], [57674, 41], [57719, 172], [57974, 41], [58019, 173], [58273, 42], [58319, 173], [58573, 42], [58619, 174], [58873, 42], [58918, 175], [59173, 43], [59218, 176], [59472, 44], [59518, 176], [59772, 44], [59818, 177], [60072, 44], [60118, 177], [60372, 44], [60418, 178], [60671, 45], [60718, 178], [60971, 226], [61271, 226], [61571, 226], [61870, 228], [62170, 228], [62464, 236], [62762, 238], [63061, 239], [63361, 239], [63660, 240], [63960, 240], [64260, 240], [64559, 241], [64859, 241], [65160, 240], [65460, 240], [65761, 239], [66061, 239], [66362, 238], [66662, 238], [66963, 237], [67263, 237], [67564, 236], [67864, 236], [68165, 235], [68465, 235], [68765, 235], [69066, 234], [69366, 234], [69667, 232], [69967, 231], [70268, 229], [70568, 229], [70869, 227], [71169, 226], [71470, 224], [71770, 223], [72071, 221], [72371, 220], [72672, 219], [72972, 218], [73273, 216], [73573, 215], [73873, 214], [74174, 212], [74474, 212], [74775, 210], [75075, 209], [75376, 207], [75676, 206], [75977, 204], [76277, 204], [76578, 202], [76878, 201], [77179, 199], [77479, 198], [77780, 196]], "point": [179, 200]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan311", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": 1.0, "y": 0.9101201, "z": 0.75}, "object_poses": [{"objectName": "CellPhone_73ef3690", "position": {"x": -0.749808, "y": 0.913408458, "z": 4.35}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_73ef3690", "position": {"x": -0.8811438, "y": 0.913408458, "z": 4.53366375}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_2d1adb92", "position": {"x": 2.36734867, "y": 0.8130433, "z": 3.71508837}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_2d1adb92", "position": {"x": 2.54577255, "y": 0.751524746, "z": -1.73182178}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_2d1adb92", "position": {"x": 2.47171044, "y": 0.813980162, "z": 3.27429938}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "KeyChain_81ebd795", "position": {"x": -0.8811438, "y": 0.9143347, "z": 4.90099144}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "RemoteControl_787808a4", "position": {"x": -0.749808, "y": 0.9147331, "z": 4.53366375}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "RemoteControl_787808a4", "position": {"x": 2.44725227, "y": 0.747106135, "z": -1.58802092}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "AlarmClock_64d4017f", "position": {"x": -0.946811736, "y": 0.9215688, "z": 4.166336}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "AlarmClock_64d4017f", "position": {"x": 2.54577255, "y": 0.7539418, "z": -1.30041909}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_098867ef", "position": {"x": -0.815475941, "y": 0.911498964, "z": 4.71732759}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_098867ef", "position": {"x": 2.275018, "y": 0.3978147, "z": -0.594486952}, "rotation": {"x": 0.0, "y": 269.999817, "z": 0.0}}, {"objectName": "Book_4c88af94", "position": {"x": -1.07318592, "y": 0.713601649, "z": 2.25661373}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "AlarmClock_64d4017f", "position": {"x": 2.47171044, "y": 0.8154603, "z": 3.053905}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_73ef3690", "position": {"x": 2.26298714, "y": 0.8073, "z": 3.27429938}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_65828e43", "position": {"x": 2.78479528, "y": 0.809822559, "z": 2.83351064}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_e4e177ec", "position": {"x": 2.66068769, "y": 0.458984017, "z": 0.624}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "KeyChain_81ebd795", "position": {"x": 2.77032924, "y": 0.8438358, "z": 0.7551403}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "RemoteControl_787808a4", "position": {"x": 2.53340769, "y": 0.460222185, "z": 0.5260799}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_098867ef", "position": {"x": -0.5946238, "y": 0.7123361, "z": 1.973788}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pencil_172c7898", "position": {"x": 2.77032924, "y": 0.8463409, "z": 0.49285984}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pillow_1f84c627", "position": {"x": -0.116061687, "y": 0.785215139, "z": 1.6909622}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pillow_e745bb37", "position": {"x": -1.07318592, "y": 0.785215139, "z": 1.973788}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CD_fbb244a0", "position": {"x": 2.36492324, "y": 0.3994214, "z": -0.3331275}, "rotation": {"x": 0.0, "y": 269.999817, "z": 0.0}}, {"objectName": "Pen_2d1adb92", "position": {"x": 2.36734867, "y": 0.813980162, "z": 3.49469376}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_34663f02", "position": {"x": 2.26298714, "y": 0.8053905, "z": 2.83351064}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3232693139, "scene_num": 311}, "task_id": "trial_T20190908_072309_675103", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "AKW57KYG90X61_3CFVK00FWO2XUA3DFN8NQCZ4UKV6L0", "high_descs": ["move forward to the table ", "pick up a pen from the table", "turn right and move to the tv stand ", "place the pen in the drawer under the tv stand", "move left to the dining table", "pick up a pen on the table", "move right to the drawer under the tv stand", "place the pen in it"], "task_desc": "place two pens in the drawer under the tv stand", "votes": [1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3QFUFYSY91WXFVP3AOCAY1A61W2F4G", "high_descs": ["Walk across the room, then turn right and walk up to the table.", "Pick up the black pen off of the table.", "Turn around and walk forward, then hang a left and walk to the white dresser with the television on it.", "Open the top drawer of the dresser and put the pen inside, then close the drawer.", "Turn left and walk over to the table.", "Pick up the black pen off of the table.", "Walk back over to the white dresser with the television on it.", "Open the top drawer of the dresser and put the pen inside, then close the drawer."], "task_desc": "Move two pens into a drawer.", "votes": [1, 1]}, {"assignment_id": "A3A0VPWPASCO9J_3X65QVEQI34MARCEPELQCCREQ47CLV", "high_descs": ["Go to the table on the right.", "Pick up the pen between the other two pens on the table.", "Take the pen to the TV stand.", "Put the pen in the top drawer. ", "Go back to the table across the room.", "Pick up the pen by the cell phone.", "Take the pen to the TV stand.", "Put the pen in the top drawer of the TV stand."], "task_desc": "Put two pens in the top drawer of the TV stand.", "votes": [1, 1]}]}}