
(define (problem plan_trial_T20190908_132827_840235)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Box_bar__plus_00_dot_03_bar__plus_00_dot_65_bar__plus_00_dot_27 - object
        CreditCard_bar__plus_00_dot_11_bar__plus_00_dot_47_bar__plus_00_dot_44 - object
        CreditCard_bar__plus_01_dot_60_bar__plus_00_dot_76_bar__minus_00_dot_06 - object
        CreditCard_bar__minus_01_dot_43_bar__plus_00_dot_34_bar__plus_00_dot_64 - object
        FloorLamp_bar__plus_01_dot_23_bar__plus_00_dot_00_bar__plus_03_dot_80 - object
        HousePlant_bar__minus_01_dot_78_bar__plus_00_dot_48_bar__plus_01_dot_97 - object
        KeyChain_bar__minus_00_dot_30_bar__plus_00_dot_48_bar__plus_00_dot_44 - object
        KeyChain_bar__minus_01_dot_19_bar__plus_00_dot_34_bar__plus_00_dot_24 - object
        KeyChain_bar__minus_01_dot_35_bar__plus_00_dot_34_bar__plus_00_dot_64 - object
        Laptop_bar__plus_01_dot_53_bar__plus_00_dot_76_bar__plus_00_dot_24 - object
        Laptop_bar__minus_00_dot_30_bar__plus_00_dot_48_bar__minus_00_dot_09 - object
        Laptop_bar__minus_01_dot_27_bar__plus_00_dot_33_bar__plus_00_dot_85 - object
        LightSwitch_bar__minus_02_dot_00_bar__plus_01_dot_33_bar__plus_03_dot_92 - object
        Painting_bar__minus_00_dot_10_bar__plus_01_dot_58_bar__plus_04_dot_05 - object
        Painting_bar__minus_02_dot_00_bar__plus_01_dot_58_bar__plus_00_dot_42 - object
        Pillow_bar__minus_01_dot_24_bar__plus_00_dot_46_bar__minus_00_dot_03 - object
        RemoteControl_bar__plus_01_dot_68_bar__plus_00_dot_76_bar__minus_00_dot_21 - object
        RemoteControl_bar__minus_01_dot_19_bar__plus_00_dot_33_bar__plus_00_dot_44 - object
        RemoteControl_bar__minus_01_dot_57_bar__plus_00_dot_49_bar__plus_01_dot_95 - object
        RemoteControl_bar__minus_01_dot_70_bar__plus_00_dot_49_bar__plus_01_dot_69 - object
        Statue_bar__plus_00_dot_03_bar__plus_00_dot_48_bar__plus_00_dot_97 - object
        Statue_bar__plus_01_dot_67_bar__plus_00_dot_19_bar__plus_00_dot_18 - object
        Statue_bar__plus_01_dot_68_bar__plus_00_dot_77_bar__plus_00_dot_69 - object
        Television_bar__plus_01_dot_93_bar__plus_01_dot_24_bar__plus_00_dot_25 - object
        Vase_bar__minus_01_dot_44_bar__plus_00_dot_49_bar__plus_01_dot_69 - object
        Vase_bar__minus_01_dot_80_bar__plus_01_dot_32_bar__minus_01_dot_47 - object
        Vase_bar__minus_01_dot_80_bar__plus_01_dot_62_bar__minus_01_dot_79 - object
        Watch_bar__minus_00_dot_05_bar__plus_00_dot_47_bar__plus_00_dot_44 - object
        Watch_bar__minus_01_dot_63_bar__plus_00_dot_49_bar__plus_01_dot_75 - object
        Window_bar__minus_00_dot_22_bar__plus_01_dot_50_bar__minus_01_dot_88 - object
        ArmChair_bar__plus_00_dot_40_bar__plus_00_dot_00_bar__plus_02_dot_19 - receptacle
        ArmChair_bar__minus_00_dot_63_bar__plus_00_dot_00_bar__plus_02_dot_19 - receptacle
        CoffeeTable_bar__minus_00_dot_15_bar_00_dot_00_bar__plus_00_dot_42 - receptacle
        CoffeeTable_bar__minus_01_dot_57_bar__plus_00_dot_00_bar__plus_01_dot_82 - receptacle
        GarbageCan_bar__plus_01_dot_77_bar__plus_00_dot_00_bar__plus_03_dot_91 - receptacle
        Shelf_bar__plus_01_dot_51_bar__plus_00_dot_17_bar__minus_00_dot_13 - receptacle
        Shelf_bar__plus_01_dot_52_bar__plus_00_dot_17_bar__plus_00_dot_60 - receptacle
        Shelf_bar__plus_01_dot_52_bar__plus_00_dot_41_bar__plus_00_dot_60 - receptacle
        Shelf_bar__plus_01_dot_53_bar__plus_00_dot_47_bar__minus_00_dot_13 - receptacle
        Shelf_bar__plus_01_dot_56_bar__plus_00_dot_56_bar__plus_00_dot_60 - receptacle
        Shelf_bar__plus_01_dot_67_bar__plus_00_dot_17_bar__plus_00_dot_24 - receptacle
        Shelf_bar__minus_01_dot_83_bar__plus_01_dot_32_bar__minus_01_dot_61 - receptacle
        Shelf_bar__minus_01_dot_83_bar__plus_01_dot_62_bar__minus_01_dot_61 - receptacle
        Sofa_bar__minus_01_dot_45_bar__minus_00_dot_03_bar__plus_00_dot_42 - receptacle
        TVStand_bar__plus_01_dot_67_bar__plus_00_dot_00_bar__plus_00_dot_24 - receptacle
        loc_bar__minus_3_bar_4_bar_0_bar_60 - location
        loc_bar_3_bar__minus_1_bar_1_bar_60 - location
        loc_bar_3_bar_1_bar_1_bar_60 - location
        loc_bar_2_bar_1_bar_1_bar_60 - location
        loc_bar_2_bar_1_bar_1_bar_45 - location
        loc_bar__minus_2_bar__minus_5_bar_3_bar_0 - location
        loc_bar_6_bar_12_bar_0_bar_60 - location
        loc_bar__minus_1_bar__minus_5_bar_2_bar_15 - location
        loc_bar__minus_3_bar_6_bar_3_bar_60 - location
        loc_bar_2_bar_4_bar_0_bar_60 - location
        loc_bar_2_bar_0_bar_1_bar_45 - location
        loc_bar_3_bar_14_bar_1_bar_60 - location
        loc_bar__minus_6_bar_14_bar_3_bar_30 - location
        loc_bar__minus_3_bar_2_bar_3_bar_0 - location
        loc_bar_4_bar_2_bar_3_bar_60 - location
        loc_bar_0_bar_14_bar_0_bar_0 - location
        loc_bar__minus_5_bar__minus_5_bar_0_bar_45 - location
        loc_bar__minus_5_bar__minus_6_bar_3_bar_30 - location
        loc_bar__minus_1_bar__minus_4_bar_2_bar_30 - location
        )
    

(:init


        (receptacleType Shelf_bar__minus_01_dot_83_bar__plus_01_dot_32_bar__minus_01_dot_61 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_51_bar__plus_00_dot_17_bar__minus_00_dot_13 ShelfType)
        (receptacleType Shelf_bar__minus_01_dot_83_bar__plus_01_dot_62_bar__minus_01_dot_61 ShelfType)
        (receptacleType GarbageCan_bar__plus_01_dot_77_bar__plus_00_dot_00_bar__plus_03_dot_91 GarbageCanType)
        (receptacleType Shelf_bar__plus_01_dot_67_bar__plus_00_dot_17_bar__plus_00_dot_24 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_52_bar__plus_00_dot_17_bar__plus_00_dot_60 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_53_bar__plus_00_dot_47_bar__minus_00_dot_13 ShelfType)
        (receptacleType CoffeeTable_bar__minus_01_dot_57_bar__plus_00_dot_00_bar__plus_01_dot_82 CoffeeTableType)
        (receptacleType ArmChair_bar__plus_00_dot_40_bar__plus_00_dot_00_bar__plus_02_dot_19 ArmChairType)
        (receptacleType CoffeeTable_bar__minus_00_dot_15_bar_00_dot_00_bar__plus_00_dot_42 CoffeeTableType)
        (receptacleType Sofa_bar__minus_01_dot_45_bar__minus_00_dot_03_bar__plus_00_dot_42 SofaType)
        (receptacleType TVStand_bar__plus_01_dot_67_bar__plus_00_dot_00_bar__plus_00_dot_24 TVStandType)
        (receptacleType Shelf_bar__plus_01_dot_52_bar__plus_00_dot_41_bar__plus_00_dot_60 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_56_bar__plus_00_dot_56_bar__plus_00_dot_60 ShelfType)
        (receptacleType ArmChair_bar__minus_00_dot_63_bar__plus_00_dot_00_bar__plus_02_dot_19 ArmChairType)
        (objectType LightSwitch_bar__minus_02_dot_00_bar__plus_01_dot_33_bar__plus_03_dot_92 LightSwitchType)
        (objectType Box_bar__plus_00_dot_03_bar__plus_00_dot_65_bar__plus_00_dot_27 BoxType)
        (objectType Watch_bar__minus_00_dot_05_bar__plus_00_dot_47_bar__plus_00_dot_44 WatchType)
        (objectType Vase_bar__minus_01_dot_80_bar__plus_01_dot_32_bar__minus_01_dot_47 VaseType)
        (objectType CreditCard_bar__minus_01_dot_43_bar__plus_00_dot_34_bar__plus_00_dot_64 CreditCardType)
        (objectType Vase_bar__minus_01_dot_80_bar__plus_01_dot_62_bar__minus_01_dot_79 VaseType)
        (objectType Laptop_bar__minus_01_dot_27_bar__plus_00_dot_33_bar__plus_00_dot_85 LaptopType)
        (objectType Statue_bar__plus_01_dot_68_bar__plus_00_dot_77_bar__plus_00_dot_69 StatueType)
        (objectType Window_bar__minus_00_dot_22_bar__plus_01_dot_50_bar__minus_01_dot_88 WindowType)
        (objectType Painting_bar__minus_00_dot_10_bar__plus_01_dot_58_bar__plus_04_dot_05 PaintingType)
        (objectType KeyChain_bar__minus_00_dot_30_bar__plus_00_dot_48_bar__plus_00_dot_44 KeyChainType)
        (objectType Vase_bar__minus_01_dot_44_bar__plus_00_dot_49_bar__plus_01_dot_69 VaseType)
        (objectType Television_bar__plus_01_dot_93_bar__plus_01_dot_24_bar__plus_00_dot_25 TelevisionType)
        (objectType CreditCard_bar__plus_00_dot_11_bar__plus_00_dot_47_bar__plus_00_dot_44 CreditCardType)
        (objectType KeyChain_bar__minus_01_dot_19_bar__plus_00_dot_34_bar__plus_00_dot_24 KeyChainType)
        (objectType FloorLamp_bar__plus_01_dot_23_bar__plus_00_dot_00_bar__plus_03_dot_80 FloorLampType)
        (objectType Statue_bar__plus_01_dot_67_bar__plus_00_dot_19_bar__plus_00_dot_18 StatueType)
        (objectType Watch_bar__minus_01_dot_63_bar__plus_00_dot_49_bar__plus_01_dot_75 WatchType)
        (objectType KeyChain_bar__minus_01_dot_35_bar__plus_00_dot_34_bar__plus_00_dot_64 KeyChainType)
        (objectType Pillow_bar__minus_01_dot_24_bar__plus_00_dot_46_bar__minus_00_dot_03 PillowType)
        (objectType CreditCard_bar__plus_01_dot_60_bar__plus_00_dot_76_bar__minus_00_dot_06 CreditCardType)
        (objectType RemoteControl_bar__minus_01_dot_57_bar__plus_00_dot_49_bar__plus_01_dot_95 RemoteControlType)
        (objectType RemoteControl_bar__minus_01_dot_70_bar__plus_00_dot_49_bar__plus_01_dot_69 RemoteControlType)
        (objectType Laptop_bar__minus_00_dot_30_bar__plus_00_dot_48_bar__minus_00_dot_09 LaptopType)
        (objectType Statue_bar__plus_00_dot_03_bar__plus_00_dot_48_bar__plus_00_dot_97 StatueType)
        (objectType RemoteControl_bar__plus_01_dot_68_bar__plus_00_dot_76_bar__minus_00_dot_21 RemoteControlType)
        (objectType RemoteControl_bar__minus_01_dot_19_bar__plus_00_dot_33_bar__plus_00_dot_44 RemoteControlType)
        (objectType Painting_bar__minus_02_dot_00_bar__plus_01_dot_58_bar__plus_00_dot_42 PaintingType)
        (objectType Laptop_bar__plus_01_dot_53_bar__plus_00_dot_76_bar__plus_00_dot_24 LaptopType)
        (objectType HousePlant_bar__minus_01_dot_78_bar__plus_00_dot_48_bar__plus_01_dot_97 HousePlantType)
        (canContain ShelfType WatchType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType WatchType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType WatchType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType WatchType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType WatchType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType WatchType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain CoffeeTableType WatchType)
        (canContain CoffeeTableType VaseType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType StatueType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (canContain CoffeeTableType WatchType)
        (canContain CoffeeTableType VaseType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType StatueType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType CreditCardType)
        (canContain ShelfType WatchType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType WatchType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (pickupable Box_bar__plus_00_dot_03_bar__plus_00_dot_65_bar__plus_00_dot_27)
        (pickupable Watch_bar__minus_00_dot_05_bar__plus_00_dot_47_bar__plus_00_dot_44)
        (pickupable Vase_bar__minus_01_dot_80_bar__plus_01_dot_32_bar__minus_01_dot_47)
        (pickupable CreditCard_bar__minus_01_dot_43_bar__plus_00_dot_34_bar__plus_00_dot_64)
        (pickupable Vase_bar__minus_01_dot_80_bar__plus_01_dot_62_bar__minus_01_dot_79)
        (pickupable Laptop_bar__minus_01_dot_27_bar__plus_00_dot_33_bar__plus_00_dot_85)
        (pickupable Statue_bar__plus_01_dot_68_bar__plus_00_dot_77_bar__plus_00_dot_69)
        (pickupable KeyChain_bar__minus_00_dot_30_bar__plus_00_dot_48_bar__plus_00_dot_44)
        (pickupable Vase_bar__minus_01_dot_44_bar__plus_00_dot_49_bar__plus_01_dot_69)
        (pickupable CreditCard_bar__plus_00_dot_11_bar__plus_00_dot_47_bar__plus_00_dot_44)
        (pickupable KeyChain_bar__minus_01_dot_19_bar__plus_00_dot_34_bar__plus_00_dot_24)
        (pickupable Statue_bar__plus_01_dot_67_bar__plus_00_dot_19_bar__plus_00_dot_18)
        (pickupable Watch_bar__minus_01_dot_63_bar__plus_00_dot_49_bar__plus_01_dot_75)
        (pickupable KeyChain_bar__minus_01_dot_35_bar__plus_00_dot_34_bar__plus_00_dot_64)
        (pickupable Pillow_bar__minus_01_dot_24_bar__plus_00_dot_46_bar__minus_00_dot_03)
        (pickupable CreditCard_bar__plus_01_dot_60_bar__plus_00_dot_76_bar__minus_00_dot_06)
        (pickupable RemoteControl_bar__minus_01_dot_57_bar__plus_00_dot_49_bar__plus_01_dot_95)
        (pickupable RemoteControl_bar__minus_01_dot_70_bar__plus_00_dot_49_bar__plus_01_dot_69)
        (pickupable Laptop_bar__minus_00_dot_30_bar__plus_00_dot_48_bar__minus_00_dot_09)
        (pickupable Statue_bar__plus_00_dot_03_bar__plus_00_dot_48_bar__plus_00_dot_97)
        (pickupable RemoteControl_bar__plus_01_dot_68_bar__plus_00_dot_76_bar__minus_00_dot_21)
        (pickupable RemoteControl_bar__minus_01_dot_19_bar__plus_00_dot_33_bar__plus_00_dot_44)
        (pickupable Laptop_bar__plus_01_dot_53_bar__plus_00_dot_76_bar__plus_00_dot_24)
        (isReceptacleObject Box_bar__plus_00_dot_03_bar__plus_00_dot_65_bar__plus_00_dot_27)
        
        
        (atLocation agent1 loc_bar__minus_1_bar__minus_4_bar_2_bar_30)
        
        
        
        
        
        
        
        (toggleable FloorLamp_bar__plus_01_dot_23_bar__plus_00_dot_00_bar__plus_03_dot_80)
        
        
        
        
        (inReceptacle Laptop_bar__minus_01_dot_27_bar__plus_00_dot_33_bar__plus_00_dot_85 Sofa_bar__minus_01_dot_45_bar__minus_00_dot_03_bar__plus_00_dot_42)
        (inReceptacle KeyChain_bar__minus_01_dot_19_bar__plus_00_dot_34_bar__plus_00_dot_24 Sofa_bar__minus_01_dot_45_bar__minus_00_dot_03_bar__plus_00_dot_42)
        (inReceptacle RemoteControl_bar__minus_01_dot_19_bar__plus_00_dot_33_bar__plus_00_dot_44 Sofa_bar__minus_01_dot_45_bar__minus_00_dot_03_bar__plus_00_dot_42)
        (inReceptacle KeyChain_bar__minus_01_dot_35_bar__plus_00_dot_34_bar__plus_00_dot_64 Sofa_bar__minus_01_dot_45_bar__minus_00_dot_03_bar__plus_00_dot_42)
        (inReceptacle Pillow_bar__minus_01_dot_24_bar__plus_00_dot_46_bar__minus_00_dot_03 Sofa_bar__minus_01_dot_45_bar__minus_00_dot_03_bar__plus_00_dot_42)
        (inReceptacle CreditCard_bar__minus_01_dot_43_bar__plus_00_dot_34_bar__plus_00_dot_64 Sofa_bar__minus_01_dot_45_bar__minus_00_dot_03_bar__plus_00_dot_42)
        (inReceptacle Vase_bar__minus_01_dot_80_bar__plus_01_dot_62_bar__minus_01_dot_79 Shelf_bar__minus_01_dot_83_bar__plus_01_dot_62_bar__minus_01_dot_61)
        (inReceptacle HousePlant_bar__minus_01_dot_78_bar__plus_00_dot_48_bar__plus_01_dot_97 CoffeeTable_bar__minus_01_dot_57_bar__plus_00_dot_00_bar__plus_01_dot_82)
        (inReceptacle Watch_bar__minus_01_dot_63_bar__plus_00_dot_49_bar__plus_01_dot_75 CoffeeTable_bar__minus_01_dot_57_bar__plus_00_dot_00_bar__plus_01_dot_82)
        (inReceptacle Vase_bar__minus_01_dot_44_bar__plus_00_dot_49_bar__plus_01_dot_69 CoffeeTable_bar__minus_01_dot_57_bar__plus_00_dot_00_bar__plus_01_dot_82)
        (inReceptacle RemoteControl_bar__minus_01_dot_70_bar__plus_00_dot_49_bar__plus_01_dot_69 CoffeeTable_bar__minus_01_dot_57_bar__plus_00_dot_00_bar__plus_01_dot_82)
        (inReceptacle RemoteControl_bar__minus_01_dot_57_bar__plus_00_dot_49_bar__plus_01_dot_95 CoffeeTable_bar__minus_01_dot_57_bar__plus_00_dot_00_bar__plus_01_dot_82)
        (inReceptacle CreditCard_bar__plus_00_dot_11_bar__plus_00_dot_47_bar__plus_00_dot_44 CoffeeTable_bar__minus_00_dot_15_bar_00_dot_00_bar__plus_00_dot_42)
        (inReceptacle Laptop_bar__minus_00_dot_30_bar__plus_00_dot_48_bar__minus_00_dot_09 CoffeeTable_bar__minus_00_dot_15_bar_00_dot_00_bar__plus_00_dot_42)
        (inReceptacle Statue_bar__plus_00_dot_03_bar__plus_00_dot_48_bar__plus_00_dot_97 CoffeeTable_bar__minus_00_dot_15_bar_00_dot_00_bar__plus_00_dot_42)
        (inReceptacle KeyChain_bar__minus_00_dot_30_bar__plus_00_dot_48_bar__plus_00_dot_44 CoffeeTable_bar__minus_00_dot_15_bar_00_dot_00_bar__plus_00_dot_42)
        (inReceptacle Box_bar__plus_00_dot_03_bar__plus_00_dot_65_bar__plus_00_dot_27 CoffeeTable_bar__minus_00_dot_15_bar_00_dot_00_bar__plus_00_dot_42)
        (inReceptacle Watch_bar__minus_00_dot_05_bar__plus_00_dot_47_bar__plus_00_dot_44 CoffeeTable_bar__minus_00_dot_15_bar_00_dot_00_bar__plus_00_dot_42)
        (inReceptacle Vase_bar__minus_01_dot_80_bar__plus_01_dot_32_bar__minus_01_dot_47 Shelf_bar__minus_01_dot_83_bar__plus_01_dot_32_bar__minus_01_dot_61)
        (inReceptacle Statue_bar__plus_01_dot_67_bar__plus_00_dot_19_bar__plus_00_dot_18 Shelf_bar__plus_01_dot_67_bar__plus_00_dot_17_bar__plus_00_dot_24)
        (inReceptacle Statue_bar__plus_01_dot_68_bar__plus_00_dot_77_bar__plus_00_dot_69 TVStand_bar__plus_01_dot_67_bar__plus_00_dot_00_bar__plus_00_dot_24)
        (inReceptacle RemoteControl_bar__plus_01_dot_68_bar__plus_00_dot_76_bar__minus_00_dot_21 TVStand_bar__plus_01_dot_67_bar__plus_00_dot_00_bar__plus_00_dot_24)
        (inReceptacle Laptop_bar__plus_01_dot_53_bar__plus_00_dot_76_bar__plus_00_dot_24 TVStand_bar__plus_01_dot_67_bar__plus_00_dot_00_bar__plus_00_dot_24)
        (inReceptacle CreditCard_bar__plus_01_dot_60_bar__plus_00_dot_76_bar__minus_00_dot_06 TVStand_bar__plus_01_dot_67_bar__plus_00_dot_00_bar__plus_00_dot_24)
        (inReceptacle Television_bar__plus_01_dot_93_bar__plus_01_dot_24_bar__plus_00_dot_25 TVStand_bar__plus_01_dot_67_bar__plus_00_dot_00_bar__plus_00_dot_24)
        
        
        (receptacleAtLocation ArmChair_bar__plus_00_dot_40_bar__plus_00_dot_00_bar__plus_02_dot_19 loc_bar_2_bar_4_bar_0_bar_60)
        (receptacleAtLocation ArmChair_bar__minus_00_dot_63_bar__plus_00_dot_00_bar__plus_02_dot_19 loc_bar__minus_3_bar_4_bar_0_bar_60)
        (receptacleAtLocation CoffeeTable_bar__minus_00_dot_15_bar_00_dot_00_bar__plus_00_dot_42 loc_bar_4_bar_2_bar_3_bar_60)
        (receptacleAtLocation CoffeeTable_bar__minus_01_dot_57_bar__plus_00_dot_00_bar__plus_01_dot_82 loc_bar__minus_3_bar_6_bar_3_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_01_dot_77_bar__plus_00_dot_00_bar__plus_03_dot_91 loc_bar_6_bar_12_bar_0_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_51_bar__plus_00_dot_17_bar__minus_00_dot_13 loc_bar_2_bar_1_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_52_bar__plus_00_dot_17_bar__plus_00_dot_60 loc_bar_2_bar_1_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_52_bar__plus_00_dot_41_bar__plus_00_dot_60 loc_bar_2_bar_1_bar_1_bar_45)
        (receptacleAtLocation Shelf_bar__plus_01_dot_53_bar__plus_00_dot_47_bar__minus_00_dot_13 loc_bar_3_bar__minus_1_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_56_bar__plus_00_dot_56_bar__plus_00_dot_60 loc_bar_3_bar_1_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_67_bar__plus_00_dot_17_bar__plus_00_dot_24 loc_bar_2_bar_0_bar_1_bar_45)
        (receptacleAtLocation Shelf_bar__minus_01_dot_83_bar__plus_01_dot_32_bar__minus_01_dot_61 loc_bar__minus_5_bar__minus_6_bar_3_bar_30)
        (receptacleAtLocation Shelf_bar__minus_01_dot_83_bar__plus_01_dot_62_bar__minus_01_dot_61 loc_bar__minus_2_bar__minus_5_bar_3_bar_0)
        (receptacleAtLocation Sofa_bar__minus_01_dot_45_bar__minus_00_dot_03_bar__plus_00_dot_42 loc_bar__minus_5_bar__minus_5_bar_0_bar_45)
        (receptacleAtLocation TVStand_bar__plus_01_dot_67_bar__plus_00_dot_00_bar__plus_00_dot_24 loc_bar_3_bar_1_bar_1_bar_60)
        (objectAtLocation Statue_bar__plus_01_dot_68_bar__plus_00_dot_77_bar__plus_00_dot_69 loc_bar_3_bar_1_bar_1_bar_60)
        (objectAtLocation Watch_bar__minus_01_dot_63_bar__plus_00_dot_49_bar__plus_01_dot_75 loc_bar__minus_3_bar_6_bar_3_bar_60)
        (objectAtLocation RemoteControl_bar__minus_01_dot_19_bar__plus_00_dot_33_bar__plus_00_dot_44 loc_bar__minus_5_bar__minus_5_bar_0_bar_45)
        (objectAtLocation KeyChain_bar__minus_01_dot_35_bar__plus_00_dot_34_bar__plus_00_dot_64 loc_bar__minus_5_bar__minus_5_bar_0_bar_45)
        (objectAtLocation CreditCard_bar__minus_01_dot_43_bar__plus_00_dot_34_bar__plus_00_dot_64 loc_bar__minus_5_bar__minus_5_bar_0_bar_45)
        (objectAtLocation Laptop_bar__minus_00_dot_30_bar__plus_00_dot_48_bar__minus_00_dot_09 loc_bar_4_bar_2_bar_3_bar_60)
        (objectAtLocation Laptop_bar__plus_01_dot_53_bar__plus_00_dot_76_bar__plus_00_dot_24 loc_bar_3_bar_1_bar_1_bar_60)
        (objectAtLocation RemoteControl_bar__plus_01_dot_68_bar__plus_00_dot_76_bar__minus_00_dot_21 loc_bar_3_bar_1_bar_1_bar_60)
        (objectAtLocation RemoteControl_bar__minus_01_dot_57_bar__plus_00_dot_49_bar__plus_01_dot_95 loc_bar__minus_3_bar_6_bar_3_bar_60)
        (objectAtLocation CreditCard_bar__plus_00_dot_11_bar__plus_00_dot_47_bar__plus_00_dot_44 loc_bar_4_bar_2_bar_3_bar_60)
        (objectAtLocation KeyChain_bar__minus_01_dot_19_bar__plus_00_dot_34_bar__plus_00_dot_24 loc_bar__minus_5_bar__minus_5_bar_0_bar_45)
        (objectAtLocation Statue_bar__plus_00_dot_03_bar__plus_00_dot_48_bar__plus_00_dot_97 loc_bar_4_bar_2_bar_3_bar_60)
        (objectAtLocation Box_bar__plus_00_dot_03_bar__plus_00_dot_65_bar__plus_00_dot_27 loc_bar_4_bar_2_bar_3_bar_60)
        (objectAtLocation Television_bar__plus_01_dot_93_bar__plus_01_dot_24_bar__plus_00_dot_25 loc_bar_3_bar_1_bar_1_bar_60)
        (objectAtLocation Laptop_bar__minus_01_dot_27_bar__plus_00_dot_33_bar__plus_00_dot_85 loc_bar__minus_5_bar__minus_5_bar_0_bar_45)
        (objectAtLocation LightSwitch_bar__minus_02_dot_00_bar__plus_01_dot_33_bar__plus_03_dot_92 loc_bar__minus_6_bar_14_bar_3_bar_30)
        (objectAtLocation CreditCard_bar__plus_01_dot_60_bar__plus_00_dot_76_bar__minus_00_dot_06 loc_bar_3_bar_1_bar_1_bar_60)
        (objectAtLocation KeyChain_bar__minus_00_dot_30_bar__plus_00_dot_48_bar__plus_00_dot_44 loc_bar_4_bar_2_bar_3_bar_60)
        (objectAtLocation Vase_bar__minus_01_dot_44_bar__plus_00_dot_49_bar__plus_01_dot_69 loc_bar__minus_3_bar_6_bar_3_bar_60)
        (objectAtLocation Vase_bar__minus_01_dot_80_bar__plus_01_dot_62_bar__minus_01_dot_79 loc_bar__minus_2_bar__minus_5_bar_3_bar_0)
        (objectAtLocation Window_bar__minus_00_dot_22_bar__plus_01_dot_50_bar__minus_01_dot_88 loc_bar__minus_1_bar__minus_5_bar_2_bar_15)
        (objectAtLocation HousePlant_bar__minus_01_dot_78_bar__plus_00_dot_48_bar__plus_01_dot_97 loc_bar__minus_3_bar_6_bar_3_bar_60)
        (objectAtLocation Pillow_bar__minus_01_dot_24_bar__plus_00_dot_46_bar__minus_00_dot_03 loc_bar__minus_5_bar__minus_5_bar_0_bar_45)
        (objectAtLocation Painting_bar__minus_02_dot_00_bar__plus_01_dot_58_bar__plus_00_dot_42 loc_bar__minus_3_bar_2_bar_3_bar_0)
        (objectAtLocation RemoteControl_bar__minus_01_dot_70_bar__plus_00_dot_49_bar__plus_01_dot_69 loc_bar__minus_3_bar_6_bar_3_bar_60)
        (objectAtLocation Watch_bar__minus_00_dot_05_bar__plus_00_dot_47_bar__plus_00_dot_44 loc_bar_4_bar_2_bar_3_bar_60)
        (objectAtLocation FloorLamp_bar__plus_01_dot_23_bar__plus_00_dot_00_bar__plus_03_dot_80 loc_bar_3_bar_14_bar_1_bar_60)
        (objectAtLocation Vase_bar__minus_01_dot_80_bar__plus_01_dot_32_bar__minus_01_dot_47 loc_bar__minus_5_bar__minus_6_bar_3_bar_30)
        (objectAtLocation Painting_bar__minus_00_dot_10_bar__plus_01_dot_58_bar__plus_04_dot_05 loc_bar_0_bar_14_bar_0_bar_0)
        (objectAtLocation Statue_bar__plus_01_dot_67_bar__plus_00_dot_19_bar__plus_00_dot_18 loc_bar_2_bar_0_bar_1_bar_45)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 RemoteControlType)
                                    (receptacleType ?r ArmChairType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 RemoteControlType)
                                            (receptacleType ?r ArmChairType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            