
(define (problem plan_trial_T20190909_095533_482785)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        AlarmClock_bar__plus_02_dot_73_bar__plus_00_dot_98_bar__plus_00_dot_44 - object
        AlarmClock_bar__plus_03_dot_00_bar__plus_00_dot_98_bar__plus_00_dot_44 - object
        AlarmClock_bar__minus_01_dot_60_bar__plus_00_dot_66_bar__plus_01_dot_63 - object
        BaseballBat_bar__plus_00_dot_81_bar__plus_00_dot_05_bar__plus_02_dot_15 - object
        BaseballBat_bar__plus_03_dot_08_bar__plus_00_dot_63_bar__minus_01_dot_76 - object
        Book_bar__plus_00_dot_09_bar__plus_00_dot_69_bar__plus_00_dot_56 - object
        Book_bar__plus_02_dot_71_bar__plus_00_dot_18_bar__plus_01_dot_27 - object
        Book_bar__minus_00_dot_72_bar__plus_00_dot_69_bar__minus_00_dot_33 - object
        CD_bar__plus_01_dot_33_bar__plus_00_dot_02_bar__minus_01_dot_75 - object
        CD_bar__plus_02_dot_81_bar__plus_00_dot_45_bar__minus_00_dot_62 - object
        CellPhone_bar__minus_01_dot_52_bar__plus_00_dot_69_bar__plus_00_dot_34 - object
        CellPhone_bar__minus_01_dot_54_bar__plus_00_dot_36_bar__plus_01_dot_48 - object
        Cloth_bar__plus_01_dot_76_bar__plus_00_dot_00_bar__minus_01_dot_73 - object
        CreditCard_bar__plus_02_dot_56_bar__plus_00_dot_99_bar__plus_01_dot_58 - object
        CreditCard_bar__plus_02_dot_58_bar__plus_00_dot_89_bar__plus_01_dot_63 - object
        Curtains_bar__minus_00_dot_02_bar__plus_02_dot_92_bar__minus_01_dot_85 - object
        KeyChain_bar__plus_02_dot_23_bar__plus_00_dot_45_bar__minus_00_dot_84 - object
        KeyChain_bar__plus_02_dot_48_bar__plus_00_dot_45_bar__minus_00_dot_54 - object
        KeyChain_bar__minus_01_dot_54_bar__plus_00_dot_36_bar__minus_01_dot_42 - object
        Lamp_bar__minus_01_dot_62_bar__plus_00_dot_65_bar__plus_01_dot_49 - object
        Lamp_bar__minus_01_dot_63_bar__plus_00_dot_65_bar__minus_01_dot_31 - object
        Laptop_bar__plus_02_dot_91_bar__plus_00_dot_99_bar__plus_00_dot_16 - object
        LightSwitch_bar__plus_01_dot_32_bar__plus_01_dot_30_bar__plus_02_dot_20 - object
        Mirror_bar__plus_00_dot_58_bar__plus_01_dot_35_bar__plus_02_dot_20 - object
        Mug_bar__minus_01_dot_51_bar__plus_00_dot_66_bar__plus_01_dot_25 - object
        Painting_bar__plus_01_dot_53_bar__plus_01_dot_52_bar__minus_01_dot_85 - object
        Pencil_bar__minus_01_dot_65_bar__plus_00_dot_67_bar__minus_01_dot_48 - object
        Pen_bar__plus_03_dot_00_bar__plus_00_dot_99_bar__plus_00_dot_72 - object
        Pen_bar__plus_03_dot_00_bar__plus_00_dot_99_bar__plus_01_dot_01 - object
        Pen_bar__minus_01_dot_60_bar__plus_00_dot_67_bar__minus_01_dot_25 - object
        Pillow_bar__plus_00_dot_09_bar__plus_00_dot_78_bar__minus_00_dot_33 - object
        Pillow_bar__minus_00_dot_45_bar__plus_00_dot_78_bar__plus_00_dot_78 - object
        Pillow_bar__minus_01_dot_52_bar__plus_00_dot_78_bar__plus_00_dot_78 - object
        Window_bar__minus_00_dot_01_bar__plus_01_dot_69_bar__minus_01_dot_92 - object
        ArmChair_bar__plus_02_dot_58_bar__minus_00_dot_01_bar__minus_00_dot_82 - receptacle
        Bed_bar__minus_00_dot_75_bar__plus_00_dot_03_bar__plus_00_dot_11 - receptacle
        Cabinet_bar__plus_02_dot_48_bar__plus_00_dot_50_bar__plus_01_dot_00 - receptacle
        Cabinet_bar__plus_02_dot_48_bar__plus_00_dot_50_bar__plus_01_dot_01 - receptacle
        Cabinet_bar__plus_02_dot_48_bar__plus_00_dot_50_bar__plus_02_dot_03 - receptacle
        Cabinet_bar__plus_02_dot_48_bar__plus_00_dot_50_bar__minus_00_dot_02 - receptacle
        Drawer_bar__plus_02_dot_57_bar__plus_00_dot_90_bar__plus_00_dot_23 - receptacle
        Drawer_bar__plus_02_dot_57_bar__plus_00_dot_90_bar__plus_00_dot_75 - receptacle
        Drawer_bar__plus_02_dot_57_bar__plus_00_dot_90_bar__plus_01_dot_27 - receptacle
        Drawer_bar__plus_02_dot_57_bar__plus_00_dot_90_bar__plus_01_dot_78 - receptacle
        Drawer_bar__minus_01_dot_53_bar__plus_00_dot_44_bar__plus_01_dot_48 - receptacle
        Drawer_bar__minus_01_dot_53_bar__plus_00_dot_44_bar__minus_01_dot_25 - receptacle
        Dresser_bar__plus_02_dot_82_bar__plus_00_dot_00_bar__plus_01_dot_01 - receptacle
        GarbageCan_bar__plus_01_dot_35_bar__plus_00_dot_00_bar__minus_01_dot_69 - receptacle
        SideTable_bar__minus_01_dot_60_bar__minus_00_dot_01_bar__plus_01_dot_48 - receptacle
        SideTable_bar__minus_01_dot_60_bar__minus_00_dot_01_bar__minus_01_dot_25 - receptacle
        loc_bar__minus_4_bar__minus_5_bar_3_bar_60 - location
        loc_bar_4_bar_7_bar_0_bar_60 - location
        loc_bar_8_bar_1_bar_1_bar_60 - location
        loc_bar_5_bar_7_bar_0_bar_45 - location
        loc_bar_6_bar_3_bar_1_bar_45 - location
        loc_bar_8_bar_5_bar_1_bar_60 - location
        loc_bar_6_bar__minus_5_bar_2_bar_15 - location
        loc_bar_8_bar_3_bar_1_bar_60 - location
        loc_bar_2_bar_6_bar_0_bar_30 - location
        loc_bar_7_bar_6_bar_1_bar_60 - location
        loc_bar_6_bar__minus_1_bar_1_bar_45 - location
        loc_bar_5_bar__minus_5_bar_2_bar_60 - location
        loc_bar_7_bar__minus_5_bar_1_bar_45 - location
        loc_bar_0_bar__minus_5_bar_2_bar_0 - location
        loc_bar_8_bar_7_bar_1_bar_60 - location
        loc_bar_7_bar_2_bar_1_bar_60 - location
        loc_bar_7_bar__minus_3_bar_1_bar_60 - location
        loc_bar_0_bar__minus_5_bar_2_bar__minus_30 - location
        loc_bar_9_bar_1_bar_0_bar_60 - location
        loc_bar_4_bar_0_bar_3_bar_45 - location
        loc_bar__minus_2_bar_6_bar_3_bar_60 - location
        loc_bar__minus_2_bar__minus_5_bar_3_bar_60 - location
        loc_bar_7_bar__minus_5_bar_2_bar_60 - location
        loc_bar__minus_4_bar_6_bar_3_bar_60 - location
        loc_bar_4_bar_6_bar_2_bar_30 - location
        )
    

(:init


        (receptacleType Bed_bar__minus_00_dot_75_bar__plus_00_dot_03_bar__plus_00_dot_11 BedType)
        (receptacleType Drawer_bar__minus_01_dot_53_bar__plus_00_dot_44_bar__plus_01_dot_48 DrawerType)
        (receptacleType Drawer_bar__plus_02_dot_57_bar__plus_00_dot_90_bar__plus_01_dot_27 DrawerType)
        (receptacleType Drawer_bar__plus_02_dot_57_bar__plus_00_dot_90_bar__plus_01_dot_78 DrawerType)
        (receptacleType SideTable_bar__minus_01_dot_60_bar__minus_00_dot_01_bar__plus_01_dot_48 SideTableType)
        (receptacleType Cabinet_bar__plus_02_dot_48_bar__plus_00_dot_50_bar__plus_01_dot_00 CabinetType)
        (receptacleType Drawer_bar__plus_02_dot_57_bar__plus_00_dot_90_bar__plus_00_dot_23 DrawerType)
        (receptacleType Drawer_bar__minus_01_dot_53_bar__plus_00_dot_44_bar__minus_01_dot_25 DrawerType)
        (receptacleType Cabinet_bar__plus_02_dot_48_bar__plus_00_dot_50_bar__minus_00_dot_02 CabinetType)
        (receptacleType GarbageCan_bar__plus_01_dot_35_bar__plus_00_dot_00_bar__minus_01_dot_69 GarbageCanType)
        (receptacleType Drawer_bar__plus_02_dot_57_bar__plus_00_dot_90_bar__plus_00_dot_75 DrawerType)
        (receptacleType Cabinet_bar__plus_02_dot_48_bar__plus_00_dot_50_bar__plus_01_dot_01 CabinetType)
        (receptacleType ArmChair_bar__plus_02_dot_58_bar__minus_00_dot_01_bar__minus_00_dot_82 ArmChairType)
        (receptacleType Dresser_bar__plus_02_dot_82_bar__plus_00_dot_00_bar__plus_01_dot_01 DresserType)
        (receptacleType SideTable_bar__minus_01_dot_60_bar__minus_00_dot_01_bar__minus_01_dot_25 SideTableType)
        (receptacleType Cabinet_bar__plus_02_dot_48_bar__plus_00_dot_50_bar__plus_02_dot_03 CabinetType)
        (objectType Painting_bar__plus_01_dot_53_bar__plus_01_dot_52_bar__minus_01_dot_85 PaintingType)
        (objectType Mug_bar__minus_01_dot_51_bar__plus_00_dot_66_bar__plus_01_dot_25 MugType)
        (objectType CellPhone_bar__minus_01_dot_54_bar__plus_00_dot_36_bar__plus_01_dot_48 CellPhoneType)
        (objectType Pillow_bar__plus_00_dot_09_bar__plus_00_dot_78_bar__minus_00_dot_33 PillowType)
        (objectType BaseballBat_bar__plus_00_dot_81_bar__plus_00_dot_05_bar__plus_02_dot_15 BaseballBatType)
        (objectType Book_bar__plus_00_dot_09_bar__plus_00_dot_69_bar__plus_00_dot_56 BookType)
        (objectType AlarmClock_bar__minus_01_dot_60_bar__plus_00_dot_66_bar__plus_01_dot_63 AlarmClockType)
        (objectType Book_bar__minus_00_dot_72_bar__plus_00_dot_69_bar__minus_00_dot_33 BookType)
        (objectType LightSwitch_bar__plus_01_dot_32_bar__plus_01_dot_30_bar__plus_02_dot_20 LightSwitchType)
        (objectType CellPhone_bar__minus_01_dot_52_bar__plus_00_dot_69_bar__plus_00_dot_34 CellPhoneType)
        (objectType KeyChain_bar__plus_02_dot_48_bar__plus_00_dot_45_bar__minus_00_dot_54 KeyChainType)
        (objectType CD_bar__plus_02_dot_81_bar__plus_00_dot_45_bar__minus_00_dot_62 CDType)
        (objectType Pen_bar__minus_01_dot_60_bar__plus_00_dot_67_bar__minus_01_dot_25 PenType)
        (objectType AlarmClock_bar__plus_03_dot_00_bar__plus_00_dot_98_bar__plus_00_dot_44 AlarmClockType)
        (objectType Laptop_bar__plus_02_dot_91_bar__plus_00_dot_99_bar__plus_00_dot_16 LaptopType)
        (objectType CreditCard_bar__plus_02_dot_56_bar__plus_00_dot_99_bar__plus_01_dot_58 CreditCardType)
        (objectType BaseballBat_bar__plus_03_dot_08_bar__plus_00_dot_63_bar__minus_01_dot_76 BaseballBatType)
        (objectType AlarmClock_bar__plus_02_dot_73_bar__plus_00_dot_98_bar__plus_00_dot_44 AlarmClockType)
        (objectType KeyChain_bar__minus_01_dot_54_bar__plus_00_dot_36_bar__minus_01_dot_42 KeyChainType)
        (objectType Window_bar__minus_00_dot_01_bar__plus_01_dot_69_bar__minus_01_dot_92 WindowType)
        (objectType CD_bar__plus_01_dot_33_bar__plus_00_dot_02_bar__minus_01_dot_75 CDType)
        (objectType Curtains_bar__minus_00_dot_02_bar__plus_02_dot_92_bar__minus_01_dot_85 CurtainsType)
        (objectType Pillow_bar__minus_01_dot_52_bar__plus_00_dot_78_bar__plus_00_dot_78 PillowType)
        (objectType Book_bar__plus_02_dot_71_bar__plus_00_dot_18_bar__plus_01_dot_27 BookType)
        (objectType Pencil_bar__minus_01_dot_65_bar__plus_00_dot_67_bar__minus_01_dot_48 PencilType)
        (objectType KeyChain_bar__plus_02_dot_23_bar__plus_00_dot_45_bar__minus_00_dot_84 KeyChainType)
        (objectType Pen_bar__plus_03_dot_00_bar__plus_00_dot_99_bar__plus_00_dot_72 PenType)
        (objectType CreditCard_bar__plus_02_dot_58_bar__plus_00_dot_89_bar__plus_01_dot_63 CreditCardType)
        (objectType Mirror_bar__plus_00_dot_58_bar__plus_01_dot_35_bar__plus_02_dot_20 MirrorType)
        (objectType Cloth_bar__plus_01_dot_76_bar__plus_00_dot_00_bar__minus_01_dot_73 ClothType)
        (objectType Pillow_bar__minus_00_dot_45_bar__plus_00_dot_78_bar__plus_00_dot_78 PillowType)
        (objectType Pen_bar__plus_03_dot_00_bar__plus_00_dot_99_bar__plus_01_dot_01 PenType)
        (canContain BedType BaseballBatType)
        (canContain BedType LaptopType)
        (canContain BedType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType PillowType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType ClothType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType ClothType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType ClothType)
        (canContain DrawerType PencilType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType CDType)
        (canContain SideTableType MugType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType BaseballBatType)
        (canContain SideTableType ClothType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType AlarmClockType)
        (canContain CabinetType BookType)
        (canContain CabinetType CDType)
        (canContain CabinetType MugType)
        (canContain CabinetType ClothType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType ClothType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType ClothType)
        (canContain DrawerType PencilType)
        (canContain CabinetType BookType)
        (canContain CabinetType CDType)
        (canContain CabinetType MugType)
        (canContain CabinetType ClothType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType CDType)
        (canContain GarbageCanType ClothType)
        (canContain GarbageCanType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType ClothType)
        (canContain DrawerType PencilType)
        (canContain CabinetType BookType)
        (canContain CabinetType CDType)
        (canContain CabinetType MugType)
        (canContain CabinetType ClothType)
        (canContain ArmChairType ClothType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType BookType)
        (canContain ArmChairType CellPhoneType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (canContain DresserType PenType)
        (canContain DresserType BookType)
        (canContain DresserType CDType)
        (canContain DresserType MugType)
        (canContain DresserType CellPhoneType)
        (canContain DresserType KeyChainType)
        (canContain DresserType CreditCardType)
        (canContain DresserType ClothType)
        (canContain DresserType LaptopType)
        (canContain DresserType PencilType)
        (canContain DresserType AlarmClockType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType CDType)
        (canContain SideTableType MugType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType BaseballBatType)
        (canContain SideTableType ClothType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType AlarmClockType)
        (canContain CabinetType BookType)
        (canContain CabinetType CDType)
        (canContain CabinetType MugType)
        (canContain CabinetType ClothType)
        (pickupable Mug_bar__minus_01_dot_51_bar__plus_00_dot_66_bar__plus_01_dot_25)
        (pickupable CellPhone_bar__minus_01_dot_54_bar__plus_00_dot_36_bar__plus_01_dot_48)
        (pickupable Pillow_bar__plus_00_dot_09_bar__plus_00_dot_78_bar__minus_00_dot_33)
        (pickupable BaseballBat_bar__plus_00_dot_81_bar__plus_00_dot_05_bar__plus_02_dot_15)
        (pickupable Book_bar__plus_00_dot_09_bar__plus_00_dot_69_bar__plus_00_dot_56)
        (pickupable AlarmClock_bar__minus_01_dot_60_bar__plus_00_dot_66_bar__plus_01_dot_63)
        (pickupable Book_bar__minus_00_dot_72_bar__plus_00_dot_69_bar__minus_00_dot_33)
        (pickupable CellPhone_bar__minus_01_dot_52_bar__plus_00_dot_69_bar__plus_00_dot_34)
        (pickupable KeyChain_bar__plus_02_dot_48_bar__plus_00_dot_45_bar__minus_00_dot_54)
        (pickupable CD_bar__plus_02_dot_81_bar__plus_00_dot_45_bar__minus_00_dot_62)
        (pickupable Pen_bar__minus_01_dot_60_bar__plus_00_dot_67_bar__minus_01_dot_25)
        (pickupable AlarmClock_bar__plus_03_dot_00_bar__plus_00_dot_98_bar__plus_00_dot_44)
        (pickupable Laptop_bar__plus_02_dot_91_bar__plus_00_dot_99_bar__plus_00_dot_16)
        (pickupable CreditCard_bar__plus_02_dot_56_bar__plus_00_dot_99_bar__plus_01_dot_58)
        (pickupable BaseballBat_bar__plus_03_dot_08_bar__plus_00_dot_63_bar__minus_01_dot_76)
        (pickupable AlarmClock_bar__plus_02_dot_73_bar__plus_00_dot_98_bar__plus_00_dot_44)
        (pickupable KeyChain_bar__minus_01_dot_54_bar__plus_00_dot_36_bar__minus_01_dot_42)
        (pickupable CD_bar__plus_01_dot_33_bar__plus_00_dot_02_bar__minus_01_dot_75)
        (pickupable Pillow_bar__minus_01_dot_52_bar__plus_00_dot_78_bar__plus_00_dot_78)
        (pickupable Book_bar__plus_02_dot_71_bar__plus_00_dot_18_bar__plus_01_dot_27)
        (pickupable Pencil_bar__minus_01_dot_65_bar__plus_00_dot_67_bar__minus_01_dot_48)
        (pickupable KeyChain_bar__plus_02_dot_23_bar__plus_00_dot_45_bar__minus_00_dot_84)
        (pickupable Pen_bar__plus_03_dot_00_bar__plus_00_dot_99_bar__plus_00_dot_72)
        (pickupable CreditCard_bar__plus_02_dot_58_bar__plus_00_dot_89_bar__plus_01_dot_63)
        (pickupable Cloth_bar__plus_01_dot_76_bar__plus_00_dot_00_bar__minus_01_dot_73)
        (pickupable Pillow_bar__minus_00_dot_45_bar__plus_00_dot_78_bar__plus_00_dot_78)
        (pickupable Pen_bar__plus_03_dot_00_bar__plus_00_dot_99_bar__plus_01_dot_01)
        (isReceptacleObject Mug_bar__minus_01_dot_51_bar__plus_00_dot_66_bar__plus_01_dot_25)
        (openable Drawer_bar__plus_02_dot_57_bar__plus_00_dot_90_bar__plus_01_dot_27)
        (openable Drawer_bar__plus_02_dot_57_bar__plus_00_dot_90_bar__plus_01_dot_78)
        (openable Cabinet_bar__plus_02_dot_48_bar__plus_00_dot_50_bar__plus_01_dot_00)
        (openable Drawer_bar__plus_02_dot_57_bar__plus_00_dot_90_bar__plus_00_dot_23)
        (openable Cabinet_bar__plus_02_dot_48_bar__plus_00_dot_50_bar__minus_00_dot_02)
        (openable Drawer_bar__plus_02_dot_57_bar__plus_00_dot_90_bar__plus_00_dot_75)
        (openable Cabinet_bar__plus_02_dot_48_bar__plus_00_dot_50_bar__plus_01_dot_01)
        (openable Cabinet_bar__plus_02_dot_48_bar__plus_00_dot_50_bar__plus_02_dot_03)
        
        (atLocation agent1 loc_bar_4_bar_6_bar_2_bar_30)
        
        (cleanable Mug_bar__minus_01_dot_51_bar__plus_00_dot_66_bar__plus_01_dot_25)
        (cleanable Cloth_bar__plus_01_dot_76_bar__plus_00_dot_00_bar__minus_01_dot_73)
        
        (heatable Mug_bar__minus_01_dot_51_bar__plus_00_dot_66_bar__plus_01_dot_25)
        (coolable Mug_bar__minus_01_dot_51_bar__plus_00_dot_66_bar__plus_01_dot_25)
        
        
        
        
        
        
        
        (inReceptacle AlarmClock_bar__minus_01_dot_60_bar__plus_00_dot_66_bar__plus_01_dot_63 SideTable_bar__minus_01_dot_60_bar__minus_00_dot_01_bar__plus_01_dot_48)
        (inReceptacle Mug_bar__minus_01_dot_51_bar__plus_00_dot_66_bar__plus_01_dot_25 SideTable_bar__minus_01_dot_60_bar__minus_00_dot_01_bar__plus_01_dot_48)
        (inReceptacle CellPhone_bar__minus_01_dot_54_bar__plus_00_dot_36_bar__plus_01_dot_48 Drawer_bar__minus_01_dot_53_bar__plus_00_dot_44_bar__plus_01_dot_48)
        (inReceptacle Pencil_bar__minus_01_dot_65_bar__plus_00_dot_67_bar__minus_01_dot_48 SideTable_bar__minus_01_dot_60_bar__minus_00_dot_01_bar__minus_01_dot_25)
        (inReceptacle Pen_bar__minus_01_dot_60_bar__plus_00_dot_67_bar__minus_01_dot_25 SideTable_bar__minus_01_dot_60_bar__minus_00_dot_01_bar__minus_01_dot_25)
        (inReceptacle Book_bar__plus_02_dot_71_bar__plus_00_dot_18_bar__plus_01_dot_27 Cabinet_bar__plus_02_dot_48_bar__plus_00_dot_50_bar__plus_01_dot_01)
        (inReceptacle KeyChain_bar__minus_01_dot_54_bar__plus_00_dot_36_bar__minus_01_dot_42 Drawer_bar__minus_01_dot_53_bar__plus_00_dot_44_bar__minus_01_dot_25)
        (inReceptacle KeyChain_bar__plus_02_dot_48_bar__plus_00_dot_45_bar__minus_00_dot_54 ArmChair_bar__plus_02_dot_58_bar__minus_00_dot_01_bar__minus_00_dot_82)
        (inReceptacle CD_bar__plus_02_dot_81_bar__plus_00_dot_45_bar__minus_00_dot_62 ArmChair_bar__plus_02_dot_58_bar__minus_00_dot_01_bar__minus_00_dot_82)
        (inReceptacle KeyChain_bar__plus_02_dot_23_bar__plus_00_dot_45_bar__minus_00_dot_84 ArmChair_bar__plus_02_dot_58_bar__minus_00_dot_01_bar__minus_00_dot_82)
        (inReceptacle AlarmClock_bar__plus_03_dot_00_bar__plus_00_dot_98_bar__plus_00_dot_44 Dresser_bar__plus_02_dot_82_bar__plus_00_dot_00_bar__plus_01_dot_01)
        (inReceptacle Laptop_bar__plus_02_dot_91_bar__plus_00_dot_99_bar__plus_00_dot_16 Dresser_bar__plus_02_dot_82_bar__plus_00_dot_00_bar__plus_01_dot_01)
        (inReceptacle Pen_bar__plus_03_dot_00_bar__plus_00_dot_99_bar__plus_00_dot_72 Dresser_bar__plus_02_dot_82_bar__plus_00_dot_00_bar__plus_01_dot_01)
        (inReceptacle CreditCard_bar__plus_02_dot_56_bar__plus_00_dot_99_bar__plus_01_dot_58 Dresser_bar__plus_02_dot_82_bar__plus_00_dot_00_bar__plus_01_dot_01)
        (inReceptacle AlarmClock_bar__plus_02_dot_73_bar__plus_00_dot_98_bar__plus_00_dot_44 Dresser_bar__plus_02_dot_82_bar__plus_00_dot_00_bar__plus_01_dot_01)
        (inReceptacle Pen_bar__plus_03_dot_00_bar__plus_00_dot_99_bar__plus_01_dot_01 Dresser_bar__plus_02_dot_82_bar__plus_00_dot_00_bar__plus_01_dot_01)
        (inReceptacle Pillow_bar__minus_01_dot_52_bar__plus_00_dot_78_bar__plus_00_dot_78 Bed_bar__minus_00_dot_75_bar__plus_00_dot_03_bar__plus_00_dot_11)
        (inReceptacle Pillow_bar__plus_00_dot_09_bar__plus_00_dot_78_bar__minus_00_dot_33 Bed_bar__minus_00_dot_75_bar__plus_00_dot_03_bar__plus_00_dot_11)
        (inReceptacle Book_bar__plus_00_dot_09_bar__plus_00_dot_69_bar__plus_00_dot_56 Bed_bar__minus_00_dot_75_bar__plus_00_dot_03_bar__plus_00_dot_11)
        (inReceptacle Book_bar__minus_00_dot_72_bar__plus_00_dot_69_bar__minus_00_dot_33 Bed_bar__minus_00_dot_75_bar__plus_00_dot_03_bar__plus_00_dot_11)
        (inReceptacle CellPhone_bar__minus_01_dot_52_bar__plus_00_dot_69_bar__plus_00_dot_34 Bed_bar__minus_00_dot_75_bar__plus_00_dot_03_bar__plus_00_dot_11)
        (inReceptacle Pillow_bar__minus_00_dot_45_bar__plus_00_dot_78_bar__plus_00_dot_78 Bed_bar__minus_00_dot_75_bar__plus_00_dot_03_bar__plus_00_dot_11)
        (inReceptacle CreditCard_bar__plus_02_dot_58_bar__plus_00_dot_89_bar__plus_01_dot_63 Drawer_bar__plus_02_dot_57_bar__plus_00_dot_90_bar__plus_01_dot_78)
        (inReceptacle CD_bar__plus_01_dot_33_bar__plus_00_dot_02_bar__minus_01_dot_75 GarbageCan_bar__plus_01_dot_35_bar__plus_00_dot_00_bar__minus_01_dot_69)
        
        
        (receptacleAtLocation ArmChair_bar__plus_02_dot_58_bar__minus_00_dot_01_bar__minus_00_dot_82 loc_bar_7_bar__minus_3_bar_1_bar_60)
        (receptacleAtLocation Bed_bar__minus_00_dot_75_bar__plus_00_dot_03_bar__plus_00_dot_11 loc_bar_4_bar_0_bar_3_bar_45)
        (receptacleAtLocation Cabinet_bar__plus_02_dot_48_bar__plus_00_dot_50_bar__plus_01_dot_00 loc_bar_7_bar_2_bar_1_bar_60)
        (receptacleAtLocation Cabinet_bar__plus_02_dot_48_bar__plus_00_dot_50_bar__plus_01_dot_01 loc_bar_6_bar_3_bar_1_bar_45)
        (receptacleAtLocation Cabinet_bar__plus_02_dot_48_bar__plus_00_dot_50_bar__plus_02_dot_03 loc_bar_7_bar_6_bar_1_bar_60)
        (receptacleAtLocation Cabinet_bar__plus_02_dot_48_bar__plus_00_dot_50_bar__minus_00_dot_02 loc_bar_6_bar__minus_1_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__plus_02_dot_57_bar__plus_00_dot_90_bar__plus_00_dot_23 loc_bar_8_bar_1_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_02_dot_57_bar__plus_00_dot_90_bar__plus_00_dot_75 loc_bar_8_bar_3_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_02_dot_57_bar__plus_00_dot_90_bar__plus_01_dot_27 loc_bar_8_bar_5_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_02_dot_57_bar__plus_00_dot_90_bar__plus_01_dot_78 loc_bar_8_bar_7_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_53_bar__plus_00_dot_44_bar__plus_01_dot_48 loc_bar__minus_4_bar_6_bar_3_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_53_bar__plus_00_dot_44_bar__minus_01_dot_25 loc_bar__minus_4_bar__minus_5_bar_3_bar_60)
        (receptacleAtLocation Dresser_bar__plus_02_dot_82_bar__plus_00_dot_00_bar__plus_01_dot_01 loc_bar_9_bar_1_bar_0_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_01_dot_35_bar__plus_00_dot_00_bar__minus_01_dot_69 loc_bar_5_bar__minus_5_bar_2_bar_60)
        (receptacleAtLocation SideTable_bar__minus_01_dot_60_bar__minus_00_dot_01_bar__plus_01_dot_48 loc_bar__minus_2_bar_6_bar_3_bar_60)
        (receptacleAtLocation SideTable_bar__minus_01_dot_60_bar__minus_00_dot_01_bar__minus_01_dot_25 loc_bar__minus_2_bar__minus_5_bar_3_bar_60)
        (objectAtLocation CD_bar__plus_02_dot_81_bar__plus_00_dot_45_bar__minus_00_dot_62 loc_bar_7_bar__minus_3_bar_1_bar_60)
        (objectAtLocation CellPhone_bar__minus_01_dot_54_bar__plus_00_dot_36_bar__plus_01_dot_48 loc_bar__minus_4_bar_6_bar_3_bar_60)
        (objectAtLocation Pillow_bar__minus_00_dot_45_bar__plus_00_dot_78_bar__plus_00_dot_78 loc_bar_4_bar_0_bar_3_bar_45)
        (objectAtLocation AlarmClock_bar__plus_02_dot_73_bar__plus_00_dot_98_bar__plus_00_dot_44 loc_bar_9_bar_1_bar_0_bar_60)
        (objectAtLocation Pen_bar__minus_01_dot_60_bar__plus_00_dot_67_bar__minus_01_dot_25 loc_bar__minus_2_bar__minus_5_bar_3_bar_60)
        (objectAtLocation CreditCard_bar__plus_02_dot_58_bar__plus_00_dot_89_bar__plus_01_dot_63 loc_bar_8_bar_7_bar_1_bar_60)
        (objectAtLocation KeyChain_bar__plus_02_dot_48_bar__plus_00_dot_45_bar__minus_00_dot_54 loc_bar_7_bar__minus_3_bar_1_bar_60)
        (objectAtLocation Book_bar__plus_00_dot_09_bar__plus_00_dot_69_bar__plus_00_dot_56 loc_bar_4_bar_0_bar_3_bar_45)
        (objectAtLocation AlarmClock_bar__plus_03_dot_00_bar__plus_00_dot_98_bar__plus_00_dot_44 loc_bar_9_bar_1_bar_0_bar_60)
        (objectAtLocation Book_bar__minus_00_dot_72_bar__plus_00_dot_69_bar__minus_00_dot_33 loc_bar_4_bar_0_bar_3_bar_45)
        (objectAtLocation KeyChain_bar__minus_01_dot_54_bar__plus_00_dot_36_bar__minus_01_dot_42 loc_bar__minus_4_bar__minus_5_bar_3_bar_60)
        (objectAtLocation Pen_bar__plus_03_dot_00_bar__plus_00_dot_99_bar__plus_01_dot_01 loc_bar_9_bar_1_bar_0_bar_60)
        (objectAtLocation Book_bar__plus_02_dot_71_bar__plus_00_dot_18_bar__plus_01_dot_27 loc_bar_6_bar_3_bar_1_bar_45)
        (objectAtLocation Curtains_bar__minus_00_dot_02_bar__plus_02_dot_92_bar__minus_01_dot_85 loc_bar_0_bar__minus_5_bar_2_bar__minus_30)
        (objectAtLocation KeyChain_bar__plus_02_dot_23_bar__plus_00_dot_45_bar__minus_00_dot_84 loc_bar_7_bar__minus_3_bar_1_bar_60)
        (objectAtLocation Laptop_bar__plus_02_dot_91_bar__plus_00_dot_99_bar__plus_00_dot_16 loc_bar_9_bar_1_bar_0_bar_60)
        (objectAtLocation Cloth_bar__plus_01_dot_76_bar__plus_00_dot_00_bar__minus_01_dot_73 loc_bar_7_bar__minus_5_bar_2_bar_60)
        (objectAtLocation CreditCard_bar__plus_02_dot_56_bar__plus_00_dot_99_bar__plus_01_dot_58 loc_bar_9_bar_1_bar_0_bar_60)
        (objectAtLocation Pen_bar__plus_03_dot_00_bar__plus_00_dot_99_bar__plus_00_dot_72 loc_bar_9_bar_1_bar_0_bar_60)
        (objectAtLocation Pencil_bar__minus_01_dot_65_bar__plus_00_dot_67_bar__minus_01_dot_48 loc_bar__minus_2_bar__minus_5_bar_3_bar_60)
        (objectAtLocation BaseballBat_bar__plus_03_dot_08_bar__plus_00_dot_63_bar__minus_01_dot_76 loc_bar_7_bar__minus_5_bar_1_bar_45)
        (objectAtLocation LightSwitch_bar__plus_01_dot_32_bar__plus_01_dot_30_bar__plus_02_dot_20 loc_bar_5_bar_7_bar_0_bar_45)
        (objectAtLocation AlarmClock_bar__minus_01_dot_60_bar__plus_00_dot_66_bar__plus_01_dot_63 loc_bar__minus_2_bar_6_bar_3_bar_60)
        (objectAtLocation BaseballBat_bar__plus_00_dot_81_bar__plus_00_dot_05_bar__plus_02_dot_15 loc_bar_4_bar_7_bar_0_bar_60)
        (objectAtLocation Pillow_bar__minus_01_dot_52_bar__plus_00_dot_78_bar__plus_00_dot_78 loc_bar_4_bar_0_bar_3_bar_45)
        (objectAtLocation Pillow_bar__plus_00_dot_09_bar__plus_00_dot_78_bar__minus_00_dot_33 loc_bar_4_bar_0_bar_3_bar_45)
        (objectAtLocation Painting_bar__plus_01_dot_53_bar__plus_01_dot_52_bar__minus_01_dot_85 loc_bar_6_bar__minus_5_bar_2_bar_15)
        (objectAtLocation Mirror_bar__plus_00_dot_58_bar__plus_01_dot_35_bar__plus_02_dot_20 loc_bar_2_bar_6_bar_0_bar_30)
        (objectAtLocation CellPhone_bar__minus_01_dot_52_bar__plus_00_dot_69_bar__plus_00_dot_34 loc_bar_4_bar_0_bar_3_bar_45)
        (objectAtLocation CD_bar__plus_01_dot_33_bar__plus_00_dot_02_bar__minus_01_dot_75 loc_bar_5_bar__minus_5_bar_2_bar_60)
        (objectAtLocation Window_bar__minus_00_dot_01_bar__plus_01_dot_69_bar__minus_01_dot_92 loc_bar_0_bar__minus_5_bar_2_bar_0)
        (objectAtLocation Mug_bar__minus_01_dot_51_bar__plus_00_dot_66_bar__plus_01_dot_25 loc_bar__minus_2_bar_6_bar_3_bar_60)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 KeyChainType)
                                    (receptacleType ?r SideTableType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 KeyChainType)
                                            (receptacleType ?r SideTableType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            