{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000211.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000212.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000283.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000284.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000285.png", "low_idx": 55}, {"high_idx": 7, "image_name": "000000286.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000287.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000288.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000289.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000290.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000291.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000292.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000293.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000294.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000295.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000296.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000297.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000298.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000299.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000300.png", "low_idx": 56}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Pillow", "parent_target": "ArmChair", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["bed"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|4|-4|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["pillow"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Pillow", [3.8750832, 3.8750832, -0.18448996, -0.18448996, 2.7083828, 2.7083828]], "coordinateReceptacleObjectId": ["Bed", [3.764, 3.764, 0.584, 0.584, 0.0, 0.0]], "forceVisible": true, "objectId": "Pillow|+00.97|+00.68|-00.05"}}, {"discrete_action": {"action": "GotoLocation", "args": ["armchair"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|7|-8|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["pillow", "armchair"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Pillow", [3.8750832, 3.8750832, -0.18448996, -0.18448996, 2.7083828, 2.7083828]], "coordinateReceptacleObjectId": ["ArmChair", [2.804, 2.804, -9.068, -9.068, 0.055999998, 0.055999998]], "forceVisible": true, "objectId": "Pillow|+00.97|+00.68|-00.05", "receptacleObjectId": "ArmC<PERSON>r|+00.70|+00.01|-02.27"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bed"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|3|-4|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["pillow"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Pillow", [2.14, 2.14, -0.476, -0.476, 2.5556, 2.5556]], "coordinateReceptacleObjectId": ["Bed", [3.764, 3.764, 0.584, 0.584, 0.0, 0.0]], "forceVisible": true, "objectId": "Pillow|+00.54|+00.64|-00.12"}}, {"discrete_action": {"action": "GotoLocation", "args": ["armchair"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|7|-8|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["pillow", "armchair"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Pillow", [2.14, 2.14, -0.476, -0.476, 2.5556, 2.5556]], "coordinateReceptacleObjectId": ["ArmChair", [2.804, 2.804, -9.068, -9.068, 0.055999998, 0.055999998]], "forceVisible": true, "objectId": "Pillow|+00.54|+00.64|-00.12", "receptacleObjectId": "ArmC<PERSON>r|+00.70|+00.01|-02.27"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Pillow|+00.97|+00.68|-00.05"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [104, 117, 186, 171], "mask": [[34918, 8], [34929, 15], [34948, 14], [34966, 8], [35212, 67], [35511, 70], [35810, 71], [36110, 72], [36410, 72], [36709, 73], [37009, 73], [37309, 73], [37609, 73], [37909, 73], [38209, 74], [38509, 74], [38808, 75], [39108, 75], [39408, 75], [39708, 75], [40008, 75], [40307, 76], [40607, 77], [40907, 77], [41207, 77], [41507, 77], [41807, 77], [42106, 78], [42406, 79], [42706, 79], [43006, 79], [43306, 79], [43606, 79], [43905, 80], [44205, 80], [44505, 80], [44805, 81], [45105, 81], [45404, 82], [45704, 82], [46004, 82], [46304, 82], [46604, 82], [46904, 83], [47204, 83], [47504, 83], [47804, 83], [48104, 82], [48404, 82], [48704, 82], [49004, 82], [49305, 81], [49605, 80], [49905, 80], [50206, 79], [50506, 78], [50806, 78], [51108, 74]], "point": [145, 143]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Pillow|+00.97|+00.68|-00.05", "placeStationary": true, "receptacleObjectId": "ArmC<PERSON>r|+00.70|+00.01|-02.27"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 217, 179], "mask": [[0, 142], [300, 143], [600, 144], [900, 145], [1200, 146], [1500, 147], [1800, 148], [2100, 149], [2400, 150], [2700, 151], [3000, 152], [3300, 153], [3600, 154], [3900, 155], [4200, 156], [4500, 157], [4800, 158], [5100, 159], [5400, 160], [5700, 161], [6000, 162], [6300, 163], [6600, 164], [6900, 165], [7200, 166], [7500, 167], [7800, 167], [8100, 168], [8400, 169], [8700, 170], [9000, 171], [9300, 171], [9600, 172], [9900, 173], [10200, 174], [10500, 175], [10800, 175], [11100, 176], [11400, 177], [11700, 178], [12000, 179], [12300, 180], [12600, 180], [12900, 181], [13200, 182], [13500, 183], [13800, 183], [14100, 184], [14400, 184], [14700, 185], [15000, 185], [15300, 186], [15600, 186], [15900, 187], [16200, 187], [16500, 188], [16800, 188], [17100, 189], [17400, 189], [17700, 190], [18000, 190], [18300, 191], [18600, 191], [18900, 192], [19200, 192], [19500, 193], [19800, 193], [20100, 193], [20400, 194], [20700, 194], [21000, 194], [21300, 195], [21600, 195], [21900, 195], [22200, 196], [22500, 196], [22800, 196], [23100, 197], [23400, 197], [23700, 197], [24000, 197], [24300, 198], [24600, 198], [24900, 199], [25200, 199], [25500, 199], [25800, 200], [26100, 200], [26400, 201], [26700, 202], [27000, 203], [27300, 204], [27600, 205], [27901, 205], [28201, 206], [28501, 207], [28802, 206], [29102, 207], [29403, 207], [29703, 208], [30004, 208], [30304, 208], [30604, 209], [30905, 208], [31205, 209], [31506, 209], [31806, 209], [32107, 209], [32407, 209], [32707, 210], [33008, 210], [33308, 210], [33609, 209], [33909, 209], [34209, 209], [34510, 208], [34810, 208], [35111, 207], [35411, 207], [35712, 206], [36012, 206], [36313, 205], [36613, 205], [36914, 204], [37214, 203], [37515, 202], [37815, 202], [38116, 200], [38416, 200], [38717, 198], [39017, 198], [39318, 197], [39618, 196], [39919, 195], [40219, 194], [40520, 193], [40820, 193], [41121, 191], [41421, 191], [41722, 190], [42022, 189], [42323, 188], [42624, 186], [42924, 186], [43225, 185], [43525, 183], [43826, 181], [44126, 179], [44427, 177], [44728, 174], [45028, 172], [45329, 169], [45629, 167], [45930, 164], [46230, 162], [46531, 159], [46831, 157], [47132, 154], [47432, 152], [47733, 150], [48033, 148], [48334, 27], [48372, 107], [48634, 24], [48935, 20], [49235, 17], [49536, 14], [49836, 13], [50136, 13], [50437, 11], [50737, 10], [51038, 8], [51338, 7], [51638, 6], [51939, 5], [52239, 4], [52540, 3], [52840, 2], [53140, 2], [53441, 1]], "point": [108, 89]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Pillow|+00.54|+00.64|-00.12"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [62, 125, 143, 196], "mask": [[37289, 13], [37587, 20], [37886, 33], [38186, 45], [38485, 52], [38785, 57], [39084, 59], [39384, 60], [39683, 61], [39983, 61], [40282, 62], [40582, 62], [40881, 63], [41181, 63], [41480, 64], [41780, 64], [42079, 65], [42379, 65], [42678, 66], [42978, 66], [43277, 67], [43577, 67], [43876, 67], [44176, 67], [44475, 68], [44774, 68], [45074, 68], [45373, 69], [45673, 69], [45972, 69], [46272, 69], [46571, 70], [46871, 70], [47170, 70], [47470, 70], [47769, 71], [48069, 70], [48368, 71], [48668, 71], [48967, 72], [49267, 71], [49566, 72], [49866, 72], [50165, 73], [50464, 74], [50764, 73], [51063, 74], [51363, 74], [51662, 74], [51962, 74], [52262, 74], [52562, 74], [52862, 73], [53163, 72], [53463, 72], [53763, 72], [54063, 71], [54363, 71], [54663, 71], [54964, 70], [55264, 69], [55565, 68], [55866, 67], [56167, 66], [56469, 63], [56774, 58], [57083, 49], [57391, 41], [57697, 35], [58006, 25], [58312, 18], [58619, 10]], "point": [102, 159]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Pillow|+00.54|+00.64|-00.12", "placeStationary": true, "receptacleObjectId": "ArmC<PERSON>r|+00.70|+00.01|-02.27"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 217, 179], "mask": [[0, 142], [300, 143], [600, 144], [900, 145], [1200, 146], [1500, 147], [1800, 148], [2100, 149], [2400, 150], [2700, 151], [3000, 152], [3300, 153], [3600, 154], [3900, 155], [4200, 156], [4500, 157], [4800, 158], [5100, 159], [5400, 160], [5700, 161], [6000, 162], [6300, 163], [6600, 164], [6900, 165], [7200, 166], [7500, 167], [7800, 167], [8100, 168], [8400, 169], [8700, 170], [9000, 171], [9300, 171], [9600, 172], [9900, 173], [10200, 174], [10500, 175], [10800, 175], [11100, 176], [11400, 177], [11700, 178], [12000, 179], [12300, 180], [12600, 180], [12900, 181], [13200, 182], [13500, 183], [13800, 183], [14100, 184], [14400, 184], [14700, 185], [15000, 185], [15300, 186], [15600, 186], [15900, 187], [16200, 187], [16500, 188], [16800, 188], [17100, 189], [17400, 189], [17700, 190], [18000, 190], [18300, 191], [18600, 191], [18900, 192], [19200, 192], [19500, 70], [19571, 122], [19800, 63], [19872, 121], [20100, 60], [20174, 119], [20400, 58], [20475, 119], [20700, 57], [20777, 117], [21000, 55], [21078, 116], [21300, 53], [21379, 116], [21600, 54], [21680, 115], [21900, 53], [21981, 114], [22200, 53], [22282, 114], [22500, 53], [22583, 113], [22800, 53], [22883, 113], [23100, 53], [23184, 113], [23400, 53], [23485, 112], [23700, 53], [23785, 112], [24000, 53], [24086, 111], [24300, 53], [24386, 112], [24600, 53], [24687, 111], [24900, 53], [24988, 111], [25200, 53], [25289, 110], [25500, 53], [25589, 110], [25800, 53], [25890, 110], [26100, 53], [26191, 109], [26400, 53], [26491, 110], [26700, 54], [26792, 110], [27000, 54], [27093, 110], [27300, 55], [27393, 111], [27600, 55], [27694, 111], [27901, 55], [27994, 112], [28201, 55], [28294, 113], [28501, 56], [28595, 113], [28802, 56], [28895, 113], [29102, 56], [29196, 113], [29403, 55], [29497, 113], [29703, 56], [29798, 113], [30004, 55], [30099, 113], [30304, 56], [30399, 113], [30604, 56], [30700, 113], [30905, 55], [31001, 112], [31205, 56], [31301, 113], [31506, 55], [31602, 113], [31806, 56], [31902, 113], [32107, 56], [32203, 113], [32407, 56], [32503, 113], [32707, 57], [32804, 113], [33008, 56], [33104, 114], [33308, 56], [33405, 113], [33609, 55], [33705, 113], [33909, 56], [34006, 112], [34209, 56], [34307, 111], [34510, 56], [34608, 110], [34810, 56], [34908, 110], [35111, 56], [35209, 109], [35411, 56], [35509, 109], [35712, 56], [35810, 108], [36012, 57], [36110, 108], [36313, 56], [36410, 108], [36613, 56], [36710, 108], [36914, 56], [37011, 107], [37214, 56], [37311, 106], [37515, 55], [37611, 106], [37815, 56], [37911, 106], [38116, 55], [38211, 105], [38416, 56], [38511, 105], [38717, 55], [38811, 104], [39017, 56], [39112, 103], [39318, 56], [39412, 103], [39618, 57], [39712, 102], [39919, 56], [40013, 101], [40219, 57], [40313, 100], [40520, 56], [40613, 100], [40820, 57], [40913, 100], [41121, 56], [41213, 99], [41421, 56], [41513, 99], [41722, 56], [41814, 98], [42022, 56], [42114, 97], [42323, 56], [42415, 96], [42624, 55], [42715, 95], [42924, 56], [43015, 95], [43225, 55], [43315, 95], [43525, 56], [43615, 93], [43826, 56], [43915, 92], [44126, 57], [44215, 90], [44427, 56], [44515, 89], [44728, 56], [44815, 87], [45028, 56], [45115, 85], [45329, 56], [45415, 83], [45629, 56], [45715, 81], [45930, 55], [46015, 79], [46230, 56], [46314, 78], [46531, 55], [46614, 76], [46831, 56], [46914, 74], [47132, 56], [47214, 72], [47432, 57], [47513, 71], [47733, 57], [47812, 71], [48033, 58], [48110, 71], [48334, 27], [48372, 20], [48409, 70], [48634, 24], [48935, 20], [49235, 17], [49536, 14], [49836, 13], [50136, 13], [50437, 11], [50737, 10], [51038, 8], [51338, 7], [51638, 6], [51939, 5], [52239, 4], [52540, 3], [52840, 2], [53140, 2], [53441, 1]], "point": [108, 89]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan321", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": 3.0, "y": 0.9009992, "z": 0.75}, "object_poses": [{"objectName": "KeyChain_1a9bddbb", "position": {"x": 0.2917841, "y": 0.7071626, "z": 1.29679394}, "rotation": {"x": 0.0, "y": 90.0007, "z": 0.0}}, {"objectName": "Book_9dcb1956", "position": {"x": 1.577003, "y": 0.5961157, "z": -0.238244981}, "rotation": {"x": 0.0, "y": 2.3905659e-05, "z": 0.0}}, {"objectName": "Book_9dcb1956", "position": {"x": 0.968771, "y": 0.5961157, "z": 0.5302448}, "rotation": {"x": 0.0, "y": 2.3905659e-05, "z": 0.0}}, {"objectName": "KeyChain_1a9bddbb", "position": {"x": 0.313432783, "y": 0.6176292, "z": -1.09348416}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Pillow_801065cc", "position": {"x": 0.9687708, "y": 0.6770957, "z": -0.04612249}, "rotation": {"x": 0.0, "y": 2.3905659e-05, "z": 0.0}}, {"objectName": "Pillow_b8577ff9", "position": {"x": 0.535, "y": 0.6389, "z": -0.119}, "rotation": {"x": 0.0, "y": 279.1212, "z": 0.0}}, {"objectName": "CreditCard_2946225d", "position": {"x": 3.08483267, "y": 0.779769242, "z": -1.32210469}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_369f1c16", "position": {"x": 3.239, "y": 0.783652842, "z": -1.32210469}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pencil_7030f200", "position": {"x": 3.08483267, "y": 0.784027159, "z": -1.59}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "TissueBox_528d8e8a", "position": {"x": 3.7015028, "y": 0.779699564, "z": -1.67929852}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_f3c3a308", "position": {"x": 3.08483267, "y": 0.777773261, "z": -1.76859689}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_a02909a5", "position": {"x": 0.2284198, "y": 0.70448935, "z": -1.18653691}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "AlarmClock_55797a1f", "position": {"x": 2.930665, "y": 0.7780428, "z": -1.32210469}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CD_f2466fdb", "position": {"x": 0.323681623, "y": 0.7041067, "z": -1.10207188}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}], "object_toggles": [], "random_seed": 3813394856, "scene_num": 321}, "task_id": "trial_T20190907_121714_804185", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "AJQGWGESKQT4Y_32SCWG5HIKLNLVIB7F1U7T8OZXLP6C", "high_descs": ["Go to the right and stand in front of the purple pillows on the bed.", "Pick up the purple pillow on the right of the other purple pillow on the bed.", "Go to the right and stand in front of the chair in the corner.", "Put the pillow on the chair.", "Turn around and stand in front of the bed where the purple pillow is.", "Pick the pillow up from the bed.", "Turn around and stand in front of the chair in the corner with the purple pillow.", "Put the purple pillow on the chair."], "task_desc": "Put two pillows on a chair.", "votes": [1, 1, 1, 1, 1, 1, 1, 1]}, {"assignment_id": "A1MKCTVKE7J0ZP_3YT88D1N0BFNDLYDBLYO49A5V40K33", "high_descs": ["Go to the desk in front of you, then turn right and go forward, then turn right to face the bed.", "Pick up the pillow on the bed, on the right.", "Turn around, and go to the chair in front of you.", "Place the pillow on the chair.", "Turn around and return to the bed.", "Pick up the pillow on the bed.", "Turn around and go back to the chair.", "Place the pillow on the chair, next to the other pillow."], "task_desc": "Put two pillows on the chair.", "votes": [1, 1, 1, 1, 1, 1, 1, 1]}, {"assignment_id": "A4CHLWPHZIP7Y_3ZR9AIQJUEQ6IRAD76TPVUN2V24044", "high_descs": ["Go to the brown desk, turn right, go to the black table, and turn right to face the bed.", "Take one purple cushion from the bed.", "Turn around and go to the tan chair.", "Place the purple cushion on the tan chair.", "Turn around, go to the bed, turn left, go to the black table, and turn right to face the bed.", "Take one purple cushion from the bed.", "Turn around and go to the tan chair.", "Place the purple cushion on the tan chair."], "task_desc": "Place two purple cushions on the tan chair.", "votes": [1, 1, 1, 1, 1, 1, 1, 1]}, {"assignment_id": "A2NGMLBFZ3YQP5_36WLNQG782RPUGAGUS2ROXVCZ4DEBA", "high_descs": ["Go to table, turn right, go to the bed, turn right to face bed.", "Pick up the right hand pillow from the bed.", "Turn right, cross to table, turn right at table, go to wall, turn right to face armchair.", "Put the pillow on the armchair.", "Turn right, cross to bed, walk to head of bed, face the bed.", "Pick up the pillow from the bed.", "Turn right, cross to table, turn right at table, go to wall, turn right to face armchair.", "Put pillow to the right of the other pillow on the chair."], "task_desc": "Put two pillows on an armchair.", "votes": [1, 1, 0]}]}}