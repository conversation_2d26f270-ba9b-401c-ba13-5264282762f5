
(define (problem plan_trial_T20190907_184422_458884)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Apple_bar__plus_00_dot_59_bar__plus_00_dot_97_bar__minus_01_dot_37 - object
        Apple_bar__minus_02_dot_37_bar__plus_00_dot_97_bar__plus_00_dot_93 - object
        Apple_bar__minus_02_dot_49_bar__plus_01_dot_15_bar__minus_00_dot_60 - object
        Bowl_bar__minus_02_dot_46_bar__plus_00_dot_91_bar__plus_01_dot_23 - object
        Bowl_bar__minus_02_dot_65_bar__plus_01_dot_53_bar__plus_01_dot_41 - object
        Bread_bar__minus_00_dot_08_bar__plus_01_dot_00_bar__plus_01_dot_32 - object
        Bread_bar__minus_02_dot_21_bar__plus_01_dot_00_bar__plus_01_dot_53 - object
        ButterKnife_bar__plus_01_dot_45_bar__plus_00_dot_92_bar__minus_01_dot_07 - object
        Chair_bar__minus_02_dot_30_bar__plus_00_dot_01_bar__plus_01_dot_84 - object
        Cup_bar__plus_00_dot_77_bar__plus_00_dot_91_bar__minus_01_dot_58 - object
        DishSponge_bar__plus_01_dot_69_bar__plus_00_dot_18_bar__plus_00_dot_71 - object
        DishSponge_bar__minus_00_dot_21_bar__plus_00_dot_91_bar__plus_01_dot_09 - object
        Egg_bar__plus_01_dot_39_bar__plus_00_dot_95_bar__minus_01_dot_27 - object
        Faucet_bar__plus_01_dot_24_bar__plus_01_dot_14_bar__minus_01_dot_47 - object
        Fork_bar__plus_01_dot_62_bar__plus_00_dot_91_bar__minus_00_dot_82 - object
        Fork_bar__minus_02_dot_37_bar__plus_00_dot_91_bar__plus_00_dot_33 - object
        Fork_bar__minus_02_dot_54_bar__plus_00_dot_91_bar__plus_01_dot_23 - object
        Kettle_bar__plus_01_dot_70_bar__plus_01_dot_66_bar__plus_00_dot_31 - object
        Knife_bar__minus_02_dot_20_bar__plus_00_dot_80_bar__plus_00_dot_39 - object
        Knife_bar__minus_02_dot_26_bar__plus_00_dot_80_bar__plus_00_dot_39 - object
        Knife_bar__minus_02_dot_35_bar__plus_00_dot_80_bar__plus_00_dot_39 - object
        Lettuce_bar__minus_02_dot_54_bar__plus_00_dot_99_bar__plus_01_dot_53 - object
        LightSwitch_bar__minus_00_dot_93_bar__plus_01_dot_35_bar__minus_01_dot_95 - object
        Mug_bar__minus_02_dot_39_bar__plus_01_dot_08_bar__minus_01_dot_07 - object
        Pan_bar__plus_01_dot_69_bar__plus_00_dot_93_bar__plus_00_dot_06 - object
        PaperTowelRoll_bar__plus_01_dot_77_bar__plus_01_dot_02_bar__plus_00_dot_35 - object
        PepperShaker_bar__minus_02_dot_56_bar__plus_01_dot_53_bar__plus_02_dot_06 - object
        Plate_bar__minus_00_dot_60_bar__plus_00_dot_91_bar__plus_01_dot_54 - object
        Potato_bar__minus_00_dot_08_bar__plus_00_dot_95_bar__plus_01_dot_54 - object
        Potato_bar__minus_02_dot_39_bar__plus_00_dot_25_bar__minus_00_dot_90 - object
        Potato_bar__minus_02_dot_53_bar__plus_01_dot_13_bar__minus_00_dot_79 - object
        Pot_bar__plus_01_dot_69_bar__plus_00_dot_93_bar__minus_00_dot_34 - object
        SaltShaker_bar__minus_02_dot_24_bar__plus_00_dot_11_bar__plus_00_dot_49 - object
        SaltShaker_bar__minus_02_dot_68_bar__plus_01_dot_53_bar__plus_01_dot_84 - object
        Sink_bar__plus_01_dot_38_bar__plus_00_dot_81_bar__minus_01_dot_27 - object
        SoapBottle_bar__plus_00_dot_05_bar__plus_00_dot_91_bar__plus_00_dot_42 - object
        SoapBottle_bar__minus_02_dot_37_bar__plus_00_dot_91_bar__plus_00_dot_63 - object
        Spatula_bar__plus_01_dot_42_bar__plus_00_dot_93_bar__minus_01_dot_25 - object
        Spatula_bar__minus_02_dot_21_bar__plus_00_dot_93_bar__plus_00_dot_33 - object
        Spoon_bar__plus_01_dot_45_bar__plus_00_dot_92_bar__minus_01_dot_07 - object
        Spoon_bar__minus_00_dot_60_bar__plus_00_dot_91_bar__plus_01_dot_32 - object
        StoveKnob_bar__plus_01_dot_88_bar__plus_01_dot_09_bar__plus_00_dot_02 - object
        StoveKnob_bar__plus_01_dot_88_bar__plus_01_dot_09_bar__plus_00_dot_15 - object
        StoveKnob_bar__plus_01_dot_88_bar__plus_01_dot_09_bar__minus_00_dot_31 - object
        StoveKnob_bar__plus_01_dot_88_bar__plus_01_dot_09_bar__minus_00_dot_44 - object
        Tomato_bar__minus_02_dot_21_bar__plus_00_dot_95_bar__plus_01_dot_83 - object
        Window_bar__plus_01_dot_51_bar__plus_01_dot_53_bar__minus_01_dot_50 - object
        Window_bar__plus_01_dot_97_bar__plus_01_dot_52_bar__plus_01_dot_12 - object
        Cabinet_bar__plus_00_dot_15_bar__plus_02_dot_01_bar__minus_01_dot_60 - receptacle
        Cabinet_bar__plus_01_dot_57_bar__plus_02_dot_01_bar__plus_00_dot_47 - receptacle
        Cabinet_bar__plus_01_dot_57_bar__plus_02_dot_01_bar__minus_00_dot_78 - receptacle
        Cabinet_bar__minus_02_dot_15_bar__plus_00_dot_40_bar__plus_00_dot_64 - receptacle
        Cabinet_bar__minus_02_dot_15_bar__plus_00_dot_40_bar__plus_00_dot_70 - receptacle
        Cabinet_bar__minus_02_dot_15_bar__plus_00_dot_40_bar__plus_01_dot_58 - receptacle
        Cabinet_bar__minus_02_dot_15_bar__plus_00_dot_40_bar__minus_00_dot_24 - receptacle
        Cabinet_bar__minus_02_dot_29_bar__plus_01_dot_97_bar__minus_01_dot_33 - receptacle
        Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_00_dot_36 - receptacle
        Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_00_dot_41 - receptacle
        Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_01_dot_64 - receptacle
        Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_01_dot_69 - receptacle
        Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_02_dot_93 - receptacle
        Cabinet_bar__minus_02_dot_45_bar__plus_02_dot_15_bar__minus_00_dot_29 - receptacle
        Cabinet_bar__minus_02_dot_45_bar__plus_02_dot_15_bar__minus_01_dot_28 - receptacle
        CoffeeMachine_bar__plus_00_dot_46_bar__plus_00_dot_90_bar__minus_01_dot_82 - receptacle
        CounterTop_bar__plus_00_dot_47_bar__plus_00_dot_95_bar__minus_01_dot_63 - receptacle
        CounterTop_bar__plus_01_dot_59_bar__plus_00_dot_95_bar__plus_00_dot_41 - receptacle
        CounterTop_bar__minus_00_dot_36_bar__plus_00_dot_95_bar__plus_01_dot_09 - receptacle
        CounterTop_bar__minus_01_dot_49_bar__plus_00_dot_95_bar__plus_01_dot_32 - receptacle
        Drawer_bar__minus_02_dot_28_bar__plus_00_dot_79_bar__plus_00_dot_44 - receptacle
        Drawer_bar__minus_02_dot_28_bar__plus_00_dot_79_bar__plus_00_dot_90 - receptacle
        Drawer_bar__minus_02_dot_28_bar__plus_00_dot_79_bar__plus_01_dot_37 - receptacle
        Drawer_bar__minus_02_dot_28_bar__plus_00_dot_79_bar__minus_00_dot_03 - receptacle
        Fridge_bar__minus_02_dot_48_bar__plus_00_dot_00_bar__minus_00_dot_78 - receptacle
        GarbageCan_bar__plus_01_dot_65_bar__plus_00_dot_00_bar__plus_00_dot_68 - receptacle
        Microwave_bar__minus_02_dot_58_bar__plus_00_dot_90_bar__plus_02_dot_44 - receptacle
        Sink_bar__plus_01_dot_38_bar__plus_00_dot_81_bar__minus_01_dot_27_bar_SinkBasin - receptacle
        StoveBurner_bar__plus_01_dot_41_bar__plus_00_dot_92_bar__plus_00_dot_06 - receptacle
        StoveBurner_bar__plus_01_dot_41_bar__plus_00_dot_92_bar__minus_00_dot_34 - receptacle
        StoveBurner_bar__plus_01_dot_69_bar__plus_00_dot_92_bar__plus_00_dot_06 - receptacle
        StoveBurner_bar__plus_01_dot_69_bar__plus_00_dot_92_bar__minus_00_dot_34 - receptacle
        Toaster_bar__minus_02_dot_57_bar__plus_00_dot_90_bar__plus_01_dot_88 - receptacle
        loc_bar__minus_7_bar_8_bar_3_bar__minus_30 - location
        loc_bar__minus_7_bar_2_bar_0_bar_45 - location
        loc_bar__minus_6_bar_1_bar_3_bar__minus_15 - location
        loc_bar__minus_7_bar_8_bar_3_bar_45 - location
        loc_bar_3_bar_1_bar_1_bar_30 - location
        loc_bar_3_bar_1_bar_1_bar__minus_30 - location
        loc_bar_3_bar_2_bar_1_bar_60 - location
        loc_bar__minus_6_bar__minus_3_bar_3_bar_60 - location
        loc_bar__minus_6_bar_1_bar_3_bar_45 - location
        loc_bar__minus_6_bar_5_bar_3_bar_45 - location
        loc_bar__minus_5_bar_2_bar_3_bar_60 - location
        loc_bar__minus_6_bar_7_bar_3_bar_45 - location
        loc_bar_4_bar_3_bar_1_bar_45 - location
        loc_bar__minus_7_bar_10_bar_3_bar_45 - location
        loc_bar_2_bar__minus_4_bar_2_bar_45 - location
        loc_bar_3_bar_0_bar_1_bar_45 - location
        loc_bar__minus_1_bar_9_bar_2_bar_30 - location
        loc_bar_2_bar__minus_4_bar_2_bar__minus_30 - location
        loc_bar_3_bar_0_bar_1_bar_30 - location
        loc_bar__minus_5_bar__minus_5_bar_3_bar__minus_15 - location
        loc_bar_4_bar__minus_3_bar_2_bar_60 - location
        loc_bar__minus_6_bar_5_bar_3_bar__minus_15 - location
        loc_bar__minus_5_bar__minus_2_bar_3_bar_60 - location
        loc_bar_3_bar__minus_2_bar_1_bar__minus_30 - location
        loc_bar__minus_7_bar_7_bar_3_bar_60 - location
        loc_bar__minus_5_bar_0_bar_3_bar_60 - location
        loc_bar__minus_5_bar_7_bar_3_bar_60 - location
        loc_bar_3_bar__minus_4_bar_1_bar_15 - location
        loc_bar_3_bar__minus_1_bar_1_bar_30 - location
        loc_bar__minus_5_bar__minus_3_bar_3_bar__minus_30 - location
        loc_bar__minus_7_bar_10_bar_3_bar__minus_30 - location
        loc_bar__minus_7_bar_3_bar_3_bar__minus_30 - location
        loc_bar__minus_6_bar_3_bar_3_bar_45 - location
        loc_bar_3_bar__minus_1_bar_1_bar_45 - location
        loc_bar_5_bar_4_bar_1_bar_15 - location
        loc_bar__minus_4_bar__minus_6_bar_2_bar_30 - location
        loc_bar_4_bar__minus_3_bar_1_bar_30 - location
        loc_bar_3_bar__minus_4_bar_1_bar_60 - location
        loc_bar__minus_5_bar__minus_4_bar_3_bar__minus_30 - location
        loc_bar__minus_4_bar__minus_6_bar_3_bar_30 - location
        )
    

(:init


        (receptacleType Sink_bar__plus_01_dot_38_bar__plus_00_dot_81_bar__minus_01_dot_27_bar_SinkBasin SinkBasinType)
        (receptacleType Cabinet_bar__minus_02_dot_15_bar__plus_00_dot_40_bar__plus_00_dot_70 CabinetType)
        (receptacleType Cabinet_bar__minus_02_dot_15_bar__plus_00_dot_40_bar__plus_00_dot_64 CabinetType)
        (receptacleType CoffeeMachine_bar__plus_00_dot_46_bar__plus_00_dot_90_bar__minus_01_dot_82 CoffeeMachineType)
        (receptacleType Toaster_bar__minus_02_dot_57_bar__plus_00_dot_90_bar__plus_01_dot_88 ToasterType)
        (receptacleType Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_01_dot_69 CabinetType)
        (receptacleType Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_01_dot_64 CabinetType)
        (receptacleType CounterTop_bar__plus_00_dot_47_bar__plus_00_dot_95_bar__minus_01_dot_63 CounterTopType)
        (receptacleType Microwave_bar__minus_02_dot_58_bar__plus_00_dot_90_bar__plus_02_dot_44 MicrowaveType)
        (receptacleType CounterTop_bar__minus_00_dot_36_bar__plus_00_dot_95_bar__plus_01_dot_09 CounterTopType)
        (receptacleType Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_00_dot_41 CabinetType)
        (receptacleType StoveBurner_bar__plus_01_dot_41_bar__plus_00_dot_92_bar__minus_00_dot_34 StoveBurnerType)
        (receptacleType Fridge_bar__minus_02_dot_48_bar__plus_00_dot_00_bar__minus_00_dot_78 FridgeType)
        (receptacleType Cabinet_bar__plus_01_dot_57_bar__plus_02_dot_01_bar__minus_00_dot_78 CabinetType)
        (receptacleType CounterTop_bar__plus_01_dot_59_bar__plus_00_dot_95_bar__plus_00_dot_41 CounterTopType)
        (receptacleType Cabinet_bar__plus_00_dot_15_bar__plus_02_dot_01_bar__minus_01_dot_60 CabinetType)
        (receptacleType Drawer_bar__minus_02_dot_28_bar__plus_00_dot_79_bar__plus_00_dot_44 DrawerType)
        (receptacleType Cabinet_bar__minus_02_dot_15_bar__plus_00_dot_40_bar__minus_00_dot_24 CabinetType)
        (receptacleType Drawer_bar__minus_02_dot_28_bar__plus_00_dot_79_bar__plus_01_dot_37 DrawerType)
        (receptacleType CounterTop_bar__minus_01_dot_49_bar__plus_00_dot_95_bar__plus_01_dot_32 CounterTopType)
        (receptacleType Cabinet_bar__minus_02_dot_29_bar__plus_01_dot_97_bar__minus_01_dot_33 CabinetType)
        (receptacleType Cabinet_bar__minus_02_dot_15_bar__plus_00_dot_40_bar__plus_01_dot_58 CabinetType)
        (receptacleType Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_00_dot_36 CabinetType)
        (receptacleType GarbageCan_bar__plus_01_dot_65_bar__plus_00_dot_00_bar__plus_00_dot_68 GarbageCanType)
        (receptacleType Cabinet_bar__minus_02_dot_45_bar__plus_02_dot_15_bar__minus_00_dot_29 CabinetType)
        (receptacleType Cabinet_bar__minus_02_dot_45_bar__plus_02_dot_15_bar__minus_01_dot_28 CabinetType)
        (receptacleType Drawer_bar__minus_02_dot_28_bar__plus_00_dot_79_bar__plus_00_dot_90 DrawerType)
        (receptacleType Drawer_bar__minus_02_dot_28_bar__plus_00_dot_79_bar__minus_00_dot_03 DrawerType)
        (receptacleType StoveBurner_bar__plus_01_dot_69_bar__plus_00_dot_92_bar__minus_00_dot_34 StoveBurnerType)
        (receptacleType StoveBurner_bar__plus_01_dot_41_bar__plus_00_dot_92_bar__plus_00_dot_06 StoveBurnerType)
        (receptacleType Cabinet_bar__plus_01_dot_57_bar__plus_02_dot_01_bar__plus_00_dot_47 CabinetType)
        (receptacleType StoveBurner_bar__plus_01_dot_69_bar__plus_00_dot_92_bar__plus_00_dot_06 StoveBurnerType)
        (receptacleType Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_02_dot_93 CabinetType)
        (objectType StoveKnob_bar__plus_01_dot_88_bar__plus_01_dot_09_bar__plus_00_dot_02 StoveKnobType)
        (objectType Cup_bar__plus_00_dot_77_bar__plus_00_dot_91_bar__minus_01_dot_58 CupType)
        (objectType StoveKnob_bar__plus_01_dot_88_bar__plus_01_dot_09_bar__plus_00_dot_15 StoveKnobType)
        (objectType Tomato_bar__minus_02_dot_21_bar__plus_00_dot_95_bar__plus_01_dot_83 TomatoType)
        (objectType Mug_bar__minus_02_dot_39_bar__plus_01_dot_08_bar__minus_01_dot_07 MugType)
        (objectType Plate_bar__minus_00_dot_60_bar__plus_00_dot_91_bar__plus_01_dot_54 PlateType)
        (objectType Pan_bar__plus_01_dot_69_bar__plus_00_dot_93_bar__plus_00_dot_06 PanType)
        (objectType SaltShaker_bar__minus_02_dot_24_bar__plus_00_dot_11_bar__plus_00_dot_49 SaltShakerType)
        (objectType Knife_bar__minus_02_dot_35_bar__plus_00_dot_80_bar__plus_00_dot_39 KnifeType)
        (objectType Bowl_bar__minus_02_dot_65_bar__plus_01_dot_53_bar__plus_01_dot_41 BowlType)
        (objectType SoapBottle_bar__plus_00_dot_05_bar__plus_00_dot_91_bar__plus_00_dot_42 SoapBottleType)
        (objectType Bowl_bar__minus_02_dot_46_bar__plus_00_dot_91_bar__plus_01_dot_23 BowlType)
        (objectType Fork_bar__minus_02_dot_37_bar__plus_00_dot_91_bar__plus_00_dot_33 ForkType)
        (objectType Apple_bar__minus_02_dot_49_bar__plus_01_dot_15_bar__minus_00_dot_60 AppleType)
        (objectType Spoon_bar__plus_01_dot_45_bar__plus_00_dot_92_bar__minus_01_dot_07 SpoonType)
        (objectType Window_bar__plus_01_dot_97_bar__plus_01_dot_52_bar__plus_01_dot_12 WindowType)
        (objectType Potato_bar__minus_00_dot_08_bar__plus_00_dot_95_bar__plus_01_dot_54 PotatoType)
        (objectType SoapBottle_bar__minus_02_dot_37_bar__plus_00_dot_91_bar__plus_00_dot_63 SoapBottleType)
        (objectType Bread_bar__minus_02_dot_21_bar__plus_01_dot_00_bar__plus_01_dot_53 BreadType)
        (objectType Chair_bar__minus_02_dot_30_bar__plus_00_dot_01_bar__plus_01_dot_84 ChairType)
        (objectType Pot_bar__plus_01_dot_69_bar__plus_00_dot_93_bar__minus_00_dot_34 PotType)
        (objectType Sink_bar__plus_01_dot_38_bar__plus_00_dot_81_bar__minus_01_dot_27 SinkType)
        (objectType Kettle_bar__plus_01_dot_70_bar__plus_01_dot_66_bar__plus_00_dot_31 KettleType)
        (objectType Knife_bar__minus_02_dot_26_bar__plus_00_dot_80_bar__plus_00_dot_39 KnifeType)
        (objectType Lettuce_bar__minus_02_dot_54_bar__plus_00_dot_99_bar__plus_01_dot_53 LettuceType)
        (objectType Fork_bar__plus_01_dot_62_bar__plus_00_dot_91_bar__minus_00_dot_82 ForkType)
        (objectType Window_bar__plus_01_dot_51_bar__plus_01_dot_53_bar__minus_01_dot_50 WindowType)
        (objectType Apple_bar__plus_00_dot_59_bar__plus_00_dot_97_bar__minus_01_dot_37 AppleType)
        (objectType SaltShaker_bar__minus_02_dot_68_bar__plus_01_dot_53_bar__plus_01_dot_84 SaltShakerType)
        (objectType Knife_bar__minus_02_dot_20_bar__plus_00_dot_80_bar__plus_00_dot_39 KnifeType)
        (objectType Spoon_bar__minus_00_dot_60_bar__plus_00_dot_91_bar__plus_01_dot_32 SpoonType)
        (objectType DishSponge_bar__plus_01_dot_69_bar__plus_00_dot_18_bar__plus_00_dot_71 DishSpongeType)
        (objectType Spatula_bar__minus_02_dot_21_bar__plus_00_dot_93_bar__plus_00_dot_33 SpatulaType)
        (objectType PaperTowelRoll_bar__plus_01_dot_77_bar__plus_01_dot_02_bar__plus_00_dot_35 PaperTowelRollType)
        (objectType LightSwitch_bar__minus_00_dot_93_bar__plus_01_dot_35_bar__minus_01_dot_95 LightSwitchType)
        (objectType Potato_bar__minus_02_dot_53_bar__plus_01_dot_13_bar__minus_00_dot_79 PotatoType)
        (objectType StoveKnob_bar__plus_01_dot_88_bar__plus_01_dot_09_bar__minus_00_dot_44 StoveKnobType)
        (objectType PepperShaker_bar__minus_02_dot_56_bar__plus_01_dot_53_bar__plus_02_dot_06 PepperShakerType)
        (objectType DishSponge_bar__minus_00_dot_21_bar__plus_00_dot_91_bar__plus_01_dot_09 DishSpongeType)
        (objectType Fork_bar__minus_02_dot_54_bar__plus_00_dot_91_bar__plus_01_dot_23 ForkType)
        (objectType ButterKnife_bar__plus_01_dot_45_bar__plus_00_dot_92_bar__minus_01_dot_07 ButterKnifeType)
        (objectType Egg_bar__plus_01_dot_39_bar__plus_00_dot_95_bar__minus_01_dot_27 EggType)
        (objectType Apple_bar__minus_02_dot_37_bar__plus_00_dot_97_bar__plus_00_dot_93 AppleType)
        (objectType Spatula_bar__plus_01_dot_42_bar__plus_00_dot_93_bar__minus_01_dot_25 SpatulaType)
        (objectType Bread_bar__minus_00_dot_08_bar__plus_01_dot_00_bar__plus_01_dot_32 BreadType)
        (objectType StoveKnob_bar__plus_01_dot_88_bar__plus_01_dot_09_bar__minus_00_dot_31 StoveKnobType)
        (objectType Potato_bar__minus_02_dot_39_bar__plus_00_dot_25_bar__minus_00_dot_90 PotatoType)
        (canContain SinkBasinType DishSpongeType)
        (canContain SinkBasinType BowlType)
        (canContain SinkBasinType KettleType)
        (canContain SinkBasinType PotType)
        (canContain SinkBasinType MugType)
        (canContain SinkBasinType EggType)
        (canContain SinkBasinType ForkType)
        (canContain SinkBasinType SpoonType)
        (canContain SinkBasinType LettuceType)
        (canContain SinkBasinType PotatoType)
        (canContain SinkBasinType ButterKnifeType)
        (canContain SinkBasinType CupType)
        (canContain SinkBasinType PlateType)
        (canContain SinkBasinType TomatoType)
        (canContain SinkBasinType KnifeType)
        (canContain SinkBasinType AppleType)
        (canContain SinkBasinType PanType)
        (canContain SinkBasinType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CoffeeMachineType MugType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CounterTopType SaltShakerType)
        (canContain CounterTopType BreadType)
        (canContain CounterTopType DishSpongeType)
        (canContain CounterTopType BowlType)
        (canContain CounterTopType KettleType)
        (canContain CounterTopType PotType)
        (canContain CounterTopType MugType)
        (canContain CounterTopType EggType)
        (canContain CounterTopType ForkType)
        (canContain CounterTopType SpoonType)
        (canContain CounterTopType SoapBottleType)
        (canContain CounterTopType LettuceType)
        (canContain CounterTopType PotatoType)
        (canContain CounterTopType ButterKnifeType)
        (canContain CounterTopType CupType)
        (canContain CounterTopType PlateType)
        (canContain CounterTopType PepperShakerType)
        (canContain CounterTopType TomatoType)
        (canContain CounterTopType KnifeType)
        (canContain CounterTopType AppleType)
        (canContain CounterTopType PanType)
        (canContain CounterTopType SpatulaType)
        (canContain MicrowaveType MugType)
        (canContain MicrowaveType PotatoType)
        (canContain MicrowaveType EggType)
        (canContain MicrowaveType AppleType)
        (canContain MicrowaveType TomatoType)
        (canContain MicrowaveType BreadType)
        (canContain MicrowaveType CupType)
        (canContain MicrowaveType PlateType)
        (canContain MicrowaveType BowlType)
        (canContain CounterTopType SaltShakerType)
        (canContain CounterTopType BreadType)
        (canContain CounterTopType DishSpongeType)
        (canContain CounterTopType BowlType)
        (canContain CounterTopType KettleType)
        (canContain CounterTopType PotType)
        (canContain CounterTopType MugType)
        (canContain CounterTopType EggType)
        (canContain CounterTopType ForkType)
        (canContain CounterTopType SpoonType)
        (canContain CounterTopType SoapBottleType)
        (canContain CounterTopType LettuceType)
        (canContain CounterTopType PotatoType)
        (canContain CounterTopType ButterKnifeType)
        (canContain CounterTopType CupType)
        (canContain CounterTopType PlateType)
        (canContain CounterTopType PepperShakerType)
        (canContain CounterTopType TomatoType)
        (canContain CounterTopType KnifeType)
        (canContain CounterTopType AppleType)
        (canContain CounterTopType PanType)
        (canContain CounterTopType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain StoveBurnerType KettleType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain FridgeType BreadType)
        (canContain FridgeType BowlType)
        (canContain FridgeType PotType)
        (canContain FridgeType MugType)
        (canContain FridgeType EggType)
        (canContain FridgeType LettuceType)
        (canContain FridgeType PotatoType)
        (canContain FridgeType CupType)
        (canContain FridgeType PlateType)
        (canContain FridgeType TomatoType)
        (canContain FridgeType AppleType)
        (canContain FridgeType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CounterTopType SaltShakerType)
        (canContain CounterTopType BreadType)
        (canContain CounterTopType DishSpongeType)
        (canContain CounterTopType BowlType)
        (canContain CounterTopType KettleType)
        (canContain CounterTopType PotType)
        (canContain CounterTopType MugType)
        (canContain CounterTopType EggType)
        (canContain CounterTopType ForkType)
        (canContain CounterTopType SpoonType)
        (canContain CounterTopType SoapBottleType)
        (canContain CounterTopType LettuceType)
        (canContain CounterTopType PotatoType)
        (canContain CounterTopType ButterKnifeType)
        (canContain CounterTopType CupType)
        (canContain CounterTopType PlateType)
        (canContain CounterTopType PepperShakerType)
        (canContain CounterTopType TomatoType)
        (canContain CounterTopType KnifeType)
        (canContain CounterTopType AppleType)
        (canContain CounterTopType PanType)
        (canContain CounterTopType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain CounterTopType SaltShakerType)
        (canContain CounterTopType BreadType)
        (canContain CounterTopType DishSpongeType)
        (canContain CounterTopType BowlType)
        (canContain CounterTopType KettleType)
        (canContain CounterTopType PotType)
        (canContain CounterTopType MugType)
        (canContain CounterTopType EggType)
        (canContain CounterTopType ForkType)
        (canContain CounterTopType SpoonType)
        (canContain CounterTopType SoapBottleType)
        (canContain CounterTopType LettuceType)
        (canContain CounterTopType PotatoType)
        (canContain CounterTopType ButterKnifeType)
        (canContain CounterTopType CupType)
        (canContain CounterTopType PlateType)
        (canContain CounterTopType PepperShakerType)
        (canContain CounterTopType TomatoType)
        (canContain CounterTopType KnifeType)
        (canContain CounterTopType AppleType)
        (canContain CounterTopType PanType)
        (canContain CounterTopType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain GarbageCanType BreadType)
        (canContain GarbageCanType DishSpongeType)
        (canContain GarbageCanType EggType)
        (canContain GarbageCanType SoapBottleType)
        (canContain GarbageCanType LettuceType)
        (canContain GarbageCanType PotatoType)
        (canContain GarbageCanType TomatoType)
        (canContain GarbageCanType AppleType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain StoveBurnerType KettleType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain StoveBurnerType KettleType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain StoveBurnerType KettleType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (pickupable Cup_bar__plus_00_dot_77_bar__plus_00_dot_91_bar__minus_01_dot_58)
        (pickupable Tomato_bar__minus_02_dot_21_bar__plus_00_dot_95_bar__plus_01_dot_83)
        (pickupable Mug_bar__minus_02_dot_39_bar__plus_01_dot_08_bar__minus_01_dot_07)
        (pickupable Plate_bar__minus_00_dot_60_bar__plus_00_dot_91_bar__plus_01_dot_54)
        (pickupable Pan_bar__plus_01_dot_69_bar__plus_00_dot_93_bar__plus_00_dot_06)
        (pickupable SaltShaker_bar__minus_02_dot_24_bar__plus_00_dot_11_bar__plus_00_dot_49)
        (pickupable Knife_bar__minus_02_dot_35_bar__plus_00_dot_80_bar__plus_00_dot_39)
        (pickupable Bowl_bar__minus_02_dot_65_bar__plus_01_dot_53_bar__plus_01_dot_41)
        (pickupable SoapBottle_bar__plus_00_dot_05_bar__plus_00_dot_91_bar__plus_00_dot_42)
        (pickupable Bowl_bar__minus_02_dot_46_bar__plus_00_dot_91_bar__plus_01_dot_23)
        (pickupable Fork_bar__minus_02_dot_37_bar__plus_00_dot_91_bar__plus_00_dot_33)
        (pickupable Apple_bar__minus_02_dot_49_bar__plus_01_dot_15_bar__minus_00_dot_60)
        (pickupable Spoon_bar__plus_01_dot_45_bar__plus_00_dot_92_bar__minus_01_dot_07)
        (pickupable Potato_bar__minus_00_dot_08_bar__plus_00_dot_95_bar__plus_01_dot_54)
        (pickupable SoapBottle_bar__minus_02_dot_37_bar__plus_00_dot_91_bar__plus_00_dot_63)
        (pickupable Bread_bar__minus_02_dot_21_bar__plus_01_dot_00_bar__plus_01_dot_53)
        (pickupable Pot_bar__plus_01_dot_69_bar__plus_00_dot_93_bar__minus_00_dot_34)
        (pickupable Kettle_bar__plus_01_dot_70_bar__plus_01_dot_66_bar__plus_00_dot_31)
        (pickupable Knife_bar__minus_02_dot_26_bar__plus_00_dot_80_bar__plus_00_dot_39)
        (pickupable Lettuce_bar__minus_02_dot_54_bar__plus_00_dot_99_bar__plus_01_dot_53)
        (pickupable Fork_bar__plus_01_dot_62_bar__plus_00_dot_91_bar__minus_00_dot_82)
        (pickupable Apple_bar__plus_00_dot_59_bar__plus_00_dot_97_bar__minus_01_dot_37)
        (pickupable SaltShaker_bar__minus_02_dot_68_bar__plus_01_dot_53_bar__plus_01_dot_84)
        (pickupable Knife_bar__minus_02_dot_20_bar__plus_00_dot_80_bar__plus_00_dot_39)
        (pickupable Spoon_bar__minus_00_dot_60_bar__plus_00_dot_91_bar__plus_01_dot_32)
        (pickupable DishSponge_bar__plus_01_dot_69_bar__plus_00_dot_18_bar__plus_00_dot_71)
        (pickupable Spatula_bar__minus_02_dot_21_bar__plus_00_dot_93_bar__plus_00_dot_33)
        (pickupable PaperTowelRoll_bar__plus_01_dot_77_bar__plus_01_dot_02_bar__plus_00_dot_35)
        (pickupable Potato_bar__minus_02_dot_53_bar__plus_01_dot_13_bar__minus_00_dot_79)
        (pickupable PepperShaker_bar__minus_02_dot_56_bar__plus_01_dot_53_bar__plus_02_dot_06)
        (pickupable DishSponge_bar__minus_00_dot_21_bar__plus_00_dot_91_bar__plus_01_dot_09)
        (pickupable Fork_bar__minus_02_dot_54_bar__plus_00_dot_91_bar__plus_01_dot_23)
        (pickupable ButterKnife_bar__plus_01_dot_45_bar__plus_00_dot_92_bar__minus_01_dot_07)
        (pickupable Egg_bar__plus_01_dot_39_bar__plus_00_dot_95_bar__minus_01_dot_27)
        (pickupable Apple_bar__minus_02_dot_37_bar__plus_00_dot_97_bar__plus_00_dot_93)
        (pickupable Spatula_bar__plus_01_dot_42_bar__plus_00_dot_93_bar__minus_01_dot_25)
        (pickupable Bread_bar__minus_00_dot_08_bar__plus_01_dot_00_bar__plus_01_dot_32)
        (pickupable Potato_bar__minus_02_dot_39_bar__plus_00_dot_25_bar__minus_00_dot_90)
        (isReceptacleObject Cup_bar__plus_00_dot_77_bar__plus_00_dot_91_bar__minus_01_dot_58)
        (isReceptacleObject Mug_bar__minus_02_dot_39_bar__plus_01_dot_08_bar__minus_01_dot_07)
        (isReceptacleObject Plate_bar__minus_00_dot_60_bar__plus_00_dot_91_bar__plus_01_dot_54)
        (isReceptacleObject Pan_bar__plus_01_dot_69_bar__plus_00_dot_93_bar__plus_00_dot_06)
        (isReceptacleObject Bowl_bar__minus_02_dot_65_bar__plus_01_dot_53_bar__plus_01_dot_41)
        (isReceptacleObject Bowl_bar__minus_02_dot_46_bar__plus_00_dot_91_bar__plus_01_dot_23)
        (isReceptacleObject Pot_bar__plus_01_dot_69_bar__plus_00_dot_93_bar__minus_00_dot_34)
        (openable Cabinet_bar__minus_02_dot_15_bar__plus_00_dot_40_bar__plus_00_dot_70)
        (openable Cabinet_bar__minus_02_dot_15_bar__plus_00_dot_40_bar__plus_00_dot_64)
        (openable Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_01_dot_69)
        (openable Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_01_dot_64)
        (openable Microwave_bar__minus_02_dot_58_bar__plus_00_dot_90_bar__plus_02_dot_44)
        (openable Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_00_dot_41)
        (openable Fridge_bar__minus_02_dot_48_bar__plus_00_dot_00_bar__minus_00_dot_78)
        (openable Cabinet_bar__plus_01_dot_57_bar__plus_02_dot_01_bar__minus_00_dot_78)
        (openable Cabinet_bar__plus_00_dot_15_bar__plus_02_dot_01_bar__minus_01_dot_60)
        (openable Drawer_bar__minus_02_dot_28_bar__plus_00_dot_79_bar__plus_00_dot_44)
        (openable Cabinet_bar__minus_02_dot_15_bar__plus_00_dot_40_bar__minus_00_dot_24)
        (openable Drawer_bar__minus_02_dot_28_bar__plus_00_dot_79_bar__plus_01_dot_37)
        (openable Cabinet_bar__minus_02_dot_29_bar__plus_01_dot_97_bar__minus_01_dot_33)
        (openable Cabinet_bar__minus_02_dot_15_bar__plus_00_dot_40_bar__plus_01_dot_58)
        (openable Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_00_dot_36)
        (openable Cabinet_bar__minus_02_dot_45_bar__plus_02_dot_15_bar__minus_00_dot_29)
        (openable Cabinet_bar__minus_02_dot_45_bar__plus_02_dot_15_bar__minus_01_dot_28)
        (openable Drawer_bar__minus_02_dot_28_bar__plus_00_dot_79_bar__plus_00_dot_90)
        (openable Drawer_bar__minus_02_dot_28_bar__plus_00_dot_79_bar__minus_00_dot_03)
        (openable Cabinet_bar__plus_01_dot_57_bar__plus_02_dot_01_bar__plus_00_dot_47)
        (openable Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_02_dot_93)
        
        (atLocation agent1 loc_bar__minus_4_bar__minus_6_bar_3_bar_30)
        
        (cleanable Cup_bar__plus_00_dot_77_bar__plus_00_dot_91_bar__minus_01_dot_58)
        (cleanable Tomato_bar__minus_02_dot_21_bar__plus_00_dot_95_bar__plus_01_dot_83)
        (cleanable Mug_bar__minus_02_dot_39_bar__plus_01_dot_08_bar__minus_01_dot_07)
        (cleanable Plate_bar__minus_00_dot_60_bar__plus_00_dot_91_bar__plus_01_dot_54)
        (cleanable Pan_bar__plus_01_dot_69_bar__plus_00_dot_93_bar__plus_00_dot_06)
        (cleanable Knife_bar__minus_02_dot_35_bar__plus_00_dot_80_bar__plus_00_dot_39)
        (cleanable Bowl_bar__minus_02_dot_65_bar__plus_01_dot_53_bar__plus_01_dot_41)
        (cleanable Bowl_bar__minus_02_dot_46_bar__plus_00_dot_91_bar__plus_01_dot_23)
        (cleanable Fork_bar__minus_02_dot_37_bar__plus_00_dot_91_bar__plus_00_dot_33)
        (cleanable Apple_bar__minus_02_dot_49_bar__plus_01_dot_15_bar__minus_00_dot_60)
        (cleanable Spoon_bar__plus_01_dot_45_bar__plus_00_dot_92_bar__minus_01_dot_07)
        (cleanable Potato_bar__minus_00_dot_08_bar__plus_00_dot_95_bar__plus_01_dot_54)
        (cleanable Pot_bar__plus_01_dot_69_bar__plus_00_dot_93_bar__minus_00_dot_34)
        (cleanable Kettle_bar__plus_01_dot_70_bar__plus_01_dot_66_bar__plus_00_dot_31)
        (cleanable Knife_bar__minus_02_dot_26_bar__plus_00_dot_80_bar__plus_00_dot_39)
        (cleanable Lettuce_bar__minus_02_dot_54_bar__plus_00_dot_99_bar__plus_01_dot_53)
        (cleanable Fork_bar__plus_01_dot_62_bar__plus_00_dot_91_bar__minus_00_dot_82)
        (cleanable Apple_bar__plus_00_dot_59_bar__plus_00_dot_97_bar__minus_01_dot_37)
        (cleanable Knife_bar__minus_02_dot_20_bar__plus_00_dot_80_bar__plus_00_dot_39)
        (cleanable Spoon_bar__minus_00_dot_60_bar__plus_00_dot_91_bar__plus_01_dot_32)
        (cleanable DishSponge_bar__plus_01_dot_69_bar__plus_00_dot_18_bar__plus_00_dot_71)
        (cleanable Spatula_bar__minus_02_dot_21_bar__plus_00_dot_93_bar__plus_00_dot_33)
        (cleanable Potato_bar__minus_02_dot_53_bar__plus_01_dot_13_bar__minus_00_dot_79)
        (cleanable DishSponge_bar__minus_00_dot_21_bar__plus_00_dot_91_bar__plus_01_dot_09)
        (cleanable Fork_bar__minus_02_dot_54_bar__plus_00_dot_91_bar__plus_01_dot_23)
        (cleanable ButterKnife_bar__plus_01_dot_45_bar__plus_00_dot_92_bar__minus_01_dot_07)
        (cleanable Egg_bar__plus_01_dot_39_bar__plus_00_dot_95_bar__minus_01_dot_27)
        (cleanable Apple_bar__minus_02_dot_37_bar__plus_00_dot_97_bar__plus_00_dot_93)
        (cleanable Spatula_bar__plus_01_dot_42_bar__plus_00_dot_93_bar__minus_01_dot_25)
        (cleanable Potato_bar__minus_02_dot_39_bar__plus_00_dot_25_bar__minus_00_dot_90)
        
        (heatable Cup_bar__plus_00_dot_77_bar__plus_00_dot_91_bar__minus_01_dot_58)
        (heatable Tomato_bar__minus_02_dot_21_bar__plus_00_dot_95_bar__plus_01_dot_83)
        (heatable Mug_bar__minus_02_dot_39_bar__plus_01_dot_08_bar__minus_01_dot_07)
        (heatable Plate_bar__minus_00_dot_60_bar__plus_00_dot_91_bar__plus_01_dot_54)
        (heatable Apple_bar__minus_02_dot_49_bar__plus_01_dot_15_bar__minus_00_dot_60)
        (heatable Potato_bar__minus_00_dot_08_bar__plus_00_dot_95_bar__plus_01_dot_54)
        (heatable Bread_bar__minus_02_dot_21_bar__plus_01_dot_00_bar__plus_01_dot_53)
        (heatable Apple_bar__plus_00_dot_59_bar__plus_00_dot_97_bar__minus_01_dot_37)
        (heatable Potato_bar__minus_02_dot_53_bar__plus_01_dot_13_bar__minus_00_dot_79)
        (heatable Egg_bar__plus_01_dot_39_bar__plus_00_dot_95_bar__minus_01_dot_27)
        (heatable Apple_bar__minus_02_dot_37_bar__plus_00_dot_97_bar__plus_00_dot_93)
        (heatable Bread_bar__minus_00_dot_08_bar__plus_01_dot_00_bar__plus_01_dot_32)
        (heatable Potato_bar__minus_02_dot_39_bar__plus_00_dot_25_bar__minus_00_dot_90)
        (coolable Cup_bar__plus_00_dot_77_bar__plus_00_dot_91_bar__minus_01_dot_58)
        (coolable Tomato_bar__minus_02_dot_21_bar__plus_00_dot_95_bar__plus_01_dot_83)
        (coolable Mug_bar__minus_02_dot_39_bar__plus_01_dot_08_bar__minus_01_dot_07)
        (coolable Plate_bar__minus_00_dot_60_bar__plus_00_dot_91_bar__plus_01_dot_54)
        (coolable Pan_bar__plus_01_dot_69_bar__plus_00_dot_93_bar__plus_00_dot_06)
        (coolable Bowl_bar__minus_02_dot_65_bar__plus_01_dot_53_bar__plus_01_dot_41)
        (coolable Bowl_bar__minus_02_dot_46_bar__plus_00_dot_91_bar__plus_01_dot_23)
        (coolable Apple_bar__minus_02_dot_49_bar__plus_01_dot_15_bar__minus_00_dot_60)
        (coolable Potato_bar__minus_00_dot_08_bar__plus_00_dot_95_bar__plus_01_dot_54)
        (coolable Bread_bar__minus_02_dot_21_bar__plus_01_dot_00_bar__plus_01_dot_53)
        (coolable Pot_bar__plus_01_dot_69_bar__plus_00_dot_93_bar__minus_00_dot_34)
        (coolable Lettuce_bar__minus_02_dot_54_bar__plus_00_dot_99_bar__plus_01_dot_53)
        (coolable Apple_bar__plus_00_dot_59_bar__plus_00_dot_97_bar__minus_01_dot_37)
        (coolable Potato_bar__minus_02_dot_53_bar__plus_01_dot_13_bar__minus_00_dot_79)
        (coolable Egg_bar__plus_01_dot_39_bar__plus_00_dot_95_bar__minus_01_dot_27)
        (coolable Apple_bar__minus_02_dot_37_bar__plus_00_dot_97_bar__plus_00_dot_93)
        (coolable Bread_bar__minus_00_dot_08_bar__plus_01_dot_00_bar__plus_01_dot_32)
        (coolable Potato_bar__minus_02_dot_39_bar__plus_00_dot_25_bar__minus_00_dot_90)
        
        
        
        
        
        (sliceable Tomato_bar__minus_02_dot_21_bar__plus_00_dot_95_bar__plus_01_dot_83)
        (sliceable Apple_bar__minus_02_dot_49_bar__plus_01_dot_15_bar__minus_00_dot_60)
        (sliceable Potato_bar__minus_00_dot_08_bar__plus_00_dot_95_bar__plus_01_dot_54)
        (sliceable Bread_bar__minus_02_dot_21_bar__plus_01_dot_00_bar__plus_01_dot_53)
        (sliceable Lettuce_bar__minus_02_dot_54_bar__plus_00_dot_99_bar__plus_01_dot_53)
        (sliceable Apple_bar__plus_00_dot_59_bar__plus_00_dot_97_bar__minus_01_dot_37)
        (sliceable Potato_bar__minus_02_dot_53_bar__plus_01_dot_13_bar__minus_00_dot_79)
        (sliceable Egg_bar__plus_01_dot_39_bar__plus_00_dot_95_bar__minus_01_dot_27)
        (sliceable Apple_bar__minus_02_dot_37_bar__plus_00_dot_97_bar__plus_00_dot_93)
        (sliceable Bread_bar__minus_00_dot_08_bar__plus_01_dot_00_bar__plus_01_dot_32)
        (sliceable Potato_bar__minus_02_dot_39_bar__plus_00_dot_25_bar__minus_00_dot_90)
        
        (inReceptacle SoapBottle_bar__plus_00_dot_05_bar__plus_00_dot_91_bar__plus_00_dot_42 CounterTop_bar__minus_00_dot_36_bar__plus_00_dot_95_bar__plus_01_dot_09)
        (inReceptacle DishSponge_bar__minus_00_dot_21_bar__plus_00_dot_91_bar__plus_01_dot_09 CounterTop_bar__minus_00_dot_36_bar__plus_00_dot_95_bar__plus_01_dot_09)
        (inReceptacle Spoon_bar__minus_00_dot_60_bar__plus_00_dot_91_bar__plus_01_dot_32 CounterTop_bar__minus_00_dot_36_bar__plus_00_dot_95_bar__plus_01_dot_09)
        (inReceptacle Potato_bar__minus_00_dot_08_bar__plus_00_dot_95_bar__plus_01_dot_54 CounterTop_bar__minus_00_dot_36_bar__plus_00_dot_95_bar__plus_01_dot_09)
        (inReceptacle Plate_bar__minus_00_dot_60_bar__plus_00_dot_91_bar__plus_01_dot_54 CounterTop_bar__minus_00_dot_36_bar__plus_00_dot_95_bar__plus_01_dot_09)
        (inReceptacle Bread_bar__minus_00_dot_08_bar__plus_01_dot_00_bar__plus_01_dot_32 CounterTop_bar__minus_00_dot_36_bar__plus_00_dot_95_bar__plus_01_dot_09)
        (inReceptacle Pot_bar__plus_01_dot_69_bar__plus_00_dot_93_bar__minus_00_dot_34 StoveBurner_bar__plus_01_dot_69_bar__plus_00_dot_92_bar__minus_00_dot_34)
        (inReceptacle PaperTowelRoll_bar__plus_01_dot_77_bar__plus_01_dot_02_bar__plus_00_dot_35 CounterTop_bar__plus_01_dot_59_bar__plus_00_dot_95_bar__plus_00_dot_41)
        (inReceptacle Fork_bar__plus_01_dot_62_bar__plus_00_dot_91_bar__minus_00_dot_82 CounterTop_bar__plus_00_dot_47_bar__plus_00_dot_95_bar__minus_01_dot_63)
        (inReceptacle Apple_bar__plus_00_dot_59_bar__plus_00_dot_97_bar__minus_01_dot_37 CounterTop_bar__plus_00_dot_47_bar__plus_00_dot_95_bar__minus_01_dot_63)
        (inReceptacle Cup_bar__plus_00_dot_77_bar__plus_00_dot_91_bar__minus_01_dot_58 CounterTop_bar__plus_00_dot_47_bar__plus_00_dot_95_bar__minus_01_dot_63)
        (inReceptacle Pan_bar__plus_01_dot_69_bar__plus_00_dot_93_bar__plus_00_dot_06 StoveBurner_bar__plus_01_dot_69_bar__plus_00_dot_92_bar__plus_00_dot_06)
        (inReceptacle SaltShaker_bar__minus_02_dot_68_bar__plus_01_dot_53_bar__plus_01_dot_84 Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_01_dot_69)
        (inReceptacle PepperShaker_bar__minus_02_dot_56_bar__plus_01_dot_53_bar__plus_02_dot_06 Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_01_dot_69)
        (inReceptacle Bowl_bar__minus_02_dot_65_bar__plus_01_dot_53_bar__plus_01_dot_41 Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_01_dot_64)
        (inReceptacle Knife_bar__minus_02_dot_35_bar__plus_00_dot_80_bar__plus_00_dot_39 Drawer_bar__minus_02_dot_28_bar__plus_00_dot_79_bar__plus_00_dot_44)
        (inReceptacle Knife_bar__minus_02_dot_20_bar__plus_00_dot_80_bar__plus_00_dot_39 Drawer_bar__minus_02_dot_28_bar__plus_00_dot_79_bar__plus_00_dot_44)
        (inReceptacle Knife_bar__minus_02_dot_26_bar__plus_00_dot_80_bar__plus_00_dot_39 Drawer_bar__minus_02_dot_28_bar__plus_00_dot_79_bar__plus_00_dot_44)
        (inReceptacle Kettle_bar__plus_01_dot_70_bar__plus_01_dot_66_bar__plus_00_dot_31 Cabinet_bar__plus_01_dot_57_bar__plus_02_dot_01_bar__plus_00_dot_47)
        (inReceptacle SaltShaker_bar__minus_02_dot_24_bar__plus_00_dot_11_bar__plus_00_dot_49 Cabinet_bar__minus_02_dot_15_bar__plus_00_dot_40_bar__plus_00_dot_64)
        (inReceptacle Fork_bar__minus_02_dot_54_bar__plus_00_dot_91_bar__plus_01_dot_23 CounterTop_bar__minus_01_dot_49_bar__plus_00_dot_95_bar__plus_01_dot_32)
        (inReceptacle Bowl_bar__minus_02_dot_46_bar__plus_00_dot_91_bar__plus_01_dot_23 CounterTop_bar__minus_01_dot_49_bar__plus_00_dot_95_bar__plus_01_dot_32)
        (inReceptacle Fork_bar__minus_02_dot_37_bar__plus_00_dot_91_bar__plus_00_dot_33 CounterTop_bar__minus_01_dot_49_bar__plus_00_dot_95_bar__plus_01_dot_32)
        (inReceptacle Tomato_bar__minus_02_dot_21_bar__plus_00_dot_95_bar__plus_01_dot_83 CounterTop_bar__minus_01_dot_49_bar__plus_00_dot_95_bar__plus_01_dot_32)
        (inReceptacle Apple_bar__minus_02_dot_37_bar__plus_00_dot_97_bar__plus_00_dot_93 CounterTop_bar__minus_01_dot_49_bar__plus_00_dot_95_bar__plus_01_dot_32)
        (inReceptacle Spatula_bar__minus_02_dot_21_bar__plus_00_dot_93_bar__plus_00_dot_33 CounterTop_bar__minus_01_dot_49_bar__plus_00_dot_95_bar__plus_01_dot_32)
        (inReceptacle SoapBottle_bar__minus_02_dot_37_bar__plus_00_dot_91_bar__plus_00_dot_63 CounterTop_bar__minus_01_dot_49_bar__plus_00_dot_95_bar__plus_01_dot_32)
        (inReceptacle Bread_bar__minus_02_dot_21_bar__plus_01_dot_00_bar__plus_01_dot_53 CounterTop_bar__minus_01_dot_49_bar__plus_00_dot_95_bar__plus_01_dot_32)
        (inReceptacle Lettuce_bar__minus_02_dot_54_bar__plus_00_dot_99_bar__plus_01_dot_53 CounterTop_bar__minus_01_dot_49_bar__plus_00_dot_95_bar__plus_01_dot_32)
        (inReceptacle Potato_bar__minus_02_dot_53_bar__plus_01_dot_13_bar__minus_00_dot_79 Fridge_bar__minus_02_dot_48_bar__plus_00_dot_00_bar__minus_00_dot_78)
        (inReceptacle Apple_bar__minus_02_dot_49_bar__plus_01_dot_15_bar__minus_00_dot_60 Fridge_bar__minus_02_dot_48_bar__plus_00_dot_00_bar__minus_00_dot_78)
        (inReceptacle Mug_bar__minus_02_dot_39_bar__plus_01_dot_08_bar__minus_01_dot_07 Fridge_bar__minus_02_dot_48_bar__plus_00_dot_00_bar__minus_00_dot_78)
        (inReceptacle Potato_bar__minus_02_dot_39_bar__plus_00_dot_25_bar__minus_00_dot_90 Fridge_bar__minus_02_dot_48_bar__plus_00_dot_00_bar__minus_00_dot_78)
        (inReceptacle DishSponge_bar__plus_01_dot_69_bar__plus_00_dot_18_bar__plus_00_dot_71 GarbageCan_bar__plus_01_dot_65_bar__plus_00_dot_00_bar__plus_00_dot_68)
        
        
        (receptacleAtLocation Cabinet_bar__plus_00_dot_15_bar__plus_02_dot_01_bar__minus_01_dot_60 loc_bar_2_bar__minus_4_bar_2_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_57_bar__plus_02_dot_01_bar__plus_00_dot_47 loc_bar_3_bar_1_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_57_bar__plus_02_dot_01_bar__minus_00_dot_78 loc_bar_3_bar__minus_2_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_02_dot_15_bar__plus_00_dot_40_bar__plus_00_dot_64 loc_bar__minus_5_bar_0_bar_3_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_02_dot_15_bar__plus_00_dot_40_bar__plus_00_dot_70 loc_bar__minus_5_bar_2_bar_3_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_02_dot_15_bar__plus_00_dot_40_bar__plus_01_dot_58 loc_bar__minus_5_bar_7_bar_3_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_02_dot_15_bar__plus_00_dot_40_bar__minus_00_dot_24 loc_bar__minus_5_bar__minus_2_bar_3_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_02_dot_29_bar__plus_01_dot_97_bar__minus_01_dot_33 loc_bar__minus_5_bar__minus_5_bar_3_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_00_dot_36 loc_bar__minus_6_bar_1_bar_3_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_00_dot_41 loc_bar__minus_7_bar_3_bar_3_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_01_dot_64 loc_bar__minus_6_bar_5_bar_3_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_01_dot_69 loc_bar__minus_7_bar_8_bar_3_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_02_dot_45_bar__plus_01_dot_95_bar__plus_02_dot_93 loc_bar__minus_7_bar_10_bar_3_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_02_dot_45_bar__plus_02_dot_15_bar__minus_00_dot_29 loc_bar__minus_5_bar__minus_3_bar_3_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_02_dot_45_bar__plus_02_dot_15_bar__minus_01_dot_28 loc_bar__minus_5_bar__minus_4_bar_3_bar__minus_30)
        (receptacleAtLocation CoffeeMachine_bar__plus_00_dot_46_bar__plus_00_dot_90_bar__minus_01_dot_82 loc_bar_2_bar__minus_4_bar_2_bar_45)
        (receptacleAtLocation CounterTop_bar__plus_00_dot_47_bar__plus_00_dot_95_bar__minus_01_dot_63 loc_bar_2_bar__minus_4_bar_2_bar_45)
        (receptacleAtLocation CounterTop_bar__plus_01_dot_59_bar__plus_00_dot_95_bar__plus_00_dot_41 loc_bar_4_bar_3_bar_1_bar_45)
        (receptacleAtLocation CounterTop_bar__minus_00_dot_36_bar__plus_00_dot_95_bar__plus_01_dot_09 loc_bar__minus_1_bar_9_bar_2_bar_30)
        (receptacleAtLocation CounterTop_bar__minus_01_dot_49_bar__plus_00_dot_95_bar__plus_01_dot_32 loc_bar__minus_7_bar_2_bar_0_bar_45)
        (receptacleAtLocation Drawer_bar__minus_02_dot_28_bar__plus_00_dot_79_bar__plus_00_dot_44 loc_bar__minus_6_bar_3_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_02_dot_28_bar__plus_00_dot_79_bar__plus_00_dot_90 loc_bar__minus_6_bar_5_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_02_dot_28_bar__plus_00_dot_79_bar__plus_01_dot_37 loc_bar__minus_6_bar_7_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_02_dot_28_bar__plus_00_dot_79_bar__minus_00_dot_03 loc_bar__minus_6_bar_1_bar_3_bar_45)
        (receptacleAtLocation Fridge_bar__minus_02_dot_48_bar__plus_00_dot_00_bar__minus_00_dot_78 loc_bar__minus_6_bar__minus_3_bar_3_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_01_dot_65_bar__plus_00_dot_00_bar__plus_00_dot_68 loc_bar_3_bar_2_bar_1_bar_60)
        (receptacleAtLocation Microwave_bar__minus_02_dot_58_bar__plus_00_dot_90_bar__plus_02_dot_44 loc_bar__minus_7_bar_10_bar_3_bar_45)
        (receptacleAtLocation Sink_bar__plus_01_dot_38_bar__plus_00_dot_81_bar__minus_01_dot_27_bar_SinkBasin loc_bar_4_bar__minus_3_bar_2_bar_60)
        (receptacleAtLocation StoveBurner_bar__plus_01_dot_41_bar__plus_00_dot_92_bar__plus_00_dot_06 loc_bar_3_bar_0_bar_1_bar_45)
        (receptacleAtLocation StoveBurner_bar__plus_01_dot_41_bar__plus_00_dot_92_bar__minus_00_dot_34 loc_bar_3_bar__minus_1_bar_1_bar_45)
        (receptacleAtLocation StoveBurner_bar__plus_01_dot_69_bar__plus_00_dot_92_bar__plus_00_dot_06 loc_bar_3_bar_0_bar_1_bar_45)
        (receptacleAtLocation StoveBurner_bar__plus_01_dot_69_bar__plus_00_dot_92_bar__minus_00_dot_34 loc_bar_3_bar__minus_1_bar_1_bar_45)
        (receptacleAtLocation Toaster_bar__minus_02_dot_57_bar__plus_00_dot_90_bar__plus_01_dot_88 loc_bar__minus_7_bar_8_bar_3_bar_45)
        (objectAtLocation Spoon_bar__minus_00_dot_60_bar__plus_00_dot_91_bar__plus_01_dot_32 loc_bar__minus_1_bar_9_bar_2_bar_30)
        (objectAtLocation DishSponge_bar__plus_01_dot_69_bar__plus_00_dot_18_bar__plus_00_dot_71 loc_bar_3_bar_2_bar_1_bar_60)
        (objectAtLocation Spatula_bar__plus_01_dot_42_bar__plus_00_dot_93_bar__minus_01_dot_25 loc_bar_4_bar__minus_3_bar_2_bar_60)
        (objectAtLocation SoapBottle_bar__minus_02_dot_37_bar__plus_00_dot_91_bar__plus_00_dot_63 loc_bar__minus_7_bar_2_bar_0_bar_45)
        (objectAtLocation SaltShaker_bar__minus_02_dot_68_bar__plus_01_dot_53_bar__plus_01_dot_84 loc_bar__minus_7_bar_8_bar_3_bar__minus_30)
        (objectAtLocation Potato_bar__minus_02_dot_39_bar__plus_00_dot_25_bar__minus_00_dot_90 loc_bar__minus_6_bar__minus_3_bar_3_bar_60)
        (objectAtLocation Bread_bar__minus_00_dot_08_bar__plus_01_dot_00_bar__plus_01_dot_32 loc_bar__minus_1_bar_9_bar_2_bar_30)
        (objectAtLocation Fork_bar__minus_02_dot_37_bar__plus_00_dot_91_bar__plus_00_dot_33 loc_bar__minus_7_bar_2_bar_0_bar_45)
        (objectAtLocation Bowl_bar__minus_02_dot_46_bar__plus_00_dot_91_bar__plus_01_dot_23 loc_bar__minus_7_bar_2_bar_0_bar_45)
        (objectAtLocation Apple_bar__plus_00_dot_59_bar__plus_00_dot_97_bar__minus_01_dot_37 loc_bar_2_bar__minus_4_bar_2_bar_45)
        (objectAtLocation Knife_bar__minus_02_dot_20_bar__plus_00_dot_80_bar__plus_00_dot_39 loc_bar__minus_6_bar_3_bar_3_bar_45)
        (objectAtLocation Apple_bar__minus_02_dot_49_bar__plus_01_dot_15_bar__minus_00_dot_60 loc_bar__minus_6_bar__minus_3_bar_3_bar_60)
        (objectAtLocation Knife_bar__minus_02_dot_35_bar__plus_00_dot_80_bar__plus_00_dot_39 loc_bar__minus_6_bar_3_bar_3_bar_45)
        (objectAtLocation Fork_bar__plus_01_dot_62_bar__plus_00_dot_91_bar__minus_00_dot_82 loc_bar_2_bar__minus_4_bar_2_bar_45)
        (objectAtLocation Potato_bar__minus_02_dot_53_bar__plus_01_dot_13_bar__minus_00_dot_79 loc_bar__minus_6_bar__minus_3_bar_3_bar_60)
        (objectAtLocation StoveKnob_bar__plus_01_dot_88_bar__plus_01_dot_09_bar__plus_00_dot_02 loc_bar_3_bar_0_bar_1_bar_30)
        (objectAtLocation StoveKnob_bar__plus_01_dot_88_bar__plus_01_dot_09_bar__minus_00_dot_31 loc_bar_3_bar__minus_1_bar_1_bar_30)
        (objectAtLocation Chair_bar__minus_02_dot_30_bar__plus_00_dot_01_bar__plus_01_dot_84 loc_bar__minus_7_bar_7_bar_3_bar_60)
        (objectAtLocation Sink_bar__plus_01_dot_38_bar__plus_00_dot_81_bar__minus_01_dot_27 loc_bar_3_bar__minus_4_bar_1_bar_60)
        (objectAtLocation StoveKnob_bar__plus_01_dot_88_bar__plus_01_dot_09_bar__plus_00_dot_15 loc_bar_3_bar_1_bar_1_bar_30)
        (objectAtLocation StoveKnob_bar__plus_01_dot_88_bar__plus_01_dot_09_bar__minus_00_dot_44 loc_bar_4_bar__minus_3_bar_1_bar_30)
        (objectAtLocation Pan_bar__plus_01_dot_69_bar__plus_00_dot_93_bar__plus_00_dot_06 loc_bar_3_bar_0_bar_1_bar_45)
        (objectAtLocation Knife_bar__minus_02_dot_26_bar__plus_00_dot_80_bar__plus_00_dot_39 loc_bar__minus_6_bar_3_bar_3_bar_45)
        (objectAtLocation Apple_bar__minus_02_dot_37_bar__plus_00_dot_97_bar__plus_00_dot_93 loc_bar__minus_7_bar_2_bar_0_bar_45)
        (objectAtLocation Bowl_bar__minus_02_dot_65_bar__plus_01_dot_53_bar__plus_01_dot_41 loc_bar__minus_6_bar_5_bar_3_bar__minus_15)
        (objectAtLocation LightSwitch_bar__minus_00_dot_93_bar__plus_01_dot_35_bar__minus_01_dot_95 loc_bar__minus_4_bar__minus_6_bar_2_bar_30)
        (objectAtLocation Fork_bar__minus_02_dot_54_bar__plus_00_dot_91_bar__plus_01_dot_23 loc_bar__minus_7_bar_2_bar_0_bar_45)
        (objectAtLocation Bread_bar__minus_02_dot_21_bar__plus_01_dot_00_bar__plus_01_dot_53 loc_bar__minus_7_bar_2_bar_0_bar_45)
        (objectAtLocation Plate_bar__minus_00_dot_60_bar__plus_00_dot_91_bar__plus_01_dot_54 loc_bar__minus_1_bar_9_bar_2_bar_30)
        (objectAtLocation Egg_bar__plus_01_dot_39_bar__plus_00_dot_95_bar__minus_01_dot_27 loc_bar_4_bar__minus_3_bar_2_bar_60)
        (objectAtLocation Kettle_bar__plus_01_dot_70_bar__plus_01_dot_66_bar__plus_00_dot_31 loc_bar_3_bar_1_bar_1_bar__minus_30)
        (objectAtLocation Potato_bar__minus_00_dot_08_bar__plus_00_dot_95_bar__plus_01_dot_54 loc_bar__minus_1_bar_9_bar_2_bar_30)
        (objectAtLocation SaltShaker_bar__minus_02_dot_24_bar__plus_00_dot_11_bar__plus_00_dot_49 loc_bar__minus_5_bar_0_bar_3_bar_60)
        (objectAtLocation Lettuce_bar__minus_02_dot_54_bar__plus_00_dot_99_bar__plus_01_dot_53 loc_bar__minus_7_bar_2_bar_0_bar_45)
        (objectAtLocation Cup_bar__plus_00_dot_77_bar__plus_00_dot_91_bar__minus_01_dot_58 loc_bar_2_bar__minus_4_bar_2_bar_45)
        (objectAtLocation SoapBottle_bar__plus_00_dot_05_bar__plus_00_dot_91_bar__plus_00_dot_42 loc_bar__minus_1_bar_9_bar_2_bar_30)
        (objectAtLocation Window_bar__plus_01_dot_51_bar__plus_01_dot_53_bar__minus_01_dot_50 loc_bar_3_bar__minus_4_bar_1_bar_15)
        (objectAtLocation Window_bar__plus_01_dot_97_bar__plus_01_dot_52_bar__plus_01_dot_12 loc_bar_5_bar_4_bar_1_bar_15)
        (objectAtLocation Spatula_bar__minus_02_dot_21_bar__plus_00_dot_93_bar__plus_00_dot_33 loc_bar__minus_7_bar_2_bar_0_bar_45)
        (objectAtLocation PaperTowelRoll_bar__plus_01_dot_77_bar__plus_01_dot_02_bar__plus_00_dot_35 loc_bar_4_bar_3_bar_1_bar_45)
        (objectAtLocation ButterKnife_bar__plus_01_dot_45_bar__plus_00_dot_92_bar__minus_01_dot_07 loc_bar_4_bar__minus_3_bar_2_bar_60)
        (objectAtLocation PepperShaker_bar__minus_02_dot_56_bar__plus_01_dot_53_bar__plus_02_dot_06 loc_bar__minus_7_bar_8_bar_3_bar__minus_30)
        (objectAtLocation DishSponge_bar__minus_00_dot_21_bar__plus_00_dot_91_bar__plus_01_dot_09 loc_bar__minus_1_bar_9_bar_2_bar_30)
        (objectAtLocation Pot_bar__plus_01_dot_69_bar__plus_00_dot_93_bar__minus_00_dot_34 loc_bar_3_bar__minus_1_bar_1_bar_45)
        (objectAtLocation Spoon_bar__plus_01_dot_45_bar__plus_00_dot_92_bar__minus_01_dot_07 loc_bar_4_bar__minus_3_bar_2_bar_60)
        (objectAtLocation Tomato_bar__minus_02_dot_21_bar__plus_00_dot_95_bar__plus_01_dot_83 loc_bar__minus_7_bar_2_bar_0_bar_45)
        (objectAtLocation Mug_bar__minus_02_dot_39_bar__plus_01_dot_08_bar__minus_01_dot_07 loc_bar__minus_6_bar__minus_3_bar_3_bar_60)
        )
    

        (:goal
            (and
                (exists (?r - receptacle)
                    (exists (?o - object)
                        (and
                            (heatable ?o)
                            (objectType ?o EggType)
                            (receptacleType ?r FridgeType)
                            (isHot ?o)
                            (inReceptacle ?o ?r)
                        )
                    )
                )
            )
        )
    )
    