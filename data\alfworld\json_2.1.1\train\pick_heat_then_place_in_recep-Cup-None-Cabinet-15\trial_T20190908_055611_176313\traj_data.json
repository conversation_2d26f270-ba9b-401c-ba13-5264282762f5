{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000270.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000271.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000272.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000273.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000274.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000275.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000276.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000277.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000278.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000279.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000280.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000281.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000282.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000283.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000284.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000285.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000286.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000287.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000288.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000289.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000290.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000291.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000292.png", "low_idx": 36}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-8|13|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-7.67649888, -7.67649888, 16.75564576, 16.75564576, 3.6611472, 3.6611472]], "coordinateReceptacleObjectId": ["CounterTop", [-13.5272, -13.5272, 13.344, 13.344, 3.804, 3.804]], "forceVisible": true, "objectId": "Cup|-01.92|+00.92|+04.19"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-10|9|3|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-13.94, -13.94, 8.732, 8.732, 5.723384, 5.723384]], "forceVisible": true, "objectId": "Microwave|-03.49|+01.43|+02.18"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-10|9|3|-30"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-7.67649888, -7.67649888, 16.75564576, 16.75564576, 3.6611472, 3.6611472]], "coordinateReceptacleObjectId": ["Cabinet", [-13.408548, -13.408548, 6.86034964, 6.86034964, 8.822844, 8.822844]], "forceVisible": true, "objectId": "Cup|-01.92|+00.92|+04.19", "receptacleObjectId": "Cabinet|-03.35|+02.21|+01.72"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.92|+00.92|+04.19"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [156, 65, 184, 114], "mask": [[19367, 5], [19662, 16], [19960, 20], [20259, 23], [20557, 26], [20857, 27], [21156, 29], [21456, 29], [21756, 29], [22056, 29], [22356, 29], [22656, 28], [22956, 28], [23256, 28], [23556, 28], [23856, 28], [24156, 28], [24457, 27], [24757, 26], [25057, 26], [25358, 24], [25659, 23], [25959, 22], [26259, 22], [26559, 22], [26859, 22], [27159, 22], [27459, 22], [27759, 22], [28059, 21], [28359, 21], [28659, 21], [28959, 21], [29259, 21], [29559, 21], [29859, 21], [30159, 21], [30459, 20], [30759, 20], [31059, 20], [31358, 21], [31658, 21], [31958, 21], [32258, 21], [32558, 21], [32859, 19], [33160, 18], [33461, 15], [33763, 12], [34068, 2]], "point": [170, 88]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-03.49|+01.43|+02.18"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [17, 14, 240, 126], "mask": [[3921, 168], [4090, 40], [4221, 168], [4390, 41], [4521, 210], [4820, 211], [5117, 224], [5417, 224], [5717, 224], [6017, 224], [6317, 224], [6618, 223], [6918, 223], [7218, 223], [7518, 223], [7818, 223], [8118, 222], [8418, 222], [8718, 222], [9018, 222], [9319, 221], [9619, 221], [9919, 221], [10219, 221], [10519, 221], [10819, 221], [11119, 221], [11419, 221], [11720, 219], [12020, 219], [12320, 219], [12620, 219], [12920, 219], [13220, 219], [13520, 219], [13820, 219], [14121, 218], [14421, 218], [14721, 218], [15021, 218], [15321, 217], [15621, 217], [15921, 217], [16221, 217], [16522, 216], [16822, 216], [17122, 216], [17422, 216], [17722, 216], [18022, 216], [18322, 216], [18622, 216], [18922, 215], [19223, 214], [19523, 214], [19823, 214], [20123, 214], [20423, 214], [20723, 214], [21023, 214], [21323, 214], [21624, 213], [21924, 213], [22224, 213], [22524, 212], [22824, 212], [23124, 212], [23424, 212], [23724, 212], [24025, 211], [24325, 211], [24625, 211], [24925, 211], [25225, 211], [25525, 211], [25825, 211], [26125, 210], [26425, 210], [26726, 209], [27026, 209], [27326, 209], [27626, 209], [27926, 209], [28226, 209], [28526, 209], [28826, 209], [29127, 208], [29427, 208], [29727, 207], [30027, 207], [30327, 207], [30627, 207], [30927, 207], [31227, 207], [31528, 206], [31828, 206], [32128, 206], [32428, 206], [32728, 206], [33028, 206], [33328, 205], [33628, 205], [33929, 204], [34229, 204], [34529, 204], [34829, 204], [35129, 204], [35429, 204], [35742, 178], [36062, 7], [36203, 7], [36362, 7], [36503, 6], [36662, 7], [36803, 6], [36962, 7], [37103, 6], [37262, 7], [37403, 6], [37563, 6], [37703, 6]], "point": [128, 69]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.92|+00.92|+04.19", "placeStationary": true, "receptacleObjectId": "Microwave|-03.49|+01.43|+02.18"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 240, 131], "mask": [[0, 2], [300, 4], [600, 6], [900, 8], [1200, 9], [1500, 11], [1800, 13], [2100, 15], [2400, 17], [2700, 19], [3000, 21], [3300, 22], [3600, 24], [3900, 26], [4090, 40], [4200, 28], [4390, 41], [4500, 30], [4689, 42], [4800, 231], [5100, 241], [5400, 241], [5700, 241], [6000, 241], [6300, 241], [6600, 241], [6900, 241], [7200, 241], [7500, 241], [7800, 241], [8100, 240], [8400, 240], [8700, 240], [9000, 240], [9300, 240], [9600, 240], [9900, 240], [10200, 240], [10500, 240], [10800, 240], [11100, 240], [11400, 240], [11700, 239], [12000, 239], [12300, 239], [12600, 239], [12900, 239], [13200, 239], [13500, 239], [13800, 239], [14100, 239], [14400, 239], [14700, 239], [15000, 239], [15300, 238], [15600, 238], [15900, 238], [16200, 238], [16500, 238], [16800, 238], [17100, 238], [17400, 238], [17700, 238], [18000, 238], [18300, 238], [18600, 238], [18900, 237], [19200, 237], [19500, 237], [19800, 237], [20100, 237], [20400, 237], [20700, 237], [21000, 237], [21300, 237], [21600, 237], [21900, 237], [22200, 237], [22500, 236], [22800, 236], [23100, 236], [23400, 236], [23700, 236], [24000, 236], [24300, 236], [24600, 236], [24900, 236], [25200, 236], [25500, 236], [25800, 236], [26100, 235], [26400, 235], [26700, 235], [27000, 235], [27300, 235], [27600, 235], [27900, 235], [28200, 235], [28500, 235], [28800, 235], [29100, 235], [29400, 235], [29700, 234], [30000, 234], [30300, 234], [30600, 234], [30900, 234], [31200, 234], [31500, 234], [31800, 234], [32100, 234], [32400, 234], [32700, 234], [33000, 234], [33300, 233], [33600, 233], [33900, 233], [34200, 233], [34500, 233], [34800, 233], [35100, 233], [35400, 233], [35700, 33], [35742, 178], [36000, 30], [36062, 7], [36203, 7], [36300, 27], [36362, 7], [36503, 6], [36600, 24], [36662, 7], [36803, 6], [36900, 21], [36962, 7], [37103, 6], [37200, 18], [37262, 7], [37403, 6], [37500, 15], [37563, 6], [37703, 6], [37800, 13], [38100, 10], [38400, 7], [38700, 4], [39000, 1]], "point": [120, 65]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-03.49|+01.43|+02.18"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 240, 131], "mask": [[0, 2], [300, 4], [600, 6], [900, 8], [1200, 9], [1500, 11], [1800, 13], [2100, 15], [2400, 17], [2700, 19], [3000, 21], [3300, 22], [3600, 24], [3900, 26], [4090, 40], [4200, 28], [4390, 41], [4500, 30], [4689, 42], [4800, 231], [5100, 241], [5400, 241], [5700, 241], [6000, 241], [6300, 241], [6600, 241], [6900, 241], [7200, 241], [7500, 241], [7800, 241], [8100, 240], [8400, 240], [8700, 240], [9000, 240], [9300, 240], [9600, 240], [9900, 240], [10200, 240], [10500, 240], [10800, 240], [11100, 240], [11400, 116], [11536, 104], [11700, 111], [11842, 97], [12000, 110], [12143, 96], [12300, 110], [12444, 95], [12600, 110], [12744, 95], [12900, 110], [13044, 95], [13200, 111], [13344, 95], [13500, 111], [13644, 95], [13800, 111], [13944, 95], [14100, 111], [14244, 95], [14400, 111], [14544, 95], [14700, 111], [14844, 95], [15000, 111], [15144, 95], [15300, 111], [15444, 94], [15600, 111], [15744, 94], [15900, 111], [16043, 95], [16200, 112], [16343, 95], [16500, 112], [16642, 96], [16800, 113], [16942, 96], [17100, 113], [17241, 97], [17400, 113], [17541, 97], [17700, 113], [17841, 97], [18000, 114], [18141, 97], [18300, 114], [18441, 97], [18600, 114], [18741, 97], [18900, 114], [19041, 96], [19200, 114], [19341, 96], [19500, 114], [19641, 96], [19800, 114], [19941, 96], [20100, 114], [20241, 96], [20400, 114], [20541, 96], [20700, 114], [20841, 96], [21000, 114], [21141, 96], [21300, 114], [21441, 96], [21600, 114], [21741, 96], [21900, 114], [22041, 96], [22200, 114], [22341, 96], [22500, 115], [22641, 95], [22800, 115], [22941, 95], [23100, 115], [23241, 95], [23400, 115], [23541, 95], [23700, 115], [23841, 95], [24000, 115], [24141, 95], [24300, 115], [24440, 96], [24600, 115], [24740, 96], [24900, 115], [25040, 96], [25200, 115], [25340, 96], [25500, 115], [25640, 96], [25800, 115], [25940, 96], [26100, 115], [26240, 95], [26400, 115], [26540, 95], [26700, 115], [26840, 95], [27000, 115], [27141, 94], [27300, 115], [27441, 94], [27600, 115], [27741, 94], [27900, 117], [28039, 96], [28200, 235], [28500, 235], [28800, 235], [29100, 235], [29400, 235], [29700, 234], [30000, 234], [30300, 234], [30600, 234], [30900, 234], [31200, 234], [31500, 234], [31800, 234], [32100, 234], [32400, 234], [32700, 234], [33000, 234], [33300, 233], [33600, 233], [33900, 233], [34200, 233], [34500, 233], [34800, 233], [35100, 233], [35400, 233], [35700, 33], [35742, 178], [36000, 30], [36062, 7], [36203, 7], [36300, 27], [36362, 7], [36503, 6], [36600, 24], [36662, 7], [36803, 6], [36900, 21], [36962, 7], [37103, 6], [37200, 18], [37262, 7], [37403, 6], [37500, 15], [37563, 6], [37703, 6], [37800, 13], [38100, 10], [38400, 7], [38700, 4], [39000, 1]], "point": [113, 65]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-13.94, -13.94, 8.732, 8.732, 5.723384, 5.723384]], "forceVisible": true, "objectId": "Microwave|-03.49|+01.43|+02.18"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [17, 14, 240, 126], "mask": [[3921, 168], [4090, 40], [4221, 168], [4390, 41], [4521, 210], [4820, 211], [5117, 224], [5417, 224], [5717, 224], [6017, 224], [6317, 224], [6618, 223], [6918, 223], [7218, 223], [7518, 223], [7818, 223], [8118, 222], [8418, 222], [8718, 222], [9018, 222], [9319, 221], [9619, 221], [9919, 221], [10219, 221], [10519, 221], [10819, 221], [11119, 221], [11419, 221], [11720, 219], [12020, 219], [12320, 219], [12620, 219], [12920, 219], [13220, 219], [13520, 219], [13820, 219], [14121, 218], [14421, 218], [14721, 218], [15021, 218], [15321, 217], [15621, 217], [15921, 217], [16221, 217], [16522, 216], [16822, 216], [17122, 216], [17422, 216], [17722, 216], [18022, 216], [18322, 216], [18622, 216], [18922, 215], [19223, 214], [19523, 214], [19823, 214], [20123, 214], [20423, 214], [20723, 214], [21023, 214], [21323, 214], [21624, 213], [21924, 213], [22224, 213], [22524, 212], [22824, 212], [23124, 212], [23424, 212], [23724, 212], [24025, 211], [24325, 211], [24625, 211], [24925, 211], [25225, 211], [25525, 211], [25825, 211], [26125, 210], [26425, 210], [26726, 209], [27026, 209], [27326, 209], [27626, 209], [27926, 209], [28226, 209], [28526, 209], [28826, 209], [29127, 208], [29427, 208], [29727, 207], [30027, 207], [30327, 207], [30627, 207], [30927, 207], [31227, 207], [31528, 206], [31828, 206], [32128, 206], [32428, 206], [32728, 206], [33028, 206], [33328, 205], [33628, 205], [33929, 204], [34229, 204], [34529, 204], [34829, 204], [35129, 204], [35429, 204], [35742, 178], [36062, 7], [36203, 7], [36362, 7], [36503, 6], [36662, 7], [36803, 6], [36962, 7], [37103, 6], [37262, 7], [37403, 6], [37563, 6], [37703, 6]], "point": [128, 69]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-13.94, -13.94, 8.732, 8.732, 5.723384, 5.723384]], "forceVisible": true, "objectId": "Microwave|-03.49|+01.43|+02.18"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [17, 14, 240, 126], "mask": [[3921, 168], [4090, 40], [4221, 168], [4390, 41], [4521, 210], [4820, 211], [5117, 224], [5417, 224], [5717, 224], [6017, 224], [6317, 224], [6618, 223], [6918, 223], [7218, 223], [7518, 223], [7818, 223], [8118, 222], [8418, 222], [8718, 222], [9018, 222], [9319, 221], [9619, 221], [9919, 221], [10219, 221], [10519, 221], [10819, 221], [11119, 221], [11419, 221], [11720, 219], [12020, 219], [12320, 219], [12620, 219], [12920, 219], [13220, 219], [13520, 219], [13820, 219], [14121, 218], [14421, 218], [14721, 218], [15021, 218], [15321, 217], [15621, 217], [15921, 217], [16221, 217], [16522, 216], [16822, 216], [17122, 216], [17422, 216], [17722, 216], [18022, 216], [18322, 216], [18622, 216], [18922, 215], [19223, 214], [19523, 214], [19823, 214], [20123, 214], [20423, 214], [20723, 214], [21023, 214], [21323, 214], [21624, 213], [21924, 213], [22224, 213], [22524, 212], [22824, 212], [23124, 212], [23424, 212], [23724, 212], [24025, 211], [24325, 211], [24625, 211], [24925, 211], [25225, 211], [25525, 211], [25825, 211], [26125, 210], [26425, 210], [26726, 209], [27026, 209], [27326, 209], [27626, 209], [27926, 209], [28226, 209], [28526, 209], [28826, 209], [29127, 208], [29427, 208], [29727, 207], [30027, 207], [30327, 207], [30627, 207], [30927, 207], [31227, 207], [31528, 206], [31828, 206], [32128, 206], [32428, 206], [32728, 206], [33028, 206], [33328, 205], [33628, 205], [33929, 204], [34229, 204], [34529, 204], [34829, 204], [35129, 204], [35429, 204], [35742, 178], [36062, 7], [36203, 7], [36362, 7], [36503, 6], [36662, 7], [36803, 6], [36962, 7], [37103, 6], [37262, 7], [37403, 6], [37563, 6], [37703, 6]], "point": [128, 69]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-03.49|+01.43|+02.18"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [17, 14, 240, 126], "mask": [[3921, 168], [4090, 40], [4221, 168], [4390, 41], [4521, 210], [4820, 211], [5117, 224], [5417, 224], [5717, 224], [6017, 224], [6317, 224], [6618, 223], [6918, 223], [7218, 223], [7518, 223], [7818, 223], [8118, 222], [8418, 222], [8718, 222], [9018, 222], [9319, 221], [9619, 221], [9919, 221], [10219, 221], [10519, 221], [10819, 221], [11119, 221], [11419, 221], [11720, 219], [12020, 219], [12320, 219], [12620, 219], [12920, 219], [13220, 219], [13520, 219], [13820, 219], [14121, 218], [14421, 218], [14721, 218], [15021, 218], [15321, 217], [15621, 217], [15921, 217], [16221, 217], [16522, 216], [16822, 216], [17122, 216], [17422, 216], [17722, 216], [18022, 216], [18322, 216], [18622, 216], [18922, 215], [19223, 214], [19523, 214], [19823, 214], [20123, 214], [20423, 214], [20723, 214], [21023, 214], [21323, 214], [21624, 213], [21924, 213], [22224, 213], [22524, 212], [22824, 212], [23124, 212], [23424, 212], [23724, 212], [24025, 211], [24325, 211], [24625, 211], [24925, 211], [25225, 211], [25525, 211], [25825, 211], [26125, 210], [26425, 210], [26726, 209], [27026, 209], [27326, 209], [27626, 209], [27926, 209], [28226, 209], [28526, 209], [28826, 209], [29127, 208], [29427, 208], [29727, 207], [30027, 207], [30327, 207], [30627, 207], [30927, 207], [31227, 207], [31528, 206], [31828, 206], [32128, 206], [32428, 206], [32728, 206], [33028, 206], [33328, 205], [33628, 205], [33929, 204], [34229, 204], [34529, 204], [34829, 204], [35129, 204], [35429, 204], [35742, 178], [36062, 7], [36203, 7], [36362, 7], [36503, 6], [36662, 7], [36803, 6], [36962, 7], [37103, 6], [37262, 7], [37403, 6], [37563, 6], [37703, 6]], "point": [128, 69]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.92|+00.92|+04.19"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [110, 39, 143, 94], "mask": [[11516, 20], [11811, 31], [12110, 33], [12410, 34], [12710, 34], [13010, 34], [13311, 33], [13611, 33], [13911, 33], [14211, 33], [14511, 33], [14811, 33], [15111, 33], [15411, 33], [15711, 33], [16011, 32], [16312, 31], [16612, 30], [16913, 29], [17213, 28], [17513, 28], [17813, 28], [18114, 27], [18414, 27], [18714, 27], [19014, 27], [19314, 27], [19614, 27], [19914, 27], [20214, 27], [20514, 27], [20814, 27], [21114, 27], [21414, 27], [21714, 27], [22014, 27], [22314, 27], [22615, 26], [22915, 26], [23215, 26], [23515, 26], [23815, 26], [24115, 26], [24415, 25], [24715, 25], [25015, 25], [25315, 25], [25615, 25], [25915, 25], [26215, 25], [26515, 25], [26815, 25], [27115, 26], [27415, 26], [27715, 26], [28017, 22]], "point": [126, 65]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-03.49|+01.43|+02.18"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 240, 131], "mask": [[0, 2], [300, 4], [600, 6], [900, 8], [1200, 9], [1500, 11], [1800, 13], [2100, 15], [2400, 17], [2700, 19], [3000, 21], [3300, 22], [3600, 24], [3900, 26], [4090, 40], [4200, 28], [4390, 41], [4500, 30], [4689, 42], [4800, 231], [5100, 241], [5400, 241], [5700, 241], [6000, 241], [6300, 241], [6600, 241], [6900, 241], [7200, 241], [7500, 241], [7800, 241], [8100, 240], [8400, 240], [8700, 240], [9000, 240], [9300, 240], [9600, 240], [9900, 240], [10200, 240], [10500, 240], [10800, 240], [11100, 240], [11400, 240], [11700, 239], [12000, 239], [12300, 239], [12600, 239], [12900, 239], [13200, 239], [13500, 239], [13800, 239], [14100, 239], [14400, 239], [14700, 239], [15000, 239], [15300, 238], [15600, 238], [15900, 238], [16200, 238], [16500, 238], [16800, 238], [17100, 238], [17400, 238], [17700, 238], [18000, 238], [18300, 238], [18600, 238], [18900, 237], [19200, 237], [19500, 237], [19800, 237], [20100, 237], [20400, 237], [20700, 237], [21000, 237], [21300, 237], [21600, 237], [21900, 237], [22200, 237], [22500, 236], [22800, 236], [23100, 236], [23400, 236], [23700, 236], [24000, 236], [24300, 236], [24600, 236], [24900, 236], [25200, 236], [25500, 236], [25800, 236], [26100, 235], [26400, 235], [26700, 235], [27000, 235], [27300, 235], [27600, 235], [27900, 235], [28200, 235], [28500, 235], [28800, 235], [29100, 235], [29400, 235], [29700, 234], [30000, 234], [30300, 234], [30600, 234], [30900, 234], [31200, 234], [31500, 234], [31800, 234], [32100, 234], [32400, 234], [32700, 234], [33000, 234], [33300, 233], [33600, 233], [33900, 233], [34200, 233], [34500, 233], [34800, 233], [35100, 233], [35400, 233], [35700, 33], [35742, 178], [36000, 30], [36062, 7], [36203, 7], [36300, 27], [36362, 7], [36503, 6], [36600, 24], [36662, 7], [36803, 6], [36900, 21], [36962, 7], [37103, 6], [37200, 18], [37262, 7], [37403, 6], [37500, 15], [37563, 6], [37703, 6], [37800, 13], [38100, 10], [38400, 7], [38700, 4], [39000, 1]], "point": [120, 65]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-03.35|+02.21|+01.72"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [1, 82, 131, 175], "mask": [[24330, 101], [24629, 103], [24929, 103], [25229, 103], [25528, 104], [25828, 104], [26128, 104], [26427, 105], [26727, 105], [27027, 105], [27327, 105], [27626, 106], [27926, 106], [28226, 106], [28525, 106], [28825, 106], [29125, 106], [29424, 107], [29724, 107], [30024, 107], [30323, 108], [30623, 108], [30923, 108], [31222, 109], [31522, 109], [31822, 109], [32121, 110], [32421, 110], [32721, 110], [33021, 110], [33320, 111], [33620, 111], [33920, 111], [34219, 112], [34519, 112], [34819, 111], [35118, 112], [35418, 112], [35718, 112], [36017, 113], [36317, 113], [36617, 113], [36916, 114], [37216, 114], [37516, 114], [37815, 115], [38115, 115], [38415, 115], [38715, 115], [39014, 116], [39314, 116], [39614, 116], [39913, 117], [40213, 117], [40513, 117], [40812, 118], [41112, 111], [41412, 107], [41711, 104], [42011, 101], [42311, 100], [42610, 99], [42910, 98], [43210, 96], [43509, 97], [43809, 97], [44109, 97], [44409, 97], [44708, 99], [45008, 99], [45308, 99], [45607, 100], [45907, 100], [46207, 100], [46506, 101], [46806, 101], [47106, 101], [47405, 102], [47705, 102], [48005, 102], [48304, 104], [48604, 104], [48904, 104], [49203, 105], [49503, 105], [49803, 105], [50103, 105], [50402, 106], [50702, 106], [51002, 106], [51301, 107], [51601, 107], [51901, 108], [52202, 107]], "point": [66, 127]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.92|+00.92|+04.19", "placeStationary": true, "receptacleObjectId": "Cabinet|-03.35|+02.21|+01.72"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 26, 130, 175], "mask": [[7500, 1], [7800, 1], [8100, 2], [8400, 2], [8700, 3], [9000, 3], [9300, 4], [9600, 4], [9900, 5], [10200, 5], [10500, 6], [10800, 7], [11100, 7], [11400, 8], [11700, 8], [12000, 9], [12300, 9], [12600, 10], [12900, 10], [13200, 11], [13500, 11], [13800, 12], [14100, 13], [14400, 13], [14700, 14], [15000, 14], [15300, 15], [15600, 15], [15900, 16], [16200, 16], [16500, 17], [16800, 17], [17100, 18], [17400, 19], [17700, 19], [18000, 20], [18300, 20], [18600, 21], [18900, 21], [19200, 22], [19500, 22], [19800, 23], [20100, 23], [20400, 24], [20700, 25], [21000, 25], [21300, 26], [21600, 26], [21900, 27], [22200, 27], [22500, 28], [22800, 28], [23100, 29], [23400, 29], [23700, 30], [24000, 31], [24300, 31], [24600, 31], [24900, 31], [25200, 31], [25500, 30], [25534, 97], [25800, 30], [25833, 98], [26100, 30], [26133, 98], [26400, 30], [26433, 98], [26700, 29], [26732, 99], [27000, 29], [27032, 99], [27300, 29], [27332, 99], [27600, 28], [27631, 100], [27900, 28], [27931, 100], [28200, 28], [28231, 100], [28500, 27], [28531, 100], [28800, 27], [28830, 100], [29100, 27], [29130, 100], [29400, 26], [29430, 100], [29700, 26], [29729, 101], [30000, 26], [30029, 101], [30300, 25], [30329, 101], [30600, 25], [30628, 102], [30900, 25], [30928, 102], [31200, 25], [31228, 102], [31500, 24], [31527, 103], [31800, 24], [31827, 103], [32100, 24], [32127, 103], [32400, 23], [32427, 103], [32700, 23], [32726, 104], [33000, 23], [33026, 104], [33300, 22], [33326, 104], [33600, 22], [33625, 105], [33900, 22], [33925, 105], [34200, 21], [34225, 105], [34500, 21], [34525, 105], [34800, 21], [35100, 21], [35400, 20], [35700, 20], [36000, 20], [36300, 19], [36323, 106], [36600, 19], [36622, 107], [36900, 19], [36922, 107], [37200, 18], [37222, 107], [37500, 18], [37521, 108], [37800, 18], [37821, 108], [38100, 17], [38121, 108], [38400, 17], [38421, 108], [38700, 17], [38720, 109], [39000, 17], [39020, 109], [39300, 16], [39320, 109], [39600, 16], [39619, 110], [39900, 16], [39919, 110], [40200, 15], [40219, 110], [40500, 15], [40518, 111], [40800, 15], [40818, 110], [41100, 14], [41118, 105], [41400, 14], [41417, 102], [41700, 14], [41717, 98], [42000, 13], [42017, 95], [42300, 13], [42317, 94], [42600, 13], [42616, 93], [42900, 12], [42916, 92], [43200, 12], [43216, 90], [43500, 12], [43515, 91], [43800, 12], [43815, 91], [44100, 11], [44115, 91], [44400, 11], [44414, 92], [44700, 11], [44714, 93], [45000, 10], [45014, 93], [45300, 10], [45314, 93], [45600, 10], [45613, 94], [45900, 9], [45913, 94], [46200, 9], [46213, 94], [46500, 9], [46512, 95], [46800, 8], [46812, 95], [47100, 8], [47112, 95], [47400, 8], [47411, 96], [47700, 8], [47711, 96], [48000, 7], [48011, 96], [48300, 7], [48311, 97], [48600, 7], [48610, 98], [48900, 6], [48910, 98], [49200, 6], [49210, 98], [49500, 6], [49509, 99], [49800, 5], [49809, 99], [50100, 5], [50109, 99], [50400, 5], [50408, 100], [50700, 4], [50708, 100], [51000, 4], [51008, 100], [51300, 4], [51308, 100], [51600, 3], [51607, 101], [51900, 3], [51907, 102], [52207, 102]], "point": [65, 99]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-03.35|+02.21|+01.72"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 26, 130, 175], "mask": [[7500, 1], [7800, 1], [8100, 2], [8400, 2], [8700, 3], [9000, 3], [9300, 4], [9600, 4], [9900, 5], [10200, 5], [10500, 6], [10800, 7], [11100, 7], [11400, 8], [11700, 8], [12000, 9], [12300, 9], [12600, 10], [12900, 10], [13200, 11], [13500, 11], [13800, 12], [14100, 13], [14400, 13], [14700, 14], [15000, 14], [15300, 15], [15600, 15], [15900, 16], [16200, 16], [16500, 17], [16800, 17], [17100, 18], [17400, 19], [17700, 19], [18000, 20], [18300, 20], [18600, 21], [18900, 21], [19200, 22], [19500, 22], [19800, 23], [20100, 23], [20400, 24], [20700, 25], [21000, 25], [21300, 26], [21600, 26], [21900, 27], [22200, 27], [22500, 28], [22800, 28], [23100, 29], [23400, 29], [23700, 30], [24000, 31], [24300, 31], [24600, 31], [24900, 31], [25200, 31], [25500, 30], [25534, 97], [25800, 30], [25833, 98], [26100, 30], [26133, 98], [26400, 30], [26433, 98], [26700, 29], [26732, 99], [27000, 29], [27032, 99], [27300, 29], [27332, 99], [27600, 28], [27631, 100], [27900, 28], [27931, 100], [28200, 28], [28231, 100], [28500, 27], [28531, 100], [28800, 27], [28830, 100], [29100, 27], [29130, 100], [29400, 26], [29430, 100], [29700, 26], [29729, 101], [30000, 26], [30029, 101], [30300, 25], [30329, 101], [30600, 25], [30628, 102], [30900, 25], [30928, 102], [31200, 25], [31228, 102], [31500, 24], [31527, 103], [31800, 24], [31827, 103], [32100, 24], [32127, 103], [32400, 23], [32427, 103], [32700, 23], [32726, 104], [33000, 23], [33026, 104], [33300, 22], [33326, 104], [33600, 22], [33625, 105], [33900, 22], [33925, 105], [34200, 21], [34225, 105], [34500, 21], [34525, 105], [34800, 21], [35100, 21], [35400, 20], [35700, 20], [36000, 20], [36300, 19], [36323, 106], [36600, 19], [36622, 107], [36900, 19], [36922, 107], [37200, 18], [37222, 107], [37500, 18], [37521, 108], [37800, 18], [37821, 108], [38100, 17], [38121, 108], [38400, 17], [38421, 108], [38700, 17], [38720, 109], [39000, 17], [39020, 109], [39300, 16], [39320, 109], [39600, 16], [39619, 110], [39900, 16], [39919, 110], [40200, 15], [40219, 76], [40308, 21], [40500, 15], [40518, 75], [40611, 18], [40800, 15], [40818, 74], [40913, 15], [41100, 14], [41118, 73], [41214, 14], [41400, 14], [41417, 73], [41515, 13], [41700, 14], [41717, 73], [41816, 12], [42000, 13], [42017, 73], [42116, 12], [42300, 13], [42317, 73], [42417, 11], [42600, 13], [42616, 73], [42717, 11], [42900, 12], [42916, 73], [43017, 11], [43200, 12], [43216, 73], [43316, 12], [43500, 12], [43515, 74], [43616, 12], [43800, 12], [43815, 74], [43916, 12], [44100, 11], [44115, 74], [44216, 12], [44400, 11], [44414, 75], [44516, 12], [44700, 11], [44714, 75], [44816, 12], [45000, 10], [45014, 76], [45116, 12], [45300, 10], [45314, 76], [45416, 12], [45600, 10], [45613, 77], [45715, 13], [45900, 9], [45913, 77], [46014, 14], [46200, 9], [46213, 77], [46314, 14], [46500, 9], [46512, 78], [46614, 14], [46800, 8], [46812, 77], [46914, 13], [47100, 8], [47112, 77], [47214, 13], [47400, 8], [47411, 78], [47513, 14], [47700, 8], [47711, 78], [47813, 14], [48000, 7], [48011, 78], [48113, 14], [48300, 7], [48311, 78], [48413, 14], [48600, 7], [48610, 79], [48713, 14], [48900, 6], [48910, 79], [49013, 14], [49200, 6], [49210, 79], [49313, 14], [49500, 6], [49509, 80], [49613, 14], [49800, 5], [49809, 80], [49913, 14], [50100, 5], [50109, 79], [50212, 15], [50400, 5], [50408, 80], [50512, 15], [50700, 4], [50708, 80], [50812, 15], [51000, 4], [51008, 80], [51112, 15], [51300, 4], [51308, 80], [51412, 15], [51600, 3], [51607, 81], [51712, 15], [51900, 3], [51907, 81], [52012, 15], [52207, 81], [52312, 15]], "point": [65, 99]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan15", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -2.25, "y": 0.914953351, "z": 2.5}, "object_poses": [{"objectName": "Pan_7e5e2cad", "position": {"x": -3.1432, "y": 0.9668, "z": 1.9715}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_73731673", "position": {"x": -1.71187186, "y": 0.915064156, "z": 4.14679575}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_73731673", "position": {"x": -3.65481567, "y": 0.915364265, "z": 1.60797334}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_657c04b2", "position": {"x": -3.36803436, "y": 0.9158421, "z": 1.44065785}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "WineBottle_2242b994", "position": {"x": -2.30927181, "y": 0.08194047, "z": 3.62530613}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_2242b994", "position": {"x": -2.40283132, "y": 0.9164286, "z": 4.10673857}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_a09ab4be", "position": {"x": -0.994908631, "y": 0.892513931, "z": 0.152496532}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Kettle_a09ab4be", "position": {"x": -3.4247, "y": 0.9668, "z": 1.9715}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_fd79f50b", "position": {"x": -2.80491138, "y": 0.9304998, "z": 3.56724548}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_fd79f50b", "position": {"x": -1.65551162, "y": 0.9064725, "z": -0.0535847545}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Spoon_58cf1806", "position": {"x": -3.36803436, "y": 0.9166294, "z": 3.00817466}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Spoon_58cf1806", "position": {"x": -1.78763235, "y": 0.892602, "z": 0.255537331}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_69fc67c7", "position": {"x": -1.91912472, "y": 0.9152868, "z": 4.18891144}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_0e75bb4f", "position": {"x": -2.12637734, "y": 0.914389, "z": 4.18891144}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_d1c7827d", "position": {"x": -1.56585193, "y": 0.07731795, "z": 3.58648038}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_94539d9a", "position": {"x": -1.25914979, "y": 0.8877788, "z": 0.4616186}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_94931e65", "position": {"x": -1.523391, "y": 0.9775047, "z": 0.255537271}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_0fed2699", "position": {"x": -1.1929, "y": 0.167984962, "z": 3.78829956}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bread_0fed2699", "position": {"x": -1.25914979, "y": 0.9728629, "z": 0.255537271}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_0af3d2bb", "position": {"x": -3.54094815, "y": 1.45412147, "z": 1.15218687}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_0af3d2bb", "position": {"x": -1.79379869, "y": 0.78455615, "z": 3.662686}, "rotation": {"x": 0.0, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Apple_8bd3d3d2", "position": {"x": -0.43999964, "y": 1.04621, "z": 3.92641139}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Apple_8bd3d3d2", "position": {"x": -0.994908631, "y": 0.95695, "z": -0.0535848737}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_0c310241", "position": {"x": -3.081253, "y": 0.950663269, "z": 3.17208719}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_4b738b3b", "position": {"x": -0.9949086, "y": 0.9150003, "z": 0.358577877}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_72bf05b8", "position": {"x": -2.30231142, "y": 0.916048467, "z": 4.21463728}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bread_0fed2699", "position": {"x": -3.463628, "y": 0.9971903, "z": 1.18968439}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_08de48c7", "position": {"x": -1.523391, "y": 0.9238395, "z": 0.564659357}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_a5e935d4", "position": {"x": -3.0059998, "y": 0.9045925, "z": 3.677001}, "rotation": {"x": 0.0198185351, "y": -0.000155744448, "z": 0.00135103019}}, {"objectName": "Pot_46b03e61", "position": {"x": -3.144, "y": 0.9633135, "z": 2.374}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_a09ab4be", "position": {"x": -3.176847, "y": 0.916841269, "z": 1.27334213}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_2242b994", "position": {"x": -3.081253, "y": 0.9164287, "z": 3.00817466}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_0e75bb4f", "position": {"x": -1.523391, "y": 0.8903617, "z": 0.04945594}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_66cbdca0", "position": {"x": -0.790436149, "y": 1.05489373, "z": 3.80125165}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Cup_69fc67c7", "position": {"x": -0.439999282, "y": 0.722673535, "z": 3.86935735}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Spatula_fd79f50b", "position": {"x": -3.463628, "y": 0.930499852, "z": 3.66382527}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_da34a2c3", "position": {"x": -3.45919847, "y": 1.99503958, "z": 2.42121816}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_94931e65", "position": {"x": -0.673625231, "y": 1.0722692, "z": 4.05156755}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Pan_7e5e2cad", "position": {"x": -2.61477637, "y": 0.902653933, "z": 3.62041235}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_8bd3d3d2", "position": {"x": -1.92159271, "y": 0.8495291, "z": 3.737314}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_d1c7827d", "position": {"x": -3.271956, "y": 0.07780033, "z": 2.75202847}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_94539d9a", "position": {"x": -3.126937, "y": 0.7430969, "z": 3.02765965}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_657c04b2", "position": {"x": -3.36803436, "y": 0.915542066, "z": 3.82773781}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_73731673", "position": {"x": -1.65551162, "y": 0.891036868, "z": 0.564659357}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_58cf1806", "position": {"x": -2.40283132, "y": 0.9166293, "z": 3.89094138}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_0af3d2bb", "position": {"x": -3.463628, "y": 0.91080004, "z": 1.44065785}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1986670109, "scene_num": 15}, "task_id": "trial_T20190908_055611_176313", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A17TKHT8FEVH0R_3YT88D1N0BFNDLYDBLYO49A5VQTK34", "high_descs": ["Turn around and go to the sink", "Grab the cup that is at the top of the sink", "Turn around and go to the microwave", "Heat the cup in the microwave and then take it out", "Look up at the cabinets above the microwave", "Put the cup in the top left cabinet"], "task_desc": "Putting a cup in a cabinet", "votes": [1, 1, 1, 1, 1]}, {"assignment_id": "AKW57KYG90X61_308Q0PEVBBUIMSX5SREMI6YE3GMI9K", "high_descs": ["move around to the sink", "pick up the cup", "turn left to the oven", "keep the cup to heat in the oven and take it out", "move up to the shelf above the oven", "place the cup in the shelf"], "task_desc": "place a hot cup in the shelf above the oven", "votes": [1, 0, 1, 1, 1]}, {"assignment_id": "A1SX8IVV82M0LW_3SKEMFQBZ6M9EI4JN2TKVQIGCJNK8D", "high_descs": ["Walk to the kitchen sink on your left.", "Grab a cup from the sink.", "Walk in front of the microwave on your left.", "Place the cup in the microwave, then remove it.", "Locate the cabinet above the microwave.", "Place the cup in the left cabinet."], "task_desc": "Heat a cup and put it away in a cabinet.", "votes": [1, 1]}]}}