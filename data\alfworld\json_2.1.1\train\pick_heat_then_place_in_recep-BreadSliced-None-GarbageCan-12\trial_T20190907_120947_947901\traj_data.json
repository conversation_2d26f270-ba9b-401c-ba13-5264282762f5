{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000139.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000140.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000141.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000142.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000143.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000144.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000145.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000146.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000147.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000148.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000149.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000150.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000151.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000152.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000153.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000154.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000155.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000156.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000157.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000158.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000159.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000160.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000161.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000162.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000163.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000164.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000165.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000166.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000167.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000168.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000169.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000170.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000171.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000172.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000173.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000174.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000175.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000176.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000177.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000178.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000179.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000180.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000181.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000214.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000215.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000216.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000217.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000218.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000219.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000220.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000221.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000222.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000223.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000224.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000225.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000226.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000227.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000228.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000229.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000230.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000231.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000232.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000233.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000234.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000235.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000236.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000237.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000238.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000239.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000240.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000241.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000242.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000243.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000244.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000245.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000246.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000247.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000248.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000249.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000250.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000251.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000252.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000253.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000254.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000255.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000256.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000257.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000258.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000259.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000260.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000261.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000262.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000263.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000264.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000265.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000266.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000267.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000268.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000269.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000270.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000271.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000272.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000273.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000274.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000275.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000277.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000278.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000279.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000280.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000281.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000282.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000284.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000285.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000286.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000287.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000288.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000289.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000290.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000291.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000292.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000293.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000294.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000295.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000296.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000297.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000298.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000299.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000300.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000301.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000302.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000303.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000304.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000305.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000306.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000307.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000308.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000309.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000310.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000311.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000312.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000313.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000314.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000315.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000316.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000317.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000318.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000319.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000320.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000321.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000322.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000323.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000324.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000325.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000326.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000327.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000328.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000329.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000330.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000331.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000332.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000333.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000334.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000335.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000336.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000337.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000338.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000339.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000340.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000341.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000342.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000343.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000344.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000345.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000346.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000347.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000348.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000349.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000350.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000351.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000352.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000353.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000354.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000355.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000356.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000357.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000358.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000359.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000360.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000361.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000362.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000363.png", "low_idx": 52}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "GarbageCan", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-1|5|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["butterknife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ButterKnife", [-3.175119876, -3.175119876, 4.61625768, 4.61625768, 3.7517684, 3.7517684]], "coordinateReceptacleObjectId": ["CounterTop", [-3.888, -3.888, 3.684, 3.684, 3.8936, 3.8936]], "forceVisible": true, "objectId": "ButterKnife|-00.79|+00.94|+01.15"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-1|9|3|45"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-3.6920008, -3.6920008, 9.06199932, 9.06199932, 3.96270418, 3.96270418]], "forceVisible": true, "objectId": "Bread|-00.92|+00.99|+02.27"}}, {"discrete_action": {"action": "PutObject", "args": ["butterknife", "countertop"]}, "high_idx": 4, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ButterKnife", [-3.175119876, -3.175119876, 4.61625768, 4.61625768, 3.7517684, 3.7517684]], "coordinateReceptacleObjectId": ["CounterTop", [-3.8944, -3.8944, 9.5456, 9.5456, 3.8936, 3.8936]], "forceVisible": true, "objectId": "ButterKnife|-00.79|+00.94|+01.15", "receptacleObjectId": "CounterTop|-00.97|+00.97|+02.39"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-3.6920008, -3.6920008, 9.06199932, 9.06199932, 3.96270418, 3.96270418]], "coordinateReceptacleObjectId": ["CounterTop", [-3.8944, -3.8944, 9.5456, 9.5456, 3.8936, 3.8936]], "forceVisible": true, "objectId": "Bread|-00.92|+00.99|+02.27|BreadSliced_4"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|4|6|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|4|11|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "garbagecan"]}, "high_idx": 9, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-3.6920008, -3.6920008, 9.06199932, 9.06199932, 3.96270418, 3.96270418]], "coordinateReceptacleObjectId": ["GarbageCan", [5.80777024, 5.80777024, 11.65005396, 11.65005396, 0.0, 0.0]], "forceVisible": true, "objectId": "Bread|-00.92|+00.99|+02.27|BreadSliced_4", "receptacleObjectId": "GarbageCan|+01.45|+00.00|+02.91"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ButterKnife|-00.79|+00.94|+01.15"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [112, 72, 124, 138], "mask": [[21421, 2], [21719, 5], [22019, 6], [22319, 6], [22618, 7], [22918, 7], [23218, 7], [23518, 7], [23818, 7], [24118, 6], [24418, 6], [24718, 6], [25018, 6], [25318, 6], [25618, 6], [25918, 6], [26218, 5], [26518, 5], [26818, 5], [27118, 5], [27418, 5], [27718, 4], [28018, 4], [28318, 4], [28618, 4], [28918, 4], [29218, 4], [29518, 4], [29817, 5], [30117, 4], [30417, 4], [30717, 4], [31016, 5], [31316, 5], [31616, 5], [31916, 5], [32215, 6], [32515, 6], [32815, 6], [33114, 7], [33414, 7], [33714, 7], [34013, 8], [34313, 8], [34613, 7], [34913, 7], [35213, 7], [35513, 7], [35812, 8], [36112, 8], [36412, 8], [36712, 8], [37012, 8], [37312, 8], [37612, 8], [37912, 7], [38213, 6], [38513, 6], [38813, 6], [39113, 6], [39413, 6], [39713, 6], [40014, 5], [40314, 5], [40614, 5], [40914, 5], [41215, 4]], "point": [118, 104]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-00.92|+00.99|+02.27"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [115, 101, 189, 176], "mask": [[30165, 4], [30462, 11], [30761, 14], [31056, 21], [31355, 23], [31653, 26], [31952, 29], [32252, 32], [32551, 33], [32850, 35], [33149, 36], [33448, 38], [33746, 40], [34045, 41], [34344, 42], [34642, 44], [34941, 46], [35241, 47], [35540, 48], [35839, 49], [36138, 50], [36437, 52], [36736, 53], [37035, 54], [37333, 56], [37633, 56], [37932, 57], [38231, 59], [38531, 59], [38830, 59], [39128, 61], [39427, 62], [39726, 62], [40024, 63], [40324, 63], [40623, 63], [40922, 64], [41222, 63], [41521, 63], [41820, 63], [42120, 62], [42419, 63], [42719, 62], [43019, 61], [43318, 61], [43619, 59], [43918, 58], [44218, 58], [44517, 59], [44817, 58], [45116, 59], [45416, 58], [45716, 57], [46015, 58], [46315, 57], [46615, 56], [46915, 55], [47215, 54], [47515, 54], [47815, 53], [48115, 51], [48415, 49], [48716, 48], [49016, 47], [49317, 45], [49617, 26], [49646, 16], [49918, 23], [49948, 13], [50219, 21], [50249, 12], [50521, 18], [50550, 10], [50822, 17], [50850, 9], [51124, 15], [51149, 10], [51426, 14], [51449, 9], [51727, 12], [51750, 7], [52030, 9], [52051, 4], [52333, 5], [52351, 3], [52635, 3], [52651, 2]], "point": [152, 137]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ButterKnife|-00.79|+00.94|+01.15", "placeStationary": true, "receptacleObjectId": "CounterTop|-00.97|+00.97|+02.39"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [92, 90, 299, 216], "mask": [[26815, 28], [26850, 5], [26874, 28], [27114, 29], [27149, 6], [27174, 28], [27414, 28], [27450, 5], [27474, 28], [27714, 27], [27751, 4], [27773, 29], [28014, 27], [28051, 5], [28073, 28], [28314, 27], [28351, 5], [28373, 28], [28613, 28], [28651, 5], [28673, 28], [28913, 28], [28951, 5], [28973, 28], [29213, 28], [29252, 4], [29272, 29], [29513, 28], [29552, 4], [29572, 29], [29813, 28], [29852, 4], [29872, 29], [30113, 28], [30152, 5], [30171, 30], [30412, 29], [30452, 8], [30474, 27], [30712, 28], [30752, 6], [30759, 2], [30776, 26], [31012, 28], [31052, 4], [31077, 26], [31312, 28], [31352, 2], [31379, 24], [31612, 28], [31652, 1], [31680, 23], [31911, 29], [31983, 21], [32211, 29], [32284, 20], [32511, 29], [32585, 19], [32811, 29], [32885, 19], [33111, 29], [33186, 19], [33411, 30], [33486, 19], [33710, 32], [33786, 19], [34010, 35], [34086, 19], [34310, 34], [34386, 20], [34610, 32], [34686, 20], [34910, 32], [34987, 19], [35210, 31], [35288, 18], [35509, 31], [35588, 19], [35809, 30], [35889, 18], [36109, 29], [36189, 18], [36409, 28], [36489, 18], [36709, 26], [36789, 19], [37008, 26], [37089, 19], [37308, 26], [37390, 18], [37608, 24], [37690, 18], [37908, 24], [37990, 19], [38208, 23], [38290, 19], [38508, 22], [38590, 19], [38807, 23], [38890, 19], [39107, 22], [39189, 21], [39407, 20], [39489, 21], [39707, 18], [39788, 22], [40007, 17], [40088, 22], [40306, 17], [40387, 24], [40606, 16], [40687, 16], [40707, 4], [40906, 15], [40986, 15], [41206, 15], [41285, 15], [41506, 14], [41584, 15], [41806, 13], [41883, 16], [42105, 14], [42183, 15], [42405, 13], [42482, 14], [42705, 13], [42781, 9], [43005, 12], [43080, 8], [43305, 12], [43379, 7], [43605, 11], [43678, 6], [43904, 12], [43977, 6], [44204, 12], [44276, 6], [44504, 12], [44576, 5], [44804, 11], [44875, 5], [45104, 11], [45175, 4], [45403, 11], [45474, 5], [45703, 11], [45773, 5], [46003, 11], [46073, 5], [46303, 10], [46372, 6], [46603, 10], [46671, 7], [46903, 10], [46970, 8], [47202, 10], [47270, 8], [47502, 10], [47569, 8], [47802, 10], [47868, 9], [47978, 1], [48102, 10], [48167, 10], [48278, 1], [48402, 10], [48466, 11], [48577, 3], [48702, 10], [48764, 13], [48877, 3], [49001, 12], [49063, 14], [49177, 4], [49301, 12], [49362, 15], [49476, 5], [49601, 12], [49662, 15], [49775, 6], [49901, 13], [49961, 16], [50074, 8], [50201, 13], [50260, 17], [50327, 39], [50374, 8], [50500, 15], [50560, 17], [50627, 39], [50673, 10], [50800, 15], [50859, 18], [50925, 42], [50973, 10], [51100, 17], [51159, 18], [51226, 42], [51272, 12], [51400, 18], [51458, 19], [51525, 59], [51700, 20], [51757, 20], [51825, 60], [52000, 21], [52056, 21], [52125, 60], [52299, 24], [52355, 22], [52424, 62], [52599, 25], [52655, 22], [52724, 62], [52899, 28], [52953, 24], [53023, 64], [53199, 31], [53252, 25], [53323, 64], [53499, 33], [53552, 25], [53622, 66], [53798, 37], [53852, 25], [53922, 66], [54098, 39], [54152, 25], [54222, 67], [54398, 39], [54452, 25], [54521, 68], [54698, 39], [54753, 24], [54821, 69], [54998, 39], [55053, 24], [55120, 70], [55298, 39], [55353, 24], [55420, 71], [55597, 40], [55653, 24], [55720, 71], [55897, 40], [55953, 24], [56019, 73], [56197, 39], [56253, 23], [56319, 73], [56497, 39], [56553, 23], [56618, 74], [56797, 39], [56854, 22], [56918, 75], [57097, 39], [57154, 22], [57217, 76], [57396, 40], [57454, 22], [57517, 77], [57696, 40], [57754, 23], [57817, 77], [57996, 41], [58053, 24], [58116, 79], [58296, 41], [58353, 24], [58416, 79], [58596, 41], [58652, 26], [58715, 81], [58895, 43], [58952, 26], [59015, 81], [59195, 43], [59251, 27], [59315, 82], [59495, 46], [59549, 30], [59614, 83], [59795, 49], [59846, 34], [59914, 84], [60095, 86], [60213, 85], [60395, 87], [60512, 87], [60694, 89], [60812, 87], [60994, 90], [61111, 89], [61294, 92], [61409, 91], [61594, 93], [61708, 92], [61894, 95], [62006, 94], [62193, 100], [62302, 98], [62493, 207], [62793, 207], [63093, 207], [63393, 207], [63693, 207], [63992, 208], [64292, 208], [64592, 208]], "point": [195, 141]}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-00.92|+00.99|+02.27|BreadSliced_4"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [112, 138, 154, 180], "mask": [[41221, 6], [41520, 10], [41819, 13], [42119, 15], [42418, 18], [42718, 19], [43017, 21], [43317, 22], [43616, 26], [43916, 28], [44216, 29], [44516, 30], [44816, 31], [45116, 32], [45416, 33], [45716, 33], [46017, 33], [46317, 33], [46617, 33], [46917, 33], [47217, 33], [47517, 33], [47817, 34], [48117, 35], [48418, 34], [48712, 1], [48718, 35], [49018, 35], [49318, 35], [49613, 1], [49618, 25], [49646, 8], [49918, 23], [49948, 6], [50219, 21], [50249, 5], [50519, 20], [50550, 4], [50819, 20], [50850, 4], [51119, 20], [51149, 6], [51419, 21], [51449, 6], [51720, 19], [51750, 5], [52021, 18], [52051, 4], [52323, 15], [52351, 4], [52624, 14], [52651, 4], [52927, 11], [52952, 1], [53230, 8], [53532, 5], [53835, 2]], "point": [133, 158]}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-00.92|+00.99|+02.27|BreadSliced_4", "placeStationary": true, "receptacleObjectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 28], [7829, 229], [8100, 13], [8116, 242], [8400, 11], [8427, 231], [8700, 9], [8732, 226], [9000, 7], [9037, 221], [9300, 6], [9340, 218], [9600, 5], [9643, 215], [9900, 5], [9945, 212], [10200, 5], [10246, 211], [10500, 4], [10547, 210], [10800, 4], [10848, 209], [11100, 3], [11149, 208], [11400, 3], [11450, 207], [11700, 3], [11750, 207], [12000, 3], [12051, 206], [12300, 3], [12351, 206], [12600, 3], [12651, 206], [12900, 3], [12951, 205], [13200, 3], [13252, 204], [13500, 4], [13552, 204], [13800, 4], [13852, 204], [14100, 4], [14152, 204], [14400, 4], [14452, 204], [14700, 4], [14752, 204], [15000, 5], [15052, 31], [15088, 168], [15300, 5], [15352, 30], [15390, 166], [15600, 5], [15652, 28], [15691, 165], [15900, 5], [15951, 29], [15992, 163], [16200, 6], [16251, 28], [16293, 162], [16500, 6], [16551, 27], [16594, 161], [16800, 7], [16850, 28], [16894, 161], [17100, 7], [17150, 27], [17195, 160], [17400, 8], [17450, 27], [17495, 160], [17700, 8], [17750, 27], [17796, 159], [18000, 9], [18049, 28], [18096, 159], [18300, 9], [18349, 27], [18396, 159], [18600, 10], [18648, 28], [18697, 158], [18900, 10], [18948, 28], [18997, 157], [19200, 11], [19247, 29], [19297, 157], [19500, 11], [19547, 29], [19597, 157], [19800, 12], [19846, 30], [19898, 156], [20100, 13], [20146, 30], [20198, 156], [20400, 14], [20445, 31], [20498, 156], [20700, 15], [20745, 31], [20798, 156], [21000, 17], [21044, 32], [21098, 156], [21300, 18], [21343, 33], [21398, 156], [21600, 19], [21641, 35], [21698, 156], [21900, 20], [21940, 37], [21998, 155], [22200, 25], [22239, 38], [22298, 155], [22500, 77], [22597, 156], [22800, 78], [22897, 156], [23100, 78], [23197, 156], [23400, 79], [23496, 157], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 63], [103, 2], [113, 148], [300, 63], [414, 147], [600, 63], [716, 145], [900, 63], [1019, 141], [1200, 63], [1319, 141], [1500, 64], [1620, 140], [1800, 64], [1921, 139], [2100, 64], [2221, 139], [2400, 64], [2522, 138], [2700, 64], [2823, 137], [3000, 64], [3123, 137], [3300, 64], [3424, 136], [3600, 64], [3724, 136], [3900, 64], [4025, 134], [4200, 64], [4325, 134], [4500, 64], [4625, 134], [4800, 64], [4925, 134], [5100, 64], [5225, 134], [5400, 64], [5525, 134], [5700, 64], [5825, 134], [6000, 64], [6125, 134], [6300, 64], [6425, 134], [6600, 65], [6725, 134], [6900, 65], [7025, 133], [7200, 65], [7326, 132], [7500, 65], [7626, 132], [7800, 28], [7829, 36], [7927, 131], [8100, 13], [8116, 49], [8227, 131], [8400, 11], [8427, 38], [8528, 130], [8700, 9], [8732, 33], [8828, 130], [9000, 7], [9037, 28], [9129, 129], [9300, 6], [9340, 25], [9429, 129], [9600, 5], [9643, 22], [9729, 129], [9900, 5], [9945, 21], [10029, 128], [10200, 5], [10246, 20], [10329, 128], [10500, 4], [10547, 19], [10630, 127], [10800, 4], [10848, 18], [10930, 127], [11100, 3], [11149, 17], [11230, 127], [11400, 3], [11450, 16], [11530, 127], [11700, 3], [11750, 16], [11830, 127], [12000, 3], [12051, 15], [12130, 127], [12300, 3], [12351, 15], [12430, 127], [12600, 3], [12651, 15], [12729, 128], [12900, 3], [12951, 15], [13029, 127], [13200, 3], [13252, 14], [13329, 127], [13500, 4], [13552, 14], [13629, 127], [13800, 4], [13852, 15], [13929, 127], [14100, 4], [14152, 15], [14229, 127], [14400, 4], [14452, 15], [14529, 127], [14700, 4], [14752, 15], [14829, 127], [15000, 5], [15052, 15], [15128, 128], [15300, 5], [15352, 15], [15428, 128], [15600, 5], [15652, 15], [15728, 128], [15900, 5], [15951, 16], [16028, 127], [16200, 6], [16251, 16], [16327, 128], [16500, 6], [16551, 16], [16627, 128], [16800, 7], [16850, 17], [16926, 129], [17100, 7], [17150, 17], [17226, 129], [17400, 8], [17450, 18], [17525, 130], [17700, 8], [17750, 18], [17825, 130], [18000, 9], [18049, 19], [18124, 131], [18300, 9], [18349, 19], [18423, 132], [18600, 10], [18648, 20], [18722, 133], [18900, 10], [18948, 20], [19021, 133], [19200, 11], [19247, 21], [19319, 135], [19500, 11], [19547, 21], [19617, 137], [19800, 12], [19846, 23], [19915, 139], [20100, 13], [20146, 23], [20212, 142], [20400, 14], [20445, 24], [20506, 148], [20700, 15], [20745, 24], [20802, 152], [21000, 17], [21044, 25], [21101, 153], [21300, 18], [21343, 26], [21400, 154], [21600, 19], [21641, 29], [21698, 156], [21900, 20], [21940, 31], [21998, 155], [22200, 25], [22239, 33], [22298, 155], [22500, 73], [22597, 156], [22800, 78], [22897, 156], [23100, 78], [23197, 156], [23400, 79], [23496, 157], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 7}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 7}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 7}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 7}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-00.92|+00.99|+02.27|BreadSliced_4"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [63, 1, 129, 76], "mask": [[63, 40], [105, 8], [363, 51], [663, 53], [963, 56], [1263, 56], [1564, 56], [1864, 57], [2164, 57], [2464, 58], [2764, 59], [3064, 59], [3364, 60], [3664, 60], [3964, 61], [4264, 61], [4564, 61], [4864, 61], [5164, 61], [5464, 61], [5764, 61], [6064, 61], [6364, 61], [6665, 60], [6965, 60], [7265, 61], [7565, 61], [7865, 62], [8165, 62], [8465, 63], [8765, 63], [9065, 64], [9365, 64], [9665, 64], [9966, 63], [10266, 63], [10566, 64], [10866, 64], [11166, 64], [11466, 64], [11766, 64], [12066, 64], [12366, 64], [12666, 63], [12966, 63], [13266, 63], [13566, 63], [13867, 62], [14167, 62], [14467, 62], [14767, 62], [15067, 61], [15367, 61], [15667, 61], [15967, 61], [16267, 60], [16567, 60], [16867, 59], [17167, 59], [17468, 57], [17768, 57], [18068, 56], [18368, 55], [18668, 54], [18968, 53], [19268, 51], [19568, 49], [19869, 46], [20169, 43], [20469, 37], [20769, 33], [21069, 32], [21369, 31], [21670, 28], [21971, 23], [22272, 18], [22573, 11]], "point": [96, 37]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 28], [7829, 229], [8100, 13], [8116, 242], [8400, 11], [8427, 231], [8700, 9], [8732, 226], [9000, 7], [9037, 221], [9300, 6], [9340, 218], [9600, 5], [9643, 215], [9900, 5], [9945, 212], [10200, 5], [10246, 211], [10500, 4], [10547, 210], [10800, 4], [10848, 209], [11100, 3], [11149, 208], [11400, 3], [11450, 207], [11700, 3], [11750, 207], [12000, 3], [12051, 206], [12300, 3], [12351, 206], [12600, 3], [12651, 206], [12900, 3], [12951, 205], [13200, 3], [13252, 204], [13500, 4], [13552, 204], [13800, 4], [13852, 204], [14100, 4], [14152, 204], [14400, 4], [14452, 204], [14700, 4], [14752, 204], [15000, 5], [15052, 31], [15088, 168], [15300, 5], [15352, 30], [15390, 166], [15600, 5], [15652, 28], [15691, 165], [15900, 5], [15951, 29], [15992, 163], [16200, 6], [16251, 28], [16293, 162], [16500, 6], [16551, 27], [16594, 161], [16800, 7], [16850, 28], [16894, 161], [17100, 7], [17150, 27], [17195, 160], [17400, 8], [17450, 27], [17495, 160], [17700, 8], [17750, 27], [17796, 159], [18000, 9], [18049, 28], [18096, 159], [18300, 9], [18349, 27], [18396, 159], [18600, 10], [18648, 28], [18697, 158], [18900, 10], [18948, 28], [18997, 157], [19200, 11], [19247, 29], [19297, 157], [19500, 11], [19547, 29], [19597, 157], [19800, 12], [19846, 30], [19898, 156], [20100, 13], [20146, 30], [20198, 156], [20400, 14], [20445, 31], [20498, 156], [20700, 15], [20745, 31], [20798, 156], [21000, 17], [21044, 32], [21098, 156], [21300, 18], [21343, 33], [21398, 156], [21600, 19], [21641, 35], [21698, 156], [21900, 20], [21940, 37], [21998, 155], [22200, 25], [22239, 38], [22298, 155], [22500, 77], [22597, 156], [22800, 78], [22897, 156], [23100, 78], [23197, 156], [23400, 79], [23496, 157], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 7}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-00.92|+00.99|+02.27|BreadSliced_4", "placeStationary": true, "receptacleObjectId": "GarbageCan|+01.45|+00.00|+02.91"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [64, 137, 150, 241], "mask": [[40886, 55], [41182, 62], [41479, 69], [41778, 70], [42077, 72], [42376, 74], [42675, 75], [42975, 76], [43275, 76], [43575, 76], [43874, 77], [44174, 77], [44474, 77], [44774, 77], [45074, 77], [45374, 77], [45673, 78], [45973, 78], [46273, 78], [46573, 78], [46873, 78], [47173, 78], [47472, 79], [47772, 79], [48072, 79], [48372, 79], [48672, 79], [48971, 80], [49271, 80], [49571, 80], [49871, 80], [50171, 80], [50471, 80], [50770, 81], [51070, 81], [51370, 81], [51670, 81], [51970, 81], [52270, 81], [52569, 82], [52869, 82], [53169, 82], [53469, 82], [53769, 82], [54069, 82], [54368, 83], [54668, 83], [54968, 83], [55268, 83], [55568, 83], [55868, 83], [56167, 84], [56467, 84], [56767, 84], [57067, 84], [57367, 84], [57667, 84], [57966, 85], [58266, 85], [58566, 85], [58866, 85], [59166, 85], [59466, 85], [59765, 86], [60065, 86], [60365, 86], [60665, 86], [60965, 86], [61265, 86], [61564, 87], [61864, 87], [62164, 87], [62464, 87], [62764, 87], [63064, 87], [63365, 85], [63665, 85], [63965, 84], [64266, 69], [64566, 63], [64868, 58], [65170, 54], [65473, 49], [65775, 45], [66077, 42], [66379, 39], [66682, 35], [66985, 30], [67287, 28], [67589, 25], [67891, 23], [68193, 20], [68494, 19], [68795, 17], [69095, 17], [69396, 15], [69697, 14], [69998, 13], [70298, 13], [70599, 12], [70900, 11], [71200, 10], [71501, 9], [71801, 9], [72104, 6]], "point": [107, 188]}}, "high_idx": 9}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan12", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 0.25, "y": 0.9009999, "z": 2.5}, "object_poses": [{"objectName": "Pan_9f7abbea", "position": {"x": -1.109, "y": 0.9307289, "z": 1.4704}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": -1.12137806, "y": 1.50067186, "z": 2.27596354}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": 1.39171886, "y": 0.08180314, "z": 2.39202}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": -1.12628, "y": 0.9379421, "z": 0.8433119}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": 1.597631, "y": 0.9458269, "z": 1.75674188}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": -0.689003944, "y": 0.7479664, "z": 0.779062569}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_733b0828", "position": {"x": -0.876905, "y": 0.9528999, "z": 1.0763762}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": 1.65219235, "y": 1.62143028, "z": 1.65140128}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": -1.211005, "y": 0.9364782, "z": 2.310654}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": 1.38253057, "y": 0.105463147, "z": 0.0706810355}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -0.9376838, "y": 0.6270374, "z": 0.348954}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -0.971269131, "y": 1.56053746, "z": 0.195651278}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": 1.38017225, "y": 1.74313784, "z": 0.0663864}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": -0.971268654, "y": 1.31560028, "z": 0.04234863}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": -1.0011425, "y": 1.497415, "z": 2.5538}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": -0.712255, "y": 0.9342062, "z": 2.234908}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": -0.8771876, "y": 1.49431944, "z": -0.03430243}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": 1.36229229, "y": 0.932899952, "z": 1.00726593}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_f3700d17", "position": {"x": 1.45669448, "y": 1.01416636, "z": 1.68157053}, "rotation": {"x": 3.86250019, "y": 59.981308, "z": 359.927338}}, {"objectName": "SoapBottle_09f78d94", "position": {"x": -0.712255, "y": 0.937205851, "z": 2.3864}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_20fd7fed", "position": {"x": -0.9230002, "y": 0.990676045, "z": 2.26549983}, "rotation": {"x": 3.20195068e-05, "y": 302.4703, "z": 7.218442e-06}}, {"objectName": "Fork_d40bfead", "position": {"x": 1.31562316, "y": 0.945246041, "z": 1.756742}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -0.96003, "y": 0.999118, "z": 1.0763762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": 1.70056438, "y": 1.9449389, "z": 1.87766373}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -1.109, "y": 0.9307289, "z": 1.8657}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_733b0828", "position": {"x": -0.7582013, "y": 0.7618369, "z": 0.914999962}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": -0.8769051, "y": 0.9598927, "z": 0.765623748}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": -1.12788, "y": 0.9342062, "z": 2.234908}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": 1.68450356, "y": 1.6457032, "z": 1.80861282}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": 1.71018851, "y": 1.49741375, "z": 2.58550858}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": -0.793779969, "y": 0.9379421, "z": 1.15406442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_22312ae0", "position": {"x": -1.12628, "y": 0.9705572, "z": 1.15406442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": 1.39171886, "y": 0.0867086649, "z": 0.848087549}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": -1.12628, "y": 0.937464237, "z": 0.9986881}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": 1.59763086, "y": 0.9798608, "z": 1.63136768}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": -1.043155, "y": 0.9364782, "z": 0.8433119}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_9f7abbea", "position": {"x": 1.3781, "y": 1.73753989, "z": 0.3929}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": 1.51607227, "y": 0.939029336, "z": 0.8063456}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": -0.964836538, "y": 0.8101194, "z": 0.118999973}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}], "object_toggles": [], "random_seed": 3568421692, "scene_num": 12}, "task_id": "trial_T20190907_120947_947901", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A17TKHT8FEVH0R_30BUDKLTXGCN50T7TIBELBVKPWYE5S", "high_descs": ["Head to the left of the oven", "Grab the knife off of the counter", "Turn right and head to the counter to the right of the oven", "Slice the loaf of bread into slices", "Place the knife on the counter", "Grab a slice of bread", "Turn around and head to the microwave", "Put the bread in the microwave and cook it. Grab the bread and close the microwave.", "Turn left and stop at the trash can", "Throw the bread in the trash can"], "task_desc": "Putting bread in a trash can", "votes": [1, 1, 1]}, {"assignment_id": "A3A0VPWPASCO9J_3YMTUJH0DVX7Y07458HZF5P7SCYT44", "high_descs": ["Go to the counter next to the stove.", "Pick up the butter knife next to the stove.", "Go to the counter on the other side of the stove.", "Slice the bread next to the toaster.", "Set the butter knife down on the counter next to the stove.", "Pick up a piece of bread.", "Take the bread to the microwave.", "Heat the bread in the microwave.", "Take the warm bread to the trash can at the end of the counter.", "Throw the warm bread in the trash can."], "task_desc": "Throw a warm piece of bread in the trash can.", "votes": [1, 1, 1]}, {"assignment_id": "A2CT57W84KXX25_3HMIGG0U4ON4YNT0RHUB4YXR90TY8M", "high_descs": ["Walk straight turn to the right", "Walk over to the counter", "Pick the knife up from the counter", "Back up and turn walk straight to the other counter", "Take the knife and use it to cut a piece of bread", "Pick the bread up and walk over to the microwave", "Place the bread in the microwave", "Turn the microwave on to heat the bread up", "Take the bread out of the microwave ", "Carry the bread to other side of the kitchen"], "task_desc": "Cut a piece of bread and warm it up to get ready to eat", "votes": [1, 0, 1]}]}}