
(define (problem plan_trial_T20190907_182257_096741)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Box_bar__minus_01_dot_73_bar__plus_00_dot_87_bar__plus_01_dot_55 - object
        Box_bar__minus_01_dot_87_bar__plus_00_dot_87_bar__plus_01_dot_13 - object
        Chair_bar__minus_01_dot_08_bar__plus_00_dot_00_bar__plus_01_dot_29 - object
        Chair_bar__minus_01_dot_55_bar__plus_00_dot_00_bar__plus_01_dot_78 - object
        Chair_bar__minus_01_dot_63_bar__plus_00_dot_00_bar__plus_00_dot_78 - object
        Chair_bar__minus_02_dot_15_bar__plus_00_dot_00_bar__plus_01_dot_29 - object
        CreditCard_bar__minus_01_dot_59_bar__plus_00_dot_76_bar__plus_00_dot_99 - object
        Curtains_bar__minus_00_dot_04_bar__plus_02_dot_15_bar__plus_01_dot_00 - object
        DeskLamp_bar__minus_00_dot_26_bar__plus_00_dot_62_bar__plus_03_dot_31 - object
        FloorLamp_bar__minus_04_dot_72_bar__plus_00_dot_00_bar__plus_03_dot_69 - object
        HousePlant_bar__minus_00_dot_17_bar__plus_00_dot_62_bar__plus_03_dot_99 - object
        KeyChain_bar__minus_04_dot_08_bar__plus_00_dot_48_bar__plus_03_dot_59 - object
        KeyChain_bar__minus_04_dot_19_bar__plus_00_dot_48_bar__plus_03_dot_59 - object
        KeyChain_bar__minus_04_dot_38_bar__plus_00_dot_48_bar__plus_04_dot_86 - object
        Laptop_bar__minus_03_dot_54_bar__plus_00_dot_40_bar__plus_03_dot_69 - object
        Laptop_bar__minus_04_dot_38_bar__plus_00_dot_48_bar__plus_05_dot_79 - object
        LightSwitch_bar__minus_03_dot_48_bar__plus_01_dot_47_bar__plus_00_dot_00 - object
        Painting_bar__minus_00_dot_02_bar__plus_02_dot_06_bar__plus_05_dot_38 - object
        Pillow_bar__minus_02_dot_40_bar__plus_00_dot_53_bar__plus_03_dot_63 - object
        Pillow_bar__minus_04_dot_34_bar__plus_00_dot_47_bar__plus_04_dot_40 - object
        RemoteControl_bar__minus_01_dot_17_bar__plus_00_dot_76_bar__plus_00_dot_85 - object
        RemoteControl_bar__minus_01_dot_17_bar__plus_00_dot_76_bar__plus_01_dot_41 - object
        RemoteControl_bar__minus_01_dot_17_bar__plus_00_dot_76_bar__plus_01_dot_70 - object
        Statue_bar__minus_02_dot_01_bar__plus_00_dot_85_bar__plus_06_dot_77 - object
        Television_bar__minus_03_dot_19_bar__plus_01_dot_18_bar__plus_06_dot_78 - object
        Watch_bar__minus_00_dot_30_bar__plus_00_dot_63_bar__plus_03_dot_96 - object
        Watch_bar__minus_00_dot_46_bar__plus_00_dot_63_bar__plus_03_dot_39 - object
        Watch_bar__minus_04_dot_19_bar__plus_00_dot_48_bar__plus_03_dot_47 - object
        WateringCan_bar__minus_00_dot_21_bar_00_dot_00_bar__plus_03_dot_23 - object
        Window_bar__minus_00_dot_01_bar__plus_01_dot_34_bar__plus_01_dot_73 - object
        ArmChair_bar__minus_02_dot_45_bar__plus_00_dot_01_bar__plus_03_dot_50 - receptacle
        ArmChair_bar__minus_03_dot_55_bar__plus_00_dot_00_bar__plus_03_dot_58 - receptacle
        DiningTable_bar__minus_01_dot_59_bar__plus_00_dot_31_bar__plus_01_dot_27 - receptacle
        Drawer_bar__minus_02_dot_93_bar__plus_00_dot_16_bar__plus_06_dot_71 - receptacle
        Drawer_bar__minus_02_dot_93_bar__plus_00_dot_47_bar__plus_06_dot_71 - receptacle
        Drawer_bar__minus_03_dot_48_bar__plus_00_dot_16_bar__plus_06_dot_71 - receptacle
        Drawer_bar__minus_03_dot_48_bar__plus_00_dot_47_bar__plus_06_dot_71 - receptacle
        Dresser_bar__minus_03_dot_20_bar__minus_00_dot_01_bar__plus_06_dot_79 - receptacle
        GarbageCan_bar__minus_00_dot_26_bar__plus_00_dot_00_bar__plus_06_dot_80 - receptacle
        Ottoman_bar__minus_02_dot_43_bar__plus_00_dot_03_bar__plus_04_dot_41 - receptacle
        SideTable_bar__minus_00_dot_24_bar__plus_00_dot_00_bar__plus_03_dot_52 - receptacle
        SideTable_bar__minus_02_dot_07_bar_00_dot_00_bar__plus_06_dot_70 - receptacle
        SideTable_bar__minus_04_dot_19_bar_00_dot_00_bar__plus_03_dot_59 - receptacle
        Sofa_bar__minus_04_dot_43_bar__plus_00_dot_01_bar__plus_05_dot_06 - receptacle
        loc_bar__minus_15_bar_18_bar_2_bar_60 - location
        loc_bar__minus_12_bar_23_bar_0_bar_45 - location
        loc_bar__minus_18_bar_12_bar_0_bar_60 - location
        loc_bar__minus_13_bar_24_bar_0_bar_60 - location
        loc_bar__minus_7_bar_24_bar_0_bar_60 - location
        loc_bar__minus_10_bar_23_bar_0_bar_45 - location
        loc_bar__minus_11_bar_5_bar_1_bar_60 - location
        loc_bar__minus_3_bar_26_bar_1_bar_60 - location
        loc_bar__minus_6_bar_9_bar_2_bar_60 - location
        loc_bar__minus_4_bar_14_bar_1_bar_60 - location
        loc_bar__minus_13_bar_21_bar_3_bar_60 - location
        loc_bar__minus_9_bar_2_bar_1_bar_60 - location
        loc_bar__minus_3_bar_3_bar_1_bar__minus_30 - location
        loc_bar__minus_10_bar_20_bar_2_bar_60 - location
        loc_bar__minus_2_bar_6_bar_3_bar_60 - location
        loc_bar__minus_12_bar_22_bar_0_bar_45 - location
        loc_bar__minus_14_bar_2_bar_2_bar_15 - location
        loc_bar__minus_14_bar_22_bar_0_bar_45 - location
        loc_bar__minus_15_bar_11_bar_0_bar_60 - location
        loc_bar__minus_3_bar_22_bar_1_bar__minus_30 - location
        loc_bar__minus_2_bar_7_bar_1_bar_30 - location
        loc_bar__minus_3_bar_13_bar_1_bar_60 - location
        loc_bar__minus_10_bar_10_bar_0_bar_60 - location
        loc_bar__minus_11_bar_6_bar_0_bar_30 - location
        )
    

(:init


        (receptacleType GarbageCan_bar__minus_00_dot_26_bar__plus_00_dot_00_bar__plus_06_dot_80 GarbageCanType)
        (receptacleType SideTable_bar__minus_02_dot_07_bar_00_dot_00_bar__plus_06_dot_70 SideTableType)
        (receptacleType Ottoman_bar__minus_02_dot_43_bar__plus_00_dot_03_bar__plus_04_dot_41 OttomanType)
        (receptacleType Sofa_bar__minus_04_dot_43_bar__plus_00_dot_01_bar__plus_05_dot_06 SofaType)
        (receptacleType ArmChair_bar__minus_03_dot_55_bar__plus_00_dot_00_bar__plus_03_dot_58 ArmChairType)
        (receptacleType Drawer_bar__minus_03_dot_48_bar__plus_00_dot_16_bar__plus_06_dot_71 DrawerType)
        (receptacleType Drawer_bar__minus_03_dot_48_bar__plus_00_dot_47_bar__plus_06_dot_71 DrawerType)
        (receptacleType Drawer_bar__minus_02_dot_93_bar__plus_00_dot_47_bar__plus_06_dot_71 DrawerType)
        (receptacleType ArmChair_bar__minus_02_dot_45_bar__plus_00_dot_01_bar__plus_03_dot_50 ArmChairType)
        (receptacleType Drawer_bar__minus_02_dot_93_bar__plus_00_dot_16_bar__plus_06_dot_71 DrawerType)
        (receptacleType SideTable_bar__minus_04_dot_19_bar_00_dot_00_bar__plus_03_dot_59 SideTableType)
        (receptacleType SideTable_bar__minus_00_dot_24_bar__plus_00_dot_00_bar__plus_03_dot_52 SideTableType)
        (receptacleType Dresser_bar__minus_03_dot_20_bar__minus_00_dot_01_bar__plus_06_dot_79 DresserType)
        (receptacleType DiningTable_bar__minus_01_dot_59_bar__plus_00_dot_31_bar__plus_01_dot_27 DiningTableType)
        (objectType Chair_bar__minus_01_dot_63_bar__plus_00_dot_00_bar__plus_00_dot_78 ChairType)
        (objectType Watch_bar__minus_00_dot_46_bar__plus_00_dot_63_bar__plus_03_dot_39 WatchType)
        (objectType FloorLamp_bar__minus_04_dot_72_bar__plus_00_dot_00_bar__plus_03_dot_69 FloorLampType)
        (objectType Box_bar__minus_01_dot_73_bar__plus_00_dot_87_bar__plus_01_dot_55 BoxType)
        (objectType Chair_bar__minus_01_dot_55_bar__plus_00_dot_00_bar__plus_01_dot_78 ChairType)
        (objectType CreditCard_bar__minus_01_dot_59_bar__plus_00_dot_76_bar__plus_00_dot_99 CreditCardType)
        (objectType Chair_bar__minus_01_dot_08_bar__plus_00_dot_00_bar__plus_01_dot_29 ChairType)
        (objectType KeyChain_bar__minus_04_dot_08_bar__plus_00_dot_48_bar__plus_03_dot_59 KeyChainType)
        (objectType Pillow_bar__minus_04_dot_34_bar__plus_00_dot_47_bar__plus_04_dot_40 PillowType)
        (objectType RemoteControl_bar__minus_01_dot_17_bar__plus_00_dot_76_bar__plus_00_dot_85 RemoteControlType)
        (objectType LightSwitch_bar__minus_03_dot_48_bar__plus_01_dot_47_bar__plus_00_dot_00 LightSwitchType)
        (objectType Laptop_bar__minus_04_dot_38_bar__plus_00_dot_48_bar__plus_05_dot_79 LaptopType)
        (objectType RemoteControl_bar__minus_01_dot_17_bar__plus_00_dot_76_bar__plus_01_dot_70 RemoteControlType)
        (objectType Chair_bar__minus_02_dot_15_bar__plus_00_dot_00_bar__plus_01_dot_29 ChairType)
        (objectType Television_bar__minus_03_dot_19_bar__plus_01_dot_18_bar__plus_06_dot_78 TelevisionType)
        (objectType Window_bar__minus_00_dot_01_bar__plus_01_dot_34_bar__plus_01_dot_73 WindowType)
        (objectType Watch_bar__minus_04_dot_19_bar__plus_00_dot_48_bar__plus_03_dot_47 WatchType)
        (objectType WateringCan_bar__minus_00_dot_21_bar_00_dot_00_bar__plus_03_dot_23 WateringCanType)
        (objectType Watch_bar__minus_00_dot_30_bar__plus_00_dot_63_bar__plus_03_dot_96 WatchType)
        (objectType Statue_bar__minus_02_dot_01_bar__plus_00_dot_85_bar__plus_06_dot_77 StatueType)
        (objectType Box_bar__minus_01_dot_87_bar__plus_00_dot_87_bar__plus_01_dot_13 BoxType)
        (objectType DeskLamp_bar__minus_00_dot_26_bar__plus_00_dot_62_bar__plus_03_dot_31 DeskLampType)
        (objectType HousePlant_bar__minus_00_dot_17_bar__plus_00_dot_62_bar__plus_03_dot_99 HousePlantType)
        (objectType Laptop_bar__minus_03_dot_54_bar__plus_00_dot_40_bar__plus_03_dot_69 LaptopType)
        (objectType KeyChain_bar__minus_04_dot_19_bar__plus_00_dot_48_bar__plus_03_dot_59 KeyChainType)
        (objectType KeyChain_bar__minus_04_dot_38_bar__plus_00_dot_48_bar__plus_04_dot_86 KeyChainType)
        (objectType Pillow_bar__minus_02_dot_40_bar__plus_00_dot_53_bar__plus_03_dot_63 PillowType)
        (objectType Curtains_bar__minus_00_dot_04_bar__plus_02_dot_15_bar__plus_01_dot_00 CurtainsType)
        (objectType Painting_bar__minus_00_dot_02_bar__plus_02_dot_06_bar__plus_05_dot_38 PaintingType)
        (objectType RemoteControl_bar__minus_01_dot_17_bar__plus_00_dot_76_bar__plus_01_dot_41 RemoteControlType)
        (canContain SideTableType WatchType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain SideTableType WateringCanType)
        (canContain OttomanType BoxType)
        (canContain OttomanType LaptopType)
        (canContain OttomanType PillowType)
        (canContain OttomanType RemoteControlType)
        (canContain OttomanType KeyChainType)
        (canContain OttomanType CreditCardType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType CreditCardType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (canContain DrawerType WatchType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType WatchType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType WatchType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (canContain DrawerType WatchType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain SideTableType WatchType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain SideTableType WateringCanType)
        (canContain SideTableType WatchType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain SideTableType WateringCanType)
        (canContain DresserType WatchType)
        (canContain DresserType BoxType)
        (canContain DresserType KeyChainType)
        (canContain DresserType CreditCardType)
        (canContain DresserType LaptopType)
        (canContain DresserType RemoteControlType)
        (canContain DresserType StatueType)
        (canContain DresserType WateringCanType)
        (canContain DiningTableType WatchType)
        (canContain DiningTableType BoxType)
        (canContain DiningTableType KeyChainType)
        (canContain DiningTableType CreditCardType)
        (canContain DiningTableType LaptopType)
        (canContain DiningTableType RemoteControlType)
        (canContain DiningTableType StatueType)
        (canContain DiningTableType WateringCanType)
        (pickupable Watch_bar__minus_00_dot_46_bar__plus_00_dot_63_bar__plus_03_dot_39)
        (pickupable Box_bar__minus_01_dot_73_bar__plus_00_dot_87_bar__plus_01_dot_55)
        (pickupable CreditCard_bar__minus_01_dot_59_bar__plus_00_dot_76_bar__plus_00_dot_99)
        (pickupable KeyChain_bar__minus_04_dot_08_bar__plus_00_dot_48_bar__plus_03_dot_59)
        (pickupable Pillow_bar__minus_04_dot_34_bar__plus_00_dot_47_bar__plus_04_dot_40)
        (pickupable RemoteControl_bar__minus_01_dot_17_bar__plus_00_dot_76_bar__plus_00_dot_85)
        (pickupable Laptop_bar__minus_04_dot_38_bar__plus_00_dot_48_bar__plus_05_dot_79)
        (pickupable RemoteControl_bar__minus_01_dot_17_bar__plus_00_dot_76_bar__plus_01_dot_70)
        (pickupable Watch_bar__minus_04_dot_19_bar__plus_00_dot_48_bar__plus_03_dot_47)
        (pickupable WateringCan_bar__minus_00_dot_21_bar_00_dot_00_bar__plus_03_dot_23)
        (pickupable Watch_bar__minus_00_dot_30_bar__plus_00_dot_63_bar__plus_03_dot_96)
        (pickupable Statue_bar__minus_02_dot_01_bar__plus_00_dot_85_bar__plus_06_dot_77)
        (pickupable Box_bar__minus_01_dot_87_bar__plus_00_dot_87_bar__plus_01_dot_13)
        (pickupable Laptop_bar__minus_03_dot_54_bar__plus_00_dot_40_bar__plus_03_dot_69)
        (pickupable KeyChain_bar__minus_04_dot_19_bar__plus_00_dot_48_bar__plus_03_dot_59)
        (pickupable KeyChain_bar__minus_04_dot_38_bar__plus_00_dot_48_bar__plus_04_dot_86)
        (pickupable Pillow_bar__minus_02_dot_40_bar__plus_00_dot_53_bar__plus_03_dot_63)
        (pickupable RemoteControl_bar__minus_01_dot_17_bar__plus_00_dot_76_bar__plus_01_dot_41)
        (isReceptacleObject Box_bar__minus_01_dot_73_bar__plus_00_dot_87_bar__plus_01_dot_55)
        (isReceptacleObject Box_bar__minus_01_dot_87_bar__plus_00_dot_87_bar__plus_01_dot_13)
        (openable Drawer_bar__minus_03_dot_48_bar__plus_00_dot_16_bar__plus_06_dot_71)
        (openable Drawer_bar__minus_03_dot_48_bar__plus_00_dot_47_bar__plus_06_dot_71)
        (openable Drawer_bar__minus_02_dot_93_bar__plus_00_dot_47_bar__plus_06_dot_71)
        (openable Drawer_bar__minus_02_dot_93_bar__plus_00_dot_16_bar__plus_06_dot_71)
        
        (atLocation agent1 loc_bar__minus_11_bar_6_bar_0_bar_30)
        
        
        
        
        
        
        
        (toggleable FloorLamp_bar__minus_04_dot_72_bar__plus_00_dot_00_bar__plus_03_dot_69)
        (toggleable DeskLamp_bar__minus_00_dot_26_bar__plus_00_dot_62_bar__plus_03_dot_31)
        
        
        
        
        (inReceptacle Laptop_bar__minus_03_dot_54_bar__plus_00_dot_40_bar__plus_03_dot_69 ArmChair_bar__minus_03_dot_55_bar__plus_00_dot_00_bar__plus_03_dot_58)
        (inReceptacle RemoteControl_bar__minus_01_dot_17_bar__plus_00_dot_76_bar__plus_01_dot_70 DiningTable_bar__minus_01_dot_59_bar__plus_00_dot_31_bar__plus_01_dot_27)
        (inReceptacle Box_bar__minus_01_dot_87_bar__plus_00_dot_87_bar__plus_01_dot_13 DiningTable_bar__minus_01_dot_59_bar__plus_00_dot_31_bar__plus_01_dot_27)
        (inReceptacle Box_bar__minus_01_dot_73_bar__plus_00_dot_87_bar__plus_01_dot_55 DiningTable_bar__minus_01_dot_59_bar__plus_00_dot_31_bar__plus_01_dot_27)
        (inReceptacle CreditCard_bar__minus_01_dot_59_bar__plus_00_dot_76_bar__plus_00_dot_99 DiningTable_bar__minus_01_dot_59_bar__plus_00_dot_31_bar__plus_01_dot_27)
        (inReceptacle RemoteControl_bar__minus_01_dot_17_bar__plus_00_dot_76_bar__plus_01_dot_41 DiningTable_bar__minus_01_dot_59_bar__plus_00_dot_31_bar__plus_01_dot_27)
        (inReceptacle RemoteControl_bar__minus_01_dot_17_bar__plus_00_dot_76_bar__plus_00_dot_85 DiningTable_bar__minus_01_dot_59_bar__plus_00_dot_31_bar__plus_01_dot_27)
        (inReceptacle Statue_bar__minus_02_dot_01_bar__plus_00_dot_85_bar__plus_06_dot_77 SideTable_bar__minus_02_dot_07_bar_00_dot_00_bar__plus_06_dot_70)
        (inReceptacle DeskLamp_bar__minus_00_dot_26_bar__plus_00_dot_62_bar__plus_03_dot_31 SideTable_bar__minus_00_dot_24_bar__plus_00_dot_00_bar__plus_03_dot_52)
        (inReceptacle Watch_bar__minus_00_dot_46_bar__plus_00_dot_63_bar__plus_03_dot_39 SideTable_bar__minus_00_dot_24_bar__plus_00_dot_00_bar__plus_03_dot_52)
        (inReceptacle Watch_bar__minus_00_dot_30_bar__plus_00_dot_63_bar__plus_03_dot_96 SideTable_bar__minus_00_dot_24_bar__plus_00_dot_00_bar__plus_03_dot_52)
        (inReceptacle HousePlant_bar__minus_00_dot_17_bar__plus_00_dot_62_bar__plus_03_dot_99 SideTable_bar__minus_00_dot_24_bar__plus_00_dot_00_bar__plus_03_dot_52)
        (inReceptacle Television_bar__minus_03_dot_19_bar__plus_01_dot_18_bar__plus_06_dot_78 Dresser_bar__minus_03_dot_20_bar__minus_00_dot_01_bar__plus_06_dot_79)
        (inReceptacle Pillow_bar__minus_02_dot_40_bar__plus_00_dot_53_bar__plus_03_dot_63 ArmChair_bar__minus_02_dot_45_bar__plus_00_dot_01_bar__plus_03_dot_50)
        (inReceptacle Laptop_bar__minus_04_dot_38_bar__plus_00_dot_48_bar__plus_05_dot_79 Sofa_bar__minus_04_dot_43_bar__plus_00_dot_01_bar__plus_05_dot_06)
        (inReceptacle Pillow_bar__minus_04_dot_34_bar__plus_00_dot_47_bar__plus_04_dot_40 Sofa_bar__minus_04_dot_43_bar__plus_00_dot_01_bar__plus_05_dot_06)
        (inReceptacle KeyChain_bar__minus_04_dot_38_bar__plus_00_dot_48_bar__plus_04_dot_86 Sofa_bar__minus_04_dot_43_bar__plus_00_dot_01_bar__plus_05_dot_06)
        (inReceptacle KeyChain_bar__minus_04_dot_19_bar__plus_00_dot_48_bar__plus_03_dot_59 SideTable_bar__minus_04_dot_19_bar_00_dot_00_bar__plus_03_dot_59)
        (inReceptacle KeyChain_bar__minus_04_dot_08_bar__plus_00_dot_48_bar__plus_03_dot_59 SideTable_bar__minus_04_dot_19_bar_00_dot_00_bar__plus_03_dot_59)
        (inReceptacle Watch_bar__minus_04_dot_19_bar__plus_00_dot_48_bar__plus_03_dot_47 SideTable_bar__minus_04_dot_19_bar_00_dot_00_bar__plus_03_dot_59)
        
        
        (receptacleAtLocation ArmChair_bar__minus_02_dot_45_bar__plus_00_dot_01_bar__plus_03_dot_50 loc_bar__minus_10_bar_10_bar_0_bar_60)
        (receptacleAtLocation ArmChair_bar__minus_03_dot_55_bar__plus_00_dot_00_bar__plus_03_dot_58 loc_bar__minus_15_bar_18_bar_2_bar_60)
        (receptacleAtLocation DiningTable_bar__minus_01_dot_59_bar__plus_00_dot_31_bar__plus_01_dot_27 loc_bar__minus_6_bar_9_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__minus_02_dot_93_bar__plus_00_dot_16_bar__plus_06_dot_71 loc_bar__minus_14_bar_22_bar_0_bar_45)
        (receptacleAtLocation Drawer_bar__minus_02_dot_93_bar__plus_00_dot_47_bar__plus_06_dot_71 loc_bar__minus_10_bar_23_bar_0_bar_45)
        (receptacleAtLocation Drawer_bar__minus_03_dot_48_bar__plus_00_dot_16_bar__plus_06_dot_71 loc_bar__minus_12_bar_22_bar_0_bar_45)
        (receptacleAtLocation Drawer_bar__minus_03_dot_48_bar__plus_00_dot_47_bar__plus_06_dot_71 loc_bar__minus_12_bar_23_bar_0_bar_45)
        (receptacleAtLocation Dresser_bar__minus_03_dot_20_bar__minus_00_dot_01_bar__plus_06_dot_79 loc_bar__minus_13_bar_24_bar_0_bar_60)
        (receptacleAtLocation GarbageCan_bar__minus_00_dot_26_bar__plus_00_dot_00_bar__plus_06_dot_80 loc_bar__minus_3_bar_26_bar_1_bar_60)
        (receptacleAtLocation Ottoman_bar__minus_02_dot_43_bar__plus_00_dot_03_bar__plus_04_dot_41 loc_bar__minus_10_bar_20_bar_2_bar_60)
        (receptacleAtLocation SideTable_bar__minus_00_dot_24_bar__plus_00_dot_00_bar__plus_03_dot_52 loc_bar__minus_4_bar_14_bar_1_bar_60)
        (receptacleAtLocation SideTable_bar__minus_02_dot_07_bar_00_dot_00_bar__plus_06_dot_70 loc_bar__minus_7_bar_24_bar_0_bar_60)
        (receptacleAtLocation SideTable_bar__minus_04_dot_19_bar_00_dot_00_bar__plus_03_dot_59 loc_bar__minus_15_bar_11_bar_0_bar_60)
        (receptacleAtLocation Sofa_bar__minus_04_dot_43_bar__plus_00_dot_01_bar__plus_05_dot_06 loc_bar__minus_13_bar_21_bar_3_bar_60)
        (objectAtLocation RemoteControl_bar__minus_01_dot_17_bar__plus_00_dot_76_bar__plus_00_dot_85 loc_bar__minus_6_bar_9_bar_2_bar_60)
        (objectAtLocation Pillow_bar__minus_04_dot_34_bar__plus_00_dot_47_bar__plus_04_dot_40 loc_bar__minus_13_bar_21_bar_3_bar_60)
        (objectAtLocation KeyChain_bar__minus_04_dot_38_bar__plus_00_dot_48_bar__plus_04_dot_86 loc_bar__minus_13_bar_21_bar_3_bar_60)
        (objectAtLocation Laptop_bar__minus_04_dot_38_bar__plus_00_dot_48_bar__plus_05_dot_79 loc_bar__minus_13_bar_21_bar_3_bar_60)
        (objectAtLocation Watch_bar__minus_00_dot_30_bar__plus_00_dot_63_bar__plus_03_dot_96 loc_bar__minus_4_bar_14_bar_1_bar_60)
        (objectAtLocation Box_bar__minus_01_dot_73_bar__plus_00_dot_87_bar__plus_01_dot_55 loc_bar__minus_6_bar_9_bar_2_bar_60)
        (objectAtLocation RemoteControl_bar__minus_01_dot_17_bar__plus_00_dot_76_bar__plus_01_dot_41 loc_bar__minus_6_bar_9_bar_2_bar_60)
        (objectAtLocation KeyChain_bar__minus_04_dot_19_bar__plus_00_dot_48_bar__plus_03_dot_59 loc_bar__minus_15_bar_11_bar_0_bar_60)
        (objectAtLocation Watch_bar__minus_00_dot_46_bar__plus_00_dot_63_bar__plus_03_dot_39 loc_bar__minus_4_bar_14_bar_1_bar_60)
        (objectAtLocation Curtains_bar__minus_00_dot_04_bar__plus_02_dot_15_bar__plus_01_dot_00 loc_bar__minus_3_bar_3_bar_1_bar__minus_30)
        (objectAtLocation Box_bar__minus_01_dot_87_bar__plus_00_dot_87_bar__plus_01_dot_13 loc_bar__minus_6_bar_9_bar_2_bar_60)
        (objectAtLocation Chair_bar__minus_01_dot_63_bar__plus_00_dot_00_bar__plus_00_dot_78 loc_bar__minus_9_bar_2_bar_1_bar_60)
        (objectAtLocation Chair_bar__minus_02_dot_15_bar__plus_00_dot_00_bar__plus_01_dot_29 loc_bar__minus_11_bar_5_bar_1_bar_60)
        (objectAtLocation Painting_bar__minus_00_dot_02_bar__plus_02_dot_06_bar__plus_05_dot_38 loc_bar__minus_3_bar_22_bar_1_bar__minus_30)
        (objectAtLocation Chair_bar__minus_01_dot_55_bar__plus_00_dot_00_bar__plus_01_dot_78 loc_bar__minus_6_bar_9_bar_2_bar_60)
        (objectAtLocation Chair_bar__minus_01_dot_08_bar__plus_00_dot_00_bar__plus_01_dot_29 loc_bar__minus_2_bar_6_bar_3_bar_60)
        (objectAtLocation Statue_bar__minus_02_dot_01_bar__plus_00_dot_85_bar__plus_06_dot_77 loc_bar__minus_7_bar_24_bar_0_bar_60)
        (objectAtLocation Watch_bar__minus_04_dot_19_bar__plus_00_dot_48_bar__plus_03_dot_47 loc_bar__minus_15_bar_11_bar_0_bar_60)
        (objectAtLocation FloorLamp_bar__minus_04_dot_72_bar__plus_00_dot_00_bar__plus_03_dot_69 loc_bar__minus_18_bar_12_bar_0_bar_60)
        (objectAtLocation WateringCan_bar__minus_00_dot_21_bar_00_dot_00_bar__plus_03_dot_23 loc_bar__minus_3_bar_13_bar_1_bar_60)
        (objectAtLocation Laptop_bar__minus_03_dot_54_bar__plus_00_dot_40_bar__plus_03_dot_69 loc_bar__minus_15_bar_18_bar_2_bar_60)
        (objectAtLocation Television_bar__minus_03_dot_19_bar__plus_01_dot_18_bar__plus_06_dot_78 loc_bar__minus_13_bar_24_bar_0_bar_60)
        (objectAtLocation CreditCard_bar__minus_01_dot_59_bar__plus_00_dot_76_bar__plus_00_dot_99 loc_bar__minus_6_bar_9_bar_2_bar_60)
        (objectAtLocation KeyChain_bar__minus_04_dot_08_bar__plus_00_dot_48_bar__plus_03_dot_59 loc_bar__minus_15_bar_11_bar_0_bar_60)
        (objectAtLocation HousePlant_bar__minus_00_dot_17_bar__plus_00_dot_62_bar__plus_03_dot_99 loc_bar__minus_4_bar_14_bar_1_bar_60)
        (objectAtLocation LightSwitch_bar__minus_03_dot_48_bar__plus_01_dot_47_bar__plus_00_dot_00 loc_bar__minus_14_bar_2_bar_2_bar_15)
        (objectAtLocation Pillow_bar__minus_02_dot_40_bar__plus_00_dot_53_bar__plus_03_dot_63 loc_bar__minus_10_bar_10_bar_0_bar_60)
        (objectAtLocation RemoteControl_bar__minus_01_dot_17_bar__plus_00_dot_76_bar__plus_01_dot_70 loc_bar__minus_6_bar_9_bar_2_bar_60)
        (objectAtLocation DeskLamp_bar__minus_00_dot_26_bar__plus_00_dot_62_bar__plus_03_dot_31 loc_bar__minus_4_bar_14_bar_1_bar_60)
        (objectAtLocation Window_bar__minus_00_dot_01_bar__plus_01_dot_34_bar__plus_01_dot_73 loc_bar__minus_2_bar_7_bar_1_bar_30)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 WatchType)
                                    (receptacleType ?r DresserType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 WatchType)
                                            (receptacleType ?r DresserType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            