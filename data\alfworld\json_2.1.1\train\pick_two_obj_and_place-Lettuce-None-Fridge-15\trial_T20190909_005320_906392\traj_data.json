{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000205.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000206.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000207.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000208.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000209.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000210.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000211.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000212.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000262.png", "low_idx": 60}, {"high_idx": 7, "image_name": "000000263.png", "low_idx": 60}, {"high_idx": 7, "image_name": "000000264.png", "low_idx": 60}, {"high_idx": 7, "image_name": "000000265.png", "low_idx": 60}, {"high_idx": 7, "image_name": "000000266.png", "low_idx": 61}, {"high_idx": 7, "image_name": "000000267.png", "low_idx": 61}, {"high_idx": 7, "image_name": "000000268.png", "low_idx": 61}, {"high_idx": 7, "image_name": "000000269.png", "low_idx": 61}, {"high_idx": 7, "image_name": "000000270.png", "low_idx": 61}, {"high_idx": 7, "image_name": "000000271.png", "low_idx": 61}, {"high_idx": 7, "image_name": "000000272.png", "low_idx": 61}, {"high_idx": 7, "image_name": "000000273.png", "low_idx": 61}, {"high_idx": 7, "image_name": "000000274.png", "low_idx": 61}, {"high_idx": 7, "image_name": "000000275.png", "low_idx": 61}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 61}, {"high_idx": 7, "image_name": "000000277.png", "low_idx": 61}, {"high_idx": 7, "image_name": "000000278.png", "low_idx": 61}, {"high_idx": 7, "image_name": "000000279.png", "low_idx": 61}, {"high_idx": 7, "image_name": "000000280.png", "low_idx": 61}, {"high_idx": 7, "image_name": "000000281.png", "low_idx": 62}, {"high_idx": 7, "image_name": "000000282.png", "low_idx": 62}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 62}, {"high_idx": 7, "image_name": "000000284.png", "low_idx": 62}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Lettuce", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-2|2|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["lettuce"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Lettuce", [-4.50811672, -4.50811672, 0.19782352, 0.19782352, 3.9100184, 3.9100184]], "coordinateReceptacleObjectId": ["DiningTable", [-5.38, -5.38, 1.332, 1.332, 0.0562074184, 0.0562074184]], "forceVisible": true, "objectId": "Lettuce|-01.13|+00.98|+00.05"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-2|12|0|60"}}, {"discrete_action": {"action": "PutObject", "args": ["lettuce", "fridge"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Lettuce", [-4.50811672, -4.50811672, 0.19782352, 0.19782352, 3.9100184, 3.9100184]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-1.76, -1.76, 15.988, 15.988, 0.0562069416, 0.0562069416]], "forceVisible": true, "objectId": "Lettuce|-01.13|+00.98|+00.05", "receptacleObjectId": "<PERSON><PERSON>|-00.44|+00.01|+04.00"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-2|2|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["lettuce"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Lettuce", [-5.03659916, -5.03659916, 1.4343116, 1.4343116, 3.9100184, 3.9100184]], "coordinateReceptacleObjectId": ["DiningTable", [-5.38, -5.38, 1.332, 1.332, 0.0562074184, 0.0562074184]], "forceVisible": true, "objectId": "Lettuce|-01.26|+00.98|+00.36"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-2|12|0|60"}}, {"discrete_action": {"action": "PutObject", "args": ["lettuce", "fridge"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Lettuce", [-5.03659916, -5.03659916, 1.4343116, 1.4343116, 3.9100184, 3.9100184]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-1.76, -1.76, 15.988, 15.988, 0.0562069416, 0.0562069416]], "forceVisible": true, "objectId": "Lettuce|-01.26|+00.98|+00.36", "receptacleObjectId": "<PERSON><PERSON>|-00.44|+00.01|+04.00"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Lettuce|-01.13|+00.98|+00.05"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [0, 118, 51, 174], "mask": [[35110, 3], [35406, 11], [35703, 18], [36000, 25], [36300, 27], [36600, 29], [36900, 31], [37200, 33], [37500, 35], [37800, 36], [38100, 37], [38400, 38], [38700, 39], [39000, 40], [39300, 40], [39600, 41], [39900, 43], [40200, 44], [40500, 45], [40800, 46], [41100, 47], [41400, 48], [41700, 49], [42000, 49], [42300, 50], [42600, 51], [42900, 51], [43200, 52], [43500, 52], [43800, 52], [44100, 52], [44400, 52], [44700, 52], [45000, 52], [45300, 52], [45600, 51], [45900, 51], [46200, 50], [46500, 49], [46800, 48], [47100, 47], [47400, 46], [47700, 44], [48000, 42], [48300, 41], [48600, 40], [48900, 39], [49200, 38], [49500, 36], [49800, 35], [50100, 33], [50400, 32], [50700, 30], [51000, 28], [51300, 26], [51600, 24], [51902, 19]], "point": [25, 145]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-00.44|+00.01|+04.00"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 201], "mask": [[0, 24300], [24301, 299], [24602, 298], [24902, 298], [25203, 297], [25504, 296], [25804, 296], [26105, 295], [26406, 294], [26706, 294], [27007, 293], [27308, 292], [27608, 292], [27909, 291], [28210, 290], [28511, 289], [28811, 289], [29112, 288], [29413, 287], [29713, 287], [30014, 286], [30315, 285], [30615, 285], [30916, 284], [31217, 283], [31517, 283], [31818, 282], [32119, 281], [32419, 281], [32720, 280], [33021, 279], [33321, 279], [33622, 278], [33923, 277], [34223, 277], [34524, 276], [34825, 275], [35126, 274], [35426, 274], [35727, 273], [36028, 272], [36328, 272], [36629, 271], [36930, 270], [37230, 270], [37531, 268], [37832, 267], [38132, 266], [38433, 264], [38734, 262], [39034, 261], [39335, 259], [39636, 257], [39936, 257], [40237, 255], [40538, 253], [40838, 252], [41139, 250], [41440, 248], [41741, 246], [42041, 246], [42342, 244], [42643, 242], [42943, 241], [43244, 239], [43545, 237], [43845, 236], [44146, 235], [44447, 233], [44747, 232], [45048, 230], [45349, 228], [45649, 227], [45950, 225], [46251, 224], [46551, 223], [46852, 221], [47153, 219], [47453, 218], [47754, 216], [48055, 214], [48356, 213], [48656, 212], [48957, 210], [49258, 208], [49558, 207], [49859, 205], [50160, 203], [50460, 203], [50761, 201], [51062, 199], [51362, 198], [51663, 196], [51964, 194], [52264, 193], [52565, 192], [52866, 190], [53166, 189], [53467, 187], [53768, 185], [54068, 184], [54369, 182], [54670, 181], [54971, 179], [55271, 178], [55572, 176], [55873, 174], [56173, 173], [56474, 171], [56775, 170], [57075, 71], [57155, 89], [57377, 66], [57458, 84], [57682, 59], [57760, 77], [57988, 50], [58062, 69], [58294, 42], [58364, 61], [58600, 34], [58665, 53], [58907, 25], [58967, 44], [59214, 17], [59268, 37], [59521, 9], [59569, 29], [59870, 18], [60171, 4]], "point": [149, 100]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Lettuce|-01.13|+00.98|+00.05", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-00.44|+00.01|+04.00"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 174], [223, 251], [523, 251], [823, 251], [1123, 251], [1423, 252], [1723, 252], [2023, 252], [2323, 252], [2622, 253], [2922, 253], [3222, 253], [3522, 253], [3821, 254], [4121, 254], [4421, 254], [4720, 255], [5020, 256], [5320, 256], [5619, 257], [5919, 258], [6218, 259], [6518, 260], [6817, 261], [7116, 263], [7414, 267], [7713, 270], [8011, 278], [8304, 10701], [19012, 104], [19125, 178], [19315, 99], [19427, 174], [19616, 97], [19729, 171], [19918, 94], [20030, 169], [20219, 91], [20331, 167], [20520, 89], [20632, 165], [20820, 88], [20932, 165], [21121, 87], [21232, 164], [21422, 85], [21533, 163], [21723, 83], [21833, 162], [22023, 83], [22133, 162], [22323, 82], [22434, 161], [22624, 81], [22734, 161], [22924, 80], [23034, 161], [23224, 80], [23334, 161], [23524, 80], [23634, 161], [23824, 80], [23934, 161], [24124, 79], [24233, 162], [24424, 79], [24533, 162], [24724, 79], [24833, 162], [25024, 79], [25132, 164], [25324, 79], [25432, 164], [25623, 80], [25731, 166], [25923, 81], [26030, 167], [26223, 81], [26330, 168], [26522, 82], [26629, 170], [26821, 84], [26928, 172], [27121, 85], [27227, 174], [27420, 87], [27526, 176], [27718, 90], [27825, 179], [28017, 93], [28123, 183], [28315, 98], [28421, 2178], [30600, 299], [30900, 298], [31200, 297], [31500, 296], [31800, 296], [32100, 295], [32400, 294], [32700, 293], [33000, 293], [33300, 292], [33600, 291], [33900, 290], [34200, 289], [34500, 289], [34800, 288], [35100, 287], [35400, 286], [35700, 286], [36000, 285], [36300, 284], [36600, 283], [36900, 283], [37200, 282], [37500, 281], [37800, 280], [38100, 280], [38400, 279], [38700, 278], [39000, 277], [39300, 277], [39600, 276], [39900, 275], [40200, 274], [40500, 274], [40800, 273], [41100, 272], [41400, 271], [41700, 271], [42000, 270], [42300, 269], [42600, 268], [42900, 268], [43200, 267], [43500, 266], [43800, 265], [44100, 265], [44400, 264], [44700, 263], [45000, 262], [45300, 262], [45600, 261], [45900, 260], [46200, 259], [46500, 259], [46800, 258], [47100, 257], [47400, 256], [47700, 256], [48000, 255], [48300, 254], [48600, 253], [48900, 253], [49200, 252], [49500, 251], [49800, 250], [50100, 250], [50400, 249], [50700, 248], [51000, 247], [51300, 247], [51600, 246], [51900, 245], [52200, 244], [52500, 244], [52800, 243], [53100, 242], [53400, 241], [53700, 239], [54000, 84], [54300, 84], [54600, 84], [54900, 84], [55200, 84], [55500, 84], [55800, 84], [56100, 83], [56400, 83], [56700, 83], [57000, 83], [57300, 83], [57600, 83], [57900, 83], [58200, 82], [58500, 82], [58800, 83], [59100, 82], [59400, 82], [59700, 82], [60000, 82], [60300, 82], [60600, 82], [60900, 82], [61200, 81], [61500, 81], [61800, 81], [62100, 81], [62400, 81], [62700, 81], [63000, 81], [63300, 80], [63600, 80], [63900, 80], [64200, 80], [64500, 80], [64800, 80], [65100, 80], [65400, 80], [65700, 79], [66000, 79], [66300, 79], [66600, 79], [66900, 79], [67200, 79], [67500, 79], [67800, 78], [68100, 78], [68400, 78], [68700, 78], [69000, 78], [69300, 78], [69600, 78], [69900, 77], [70200, 77], [70500, 77], [70800, 77], [71100, 77], [71400, 77], [71700, 77], [72000, 77], [72300, 76], [72600, 76], [72900, 76], [73200, 76], [73500, 76], [73800, 76], [74100, 76], [74400, 75], [74700, 75], [75000, 75], [75300, 75], [75600, 75], [75900, 75], [76200, 75], [76500, 75], [76800, 74], [77100, 74], [77400, 74], [77700, 74], [78000, 74], [78300, 73], [78600, 73], [78900, 73], [79200, 73], [79500, 73], [79800, 73], [80100, 73], [80400, 73], [80700, 73], [81000, 72], [81300, 72], [81600, 72], [81900, 72], [82200, 72], [82500, 72], [82800, 72], [83100, 72], [83400, 71], [83700, 71], [84000, 71], [84300, 71], [84600, 71], [84900, 71], [85200, 71], [85500, 70], [85800, 70], [86100, 70], [86400, 70], [86700, 70], [87000, 70], [87300, 70], [87600, 69], [87900, 69], [88200, 69], [88500, 69], [88800, 69], [89100, 69], [89400, 69], [89700, 69]], "point": [149, 149]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-00.44|+00.01|+04.00"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 111], [163, 11], [223, 188], [462, 12], [523, 189], [762, 12], [823, 189], [1062, 12], [1123, 189], [1361, 13], [1423, 189], [1660, 15], [1723, 189], [1960, 15], [2023, 190], [2260, 15], [2323, 190], [2559, 16], [2622, 192], [2858, 17], [2922, 192], [3157, 18], [3222, 193], [3457, 18], [3522, 193], [3756, 19], [3821, 195], [4055, 20], [4121, 196], [4354, 21], [4421, 196], [4653, 22], [4720, 198], [4952, 23], [5020, 199], [5251, 25], [5320, 201], [5549, 27], [5619, 204], [5847, 29], [5919, 206], [6145, 32], [6218, 208], [6443, 34], [6518, 213], [6737, 41], [6817, 261], [7116, 263], [7414, 267], [7713, 270], [8011, 278], [8304, 10701], [19012, 104], [19125, 178], [19315, 99], [19427, 174], [19616, 97], [19729, 171], [19918, 94], [20030, 169], [20219, 91], [20331, 167], [20520, 89], [20632, 165], [20820, 88], [20932, 165], [21121, 87], [21232, 164], [21422, 85], [21533, 163], [21723, 83], [21833, 162], [22023, 83], [22133, 162], [22323, 82], [22434, 161], [22624, 81], [22734, 161], [22924, 80], [23034, 161], [23224, 80], [23334, 161], [23524, 80], [23634, 161], [23824, 80], [23934, 161], [24124, 79], [24233, 162], [24424, 79], [24533, 162], [24724, 79], [24833, 162], [25024, 79], [25132, 164], [25324, 79], [25432, 164], [25623, 80], [25731, 166], [25923, 81], [26030, 167], [26223, 81], [26330, 168], [26522, 82], [26629, 170], [26821, 84], [26928, 172], [27121, 85], [27227, 174], [27420, 87], [27526, 176], [27718, 90], [27825, 179], [28017, 93], [28123, 183], [28315, 98], [28421, 2178], [30600, 299], [30900, 298], [31200, 297], [31500, 296], [31800, 296], [32100, 295], [32400, 294], [32700, 293], [33000, 293], [33300, 292], [33600, 291], [33900, 290], [34200, 289], [34500, 289], [34800, 288], [35100, 287], [35400, 286], [35700, 286], [36000, 285], [36300, 284], [36600, 283], [36900, 283], [37200, 282], [37500, 281], [37800, 280], [38100, 280], [38400, 279], [38700, 278], [39000, 277], [39300, 277], [39600, 276], [39900, 275], [40200, 274], [40500, 274], [40800, 273], [41100, 272], [41400, 271], [41700, 271], [42000, 270], [42300, 269], [42600, 268], [42900, 268], [43200, 267], [43500, 266], [43800, 265], [44100, 265], [44400, 264], [44700, 263], [45000, 262], [45300, 262], [45600, 261], [45900, 260], [46200, 259], [46500, 259], [46800, 258], [47100, 257], [47400, 256], [47700, 256], [48000, 255], [48300, 254], [48600, 253], [48900, 253], [49200, 252], [49500, 251], [49800, 250], [50100, 250], [50400, 249], [50700, 248], [51000, 247], [51300, 247], [51600, 246], [51900, 245], [52200, 244], [52500, 244], [52800, 243], [53100, 242], [53400, 241], [53700, 239], [54000, 84], [54300, 84], [54600, 84], [54900, 84], [55200, 84], [55500, 84], [55800, 84], [56100, 83], [56400, 83], [56700, 83], [57000, 83], [57300, 83], [57600, 83], [57900, 83], [58200, 82], [58500, 82], [58800, 83], [59100, 82], [59400, 82], [59700, 82], [60000, 82], [60300, 82], [60600, 82], [60900, 82], [61200, 81], [61500, 81], [61800, 81], [62100, 81], [62400, 81], [62700, 81], [63000, 81], [63300, 80], [63600, 80], [63900, 80], [64200, 80], [64500, 80], [64800, 80], [65100, 80], [65400, 80], [65700, 79], [66000, 79], [66300, 79], [66600, 79], [66900, 79], [67200, 79], [67500, 79], [67800, 78], [68100, 78], [68400, 78], [68700, 78], [69000, 78], [69300, 78], [69600, 78], [69900, 77], [70200, 77], [70500, 77], [70800, 77], [71100, 77], [71400, 77], [71700, 77], [72000, 77], [72300, 76], [72600, 76], [72900, 76], [73200, 76], [73500, 76], [73800, 76], [74100, 76], [74400, 75], [74700, 75], [75000, 75], [75300, 75], [75600, 75], [75900, 75], [76200, 75], [76500, 75], [76800, 74], [77100, 74], [77400, 74], [77700, 74], [78000, 74], [78300, 74], [78600, 74], [78900, 73], [79200, 73], [79500, 73], [79800, 73], [80100, 73], [80400, 73], [80700, 73], [81000, 72], [81300, 72], [81600, 72], [81900, 72], [82200, 72], [82500, 72], [82800, 72], [83100, 72], [83400, 71], [83700, 71], [84000, 71], [84300, 71], [84600, 71], [84900, 71], [85200, 71], [85500, 70], [85800, 70], [86100, 70], [86400, 70], [86700, 70], [87000, 70], [87300, 70], [87600, 69], [87900, 69], [88200, 69], [88500, 69], [88800, 69], [89100, 69], [89400, 69], [89700, 69]], "point": [149, 149]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Lettuce|-01.26|+00.98|+00.36"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [81, 98, 142, 148], "mask": [[29211, 11], [29506, 20], [29803, 26], [30101, 29], [30399, 33], [30698, 36], [30997, 38], [31295, 41], [31595, 42], [31894, 44], [32193, 46], [32492, 48], [32791, 49], [33089, 52], [33388, 53], [33687, 55], [33985, 57], [34284, 58], [34583, 60], [34883, 60], [35182, 61], [35481, 62], [35781, 62], [36081, 62], [36381, 62], [36681, 62], [36981, 62], [37281, 62], [37581, 62], [37881, 62], [38182, 61], [38482, 61], [38783, 60], [39084, 58], [39385, 57], [39686, 56], [39987, 54], [40288, 53], [40590, 51], [40891, 49], [41192, 47], [41493, 46], [41794, 44], [42095, 42], [42397, 39], [42698, 37], [43000, 32], [43313, 17], [43616, 12], [43919, 7], [44220, 3]], "point": [111, 122]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-00.44|+00.01|+04.00"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 201], "mask": [[0, 24300], [24301, 299], [24602, 298], [24902, 298], [25203, 297], [25504, 296], [25804, 296], [26105, 295], [26406, 294], [26706, 294], [27007, 293], [27308, 292], [27608, 292], [27909, 291], [28210, 290], [28511, 289], [28811, 289], [29112, 288], [29413, 287], [29713, 287], [30014, 286], [30315, 285], [30615, 285], [30916, 284], [31217, 283], [31517, 283], [31818, 282], [32119, 281], [32419, 281], [32720, 280], [33021, 279], [33321, 279], [33622, 278], [33923, 277], [34223, 277], [34524, 276], [34825, 275], [35126, 274], [35426, 274], [35727, 273], [36028, 272], [36328, 272], [36629, 271], [36930, 270], [37230, 270], [37531, 268], [37832, 267], [38132, 266], [38433, 264], [38734, 262], [39034, 261], [39335, 259], [39636, 257], [39936, 257], [40237, 255], [40538, 253], [40838, 252], [41139, 250], [41440, 248], [41741, 246], [42041, 246], [42342, 244], [42643, 242], [42943, 241], [43244, 239], [43545, 237], [43845, 236], [44146, 235], [44447, 233], [44747, 232], [45048, 230], [45349, 228], [45649, 227], [45950, 225], [46251, 224], [46551, 223], [46852, 221], [47153, 219], [47453, 218], [47754, 216], [48055, 214], [48356, 213], [48656, 212], [48957, 210], [49258, 208], [49558, 207], [49859, 205], [50160, 203], [50460, 203], [50761, 201], [51062, 199], [51362, 198], [51663, 196], [51964, 194], [52264, 193], [52565, 192], [52866, 190], [53166, 189], [53467, 187], [53768, 185], [54068, 184], [54369, 182], [54670, 181], [54971, 179], [55271, 178], [55572, 176], [55873, 174], [56173, 173], [56474, 171], [56775, 170], [57075, 71], [57155, 89], [57377, 66], [57458, 84], [57682, 59], [57760, 77], [57988, 50], [58062, 69], [58294, 42], [58364, 61], [58600, 34], [58665, 53], [58907, 25], [58967, 44], [59214, 17], [59268, 37], [59521, 9], [59569, 29], [59870, 18], [60171, 4]], "point": [149, 100]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Lettuce|-01.26|+00.98|+00.36", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-00.44|+00.01|+04.00"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 111], [163, 11], [223, 188], [462, 12], [523, 189], [762, 12], [823, 189], [1062, 12], [1123, 189], [1361, 13], [1423, 189], [1660, 15], [1723, 189], [1960, 15], [2023, 190], [2260, 15], [2323, 190], [2559, 16], [2622, 192], [2858, 17], [2922, 192], [3157, 18], [3222, 193], [3457, 18], [3522, 193], [3756, 19], [3821, 195], [4055, 20], [4121, 196], [4354, 21], [4421, 196], [4653, 22], [4720, 198], [4952, 23], [5020, 199], [5251, 25], [5320, 201], [5549, 27], [5619, 204], [5847, 29], [5919, 206], [6145, 32], [6218, 208], [6443, 34], [6518, 213], [6737, 41], [6817, 261], [7116, 263], [7414, 267], [7713, 270], [8011, 278], [8304, 10701], [19012, 104], [19125, 178], [19315, 99], [19427, 174], [19616, 97], [19729, 171], [19918, 94], [20030, 169], [20219, 91], [20331, 167], [20520, 89], [20632, 165], [20820, 88], [20932, 165], [21121, 87], [21232, 164], [21422, 85], [21533, 163], [21723, 83], [21833, 162], [22023, 83], [22133, 162], [22323, 82], [22434, 161], [22624, 81], [22734, 161], [22924, 80], [23034, 161], [23224, 80], [23334, 161], [23524, 80], [23634, 161], [23824, 80], [23934, 161], [24124, 79], [24233, 162], [24424, 79], [24533, 162], [24724, 79], [24833, 162], [25024, 79], [25132, 164], [25324, 79], [25432, 164], [25623, 80], [25731, 166], [25923, 81], [26030, 167], [26223, 81], [26330, 168], [26522, 82], [26629, 170], [26821, 84], [26928, 172], [27121, 85], [27227, 174], [27420, 87], [27526, 176], [27718, 90], [27825, 179], [28017, 93], [28123, 183], [28315, 98], [28421, 2178], [30600, 299], [30900, 298], [31200, 297], [31500, 296], [31800, 296], [32100, 295], [32400, 294], [32700, 293], [33000, 293], [33300, 292], [33600, 291], [33900, 290], [34200, 289], [34500, 289], [34800, 288], [35100, 287], [35400, 286], [35700, 286], [36000, 285], [36300, 284], [36600, 283], [36900, 283], [37200, 282], [37500, 281], [37800, 280], [38100, 280], [38400, 279], [38700, 278], [39000, 277], [39300, 277], [39600, 276], [39900, 275], [40200, 274], [40500, 274], [40800, 273], [41100, 272], [41400, 271], [41700, 271], [42000, 270], [42300, 269], [42600, 268], [42900, 268], [43200, 267], [43500, 266], [43800, 265], [44100, 265], [44400, 264], [44700, 263], [45000, 262], [45300, 262], [45600, 261], [45900, 260], [46200, 259], [46500, 259], [46800, 258], [47100, 257], [47400, 256], [47700, 256], [48000, 255], [48300, 254], [48600, 253], [48900, 253], [49200, 252], [49500, 251], [49800, 250], [50100, 250], [50400, 249], [50700, 248], [51000, 247], [51300, 247], [51600, 246], [51900, 245], [52200, 244], [52500, 244], [52800, 243], [53100, 242], [53400, 241], [53700, 239], [54000, 84], [54300, 84], [54600, 84], [54900, 84], [55200, 84], [55500, 84], [55800, 84], [56100, 83], [56400, 83], [56700, 83], [57000, 83], [57300, 83], [57600, 83], [57900, 83], [58200, 82], [58500, 82], [58800, 83], [59100, 82], [59400, 82], [59700, 82], [60000, 82], [60300, 82], [60600, 82], [60900, 82], [61200, 81], [61500, 81], [61800, 81], [62100, 81], [62400, 81], [62700, 81], [63000, 81], [63300, 80], [63600, 80], [63900, 80], [64200, 80], [64500, 80], [64800, 80], [65100, 80], [65400, 80], [65700, 79], [66000, 79], [66300, 79], [66600, 79], [66900, 79], [67200, 79], [67500, 79], [67800, 78], [68100, 78], [68400, 78], [68700, 78], [69000, 78], [69300, 78], [69600, 78], [69900, 77], [70200, 77], [70500, 77], [70800, 77], [71100, 77], [71400, 77], [71700, 77], [72000, 77], [72300, 76], [72600, 76], [72900, 76], [73200, 76], [73500, 76], [73800, 76], [74100, 76], [74400, 75], [74700, 75], [75000, 75], [75300, 75], [75600, 75], [75900, 75], [76200, 75], [76500, 75], [76800, 74], [77100, 74], [77400, 74], [77700, 74], [78000, 74], [78300, 73], [78600, 73], [78900, 73], [79200, 73], [79500, 73], [79800, 73], [80100, 73], [80400, 73], [80700, 73], [81000, 72], [81300, 72], [81600, 72], [81900, 72], [82200, 72], [82500, 72], [82800, 72], [83100, 72], [83400, 71], [83700, 71], [84000, 71], [84300, 71], [84600, 71], [84900, 71], [85200, 71], [85500, 70], [85800, 70], [86100, 70], [86400, 70], [86700, 70], [87000, 70], [87300, 70], [87600, 69], [87900, 69], [88200, 69], [88500, 69], [88800, 69], [89100, 69], [89400, 69], [89700, 69]], "point": [149, 149]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-00.44|+00.01|+04.00"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 44], [98, 13], [163, 11], [223, 121], [398, 13], [462, 12], [523, 121], [698, 14], [762, 12], [823, 122], [998, 14], [1062, 12], [1123, 122], [1297, 15], [1361, 13], [1423, 123], [1596, 16], [1660, 15], [1723, 123], [1896, 16], [1960, 15], [2023, 124], [2195, 18], [2260, 15], [2323, 124], [2494, 19], [2559, 16], [2622, 126], [2794, 20], [2858, 17], [2922, 126], [3093, 21], [3157, 18], [3222, 127], [3392, 23], [3457, 18], [3522, 128], [3692, 23], [3756, 19], [3821, 130], [3991, 25], [4055, 20], [4121, 131], [4290, 27], [4354, 21], [4421, 132], [4589, 28], [4653, 22], [4720, 134], [4888, 30], [4952, 23], [5020, 135], [5186, 33], [5251, 25], [5320, 136], [5485, 36], [5549, 27], [5619, 138], [5783, 40], [5847, 29], [5919, 140], [6081, 44], [6145, 32], [6218, 143], [6379, 47], [6443, 34], [6518, 147], [6673, 58], [6737, 41], [6817, 261], [7116, 263], [7414, 267], [7713, 270], [8011, 278], [8304, 10701], [19012, 104], [19125, 178], [19315, 99], [19427, 174], [19616, 97], [19729, 171], [19918, 94], [20030, 169], [20219, 91], [20331, 167], [20520, 89], [20632, 165], [20820, 88], [20932, 165], [21121, 87], [21232, 164], [21422, 85], [21533, 163], [21723, 83], [21833, 162], [22023, 83], [22133, 162], [22323, 82], [22434, 161], [22624, 81], [22734, 161], [22924, 80], [23034, 161], [23224, 80], [23334, 161], [23524, 80], [23634, 161], [23824, 80], [23934, 161], [24124, 79], [24233, 162], [24424, 79], [24533, 162], [24724, 79], [24833, 162], [25024, 79], [25132, 164], [25324, 79], [25432, 164], [25623, 80], [25731, 166], [25923, 81], [26030, 167], [26223, 81], [26330, 168], [26522, 82], [26629, 170], [26821, 84], [26928, 172], [27121, 85], [27227, 174], [27420, 87], [27526, 176], [27718, 90], [27825, 179], [28017, 93], [28123, 183], [28315, 98], [28421, 2178], [30600, 299], [30900, 298], [31200, 297], [31500, 296], [31800, 296], [32100, 295], [32400, 294], [32700, 293], [33000, 293], [33300, 292], [33600, 291], [33900, 290], [34200, 289], [34500, 289], [34800, 288], [35100, 287], [35400, 286], [35700, 286], [36000, 285], [36300, 284], [36600, 283], [36900, 283], [37200, 282], [37500, 281], [37800, 280], [38100, 280], [38400, 279], [38700, 278], [39000, 277], [39300, 277], [39600, 276], [39900, 275], [40200, 274], [40500, 274], [40800, 273], [41100, 272], [41400, 271], [41700, 271], [42000, 270], [42300, 269], [42600, 268], [42900, 268], [43200, 267], [43500, 266], [43800, 265], [44100, 265], [44400, 264], [44700, 263], [45000, 262], [45300, 262], [45600, 261], [45900, 260], [46200, 259], [46500, 259], [46800, 258], [47100, 257], [47400, 256], [47700, 256], [48000, 255], [48300, 254], [48600, 253], [48900, 253], [49200, 252], [49500, 251], [49800, 250], [50100, 250], [50400, 249], [50700, 248], [51000, 247], [51300, 247], [51600, 246], [51900, 245], [52200, 244], [52500, 244], [52800, 243], [53100, 242], [53400, 241], [53700, 239], [54000, 84], [54300, 84], [54600, 84], [54900, 84], [55200, 84], [55500, 84], [55800, 84], [56100, 83], [56400, 83], [56700, 83], [57000, 83], [57300, 83], [57600, 83], [57900, 83], [58200, 82], [58500, 82], [58800, 83], [59100, 82], [59400, 82], [59700, 82], [60000, 82], [60300, 82], [60600, 82], [60900, 82], [61200, 81], [61500, 81], [61800, 81], [62100, 81], [62400, 81], [62700, 81], [63000, 81], [63300, 80], [63600, 80], [63900, 80], [64200, 80], [64500, 80], [64800, 80], [65100, 80], [65400, 80], [65700, 79], [66000, 79], [66300, 79], [66600, 79], [66900, 79], [67200, 79], [67500, 79], [67800, 78], [68100, 78], [68400, 78], [68700, 78], [69000, 78], [69300, 78], [69600, 78], [69900, 77], [70200, 77], [70500, 77], [70800, 77], [71100, 77], [71400, 77], [71700, 77], [72000, 77], [72300, 76], [72600, 76], [72900, 76], [73200, 76], [73500, 76], [73800, 76], [74100, 76], [74400, 75], [74700, 75], [75000, 75], [75300, 75], [75600, 75], [75900, 75], [76200, 75], [76500, 75], [76800, 74], [77100, 74], [77400, 74], [77700, 74], [78000, 74], [78300, 74], [78600, 74], [78900, 73], [79200, 73], [79500, 73], [79800, 73], [80100, 73], [80400, 73], [80700, 73], [81000, 72], [81300, 72], [81600, 72], [81900, 72], [82200, 72], [82500, 72], [82800, 72], [83100, 72], [83400, 71], [83700, 71], [84000, 71], [84300, 71], [84600, 71], [84900, 71], [85200, 71], [85500, 70], [85800, 70], [86100, 70], [86400, 70], [86700, 70], [87000, 70], [87300, 70], [87600, 69], [87900, 69], [88200, 69], [88500, 69], [88800, 69], [89100, 69], [89400, 69], [89700, 69]], "point": [149, 149]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan15", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -1.5, "y": 0.914953351, "z": 3.0}, "object_poses": [{"objectName": "Potato_08de48c7", "position": {"x": -1.523391, "y": 0.9238395, "z": -0.0535847545}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "ButterKnife_657c04b2", "position": {"x": -3.55922174, "y": 0.9158421, "z": 1.44065785}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_a09ab4be", "position": {"x": -3.4247, "y": 0.9668, "z": 1.9715}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_a09ab4be", "position": {"x": -3.27244067, "y": 0.9165412, "z": 3.82773781}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_fd79f50b", "position": {"x": -3.463628, "y": 0.9307999, "z": 1.5243156}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_fd79f50b", "position": {"x": -0.994908631, "y": 0.9064725, "z": 0.152496532}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_72bf05b8", "position": {"x": -0.9949086, "y": 0.8920212, "z": 0.461618572}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_0c310241", "position": {"x": -0.994908631, "y": 0.9266359, "z": 0.04945585}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_0c310241", "position": {"x": -0.323188424, "y": 1.57690024, "z": 4.136378}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Cup_69fc67c7", "position": {"x": -1.78763235, "y": 0.8912595, "z": 0.152496666}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_66cbdca0", "position": {"x": -0.206374109, "y": 0.7915431, "z": 3.817087}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Tomato_66cbdca0", "position": {"x": -0.67362386, "y": 0.7915431, "z": 3.81708431}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "SoapBottle_da34a2c3", "position": {"x": -1.16132855, "y": 0.0876939, "z": 3.78829956}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_da34a2c3", "position": {"x": -3.49228621, "y": 1.99503958, "z": 2.36322284}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_d1c7827d", "position": {"x": -1.65551174, "y": 0.8877788, "z": 0.04945594}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_d1c7827d", "position": {"x": -3.49270058, "y": 1.99075425, "z": 2.05446386}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_94931e65", "position": {"x": -2.30231142, "y": 1.001532, "z": 3.99884}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_94931e65", "position": {"x": -1.12702918, "y": 0.9775046, "z": 0.04945588}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_0fed2699", "position": {"x": -0.9949086, "y": 0.9728629, "z": 0.358577877}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_0fed2699", "position": {"x": -0.32318753, "y": 1.06762743, "z": 3.98899078}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Mug_0af3d2bb", "position": {"x": -3.49270058, "y": 1.98944807, "z": 1.94508243}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_0c310241", "position": {"x": -1.25604284, "y": 0.122954339, "z": 3.85227823}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_4b738b3b", "position": {"x": -3.36803436, "y": 0.9393277, "z": 1.18968439}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_72bf05b8", "position": {"x": -3.65481567, "y": 0.9163486, "z": 1.44065785}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_0fed2699", "position": {"x": -3.50548887, "y": 1.62768888, "z": 2.12050462}, "rotation": {"x": 0.0, "y": 89.99987, "z": 0.0}}, {"objectName": "Potato_08de48c7", "position": {"x": -0.5568129, "y": 1.30620408, "z": 4.06263}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Plate_a5e935d4", "position": {"x": -3.0059998, "y": 0.9045948, "z": 3.67700124}, "rotation": {"x": 0.01985445, "y": -0.000147917526, "z": 0.0012652165}}, {"objectName": "Pot_46b03e61", "position": {"x": -1.65551162, "y": 0.893938, "z": 0.461618662}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Kettle_a09ab4be", "position": {"x": -3.19179678, "y": 0.08461186, "z": 1.2785722}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_2242b994", "position": {"x": -3.27244067, "y": 0.9164287, "z": 3.66382527}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_0e75bb4f", "position": {"x": -0.0895631, "y": 1.2727263, "z": 4.06263256}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Tomato_66cbdca0", "position": {"x": -0.790438354, "y": 1.34249377, "z": 4.16863346}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Cup_69fc67c7", "position": {"x": -1.523391, "y": 0.8912595, "z": 0.04945594}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_fd79f50b", "position": {"x": -3.55922174, "y": 0.9307999, "z": 1.10602665}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_da34a2c3", "position": {"x": -3.65481567, "y": 0.9157029, "z": 1.27334213}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_94931e65", "position": {"x": -1.25914979, "y": 0.9775046, "z": 0.3585779}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pan_7e5e2cad", "position": {"x": -2.61477637, "y": 0.902653933, "z": 3.62041235}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_8bd3d3d2", "position": {"x": -1.65551162, "y": 0.9514456, "z": -0.0535847545}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_d1c7827d", "position": {"x": -3.50825119, "y": 1.45542765, "z": 1.53008878}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_94539d9a", "position": {"x": -3.463628, "y": 0.9121062, "z": 1.10602665}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_657c04b2", "position": {"x": -1.72990179, "y": 0.7895982, "z": 3.7}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_73731673", "position": {"x": -3.081253, "y": 0.915364265, "z": 1.10602665}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_58cf1806", "position": {"x": -3.27244067, "y": 0.9169294, "z": 1.60797334}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_0af3d2bb", "position": {"x": -1.3912704, "y": 0.886472642, "z": 0.04945582}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}], "object_toggles": [], "random_seed": 2759391546, "scene_num": 15}, "task_id": "trial_T20190909_005320_906392", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A2KAGFQU28JY43_36NEMU28XI4NFWM22PBNP5X8C4TWMV", "high_descs": ["Go to the door ahead of you and turn right then go to the left end of the table across from you and turn to your right. ", "Pick up the head of lettuce on the back of the table. ", "Turn to your right and go across the room, to the refrigerator. ", "Place the head of lettuce on the top shelf in the refrigerator. ", "Turn around and go back to the left end of the table, then turn to your right. ", "Grab the head of lettuce that is behind the loaf of bread. ", "Turn to your right and go across the room to the refrigerator. ", "Place the head of lettuce on the top shelf to the left of the other head of lettuce. "], "task_desc": "Put two heads of lettuce in the refrigerator. ", "votes": [1, 1]}, {"assignment_id": "A3PPLDHC3CG0YN_3FQ5JJ512OF22EDLPC7KE9SDUMKNKE", "high_descs": ["Turn right at the door and walk towards the trash bag, turn right to face the table. ", "Pick up the head of lettuce to the left of the loaf of bread. ", "Turn right and walk to the fridge. ", "Open the fridge door, put the lettuce inside the fridge, close the fridge door. ", "Turn around and walk back over to the table. ", "Pick up the lettuce from the table that's behind the loaf of bread. ", "Turn right and walk to the fridge. ", "Open the fridge door, put the lettuce in the fridge on the top shelf to the left of the first head of lettuce, close the door. "], "task_desc": "To move two heads of lettuce to the fridge. ", "votes": [1, 1]}, {"assignment_id": "A10AVWALIHR4UQ_3PQ8K71NH0BP6IAA4HPQYP5HQ3OAAZ", "high_descs": ["Move to the door to the right of the refrigerator then turn right and move to the white trash bag in the corner of the room, then turn right and face the left edge of the table.", "Pick up the head of lettuce between the loaf of bread and the window from the table.", "Turn right and move to the black refrigerator to the right of the gray rubbish bin.", "Open the refrigerator and place the lettuce on the center shelf and then close the refrigerator door.", "Turn around and move to the left side of the table in front of the window.", "Pick up the head of lettuce between the loaf of bread and the pot from the center of the table.", "Turn right and move to the black refrigerator.", "Open the refrigerator, place the head of lettuce on the left side of the center shelf, and then close the refrigerator door."], "task_desc": "Place two heads of lettuce in the refrigerator.", "votes": [1, 1]}]}}