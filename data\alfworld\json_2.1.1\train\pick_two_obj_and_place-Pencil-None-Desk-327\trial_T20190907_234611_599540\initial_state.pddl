
(define (problem plan_trial_T20190907_234611_599540)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        AlarmClock_bar__minus_01_dot_58_bar__plus_00_dot_70_bar__minus_01_dot_90 - object
        BaseballBat_bar__plus_01_dot_44_bar__plus_00_dot_64_bar__plus_01_dot_99 - object
        BasketBall_bar__plus_01_dot_27_bar__plus_00_dot_12_bar__minus_01_dot_29 - object
        Blinds_bar__plus_00_dot_25_bar__plus_02_dot_19_bar__minus_02_dot_69 - object
        Blinds_bar__minus_00_dot_35_bar__plus_02_dot_19_bar__minus_02_dot_69 - object
        Blinds_bar__minus_00_dot_95_bar__plus_02_dot_19_bar__minus_02_dot_69 - object
        Book_bar__plus_00_dot_62_bar__plus_00_dot_57_bar__minus_02_dot_47 - object
        Boots_bar__plus_01_dot_29_bar__plus_00_dot_00_bar__minus_00_dot_96 - object
        Bowl_bar__plus_00_dot_94_bar__plus_00_dot_58_bar__minus_02_dot_54 - object
        Bowl_bar__plus_01_dot_23_bar__plus_00_dot_91_bar__plus_00_dot_04 - object
        Bowl_bar__minus_01_dot_33_bar__plus_00_dot_70_bar__minus_01_dot_90 - object
        CD_bar__plus_00_dot_31_bar__plus_00_dot_58_bar__minus_02_dot_54 - object
        CD_bar__plus_01_dot_32_bar__plus_00_dot_50_bar__minus_01_dot_98 - object
        CellPhone_bar__minus_01_dot_40_bar__plus_00_dot_40_bar__minus_02_dot_15 - object
        CellPhone_bar__minus_01_dot_46_bar__plus_00_dot_70_bar__minus_02_dot_19 - object
        Chair_bar__plus_00_dot_28_bar__plus_00_dot_00_bar__minus_02_dot_26 - object
        CreditCard_bar__plus_01_dot_07_bar__plus_00_dot_50_bar__minus_01_dot_82 - object
        CreditCard_bar__plus_01_dot_29_bar__plus_01_dot_41_bar__minus_00_dot_67 - object
        CreditCard_bar__minus_01_dot_27_bar__plus_00_dot_70_bar__minus_02_dot_34 - object
        DeskLamp_bar__minus_01_dot_49_bar__plus_00_dot_69_bar__minus_02_dot_42 - object
        KeyChain_bar__plus_01_dot_12_bar__plus_00_dot_51_bar__minus_02_dot_04 - object
        KeyChain_bar__minus_01_dot_27_bar__plus_00_dot_70_bar__minus_02_dot_48 - object
        KeyChain_bar__minus_01_dot_33_bar__plus_00_dot_10_bar__minus_02_dot_15 - object
        Laptop_bar__minus_01_dot_32_bar__plus_00_dot_61_bar__minus_01_dot_07 - object
        LightSwitch_bar__minus_00_dot_76_bar__plus_01_dot_18_bar__plus_02_dot_10 - object
        Mirror_bar__plus_00_dot_74_bar__plus_01_dot_49_bar__plus_02_dot_10 - object
        Pencil_bar__plus_01_dot_16_bar__plus_00_dot_39_bar__minus_00_dot_24 - object
        Pencil_bar__plus_01_dot_29_bar__plus_00_dot_92_bar__minus_00_dot_54 - object
        Pen_bar__plus_01_dot_22_bar__plus_00_dot_11_bar__minus_01_dot_96 - object
        Pillow_bar__minus_01_dot_08_bar__plus_00_dot_71_bar__minus_00_dot_22 - object
        Window_bar__plus_00_dot_24_bar__plus_00_dot_96_bar__minus_02_dot_68 - object
        Window_bar__minus_00_dot_36_bar__plus_00_dot_96_bar__minus_02_dot_68 - object
        Window_bar__minus_00_dot_96_bar__plus_00_dot_96_bar__minus_02_dot_68 - object
        Bed_bar__minus_01_dot_21_bar__plus_00_dot_02_bar__minus_00_dot_27 - receptacle
        Desk_bar__plus_00_dot_45_bar_00_dot_00_bar__minus_02_dot_53 - receptacle
        Drawer_bar__plus_00_dot_98_bar__plus_00_dot_14_bar__minus_01_dot_64 - receptacle
        Drawer_bar__plus_00_dot_98_bar__plus_00_dot_14_bar__minus_01_dot_96 - receptacle
        Drawer_bar__plus_00_dot_98_bar__plus_00_dot_27_bar__minus_01_dot_64 - receptacle
        Drawer_bar__plus_00_dot_98_bar__plus_00_dot_27_bar__minus_01_dot_96 - receptacle
        Drawer_bar__plus_00_dot_98_bar__plus_00_dot_40_bar__minus_01_dot_64 - receptacle
        Drawer_bar__plus_00_dot_98_bar__plus_00_dot_40_bar__minus_01_dot_96 - receptacle
        Drawer_bar__minus_01_dot_35_bar__plus_00_dot_19_bar__minus_02_dot_05 - receptacle
        Drawer_bar__minus_01_dot_35_bar__plus_00_dot_48_bar__minus_02_dot_05 - receptacle
        Dresser_bar__minus_01_dot_47_bar__plus_00_dot_03_bar__minus_02_dot_05 - receptacle
        GarbageCan_bar__minus_01_dot_57_bar__plus_00_dot_00_bar__plus_01_dot_94 - receptacle
        Shelf_bar__plus_01_dot_22_bar__plus_00_dot_86_bar__minus_01_dot_80 - receptacle
        Shelf_bar__plus_01_dot_26_bar__plus_00_dot_38_bar__minus_00_dot_26 - receptacle
        Shelf_bar__plus_01_dot_26_bar__plus_00_dot_90_bar__minus_00_dot_27 - receptacle
        Shelf_bar__plus_01_dot_26_bar__plus_01_dot_40_bar__minus_00_dot_26 - receptacle
        Shelf_bar__minus_01_dot_58_bar__plus_01_dot_51_bar__minus_02_dot_03 - receptacle
        loc_bar_0_bar_0_bar_3_bar_60 - location
        loc_bar_0_bar__minus_6_bar_1_bar_60 - location
        loc_bar__minus_2_bar__minus_9_bar_2_bar__minus_30 - location
        loc_bar__minus_2_bar__minus_9_bar_2_bar_60 - location
        loc_bar_1_bar__minus_6_bar_1_bar_60 - location
        loc_bar_2_bar__minus_1_bar_1_bar_45 - location
        loc_bar_1_bar__minus_5_bar_1_bar_60 - location
        loc_bar__minus_2_bar_5_bar_3_bar_60 - location
        loc_bar_3_bar_6_bar_0_bar_15 - location
        loc_bar_2_bar__minus_6_bar_2_bar_60 - location
        loc_bar_2_bar__minus_6_bar_1_bar_45 - location
        loc_bar_4_bar_6_bar_0_bar_60 - location
        loc_bar__minus_2_bar__minus_9_bar_1_bar_45 - location
        loc_bar__minus_3_bar__minus_8_bar_3_bar_60 - location
        loc_bar__minus_1_bar__minus_7_bar_3_bar_60 - location
        loc_bar__minus_3_bar__minus_9_bar_2_bar__minus_30 - location
        loc_bar__minus_3_bar_6_bar_0_bar_45 - location
        loc_bar__minus_3_bar__minus_9_bar_2_bar_60 - location
        loc_bar_2_bar__minus_1_bar_1_bar_60 - location
        loc_bar_2_bar__minus_5_bar_1_bar_60 - location
        loc_bar_2_bar__minus_1_bar_1_bar_15 - location
        loc_bar__minus_1_bar__minus_5_bar_1_bar_45 - location
        loc_bar__minus_2_bar__minus_9_bar_1_bar_60 - location
        loc_bar_0_bar__minus_5_bar_1_bar_60 - location
        loc_bar__minus_2_bar__minus_9_bar_1_bar__minus_30 - location
        loc_bar__minus_3_bar__minus_8_bar_3_bar_15 - location
        loc_bar_3_bar__minus_4_bar_1_bar_60 - location
        loc_bar_2_bar_4_bar_0_bar_30 - location
        )
    

(:init


        (receptacleType Drawer_bar__plus_00_dot_98_bar__plus_00_dot_40_bar__minus_01_dot_64 DrawerType)
        (receptacleType Shelf_bar__minus_01_dot_58_bar__plus_01_dot_51_bar__minus_02_dot_03 ShelfType)
        (receptacleType Dresser_bar__minus_01_dot_47_bar__plus_00_dot_03_bar__minus_02_dot_05 DresserType)
        (receptacleType Drawer_bar__minus_01_dot_35_bar__plus_00_dot_19_bar__minus_02_dot_05 DrawerType)
        (receptacleType Drawer_bar__plus_00_dot_98_bar__plus_00_dot_14_bar__minus_01_dot_64 DrawerType)
        (receptacleType Shelf_bar__plus_01_dot_26_bar__plus_01_dot_40_bar__minus_00_dot_26 ShelfType)
        (receptacleType Drawer_bar__plus_00_dot_98_bar__plus_00_dot_14_bar__minus_01_dot_96 DrawerType)
        (receptacleType Drawer_bar__plus_00_dot_98_bar__plus_00_dot_27_bar__minus_01_dot_64 DrawerType)
        (receptacleType Shelf_bar__plus_01_dot_26_bar__plus_00_dot_38_bar__minus_00_dot_26 ShelfType)
        (receptacleType GarbageCan_bar__minus_01_dot_57_bar__plus_00_dot_00_bar__plus_01_dot_94 GarbageCanType)
        (receptacleType Drawer_bar__plus_00_dot_98_bar__plus_00_dot_27_bar__minus_01_dot_96 DrawerType)
        (receptacleType Shelf_bar__plus_01_dot_22_bar__plus_00_dot_86_bar__minus_01_dot_80 ShelfType)
        (receptacleType Drawer_bar__plus_00_dot_98_bar__plus_00_dot_40_bar__minus_01_dot_96 DrawerType)
        (receptacleType Shelf_bar__plus_01_dot_26_bar__plus_00_dot_90_bar__minus_00_dot_27 ShelfType)
        (receptacleType Desk_bar__plus_00_dot_45_bar_00_dot_00_bar__minus_02_dot_53 DeskType)
        (receptacleType Bed_bar__minus_01_dot_21_bar__plus_00_dot_02_bar__minus_00_dot_27 BedType)
        (receptacleType Drawer_bar__minus_01_dot_35_bar__plus_00_dot_48_bar__minus_02_dot_05 DrawerType)
        (objectType CreditCard_bar__plus_01_dot_07_bar__plus_00_dot_50_bar__minus_01_dot_82 CreditCardType)
        (objectType KeyChain_bar__plus_01_dot_12_bar__plus_00_dot_51_bar__minus_02_dot_04 KeyChainType)
        (objectType Pen_bar__plus_01_dot_22_bar__plus_00_dot_11_bar__minus_01_dot_96 PenType)
        (objectType Window_bar__minus_00_dot_96_bar__plus_00_dot_96_bar__minus_02_dot_68 WindowType)
        (objectType Blinds_bar__minus_00_dot_35_bar__plus_02_dot_19_bar__minus_02_dot_69 BlindsType)
        (objectType Boots_bar__plus_01_dot_29_bar__plus_00_dot_00_bar__minus_00_dot_96 BootsType)
        (objectType Blinds_bar__plus_00_dot_25_bar__plus_02_dot_19_bar__minus_02_dot_69 BlindsType)
        (objectType Blinds_bar__minus_00_dot_95_bar__plus_02_dot_19_bar__minus_02_dot_69 BlindsType)
        (objectType CellPhone_bar__minus_01_dot_46_bar__plus_00_dot_70_bar__minus_02_dot_19 CellPhoneType)
        (objectType Laptop_bar__minus_01_dot_32_bar__plus_00_dot_61_bar__minus_01_dot_07 LaptopType)
        (objectType KeyChain_bar__minus_01_dot_27_bar__plus_00_dot_70_bar__minus_02_dot_48 KeyChainType)
        (objectType DeskLamp_bar__minus_01_dot_49_bar__plus_00_dot_69_bar__minus_02_dot_42 DeskLampType)
        (objectType CellPhone_bar__minus_01_dot_40_bar__plus_00_dot_40_bar__minus_02_dot_15 CellPhoneType)
        (objectType Chair_bar__plus_00_dot_28_bar__plus_00_dot_00_bar__minus_02_dot_26 ChairType)
        (objectType Pillow_bar__minus_01_dot_08_bar__plus_00_dot_71_bar__minus_00_dot_22 PillowType)
        (objectType Pencil_bar__plus_01_dot_29_bar__plus_00_dot_92_bar__minus_00_dot_54 PencilType)
        (objectType Book_bar__plus_00_dot_62_bar__plus_00_dot_57_bar__minus_02_dot_47 BookType)
        (objectType Window_bar__plus_00_dot_24_bar__plus_00_dot_96_bar__minus_02_dot_68 WindowType)
        (objectType BasketBall_bar__plus_01_dot_27_bar__plus_00_dot_12_bar__minus_01_dot_29 BasketBallType)
        (objectType AlarmClock_bar__minus_01_dot_58_bar__plus_00_dot_70_bar__minus_01_dot_90 AlarmClockType)
        (objectType Bowl_bar__minus_01_dot_33_bar__plus_00_dot_70_bar__minus_01_dot_90 BowlType)
        (objectType KeyChain_bar__minus_01_dot_33_bar__plus_00_dot_10_bar__minus_02_dot_15 KeyChainType)
        (objectType Bowl_bar__plus_01_dot_23_bar__plus_00_dot_91_bar__plus_00_dot_04 BowlType)
        (objectType BaseballBat_bar__plus_01_dot_44_bar__plus_00_dot_64_bar__plus_01_dot_99 BaseballBatType)
        (objectType CD_bar__plus_00_dot_31_bar__plus_00_dot_58_bar__minus_02_dot_54 CDType)
        (objectType Window_bar__minus_00_dot_36_bar__plus_00_dot_96_bar__minus_02_dot_68 WindowType)
        (objectType CD_bar__plus_01_dot_32_bar__plus_00_dot_50_bar__minus_01_dot_98 CDType)
        (objectType Mirror_bar__plus_00_dot_74_bar__plus_01_dot_49_bar__plus_02_dot_10 MirrorType)
        (objectType Pencil_bar__plus_01_dot_16_bar__plus_00_dot_39_bar__minus_00_dot_24 PencilType)
        (objectType CreditCard_bar__minus_01_dot_27_bar__plus_00_dot_70_bar__minus_02_dot_34 CreditCardType)
        (objectType LightSwitch_bar__minus_00_dot_76_bar__plus_01_dot_18_bar__plus_02_dot_10 LightSwitchType)
        (objectType Bowl_bar__plus_00_dot_94_bar__plus_00_dot_58_bar__minus_02_dot_54 BowlType)
        (objectType CreditCard_bar__plus_01_dot_29_bar__plus_01_dot_41_bar__minus_00_dot_67 CreditCardType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain DresserType PenType)
        (canContain DresserType BookType)
        (canContain DresserType BowlType)
        (canContain DresserType CDType)
        (canContain DresserType CellPhoneType)
        (canContain DresserType KeyChainType)
        (canContain DresserType CreditCardType)
        (canContain DresserType BasketBallType)
        (canContain DresserType LaptopType)
        (canContain DresserType PencilType)
        (canContain DresserType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType CDType)
        (canContain GarbageCanType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain DeskType PenType)
        (canContain DeskType BookType)
        (canContain DeskType BowlType)
        (canContain DeskType CDType)
        (canContain DeskType CellPhoneType)
        (canContain DeskType KeyChainType)
        (canContain DeskType CreditCardType)
        (canContain DeskType BasketBallType)
        (canContain DeskType LaptopType)
        (canContain DeskType PencilType)
        (canContain DeskType AlarmClockType)
        (canContain BedType BasketBallType)
        (canContain BedType BaseballBatType)
        (canContain BedType LaptopType)
        (canContain BedType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType PillowType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (pickupable CreditCard_bar__plus_01_dot_07_bar__plus_00_dot_50_bar__minus_01_dot_82)
        (pickupable KeyChain_bar__plus_01_dot_12_bar__plus_00_dot_51_bar__minus_02_dot_04)
        (pickupable Pen_bar__plus_01_dot_22_bar__plus_00_dot_11_bar__minus_01_dot_96)
        (pickupable Boots_bar__plus_01_dot_29_bar__plus_00_dot_00_bar__minus_00_dot_96)
        (pickupable CellPhone_bar__minus_01_dot_46_bar__plus_00_dot_70_bar__minus_02_dot_19)
        (pickupable Laptop_bar__minus_01_dot_32_bar__plus_00_dot_61_bar__minus_01_dot_07)
        (pickupable KeyChain_bar__minus_01_dot_27_bar__plus_00_dot_70_bar__minus_02_dot_48)
        (pickupable CellPhone_bar__minus_01_dot_40_bar__plus_00_dot_40_bar__minus_02_dot_15)
        (pickupable Pillow_bar__minus_01_dot_08_bar__plus_00_dot_71_bar__minus_00_dot_22)
        (pickupable Pencil_bar__plus_01_dot_29_bar__plus_00_dot_92_bar__minus_00_dot_54)
        (pickupable Book_bar__plus_00_dot_62_bar__plus_00_dot_57_bar__minus_02_dot_47)
        (pickupable BasketBall_bar__plus_01_dot_27_bar__plus_00_dot_12_bar__minus_01_dot_29)
        (pickupable AlarmClock_bar__minus_01_dot_58_bar__plus_00_dot_70_bar__minus_01_dot_90)
        (pickupable Bowl_bar__minus_01_dot_33_bar__plus_00_dot_70_bar__minus_01_dot_90)
        (pickupable KeyChain_bar__minus_01_dot_33_bar__plus_00_dot_10_bar__minus_02_dot_15)
        (pickupable Bowl_bar__plus_01_dot_23_bar__plus_00_dot_91_bar__plus_00_dot_04)
        (pickupable BaseballBat_bar__plus_01_dot_44_bar__plus_00_dot_64_bar__plus_01_dot_99)
        (pickupable CD_bar__plus_00_dot_31_bar__plus_00_dot_58_bar__minus_02_dot_54)
        (pickupable CD_bar__plus_01_dot_32_bar__plus_00_dot_50_bar__minus_01_dot_98)
        (pickupable Pencil_bar__plus_01_dot_16_bar__plus_00_dot_39_bar__minus_00_dot_24)
        (pickupable CreditCard_bar__minus_01_dot_27_bar__plus_00_dot_70_bar__minus_02_dot_34)
        (pickupable Bowl_bar__plus_00_dot_94_bar__plus_00_dot_58_bar__minus_02_dot_54)
        (pickupable CreditCard_bar__plus_01_dot_29_bar__plus_01_dot_41_bar__minus_00_dot_67)
        (isReceptacleObject Bowl_bar__minus_01_dot_33_bar__plus_00_dot_70_bar__minus_01_dot_90)
        (isReceptacleObject Bowl_bar__plus_01_dot_23_bar__plus_00_dot_91_bar__plus_00_dot_04)
        (isReceptacleObject Bowl_bar__plus_00_dot_94_bar__plus_00_dot_58_bar__minus_02_dot_54)
        (openable Drawer_bar__plus_00_dot_98_bar__plus_00_dot_40_bar__minus_01_dot_64)
        (openable Drawer_bar__minus_01_dot_35_bar__plus_00_dot_19_bar__minus_02_dot_05)
        (openable Drawer_bar__plus_00_dot_98_bar__plus_00_dot_14_bar__minus_01_dot_64)
        (openable Drawer_bar__plus_00_dot_98_bar__plus_00_dot_14_bar__minus_01_dot_96)
        (openable Drawer_bar__plus_00_dot_98_bar__plus_00_dot_27_bar__minus_01_dot_64)
        (openable Drawer_bar__plus_00_dot_98_bar__plus_00_dot_27_bar__minus_01_dot_96)
        (openable Drawer_bar__plus_00_dot_98_bar__plus_00_dot_40_bar__minus_01_dot_96)
        
        (atLocation agent1 loc_bar_2_bar_4_bar_0_bar_30)
        
        (cleanable Bowl_bar__minus_01_dot_33_bar__plus_00_dot_70_bar__minus_01_dot_90)
        (cleanable Bowl_bar__plus_01_dot_23_bar__plus_00_dot_91_bar__plus_00_dot_04)
        (cleanable Bowl_bar__plus_00_dot_94_bar__plus_00_dot_58_bar__minus_02_dot_54)
        
        
        (coolable Bowl_bar__minus_01_dot_33_bar__plus_00_dot_70_bar__minus_01_dot_90)
        (coolable Bowl_bar__plus_01_dot_23_bar__plus_00_dot_91_bar__plus_00_dot_04)
        (coolable Bowl_bar__plus_00_dot_94_bar__plus_00_dot_58_bar__minus_02_dot_54)
        
        
        (toggleable DeskLamp_bar__minus_01_dot_49_bar__plus_00_dot_69_bar__minus_02_dot_42)
        
        
        
        
        (inReceptacle CellPhone_bar__minus_01_dot_40_bar__plus_00_dot_40_bar__minus_02_dot_15 Drawer_bar__minus_01_dot_35_bar__plus_00_dot_48_bar__minus_02_dot_05)
        (inReceptacle Bowl_bar__plus_00_dot_94_bar__plus_00_dot_58_bar__minus_02_dot_54 Desk_bar__plus_00_dot_45_bar_00_dot_00_bar__minus_02_dot_53)
        (inReceptacle CD_bar__plus_00_dot_31_bar__plus_00_dot_58_bar__minus_02_dot_54 Desk_bar__plus_00_dot_45_bar_00_dot_00_bar__minus_02_dot_53)
        (inReceptacle Book_bar__plus_00_dot_62_bar__plus_00_dot_57_bar__minus_02_dot_47 Desk_bar__plus_00_dot_45_bar_00_dot_00_bar__minus_02_dot_53)
        (inReceptacle CellPhone_bar__minus_01_dot_46_bar__plus_00_dot_70_bar__minus_02_dot_19 Dresser_bar__minus_01_dot_47_bar__plus_00_dot_03_bar__minus_02_dot_05)
        (inReceptacle AlarmClock_bar__minus_01_dot_58_bar__plus_00_dot_70_bar__minus_01_dot_90 Dresser_bar__minus_01_dot_47_bar__plus_00_dot_03_bar__minus_02_dot_05)
        (inReceptacle KeyChain_bar__minus_01_dot_27_bar__plus_00_dot_70_bar__minus_02_dot_48 Dresser_bar__minus_01_dot_47_bar__plus_00_dot_03_bar__minus_02_dot_05)
        (inReceptacle Bowl_bar__minus_01_dot_33_bar__plus_00_dot_70_bar__minus_01_dot_90 Dresser_bar__minus_01_dot_47_bar__plus_00_dot_03_bar__minus_02_dot_05)
        (inReceptacle CreditCard_bar__minus_01_dot_27_bar__plus_00_dot_70_bar__minus_02_dot_34 Dresser_bar__minus_01_dot_47_bar__plus_00_dot_03_bar__minus_02_dot_05)
        (inReceptacle DeskLamp_bar__minus_01_dot_49_bar__plus_00_dot_69_bar__minus_02_dot_42 Dresser_bar__minus_01_dot_47_bar__plus_00_dot_03_bar__minus_02_dot_05)
        (inReceptacle Pen_bar__plus_01_dot_22_bar__plus_00_dot_11_bar__minus_01_dot_96 Drawer_bar__plus_00_dot_98_bar__plus_00_dot_14_bar__minus_01_dot_96)
        (inReceptacle Pillow_bar__minus_01_dot_08_bar__plus_00_dot_71_bar__minus_00_dot_22 Bed_bar__minus_01_dot_21_bar__plus_00_dot_02_bar__minus_00_dot_27)
        (inReceptacle Laptop_bar__minus_01_dot_32_bar__plus_00_dot_61_bar__minus_01_dot_07 Bed_bar__minus_01_dot_21_bar__plus_00_dot_02_bar__minus_00_dot_27)
        (inReceptacle KeyChain_bar__minus_01_dot_33_bar__plus_00_dot_10_bar__minus_02_dot_15 Drawer_bar__minus_01_dot_35_bar__plus_00_dot_19_bar__minus_02_dot_05)
        (inReceptacle KeyChain_bar__plus_01_dot_12_bar__plus_00_dot_51_bar__minus_02_dot_04 Shelf_bar__plus_01_dot_22_bar__plus_00_dot_86_bar__minus_01_dot_80)
        (inReceptacle CD_bar__plus_01_dot_32_bar__plus_00_dot_50_bar__minus_01_dot_98 Shelf_bar__plus_01_dot_22_bar__plus_00_dot_86_bar__minus_01_dot_80)
        (inReceptacle CreditCard_bar__plus_01_dot_07_bar__plus_00_dot_50_bar__minus_01_dot_82 Shelf_bar__plus_01_dot_22_bar__plus_00_dot_86_bar__minus_01_dot_80)
        (inReceptacle Bowl_bar__plus_01_dot_23_bar__plus_00_dot_91_bar__plus_00_dot_04 Shelf_bar__plus_01_dot_26_bar__plus_00_dot_90_bar__minus_00_dot_27)
        (inReceptacle Pencil_bar__plus_01_dot_29_bar__plus_00_dot_92_bar__minus_00_dot_54 Shelf_bar__plus_01_dot_26_bar__plus_00_dot_90_bar__minus_00_dot_27)
        (inReceptacle Pencil_bar__plus_01_dot_16_bar__plus_00_dot_39_bar__minus_00_dot_24 Shelf_bar__plus_01_dot_26_bar__plus_00_dot_38_bar__minus_00_dot_26)
        (inReceptacle CreditCard_bar__plus_01_dot_29_bar__plus_01_dot_41_bar__minus_00_dot_67 Shelf_bar__plus_01_dot_26_bar__plus_01_dot_40_bar__minus_00_dot_26)
        
        
        (receptacleAtLocation Bed_bar__minus_01_dot_21_bar__plus_00_dot_02_bar__minus_00_dot_27 loc_bar_0_bar_0_bar_3_bar_60)
        (receptacleAtLocation Desk_bar__plus_00_dot_45_bar_00_dot_00_bar__minus_02_dot_53 loc_bar_2_bar__minus_6_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_98_bar__plus_00_dot_14_bar__minus_01_dot_64 loc_bar_0_bar__minus_5_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_98_bar__plus_00_dot_14_bar__minus_01_dot_96 loc_bar_0_bar__minus_6_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_98_bar__plus_00_dot_27_bar__minus_01_dot_64 loc_bar__minus_1_bar__minus_5_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__plus_00_dot_98_bar__plus_00_dot_27_bar__minus_01_dot_96 loc_bar_0_bar__minus_6_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_98_bar__plus_00_dot_40_bar__minus_01_dot_64 loc_bar_1_bar__minus_5_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_98_bar__plus_00_dot_40_bar__minus_01_dot_96 loc_bar_1_bar__minus_6_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_35_bar__plus_00_dot_19_bar__minus_02_dot_05 loc_bar__minus_1_bar__minus_7_bar_3_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_35_bar__plus_00_dot_48_bar__minus_02_dot_05 loc_bar__minus_3_bar__minus_8_bar_3_bar_60)
        (receptacleAtLocation Dresser_bar__minus_01_dot_47_bar__plus_00_dot_03_bar__minus_02_dot_05 loc_bar__minus_3_bar__minus_8_bar_3_bar_60)
        (receptacleAtLocation GarbageCan_bar__minus_01_dot_57_bar__plus_00_dot_00_bar__plus_01_dot_94 loc_bar__minus_2_bar_5_bar_3_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_22_bar__plus_00_dot_86_bar__minus_01_dot_80 loc_bar_2_bar__minus_6_bar_1_bar_45)
        (receptacleAtLocation Shelf_bar__plus_01_dot_26_bar__plus_00_dot_38_bar__minus_00_dot_26 loc_bar_2_bar__minus_1_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_26_bar__plus_00_dot_90_bar__minus_00_dot_27 loc_bar_2_bar__minus_1_bar_1_bar_45)
        (receptacleAtLocation Shelf_bar__plus_01_dot_26_bar__plus_01_dot_40_bar__minus_00_dot_26 loc_bar_2_bar__minus_1_bar_1_bar_15)
        (receptacleAtLocation Shelf_bar__minus_01_dot_58_bar__plus_01_dot_51_bar__minus_02_dot_03 loc_bar__minus_3_bar__minus_8_bar_3_bar_15)
        (objectAtLocation Bowl_bar__plus_01_dot_23_bar__plus_00_dot_91_bar__plus_00_dot_04 loc_bar_2_bar__minus_1_bar_1_bar_45)
        (objectAtLocation CD_bar__plus_01_dot_32_bar__plus_00_dot_50_bar__minus_01_dot_98 loc_bar_2_bar__minus_6_bar_1_bar_45)
        (objectAtLocation CreditCard_bar__plus_01_dot_29_bar__plus_01_dot_41_bar__minus_00_dot_67 loc_bar_2_bar__minus_1_bar_1_bar_15)
        (objectAtLocation CellPhone_bar__minus_01_dot_40_bar__plus_00_dot_40_bar__minus_02_dot_15 loc_bar__minus_3_bar__minus_8_bar_3_bar_60)
        (objectAtLocation Pencil_bar__plus_01_dot_16_bar__plus_00_dot_39_bar__minus_00_dot_24 loc_bar_2_bar__minus_1_bar_1_bar_60)
        (objectAtLocation KeyChain_bar__plus_01_dot_12_bar__plus_00_dot_51_bar__minus_02_dot_04 loc_bar_2_bar__minus_6_bar_1_bar_45)
        (objectAtLocation CreditCard_bar__minus_01_dot_27_bar__plus_00_dot_70_bar__minus_02_dot_34 loc_bar__minus_3_bar__minus_8_bar_3_bar_60)
        (objectAtLocation KeyChain_bar__minus_01_dot_33_bar__plus_00_dot_10_bar__minus_02_dot_15 loc_bar__minus_1_bar__minus_7_bar_3_bar_60)
        (objectAtLocation Bowl_bar__minus_01_dot_33_bar__plus_00_dot_70_bar__minus_01_dot_90 loc_bar__minus_3_bar__minus_8_bar_3_bar_60)
        (objectAtLocation Book_bar__plus_00_dot_62_bar__plus_00_dot_57_bar__minus_02_dot_47 loc_bar_2_bar__minus_6_bar_2_bar_60)
        (objectAtLocation Mirror_bar__plus_00_dot_74_bar__plus_01_dot_49_bar__plus_02_dot_10 loc_bar_3_bar_6_bar_0_bar_15)
        (objectAtLocation Chair_bar__plus_00_dot_28_bar__plus_00_dot_00_bar__minus_02_dot_26 loc_bar__minus_2_bar__minus_9_bar_1_bar_60)
        (objectAtLocation KeyChain_bar__minus_01_dot_27_bar__plus_00_dot_70_bar__minus_02_dot_48 loc_bar__minus_3_bar__minus_8_bar_3_bar_60)
        (objectAtLocation BaseballBat_bar__plus_01_dot_44_bar__plus_00_dot_64_bar__plus_01_dot_99 loc_bar_4_bar_6_bar_0_bar_60)
        (objectAtLocation BasketBall_bar__plus_01_dot_27_bar__plus_00_dot_12_bar__minus_01_dot_29 loc_bar_2_bar__minus_5_bar_1_bar_60)
        (objectAtLocation Pencil_bar__plus_01_dot_29_bar__plus_00_dot_92_bar__minus_00_dot_54 loc_bar_2_bar__minus_1_bar_1_bar_45)
        (objectAtLocation CellPhone_bar__minus_01_dot_46_bar__plus_00_dot_70_bar__minus_02_dot_19 loc_bar__minus_3_bar__minus_8_bar_3_bar_60)
        (objectAtLocation CreditCard_bar__plus_01_dot_07_bar__plus_00_dot_50_bar__minus_01_dot_82 loc_bar_2_bar__minus_6_bar_1_bar_45)
        (objectAtLocation Laptop_bar__minus_01_dot_32_bar__plus_00_dot_61_bar__minus_01_dot_07 loc_bar_0_bar_0_bar_3_bar_60)
        (objectAtLocation Pillow_bar__minus_01_dot_08_bar__plus_00_dot_71_bar__minus_00_dot_22 loc_bar_0_bar_0_bar_3_bar_60)
        (objectAtLocation LightSwitch_bar__minus_00_dot_76_bar__plus_01_dot_18_bar__plus_02_dot_10 loc_bar__minus_3_bar_6_bar_0_bar_45)
        (objectAtLocation Boots_bar__plus_01_dot_29_bar__plus_00_dot_00_bar__minus_00_dot_96 loc_bar_3_bar__minus_4_bar_1_bar_60)
        (objectAtLocation Pen_bar__plus_01_dot_22_bar__plus_00_dot_11_bar__minus_01_dot_96 loc_bar_0_bar__minus_6_bar_1_bar_60)
        (objectAtLocation DeskLamp_bar__minus_01_dot_49_bar__plus_00_dot_69_bar__minus_02_dot_42 loc_bar__minus_3_bar__minus_8_bar_3_bar_60)
        (objectAtLocation AlarmClock_bar__minus_01_dot_58_bar__plus_00_dot_70_bar__minus_01_dot_90 loc_bar__minus_3_bar__minus_8_bar_3_bar_60)
        (objectAtLocation CD_bar__plus_00_dot_31_bar__plus_00_dot_58_bar__minus_02_dot_54 loc_bar_2_bar__minus_6_bar_2_bar_60)
        (objectAtLocation Window_bar__plus_00_dot_24_bar__plus_00_dot_96_bar__minus_02_dot_68 loc_bar__minus_2_bar__minus_9_bar_1_bar_45)
        (objectAtLocation Window_bar__minus_00_dot_36_bar__plus_00_dot_96_bar__minus_02_dot_68 loc_bar__minus_2_bar__minus_9_bar_2_bar_60)
        (objectAtLocation Window_bar__minus_00_dot_96_bar__plus_00_dot_96_bar__minus_02_dot_68 loc_bar__minus_3_bar__minus_9_bar_2_bar_60)
        (objectAtLocation Bowl_bar__plus_00_dot_94_bar__plus_00_dot_58_bar__minus_02_dot_54 loc_bar_2_bar__minus_6_bar_2_bar_60)
        (objectAtLocation Blinds_bar__minus_00_dot_35_bar__plus_02_dot_19_bar__minus_02_dot_69 loc_bar__minus_2_bar__minus_9_bar_2_bar__minus_30)
        (objectAtLocation Blinds_bar__plus_00_dot_25_bar__plus_02_dot_19_bar__minus_02_dot_69 loc_bar__minus_2_bar__minus_9_bar_1_bar__minus_30)
        (objectAtLocation Blinds_bar__minus_00_dot_95_bar__plus_02_dot_19_bar__minus_02_dot_69 loc_bar__minus_3_bar__minus_9_bar_2_bar__minus_30)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 PencilType)
                                    (receptacleType ?r DeskType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 PencilType)
                                            (receptacleType ?r DeskType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            