{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000254.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000255.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000256.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000258.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000259.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000260.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000261.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000262.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000263.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000264.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000265.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000266.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000267.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000268.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000269.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000270.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000271.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000272.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000273.png", "low_idx": 38}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON>g", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-8|-4|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["mug"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON>g", [-7.84, -7.84, -6.57618856, -6.57618856, 1.0558444, 1.0558444]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-8.54, -8.54, -6.764, -6.764, 0.0, 0.0]], "forceVisible": true, "objectId": "Mug|-01.96|+00.26|-01.64"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-4|2|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["mug"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-2|2|60"}}, {"discrete_action": {"action": "PutObject", "args": ["mug", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON>g", [-7.84, -7.84, -6.57618856, -6.57618856, 1.0558444, 1.0558444]], "coordinateReceptacleObjectId": ["Cabinet", [-1.586, -1.586, -5.40400028, -5.40400028, 1.610000132, 1.610000132]], "forceVisible": true, "objectId": "Mug|-01.96|+00.26|-01.64", "receptacleObjectId": "Cabinet|-00.40|+00.40|-01.35"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.14|+00.00|-01.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 235], "mask": [[0, 22500], [22501, 299], [22802, 298], [23102, 298], [23403, 297], [23704, 296], [24004, 296], [24305, 295], [24606, 294], [24906, 294], [25207, 293], [25508, 292], [25808, 292], [26109, 291], [26410, 290], [26710, 290], [27011, 289], [27312, 288], [27612, 288], [27913, 287], [28214, 286], [28514, 286], [28815, 285], [29116, 284], [29416, 284], [29717, 283], [30018, 282], [30318, 282], [30619, 281], [30920, 280], [31220, 280], [31521, 279], [31822, 278], [32122, 278], [32423, 277], [32724, 276], [33024, 276], [33325, 275], [33626, 274], [33926, 274], [34227, 273], [34528, 272], [34828, 272], [35129, 271], [35429, 271], [35730, 270], [36031, 269], [36331, 269], [36632, 268], [36933, 267], [37233, 267], [37534, 266], [37835, 265], [38135, 265], [38436, 264], [38737, 263], [39037, 263], [39338, 262], [39639, 261], [39939, 261], [40240, 260], [40541, 259], [40841, 259], [41142, 258], [41443, 257], [41743, 257], [42044, 256], [42345, 255], [42645, 255], [42946, 254], [43247, 253], [43547, 253], [43848, 252], [44149, 251], [44449, 251], [44750, 250], [45051, 249], [45351, 249], [45652, 248], [45953, 247], [46253, 247], [46554, 246], [46855, 245], [47155, 245], [47456, 244], [47757, 243], [48057, 243], [48358, 242], [48659, 241], [48959, 241], [49260, 240], [49561, 239], [49861, 239], [50162, 238], [50463, 237], [50763, 237], [51064, 236], [51365, 235], [51665, 235], [51966, 234], [52267, 233], [52567, 233], [52868, 232], [53169, 231], [53469, 231], [53770, 230], [54071, 229], [54371, 229], [54672, 228], [54973, 227], [55273, 227], [55574, 226], [55875, 225], [56175, 225], [56476, 224], [56777, 223], [57077, 222], [57378, 220], [57679, 217], [57979, 216], [58280, 214], [58581, 211], [58881, 210], [59182, 207], [59483, 205], [59783, 204], [60084, 201], [60385, 199], [60685, 198], [60986, 195], [61287, 191], [61587, 180], [61772, 1], [61888, 178], [62189, 175], [62489, 174], [62790, 172], [63090, 171], [63390, 169], [63691, 167], [63991, 166], [64292, 164], [64592, 162], [64893, 161], [65193, 161], [65495, 159], [65795, 157], [66096, 155], [66397, 153], [66697, 151], [66998, 149], [67299, 147], [67602, 141], [67906, 133], [68209, 127], [68512, 121], [68816, 113], [69122, 102], [69427, 91], [69735, 76], [70042, 61], [70353, 40]], "point": [149, 117]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Mug|-01.96|+00.26|-01.64"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [134, 153, 154, 174], "mask": [[45739, 3], [45745, 1], [47534, 17], [47834, 20], [48134, 21], [48434, 16], [48452, 2], [48735, 15], [48752, 2], [49035, 15], [49051, 2], [49335, 17], [49635, 16], [49935, 15], [50236, 14], [50536, 14], [50836, 14], [51136, 13], [51437, 11], [51738, 9], [52040, 5]], "point": [144, 162]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.14|+00.00|-01.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 11534], [11539, 3], [11547, 287], [11839, 3], [11847, 282], [12130, 4], [12139, 3], [12147, 281], [12430, 4], [12439, 3], [12447, 4], [12452, 274], [12730, 4], [12739, 3], [12747, 4], [12754, 271], [13030, 4], [13038, 4], [13047, 4], [13055, 270], [13330, 4], [13338, 4], [13347, 4], [13356, 269], [13630, 4], [13638, 4], [13647, 4], [13656, 269], [13930, 3], [13938, 4], [13947, 4], [13956, 269], [14230, 3], [14238, 4], [14247, 4], [14256, 269], [14530, 3], [14538, 4], [14547, 4], [14556, 264], [14821, 4], [14830, 3], [14838, 4], [14847, 4], [14856, 264], [15121, 4], [15130, 3], [15138, 4], [15147, 4], [15156, 263], [15421, 4], [15429, 4], [15438, 4], [15447, 4], [15456, 263], [15721, 4], [15729, 4], [15738, 4], [15747, 4], [15756, 263], [16021, 4], [16029, 4], [16038, 4], [16047, 4], [16056, 262], [16321, 4], [16329, 4], [16338, 4], [16347, 4], [16356, 262], [16621, 3], [16629, 4], [16638, 4], [16647, 4], [16656, 262], [16921, 3], [16929, 4], [16938, 4], [16947, 4], [16956, 262], [17220, 4], [17229, 4], [17238, 4], [17247, 4], [17256, 262], [17520, 4], [17529, 4], [17538, 4], [17547, 4], [17556, 4], [17561, 257], [17820, 4], [17829, 4], [17838, 4], [17847, 4], [17856, 4], [17861, 257], [18120, 4], [18129, 4], [18138, 4], [18147, 4], [18157, 3], [18161, 256], [18420, 4], [18429, 4], [18438, 4], [18447, 4], [18457, 3], [18461, 256], [18720, 4], [18729, 4], [18738, 4], [18747, 4], [18757, 3], [18762, 2355], [21161, 256], [21460, 257], [21760, 258], [22060, 258], [22360, 258], [22659, 260], [22959, 260], [23259, 261], [23558, 262], [23858, 262], [24158, 263], [24457, 264], [24757, 265], [25056, 267], [25355, 268], [25655, 269], [25954, 272], [26253, 273], [26552, 275], [26851, 277], [27150, 279], [27449, 281], [27748, 283], [28047, 285], [28345, 289], [28643, 2994], [31638, 2], [31641, 4], [31648, 3], [31654, 4], [31659, 2], [31662, 275], [31938, 2], [31941, 4], [31948, 3], [31954, 4], [31959, 2], [31962, 4], [31968, 269], [32238, 2], [32241, 4], [32248, 3], [32254, 4], [32259, 2], [32262, 4], [32268, 3], [32274, 263], [32538, 2], [32541, 4], [32548, 3], [32554, 4], [32559, 2], [32562, 4], [32569, 2], [32574, 35], [32610, 3], [32617, 220], [32838, 2], [32841, 4], [32848, 3], [32854, 4], [32859, 2], [32862, 4], [32869, 3], [32874, 33], [32911, 3], [32917, 3], [32921, 216], [33138, 2], [33141, 4], [33148, 3], [33154, 4], [33159, 2], [33162, 4], [33169, 3], [33174, 33], [33211, 3], [33217, 3], [33223, 214], [33438, 2], [33441, 4], [33448, 3], [33454, 4], [33459, 2], [33462, 4], [33469, 3], [33474, 33], [33511, 3], [33517, 3], [33524, 213], [33738, 2], [33741, 4], [33748, 3], [33754, 4], [33759, 2], [33762, 4], [33769, 3], [33775, 32], [33811, 3], [33818, 3], [33824, 213], [34038, 2], [34041, 4], [34048, 3], [34054, 4], [34059, 2], [34062, 4], [34069, 3], [34075, 28], [34104, 3], [34111, 3], [34118, 3], [34124, 213], [34338, 2], [34341, 4], [34348, 3], [34354, 4], [34359, 2], [34362, 4], [34369, 3], [34375, 27], [34404, 3], [34411, 3], [34418, 3], [34425, 212], [34638, 2], [34641, 4], [34648, 3], [34654, 4], [34659, 2], [34662, 4], [34669, 3], [34675, 27], [34704, 3], [34711, 3], [34718, 3], [34725, 212], [34938, 2], [34941, 4], [34948, 3], [34954, 4], [34959, 2], [34962, 4], [34969, 3], [34975, 27], [35005, 3], [35012, 3], [35018, 3], [35025, 212], [35238, 2], [35241, 4], [35248, 3], [35254, 4], [35259, 2], [35262, 4], [35269, 3], [35275, 26], [35305, 3], [35312, 3], [35318, 3], [35325, 212], [35538, 2], [35541, 4], [35548, 3], [35554, 4], [35559, 2], [35562, 4], [35569, 3], [35575, 26], [35605, 3], [35612, 3], [35619, 3], [35625, 1708], [37338, 2], [37343, 2], [37348, 3], [37354, 2], [37359, 2], [37364, 3], [37370, 2], [37375, 27], [37425, 207], [37638, 2], [37643, 2], [37648, 3], [37654, 2], [37659, 2], [37664, 3], [37670, 2], [37675, 27], [37724, 208], [37938, 2], [37943, 2], [37948, 3], [37954, 2], [37959, 2], [37965, 2], [37970, 2], [37975, 28], [38023, 209], [38238, 2], [38243, 2], [38248, 3], [38254, 2], [38259, 2], [38265, 2], [38270, 2], [38275, 29], [38322, 210], [38538, 2], [38543, 2], [38548, 3], [38554, 2], [38559, 2], [38565, 2], [38570, 2], [38575, 30], [38621, 211], [38838, 2], [38843, 2], [38848, 3], [38854, 2], [38859, 2], [38865, 2], [38870, 3], [38876, 30], [38920, 211], [39138, 2], [39143, 2], [39148, 3], [39154, 2], [39159, 2], [39165, 2], [39170, 3], [39176, 31], [39219, 212], [39438, 2], [39443, 2], [39448, 3], [39454, 2], [39459, 2], [39465, 2], [39470, 3], [39476, 34], [39516, 215], [39738, 1], [39743, 2], [39748, 3], [39754, 2], [39759, 2], [39765, 2], [39770, 3], [39776, 255], [40043, 2], [40048, 3], [40054, 2], [40059, 3], [40065, 2], [40070, 3], [40076, 256], [40349, 2], [40354, 2], [40359, 3], [40365, 2], [40370, 3], [40376, 264], [40654, 2], [40659, 3], [40665, 2], [40671, 2], [40676, 271], [40949, 1], [40960, 2], [40965, 2], [40971, 2], [40976, 280], [41260, 1], [41265, 2], [41271, 2], [41276, 287], [41565, 2], [41571, 2], [41576, 297], [41876, 3723], [45600, 298], [45900, 297], [46200, 296], [46500, 295], [46800, 294], [47100, 293], [47400, 292], [47700, 291], [48000, 290], [48300, 289], [48600, 288], [48900, 287], [49200, 286], [49500, 285], [49800, 284], [50100, 283], [50400, 282], [50700, 281], [51000, 280], [51300, 279], [51600, 278], [51900, 277], [52200, 276], [52500, 275], [52800, 274], [53100, 273], [53400, 272], [53700, 271], [54000, 270], [54300, 269], [54600, 268], [54900, 267], [55200, 266], [55500, 265], [55800, 264], [56100, 263], [56400, 262], [56700, 261], [57000, 260], [57300, 259], [57600, 258], [57900, 257], [58200, 256], [58500, 255], [58800, 254], [59100, 253], [59400, 95], [59496, 156], [59700, 95], [59796, 155], [60000, 95], [60096, 155], [60300, 95], [60396, 155], [60600, 95], [60696, 155], [60900, 95], [60996, 155], [61200, 94], [61296, 155], [61500, 94], [61596, 156], [61800, 94], [61895, 157], [62100, 94], [62195, 157], [62400, 94], [62495, 53], [62552, 100], [62700, 94], [62795, 45], [62860, 92], [63000, 94], [63095, 43], [63162, 91], [63300, 94], [63395, 40], [63465, 88], [63600, 94], [63695, 38], [63767, 86], [63900, 93], [63995, 36], [64069, 84], [64200, 93], [64295, 34], [64371, 82], [64500, 93], [64595, 33], [64672, 82], [64800, 93], [64894, 33], [64973, 81], [65100, 93], [65194, 31], [65275, 79], [65400, 93], [65495, 29], [65576, 78], [65700, 93], [65795, 28], [65877, 75], [66000, 93], [66096, 26], [66178, 73], [66300, 92], [66397, 24], [66479, 71], [66600, 92], [66697, 23], [66780, 68], [66900, 92], [66998, 22], [67080, 67], [67200, 92], [67299, 20], [67381, 65], [67500, 92], [67602, 16], [67682, 61], [67800, 92], [67906, 11], [67983, 56], [68100, 92], [68209, 8], [68283, 53], [68400, 92], [68512, 4], [68584, 49], [68700, 92], [68884, 45], [69000, 91], [69185, 39], [69300, 91], [69485, 33], [69600, 91], [69786, 25], [69900, 91], [70086, 17], [70200, 91], [70387, 6], [70500, 91], [70800, 91], [71100, 91], [71400, 90], [71700, 90], [72000, 90], [72300, 90], [72600, 90], [72900, 90], [73200, 90], [73500, 90], [73800, 89], [74100, 89], [74400, 89], [74700, 89], [75000, 89], [75300, 89], [75600, 89], [75900, 89], [76200, 89], [76500, 88], [76800, 88], [77100, 88], [77400, 88], [77700, 88], [78000, 88], [78300, 88], [78600, 88], [78900, 87], [79200, 87], [79500, 87], [79800, 87], [80100, 87], [80400, 87], [80700, 87], [81000, 87], [81300, 87], [81600, 86], [81900, 86], [82200, 86], [82500, 86], [82800, 86], [83100, 86], [83400, 86], [83700, 86], [84000, 85], [84300, 85], [84600, 85], [84900, 85], [85200, 85], [85500, 85], [85800, 85], [86100, 85], [86400, 84], [86700, 84], [87000, 84], [87300, 84], [87600, 84], [87900, 84], [88200, 84], [88500, 84], [88800, 84], [89100, 83], [89400, 83], [89700, 83]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|-01.96|+00.26|-01.64", "placeStationary": true, "receptacleObjectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 269], [25822, 270], [26122, 269], [26421, 270], [26720, 270], [27020, 270], [27319, 271], [27619, 270], [27918, 271], [28217, 271], [28517, 271], [28816, 271], [29115, 272], [29415, 273], [29714, 274], [30013, 275], [30313, 275], [30612, 276], [30911, 277], [31211, 277], [31510, 278], [31809, 279], [32109, 279], [32408, 280], [32708, 280], [33007, 280], [33306, 281], [33606, 281], [33905, 281], [34204, 282], [34504, 282], [34803, 282], [35102, 283], [35402, 282], [35701, 283], [36000, 283], [36300, 283], [36600, 282], [36900, 282], [37200, 281], [37500, 281], [37800, 280], [38100, 280], [38400, 280], [38700, 279], [39000, 279], [39300, 278], [39600, 278], [39900, 277], [40200, 277], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 269], [25822, 270], [26122, 269], [26421, 270], [26720, 270], [27020, 270], [27319, 271], [27619, 270], [27918, 271], [28217, 121], [28347, 141], [28517, 118], [28650, 138], [28816, 116], [28952, 135], [29115, 116], [29254, 133], [29415, 115], [29555, 133], [29714, 115], [29856, 132], [30013, 115], [30156, 132], [30313, 115], [30456, 132], [30612, 116], [30757, 131], [30911, 117], [31061, 127], [31211, 117], [31362, 126], [31510, 118], [31656, 3], [31663, 125], [31809, 119], [31956, 4], [31963, 125], [32109, 120], [32256, 4], [32263, 125], [32408, 121], [32556, 4], [32563, 125], [32708, 121], [32856, 3], [32862, 126], [33007, 122], [33156, 3], [33162, 125], [33306, 123], [33456, 3], [33461, 126], [33606, 123], [33755, 3], [33761, 126], [33905, 125], [34055, 3], [34061, 125], [34204, 126], [34355, 2], [34360, 126], [34504, 126], [34655, 2], [34660, 126], [34803, 127], [34955, 1], [34959, 126], [35102, 128], [35259, 126], [35402, 128], [35558, 126], [35701, 129], [35857, 127], [36000, 130], [36157, 126], [36300, 130], [36456, 127], [36600, 130], [36755, 127], [36900, 131], [37055, 127], [37200, 131], [37355, 126], [37500, 131], [37655, 126], [37800, 131], [37955, 125], [38100, 131], [38255, 125], [38400, 131], [38555, 125], [38700, 131], [38854, 125], [39000, 132], [39154, 125], [39300, 133], [39453, 125], [39600, 134], [39752, 126], [39900, 135], [40050, 127], [40200, 137], [40348, 129], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Mug|-01.96|+00.26|-01.64"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [128, 95, 162, 135], "mask": [[28338, 9], [28635, 15], [28932, 20], [29231, 23], [29530, 25], [29829, 27], [30128, 28], [30428, 28], [30728, 29], [31028, 33], [31328, 34], [31628, 28], [31659, 4], [31928, 28], [31960, 3], [32229, 27], [32260, 3], [32529, 27], [32560, 3], [32829, 27], [32859, 3], [33129, 27], [33159, 3], [33429, 27], [33459, 2], [33729, 26], [33758, 3], [34030, 25], [34058, 3], [34330, 25], [34357, 3], [34630, 25], [34657, 3], [34930, 25], [34956, 3], [35230, 29], [35530, 28], [35830, 27], [36130, 27], [36430, 26], [36730, 25], [37031, 24], [37331, 24], [37631, 24], [37931, 24], [38231, 24], [38531, 24], [38831, 23], [39132, 22], [39433, 20], [39734, 18], [40035, 15], [40337, 11]], "point": [145, 114]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 269], [25822, 270], [26122, 269], [26421, 270], [26720, 270], [27020, 270], [27319, 271], [27619, 270], [27918, 271], [28217, 271], [28517, 271], [28816, 271], [29115, 272], [29415, 273], [29714, 274], [30013, 275], [30313, 275], [30612, 276], [30911, 277], [31211, 277], [31510, 278], [31809, 279], [32109, 279], [32408, 280], [32708, 280], [33007, 280], [33306, 281], [33606, 281], [33905, 281], [34204, 282], [34504, 282], [34803, 282], [35102, 283], [35402, 282], [35701, 283], [36000, 283], [36300, 283], [36600, 282], [36900, 282], [37200, 281], [37500, 281], [37800, 280], [38100, 280], [38400, 280], [38700, 279], [39000, 279], [39300, 278], [39600, 278], [39900, 277], [40200, 277], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-00.40|+00.40|-01.35"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [17, 83, 116, 151], "mask": [[24617, 85], [24917, 85], [25217, 86], [25517, 86], [25817, 86], [26118, 85], [26419, 84], [26719, 85], [27020, 84], [27321, 83], [27621, 83], [27922, 82], [28222, 83], [28523, 82], [28824, 81], [29124, 81], [29425, 81], [29726, 80], [30026, 80], [30327, 79], [30627, 79], [30928, 79], [31229, 78], [31529, 78], [31830, 77], [32131, 77], [32431, 77], [32732, 76], [33032, 76], [33333, 75], [33634, 75], [33934, 75], [34235, 74], [34535, 74], [34836, 74], [35137, 73], [35437, 73], [35738, 72], [36039, 71], [36339, 72], [36640, 71], [36940, 71], [37241, 70], [37542, 70], [37842, 70], [38143, 69], [38444, 68], [38744, 68], [39045, 68], [39345, 68], [39646, 67], [39947, 66], [40247, 67], [40548, 66], [40849, 65], [41149, 65], [41450, 64], [41750, 65], [42051, 64], [42352, 63], [42652, 63], [42953, 63], [43253, 63], [43554, 62], [43855, 61], [44155, 61], [44456, 61], [44757, 60], [45057, 60]], "point": [66, 116]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|-01.96|+00.26|-01.64", "placeStationary": true, "receptacleObjectId": "Cabinet|-00.40|+00.40|-01.35"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [18, 83, 117, 211], "mask": [[24618, 84], [24918, 85], [25219, 85], [25520, 84], [25820, 84], [26121, 83], [26421, 83], [26722, 83], [27023, 82], [27323, 82], [27624, 81], [27925, 80], [28225, 81], [28526, 80], [28826, 80], [29127, 79], [29428, 79], [29728, 79], [30029, 78], [30329, 78], [30630, 77], [30931, 77], [31231, 77], [31532, 76], [31832, 76], [32133, 75], [32434, 75], [32734, 75], [33035, 74], [33335, 74], [33636, 74], [33937, 73], [34237, 73], [34538, 72], [34838, 72], [35139, 72], [35440, 71], [35740, 71], [36041, 70], [36342, 69], [36642, 70], [36943, 69], [37243, 69], [37544, 68], [37845, 68], [38145, 68], [38446, 67], [38746, 67], [39047, 66], [39348, 66], [39648, 66], [39949, 65], [40249, 65], [40550, 65], [40851, 64], [41151, 64], [41452, 63], [41752, 63], [42053, 63], [42354, 62], [42654, 62], [42955, 61], [43255, 61], [43556, 61], [43857, 60], [44157, 60], [44458, 59], [44791, 27], [45091, 27], [45391, 26], [45690, 27], [45990, 27], [46290, 27], [46590, 27], [46890, 27], [47190, 27], [47490, 27], [47789, 28], [48089, 28], [48389, 28], [48689, 28], [48989, 28], [49289, 28], [49590, 26], [49890, 26], [50191, 25], [50491, 25], [50792, 24], [51092, 24], [51392, 24], [51693, 23], [51993, 23], [52294, 22], [52594, 22], [52895, 21], [53195, 21], [53496, 20], [53796, 19], [54096, 19], [54397, 18], [54697, 18], [54998, 17], [55298, 17], [55599, 16], [55899, 16], [56200, 15], [56500, 15], [56800, 15], [57101, 14], [57401, 14], [57702, 13], [58002, 12], [58303, 11], [58603, 11], [58904, 10], [59204, 10], [59505, 9], [59805, 9], [60105, 9], [60406, 8], [60706, 8], [61007, 7], [61307, 7], [61608, 6], [61908, 6], [62209, 4], [62509, 4], [62809, 4], [63110, 3]], "point": [67, 146]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-00.40|+00.40|-01.35"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [18, 83, 117, 211], "mask": [[24618, 84], [24918, 85], [25219, 85], [25520, 84], [25820, 84], [26121, 83], [26421, 83], [26722, 83], [27023, 82], [27323, 82], [27624, 81], [27925, 80], [28225, 81], [28526, 80], [28826, 80], [29127, 79], [29428, 79], [29728, 79], [30029, 78], [30329, 78], [30630, 77], [30931, 77], [31231, 77], [31532, 76], [31832, 76], [32133, 75], [32434, 75], [32734, 75], [33035, 74], [33335, 74], [33636, 74], [33937, 73], [34237, 73], [34538, 72], [34838, 72], [35139, 72], [35440, 71], [35740, 71], [36041, 70], [36342, 69], [36642, 70], [36943, 69], [37243, 69], [37544, 38], [37590, 22], [37845, 35], [37891, 22], [38145, 34], [38191, 22], [38446, 33], [38492, 21], [38746, 32], [38792, 21], [39047, 31], [39093, 20], [39348, 30], [39393, 21], [39648, 30], [39693, 21], [39949, 30], [39993, 21], [40249, 30], [40293, 21], [40550, 30], [40593, 22], [40851, 29], [40893, 22], [41151, 30], [41192, 23], [41452, 29], [41492, 23], [41752, 30], [41792, 23], [42053, 29], [42092, 24], [42354, 29], [42392, 24], [42654, 30], [42692, 24], [42955, 31], [42990, 26], [43255, 61], [43556, 61], [43857, 60], [44157, 60], [44458, 59], [44791, 27], [45091, 27], [45391, 26], [45690, 27], [45990, 27], [46290, 27], [46590, 27], [46890, 27], [47190, 27], [47490, 27], [47789, 28], [48089, 28], [48389, 28], [48689, 28], [48989, 28], [49289, 28], [49590, 26], [49890, 26], [50191, 25], [50491, 25], [50792, 24], [51092, 24], [51392, 24], [51693, 23], [51993, 23], [52294, 22], [52594, 22], [52895, 21], [53195, 21], [53496, 20], [53796, 19], [54096, 19], [54397, 18], [54697, 18], [54998, 17], [55298, 17], [55599, 16], [55899, 16], [56200, 15], [56500, 15], [56800, 15], [57101, 14], [57401, 14], [57702, 13], [58002, 12], [58303, 11], [58603, 11], [58904, 10], [59204, 10], [59505, 9], [59805, 9], [60105, 9], [60406, 8], [60706, 8], [61007, 7], [61307, 7], [61608, 6], [61908, 6], [62209, 4], [62509, 4], [62809, 4], [63110, 3]], "point": [67, 146]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan11", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -1.0, "y": 0.9009992, "z": -0.25}, "object_poses": [{"objectName": "Potato_483d2614", "position": {"x": 0.8049864, "y": 0.9493862, "z": -1.78207254}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "DishSponge_932ef12a", "position": {"x": 1.9071784, "y": 0.9118642, "z": 0.706742644}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_932ef12a", "position": {"x": -2.47704625, "y": 0.95178926, "z": 0.614945531}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "ButterKnife_d4975865", "position": {"x": -2.06597686, "y": 0.9522671, "z": 0.614945531}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "ButterKnife_d4975865", "position": {"x": -2.340023, "y": 0.9522671, "z": 0.524996459}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_da74902f", "position": {"x": 1.361, "y": 0.962147355, "z": 0.5617}, "rotation": {"x": 0.0, "y": 269.9999, "z": 0.0}}, {"objectName": "Fork_834895c5", "position": {"x": 0.377125859, "y": 0.783435047, "z": -1.64893007}, "rotation": {"x": 1.40334191e-14, "y": 270.0, "z": 0.0}}, {"objectName": "Fork_834895c5", "position": {"x": 1.9071784, "y": 0.912848532, "z": 0.5409809}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_9fcd93ca", "position": {"x": 0.8049865, "y": 0.9206794, "z": -1.51627707}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_9fcd93ca", "position": {"x": -2.47704625, "y": 0.9533544, "z": 0.345098346}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_c3590fed", "position": {"x": -2.0475, "y": 0.5121605, "z": -1.80632138}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c3590fed", "position": {"x": 0.445706218, "y": 0.111044943, "z": 0.308500022}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_b9b64351", "position": {"x": -2.06597686, "y": 0.977574646, "z": 0.255149215}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_b9b64351", "position": {"x": 1.535548, "y": 0.9376496, "z": -1.82056189}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_09398926", "position": {"x": -0.296794683, "y": 0.06890199, "z": 0.6126471}, "rotation": {"x": 0.0, "y": 89.99995, "z": 0.0}}, {"objectName": "SoapBottle_09398926", "position": {"x": -1.92895365, "y": 0.9509215, "z": 0.345098257}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_929aede3", "position": {"x": -2.06597686, "y": 1.00127089, "z": 0.5249964}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PepperShaker_213d034a", "position": {"x": -2.203, "y": 0.9485312, "z": 0.165200174}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_22e053cf", "position": {"x": -1.95999992, "y": 0.8710422, "z": -1.76346016}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_841024df", "position": {"x": 1.79240572, "y": 0.103541851, "z": 0.274154127}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_841024df", "position": {"x": 0.6189993, "y": 0.945, "z": 0.469800025}, "rotation": {"x": 0.0, "y": 180.00032, "z": 0.0}}, {"objectName": "Mug_841024df", "position": {"x": -1.96, "y": 0.2639611, "z": -1.64404714}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_59b513fb", "position": {"x": -0.175111651, "y": 0.9951729, "z": -1.82056189}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_b9b64351", "position": {"x": -2.06597686, "y": 0.977574646, "z": 0.4350474}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Fork_834895c5", "position": {"x": 1.903969, "y": 0.912848532, "z": -1.40615737}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_c96a3c07", "position": {"x": 0.332690835, "y": 0.108118176, "z": -1.50970006}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Kettle_7922e01e", "position": {"x": -0.05555916, "y": 0.9, "z": 0.643867135}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_923adba4", "position": {"x": -2.47704625, "y": 0.9506611, "z": 0.165200233}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_929aede3", "position": {"x": -2.31, "y": 0.5603475, "z": -1.76346016}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_09398926", "position": {"x": -2.06597686, "y": 0.9509215, "z": 0.3450983}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_c3590fed", "position": {"x": 1.84613431, "y": 0.913158834, "z": 0.292338252}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_da74902f", "position": {"x": 1.0071, "y": 0.962147355, "z": 0.5617}, "rotation": {"x": 0.0, "y": 269.9999, "z": 0.0}}, {"objectName": "Spatula_b4e2a06a", "position": {"x": 1.72404635, "y": 0.927299857, "z": 0.706742644}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_54f2d061", "position": {"x": -2.61406946, "y": 1.05651045, "z": 0.255149245}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_7c2e9408", "position": {"x": 1.66300225, "y": 0.9474633, "z": 0.706742644}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_22e053cf", "position": {"x": -1.79193056, "y": 1.04246557, "z": 0.6149455}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_42ce5824", "position": {"x": -0.09697643, "y": 0.9086062, "z": 0.209457338}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_213d034a", "position": {"x": -0.09671791, "y": 0.107191741, "z": -1.54564261}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_41d4caea", "position": {"x": -1.79193056, "y": 1.0193764, "z": 0.345098257}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "ButterKnife_d4975865", "position": {"x": 0.432402283, "y": 0.9123421, "z": 0.209457338}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_483d2614", "position": {"x": 0.447480232, "y": 0.8062356, "z": -1.7455647}, "rotation": {"x": 1.40334191e-14, "y": 270.0, "z": 0.0}}, {"objectName": "Pan_4b842b85", "position": {"x": 1.022105, "y": 0.9591456, "z": 0.288545281}, "rotation": {"x": 0.0, "y": 219.19072, "z": 0.0}}, {"objectName": "DishSponge_932ef12a", "position": {"x": 0.11726848, "y": 0.7735838, "z": 0.2732857}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_9fcd93ca", "position": {"x": -0.632718563, "y": 0.7751493, "z": -1.47316957}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_841024df", "position": {"x": 0.701403737, "y": 0.91455, "z": -1.64917481}, "rotation": {"x": 0.0, "y": 6.83018925e-06, "z": 0.0}}], "object_toggles": [], "random_seed": 2813616761, "scene_num": 11}, "task_id": "trial_T20190908_073930_046570", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2KAGFQU28JY43_3300DTYQT5YCYMC5DAZHJMQBLOQEQ2", "high_descs": ["Turn to your left and walk towards the door, then turn to your left gain and go to the refrigerator. ", "Remove the black cup from the bottom shelf of the refrigerator. ", "Turn to your left and go to the microwave on the left side of the refrigerator. ", "Place the cup in the microwave, heat it up, then remove the cup from the microwave. ", "Step back from the microwave. ", "Place the cup in the left cabinet, under the microwave. "], "task_desc": "Put a warm cup in the kitchen cabinet. ", "votes": [1, 1]}, {"assignment_id": "A2A028LRDJB7ZB_3DYGAII7POPGVD1VMOFSDYPFZU0PQ4", "high_descs": ["Turn to your left move forward then head to the fridge on your left", "Open the fridge take out the mug inside then close the fridge", "Turn left move forward then turn right on the microwave", "Open the microwave warm the mug then take it out, close the microwave", "Turn around to your left then look at the second cabinet below the microwave", "Open the second cabinet on your left, put in the mug then close the cabinet"], "task_desc": "Put a warm mug in the cabinet", "votes": [1, 1]}, {"assignment_id": "AM2KK02JXXW48_3NQL1CS15UP29ZPDZA5LWCHZKR4YVN", "high_descs": ["Turn around, go to the fridge in the corner. ", "Take the black mug out of the fridge. ", "Bring the mug to the microwave, left of the fridge. ", "Heat the mug in the microwave. ", "Bring the heated mug to the left cabinet under the microwave. ", "Put the mug in the left cabinet under the microwave. "], "task_desc": "Put a heated mug in a cabinet. ", "votes": [1, 1]}]}}