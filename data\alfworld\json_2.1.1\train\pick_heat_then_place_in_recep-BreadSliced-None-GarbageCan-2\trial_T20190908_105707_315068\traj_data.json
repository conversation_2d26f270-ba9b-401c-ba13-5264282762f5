{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000151.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000152.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000153.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000154.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000155.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000156.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000157.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000158.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000159.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000160.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000161.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000162.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000163.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000164.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000165.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000166.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000167.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000168.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000169.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000170.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000171.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000172.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000173.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000174.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000175.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000176.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000177.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000178.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000179.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000180.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000181.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000211.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000212.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000213.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000214.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000215.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000216.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000217.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000218.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000219.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000220.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000221.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000222.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000223.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000224.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000225.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000226.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000227.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000228.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000229.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000230.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000231.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000232.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000233.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000234.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000235.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000236.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000237.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000238.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000239.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000240.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000241.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000242.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000243.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000244.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000245.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000246.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000247.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000248.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000249.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000250.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000251.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000252.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000253.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000254.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000255.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000256.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000257.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000258.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000259.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000260.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000261.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000262.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000263.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000264.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000265.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000266.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000267.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000268.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000269.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000270.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000271.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000272.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000273.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000274.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000275.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000276.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000277.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000278.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000279.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000280.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000281.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000282.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000283.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000284.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000285.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000286.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000287.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000288.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000289.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000290.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000291.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000292.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000293.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000294.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000295.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000296.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000297.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000298.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000299.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000300.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000301.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000302.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000303.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000304.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000305.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000306.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000307.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000308.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000309.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000310.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000311.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000312.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000313.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000314.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000315.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000316.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000317.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000318.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000319.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000320.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000321.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000322.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000323.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000324.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000325.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000326.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000327.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000328.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000329.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000330.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000331.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000332.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000333.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000334.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000335.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000336.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000337.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000338.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000339.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000340.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000341.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000342.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000343.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000344.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000345.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000346.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000347.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000348.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000349.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000350.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000351.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000352.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000353.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000354.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000355.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000356.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000357.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000358.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000359.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000360.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000361.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000362.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000363.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000364.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000365.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000366.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000367.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000368.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000369.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000370.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000371.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000372.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000373.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000374.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000375.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000376.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000377.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000378.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000379.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000380.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000381.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000382.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000383.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000384.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000385.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000386.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000387.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000388.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000389.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000390.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000391.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000392.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000393.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000394.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000395.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000396.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000397.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000398.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000399.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000400.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000401.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000402.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000403.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000404.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000405.png", "low_idx": 73}, {"high_idx": 11, "image_name": "000000406.png", "low_idx": 74}, {"high_idx": 11, "image_name": "000000407.png", "low_idx": 74}, {"high_idx": 11, "image_name": "000000408.png", "low_idx": 74}, {"high_idx": 11, "image_name": "000000409.png", "low_idx": 74}, {"high_idx": 11, "image_name": "000000410.png", "low_idx": 74}, {"high_idx": 11, "image_name": "000000411.png", "low_idx": 74}, {"high_idx": 11, "image_name": "000000412.png", "low_idx": 74}, {"high_idx": 11, "image_name": "000000413.png", "low_idx": 74}, {"high_idx": 11, "image_name": "000000414.png", "low_idx": 74}, {"high_idx": 11, "image_name": "000000415.png", "low_idx": 74}, {"high_idx": 11, "image_name": "000000416.png", "low_idx": 74}, {"high_idx": 11, "image_name": "000000417.png", "low_idx": 74}, {"high_idx": 11, "image_name": "000000418.png", "low_idx": 74}, {"high_idx": 11, "image_name": "000000419.png", "low_idx": 74}, {"high_idx": 11, "image_name": "000000420.png", "low_idx": 74}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "GarbageCan", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-3|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["butterknife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ButterKnife", [-6.908312, -6.908312, -4.25459432, -4.25459432, 3.646168232, 3.646168232]], "coordinateReceptacleObjectId": ["CounterTop", [0.008, 0.008, -6.052, -6.052, 3.788, 3.788]], "forceVisible": true, "objectId": "ButterKnife|-01.73|+00.91|-01.06"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-1|-3|0|45"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-0.85454172, -0.85454172, -0.761204244, -0.761204244, 4.02471876, 4.02471876]], "forceVisible": true, "objectId": "Bread|-00.21|+01.01|-00.19"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|1|-3|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["butterknife", "drawer"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ButterKnife", [-6.908312, -6.908312, -4.25459432, -4.25459432, 3.646168232, 3.646168232]], "coordinateReceptacleObjectId": ["Drawer", [6.0, 6.0, -0.08, -0.08, 0.786, 0.786]], "forceVisible": true, "objectId": "ButterKnife|-01.73|+00.91|-01.06", "receptacleObjectId": "Drawer|+01.50|+00.20|-00.02"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-1|-3|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-0.85454172, -0.85454172, -0.761204244, -0.761204244, 4.02471876, 4.02471876]], "coordinateReceptacleObjectId": ["CounterTop", [-0.06, -0.06, 2.024, 2.024, 3.8444, 3.8444]], "forceVisible": true, "objectId": "Bread|-00.21|+01.01|-00.19|BreadSliced_8"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|4|-3|1|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [7.732, 7.732, -3.068, -3.068, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.93|+00.90|-00.77"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|-5|5|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "garbagecan"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-0.85454172, -0.85454172, -0.761204244, -0.761204244, 4.02471876, 4.02471876]], "coordinateReceptacleObjectId": ["GarbageCan", [-7.184, -7.184, 5.32, 5.32, 0.0, 0.0]], "forceVisible": true, "objectId": "Bread|-00.21|+01.01|-00.19|BreadSliced_8", "receptacleObjectId": "GarbageCan|-01.80|+00.00|+01.33"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 12, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ButterKnife|-01.73|+00.91|-01.06"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [56, 117, 75, 165], "mask": [[34872, 4], [35171, 5], [35471, 5], [35770, 6], [36070, 6], [36370, 5], [36669, 6], [36969, 5], [37269, 5], [37569, 5], [37869, 4], [38168, 5], [38468, 4], [38768, 4], [39068, 3], [39367, 4], [39667, 3], [39967, 3], [40266, 4], [40566, 3], [40865, 4], [41165, 4], [41464, 4], [41764, 4], [42063, 5], [42363, 4], [42662, 5], [42961, 6], [43261, 5], [43560, 6], [43860, 6], [44159, 6], [44459, 6], [44758, 7], [45058, 6], [45357, 7], [45657, 7], [45957, 6], [46256, 7], [46556, 7], [46856, 6], [47156, 6], [47456, 6], [47756, 5], [48056, 5], [48356, 5], [48656, 4], [48956, 4], [49256, 4]], "point": [65, 140]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-00.21|+01.01|-00.19"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [139, 123, 186, 204], "mask": [[36755, 13], [37050, 23], [37347, 29], [37645, 32], [37944, 34], [38243, 36], [38542, 38], [38842, 38], [39141, 39], [39441, 40], [39741, 40], [40041, 40], [40341, 40], [40641, 41], [40941, 41], [41241, 41], [41541, 41], [41841, 41], [42141, 41], [42441, 42], [42741, 42], [43040, 43], [43340, 43], [43640, 43], [43940, 43], [44240, 43], [44540, 44], [44840, 44], [45139, 45], [45439, 45], [45739, 45], [46039, 45], [46339, 45], [46639, 45], [46939, 45], [47239, 45], [47539, 46], [47839, 46], [48139, 46], [48439, 46], [48739, 46], [49039, 46], [49339, 46], [49639, 46], [49939, 46], [50239, 46], [50539, 46], [50839, 47], [51139, 47], [51439, 47], [51739, 47], [52039, 48], [52339, 48], [52639, 48], [52939, 48], [53239, 48], [53539, 48], [53839, 48], [54139, 48], [54440, 47], [54740, 46], [55040, 46], [55340, 46], [55641, 44], [55941, 44], [56241, 43], [56541, 43], [56841, 43], [57141, 42], [57441, 42], [57741, 42], [58042, 41], [58342, 41], [58642, 41], [58942, 40], [59242, 40], [59543, 39], [59844, 37], [60144, 36], [60446, 32], [60750, 25], [61061, 3]], "point": [162, 162]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|+01.50|+00.20|-00.02"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 152, 91, 174], "mask": [[45300, 87], [45600, 87], [45900, 88], [46200, 88], [46500, 88], [46800, 88], [47101, 88], [47401, 88], [47702, 87], [48003, 86], [48303, 87], [48604, 86], [48904, 86], [49205, 85], [49506, 84], [49806, 85], [50107, 84], [50407, 84], [50708, 83], [51009, 83], [51309, 83], [51610, 82], [51910, 82]], "point": [45, 162]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ButterKnife|-01.73|+00.91|-01.06", "placeStationary": true, "receptacleObjectId": "Drawer|+01.50|+00.20|-00.02"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 152, 90, 223], "mask": [[45302, 85], [45601, 86], [45901, 86], [46200, 87], [46500, 88], [46800, 88], [47100, 88], [47400, 88], [47700, 89], [48000, 89], [48300, 89], [48600, 89], [48900, 90], [49200, 90], [49500, 90], [49800, 90], [50100, 91], [50400, 90], [50700, 90], [51000, 90], [51300, 90], [51600, 89], [51900, 89], [52200, 89], [52500, 89], [52800, 89], [53100, 88], [53400, 88], [53700, 88], [54000, 88], [54300, 88], [54600, 87], [54900, 87], [55200, 87], [55500, 87], [55800, 86], [56100, 86], [56400, 86], [56700, 86], [57000, 86], [57300, 85], [57600, 85], [57900, 85], [58200, 85], [58500, 85], [58800, 84], [59100, 84], [59400, 84], [59700, 84], [60000, 83], [60300, 83], [60600, 83], [60900, 83], [61200, 83], [61500, 82], [61800, 82], [62100, 82], [62400, 82], [62700, 82], [63000, 81], [63300, 81], [63600, 81], [63900, 81], [64200, 80], [64500, 80], [64800, 81], [65100, 81], [65400, 81], [65700, 82], [66000, 82], [66300, 82], [66600, 82]], "point": [45, 186]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|+01.50|+00.20|-00.02"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 152, 90, 223], "mask": [[45302, 85], [45601, 86], [45901, 86], [46200, 87], [46500, 88], [46800, 88], [47100, 88], [47400, 88], [47700, 89], [48000, 89], [48300, 89], [48600, 89], [48900, 90], [49200, 90], [49500, 90], [49800, 90], [50100, 91], [50400, 90], [50700, 90], [51000, 90], [51300, 90], [51600, 12], [51621, 68], [51900, 10], [51951, 38], [52200, 11], [52221, 8], [52250, 39], [52500, 37], [52544, 45], [52800, 89], [53100, 88], [53400, 88], [53700, 88], [54000, 88], [54300, 88], [54600, 87], [54900, 87], [55200, 87], [55500, 87], [55800, 86], [56100, 86], [56400, 86], [56700, 86], [57000, 86], [57300, 85], [57600, 85], [57900, 85], [58200, 85], [58500, 85], [58800, 84], [59100, 84], [59400, 84], [59700, 84], [60000, 83], [60300, 83], [60600, 83], [60900, 83], [61200, 83], [61500, 82], [61800, 82], [62100, 82], [62400, 82], [62700, 82], [63000, 81], [63300, 81], [63600, 81], [63900, 81], [64200, 80], [64500, 80], [64800, 81], [65100, 81], [65400, 81], [65700, 82], [66000, 82], [66300, 82], [66600, 82]], "point": [45, 186]}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-00.21|+01.01|-00.19|BreadSliced_8"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [143, 192, 182, 214], "mask": [[57460, 7], [57753, 21], [58049, 28], [58346, 34], [58644, 38], [58943, 39], [59243, 40], [59543, 40], [59843, 40], [60143, 40], [60444, 39], [60744, 39], [61043, 40], [61343, 40], [61643, 40], [61943, 39], [62244, 38], [62544, 37], [62844, 37], [63145, 36], [63446, 34], [63748, 29], [64053, 19]], "point": [162, 202]}}, "high_idx": 7}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [55, 9, 255, 124], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11160, 190], [11460, 191], [11758, 195], [12057, 197], [12356, 199], [12656, 199], [12955, 201], [13255, 201], [13555, 201], [13855, 201], [14155, 201], [14455, 200], [14756, 199], [15056, 199], [15356, 199], [15656, 198], [15957, 197], [16257, 197], [16557, 196], [16858, 195], [17158, 195], [17458, 195], [17758, 194], [18059, 193], [18359, 193], [18659, 192], [18959, 192], [19260, 191], [19560, 190], [19860, 190], [20160, 190], [20461, 189], [20761, 188], [21061, 188], [21361, 188], [21662, 186], [21962, 186], [22262, 186], [22563, 185], [22863, 184], [23163, 184], [23463, 184], [23764, 182], [24064, 182], [24364, 182], [24664, 181], [24965, 180], [25265, 180], [25565, 180], [25865, 179], [26166, 178], [26466, 178], [26766, 177], [27066, 177], [27367, 176], [27667, 176], [27967, 175], [28267, 175], [28568, 174], [28868, 173], [29168, 173], [29469, 172], [29769, 172], [30069, 171], [30369, 171], [30670, 170], [30970, 169], [31270, 169], [31570, 169], [31871, 167], [32171, 167], [32471, 167], [32771, 167], [33072, 165], [33372, 165], [33672, 165], [33972, 164], [34273, 163], [34573, 163], [34873, 163], [35174, 161], [35474, 161], [35774, 161], [36074, 160], [36375, 159], [36675, 159], [36976, 156]], "point": [155, 65]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-00.21|+01.01|-00.19|BreadSliced_8", "placeStationary": true, "receptacleObjectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 9, 255, 228], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11149, 201], [11448, 203], [11747, 206], [12046, 208], [12345, 210], [12645, 210], [12944, 212], [13243, 213], [13543, 213], [13842, 214], [14141, 215], [14441, 214], [14740, 215], [15039, 216], [15339, 216], [15638, 216], [15937, 217], [16237, 217], [16536, 217], [16835, 218], [17134, 85], [17229, 124], [17434, 81], [17532, 121], [17733, 80], [17834, 118], [18032, 80], [18135, 117], [18332, 79], [18436, 116], [18631, 79], [18736, 115], [18930, 79], [19037, 23], [19070, 81], [19230, 80], [19337, 21], [19373, 78], [19529, 81], [19637, 19], [19675, 75], [19828, 82], [19937, 17], [19976, 74], [20128, 82], [20236, 17], [20277, 73], [20427, 83], [20536, 17], [20578, 72], [20726, 85], [20836, 16], [20878, 71], [21025, 86], [21136, 16], [21179, 70], [21325, 86], [21436, 16], [21479, 70], [21624, 87], [21736, 16], [21779, 69], [21923, 89], [22036, 15], [22079, 69], [22223, 89], [22336, 15], [22379, 69], [22522, 90], [22636, 16], [22679, 69], [22821, 91], [22936, 16], [22979, 68], [23121, 91], [23236, 16], [23278, 69], [23420, 93], [23536, 16], [23578, 69], [23719, 94], [23836, 17], [23878, 68], [24019, 94], [24136, 17], [24177, 69], [24318, 95], [24436, 18], [24476, 70], [24617, 97], [24736, 18], [24776, 69], [24916, 98], [25035, 20], [25075, 70], [25216, 98], [25335, 20], [25375, 70], [25515, 99], [25635, 21], [25674, 71], [25814, 100], [25935, 22], [25973, 71], [26114, 101], [26235, 23], [26272, 72], [26413, 102], [26535, 26], [26570, 74], [26712, 103], [26835, 30], [26866, 77], [27012, 103], [27135, 108], [27311, 104], [27435, 108], [27610, 106], [27734, 109], [27910, 106], [28034, 108], [28209, 108], [28333, 109], [28508, 109], [28633, 109], [28807, 115], [28927, 114], [29107, 117], [29226, 115], [29406, 235], [29705, 236], [30005, 235], [30304, 236], [30603, 237], [30903, 236], [31202, 237], [31501, 238], [31801, 237], [32100, 238], [32400, 238], [32700, 238], [33000, 237], [33300, 237], [33600, 237], [33900, 236], [34200, 236], [34500, 236], [34800, 236], [35100, 235], [35400, 235], [35700, 79], [35902, 33], [36000, 79], [36202, 32], [36300, 78], [36502, 32], [36600, 78], [36802, 32], [36900, 78], [37103, 29], [37200, 77], [37500, 77], [37800, 77], [38100, 77], [38400, 76], [38700, 76], [39000, 76], [39300, 75], [39600, 75], [39900, 75], [40200, 74], [40500, 74], [40800, 74], [41100, 73], [41400, 73], [41700, 73], [42000, 73], [42300, 72], [42600, 72], [42900, 72], [43200, 71], [43500, 71], [43800, 71], [44100, 70], [44400, 70], [44700, 70], [45000, 69], [45300, 69], [45600, 69], [45900, 68], [46200, 68], [46500, 68], [46800, 68], [47100, 67], [47400, 67], [47700, 67], [48000, 66], [48300, 66], [48600, 66], [48900, 65], [49200, 65], [49500, 65], [49800, 64], [50100, 64], [50400, 64], [50700, 64], [51000, 63], [51300, 63], [51600, 63], [51900, 62], [52200, 62], [52500, 62], [52800, 61], [53100, 61], [53400, 61], [53700, 60], [54000, 60], [54301, 59], [54601, 59], [54902, 57], [55203, 56], [55503, 56], [55804, 54], [56105, 53], [56405, 53], [56706, 51], [57007, 50], [57307, 50], [57608, 48], [57909, 47], [58209, 47], [58510, 45], [58811, 44], [59111, 44], [59412, 43], [59713, 41], [60013, 41], [60314, 40], [60615, 38], [60915, 38], [61216, 37], [61517, 35], [61817, 35], [62118, 34], [62418, 33], [62719, 32], [63020, 31], [63320, 31], [63621, 29], [63922, 28], [64222, 28], [64522, 27], [64821, 28], [65122, 27], [65423, 25], [65723, 26], [66024, 27], [66325, 26], [66625, 27], [66926, 21], [66948, 3], [67227, 20], [67527, 19], [67828, 18], [68129, 17]], "point": [127, 117]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 9, 255, 228], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11149, 201], [11448, 203], [11747, 206], [12046, 208], [12345, 210], [12645, 210], [12944, 212], [13243, 213], [13543, 213], [13842, 214], [14141, 215], [14441, 214], [14740, 215], [15039, 216], [15339, 216], [15638, 216], [15937, 217], [16237, 217], [16536, 217], [16835, 218], [17134, 85], [17229, 124], [17434, 81], [17532, 5], [17540, 113], [17733, 80], [17844, 108], [18032, 80], [18148, 104], [18332, 79], [18449, 103], [18631, 79], [18751, 100], [18930, 79], [19052, 8], [19070, 81], [19230, 80], [19352, 6], [19373, 78], [19529, 81], [19652, 4], [19675, 75], [19828, 82], [19952, 2], [19976, 74], [20128, 82], [20252, 1], [20277, 73], [20427, 83], [20552, 1], [20578, 72], [20726, 85], [20878, 71], [21025, 86], [21179, 70], [21325, 86], [21479, 70], [21624, 87], [21779, 69], [21923, 89], [22079, 69], [22223, 89], [22379, 69], [22522, 90], [22679, 69], [22821, 91], [22979, 68], [23121, 91], [23278, 69], [23420, 93], [23578, 69], [23719, 94], [23852, 1], [23878, 68], [24019, 94], [24149, 4], [24177, 69], [24318, 95], [24444, 10], [24476, 70], [24617, 97], [24736, 18], [24776, 69], [24916, 98], [25035, 20], [25075, 70], [25216, 98], [25335, 20], [25375, 70], [25515, 99], [25635, 21], [25674, 71], [25814, 100], [25935, 22], [25973, 71], [26114, 101], [26235, 23], [26272, 72], [26413, 102], [26535, 26], [26570, 74], [26712, 103], [26835, 30], [26866, 77], [27012, 103], [27135, 108], [27311, 104], [27435, 108], [27610, 106], [27734, 109], [27910, 106], [28034, 108], [28209, 108], [28333, 109], [28508, 109], [28633, 109], [28807, 115], [28927, 114], [29107, 117], [29226, 115], [29406, 235], [29705, 236], [30005, 235], [30304, 236], [30603, 237], [30903, 236], [31202, 237], [31501, 238], [31801, 237], [32100, 238], [32400, 238], [32700, 238], [33000, 237], [33300, 237], [33600, 237], [33900, 236], [34200, 236], [34500, 236], [34800, 236], [35100, 235], [35400, 235], [35700, 79], [35902, 33], [36000, 79], [36202, 32], [36300, 78], [36502, 32], [36600, 78], [36802, 32], [36900, 78], [37103, 29], [37200, 77], [37500, 77], [37800, 77], [38100, 77], [38400, 76], [38700, 76], [39000, 76], [39300, 75], [39600, 75], [39900, 75], [40200, 74], [40500, 74], [40800, 74], [41100, 73], [41400, 73], [41700, 73], [42000, 73], [42300, 72], [42600, 72], [42900, 72], [43200, 71], [43500, 71], [43800, 71], [44100, 70], [44400, 70], [44700, 70], [45000, 69], [45300, 69], [45600, 69], [45900, 68], [46200, 68], [46500, 68], [46800, 68], [47100, 67], [47400, 67], [47700, 67], [48000, 66], [48300, 66], [48600, 66], [48900, 65], [49200, 65], [49500, 65], [49800, 64], [50100, 64], [50400, 64], [50700, 64], [51000, 63], [51300, 63], [51600, 63], [51900, 62], [52200, 62], [52500, 62], [52800, 61], [53100, 61], [53400, 61], [53700, 60], [54000, 60], [54301, 59], [54601, 59], [54902, 57], [55203, 56], [55503, 56], [55804, 54], [56105, 53], [56405, 53], [56706, 51], [57007, 50], [57307, 50], [57608, 48], [57909, 47], [58209, 47], [58510, 45], [58811, 44], [59111, 44], [59412, 43], [59713, 41], [60013, 41], [60314, 40], [60615, 38], [60915, 38], [61216, 37], [61517, 35], [61817, 35], [62118, 34], [62418, 33], [62719, 32], [63020, 31], [63320, 31], [63621, 29], [63922, 28], [64222, 28], [64522, 27], [64821, 28], [65122, 27], [65423, 25], [65723, 26], [66024, 27], [66325, 26], [66625, 27], [66926, 21], [66948, 3], [67227, 20], [67527, 19], [67828, 18], [68129, 17]], "point": [127, 117]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [7.732, 7.732, -3.068, -3.068, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [55, 9, 255, 124], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11160, 190], [11460, 191], [11758, 195], [12057, 197], [12356, 199], [12656, 199], [12955, 201], [13255, 201], [13555, 201], [13855, 201], [14155, 201], [14455, 200], [14756, 199], [15056, 199], [15356, 199], [15656, 198], [15957, 197], [16257, 197], [16557, 196], [16858, 195], [17158, 195], [17458, 195], [17758, 194], [18059, 193], [18359, 193], [18659, 192], [18959, 192], [19260, 191], [19560, 190], [19860, 190], [20160, 190], [20461, 189], [20761, 188], [21061, 188], [21361, 188], [21662, 186], [21962, 186], [22262, 186], [22563, 185], [22863, 184], [23163, 184], [23463, 184], [23764, 182], [24064, 182], [24364, 182], [24664, 181], [24965, 180], [25265, 180], [25565, 180], [25865, 179], [26166, 178], [26466, 178], [26766, 177], [27066, 177], [27367, 176], [27667, 176], [27967, 175], [28267, 175], [28568, 174], [28868, 173], [29168, 173], [29469, 172], [29769, 172], [30069, 171], [30369, 171], [30670, 170], [30970, 169], [31270, 169], [31570, 169], [31871, 167], [32171, 167], [32471, 167], [32771, 167], [33072, 165], [33372, 165], [33672, 165], [33972, 164], [34273, 163], [34573, 163], [34873, 163], [35174, 161], [35474, 161], [35774, 161], [36074, 160], [36375, 159], [36675, 159], [36976, 156]], "point": [155, 65]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [7.732, 7.732, -3.068, -3.068, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [55, 9, 255, 124], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11160, 190], [11460, 191], [11758, 195], [12057, 197], [12356, 199], [12656, 199], [12955, 201], [13255, 201], [13555, 201], [13855, 201], [14155, 201], [14455, 200], [14756, 199], [15056, 199], [15356, 199], [15656, 198], [15957, 197], [16257, 197], [16557, 196], [16858, 195], [17158, 195], [17458, 195], [17758, 194], [18059, 193], [18359, 193], [18659, 192], [18959, 192], [19260, 191], [19560, 190], [19860, 190], [20160, 190], [20461, 189], [20761, 188], [21061, 188], [21361, 188], [21662, 186], [21962, 186], [22262, 186], [22563, 185], [22863, 184], [23163, 184], [23463, 184], [23764, 182], [24064, 182], [24364, 182], [24664, 181], [24965, 180], [25265, 180], [25565, 180], [25865, 179], [26166, 178], [26466, 178], [26766, 177], [27066, 177], [27367, 176], [27667, 176], [27967, 175], [28267, 175], [28568, 174], [28868, 173], [29168, 173], [29469, 172], [29769, 172], [30069, 171], [30369, 171], [30670, 170], [30970, 169], [31270, 169], [31570, 169], [31871, 167], [32171, 167], [32471, 167], [32771, 167], [33072, 165], [33372, 165], [33672, 165], [33972, 164], [34273, 163], [34573, 163], [34873, 163], [35174, 161], [35474, 161], [35774, 161], [36074, 160], [36375, 159], [36675, 159], [36976, 156]], "point": [155, 65]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [55, 9, 255, 124], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11160, 190], [11460, 191], [11758, 195], [12057, 197], [12356, 199], [12656, 199], [12955, 201], [13255, 201], [13555, 201], [13855, 201], [14155, 201], [14455, 200], [14756, 199], [15056, 199], [15356, 199], [15656, 198], [15957, 197], [16257, 197], [16557, 196], [16858, 195], [17158, 195], [17458, 195], [17758, 194], [18059, 193], [18359, 193], [18659, 192], [18959, 192], [19260, 191], [19560, 190], [19860, 190], [20160, 190], [20461, 189], [20761, 188], [21061, 188], [21361, 188], [21662, 186], [21962, 186], [22262, 186], [22563, 185], [22863, 184], [23163, 184], [23463, 184], [23764, 182], [24064, 182], [24364, 182], [24664, 181], [24965, 180], [25265, 180], [25565, 180], [25865, 179], [26166, 178], [26466, 178], [26766, 177], [27066, 177], [27367, 176], [27667, 176], [27967, 175], [28267, 175], [28568, 174], [28868, 173], [29168, 173], [29469, 172], [29769, 172], [30069, 171], [30369, 171], [30670, 170], [30970, 169], [31270, 169], [31570, 169], [31871, 167], [32171, 167], [32471, 167], [32771, 167], [33072, 165], [33372, 165], [33672, 165], [33972, 164], [34273, 163], [34573, 163], [34873, 163], [35174, 161], [35474, 161], [35774, 161], [36074, 160], [36375, 159], [36675, 159], [36976, 156]], "point": [155, 65]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-00.21|+01.01|-00.19|BreadSliced_8"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [134, 59, 151, 82], "mask": [[17537, 3], [17834, 10], [18135, 13], [18436, 13], [18736, 15], [19037, 15], [19337, 15], [19637, 15], [19937, 15], [20236, 16], [20536, 16], [20836, 16], [21136, 16], [21436, 16], [21736, 16], [22036, 15], [22336, 15], [22636, 16], [22936, 16], [23236, 16], [23536, 16], [23836, 16], [24136, 13], [24436, 8]], "point": [142, 69]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 9, 255, 228], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11149, 201], [11448, 203], [11747, 206], [12046, 208], [12345, 210], [12645, 210], [12944, 212], [13243, 213], [13543, 213], [13842, 214], [14141, 215], [14441, 214], [14740, 215], [15039, 216], [15339, 216], [15638, 216], [15937, 217], [16237, 217], [16536, 217], [16835, 218], [17134, 85], [17229, 124], [17434, 81], [17532, 121], [17733, 80], [17834, 118], [18032, 80], [18135, 117], [18332, 79], [18436, 116], [18631, 79], [18736, 115], [18930, 79], [19037, 23], [19070, 81], [19230, 80], [19337, 21], [19373, 78], [19529, 81], [19637, 19], [19675, 75], [19828, 82], [19937, 17], [19976, 74], [20128, 82], [20236, 17], [20277, 73], [20427, 83], [20536, 17], [20578, 72], [20726, 85], [20836, 16], [20878, 71], [21025, 86], [21136, 16], [21179, 70], [21325, 86], [21436, 16], [21479, 70], [21624, 87], [21736, 16], [21779, 69], [21923, 89], [22036, 15], [22079, 69], [22223, 89], [22336, 15], [22379, 69], [22522, 90], [22636, 16], [22679, 69], [22821, 91], [22936, 16], [22979, 68], [23121, 91], [23236, 16], [23278, 69], [23420, 93], [23536, 16], [23578, 69], [23719, 94], [23836, 17], [23878, 68], [24019, 94], [24136, 17], [24177, 69], [24318, 95], [24436, 18], [24476, 70], [24617, 97], [24736, 18], [24776, 69], [24916, 98], [25035, 20], [25075, 70], [25216, 98], [25335, 20], [25375, 70], [25515, 99], [25635, 21], [25674, 71], [25814, 100], [25935, 22], [25973, 71], [26114, 101], [26235, 23], [26272, 72], [26413, 102], [26535, 26], [26570, 74], [26712, 103], [26835, 30], [26866, 77], [27012, 103], [27135, 108], [27311, 104], [27435, 108], [27610, 106], [27734, 109], [27910, 106], [28034, 108], [28209, 108], [28333, 109], [28508, 109], [28633, 109], [28807, 115], [28927, 114], [29107, 117], [29226, 115], [29406, 235], [29705, 236], [30005, 235], [30304, 236], [30603, 237], [30903, 236], [31202, 237], [31501, 238], [31801, 237], [32100, 238], [32400, 238], [32700, 238], [33000, 237], [33300, 237], [33600, 237], [33900, 236], [34200, 236], [34500, 236], [34800, 236], [35100, 235], [35400, 235], [35700, 79], [35902, 33], [36000, 79], [36202, 32], [36300, 78], [36502, 32], [36600, 78], [36802, 32], [36900, 78], [37103, 29], [37200, 77], [37500, 77], [37800, 77], [38100, 77], [38400, 76], [38700, 76], [39000, 76], [39300, 75], [39600, 75], [39900, 75], [40200, 74], [40500, 74], [40800, 74], [41100, 73], [41400, 73], [41700, 73], [42000, 73], [42300, 72], [42600, 72], [42900, 72], [43200, 71], [43500, 71], [43800, 71], [44100, 70], [44400, 70], [44700, 70], [45000, 69], [45300, 69], [45600, 69], [45900, 68], [46200, 68], [46500, 68], [46800, 68], [47100, 67], [47400, 67], [47700, 67], [48000, 66], [48300, 66], [48600, 66], [48900, 65], [49200, 65], [49500, 65], [49800, 64], [50100, 64], [50400, 64], [50700, 64], [51000, 63], [51300, 63], [51600, 63], [51900, 62], [52200, 62], [52500, 62], [52800, 61], [53100, 61], [53400, 61], [53700, 60], [54000, 60], [54301, 59], [54601, 59], [54902, 57], [55203, 56], [55503, 56], [55804, 54], [56105, 53], [56405, 53], [56706, 51], [57007, 50], [57307, 50], [57608, 48], [57909, 47], [58209, 47], [58510, 45], [58811, 44], [59111, 44], [59412, 43], [59713, 41], [60013, 41], [60314, 40], [60615, 38], [60915, 38], [61216, 37], [61517, 35], [61817, 35], [62118, 34], [62418, 33], [62719, 32], [63020, 31], [63320, 31], [63621, 29], [63922, 28], [64222, 28], [64522, 27], [64821, 28], [65122, 27], [65423, 25], [65723, 26], [66024, 27], [66325, 26], [66625, 27], [66926, 21], [66948, 3], [67227, 20], [67527, 19], [67828, 18], [68129, 17]], "point": [127, 117]}}, "high_idx": 9}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-00.21|+01.01|-00.19|BreadSliced_8", "placeStationary": true, "receptacleObjectId": "GarbageCan|-01.80|+00.00|+01.33"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [96, 129, 239, 217], "mask": [[38516, 100], [38814, 104], [39112, 108], [39410, 112], [39708, 116], [40007, 118], [40306, 120], [40606, 120], [40905, 122], [41204, 124], [41503, 126], [41803, 127], [42102, 128], [42402, 128], [42702, 129], [43002, 129], [43301, 130], [43601, 131], [43901, 131], [44201, 131], [44501, 131], [44801, 131], [45100, 133], [45400, 133], [45700, 133], [46000, 133], [46300, 133], [46600, 134], [46900, 134], [47200, 134], [47500, 134], [47799, 135], [48099, 136], [48399, 136], [48699, 136], [48999, 136], [49299, 136], [49599, 136], [49899, 137], [50198, 138], [50498, 138], [50798, 138], [51098, 138], [51398, 139], [51698, 139], [51998, 139], [52298, 139], [52597, 140], [52897, 141], [53197, 141], [53497, 141], [53797, 141], [54097, 141], [54397, 142], [54697, 142], [54997, 142], [55296, 143], [55596, 143], [55896, 144], [56196, 144], [56496, 78], [56580, 60], [56796, 76], [56882, 58], [57096, 75], [57183, 57], [57396, 75], [57483, 57], [57696, 74], [57784, 56], [57996, 74], [58084, 56], [58296, 74], [58384, 56], [58597, 73], [58684, 56], [58897, 142], [59198, 141], [59499, 139], [59799, 138], [60100, 137], [60400, 136], [60701, 135], [61002, 133], [61303, 131], [61605, 127], [61907, 123], [62208, 121], [62510, 117], [62812, 113], [63116, 105], [63429, 72], [63732, 66], [64034, 61], [64336, 56], [64639, 49], [64943, 41]], "point": [167, 172]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan2", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -1.0, "y": 0.9009992, "z": 2.75}, "object_poses": [{"objectName": "Potato_5e728867", "position": {"x": 0.1074348, "y": 0.9493317, "z": 0.7381004}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_6cb12d41", "position": {"x": -0.8440597, "y": 0.9094269, "z": -1.41596878}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_6cb12d41", "position": {"x": 0.1074348, "y": 0.923526943, "z": 0.273899674}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_4f8c70c0", "position": {"x": -1.727078, "y": 0.911542058, "z": -1.06364858}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_4f8c70c0", "position": {"x": -1.874256, "y": 0.911542058, "z": -1.2219063}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_c80d57de", "position": {"x": 1.71401417, "y": 0.120959833, "z": 0.7457101}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_bfeb74cb", "position": {"x": 1.713, "y": 0.238502845, "z": -0.157344744}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_de267f63", "position": {"x": -1.66821682, "y": 0.926499844, "z": -1.61003125}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_de267f63", "position": {"x": 0.889933, "y": 0.9264999, "z": -1.2219063}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_1bc23cfd", "position": {"x": 1.9753, "y": 0.938299954, "z": 0.8192}, "rotation": {"x": 0.0, "y": 180.000214, "z": 0.0}}, {"objectName": "SoapBottle_7ce82db0", "position": {"x": 1.924396, "y": 1.66291428, "z": -1.2800504}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_448791c6", "position": {"x": -1.80318046, "y": 1.72807324, "z": -0.269365072}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_253fa632", "position": {"x": -0.320658833, "y": 0.9240406, "z": 0.7381004}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_253fa632", "position": {"x": -0.00256601069, "y": 0.7588777, "z": -1.38022017}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_586649ce", "position": {"x": 0.8533346, "y": 0.6678068, "z": -1.3015}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_48e00ad2", "position": {"x": -0.21363543, "y": 1.00617969, "z": -0.190301061}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_aa2e533f", "position": {"x": 1.90011775, "y": 0.988203764, "z": 1.25821185}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_aa2e533f", "position": {"x": 0.170128584, "y": 0.8372748, "z": -1.49077988}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_b045e42b", "position": {"x": 1.3554858, "y": 0.9554, "z": -1.36239815}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_487a7950", "position": {"x": 1.92436874, "y": 1.05970585, "z": -0.808987558}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_487a7950", "position": {"x": -1.69229174, "y": 0.6164668, "z": -0.17519027}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_7ce82db0", "position": {"x": -1.727078, "y": 0.9150344, "z": -0.8647342}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_4e364eea", "position": {"x": 1.89233732, "y": 1.00455368, "z": -0.6519562}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pot_1bc23cfd", "position": {"x": -0.106612019, "y": 0.929685533, "z": 0.273899674}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_c80d57de", "position": {"x": 1.61219358, "y": 0.461791217, "z": 1.08805}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Ladle_ec6bb035", "position": {"x": 1.71057916, "y": 0.73976326, "z": 0.608114243}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_233c7df0", "position": {"x": -0.320658833, "y": 0.9266413, "z": 1.20230114}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7f5e18ec", "position": {"x": -1.796, "y": 0.0991948545, "z": 1.4123224}, "rotation": {"x": 0.0, "y": 269.999939, "z": 0.0}}, {"objectName": "Plate_d852c2bd", "position": {"x": -1.15179658, "y": 0.112174153, "z": -1.36015964}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_6d10ab8d", "position": {"x": -1.28641355, "y": 0.9, "z": -1.34873831}, "rotation": {"x": 0.0, "y": 79.76979, "z": 0.0}}, {"objectName": "Lettuce_aa2e533f", "position": {"x": -1.69785666, "y": 1.545918, "z": -0.0810167938}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_de267f63", "position": {"x": 1.60350847, "y": 0.598703861, "z": -0.238535538}, "rotation": {"x": 0.0, "y": 180.0, "z": 7.062251e-30}}, {"objectName": "Apple_487a7950", "position": {"x": 1.90011775, "y": 0.964782655, "z": 1.48408055}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_e0496240", "position": {"x": 0.214458212, "y": 0.9475927, "z": 0.0417993069}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_586649ce", "position": {"x": -1.10569334, "y": 0.109772086, "z": -1.5507803}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_14add4df", "position": {"x": -0.8678425, "y": 1.67594481, "z": -1.65244007}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_6cb12d41", "position": {"x": 1.76926708, "y": 0.7601382, "z": 1.22007179}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}, {"objectName": "ButterKnife_4f8c70c0", "position": {"x": 0.10743475, "y": 0.9256421, "z": 0.9702008}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_48e00ad2", "position": {"x": -0.320658833, "y": 1.00617969, "z": 0.273899674}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_448791c6", "position": {"x": -1.71607137, "y": 0.8944802, "z": 0.2015051}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_5e728867", "position": {"x": -1.71607137, "y": 0.8630446, "z": 0.0131574105}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_e1c57c26", "position": {"x": -0.00256602466, "y": 0.7602106, "z": -1.54605973}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_bfeb74cb", "position": {"x": -0.261607945, "y": 0.7618504, "z": -1.49077988}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Mug_b045e42b", "position": {"x": -1.76487529, "y": 1.6569798, "z": -1.0561744}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_253fa632", "position": {"x": 0.321481615, "y": 0.9240406, "z": 0.7381004}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1618374641, "scene_num": 2}, "task_id": "trial_T20190908_105707_315068", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A30M9SZYJKFDS9_3IOEN3P9SAASPSL7ILFL7JASQOC61X", "high_descs": ["turn to the left go forward turn right towards the counter", "pick up the knife from the counter", "turn around to the right ", "slice the loaf of bread that is on the counter", "turn to the right", "open the bottom cabinet drawer put the knife inside of the drawer", "turn around to the left towards the counter", "pick a slice of the bread that is on the counter", "turn around to the right go forward towards the microwave ", "put the slice of bread inside the microwave and heat it up get it back out ", "turn to the right go forward go right turn to the left towards the trash can", "throw the slice of bread into the trash can"], "task_desc": "put a heated slice of bread in the trash can", "votes": [1, 1]}, {"assignment_id": "A36A780ODXBDEH_3U84XHCDIF4B512178VY2FAQ7C8Z4J", "high_descs": ["Turn around and go to the part of the counter to the left of the refrigerator.", "Pick up the butterknife on the counter.", "Turn around and go to the kitchen island across from the counter.", "Slice the bread closest to you on the kitchen island.", "Turn right and face the drawers to the left of the dishwasher.", "Open the bottom drawer and place the knife inside and close it.", "Turn around and go back to the sliced bread on the kitchen island.", "Pick up a bread slice on the kitchen island.", "Turn right and move to the microwave on the counter.", "Heat the bread slice in the microwave and then remove it.", "Turn around and go to the trash bin to the right of the refrigerator.", "Place the heated bread inside the trash bin."], "task_desc": "Microwave a slice of bread and put it in the trash bin.", "votes": [1, 1]}, {"assignment_id": "A3MLUEOP3CCLXL_3WLEIWSYHR8FB2NKX8639T9NR812H6", "high_descs": ["Turn around and cross the room to the counter in front of you, at the counter turn right to face the counter directly to the left of the fridge", "Pick up the butter knife on the right", "Turn around and walk to the end of the island on your left", "Slice the loaf of bread that is sitting on the island", "Turn right and face the drawers that are to the left of the dishwasher", "Open the bottom drawer to the left of the dishwasher and place the knife inside then close the drawer", "Turn to your left and face the end of the island with the sliced loaf of bread", "Pick up a slice of bread", "Turn to your right and walk to the microwave that is sitting on the counter above the dishwasher", "Open the microwave and place the slice of bread on the plate behind the cup and apple, Close the door then turn on the microwave.  Open the microwave and pick up the slice of bread then close the door", "Turn around and cross the room towards the fridge.  Walk to the trash can that is to the right of the fridge", "Place the slice of bread into the trash can behind the egg"], "task_desc": "Place cooked bread into a trashcan", "votes": [1, 1]}]}}