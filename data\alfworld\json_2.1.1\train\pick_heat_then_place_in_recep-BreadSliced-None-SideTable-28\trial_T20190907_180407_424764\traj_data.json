{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000187.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000188.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000189.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000190.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000191.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000192.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000193.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000194.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000195.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000196.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000197.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000232.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000233.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000234.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000235.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000236.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000237.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000238.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000239.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000240.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000241.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000242.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000243.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000244.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000245.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000246.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000247.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000248.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000249.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000250.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000251.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000252.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000253.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000254.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000255.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000256.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000257.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000258.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000259.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000260.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000261.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000262.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000263.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000264.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000265.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000266.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000267.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000268.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000269.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000270.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000271.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000272.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000273.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000274.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000275.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000276.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000277.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000278.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000279.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000280.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000281.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000282.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000283.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000284.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000285.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000286.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000287.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000288.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000289.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000290.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000291.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000292.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000293.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000294.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000295.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000296.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000297.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000298.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000299.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000300.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000301.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000302.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000303.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000304.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000305.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000306.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000307.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000308.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000309.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000310.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000311.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000312.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000313.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000314.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000315.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000316.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000317.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000318.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000319.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000320.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000321.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000322.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000323.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000324.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000325.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000326.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000327.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000328.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000329.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000330.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000331.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000332.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000333.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000334.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000335.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000336.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000337.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000338.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000339.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000340.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000341.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000342.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000343.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000344.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000345.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000346.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000347.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000348.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000349.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000350.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000351.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000352.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000353.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000354.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000355.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000356.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000357.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000358.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000359.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000360.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000361.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000362.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000363.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000364.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000365.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000366.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000367.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000368.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000369.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000370.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000371.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000372.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000373.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000374.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000375.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000376.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000377.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000378.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000379.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000380.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000381.png", "low_idx": 71}, {"high_idx": 9, "image_name": "000000382.png", "low_idx": 71}, {"high_idx": 9, "image_name": "000000383.png", "low_idx": 71}, {"high_idx": 9, "image_name": "000000384.png", "low_idx": 71}, {"high_idx": 9, "image_name": "000000385.png", "low_idx": 71}, {"high_idx": 9, "image_name": "000000386.png", "low_idx": 71}, {"high_idx": 9, "image_name": "000000387.png", "low_idx": 71}, {"high_idx": 9, "image_name": "000000388.png", "low_idx": 71}, {"high_idx": 9, "image_name": "000000389.png", "low_idx": 71}, {"high_idx": 9, "image_name": "000000390.png", "low_idx": 71}, {"high_idx": 9, "image_name": "000000391.png", "low_idx": 71}, {"high_idx": 9, "image_name": "000000392.png", "low_idx": 71}, {"high_idx": 9, "image_name": "000000393.png", "low_idx": 71}, {"high_idx": 9, "image_name": "000000394.png", "low_idx": 71}, {"high_idx": 9, "image_name": "000000395.png", "low_idx": 71}, {"high_idx": 9, "image_name": "000000396.png", "low_idx": 72}, {"high_idx": 9, "image_name": "000000397.png", "low_idx": 72}, {"high_idx": 9, "image_name": "000000398.png", "low_idx": 72}, {"high_idx": 9, "image_name": "000000399.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000400.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000401.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000402.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000403.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000404.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000405.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000406.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000407.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000408.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000409.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000410.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000411.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000412.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000413.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000414.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000415.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000416.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000417.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000418.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000419.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000420.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000421.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000422.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000423.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000424.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000425.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000426.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000427.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000428.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000429.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000430.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000431.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000432.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000433.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000434.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000435.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000436.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000437.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000438.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000439.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000440.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000441.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000442.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000443.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000444.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000445.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000446.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000447.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000448.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000449.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000450.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000451.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000452.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000453.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000454.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000455.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000456.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000457.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000458.png", "low_idx": 84}, {"high_idx": 10, "image_name": "000000459.png", "low_idx": 84}, {"high_idx": 10, "image_name": "000000460.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000461.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000462.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000463.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000464.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000465.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000466.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000467.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000468.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000469.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000470.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000471.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000472.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000473.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000474.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000475.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000476.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000477.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000478.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000479.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000480.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000481.png", "low_idx": 91}, {"high_idx": 10, "image_name": "000000482.png", "low_idx": 91}, {"high_idx": 11, "image_name": "000000483.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000484.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000485.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000486.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000487.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000488.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000489.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000490.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000491.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000492.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000493.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000494.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000495.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000496.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000497.png", "low_idx": 92}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "SideTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-11|-4|0|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["butterknife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ButterKnife", [-11.04692936, -11.04692936, -1.849168064, -1.849168064, 3.578651428, 3.578651428]], "coordinateReceptacleObjectId": ["DiningTable", [-12.132, -12.132, -1.764, -1.764, 0.0, 0.0]], "forceVisible": true, "objectId": "ButterKnife|-02.76|+00.89|-00.46"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-16|-10|2|45"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-15.70138932, -15.70138932, -13.59606932, -13.59606932, 3.36608, 3.36608]], "forceVisible": true, "objectId": "Bread|-03.93|+00.84|-03.40"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-15|-5|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["butterknife", "sidetable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ButterKnife", [-11.04692936, -11.04692936, -1.849168064, -1.849168064, 3.578651428, 3.578651428]], "coordinateReceptacleObjectId": ["SideTable", [-16.284, -16.284, -1.236, -1.236, 2.904, 2.904]], "forceVisible": true, "objectId": "ButterKnife|-02.76|+00.89|-00.46", "receptacleObjectId": "SideTable|-04.07|+00.73|-00.31"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-16|-10|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-15.70138932, -15.70138932, -13.59606932, -13.59606932, 3.36608, 3.36608]], "coordinateReceptacleObjectId": ["DiningTable", [-14.464, -14.464, -13.296, -13.296, 2.673336984, 2.673336984]], "forceVisible": true, "objectId": "Bread|-03.93|+00.84|-03.40|BreadSliced_6"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-8|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|-15|-5|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "sidetable"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-15.70138932, -15.70138932, -13.59606932, -13.59606932, 3.36608, 3.36608]], "coordinateReceptacleObjectId": ["SideTable", [-16.284, -16.284, -1.236, -1.236, 2.904, 2.904]], "forceVisible": true, "objectId": "Bread|-03.93|+00.84|-03.40|BreadSliced_6", "receptacleObjectId": "SideTable|-04.07|+00.73|-00.31"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ButterKnife|-02.76|+00.89|-00.46"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [144, 85, 151, 130], "mask": [[25346, 3], [25645, 4], [25945, 5], [26245, 5], [26545, 5], [26846, 5], [27145, 6], [27445, 6], [27745, 6], [28045, 7], [28345, 7], [28645, 7], [28945, 7], [29245, 7], [29545, 7], [29845, 7], [30145, 7], [30445, 7], [30745, 6], [31045, 6], [31345, 6], [31645, 6], [31945, 6], [32245, 6], [32545, 5], [32845, 5], [33145, 5], [33445, 5], [33745, 4], [34045, 4], [34345, 4], [34645, 4], [34945, 4], [35245, 4], [35545, 4], [35845, 4], [36145, 4], [36445, 4], [36745, 4], [37044, 5], [37344, 5], [37644, 5], [37944, 5], [38244, 5], [38544, 5], [38844, 1], [38847, 2]], "point": [147, 106]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-03.93|+00.84|-03.40"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [91, 110, 173, 160], "mask": [[32864, 2], [33158, 9], [33452, 16], [33746, 22], [34039, 30], [34333, 36], [34625, 45], [34919, 51], [35211, 60], [35504, 67], [35796, 76], [36091, 81], [36391, 82], [36691, 82], [36991, 82], [37291, 82], [37591, 83], [37891, 83], [38192, 82], [38492, 82], [38792, 82], [39092, 82], [39392, 82], [39692, 81], [39992, 81], [40292, 81], [40593, 80], [40893, 80], [41193, 80], [41493, 80], [41793, 80], [42093, 80], [42393, 80], [42694, 78], [42994, 78], [43294, 78], [43594, 78], [43895, 77], [44195, 75], [44495, 70], [44795, 64], [45095, 58], [45396, 51], [45696, 46], [45996, 39], [46296, 34], [46596, 28], [46897, 20], [47197, 15], [47498, 8], [47798, 1]], "point": [132, 134]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ButterKnife|-02.76|+00.89|-00.46", "placeStationary": true, "receptacleObjectId": "SideTable|-04.07|+00.73|-00.31"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [21, 115, 140, 251], "mask": [[34251, 12], [34300, 4], [34320, 18], [34551, 11], [34600, 4], [34620, 18], [34851, 11], [34900, 4], [34921, 17], [35150, 11], [35200, 4], [35221, 17], [35450, 11], [35500, 4], [35521, 17], [35749, 12], [35821, 17], [36049, 11], [36121, 17], [36349, 11], [36421, 17], [36648, 11], [36721, 17], [36948, 11], [37021, 17], [37247, 12], [37321, 17], [37547, 11], [37622, 16], [37846, 12], [37923, 15], [38146, 12], [38224, 14], [38446, 11], [38524, 14], [38745, 12], [38825, 13], [39045, 11], [39125, 13], [39344, 12], [39426, 12], [39644, 12], [39726, 12], [39944, 13], [40027, 11], [40244, 14], [40327, 11], [40545, 14], [40627, 11], [40846, 15], [40927, 11], [41147, 41], [41227, 12], [41447, 42], [41527, 12], [41748, 41], [41827, 12], [42048, 41], [42127, 12], [42349, 41], [42427, 12], [42650, 40], [42728, 11], [42939, 1], [42950, 41], [43027, 12], [43239, 1], [43251, 40], [43327, 12], [43538, 2], [43551, 40], [43627, 12], [43838, 3], [43852, 40], [43926, 13], [44137, 4], [44152, 41], [44226, 13], [44437, 4], [44453, 40], [44526, 13], [44736, 5], [44753, 41], [44825, 14], [45036, 6], [45053, 42], [45124, 15], [45335, 7], [45353, 43], [45423, 16], [45635, 7], [45652, 45], [45722, 17], [45935, 7], [45951, 47], [46020, 19], [46234, 10], [46249, 51], [46319, 20], [46534, 69], [46617, 22], [46833, 74], [46914, 25], [47133, 106], [47432, 107], [47732, 107], [48031, 109], [48331, 109], [48631, 109], [48930, 110], [49230, 110], [49529, 111], [49829, 111], [50128, 112], [50428, 112], [50728, 112], [51027, 113], [51327, 113], [51626, 114], [51926, 114], [52225, 115], [52525, 115], [52824, 116], [53124, 116], [53424, 116], [53723, 117], [54022, 118], [54322, 118], [54621, 119], [54922, 118], [55223, 118], [55523, 11], [55546, 95], [55824, 11], [55846, 95], [56124, 11], [56145, 96], [56425, 11], [56445, 96], [56725, 11], [56745, 92], [57026, 11], [57044, 93], [57326, 11], [57344, 93], [57627, 11], [57644, 93], [57928, 10], [57943, 94], [58228, 11], [58243, 94], [58529, 10], [58542, 95], [58829, 11], [58842, 95], [59130, 10], [59142, 95], [59430, 107], [59731, 106], [60032, 105], [60332, 105], [60633, 104], [60933, 104], [61234, 103], [61534, 102], [61835, 101], [62136, 9], [62158, 78], [62436, 10], [62457, 79], [62737, 9], [62757, 79], [63037, 10], [63057, 79], [63338, 9], [63356, 80], [63638, 10], [63656, 80], [63939, 10], [63956, 80], [64240, 9], [64256, 80], [64540, 10], [64555, 81], [64841, 9], [64855, 82], [65141, 10], [65155, 82], [65442, 9], [65454, 83], [65742, 10], [65754, 83], [66043, 9], [66054, 83], [66343, 10], [66354, 83], [66644, 93], [66945, 92], [67245, 92], [67546, 91], [67846, 91], [68147, 90], [68447, 90], [68748, 89], [69049, 8], [69131, 7], [69349, 9], [69431, 7], [69650, 8], [69731, 7], [69950, 9], [70032, 6], [70251, 8], [70332, 6], [70551, 9], [70632, 6], [70852, 8], [70932, 6], [71153, 8], [71232, 6], [71453, 8], [71532, 6], [71754, 8], [71832, 6], [72054, 8], [72132, 6], [72355, 8], [72432, 6], [72655, 8], [72732, 6], [72956, 8], [73033, 5], [73256, 9], [73333, 5], [73557, 8], [73633, 6], [73858, 8], [73933, 6], [74158, 8], [74233, 6], [74459, 7], [74533, 6], [74759, 7], [74833, 6], [75060, 5], [75133, 6]], "point": [80, 182]}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-03.93|+00.84|-03.40|BreadSliced_6"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [84, 124, 91, 164], "mask": [[36984, 5], [37284, 5], [37584, 5], [37884, 6], [38184, 6], [38484, 6], [38784, 6], [39084, 6], [39384, 6], [39684, 6], [39984, 7], [40284, 7], [40584, 7], [40884, 7], [41184, 7], [41484, 7], [41784, 7], [42084, 7], [42384, 7], [42684, 7], [42984, 7], [43284, 7], [43584, 7], [43884, 8], [44184, 8], [44484, 8], [44784, 8], [45084, 8], [45384, 8], [45685, 7], [45985, 7], [46285, 7], [46585, 7], [46885, 7], [47185, 7], [47486, 6], [47786, 6], [48086, 6], [48386, 6], [48686, 6], [48987, 4]], "point": [87, 143]}}, "high_idx": 7}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-03.93|+00.84|-03.40|BreadSliced_6", "placeStationary": true, "receptacleObjectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 32743], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [149, 62]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 6445], [6446, 289], [6757, 275], [7061, 268], [7364, 262], [7666, 259], [7967, 257], [8268, 255], [8570, 252], [8871, 250], [9171, 250], [9471, 250], [9772, 248], [10072, 248], [10372, 248], [10673, 246], [10973, 246], [11273, 246], [11573, 246], [11873, 246], [12173, 246], [12473, 246], [12773, 246], [13073, 246], [13373, 247], [13673, 247], [13973, 247], [14273, 247], [14573, 246], [14873, 246], [15173, 246], [15473, 246], [15774, 245], [16074, 245], [16374, 245], [16674, 245], [16974, 245], [17274, 245], [17574, 245], [17874, 245], [18174, 245], [18474, 245], [18773, 246], [19073, 246], [19373, 246], [19673, 246], [19973, 246], [20273, 246], [20573, 246], [20873, 246], [21173, 246], [21473, 246], [21774, 245], [22073, 247], [22373, 247], [22673, 247], [22973, 247], [23272, 249], [23572, 249], [23872, 251], [24170, 8573], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [149, 81]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-03.93|+00.84|-03.40|BreadSliced_6"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [119, 22, 173, 81], "mask": [[6445, 1], [6735, 22], [7032, 29], [7329, 35], [7626, 40], [7925, 42], [8224, 44], [8523, 47], [8822, 49], [9121, 50], [9421, 50], [9721, 51], [10020, 52], [10320, 52], [10620, 53], [10919, 54], [11219, 54], [11519, 54], [11819, 54], [12119, 54], [12419, 54], [12719, 54], [13019, 54], [13319, 54], [13620, 53], [13920, 53], [14220, 53], [14520, 53], [14819, 54], [15119, 54], [15419, 54], [15719, 55], [16019, 55], [16319, 55], [16619, 55], [16919, 55], [17219, 55], [17519, 55], [17819, 55], [18119, 55], [18419, 55], [18719, 54], [19019, 54], [19319, 54], [19619, 54], [19919, 54], [20219, 54], [20519, 54], [20819, 54], [21119, 54], [21419, 54], [21719, 55], [22019, 54], [22320, 53], [22620, 53], [22920, 53], [23220, 52], [23521, 51], [23821, 51], [24123, 47]], "point": [146, 50]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 32743], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [149, 62]}}, "high_idx": 9}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-03.93|+00.84|-03.40|BreadSliced_6", "placeStationary": true, "receptacleObjectId": "SideTable|-04.07|+00.73|-00.31"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [21, 115, 140, 251], "mask": [[34251, 12], [34300, 4], [34320, 18], [34551, 11], [34600, 4], [34620, 18], [34851, 11], [34900, 4], [34921, 17], [35150, 11], [35200, 4], [35221, 17], [35450, 11], [35500, 4], [35521, 17], [35749, 12], [35821, 17], [36049, 11], [36121, 17], [36349, 11], [36421, 17], [36648, 11], [36721, 17], [36948, 11], [37021, 17], [37247, 12], [37321, 17], [37547, 11], [37622, 16], [37846, 12], [37923, 15], [38146, 12], [38224, 14], [38446, 11], [38524, 14], [38745, 12], [38825, 13], [39045, 11], [39125, 13], [39344, 12], [39426, 12], [39644, 12], [39726, 12], [39944, 13], [40027, 11], [40244, 14], [40327, 11], [40545, 14], [40627, 11], [40846, 15], [40927, 11], [41147, 41], [41227, 12], [41447, 42], [41527, 12], [41748, 41], [41827, 12], [42048, 41], [42127, 12], [42349, 41], [42427, 12], [42650, 40], [42728, 11], [42939, 1], [42950, 41], [43027, 12], [43239, 1], [43251, 40], [43327, 12], [43538, 2], [43551, 40], [43627, 12], [43838, 3], [43852, 40], [43926, 13], [44137, 4], [44152, 41], [44226, 13], [44437, 4], [44453, 40], [44526, 13], [44736, 5], [44753, 41], [44825, 14], [45036, 6], [45053, 42], [45124, 15], [45335, 7], [45353, 43], [45423, 16], [45635, 7], [45652, 45], [45722, 17], [45935, 7], [45951, 47], [46020, 19], [46234, 10], [46249, 51], [46319, 20], [46534, 69], [46617, 22], [46833, 74], [46914, 25], [47133, 106], [47432, 107], [47732, 107], [48031, 109], [48331, 109], [48631, 109], [48930, 110], [49230, 110], [49529, 111], [49829, 111], [50128, 112], [50428, 36], [50485, 55], [50728, 36], [50824, 16], [51027, 37], [51124, 16], [51327, 69], [51421, 19], [51626, 77], [51715, 25], [51926, 114], [52225, 115], [52525, 115], [52824, 116], [53124, 116], [53424, 116], [53723, 117], [54022, 118], [54322, 118], [54621, 119], [54922, 118], [55223, 118], [55523, 11], [55546, 95], [55824, 11], [55846, 95], [56124, 11], [56145, 96], [56425, 11], [56445, 96], [56725, 11], [56745, 92], [57026, 11], [57044, 93], [57326, 11], [57344, 93], [57627, 11], [57644, 93], [57928, 10], [57943, 94], [58228, 11], [58243, 94], [58529, 10], [58542, 95], [58829, 11], [58842, 95], [59130, 10], [59142, 95], [59430, 107], [59731, 106], [60032, 105], [60332, 105], [60633, 104], [60933, 104], [61234, 103], [61534, 102], [61835, 101], [62136, 9], [62158, 78], [62436, 10], [62457, 79], [62737, 9], [62757, 79], [63037, 10], [63057, 79], [63338, 9], [63356, 80], [63638, 10], [63656, 80], [63939, 10], [63956, 80], [64240, 9], [64256, 74], [64540, 10], [64555, 71], [64841, 9], [64855, 66], [65141, 10], [65155, 63], [65442, 9], [65454, 63], [65742, 10], [65754, 61], [66043, 9], [66054, 60], [66343, 10], [66354, 58], [66644, 67], [66945, 64], [67245, 64], [67546, 62], [67846, 62], [68147, 60], [68447, 60], [68748, 58], [69049, 8], [69349, 9], [69650, 8], [69950, 9], [70251, 8], [70551, 9], [70852, 8], [71153, 8], [71453, 8], [71754, 8], [72054, 8], [72355, 8], [72655, 8], [72956, 8], [73256, 9], [73557, 8], [73858, 8], [74158, 8], [74459, 7], [74759, 7], [75060, 5]], "point": [80, 182]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan28", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -3.5, "y": 0.900998235, "z": -2.0}, "object_poses": [{"objectName": "Fork_938fe393", "position": {"x": -0.236659765, "y": 0.9418486, "z": -1.31903028}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_1d69618b", "position": {"x": -0.236659765, "y": 0.9562999, "z": -1.49432325}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_1d69618b", "position": {"x": -0.4751878, "y": 0.765967667, "z": -2.84604383}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_571582a1", "position": {"x": -2.35991859, "y": 0.9356876, "z": -0.633832}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Plate_ba5a8c1e", "position": {"x": -3.56536055, "y": 0.9087526, "z": -0.20498088}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Plate_ba5a8c1e", "position": {"x": -4.122419, "y": 0.7046318, "z": -0.187332019}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_0407be4c", "position": {"x": -3.55309939, "y": 0.7582775, "z": -3.50686383}, "rotation": {"x": 0.0, "y": 199.125946, "z": 0.0}}, {"objectName": "Tomato_b1760e7a", "position": {"x": -0.359176069, "y": 1.53947651, "z": -0.413377434}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Bowl_1f51c27f", "position": {"x": -0.212670773, "y": 1.50094545, "z": -3.14060116}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_ec37c659", "position": {"x": -3.46028686, "y": 0.8510728, "z": -3.04847479}, "rotation": {"x": 0.0, "y": 199.125946, "z": 0.0}}, {"objectName": "Lettuce_ec37c659", "position": {"x": -2.56082487, "y": 0.98560524, "z": -0.462293148}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "SaltShaker_6f13b4f9", "position": {"x": -4.24255562, "y": 0.68760246, "z": -0.369833946}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_8ae9f292", "position": {"x": -0.351160467, "y": 0.07846749, "z": -1.5295893}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f3c7368a", "position": {"x": -3.66277027, "y": 0.7550884, "z": -3.63235664}, "rotation": {"x": 0.0, "y": 199.125946, "z": 0.0}}, {"objectName": "Mug_f3c7368a", "position": {"x": -0.813287735, "y": 0.075627625, "z": -3.36534119}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Apple_4eadea0d", "position": {"x": -2.45298, "y": 0.161071837, "z": -3.538296}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_4eadea0d", "position": {"x": -0.245573536, "y": 1.55012226, "z": -0.7623116}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SoapBottle_0407be4c", "position": {"x": -3.94221377, "y": 0.6886891, "z": -0.187332019}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_c7418f98", "position": {"x": -0.4374, "y": 0.961999953, "z": -1.8457}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Fork_938fe393", "position": {"x": -1.47139764, "y": 0.942848563, "z": -3.75178385}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_41873c33", "position": {"x": -0.6823228, "y": 0.814170063, "z": -3.53100038}, "rotation": {"x": 9.923132e-15, "y": 315.000031, "z": -9.92312e-15}}, {"objectName": "Tomato_b1760e7a", "position": {"x": -2.76173329, "y": 0.9453869, "z": -0.633829832}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Pan_d1250561", "position": {"x": -2.96263838, "y": 0.89266485, "z": -0.204984248}, "rotation": {"x": 0.0, "y": 0.000327849062, "z": 0.0}}, {"objectName": "Plate_ba5a8c1e", "position": {"x": -3.62175369, "y": 0.7742202, "z": -3.32050776}, "rotation": {"x": 0.0, "y": 109.125885, "z": 0.0}}, {"objectName": "Bread_4d72833d", "position": {"x": -3.92534733, "y": 0.84152, "z": -3.39901733}, "rotation": {"x": 0.0, "y": 244.125916, "z": 0.0}}, {"objectName": "Spatula_1d69618b", "position": {"x": -0.7401416, "y": 0.8284667, "z": -3.588819}, "rotation": {"x": 9.92312057e-15, "y": 45.0000343, "z": 9.923131e-15}}, {"objectName": "Knife_1b546504", "position": {"x": -0.244763374, "y": 0.9679183, "z": -2.72939873}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_6f13b4f9", "position": {"x": -0.9729337, "y": 0.938606143, "z": -3.81668615}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "PepperShaker_8ae9f292", "position": {"x": -2.35991621, "y": 0.890926957, "z": -0.204987615}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "ButterKnife_e676ac70", "position": {"x": -2.76173234, "y": 0.894662857, "z": -0.462292016}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Apple_4eadea0d", "position": {"x": -3.36445284, "y": 0.956032634, "z": -0.204982013}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "DishSponge_7e2c9e01", "position": {"x": -0.5811479, "y": 0.08019185, "z": -3.24392748}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Potato_5e3edff7", "position": {"x": -0.376194179, "y": 1.31857514, "z": -0.6459999}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Egg_571582a1", "position": {"x": -0.283440024, "y": 1.52977717, "z": -0.29706642}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Spoon_7dcfa4cd", "position": {"x": -2.560824, "y": 0.895750165, "z": -0.2907554}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Lettuce_ec37c659", "position": {"x": -3.94221377, "y": 0.7814844, "z": -0.369833976}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f3c7368a", "position": {"x": -4.037083, "y": 0.7550884, "z": -3.52267456}, "rotation": {"x": 0.0, "y": 244.125916, "z": 0.0}}, {"objectName": "Bowl_1f51c27f", "position": {"x": -0.252378881, "y": 1.28490734, "z": -0.5296893}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}], "object_toggles": [], "random_seed": 1752662116, "scene_num": 28}, "task_id": "trial_T20190907_180407_424764", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2IU5TX5S0FA1C_3TXWC2NHN277G601X380M841CY19SK", "high_descs": ["Step forward a few steps, turn left, stop at table.", "Pick up knife behind tomato on table.", "Turn left, walk to wall, turn left, stop at black table.", "Slice bread on table.", "Turn left, go to black table in corner.", "Put knife on table.", "Turn around, go back to round table.", "Pick up slice of bread.", "Turn left, take bread slice to stove.", "Put bread in microwave.", "Take bread out of microwave, turn left, turn left, go to wall, turn right, stop at corner table.", "Set bread on corner table behind knife."], "task_desc": "Microwave a slice of bread and place back on table with knife.", "votes": [1, 1]}, {"assignment_id": "A3HL2LL0LEPZT8_3YDGXNSEO2BO0GFG2A64O9TKEG048B", "high_descs": ["Go forward a step, turn left, go forward to the white table.", "Take the knife next to the tomato from the table.", "Turn left, go forward to the wall, turn left, go forward to the black round table.", "Cut the bread on the table into slices.", "Turn around, go forward to the black stand and white table.", "Put the knife on the front of the black stand.", "Turn around, go forward to the black round table.", "Take a slice of bread from the table.", "Turn around, go forward, at the door on the left turn right, go forward to the oven.", "Heat the bread in the microwave above the oven, take the bread from the microwave.", "Turn around, go forward to the door, turn right, go forward to the black stand and white table.", "Put the bread on the black stand between the lettuce and salt."], "task_desc": "Put a hot slice of bread on a stand.", "votes": [1, 1]}, {"assignment_id": "AFU00NU09CFXE_3YT88D1N0BFNDLYDBLYO49A5VE8K3V", "high_descs": ["Move to the white table against the wall.", "Pick up the butter knife from the table that's by the tomato.", "Turn and carry the knife to the round black kitchen table in the corner across the room.", "Use the knife to slice half the loaf of bread on the table.", "Turn around the carry the knife to the small square table in the opposite corner.", "Place the knife in front of the  head of lettuce on the small black square table.", "Turn around and return to the round black table in the opposite corner.", "Pick up a slice of bread from the table.", "Carry the slice of bread to the stove.", "Open the microwave above the stove, place the bread inside, and remove once heated.", "Carry the heated slice of bread to the small black square table in the corner next to the white table.", "Place the bread slice next to the salt shaker on the black table."], "task_desc": "Move a heated slice of bread to a small square table.", "votes": [1, 1]}]}}