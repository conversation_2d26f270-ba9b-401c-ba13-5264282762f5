
(define (problem plan_trial_T20190919_024225_570009)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Apple_bar__minus_03_dot_10_bar__plus_00_dot_76_bar__minus_00_dot_53 - object
        Apple_bar__minus_03_dot_18_bar__plus_00_dot_68_bar__minus_02_dot_00 - object
        Bowl_bar__minus_00_dot_13_bar__plus_01_dot_01_bar__minus_00_dot_80 - object
        Bowl_bar__minus_00_dot_16_bar__plus_00_dot_91_bar__minus_02_dot_91 - object
        Bread_bar__minus_02_dot_87_bar__plus_00_dot_76_bar__minus_00_dot_64 - object
        ButterKnife_bar__minus_00_dot_10_bar__plus_00_dot_92_bar__minus_01_dot_62 - object
        ButterKnife_bar__minus_02_dot_80_bar__plus_00_dot_71_bar__minus_00_dot_07 - object
        ButterKnife_bar__minus_03_dot_17_bar__plus_00_dot_72_bar__minus_00_dot_80 - object
        Chair_bar__minus_02_dot_38_bar__plus_00_dot_02_bar__minus_00_dot_55 - object
        Chair_bar__minus_03_dot_16_bar__plus_00_dot_01_bar__minus_00_dot_99 - object
        Cup_bar__minus_02_dot_63_bar__plus_00_dot_91_bar__minus_03_dot_95 - object
        DishSponge_bar__minus_02_dot_43_bar__plus_00_dot_71_bar__minus_00_dot_67 - object
        DishSponge_bar__minus_02_dot_58_bar__plus_00_dot_71_bar__minus_00_dot_37 - object
        DishSponge_bar__minus_02_dot_72_bar__plus_00_dot_72_bar__minus_00_dot_50 - object
        Egg_bar__minus_03_dot_23_bar__plus_01_dot_43_bar__minus_02_dot_06 - object
        Egg_bar__minus_03_dot_29_bar__plus_00_dot_75_bar__minus_00_dot_37 - object
        Faucet_bar__minus_01_dot_79_bar__plus_01_dot_14_bar__minus_03_dot_91 - object
        Fork_bar__minus_00_dot_34_bar__plus_00_dot_92_bar__minus_00_dot_54 - object
        Fork_bar__minus_02_dot_74_bar__plus_00_dot_71_bar__minus_00_dot_21 - object
        Fork_bar__minus_03_dot_05_bar__plus_00_dot_75_bar__minus_02_dot_86 - object
        Knife_bar__minus_00_dot_65_bar__plus_00_dot_95_bar__minus_03_dot_80 - object
        Lettuce_bar__minus_01_dot_32_bar__plus_00_dot_99_bar__minus_03_dot_76 - object
        LightSwitch_bar__minus_01_dot_11_bar__plus_01_dot_46_bar__plus_00_dot_34 - object
        Mug_bar__minus_00_dot_34_bar__plus_00_dot_08_bar__minus_01_dot_49 - object
        Mug_bar__minus_01_dot_98_bar__plus_00_dot_79_bar__minus_03_dot_70 - object
        Pan_bar__minus_00_dot_17_bar__plus_00_dot_98_bar__minus_02_dot_45 - object
        PepperShaker_bar__minus_00_dot_50_bar__plus_00_dot_91_bar__minus_01_dot_43 - object
        PepperShaker_bar__minus_03_dot_31_bar__plus_00_dot_91_bar__minus_03_dot_33 - object
        PepperShaker_bar__minus_03_dot_48_bar__plus_00_dot_91_bar__minus_02_dot_83 - object
        Plate_bar__minus_03_dot_08_bar__plus_01_dot_40_bar__minus_01_dot_94 - object
        Potato_bar__minus_00_dot_24_bar__plus_00_dot_96_bar__minus_03_dot_11 - object
        Potato_bar__minus_03_dot_01_bar__plus_00_dot_93_bar__minus_02_dot_50 - object
        Pot_bar__minus_02_dot_84_bar__plus_00_dot_90_bar__minus_03_dot_75 - object
        SaltShaker_bar__minus_03_dot_14_bar__plus_00_dot_74_bar__minus_02_dot_91 - object
        Sink_bar__minus_01_dot_79_bar__plus_00_dot_90_bar__minus_03_dot_75 - object
        SoapBottle_bar__minus_03_dot_20_bar__plus_00_dot_71_bar__minus_00_dot_56 - object
        Spatula_bar__minus_03_dot_48_bar__plus_00_dot_93_bar__minus_02_dot_93 - object
        Spoon_bar__minus_00_dot_34_bar__plus_00_dot_92_bar__minus_01_dot_49 - object
        StoveKnob_bar__minus_00_dot_57_bar__plus_00_dot_90_bar__minus_02_dot_00 - object
        StoveKnob_bar__minus_00_dot_57_bar__plus_00_dot_90_bar__minus_02_dot_17 - object
        StoveKnob_bar__minus_00_dot_57_bar__plus_00_dot_90_bar__minus_02_dot_31 - object
        StoveKnob_bar__minus_00_dot_57_bar__plus_00_dot_90_bar__minus_02_dot_47 - object
        Tomato_bar__minus_02_dot_65_bar__plus_00_dot_76_bar__minus_00_dot_89 - object
        Cabinet_bar__minus_00_dot_19_bar__plus_01_dot_21_bar__minus_04_dot_24 - receptacle
        Cabinet_bar__minus_00_dot_23_bar__plus_01_dot_80_bar__minus_00_dot_45 - receptacle
        Cabinet_bar__minus_00_dot_23_bar__plus_01_dot_80_bar__minus_01_dot_74 - receptacle
        Cabinet_bar__minus_00_dot_23_bar__plus_01_dot_80_bar__minus_02_dot_73 - receptacle
        Cabinet_bar__minus_00_dot_24_bar__plus_02_dot_10_bar__minus_01_dot_77 - receptacle
        Cabinet_bar__minus_00_dot_24_bar__plus_02_dot_10_bar__minus_02_dot_71 - receptacle
        Cabinet_bar__minus_00_dot_58_bar__plus_00_dot_39_bar__minus_00_dot_45 - receptacle
        Cabinet_bar__minus_00_dot_58_bar__plus_00_dot_39_bar__minus_01_dot_73 - receptacle
        Cabinet_bar__minus_00_dot_58_bar__plus_00_dot_39_bar__minus_02_dot_74 - receptacle
        Cabinet_bar__minus_00_dot_58_bar__plus_00_dot_39_bar__minus_03_dot_48 - receptacle
        Cabinet_bar__minus_00_dot_61_bar__plus_00_dot_39_bar__minus_03_dot_51 - receptacle
        Cabinet_bar__minus_01_dot_23_bar__plus_01_dot_81_bar__minus_03_dot_76 - receptacle
        Cabinet_bar__minus_01_dot_36_bar__plus_00_dot_39_bar__minus_03_dot_51 - receptacle
        Cabinet_bar__minus_01_dot_38_bar__plus_00_dot_39_bar__minus_03_dot_51 - receptacle
        Cabinet_bar__minus_02_dot_15_bar__plus_00_dot_39_bar__minus_03_dot_51 - receptacle
        Cabinet_bar__minus_02_dot_81_bar__plus_01_dot_81_bar__minus_03_dot_76 - receptacle
        Cabinet_bar__minus_02_dot_98_bar__plus_00_dot_39_bar__minus_03_dot_20 - receptacle
        Cabinet_bar__minus_03_dot_02_bar__plus_02_dot_10_bar__minus_01_dot_74 - receptacle
        Cabinet_bar__minus_03_dot_02_bar__plus_02_dot_10_bar__minus_02_dot_68 - receptacle
        Cabinet_bar__minus_03_dot_23_bar__plus_01_dot_80_bar__minus_03_dot_34 - receptacle
        Cabinet_bar__minus_06_dot_01_bar__plus_01_dot_14_bar__minus_06_dot_02 - receptacle
        CoffeeMachine_bar__minus_03_dot_10_bar__plus_00_dot_71_bar__minus_00_dot_19 - receptacle
        CounterTop_bar__minus_00_dot_27_bar__plus_00_dot_95_bar__minus_01_dot_09 - receptacle
        CounterTop_bar__minus_01_dot_79_bar__plus_00_dot_95_bar__minus_03_dot_80 - receptacle
        DiningTable_bar__minus_02_dot_87_bar__plus_00_dot_68_bar__minus_00_dot_51 - receptacle
        Drawer_bar__minus_00_dot_45_bar__plus_00_dot_80_bar__minus_00_dot_76 - receptacle
        Drawer_bar__minus_00_dot_45_bar__plus_00_dot_80_bar__minus_01_dot_41 - receptacle
        Drawer_bar__minus_00_dot_45_bar__plus_00_dot_80_bar__minus_02_dot_97 - receptacle
        Drawer_bar__minus_00_dot_45_bar__plus_00_dot_80_bar__minus_03_dot_35 - receptacle
        Drawer_bar__minus_03_dot_10_bar__plus_00_dot_80_bar__minus_02_dot_97 - receptacle
        Fridge_bar__minus_03_dot_19_bar__plus_00_dot_00_bar__minus_02_dot_19 - receptacle
        GarbageCan_bar__minus_00_dot_28_bar__plus_00_dot_00_bar__minus_00_dot_04 - receptacle
        Microwave_bar__minus_00_dot_14_bar__plus_00_dot_90_bar__minus_00_dot_91 - receptacle
        Sink_bar__minus_01_dot_79_bar__plus_00_dot_90_bar__minus_03_dot_75_bar_SinkBasin - receptacle
        StoveBurner_bar__minus_00_dot_17_bar__plus_00_dot_94_bar__minus_02_dot_03 - receptacle
        StoveBurner_bar__minus_00_dot_17_bar__plus_00_dot_94_bar__minus_02_dot_45 - receptacle
        StoveBurner_bar__minus_00_dot_38_bar__plus_00_dot_94_bar__minus_02_dot_03 - receptacle
        StoveBurner_bar__minus_00_dot_38_bar__plus_00_dot_94_bar__minus_02_dot_45 - receptacle
        Toaster_bar__minus_00_dot_23_bar__plus_00_dot_90_bar__minus_03_dot_41 - receptacle
        loc_bar__minus_9_bar__minus_10_bar_2_bar_45 - location
        loc_bar__minus_9_bar__minus_12_bar_2_bar__minus_15 - location
        loc_bar__minus_4_bar__minus_10_bar_1_bar__minus_15 - location
        loc_bar__minus_4_bar__minus_8_bar_1_bar_45 - location
        loc_bar__minus_4_bar__minus_10_bar_1_bar_60 - location
        loc_bar__minus_4_bar__minus_13_bar_1_bar_60 - location
        loc_bar__minus_4_bar__minus_13_bar_3_bar_45 - location
        loc_bar__minus_8_bar__minus_13_bar_2_bar_60 - location
        loc_bar__minus_4_bar__minus_13_bar_2_bar__minus_15 - location
        loc_bar__minus_9_bar__minus_12_bar_3_bar_45 - location
        loc_bar__minus_8_bar__minus_9_bar_3_bar__minus_30 - location
        loc_bar__minus_4_bar__minus_13_bar_1_bar_45 - location
        loc_bar__minus_4_bar__minus_3_bar_1_bar_60 - location
        loc_bar__minus_4_bar__minus_5_bar_1_bar_45 - location
        loc_bar__minus_7_bar__minus_13_bar_2_bar_60 - location
        loc_bar__minus_9_bar__minus_5_bar_3_bar_60 - location
        loc_bar__minus_7_bar__minus_4_bar_1_bar_45 - location
        loc_bar__minus_4_bar__minus_10_bar_2_bar_45 - location
        loc_bar__minus_8_bar__minus_11_bar_3_bar__minus_30 - location
        loc_bar__minus_5_bar__minus_13_bar_1_bar_30 - location
        loc_bar__minus_6_bar__minus_13_bar_1_bar_60 - location
        loc_bar__minus_4_bar__minus_10_bar_1_bar__minus_30 - location
        loc_bar__minus_4_bar__minus_10_bar_1_bar_45 - location
        loc_bar__minus_8_bar__minus_10_bar_2_bar_45 - location
        loc_bar__minus_9_bar__minus_9_bar_3_bar_60 - location
        loc_bar__minus_4_bar__minus_1_bar_0_bar_15 - location
        loc_bar__minus_8_bar__minus_3_bar_3_bar_60 - location
        loc_bar__minus_7_bar__minus_11_bar_1_bar_45 - location
        loc_bar__minus_4_bar__minus_8_bar_1_bar_60 - location
        loc_bar__minus_5_bar__minus_10_bar_2_bar_45 - location
        loc_bar__minus_9_bar__minus_12_bar_3_bar_60 - location
        loc_bar__minus_4_bar__minus_7_bar_1_bar__minus_30 - location
        loc_bar__minus_4_bar__minus_1_bar_1_bar_60 - location
        loc_bar__minus_6_bar__minus_2_bar_1_bar_60 - location
        loc_bar__minus_8_bar__minus_13_bar_3_bar_0 - location
        loc_bar__minus_4_bar__minus_12_bar_1_bar_60 - location
        loc_bar__minus_4_bar__minus_2_bar_1_bar__minus_15 - location
        loc_bar__minus_4_bar__minus_3_bar_1_bar_45 - location
        loc_bar__minus_8_bar__minus_1_bar_3_bar_45 - location
        loc_bar__minus_7_bar__minus_2_bar_3_bar_45 - location
        loc_bar__minus_4_bar__minus_6_bar_1_bar_60 - location
        loc_bar__minus_4_bar__minus_9_bar_1_bar_60 - location
        loc_bar__minus_4_bar__minus_7_bar_1_bar__minus_15 - location
        loc_bar__minus_7_bar__minus_11_bar_0_bar_30 - location
        )
    

(:init


        (receptacleType Cabinet_bar__minus_00_dot_61_bar__plus_00_dot_39_bar__minus_03_dot_51 CabinetType)
        (receptacleType CounterTop_bar__minus_01_dot_79_bar__plus_00_dot_95_bar__minus_03_dot_80 CounterTopType)
        (receptacleType Cabinet_bar__minus_02_dot_98_bar__plus_00_dot_39_bar__minus_03_dot_20 CabinetType)
        (receptacleType Cabinet_bar__minus_02_dot_15_bar__plus_00_dot_39_bar__minus_03_dot_51 CabinetType)
        (receptacleType Cabinet_bar__minus_00_dot_19_bar__plus_01_dot_21_bar__minus_04_dot_24 CabinetType)
        (receptacleType CounterTop_bar__minus_00_dot_27_bar__plus_00_dot_95_bar__minus_01_dot_09 CounterTopType)
        (receptacleType Cabinet_bar__minus_00_dot_24_bar__plus_02_dot_10_bar__minus_02_dot_71 CabinetType)
        (receptacleType Drawer_bar__minus_00_dot_45_bar__plus_00_dot_80_bar__minus_02_dot_97 DrawerType)
        (receptacleType Cabinet_bar__minus_02_dot_81_bar__plus_01_dot_81_bar__minus_03_dot_76 CabinetType)
        (receptacleType StoveBurner_bar__minus_00_dot_38_bar__plus_00_dot_94_bar__minus_02_dot_03 StoveBurnerType)
        (receptacleType Cabinet_bar__minus_01_dot_36_bar__plus_00_dot_39_bar__minus_03_dot_51 CabinetType)
        (receptacleType Drawer_bar__minus_00_dot_45_bar__plus_00_dot_80_bar__minus_03_dot_35 DrawerType)
        (receptacleType Fridge_bar__minus_03_dot_19_bar__plus_00_dot_00_bar__minus_02_dot_19 FridgeType)
        (receptacleType Cabinet_bar__minus_00_dot_58_bar__plus_00_dot_39_bar__minus_02_dot_74 CabinetType)
        (receptacleType Cabinet_bar__minus_01_dot_23_bar__plus_01_dot_81_bar__minus_03_dot_76 CabinetType)
        (receptacleType Drawer_bar__minus_00_dot_45_bar__plus_00_dot_80_bar__minus_01_dot_41 DrawerType)
        (receptacleType Cabinet_bar__minus_00_dot_23_bar__plus_01_dot_80_bar__minus_00_dot_45 CabinetType)
        (receptacleType StoveBurner_bar__minus_00_dot_17_bar__plus_00_dot_94_bar__minus_02_dot_03 StoveBurnerType)
        (receptacleType DiningTable_bar__minus_02_dot_87_bar__plus_00_dot_68_bar__minus_00_dot_51 DiningTableType)
        (receptacleType Drawer_bar__minus_00_dot_45_bar__plus_00_dot_80_bar__minus_00_dot_76 DrawerType)
        (receptacleType Toaster_bar__minus_00_dot_23_bar__plus_00_dot_90_bar__minus_03_dot_41 ToasterType)
        (receptacleType Cabinet_bar__minus_00_dot_58_bar__plus_00_dot_39_bar__minus_03_dot_48 CabinetType)
        (receptacleType StoveBurner_bar__minus_00_dot_17_bar__plus_00_dot_94_bar__minus_02_dot_45 StoveBurnerType)
        (receptacleType Cabinet_bar__minus_03_dot_23_bar__plus_01_dot_80_bar__minus_03_dot_34 CabinetType)
        (receptacleType GarbageCan_bar__minus_00_dot_28_bar__plus_00_dot_00_bar__minus_00_dot_04 GarbageCanType)
        (receptacleType Cabinet_bar__minus_03_dot_02_bar__plus_02_dot_10_bar__minus_01_dot_74 CabinetType)
        (receptacleType Sink_bar__minus_01_dot_79_bar__plus_00_dot_90_bar__minus_03_dot_75_bar_SinkBasin SinkBasinType)
        (receptacleType Cabinet_bar__minus_03_dot_02_bar__plus_02_dot_10_bar__minus_02_dot_68 CabinetType)
        (receptacleType Cabinet_bar__minus_00_dot_23_bar__plus_01_dot_80_bar__minus_01_dot_74 CabinetType)
        (receptacleType Drawer_bar__minus_03_dot_10_bar__plus_00_dot_80_bar__minus_02_dot_97 DrawerType)
        (receptacleType Cabinet_bar__minus_00_dot_58_bar__plus_00_dot_39_bar__minus_00_dot_45 CabinetType)
        (receptacleType Cabinet_bar__minus_01_dot_38_bar__plus_00_dot_39_bar__minus_03_dot_51 CabinetType)
        (receptacleType Cabinet_bar__minus_00_dot_58_bar__plus_00_dot_39_bar__minus_01_dot_73 CabinetType)
        (receptacleType Cabinet_bar__minus_00_dot_24_bar__plus_02_dot_10_bar__minus_01_dot_77 CabinetType)
        (receptacleType StoveBurner_bar__minus_00_dot_38_bar__plus_00_dot_94_bar__minus_02_dot_45 StoveBurnerType)
        (receptacleType Microwave_bar__minus_00_dot_14_bar__plus_00_dot_90_bar__minus_00_dot_91 MicrowaveType)
        (receptacleType CoffeeMachine_bar__minus_03_dot_10_bar__plus_00_dot_71_bar__minus_00_dot_19 CoffeeMachineType)
        (receptacleType Cabinet_bar__minus_06_dot_01_bar__plus_01_dot_14_bar__minus_06_dot_02 CabinetType)
        (receptacleType Cabinet_bar__minus_00_dot_23_bar__plus_01_dot_80_bar__minus_02_dot_73 CabinetType)
        (objectType Tomato_bar__minus_02_dot_65_bar__plus_00_dot_76_bar__minus_00_dot_89 TomatoType)
        (objectType ButterKnife_bar__minus_00_dot_10_bar__plus_00_dot_92_bar__minus_01_dot_62 ButterKnifeType)
        (objectType Lettuce_bar__minus_01_dot_32_bar__plus_00_dot_99_bar__minus_03_dot_76 LettuceType)
        (objectType Bowl_bar__minus_00_dot_16_bar__plus_00_dot_91_bar__minus_02_dot_91 BowlType)
        (objectType LightSwitch_bar__minus_01_dot_11_bar__plus_01_dot_46_bar__plus_00_dot_34 LightSwitchType)
        (objectType Apple_bar__minus_03_dot_10_bar__plus_00_dot_76_bar__minus_00_dot_53 AppleType)
        (objectType Mug_bar__minus_00_dot_34_bar__plus_00_dot_08_bar__minus_01_dot_49 MugType)
        (objectType Mug_bar__minus_01_dot_98_bar__plus_00_dot_79_bar__minus_03_dot_70 MugType)
        (objectType ButterKnife_bar__minus_02_dot_80_bar__plus_00_dot_71_bar__minus_00_dot_07 ButterKnifeType)
        (objectType DishSponge_bar__minus_02_dot_58_bar__plus_00_dot_71_bar__minus_00_dot_37 DishSpongeType)
        (objectType Pot_bar__minus_02_dot_84_bar__plus_00_dot_90_bar__minus_03_dot_75 PotType)
        (objectType SoapBottle_bar__minus_03_dot_20_bar__plus_00_dot_71_bar__minus_00_dot_56 SoapBottleType)
        (objectType StoveKnob_bar__minus_00_dot_57_bar__plus_00_dot_90_bar__minus_02_dot_17 StoveKnobType)
        (objectType StoveKnob_bar__minus_00_dot_57_bar__plus_00_dot_90_bar__minus_02_dot_31 StoveKnobType)
        (objectType Pan_bar__minus_00_dot_17_bar__plus_00_dot_98_bar__minus_02_dot_45 PanType)
        (objectType DishSponge_bar__minus_02_dot_43_bar__plus_00_dot_71_bar__minus_00_dot_67 DishSpongeType)
        (objectType Sink_bar__minus_01_dot_79_bar__plus_00_dot_90_bar__minus_03_dot_75 SinkType)
        (objectType Chair_bar__minus_03_dot_16_bar__plus_00_dot_01_bar__minus_00_dot_99 ChairType)
        (objectType Fork_bar__minus_02_dot_74_bar__plus_00_dot_71_bar__minus_00_dot_21 ForkType)
        (objectType Fork_bar__minus_03_dot_05_bar__plus_00_dot_75_bar__minus_02_dot_86 ForkType)
        (objectType Chair_bar__minus_02_dot_38_bar__plus_00_dot_02_bar__minus_00_dot_55 ChairType)
        (objectType Fork_bar__minus_00_dot_34_bar__plus_00_dot_92_bar__minus_00_dot_54 ForkType)
        (objectType Potato_bar__minus_00_dot_24_bar__plus_00_dot_96_bar__minus_03_dot_11 PotatoType)
        (objectType Spoon_bar__minus_00_dot_34_bar__plus_00_dot_92_bar__minus_01_dot_49 SpoonType)
        (objectType Egg_bar__minus_03_dot_23_bar__plus_01_dot_43_bar__minus_02_dot_06 EggType)
        (objectType Egg_bar__minus_03_dot_29_bar__plus_00_dot_75_bar__minus_00_dot_37 EggType)
        (objectType SaltShaker_bar__minus_03_dot_14_bar__plus_00_dot_74_bar__minus_02_dot_91 SaltShakerType)
        (objectType Spatula_bar__minus_03_dot_48_bar__plus_00_dot_93_bar__minus_02_dot_93 SpatulaType)
        (objectType Cup_bar__minus_02_dot_63_bar__plus_00_dot_91_bar__minus_03_dot_95 CupType)
        (objectType PepperShaker_bar__minus_03_dot_48_bar__plus_00_dot_91_bar__minus_02_dot_83 PepperShakerType)
        (objectType StoveKnob_bar__minus_00_dot_57_bar__plus_00_dot_90_bar__minus_02_dot_00 StoveKnobType)
        (objectType Potato_bar__minus_03_dot_01_bar__plus_00_dot_93_bar__minus_02_dot_50 PotatoType)
        (objectType Bread_bar__minus_02_dot_87_bar__plus_00_dot_76_bar__minus_00_dot_64 BreadType)
        (objectType Bowl_bar__minus_00_dot_13_bar__plus_01_dot_01_bar__minus_00_dot_80 BowlType)
        (objectType StoveKnob_bar__minus_00_dot_57_bar__plus_00_dot_90_bar__minus_02_dot_47 StoveKnobType)
        (objectType Plate_bar__minus_03_dot_08_bar__plus_01_dot_40_bar__minus_01_dot_94 PlateType)
        (objectType ButterKnife_bar__minus_03_dot_17_bar__plus_00_dot_72_bar__minus_00_dot_80 ButterKnifeType)
        (objectType PepperShaker_bar__minus_00_dot_50_bar__plus_00_dot_91_bar__minus_01_dot_43 PepperShakerType)
        (objectType Apple_bar__minus_03_dot_18_bar__plus_00_dot_68_bar__minus_02_dot_00 AppleType)
        (objectType Knife_bar__minus_00_dot_65_bar__plus_00_dot_95_bar__minus_03_dot_80 KnifeType)
        (objectType DishSponge_bar__minus_02_dot_72_bar__plus_00_dot_72_bar__minus_00_dot_50 DishSpongeType)
        (objectType PepperShaker_bar__minus_03_dot_31_bar__plus_00_dot_91_bar__minus_03_dot_33 PepperShakerType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CounterTopType SaltShakerType)
        (canContain CounterTopType BreadType)
        (canContain CounterTopType DishSpongeType)
        (canContain CounterTopType BowlType)
        (canContain CounterTopType PotType)
        (canContain CounterTopType MugType)
        (canContain CounterTopType EggType)
        (canContain CounterTopType ForkType)
        (canContain CounterTopType SpoonType)
        (canContain CounterTopType SoapBottleType)
        (canContain CounterTopType LettuceType)
        (canContain CounterTopType PotatoType)
        (canContain CounterTopType ButterKnifeType)
        (canContain CounterTopType CupType)
        (canContain CounterTopType PlateType)
        (canContain CounterTopType PepperShakerType)
        (canContain CounterTopType TomatoType)
        (canContain CounterTopType KnifeType)
        (canContain CounterTopType AppleType)
        (canContain CounterTopType PanType)
        (canContain CounterTopType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CounterTopType SaltShakerType)
        (canContain CounterTopType BreadType)
        (canContain CounterTopType DishSpongeType)
        (canContain CounterTopType BowlType)
        (canContain CounterTopType PotType)
        (canContain CounterTopType MugType)
        (canContain CounterTopType EggType)
        (canContain CounterTopType ForkType)
        (canContain CounterTopType SpoonType)
        (canContain CounterTopType SoapBottleType)
        (canContain CounterTopType LettuceType)
        (canContain CounterTopType PotatoType)
        (canContain CounterTopType ButterKnifeType)
        (canContain CounterTopType CupType)
        (canContain CounterTopType PlateType)
        (canContain CounterTopType PepperShakerType)
        (canContain CounterTopType TomatoType)
        (canContain CounterTopType KnifeType)
        (canContain CounterTopType AppleType)
        (canContain CounterTopType PanType)
        (canContain CounterTopType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain FridgeType BreadType)
        (canContain FridgeType BowlType)
        (canContain FridgeType PotType)
        (canContain FridgeType MugType)
        (canContain FridgeType EggType)
        (canContain FridgeType LettuceType)
        (canContain FridgeType PotatoType)
        (canContain FridgeType CupType)
        (canContain FridgeType PlateType)
        (canContain FridgeType TomatoType)
        (canContain FridgeType AppleType)
        (canContain FridgeType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain DiningTableType SaltShakerType)
        (canContain DiningTableType BreadType)
        (canContain DiningTableType DishSpongeType)
        (canContain DiningTableType BowlType)
        (canContain DiningTableType PotType)
        (canContain DiningTableType MugType)
        (canContain DiningTableType EggType)
        (canContain DiningTableType ForkType)
        (canContain DiningTableType SpoonType)
        (canContain DiningTableType SoapBottleType)
        (canContain DiningTableType LettuceType)
        (canContain DiningTableType PotatoType)
        (canContain DiningTableType ButterKnifeType)
        (canContain DiningTableType CupType)
        (canContain DiningTableType PlateType)
        (canContain DiningTableType PepperShakerType)
        (canContain DiningTableType TomatoType)
        (canContain DiningTableType KnifeType)
        (canContain DiningTableType AppleType)
        (canContain DiningTableType PanType)
        (canContain DiningTableType SpatulaType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain GarbageCanType BreadType)
        (canContain GarbageCanType DishSpongeType)
        (canContain GarbageCanType EggType)
        (canContain GarbageCanType SoapBottleType)
        (canContain GarbageCanType LettuceType)
        (canContain GarbageCanType PotatoType)
        (canContain GarbageCanType TomatoType)
        (canContain GarbageCanType AppleType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain SinkBasinType DishSpongeType)
        (canContain SinkBasinType BowlType)
        (canContain SinkBasinType PotType)
        (canContain SinkBasinType MugType)
        (canContain SinkBasinType EggType)
        (canContain SinkBasinType ForkType)
        (canContain SinkBasinType SpoonType)
        (canContain SinkBasinType LettuceType)
        (canContain SinkBasinType PotatoType)
        (canContain SinkBasinType ButterKnifeType)
        (canContain SinkBasinType CupType)
        (canContain SinkBasinType PlateType)
        (canContain SinkBasinType TomatoType)
        (canContain SinkBasinType KnifeType)
        (canContain SinkBasinType AppleType)
        (canContain SinkBasinType PanType)
        (canContain SinkBasinType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain MicrowaveType MugType)
        (canContain MicrowaveType PotatoType)
        (canContain MicrowaveType EggType)
        (canContain MicrowaveType AppleType)
        (canContain MicrowaveType TomatoType)
        (canContain MicrowaveType BreadType)
        (canContain MicrowaveType CupType)
        (canContain MicrowaveType PlateType)
        (canContain MicrowaveType BowlType)
        (canContain CoffeeMachineType MugType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (pickupable Tomato_bar__minus_02_dot_65_bar__plus_00_dot_76_bar__minus_00_dot_89)
        (pickupable ButterKnife_bar__minus_00_dot_10_bar__plus_00_dot_92_bar__minus_01_dot_62)
        (pickupable Lettuce_bar__minus_01_dot_32_bar__plus_00_dot_99_bar__minus_03_dot_76)
        (pickupable Bowl_bar__minus_00_dot_16_bar__plus_00_dot_91_bar__minus_02_dot_91)
        (pickupable Apple_bar__minus_03_dot_10_bar__plus_00_dot_76_bar__minus_00_dot_53)
        (pickupable Mug_bar__minus_00_dot_34_bar__plus_00_dot_08_bar__minus_01_dot_49)
        (pickupable Mug_bar__minus_01_dot_98_bar__plus_00_dot_79_bar__minus_03_dot_70)
        (pickupable ButterKnife_bar__minus_02_dot_80_bar__plus_00_dot_71_bar__minus_00_dot_07)
        (pickupable DishSponge_bar__minus_02_dot_58_bar__plus_00_dot_71_bar__minus_00_dot_37)
        (pickupable Pot_bar__minus_02_dot_84_bar__plus_00_dot_90_bar__minus_03_dot_75)
        (pickupable SoapBottle_bar__minus_03_dot_20_bar__plus_00_dot_71_bar__minus_00_dot_56)
        (pickupable Pan_bar__minus_00_dot_17_bar__plus_00_dot_98_bar__minus_02_dot_45)
        (pickupable DishSponge_bar__minus_02_dot_43_bar__plus_00_dot_71_bar__minus_00_dot_67)
        (pickupable Fork_bar__minus_02_dot_74_bar__plus_00_dot_71_bar__minus_00_dot_21)
        (pickupable Fork_bar__minus_03_dot_05_bar__plus_00_dot_75_bar__minus_02_dot_86)
        (pickupable Fork_bar__minus_00_dot_34_bar__plus_00_dot_92_bar__minus_00_dot_54)
        (pickupable Potato_bar__minus_00_dot_24_bar__plus_00_dot_96_bar__minus_03_dot_11)
        (pickupable Spoon_bar__minus_00_dot_34_bar__plus_00_dot_92_bar__minus_01_dot_49)
        (pickupable Egg_bar__minus_03_dot_23_bar__plus_01_dot_43_bar__minus_02_dot_06)
        (pickupable Egg_bar__minus_03_dot_29_bar__plus_00_dot_75_bar__minus_00_dot_37)
        (pickupable SaltShaker_bar__minus_03_dot_14_bar__plus_00_dot_74_bar__minus_02_dot_91)
        (pickupable Spatula_bar__minus_03_dot_48_bar__plus_00_dot_93_bar__minus_02_dot_93)
        (pickupable Cup_bar__minus_02_dot_63_bar__plus_00_dot_91_bar__minus_03_dot_95)
        (pickupable PepperShaker_bar__minus_03_dot_48_bar__plus_00_dot_91_bar__minus_02_dot_83)
        (pickupable Potato_bar__minus_03_dot_01_bar__plus_00_dot_93_bar__minus_02_dot_50)
        (pickupable Bread_bar__minus_02_dot_87_bar__plus_00_dot_76_bar__minus_00_dot_64)
        (pickupable Bowl_bar__minus_00_dot_13_bar__plus_01_dot_01_bar__minus_00_dot_80)
        (pickupable Plate_bar__minus_03_dot_08_bar__plus_01_dot_40_bar__minus_01_dot_94)
        (pickupable ButterKnife_bar__minus_03_dot_17_bar__plus_00_dot_72_bar__minus_00_dot_80)
        (pickupable PepperShaker_bar__minus_00_dot_50_bar__plus_00_dot_91_bar__minus_01_dot_43)
        (pickupable Apple_bar__minus_03_dot_18_bar__plus_00_dot_68_bar__minus_02_dot_00)
        (pickupable Knife_bar__minus_00_dot_65_bar__plus_00_dot_95_bar__minus_03_dot_80)
        (pickupable DishSponge_bar__minus_02_dot_72_bar__plus_00_dot_72_bar__minus_00_dot_50)
        (pickupable PepperShaker_bar__minus_03_dot_31_bar__plus_00_dot_91_bar__minus_03_dot_33)
        (isReceptacleObject Bowl_bar__minus_00_dot_16_bar__plus_00_dot_91_bar__minus_02_dot_91)
        (isReceptacleObject Mug_bar__minus_00_dot_34_bar__plus_00_dot_08_bar__minus_01_dot_49)
        (isReceptacleObject Mug_bar__minus_01_dot_98_bar__plus_00_dot_79_bar__minus_03_dot_70)
        (isReceptacleObject Pot_bar__minus_02_dot_84_bar__plus_00_dot_90_bar__minus_03_dot_75)
        (isReceptacleObject Pan_bar__minus_00_dot_17_bar__plus_00_dot_98_bar__minus_02_dot_45)
        (isReceptacleObject Cup_bar__minus_02_dot_63_bar__plus_00_dot_91_bar__minus_03_dot_95)
        (isReceptacleObject Bowl_bar__minus_00_dot_13_bar__plus_01_dot_01_bar__minus_00_dot_80)
        (isReceptacleObject Plate_bar__minus_03_dot_08_bar__plus_01_dot_40_bar__minus_01_dot_94)
        (openable Cabinet_bar__minus_00_dot_61_bar__plus_00_dot_39_bar__minus_03_dot_51)
        (openable Cabinet_bar__minus_02_dot_15_bar__plus_00_dot_39_bar__minus_03_dot_51)
        (openable Cabinet_bar__minus_00_dot_19_bar__plus_01_dot_21_bar__minus_04_dot_24)
        (openable Cabinet_bar__minus_02_dot_81_bar__plus_01_dot_81_bar__minus_03_dot_76)
        (openable Cabinet_bar__minus_01_dot_36_bar__plus_00_dot_39_bar__minus_03_dot_51)
        (openable Fridge_bar__minus_03_dot_19_bar__plus_00_dot_00_bar__minus_02_dot_19)
        (openable Cabinet_bar__minus_00_dot_58_bar__plus_00_dot_39_bar__minus_02_dot_74)
        (openable Cabinet_bar__minus_01_dot_23_bar__plus_01_dot_81_bar__minus_03_dot_76)
        (openable Cabinet_bar__minus_00_dot_58_bar__plus_00_dot_39_bar__minus_03_dot_48)
        (openable Cabinet_bar__minus_03_dot_02_bar__plus_02_dot_10_bar__minus_01_dot_74)
        (openable Cabinet_bar__minus_03_dot_02_bar__plus_02_dot_10_bar__minus_02_dot_68)
        (openable Cabinet_bar__minus_00_dot_58_bar__plus_00_dot_39_bar__minus_00_dot_45)
        (openable Cabinet_bar__minus_01_dot_38_bar__plus_00_dot_39_bar__minus_03_dot_51)
        (openable Cabinet_bar__minus_00_dot_58_bar__plus_00_dot_39_bar__minus_01_dot_73)
        (openable Microwave_bar__minus_00_dot_14_bar__plus_00_dot_90_bar__minus_00_dot_91)
        
        (atLocation agent1 loc_bar__minus_7_bar__minus_11_bar_0_bar_30)
        
        (cleanable Tomato_bar__minus_02_dot_65_bar__plus_00_dot_76_bar__minus_00_dot_89)
        (cleanable ButterKnife_bar__minus_00_dot_10_bar__plus_00_dot_92_bar__minus_01_dot_62)
        (cleanable Lettuce_bar__minus_01_dot_32_bar__plus_00_dot_99_bar__minus_03_dot_76)
        (cleanable Bowl_bar__minus_00_dot_16_bar__plus_00_dot_91_bar__minus_02_dot_91)
        (cleanable Apple_bar__minus_03_dot_10_bar__plus_00_dot_76_bar__minus_00_dot_53)
        (cleanable Mug_bar__minus_00_dot_34_bar__plus_00_dot_08_bar__minus_01_dot_49)
        (cleanable Mug_bar__minus_01_dot_98_bar__plus_00_dot_79_bar__minus_03_dot_70)
        (cleanable ButterKnife_bar__minus_02_dot_80_bar__plus_00_dot_71_bar__minus_00_dot_07)
        (cleanable DishSponge_bar__minus_02_dot_58_bar__plus_00_dot_71_bar__minus_00_dot_37)
        (cleanable Pot_bar__minus_02_dot_84_bar__plus_00_dot_90_bar__minus_03_dot_75)
        (cleanable Pan_bar__minus_00_dot_17_bar__plus_00_dot_98_bar__minus_02_dot_45)
        (cleanable DishSponge_bar__minus_02_dot_43_bar__plus_00_dot_71_bar__minus_00_dot_67)
        (cleanable Fork_bar__minus_02_dot_74_bar__plus_00_dot_71_bar__minus_00_dot_21)
        (cleanable Fork_bar__minus_03_dot_05_bar__plus_00_dot_75_bar__minus_02_dot_86)
        (cleanable Fork_bar__minus_00_dot_34_bar__plus_00_dot_92_bar__minus_00_dot_54)
        (cleanable Potato_bar__minus_00_dot_24_bar__plus_00_dot_96_bar__minus_03_dot_11)
        (cleanable Spoon_bar__minus_00_dot_34_bar__plus_00_dot_92_bar__minus_01_dot_49)
        (cleanable Egg_bar__minus_03_dot_23_bar__plus_01_dot_43_bar__minus_02_dot_06)
        (cleanable Egg_bar__minus_03_dot_29_bar__plus_00_dot_75_bar__minus_00_dot_37)
        (cleanable Spatula_bar__minus_03_dot_48_bar__plus_00_dot_93_bar__minus_02_dot_93)
        (cleanable Cup_bar__minus_02_dot_63_bar__plus_00_dot_91_bar__minus_03_dot_95)
        (cleanable Potato_bar__minus_03_dot_01_bar__plus_00_dot_93_bar__minus_02_dot_50)
        (cleanable Bowl_bar__minus_00_dot_13_bar__plus_01_dot_01_bar__minus_00_dot_80)
        (cleanable Plate_bar__minus_03_dot_08_bar__plus_01_dot_40_bar__minus_01_dot_94)
        (cleanable ButterKnife_bar__minus_03_dot_17_bar__plus_00_dot_72_bar__minus_00_dot_80)
        (cleanable Apple_bar__minus_03_dot_18_bar__plus_00_dot_68_bar__minus_02_dot_00)
        (cleanable Knife_bar__minus_00_dot_65_bar__plus_00_dot_95_bar__minus_03_dot_80)
        (cleanable DishSponge_bar__minus_02_dot_72_bar__plus_00_dot_72_bar__minus_00_dot_50)
        
        (heatable Tomato_bar__minus_02_dot_65_bar__plus_00_dot_76_bar__minus_00_dot_89)
        (heatable Apple_bar__minus_03_dot_10_bar__plus_00_dot_76_bar__minus_00_dot_53)
        (heatable Mug_bar__minus_00_dot_34_bar__plus_00_dot_08_bar__minus_01_dot_49)
        (heatable Mug_bar__minus_01_dot_98_bar__plus_00_dot_79_bar__minus_03_dot_70)
        (heatable Potato_bar__minus_00_dot_24_bar__plus_00_dot_96_bar__minus_03_dot_11)
        (heatable Egg_bar__minus_03_dot_23_bar__plus_01_dot_43_bar__minus_02_dot_06)
        (heatable Egg_bar__minus_03_dot_29_bar__plus_00_dot_75_bar__minus_00_dot_37)
        (heatable Cup_bar__minus_02_dot_63_bar__plus_00_dot_91_bar__minus_03_dot_95)
        (heatable Potato_bar__minus_03_dot_01_bar__plus_00_dot_93_bar__minus_02_dot_50)
        (heatable Bread_bar__minus_02_dot_87_bar__plus_00_dot_76_bar__minus_00_dot_64)
        (heatable Plate_bar__minus_03_dot_08_bar__plus_01_dot_40_bar__minus_01_dot_94)
        (heatable Apple_bar__minus_03_dot_18_bar__plus_00_dot_68_bar__minus_02_dot_00)
        (coolable Tomato_bar__minus_02_dot_65_bar__plus_00_dot_76_bar__minus_00_dot_89)
        (coolable Lettuce_bar__minus_01_dot_32_bar__plus_00_dot_99_bar__minus_03_dot_76)
        (coolable Bowl_bar__minus_00_dot_16_bar__plus_00_dot_91_bar__minus_02_dot_91)
        (coolable Apple_bar__minus_03_dot_10_bar__plus_00_dot_76_bar__minus_00_dot_53)
        (coolable Mug_bar__minus_00_dot_34_bar__plus_00_dot_08_bar__minus_01_dot_49)
        (coolable Mug_bar__minus_01_dot_98_bar__plus_00_dot_79_bar__minus_03_dot_70)
        (coolable Pot_bar__minus_02_dot_84_bar__plus_00_dot_90_bar__minus_03_dot_75)
        (coolable Pan_bar__minus_00_dot_17_bar__plus_00_dot_98_bar__minus_02_dot_45)
        (coolable Potato_bar__minus_00_dot_24_bar__plus_00_dot_96_bar__minus_03_dot_11)
        (coolable Egg_bar__minus_03_dot_23_bar__plus_01_dot_43_bar__minus_02_dot_06)
        (coolable Egg_bar__minus_03_dot_29_bar__plus_00_dot_75_bar__minus_00_dot_37)
        (coolable Cup_bar__minus_02_dot_63_bar__plus_00_dot_91_bar__minus_03_dot_95)
        (coolable Potato_bar__minus_03_dot_01_bar__plus_00_dot_93_bar__minus_02_dot_50)
        (coolable Bread_bar__minus_02_dot_87_bar__plus_00_dot_76_bar__minus_00_dot_64)
        (coolable Bowl_bar__minus_00_dot_13_bar__plus_01_dot_01_bar__minus_00_dot_80)
        (coolable Plate_bar__minus_03_dot_08_bar__plus_01_dot_40_bar__minus_01_dot_94)
        (coolable Apple_bar__minus_03_dot_18_bar__plus_00_dot_68_bar__minus_02_dot_00)
        
        
        
        
        
        (sliceable Tomato_bar__minus_02_dot_65_bar__plus_00_dot_76_bar__minus_00_dot_89)
        (sliceable Lettuce_bar__minus_01_dot_32_bar__plus_00_dot_99_bar__minus_03_dot_76)
        (sliceable Apple_bar__minus_03_dot_10_bar__plus_00_dot_76_bar__minus_00_dot_53)
        (sliceable Potato_bar__minus_00_dot_24_bar__plus_00_dot_96_bar__minus_03_dot_11)
        (sliceable Egg_bar__minus_03_dot_23_bar__plus_01_dot_43_bar__minus_02_dot_06)
        (sliceable Egg_bar__minus_03_dot_29_bar__plus_00_dot_75_bar__minus_00_dot_37)
        (sliceable Potato_bar__minus_03_dot_01_bar__plus_00_dot_93_bar__minus_02_dot_50)
        (sliceable Bread_bar__minus_02_dot_87_bar__plus_00_dot_76_bar__minus_00_dot_64)
        (sliceable Apple_bar__minus_03_dot_18_bar__plus_00_dot_68_bar__minus_02_dot_00)
        
        (inReceptacle Pot_bar__minus_02_dot_84_bar__plus_00_dot_90_bar__minus_03_dot_75 CounterTop_bar__minus_01_dot_79_bar__plus_00_dot_95_bar__minus_03_dot_80)
        (inReceptacle Cup_bar__minus_02_dot_63_bar__plus_00_dot_91_bar__minus_03_dot_95 CounterTop_bar__minus_01_dot_79_bar__plus_00_dot_95_bar__minus_03_dot_80)
        (inReceptacle PepperShaker_bar__minus_03_dot_48_bar__plus_00_dot_91_bar__minus_02_dot_83 CounterTop_bar__minus_01_dot_79_bar__plus_00_dot_95_bar__minus_03_dot_80)
        (inReceptacle Bowl_bar__minus_00_dot_16_bar__plus_00_dot_91_bar__minus_02_dot_91 CounterTop_bar__minus_01_dot_79_bar__plus_00_dot_95_bar__minus_03_dot_80)
        (inReceptacle Lettuce_bar__minus_01_dot_32_bar__plus_00_dot_99_bar__minus_03_dot_76 CounterTop_bar__minus_01_dot_79_bar__plus_00_dot_95_bar__minus_03_dot_80)
        (inReceptacle Spatula_bar__minus_03_dot_48_bar__plus_00_dot_93_bar__minus_02_dot_93 CounterTop_bar__minus_01_dot_79_bar__plus_00_dot_95_bar__minus_03_dot_80)
        (inReceptacle Potato_bar__minus_00_dot_24_bar__plus_00_dot_96_bar__minus_03_dot_11 CounterTop_bar__minus_01_dot_79_bar__plus_00_dot_95_bar__minus_03_dot_80)
        (inReceptacle Knife_bar__minus_00_dot_65_bar__plus_00_dot_95_bar__minus_03_dot_80 CounterTop_bar__minus_01_dot_79_bar__plus_00_dot_95_bar__minus_03_dot_80)
        (inReceptacle PepperShaker_bar__minus_03_dot_31_bar__plus_00_dot_91_bar__minus_03_dot_33 CounterTop_bar__minus_01_dot_79_bar__plus_00_dot_95_bar__minus_03_dot_80)
        (inReceptacle Mug_bar__minus_00_dot_34_bar__plus_00_dot_08_bar__minus_01_dot_49 Cabinet_bar__minus_00_dot_58_bar__plus_00_dot_39_bar__minus_01_dot_73)
        (inReceptacle Egg_bar__minus_03_dot_29_bar__plus_00_dot_75_bar__minus_00_dot_37 DiningTable_bar__minus_02_dot_87_bar__plus_00_dot_68_bar__minus_00_dot_51)
        (inReceptacle Tomato_bar__minus_02_dot_65_bar__plus_00_dot_76_bar__minus_00_dot_89 DiningTable_bar__minus_02_dot_87_bar__plus_00_dot_68_bar__minus_00_dot_51)
        (inReceptacle ButterKnife_bar__minus_03_dot_17_bar__plus_00_dot_72_bar__minus_00_dot_80 DiningTable_bar__minus_02_dot_87_bar__plus_00_dot_68_bar__minus_00_dot_51)
        (inReceptacle SoapBottle_bar__minus_03_dot_20_bar__plus_00_dot_71_bar__minus_00_dot_56 DiningTable_bar__minus_02_dot_87_bar__plus_00_dot_68_bar__minus_00_dot_51)
        (inReceptacle Apple_bar__minus_03_dot_10_bar__plus_00_dot_76_bar__minus_00_dot_53 DiningTable_bar__minus_02_dot_87_bar__plus_00_dot_68_bar__minus_00_dot_51)
        (inReceptacle DishSponge_bar__minus_02_dot_43_bar__plus_00_dot_71_bar__minus_00_dot_67 DiningTable_bar__minus_02_dot_87_bar__plus_00_dot_68_bar__minus_00_dot_51)
        (inReceptacle Fork_bar__minus_02_dot_74_bar__plus_00_dot_71_bar__minus_00_dot_21 DiningTable_bar__minus_02_dot_87_bar__plus_00_dot_68_bar__minus_00_dot_51)
        (inReceptacle Bread_bar__minus_02_dot_87_bar__plus_00_dot_76_bar__minus_00_dot_64 DiningTable_bar__minus_02_dot_87_bar__plus_00_dot_68_bar__minus_00_dot_51)
        (inReceptacle ButterKnife_bar__minus_02_dot_80_bar__plus_00_dot_71_bar__minus_00_dot_07 DiningTable_bar__minus_02_dot_87_bar__plus_00_dot_68_bar__minus_00_dot_51)
        (inReceptacle DishSponge_bar__minus_02_dot_58_bar__plus_00_dot_71_bar__minus_00_dot_37 DiningTable_bar__minus_02_dot_87_bar__plus_00_dot_68_bar__minus_00_dot_51)
        (inReceptacle DishSponge_bar__minus_02_dot_72_bar__plus_00_dot_72_bar__minus_00_dot_50 DiningTable_bar__minus_02_dot_87_bar__plus_00_dot_68_bar__minus_00_dot_51)
        (inReceptacle SaltShaker_bar__minus_03_dot_14_bar__plus_00_dot_74_bar__minus_02_dot_91 Drawer_bar__minus_03_dot_10_bar__plus_00_dot_80_bar__minus_02_dot_97)
        (inReceptacle Fork_bar__minus_03_dot_05_bar__plus_00_dot_75_bar__minus_02_dot_86 Drawer_bar__minus_03_dot_10_bar__plus_00_dot_80_bar__minus_02_dot_97)
        (inReceptacle Pan_bar__minus_00_dot_17_bar__plus_00_dot_98_bar__minus_02_dot_45 StoveBurner_bar__minus_00_dot_38_bar__plus_00_dot_94_bar__minus_02_dot_45)
        (inReceptacle Spoon_bar__minus_00_dot_34_bar__plus_00_dot_92_bar__minus_01_dot_49 CounterTop_bar__minus_00_dot_27_bar__plus_00_dot_95_bar__minus_01_dot_09)
        (inReceptacle ButterKnife_bar__minus_00_dot_10_bar__plus_00_dot_92_bar__minus_01_dot_62 CounterTop_bar__minus_00_dot_27_bar__plus_00_dot_95_bar__minus_01_dot_09)
        (inReceptacle Fork_bar__minus_00_dot_34_bar__plus_00_dot_92_bar__minus_00_dot_54 CounterTop_bar__minus_00_dot_27_bar__plus_00_dot_95_bar__minus_01_dot_09)
        (inReceptacle PepperShaker_bar__minus_00_dot_50_bar__plus_00_dot_91_bar__minus_01_dot_43 CounterTop_bar__minus_00_dot_27_bar__plus_00_dot_95_bar__minus_01_dot_09)
        (inReceptacle Pan_bar__minus_00_dot_17_bar__plus_00_dot_98_bar__minus_02_dot_45 StoveBurner_bar__minus_00_dot_17_bar__plus_00_dot_94_bar__minus_02_dot_45)
        (inReceptacle Bowl_bar__minus_00_dot_13_bar__plus_01_dot_01_bar__minus_00_dot_80 Microwave_bar__minus_00_dot_14_bar__plus_00_dot_90_bar__minus_00_dot_91)
        (inReceptacle Potato_bar__minus_03_dot_01_bar__plus_00_dot_93_bar__minus_02_dot_50 Fridge_bar__minus_03_dot_19_bar__plus_00_dot_00_bar__minus_02_dot_19)
        (inReceptacle Plate_bar__minus_03_dot_08_bar__plus_01_dot_40_bar__minus_01_dot_94 Fridge_bar__minus_03_dot_19_bar__plus_00_dot_00_bar__minus_02_dot_19)
        (inReceptacle Egg_bar__minus_03_dot_23_bar__plus_01_dot_43_bar__minus_02_dot_06 Fridge_bar__minus_03_dot_19_bar__plus_00_dot_00_bar__minus_02_dot_19)
        (inReceptacle Apple_bar__minus_03_dot_18_bar__plus_00_dot_68_bar__minus_02_dot_00 Fridge_bar__minus_03_dot_19_bar__plus_00_dot_00_bar__minus_02_dot_19)
        (inReceptacle Mug_bar__minus_01_dot_98_bar__plus_00_dot_79_bar__minus_03_dot_70 Sink_bar__minus_01_dot_79_bar__plus_00_dot_90_bar__minus_03_dot_75_bar_SinkBasin)
        
        
        (receptacleAtLocation Cabinet_bar__minus_00_dot_19_bar__plus_01_dot_21_bar__minus_04_dot_24 loc_bar__minus_5_bar__minus_13_bar_1_bar_30)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_23_bar__plus_01_dot_80_bar__minus_00_dot_45 loc_bar__minus_4_bar__minus_2_bar_1_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_23_bar__plus_01_dot_80_bar__minus_01_dot_74 loc_bar__minus_4_bar__minus_7_bar_1_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_23_bar__plus_01_dot_80_bar__minus_02_dot_73 loc_bar__minus_4_bar__minus_10_bar_1_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_24_bar__plus_02_dot_10_bar__minus_01_dot_77 loc_bar__minus_4_bar__minus_7_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_24_bar__plus_02_dot_10_bar__minus_02_dot_71 loc_bar__minus_4_bar__minus_10_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_58_bar__plus_00_dot_39_bar__minus_00_dot_45 loc_bar__minus_6_bar__minus_2_bar_1_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_58_bar__plus_00_dot_39_bar__minus_01_dot_73 loc_bar__minus_7_bar__minus_4_bar_1_bar_45)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_58_bar__plus_00_dot_39_bar__minus_02_dot_74 loc_bar__minus_6_bar__minus_13_bar_1_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_58_bar__plus_00_dot_39_bar__minus_03_dot_48 loc_bar__minus_7_bar__minus_11_bar_1_bar_45)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_61_bar__plus_00_dot_39_bar__minus_03_dot_51 loc_bar__minus_5_bar__minus_10_bar_2_bar_45)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_23_bar__plus_01_dot_81_bar__minus_03_dot_76 loc_bar__minus_4_bar__minus_13_bar_2_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_36_bar__plus_00_dot_39_bar__minus_03_dot_51 loc_bar__minus_4_bar__minus_10_bar_2_bar_45)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_38_bar__plus_00_dot_39_bar__minus_03_dot_51 loc_bar__minus_8_bar__minus_10_bar_2_bar_45)
        (receptacleAtLocation Cabinet_bar__minus_02_dot_15_bar__plus_00_dot_39_bar__minus_03_dot_51 loc_bar__minus_9_bar__minus_10_bar_2_bar_45)
        (receptacleAtLocation Cabinet_bar__minus_02_dot_81_bar__plus_01_dot_81_bar__minus_03_dot_76 loc_bar__minus_9_bar__minus_12_bar_2_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__minus_02_dot_98_bar__plus_00_dot_39_bar__minus_03_dot_20 loc_bar__minus_9_bar__minus_12_bar_3_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_03_dot_02_bar__plus_02_dot_10_bar__minus_01_dot_74 loc_bar__minus_8_bar__minus_9_bar_3_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_03_dot_02_bar__plus_02_dot_10_bar__minus_02_dot_68 loc_bar__minus_8_bar__minus_11_bar_3_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_03_dot_23_bar__plus_01_dot_80_bar__minus_03_dot_34 loc_bar__minus_8_bar__minus_13_bar_3_bar_0)
        (receptacleAtLocation Cabinet_bar__minus_06_dot_01_bar__plus_01_dot_14_bar__minus_06_dot_02 loc_bar__minus_8_bar__minus_13_bar_3_bar_0)
        (receptacleAtLocation CoffeeMachine_bar__minus_03_dot_10_bar__plus_00_dot_71_bar__minus_00_dot_19 loc_bar__minus_8_bar__minus_1_bar_3_bar_45)
        (receptacleAtLocation CounterTop_bar__minus_00_dot_27_bar__plus_00_dot_95_bar__minus_01_dot_09 loc_bar__minus_4_bar__minus_5_bar_1_bar_45)
        (receptacleAtLocation CounterTop_bar__minus_01_dot_79_bar__plus_00_dot_95_bar__minus_03_dot_80 loc_bar__minus_4_bar__minus_13_bar_3_bar_45)
        (receptacleAtLocation DiningTable_bar__minus_02_dot_87_bar__plus_00_dot_68_bar__minus_00_dot_51 loc_bar__minus_7_bar__minus_2_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_45_bar__plus_00_dot_80_bar__minus_00_dot_76 loc_bar__minus_4_bar__minus_3_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__minus_00_dot_45_bar__plus_00_dot_80_bar__minus_01_dot_41 loc_bar__minus_4_bar__minus_6_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__minus_00_dot_45_bar__plus_00_dot_80_bar__minus_02_dot_97 loc_bar__minus_4_bar__minus_12_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__minus_00_dot_45_bar__plus_00_dot_80_bar__minus_03_dot_35 loc_bar__minus_4_bar__minus_13_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__minus_03_dot_10_bar__plus_00_dot_80_bar__minus_02_dot_97 loc_bar__minus_9_bar__minus_12_bar_3_bar_45)
        (receptacleAtLocation Fridge_bar__minus_03_dot_19_bar__plus_00_dot_00_bar__minus_02_dot_19 loc_bar__minus_9_bar__minus_9_bar_3_bar_60)
        (receptacleAtLocation GarbageCan_bar__minus_00_dot_28_bar__plus_00_dot_00_bar__minus_00_dot_04 loc_bar__minus_4_bar__minus_1_bar_1_bar_60)
        (receptacleAtLocation Microwave_bar__minus_00_dot_14_bar__plus_00_dot_90_bar__minus_00_dot_91 loc_bar__minus_4_bar__minus_3_bar_1_bar_45)
        (receptacleAtLocation Sink_bar__minus_01_dot_79_bar__plus_00_dot_90_bar__minus_03_dot_75_bar_SinkBasin loc_bar__minus_8_bar__minus_13_bar_2_bar_60)
        (receptacleAtLocation StoveBurner_bar__minus_00_dot_17_bar__plus_00_dot_94_bar__minus_02_dot_03 loc_bar__minus_4_bar__minus_8_bar_1_bar_45)
        (receptacleAtLocation StoveBurner_bar__minus_00_dot_17_bar__plus_00_dot_94_bar__minus_02_dot_45 loc_bar__minus_4_bar__minus_10_bar_1_bar_45)
        (receptacleAtLocation StoveBurner_bar__minus_00_dot_38_bar__plus_00_dot_94_bar__minus_02_dot_03 loc_bar__minus_4_bar__minus_8_bar_1_bar_45)
        (receptacleAtLocation StoveBurner_bar__minus_00_dot_38_bar__plus_00_dot_94_bar__minus_02_dot_45 loc_bar__minus_4_bar__minus_10_bar_1_bar_45)
        (receptacleAtLocation Toaster_bar__minus_00_dot_23_bar__plus_00_dot_90_bar__minus_03_dot_41 loc_bar__minus_4_bar__minus_13_bar_1_bar_45)
        (objectAtLocation Bowl_bar__minus_00_dot_16_bar__plus_00_dot_91_bar__minus_02_dot_91 loc_bar__minus_4_bar__minus_13_bar_3_bar_45)
        (objectAtLocation Mug_bar__minus_01_dot_98_bar__plus_00_dot_79_bar__minus_03_dot_70 loc_bar__minus_8_bar__minus_13_bar_2_bar_60)
        (objectAtLocation DishSponge_bar__minus_02_dot_58_bar__plus_00_dot_71_bar__minus_00_dot_37 loc_bar__minus_7_bar__minus_2_bar_3_bar_45)
        (objectAtLocation ButterKnife_bar__minus_03_dot_17_bar__plus_00_dot_72_bar__minus_00_dot_80 loc_bar__minus_7_bar__minus_2_bar_3_bar_45)
        (objectAtLocation PepperShaker_bar__minus_03_dot_48_bar__plus_00_dot_91_bar__minus_02_dot_83 loc_bar__minus_4_bar__minus_13_bar_3_bar_45)
        (objectAtLocation Apple_bar__minus_03_dot_10_bar__plus_00_dot_76_bar__minus_00_dot_53 loc_bar__minus_7_bar__minus_2_bar_3_bar_45)
        (objectAtLocation Potato_bar__minus_03_dot_01_bar__plus_00_dot_93_bar__minus_02_dot_50 loc_bar__minus_9_bar__minus_9_bar_3_bar_60)
        (objectAtLocation Fork_bar__minus_03_dot_05_bar__plus_00_dot_75_bar__minus_02_dot_86 loc_bar__minus_9_bar__minus_12_bar_3_bar_45)
        (objectAtLocation Egg_bar__minus_03_dot_23_bar__plus_01_dot_43_bar__minus_02_dot_06 loc_bar__minus_9_bar__minus_9_bar_3_bar_60)
        (objectAtLocation PepperShaker_bar__minus_00_dot_50_bar__plus_00_dot_91_bar__minus_01_dot_43 loc_bar__minus_4_bar__minus_5_bar_1_bar_45)
        (objectAtLocation Fork_bar__minus_00_dot_34_bar__plus_00_dot_92_bar__minus_00_dot_54 loc_bar__minus_4_bar__minus_5_bar_1_bar_45)
        (objectAtLocation ButterKnife_bar__minus_02_dot_80_bar__plus_00_dot_71_bar__minus_00_dot_07 loc_bar__minus_7_bar__minus_2_bar_3_bar_45)
        (objectAtLocation DishSponge_bar__minus_02_dot_72_bar__plus_00_dot_72_bar__minus_00_dot_50 loc_bar__minus_7_bar__minus_2_bar_3_bar_45)
        (objectAtLocation Sink_bar__minus_01_dot_79_bar__plus_00_dot_90_bar__minus_03_dot_75 loc_bar__minus_7_bar__minus_13_bar_2_bar_60)
        (objectAtLocation Pot_bar__minus_02_dot_84_bar__plus_00_dot_90_bar__minus_03_dot_75 loc_bar__minus_4_bar__minus_13_bar_3_bar_45)
        (objectAtLocation Egg_bar__minus_03_dot_29_bar__plus_00_dot_75_bar__minus_00_dot_37 loc_bar__minus_7_bar__minus_2_bar_3_bar_45)
        (objectAtLocation Cup_bar__minus_02_dot_63_bar__plus_00_dot_91_bar__minus_03_dot_95 loc_bar__minus_4_bar__minus_13_bar_3_bar_45)
        (objectAtLocation StoveKnob_bar__minus_00_dot_57_bar__plus_00_dot_90_bar__minus_02_dot_31 loc_bar__minus_4_bar__minus_9_bar_1_bar_60)
        (objectAtLocation StoveKnob_bar__minus_00_dot_57_bar__plus_00_dot_90_bar__minus_02_dot_47 loc_bar__minus_4_bar__minus_10_bar_1_bar_60)
        (objectAtLocation StoveKnob_bar__minus_00_dot_57_bar__plus_00_dot_90_bar__minus_02_dot_17 loc_bar__minus_4_bar__minus_9_bar_1_bar_60)
        (objectAtLocation StoveKnob_bar__minus_00_dot_57_bar__plus_00_dot_90_bar__minus_02_dot_00 loc_bar__minus_4_bar__minus_8_bar_1_bar_60)
        (objectAtLocation Bread_bar__minus_02_dot_87_bar__plus_00_dot_76_bar__minus_00_dot_64 loc_bar__minus_7_bar__minus_2_bar_3_bar_45)
        (objectAtLocation Fork_bar__minus_02_dot_74_bar__plus_00_dot_71_bar__minus_00_dot_21 loc_bar__minus_7_bar__minus_2_bar_3_bar_45)
        (objectAtLocation Pan_bar__minus_00_dot_17_bar__plus_00_dot_98_bar__minus_02_dot_45 loc_bar__minus_4_bar__minus_10_bar_1_bar_45)
        (objectAtLocation Potato_bar__minus_00_dot_24_bar__plus_00_dot_96_bar__minus_03_dot_11 loc_bar__minus_4_bar__minus_13_bar_3_bar_45)
        (objectAtLocation Lettuce_bar__minus_01_dot_32_bar__plus_00_dot_99_bar__minus_03_dot_76 loc_bar__minus_4_bar__minus_13_bar_3_bar_45)
        (objectAtLocation Tomato_bar__minus_02_dot_65_bar__plus_00_dot_76_bar__minus_00_dot_89 loc_bar__minus_7_bar__minus_2_bar_3_bar_45)
        (objectAtLocation LightSwitch_bar__minus_01_dot_11_bar__plus_01_dot_46_bar__plus_00_dot_34 loc_bar__minus_4_bar__minus_1_bar_0_bar_15)
        (objectAtLocation Apple_bar__minus_03_dot_18_bar__plus_00_dot_68_bar__minus_02_dot_00 loc_bar__minus_9_bar__minus_9_bar_3_bar_60)
        (objectAtLocation SoapBottle_bar__minus_03_dot_20_bar__plus_00_dot_71_bar__minus_00_dot_56 loc_bar__minus_7_bar__minus_2_bar_3_bar_45)
        (objectAtLocation Spatula_bar__minus_03_dot_48_bar__plus_00_dot_93_bar__minus_02_dot_93 loc_bar__minus_4_bar__minus_13_bar_3_bar_45)
        (objectAtLocation SaltShaker_bar__minus_03_dot_14_bar__plus_00_dot_74_bar__minus_02_dot_91 loc_bar__minus_9_bar__minus_12_bar_3_bar_45)
        (objectAtLocation Knife_bar__minus_00_dot_65_bar__plus_00_dot_95_bar__minus_03_dot_80 loc_bar__minus_4_bar__minus_13_bar_3_bar_45)
        (objectAtLocation PepperShaker_bar__minus_03_dot_31_bar__plus_00_dot_91_bar__minus_03_dot_33 loc_bar__minus_4_bar__minus_13_bar_3_bar_45)
        (objectAtLocation ButterKnife_bar__minus_00_dot_10_bar__plus_00_dot_92_bar__minus_01_dot_62 loc_bar__minus_4_bar__minus_5_bar_1_bar_45)
        (objectAtLocation Plate_bar__minus_03_dot_08_bar__plus_01_dot_40_bar__minus_01_dot_94 loc_bar__minus_9_bar__minus_9_bar_3_bar_60)
        (objectAtLocation DishSponge_bar__minus_02_dot_43_bar__plus_00_dot_71_bar__minus_00_dot_67 loc_bar__minus_7_bar__minus_2_bar_3_bar_45)
        (objectAtLocation Chair_bar__minus_02_dot_38_bar__plus_00_dot_02_bar__minus_00_dot_55 loc_bar__minus_8_bar__minus_3_bar_3_bar_60)
        (objectAtLocation Chair_bar__minus_03_dot_16_bar__plus_00_dot_01_bar__minus_00_dot_99 loc_bar__minus_9_bar__minus_5_bar_3_bar_60)
        (objectAtLocation Spoon_bar__minus_00_dot_34_bar__plus_00_dot_92_bar__minus_01_dot_49 loc_bar__minus_4_bar__minus_5_bar_1_bar_45)
        (objectAtLocation Mug_bar__minus_00_dot_34_bar__plus_00_dot_08_bar__minus_01_dot_49 loc_bar__minus_7_bar__minus_4_bar_1_bar_45)
        (objectAtLocation Bowl_bar__minus_00_dot_13_bar__plus_01_dot_01_bar__minus_00_dot_80 loc_bar__minus_4_bar__minus_3_bar_1_bar_45)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 PepperShakerType)
                                    (receptacleType ?r DiningTableType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 PepperShakerType)
                                            (receptacleType ?r DiningTableType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            