{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000283.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000284.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000285.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000286.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000287.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000288.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000289.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000290.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000291.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000292.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000293.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000294.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000295.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000296.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000297.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000298.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000299.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000300.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000301.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000302.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000303.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000304.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000305.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000306.png", "low_idx": 50}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "RemoteControl", "parent_target": "Ottoman", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sofa"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-11|4|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["remotecontrol"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["RemoteControl", [-11.28145792, -11.28145792, 1.561898, 1.561898, 2.013454, 2.013454]], "coordinateReceptacleObjectId": ["So<PERSON>", [-10.1, -10.1, 1.056, 1.056, 0.068, 0.068]], "forceVisible": true, "objectId": "RemoteControl|-02.82|+00.50|+00.39"}}, {"discrete_action": {"action": "GotoLocation", "args": ["ottoman"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-2|6|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["remotecontrol", "ottoman"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["RemoteControl", [-11.28145792, -11.28145792, 1.561898, 1.561898, 2.013454, 2.013454]], "coordinateReceptacleObjectId": ["Ottoman", [-7.092, -7.092, 7.228, 7.228, 0.0304, 0.0304]], "forceVisible": true, "objectId": "RemoteControl|-02.82|+00.50|+00.39", "receptacleObjectId": "Ottoman|-01.77|+00.01|+01.81"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sofa"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-9|4|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["remotecontrol"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["RemoteControl", [-8.91854288, -8.91854288, 2.314846992, 2.314846992, 2.013454, 2.013454]], "coordinateReceptacleObjectId": ["So<PERSON>", [-10.1, -10.1, 1.056, 1.056, 0.068, 0.068]], "forceVisible": true, "objectId": "RemoteControl|-02.23|+00.50|+00.58"}}, {"discrete_action": {"action": "GotoLocation", "args": ["ottoman"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-2|6|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["remotecontrol", "ottoman"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["RemoteControl", [-8.91854288, -8.91854288, 2.314846992, 2.314846992, 2.013454, 2.013454]], "coordinateReceptacleObjectId": ["Ottoman", [-7.092, -7.092, 7.228, 7.228, 0.0304, 0.0304]], "forceVisible": true, "objectId": "RemoteControl|-02.23|+00.50|+00.58", "receptacleObjectId": "Ottoman|-01.77|+00.01|+01.81"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "RemoteControl|-02.82|+00.50|+00.39"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [159, 135, 171, 173], "mask": [[40359, 9], [40659, 11], [40959, 11], [41259, 11], [41559, 11], [41859, 11], [42159, 11], [42459, 11], [42759, 11], [43059, 11], [43359, 12], [43659, 12], [43959, 12], [44259, 12], [44559, 12], [44859, 12], [45159, 12], [45459, 12], [45759, 12], [46059, 12], [46359, 12], [46659, 12], [46959, 12], [47259, 12], [47559, 12], [47859, 12], [48159, 12], [48459, 12], [48759, 12], [49059, 13], [49359, 13], [49659, 13], [49959, 13], [50259, 13], [50559, 13], [50859, 13], [51159, 13], [51459, 13], [51764, 7]], "point": [165, 153]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "RemoteControl|-02.82|+00.50|+00.39", "placeStationary": true, "receptacleObjectId": "Ottoman|-01.77|+00.01|+01.81"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [133, 104, 279, 226], "mask": [[31092, 20], [31372, 51], [31667, 65], [31945, 20], [31967, 71], [32239, 26], [32267, 76], [32538, 27], [32567, 77], [32838, 27], [32868, 77], [33138, 27], [33168, 77], [33438, 27], [33468, 78], [33737, 28], [33768, 78], [34037, 28], [34068, 79], [34337, 28], [34368, 79], [34637, 28], [34668, 80], [34937, 27], [34968, 80], [35237, 27], [35268, 81], [35537, 27], [35568, 81], [35837, 27], [35868, 81], [36137, 27], [36168, 82], [36437, 27], [36468, 82], [36737, 27], [36769, 81], [37037, 27], [37069, 82], [37337, 27], [37369, 82], [37637, 27], [37669, 83], [37937, 27], [37969, 83], [38237, 27], [38269, 83], [38537, 27], [38569, 84], [38837, 27], [38869, 84], [39136, 28], [39169, 84], [39436, 28], [39469, 85], [39736, 28], [39769, 85], [40036, 28], [40069, 86], [40336, 28], [40370, 85], [40636, 28], [40670, 86], [40936, 28], [41004, 5], [41236, 28], [41304, 5], [41536, 28], [41605, 2], [41836, 28], [41905, 2], [42136, 28], [42205, 2], [42436, 28], [42505, 2], [42736, 28], [42805, 2], [43036, 28], [43106, 1], [43336, 28], [43406, 2], [43636, 28], [43706, 2], [43935, 29], [44006, 2], [44235, 29], [44307, 1], [44535, 29], [44607, 2], [44835, 29], [44907, 2], [45135, 29], [45207, 2], [45435, 29], [45507, 56], [45735, 29], [45808, 55], [46035, 29], [46108, 56], [46335, 29], [46408, 56], [46635, 30], [46708, 56], [46935, 30], [47009, 56], [47235, 30], [47309, 56], [47535, 30], [47609, 56], [47835, 30], [47909, 57], [48135, 30], [48209, 57], [48435, 30], [48510, 57], [48735, 30], [48810, 57], [49035, 30], [49110, 58], [49335, 30], [49410, 58], [49634, 31], [49711, 58], [49934, 31], [50011, 59], [50234, 31], [50311, 59], [50534, 31], [50611, 60], [50834, 31], [50911, 60], [51134, 31], [51212, 60], [51434, 31], [51512, 60], [51734, 31], [51812, 61], [52034, 32], [52112, 61], [52334, 32], [52412, 62], [52634, 32], [52713, 61], [52934, 32], [53013, 61], [53234, 32], [53313, 62], [53534, 32], [53613, 62], [53834, 32], [53913, 62], [54134, 142], [54434, 142], [54733, 144], [55033, 144], [55333, 144], [55633, 145], [55933, 145], [56233, 146], [56533, 1], [56566, 113], [56867, 112], [57167, 113], [57468, 112], [57768, 111], [58068, 111], [58368, 111], [58668, 111], [58968, 110], [59268, 110], [59568, 109], [59868, 108], [60168, 108], [60468, 107], [60768, 107], [61068, 106], [61368, 105], [61668, 105], [61968, 104], [62268, 103], [62568, 103], [62868, 102], [63168, 102], [63468, 101], [63768, 101], [64068, 100], [64368, 99], [64668, 99], [64968, 98], [65268, 98], [65568, 97], [65869, 95], [66169, 95], [66469, 94], [66769, 94], [67069, 93], [67369, 92], [67698, 62]], "point": [210, 164]}}, "high_idx": 3}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "RemoteControl|-02.23|+00.50|+00.58"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [138, 168, 150, 214], "mask": [[50246, 5], [50539, 12], [50839, 12], [51139, 12], [51439, 12], [51739, 12], [52039, 12], [52339, 12], [52639, 12], [52939, 12], [53239, 12], [53539, 12], [53839, 12], [54139, 12], [54438, 13], [54738, 13], [55038, 13], [55338, 13], [55638, 13], [55938, 13], [56238, 13], [56538, 13], [56838, 13], [57138, 13], [57438, 13], [57738, 13], [58038, 13], [58338, 13], [58638, 13], [58938, 13], [59238, 13], [59538, 13], [59838, 13], [60138, 13], [60438, 13], [60738, 13], [61038, 13], [61338, 13], [61638, 13], [61938, 13], [62238, 13], [62538, 13], [62838, 13], [63138, 13], [63438, 13], [63738, 13], [64039, 4]], "point": [144, 190]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "RemoteControl|-02.23|+00.50|+00.58", "placeStationary": true, "receptacleObjectId": "Ottoman|-01.77|+00.01|+01.81"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [133, 104, 279, 226], "mask": [[31092, 20], [31372, 51], [31667, 65], [31945, 20], [31967, 71], [32239, 26], [32267, 76], [32538, 27], [32567, 77], [32838, 27], [32868, 77], [33138, 27], [33168, 77], [33438, 27], [33468, 78], [33737, 28], [33768, 78], [34037, 28], [34068, 79], [34337, 28], [34368, 79], [34637, 28], [34668, 80], [34937, 27], [34968, 80], [35237, 27], [35268, 81], [35537, 27], [35568, 81], [35837, 27], [35868, 81], [36137, 27], [36168, 82], [36437, 27], [36468, 82], [36737, 27], [36769, 81], [37037, 27], [37069, 82], [37337, 27], [37369, 82], [37637, 27], [37669, 83], [37937, 27], [37969, 83], [38237, 27], [38269, 83], [38537, 27], [38569, 84], [38837, 27], [38869, 84], [39136, 28], [39169, 84], [39436, 28], [39469, 85], [39736, 28], [39769, 85], [40036, 28], [40069, 86], [40336, 28], [40370, 85], [40636, 28], [40670, 86], [40936, 28], [41004, 5], [41236, 28], [41304, 5], [41536, 28], [41605, 2], [41836, 28], [41905, 2], [42136, 28], [42205, 2], [42436, 28], [42505, 2], [42736, 28], [42805, 2], [43036, 28], [43106, 1], [43336, 28], [43406, 2], [43636, 28], [43706, 2], [43935, 29], [44006, 2], [44235, 29], [44307, 1], [44535, 29], [44607, 2], [44835, 29], [44907, 2], [45135, 29], [45207, 2], [45435, 29], [45507, 56], [45735, 29], [45808, 55], [46035, 29], [46108, 56], [46335, 29], [46408, 56], [46635, 30], [46708, 56], [46935, 30], [47009, 56], [47235, 30], [47309, 56], [47535, 30], [47609, 56], [47835, 30], [47909, 57], [48135, 30], [48209, 57], [48435, 30], [48510, 57], [48735, 30], [48810, 57], [49035, 30], [49110, 58], [49335, 30], [49410, 58], [49634, 31], [49743, 26], [49934, 31], [50043, 27], [50234, 31], [50344, 26], [50534, 31], [50644, 27], [50834, 31], [50944, 27], [51134, 31], [51245, 27], [51434, 31], [51545, 27], [51734, 31], [51845, 28], [52034, 32], [52145, 28], [52334, 32], [52412, 62], [52634, 32], [52713, 61], [52934, 32], [53013, 61], [53234, 32], [53313, 62], [53534, 32], [53613, 62], [53834, 32], [53913, 62], [54134, 142], [54434, 142], [54733, 144], [55033, 144], [55333, 144], [55633, 145], [55933, 145], [56233, 146], [56533, 1], [56566, 113], [56867, 112], [57167, 113], [57468, 112], [57768, 111], [58068, 111], [58368, 111], [58668, 111], [58968, 110], [59268, 110], [59568, 109], [59868, 108], [60168, 108], [60468, 107], [60768, 107], [61068, 106], [61368, 105], [61668, 105], [61968, 104], [62268, 103], [62568, 103], [62868, 102], [63168, 102], [63468, 101], [63768, 101], [64068, 100], [64368, 99], [64668, 99], [64968, 98], [65268, 98], [65568, 97], [65869, 95], [66169, 95], [66469, 94], [66769, 94], [67069, 93], [67369, 92], [67698, 62]], "point": [210, 164]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan210", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -3.0, "y": 0.9057542, "z": 1.0}, "object_poses": [{"objectName": "Watch_201d71d0", "position": {"x": -0.394545376, "y": 0.09601981, "z": 3.6574192}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_12281c43", "position": {"x": -4.489996, "y": 0.421995968, "z": 1.396}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_12281c43", "position": {"x": -0.275278866, "y": 0.0937789753, "z": 4.089885}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "RemoteControl_cbdac303", "position": {"x": -2.22963572, "y": 0.5033635, "z": 0.578711748}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Box_32200605", "position": {"x": -2.525, "y": 0.7985668, "z": 0.484593123}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Box_32200605", "position": {"x": -1.7, "y": 0.307, "z": 4.296}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Watch_201d71d0", "position": {"x": -0.3050955, "y": 0.4739199, "z": 3.748473}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pillow_575111bb", "position": {"x": -1.83093882, "y": 0.597, "z": 0.222617581}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WateringCan_a7911a75", "position": {"x": -0.269126922, "y": 0.005861737, "z": 2.36690187}, "rotation": {"x": 1.83447444, "y": 0.3207449, "z": 359.559967}}, {"objectName": "Newspaper_071d1cd2", "position": {"x": -1.66921151, "y": 0.454294145, "z": 2.030415}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "KeyChain_334198b6", "position": {"x": -4.34661341, "y": 0.412835747, "z": 0.414}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_801570fd", "position": {"x": -1.565423, "y": 0.452943653, "z": 1.71672273}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "RemoteControl_cbdac303", "position": {"x": -2.82036448, "y": 0.5033635, "z": 0.3904745}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_12281c43", "position": {"x": -2.56, "y": 1.09982288, "z": 4.299}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Statue_bfb36234", "position": {"x": -3.00375724, "y": 1.10529649, "z": 4.15318775}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2813939646, "scene_num": 210}, "task_id": "trial_T20190906_202015_373575", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A1ELPYAFO7MANS_31IBVUNM9VGW988IUKDX7RAJDXAVFG", "high_descs": ["Turn left and look at the couch.", "Pick up the remote on the right side of the box.", "Turn left and walk to the far end of the ottoman.", "Place the remote on the ottoman to the right of the laptop.", "Turn left and go around the ottoman and face the couch.", "Pick up the remaining remote off the couch.", "Turn left and go around the ottoman to the side you were just at.", "Place the other remote behind the laptop."], "task_desc": "Place two remotes on an ottoman.", "votes": [1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_39OWYR0EPN8DD23NZV0HEQEDVHVFYY", "high_descs": ["Turn around and take a step forward, then turn right to face the couch.", "Pick up the remote that is to the right of the cardboard box on the couch seat.", "Turn left and begin walking across the room, then turn left and take a step forward, turn left again to be facing the foot stool.", "Place the remote on the foot stool to the right of the laptop.", "Turn left and walk back over to where the second remote is placed on the couch.", "Pick up the remote that is to the left of the cardboard box on the couch.", "Turn left and walk back to the side of the foot stool.", "Place the second remote on the foot stool on the farthest side of the laptop."], "task_desc": "Place two remotes onto the foot stool.", "votes": [1, 1]}, {"assignment_id": "A36DK84J5YJ942_3MAOD8E57TR01PQUSNXS3HGDV3OXNK", "high_descs": ["Move to the middle of the couch", "Pick up the right side remote control from the couch", "Move to the ottoman in front of the couch", "Place the remote onto the ottoman", "Move to the same couch behind to ottoman", "Pick up the second remote from the couch", "Move to the ottoman in front of the couch", "Place the remote control on to the ottoman on the other side of the laptop"], "task_desc": "Put two remote controls on to the ottoman in front of the couch", "votes": [1, 1]}]}}