{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000243.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000244.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000245.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000246.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000247.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000248.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000249.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000254.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000255.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000256.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 41}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON>g", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-2|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["mug"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON>g", [-0.705183864, -0.705183864, -2.0301832, -2.0301832, 3.643999816, 3.643999816]], "coordinateReceptacleObjectId": ["CounterTop", [-1.076, -1.076, -4.3592, -4.3592, 3.806, 3.806]], "forceVisible": true, "objectId": "Mug|-00.18|+00.91|-00.51"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-3|1|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["mug"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-0.576, -0.576, -3.632, -3.632, 3.615618228, 3.615618228]], "forceVisible": true, "objectId": "Microwave|-00.14|+00.90|-00.91"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-7|-4|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["mug", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON>g", [-0.705183864, -0.705183864, -2.0301832, -2.0301832, 3.643999816, 3.643999816]], "coordinateReceptacleObjectId": ["Cabinet", [-2.3084748, -2.3084748, -6.90270044, -6.90270044, 1.555380104, 1.555380104]], "forceVisible": true, "objectId": "Mug|-00.18|+00.91|-00.51", "receptacleObjectId": "Cabinet|-00.58|+00.39|-01.73"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Mug|-00.18|+00.91|-00.51"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [140, 97, 163, 130], "mask": [[28948, 8], [29245, 14], [29543, 18], [29842, 20], [30141, 22], [30441, 22], [30740, 24], [31040, 24], [31340, 23], [31641, 22], [31941, 22], [32241, 22], [32541, 22], [32841, 22], [33141, 22], [33441, 22], [33741, 22], [34041, 21], [34341, 21], [34642, 20], [34942, 20], [35242, 20], [35542, 20], [35842, 20], [36142, 20], [36442, 20], [36742, 20], [37042, 20], [37342, 20], [37643, 18], [37943, 17], [38244, 15], [38546, 12], [38849, 6]], "point": [151, 112]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|-00.18|+00.91|-00.51", "placeStationary": true, "receptacleObjectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [59, 46, 299, 242], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17795, 205], [18094, 206], [18394, 206], [18694, 206], [18993, 207], [19293, 207], [19593, 207], [19892, 208], [20192, 208], [20492, 208], [20791, 209], [21091, 209], [21391, 209], [21690, 210], [21990, 210], [22290, 210], [22589, 211], [22889, 211], [23189, 210], [23488, 211], [23788, 210], [24088, 210], [24387, 211], [24687, 210], [24987, 210], [25286, 210], [25586, 210], [25886, 209], [26185, 210], [26485, 209], [26785, 209], [27084, 209], [27384, 209], [27684, 209], [27983, 209], [28283, 209], [28583, 208], [28883, 208], [29182, 208], [29482, 208], [29782, 207], [30081, 208], [30381, 208], [30681, 207], [30980, 208], [31280, 207], [31580, 207], [31879, 207], [32179, 207], [32479, 206], [32778, 207], [33078, 206], [33378, 206], [33677, 207], [33977, 206], [34277, 206], [34576, 206], [34876, 206], [35176, 205], [35475, 206], [35775, 205], [36075, 205], [36374, 206], [36674, 205], [36974, 205], [37273, 205], [37573, 205], [37873, 204], [38172, 205], [38472, 201], [38772, 201], [39071, 48], [39239, 34], [39371, 48], [39539, 34], [39671, 48], [39840, 33], [39970, 48], [40140, 32], [40270, 48], [40440, 32], [40570, 48], [40741, 30], [40869, 49], [41169, 49], [41469, 49], [41768, 50], [42068, 49], [42368, 49], [42667, 50], [42967, 50], [43267, 50], [43566, 51], [43866, 51], [44166, 51], [44465, 51], [44765, 51], [45065, 51], [45365, 51], [45664, 52], [45964, 52], [46264, 52], [46563, 53], [46863, 52], [47163, 52], [47462, 53], [47762, 53], [48062, 53], [48361, 54], [48661, 54], [48961, 53], [49260, 54], [49560, 54], [49860, 54], [50159, 55], [50460, 54], [50760, 54], [51060, 54], [51361, 52], [51661, 52], [51961, 52], [52262, 51], [52563, 50], [52864, 49], [53166, 47], [53468, 45], [53769, 43], [54071, 41], [54372, 40], [54674, 38], [54974, 38], [55275, 37], [55575, 37], [55875, 36], [56176, 35], [56476, 35], [56776, 35], [57077, 34], [57377, 34], [57677, 34], [57978, 33], [58278, 32], [58578, 32], [58879, 31], [59179, 31], [59479, 31], [59780, 30], [60080, 30], [60380, 30], [60681, 28], [60981, 28], [61281, 28], [61582, 27], [61882, 27], [62182, 27], [62483, 26], [62783, 26], [63083, 25], [63384, 24], [63684, 24], [63984, 24], [64285, 23], [64585, 23], [64885, 23], [65186, 21], [65486, 21], [65786, 21], [66087, 20], [66387, 20], [66687, 18], [66988, 15], [67288, 13], [67587, 13], [67887, 12], [68187, 11], [68487, 11], [68787, 11], [69086, 11], [69386, 11], [69686, 11], [69986, 11], [70286, 12], [70309, 2], [70586, 12], [70610, 2], [70885, 14], [70910, 2], [71185, 15], [71211, 2], [71486, 15], [71786, 16], [72087, 16], [72387, 17]], "point": [179, 129]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [59, 46, 299, 242], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17795, 205], [18094, 206], [18394, 206], [18694, 206], [18993, 207], [19293, 207], [19593, 207], [19892, 208], [20192, 208], [20492, 208], [20791, 209], [21091, 209], [21391, 209], [21690, 210], [21990, 59], [22053, 147], [22290, 54], [22358, 142], [22589, 53], [22660, 140], [22889, 51], [22962, 138], [23189, 50], [23263, 136], [23488, 50], [23563, 136], [23788, 50], [23864, 134], [24088, 50], [24166, 132], [24387, 51], [24468, 130], [24687, 51], [24769, 128], [24987, 52], [25063, 4], [25069, 128], [25286, 53], [25363, 4], [25369, 127], [25586, 53], [25663, 4], [25669, 127], [25886, 53], [25963, 3], [25969, 126], [26185, 54], [26263, 3], [26268, 127], [26485, 54], [26563, 2], [26568, 126], [26785, 54], [26862, 3], [26867, 127], [27084, 55], [27162, 2], [27167, 126], [27384, 55], [27462, 2], [27467, 126], [27684, 55], [27762, 2], [27766, 127], [27983, 56], [28062, 1], [28066, 126], [28283, 56], [28365, 127], [28583, 57], [28665, 126], [28883, 57], [28964, 127], [29182, 58], [29263, 127], [29482, 58], [29562, 128], [29782, 58], [29862, 127], [30081, 59], [30162, 127], [30381, 59], [30462, 127], [30681, 59], [30762, 126], [30980, 60], [31062, 126], [31280, 60], [31361, 126], [31580, 60], [31661, 126], [31879, 62], [31961, 125], [32179, 63], [32260, 126], [32479, 64], [32559, 126], [32778, 67], [32857, 128], [33078, 72], [33152, 132], [33378, 206], [33677, 207], [33977, 206], [34277, 206], [34576, 206], [34876, 206], [35176, 205], [35475, 206], [35775, 205], [36075, 205], [36374, 206], [36674, 205], [36974, 205], [37273, 205], [37573, 205], [37873, 204], [38172, 205], [38472, 201], [38772, 201], [39071, 48], [39239, 34], [39371, 48], [39539, 34], [39671, 48], [39840, 33], [39970, 48], [40140, 32], [40270, 48], [40440, 32], [40570, 48], [40741, 30], [40869, 49], [41169, 49], [41469, 49], [41768, 50], [42068, 49], [42368, 49], [42667, 50], [42967, 50], [43267, 50], [43566, 51], [43866, 51], [44166, 51], [44465, 51], [44765, 51], [45065, 51], [45365, 51], [45664, 52], [45964, 52], [46264, 52], [46563, 53], [46863, 52], [47163, 52], [47462, 53], [47762, 53], [48062, 53], [48361, 54], [48661, 54], [48961, 53], [49260, 54], [49560, 54], [49860, 54], [50159, 55], [50460, 54], [50760, 54], [51060, 54], [51361, 52], [51661, 52], [51961, 52], [52262, 51], [52563, 50], [52864, 49], [53166, 47], [53468, 45], [53769, 43], [54071, 41], [54372, 40], [54674, 38], [54974, 38], [55275, 37], [55575, 37], [55875, 36], [56176, 35], [56476, 35], [56776, 35], [57077, 34], [57377, 34], [57677, 34], [57978, 33], [58278, 32], [58578, 32], [58879, 31], [59179, 31], [59479, 31], [59780, 30], [60080, 30], [60380, 30], [60681, 28], [60981, 28], [61281, 28], [61582, 27], [61882, 27], [62182, 27], [62483, 26], [62783, 26], [63083, 25], [63384, 24], [63684, 24], [63984, 24], [64285, 23], [64585, 23], [64885, 23], [65186, 21], [65486, 21], [65786, 21], [66087, 20], [66387, 20], [66687, 20], [66988, 19], [67288, 19], [67587, 19], [67887, 19], [68187, 19], [68487, 19], [68787, 19], [69086, 20], [69386, 20], [69686, 20], [69986, 22], [70286, 25], [70586, 26], [70885, 20], [70908, 4], [71185, 20], [71208, 5], [71486, 19], [71509, 3], [71786, 19], [72087, 17], [72387, 17]], "point": [179, 129]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-0.576, -0.576, -3.632, -3.632, 3.615618228, 3.615618228]], "forceVisible": true, "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-0.576, -0.576, -3.632, -3.632, 3.615618228, 3.615618228]], "forceVisible": true, "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Mug|-00.18|+00.91|-00.51"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [138, 74, 168, 111], "mask": [[22049, 4], [22344, 14], [22642, 18], [22940, 22], [23239, 24], [23538, 25], [23838, 26], [24138, 28], [24438, 30], [24738, 31], [25039, 24], [25067, 2], [25339, 24], [25367, 2], [25639, 24], [25667, 2], [25939, 24], [25966, 3], [26239, 24], [26266, 2], [26539, 24], [26565, 3], [26839, 23], [26865, 2], [27139, 23], [27164, 3], [27439, 23], [27464, 3], [27739, 23], [27764, 2], [28039, 23], [28063, 3], [28339, 26], [28640, 25], [28940, 24], [29240, 23], [29540, 22], [29840, 22], [30140, 22], [30440, 22], [30740, 22], [31040, 22], [31340, 21], [31640, 21], [31941, 20], [32242, 18], [32543, 16], [32845, 12], [33150, 2]], "point": [153, 91]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [59, 46, 299, 242], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17795, 205], [18094, 206], [18394, 206], [18694, 206], [18993, 207], [19293, 207], [19593, 207], [19892, 208], [20192, 208], [20492, 208], [20791, 209], [21091, 209], [21391, 209], [21690, 210], [21990, 210], [22290, 210], [22589, 211], [22889, 211], [23189, 210], [23488, 211], [23788, 210], [24088, 210], [24387, 211], [24687, 210], [24987, 210], [25286, 210], [25586, 210], [25886, 209], [26185, 210], [26485, 209], [26785, 209], [27084, 209], [27384, 209], [27684, 209], [27983, 209], [28283, 209], [28583, 208], [28883, 208], [29182, 208], [29482, 208], [29782, 207], [30081, 208], [30381, 208], [30681, 207], [30980, 208], [31280, 207], [31580, 207], [31879, 207], [32179, 207], [32479, 206], [32778, 207], [33078, 206], [33378, 206], [33677, 207], [33977, 206], [34277, 206], [34576, 206], [34876, 206], [35176, 205], [35475, 206], [35775, 205], [36075, 205], [36374, 206], [36674, 205], [36974, 205], [37273, 205], [37573, 205], [37873, 204], [38172, 205], [38472, 201], [38772, 201], [39071, 48], [39239, 34], [39371, 48], [39539, 34], [39671, 48], [39840, 33], [39970, 48], [40140, 32], [40270, 48], [40440, 32], [40570, 48], [40741, 30], [40869, 49], [41169, 49], [41469, 49], [41768, 50], [42068, 49], [42368, 49], [42667, 50], [42967, 50], [43267, 50], [43566, 51], [43866, 51], [44166, 51], [44465, 51], [44765, 51], [45065, 51], [45365, 51], [45664, 52], [45964, 52], [46264, 52], [46563, 53], [46863, 52], [47163, 52], [47462, 53], [47762, 53], [48062, 53], [48361, 54], [48661, 54], [48961, 53], [49260, 54], [49560, 54], [49860, 54], [50159, 55], [50460, 54], [50760, 54], [51060, 54], [51361, 52], [51661, 52], [51961, 52], [52262, 51], [52563, 50], [52864, 49], [53166, 47], [53468, 45], [53769, 43], [54071, 41], [54372, 40], [54674, 38], [54974, 38], [55275, 37], [55575, 37], [55875, 36], [56176, 35], [56476, 35], [56776, 35], [57077, 34], [57377, 34], [57677, 34], [57978, 33], [58278, 32], [58578, 32], [58879, 31], [59179, 31], [59479, 31], [59780, 30], [60080, 30], [60380, 30], [60681, 28], [60981, 28], [61281, 28], [61582, 27], [61882, 27], [62182, 27], [62483, 26], [62783, 26], [63083, 25], [63384, 24], [63684, 24], [63984, 24], [64285, 23], [64585, 23], [64885, 23], [65186, 21], [65486, 21], [65786, 21], [66087, 20], [66387, 20], [66687, 18], [66988, 15], [67288, 13], [67587, 13], [67887, 12], [68187, 11], [68487, 11], [68787, 11], [69086, 11], [69386, 11], [69686, 11], [69986, 11], [70286, 12], [70309, 2], [70586, 12], [70610, 2], [70885, 14], [70910, 2], [71185, 15], [71211, 2], [71486, 15], [71786, 16], [72087, 16], [72387, 17]], "point": [179, 129]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-00.58|+00.39|-01.73"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [162, 110, 280, 184], "mask": [[32867, 114], [33166, 115], [33466, 115], [33766, 114], [34066, 114], [34366, 114], [34666, 113], [34966, 113], [35266, 112], [35566, 112], [35866, 111], [36166, 111], [36466, 110], [36766, 110], [37066, 110], [37365, 110], [37665, 110], [37965, 109], [38265, 109], [38565, 108], [38865, 108], [39165, 108], [39465, 107], [39765, 107], [40065, 106], [40365, 106], [40665, 105], [40965, 105], [41265, 104], [41565, 104], [41865, 104], [42165, 103], [42465, 103], [42764, 103], [43064, 103], [43364, 102], [43664, 102], [43964, 102], [44264, 101], [44564, 101], [44864, 100], [45164, 100], [45464, 99], [45764, 99], [46064, 98], [46364, 98], [46664, 98], [46964, 97], [47264, 97], [47564, 96], [47864, 96], [48164, 95], [48463, 96], [48763, 95], [49063, 95], [49363, 95], [49663, 94], [49963, 94], [50263, 93], [50563, 93], [50863, 92], [51163, 92], [51463, 92], [51763, 91], [52063, 91], [52363, 90], [52663, 90], [52963, 89], [53263, 89], [53563, 88], [53862, 89], [54162, 89], [54462, 88], [54762, 88], [55063, 86]], "point": [221, 146]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|-00.18|+00.91|-00.51", "placeStationary": true, "receptacleObjectId": "Cabinet|-00.58|+00.39|-01.73"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [163, 111, 299, 274], "mask": [[33280, 2], [33467, 112], [33580, 3], [33767, 111], [33879, 5], [34067, 111], [34179, 5], [34367, 110], [34478, 7], [34667, 110], [34778, 7], [34966, 120], [35266, 110], [35377, 10], [35566, 110], [35677, 10], [35866, 109], [35976, 12], [36166, 109], [36276, 12], [36466, 108], [36575, 14], [36766, 108], [36875, 15], [37066, 124], [37366, 107], [37474, 17], [37666, 107], [37774, 17], [37966, 106], [38073, 19], [38266, 106], [38373, 20], [38566, 105], [38672, 21], [38866, 105], [38972, 22], [39166, 128], [39466, 104], [39571, 24], [39766, 130], [40066, 103], [40170, 26], [40365, 104], [40470, 27], [40665, 103], [40769, 28], [40965, 103], [41069, 29], [41265, 134], [41565, 102], [41668, 31], [41865, 37], [41910, 90], [42165, 37], [42203, 4], [42210, 56], [42267, 33], [42465, 40], [42509, 57], [42567, 33], [42765, 39], [42810, 55], [42866, 34], [43065, 36], [43110, 55], [43166, 34], [43365, 36], [43411, 89], [43665, 36], [43711, 53], [43765, 35], [43965, 35], [44011, 89], [44265, 35], [44311, 52], [44364, 36], [44565, 35], [44611, 52], [44664, 36], [44865, 35], [44911, 51], [44963, 37], [45165, 35], [45211, 51], [45263, 37], [45465, 34], [45511, 50], [45562, 38], [45764, 35], [45811, 50], [45862, 38], [46064, 35], [46111, 89], [46364, 35], [46410, 50], [46461, 39], [46664, 35], [46710, 90], [46964, 35], [47010, 49], [47060, 40], [47264, 35], [47310, 49], [47360, 40], [47564, 35], [47609, 49], [47659, 41], [47864, 35], [47909, 49], [47959, 41], [48164, 35], [48209, 91], [48464, 35], [48509, 48], [48558, 42], [48764, 35], [48809, 91], [49064, 36], [49108, 48], [49157, 43], [49364, 36], [49408, 48], [49457, 43], [49664, 91], [49756, 44], [49964, 91], [50056, 44], [50264, 136], [50564, 90], [50655, 45], [50864, 136], [51163, 90], [51254, 46], [51463, 90], [51554, 46], [51763, 89], [51853, 47], [52063, 89], [52153, 47], [52363, 137], [52663, 88], [52752, 48], [52963, 137], [53263, 87], [53351, 49], [53563, 137], [53863, 86], [53950, 50], [54163, 86], [54250, 50], [54549, 51], [54849, 51], [55149, 51], [55449, 51], [55749, 51], [56050, 50], [56350, 50], [56650, 50], [56951, 49], [57251, 49], [57551, 49], [57852, 48], [58152, 48], [58452, 48], [58753, 47], [59053, 47], [59353, 47], [59654, 46], [59954, 46], [60254, 46], [60555, 45], [60855, 45], [61155, 45], [61456, 44], [61756, 44], [62056, 44], [62357, 43], [62657, 43], [62957, 43], [63258, 42], [63558, 42], [63858, 42], [64159, 41], [64459, 41], [64759, 41], [65060, 40], [65360, 40], [65660, 40], [65961, 39], [66261, 39], [66561, 39], [66862, 38], [67162, 38], [67462, 38], [67763, 37], [68063, 37], [68363, 37], [68664, 36], [68964, 36], [69264, 36], [69565, 35], [69865, 35], [70165, 35], [70466, 34], [70766, 34], [71066, 34], [71367, 33], [71667, 33], [71967, 33], [72268, 32], [72568, 32], [72868, 32], [73169, 31], [73469, 31], [73769, 31], [74070, 30], [74370, 30], [74670, 30], [74971, 29], [75271, 29], [75571, 29], [75872, 28], [76172, 28], [76472, 27], [76773, 25], [77073, 24], [77373, 23], [77674, 21], [77974, 20], [78274, 19], [78575, 17], [78875, 16], [79175, 15], [79476, 13], [79776, 12], [80076, 11], [80377, 9], [80677, 9], [80977, 8], [81278, 6], [81578, 5], [81878, 4], [82179, 2]], "point": [231, 180]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-00.58|+00.39|-01.73"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [163, 111, 299, 274], "mask": [[33280, 2], [33467, 112], [33580, 3], [33767, 111], [33879, 5], [34067, 111], [34179, 5], [34367, 110], [34478, 7], [34667, 110], [34778, 7], [34966, 120], [35266, 110], [35377, 10], [35566, 110], [35677, 10], [35866, 109], [35976, 12], [36166, 109], [36276, 12], [36466, 108], [36575, 14], [36766, 108], [36875, 15], [37066, 124], [37366, 107], [37474, 17], [37666, 107], [37774, 17], [37966, 106], [38073, 19], [38266, 106], [38373, 20], [38566, 105], [38672, 21], [38866, 105], [38972, 22], [39166, 128], [39466, 104], [39571, 24], [39766, 130], [40066, 103], [40170, 26], [40365, 104], [40470, 27], [40665, 103], [40769, 28], [40965, 103], [41069, 29], [41265, 134], [41565, 102], [41668, 31], [41865, 37], [41910, 90], [42165, 37], [42203, 4], [42210, 56], [42267, 33], [42465, 40], [42509, 57], [42567, 33], [42765, 39], [42810, 55], [42866, 34], [43065, 36], [43110, 55], [43166, 34], [43365, 36], [43411, 89], [43665, 36], [43711, 53], [43765, 35], [43965, 35], [44011, 89], [44265, 35], [44311, 52], [44364, 36], [44565, 35], [44611, 52], [44664, 36], [44865, 35], [44911, 51], [44963, 37], [45165, 35], [45211, 51], [45263, 37], [45465, 34], [45511, 50], [45562, 38], [45764, 35], [45811, 50], [45862, 38], [46064, 35], [46111, 89], [46364, 35], [46410, 50], [46461, 39], [46664, 35], [46710, 90], [46964, 35], [47010, 49], [47060, 40], [47264, 35], [47310, 49], [47360, 40], [47564, 35], [47609, 49], [47659, 41], [47864, 9], [47877, 22], [47909, 49], [47959, 41], [48164, 7], [48179, 20], [48209, 91], [48464, 6], [48480, 19], [48509, 48], [48558, 42], [48764, 5], [48781, 18], [48809, 91], [49064, 5], [49081, 19], [49108, 48], [49157, 43], [49364, 5], [49381, 19], [49408, 48], [49457, 43], [49664, 5], [49681, 74], [49756, 44], [49964, 5], [49981, 74], [50056, 44], [50264, 5], [50281, 119], [50564, 5], [50580, 74], [50655, 45], [50864, 5], [50880, 120], [51163, 6], [51180, 73], [51254, 46], [51463, 6], [51480, 73], [51554, 46], [51763, 6], [51780, 72], [51853, 47], [52063, 6], [52080, 72], [52153, 47], [52363, 7], [52379, 121], [52663, 8], [52678, 73], [52752, 48], [52963, 10], [52977, 123], [53263, 87], [53351, 49], [53563, 137], [53863, 86], [53950, 50], [54163, 86], [54250, 50], [54549, 51], [54849, 51], [55149, 51], [55449, 51], [55749, 51], [56050, 50], [56350, 50], [56650, 50], [56951, 49], [57251, 49], [57551, 49], [57852, 48], [58152, 48], [58452, 48], [58753, 47], [59053, 47], [59353, 47], [59654, 46], [59954, 46], [60254, 46], [60555, 45], [60855, 45], [61155, 45], [61456, 44], [61756, 44], [62056, 44], [62357, 43], [62657, 43], [62957, 43], [63258, 42], [63558, 42], [63858, 42], [64159, 41], [64459, 41], [64759, 41], [65060, 40], [65360, 40], [65660, 40], [65961, 39], [66261, 39], [66561, 39], [66862, 38], [67162, 38], [67462, 38], [67763, 37], [68063, 37], [68363, 37], [68664, 36], [68964, 36], [69264, 36], [69565, 35], [69865, 35], [70165, 35], [70466, 34], [70766, 34], [71066, 34], [71367, 33], [71667, 33], [71967, 33], [72268, 32], [72568, 32], [72868, 32], [73169, 31], [73469, 31], [73769, 31], [74070, 30], [74370, 30], [74670, 30], [74971, 29], [75271, 29], [75571, 29], [75872, 28], [76172, 28], [76472, 27], [76773, 25], [77073, 24], [77373, 23], [77674, 21], [77974, 20], [78274, 19], [78575, 17], [78875, 16], [79175, 15], [79476, 13], [79776, 12], [80076, 11], [80377, 9], [80677, 9], [80977, 8], [81278, 6], [81578, 5], [81878, 4], [82179, 2]], "point": [231, 180]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan19", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -2.25, "y": 0.9016907, "z": -3.0}, "object_poses": [{"objectName": "Pan_4966d0cc", "position": {"x": -2.44983053, "y": 0.912074, "z": -3.87812114}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_2b74ee3a", "position": {"x": -0.500435948, "y": 0.9597968, "z": -1.3709687}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_78f219bc", "position": {"x": -0.3521942, "y": 1.29668939, "z": -3.73019385}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "DishSponge_78f219bc", "position": {"x": -2.98770881, "y": 0.9132642, "z": -3.58163619}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_fca8a224", "position": {"x": -1.84543049, "y": 0.805556, "z": -3.66151619}, "rotation": {"x": 0.0, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Spoon_94d2125c", "position": {"x": -0.338365972, "y": 0.917129338, "z": -0.472991}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_bdd88192", "position": {"x": -2.581425, "y": 0.71192044, "z": -0.496000022}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Plate_bdd88192", "position": {"x": -1.5692029, "y": 0.08108103, "z": -3.67346835}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_dd4bbd23", "position": {"x": 0.007825613, "y": 0.9132962, "z": -3.213008}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_dd4bbd23", "position": {"x": -0.37460953, "y": 0.0800885558, "z": -1.4149735}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_697b561f", "position": {"x": -0.244556189, "y": 0.911298, "z": -2.91177487}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_2ce80e75", "position": {"x": -0.412048727, "y": 0.742647648, "z": -2.91359067}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_5fac94a5", "position": {"x": -3.14085817, "y": 0.74291414, "z": -2.8021493}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_5fac94a5", "position": {"x": -0.09526098, "y": 0.91230613, "z": -1.61635566}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_908150ce", "position": {"x": -3.13095522, "y": 1.486718, "z": -1.94104493}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Mug_f4b8ca2f", "position": {"x": -0.176295966, "y": 0.910999954, "z": -0.5075458}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_fd5c5c80", "position": {"x": -0.3767, "y": 0.9759, "z": -2.0314}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Egg_b0ce4484", "position": {"x": -1.89526391, "y": 0.949600756, "z": -3.98989987}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_410beacd", "position": {"x": -1.63819706, "y": 0.7908815, "z": -3.83288383}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_77e17ce3", "position": {"x": -0.327004015, "y": 0.9481207, "z": -3.68102622}, "rotation": {"x": 0.004293074, "y": 298.22287, "z": 0.0431186967}}, {"objectName": "Fork_1cd5bb8d", "position": {"x": -0.5301752, "y": 0.746890068, "z": -1.331406}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pan_4966d0cc", "position": {"x": -0.3946991, "y": 0.9731094, "z": -2.41475344}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_2b74ee3a", "position": {"x": -3.23445, "y": 0.6850367, "z": -2.12575579}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Lettuce_908150ce", "position": {"x": -2.708911, "y": 0.8047816, "z": -0.7412781}, "rotation": {"x": 0.0, "y": 135.000092, "z": 0.0}}, {"objectName": "Tomato_f34461c5", "position": {"x": -3.13095474, "y": 1.4484874, "z": -2.12575531}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_1634f71e", "position": {"x": -1.70727491, "y": 0.833196, "z": -3.66151619}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_dd4bbd23", "position": {"x": -3.29236221, "y": 0.7165637, "z": -0.683499932}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_fca8a224", "position": {"x": -3.0079875, "y": 0.7287701, "z": -0.496}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SaltShaker_2ce80e75", "position": {"x": -3.0079875, "y": 0.710076332, "z": -0.6835}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_46bffc5f", "position": {"x": -0.257330954, "y": 0.9484372, "z": -1.49366212}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_5fac94a5", "position": {"x": -0.176295966, "y": 0.91230613, "z": -1.30962193}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_a7531534", "position": {"x": -3.29236221, "y": 0.7138123, "z": -0.433499932}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Plate_bdd88192", "position": {"x": -2.00739837, "y": 0.07997203, "z": -3.674592}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_78f219bc", "position": {"x": -2.66072655, "y": 1.29497826, "z": -3.92726612}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_94d2125c", "position": {"x": -3.082638, "y": 0.7477374, "z": -2.91282749}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_f4b8ca2f", "position": {"x": -2.93075848, "y": 0.7624276, "z": -0.358240336}, "rotation": {"x": -0.00214235066, "y": 134.999557, "z": -0.00130094273}}, {"objectName": "Bowl_697b561f", "position": {"x": -3.079207, "y": 0.638838, "z": -2.12575531}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}], "object_toggles": [], "random_seed": 3363705466, "scene_num": 19}, "task_id": "trial_T20190907_152710_355664", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AM2KK02JXXW48_3ZPPDN2SLYD66NVJW8OVXR0MBYC9EL", "high_descs": ["Turn around and head to the microwave on the counter on the right. ", "Pick up the mug on the counter, left of the microwave. ", "Bring the mug to the microwave.", "Heat the mug in the microwave. ", "Bring the mug to the cabinet below the microwave.", "Put the mug in the right cabinet under the microwave. "], "task_desc": "Put a heated mug in the lower right cabinet. ", "votes": [1, 1, 1, 1, 1, 1, 1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_31QNSG6A5UALJ3XZ484BRLDU56A78U", "high_descs": ["turn left and walk forwards a bit, then turn left and walk to the end of the room, then take a right and walk to the microwave ahead", "grab the cup off of the counter to the left of the microwave", "move to the right a bit and face the microwave again", "place the cup inside of the microwave, microwave it then take it back out", "turn around and walk to the table ahead, then turn around and face the kitchen cabinet to the right of the microwave", "place the cup inside of the kitchen cabinet to the bottom right of the microwave"], "task_desc": "place a microwaved cup inside of the kitchen cabinet", "votes": [1, 1, 1, 1, 1, 1]}, {"assignment_id": "AJQGWGESKQT4Y_33IZTU6J84IXH2IZ0JEE2BQJJSBSXN", "high_descs": ["Turn to the left and then left again to stand in front of the gray trash can.", "Pick the brown container up from the counter.", "Move to the right and stand in front of the microwave.", "Put the container in the microwave, shut the door, open the door, and take the container out and shut the door.", "Move to the right and face the cabinet under the microwave.", "Put the container in the lower cabinet on the right and shut the door."], "task_desc": "Put a warmed container in the cabinet.", "votes": [1, 1, 0, 1, 1, 1, 1, 1]}, {"assignment_id": "A1AKL5YH9NLD2V_3Z2R0DQ0JKVV63AKBZ9GN4S85XWE22", "high_descs": ["Turn around and walk to the microwave to the right of the gray trash can.", "Pick up the mug to the left of the microwave.", "Take one step to the right and face the microwave.", "Microwave the mug and then pick the mug up from the microwave and close the microwave.", "Take a step back and look at the cabinets below the microwave.", "Place the mug in the right cabinet below the microwave."], "task_desc": "Place a microwave mug in the right cabinet below the microwave.", "votes": [1, 1]}]}}