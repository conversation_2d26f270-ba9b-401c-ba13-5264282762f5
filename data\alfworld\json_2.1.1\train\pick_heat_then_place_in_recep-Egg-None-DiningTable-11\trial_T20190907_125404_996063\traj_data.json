{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 49}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "DiningTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-10|-1|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-10.45627784, -10.45627784, 2.099985836, 2.099985836, 3.9495532, 3.9495532]], "coordinateReceptacleObjectId": ["DiningTable", [-8.812, -8.812, 1.792, 1.792, 3.96, 3.96]], "forceVisible": true, "objectId": "Egg|-02.61|+00.99|+00.52"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-4|2|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-9|-1|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "diningtable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-10.45627784, -10.45627784, 2.099985836, 2.099985836, 3.9495532, 3.9495532]], "coordinateReceptacleObjectId": ["DiningTable", [-8.812, -8.812, 1.792, 1.792, 3.96, 3.96]], "forceVisible": true, "objectId": "Egg|-02.61|+00.99|+00.52", "receptacleObjectId": "DiningTable|-02.20|+00.99|+00.45"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-02.61|+00.99|+00.52"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [111, 105, 127, 124], "mask": [[31318, 2], [31615, 8], [31914, 10], [32213, 12], [32512, 14], [32812, 14], [33111, 16], [33411, 16], [33711, 17], [34011, 17], [34311, 17], [34611, 17], [34911, 17], [35211, 17], [35511, 16], [35812, 15], [36112, 14], [36413, 13], [36714, 11], [37016, 7]], "point": [119, 113]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-02.61|+00.99|+00.52", "placeStationary": true, "receptacleObjectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 269], [25822, 270], [26122, 269], [26421, 270], [26720, 270], [27020, 270], [27319, 271], [27619, 270], [27918, 271], [28217, 271], [28517, 271], [28816, 271], [29115, 272], [29415, 273], [29714, 274], [30013, 275], [30313, 275], [30612, 276], [30911, 277], [31211, 277], [31510, 278], [31809, 279], [32109, 279], [32408, 280], [32708, 280], [33007, 280], [33306, 281], [33606, 281], [33905, 281], [34204, 282], [34504, 282], [34803, 282], [35102, 283], [35402, 282], [35701, 283], [36000, 283], [36300, 283], [36600, 282], [36900, 282], [37200, 281], [37500, 281], [37800, 280], [38100, 280], [38400, 280], [38700, 279], [39000, 279], [39300, 278], [39600, 278], [39900, 277], [40200, 277], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 269], [25822, 270], [26122, 269], [26421, 270], [26720, 270], [27020, 270], [27319, 271], [27619, 270], [27918, 271], [28217, 271], [28517, 271], [28816, 271], [29115, 272], [29415, 273], [29714, 274], [30013, 275], [30313, 275], [30612, 276], [30911, 277], [31211, 277], [31510, 278], [31809, 279], [32109, 130], [32246, 142], [32408, 130], [32547, 141], [32708, 128], [32848, 140], [33007, 129], [33149, 138], [33306, 129], [33450, 137], [33606, 128], [33751, 136], [33905, 129], [34051, 135], [34204, 129], [34352, 134], [34504, 129], [34652, 134], [34803, 130], [34952, 133], [35102, 131], [35252, 133], [35402, 131], [35552, 132], [35701, 132], [35852, 132], [36000, 133], [36152, 131], [36300, 133], [36452, 131], [36600, 133], [36752, 130], [36900, 134], [37051, 131], [37200, 134], [37351, 130], [37500, 135], [37650, 131], [37800, 136], [37949, 131], [38100, 137], [38248, 132], [38400, 139], [38546, 134], [38700, 279], [39000, 279], [39300, 278], [39600, 278], [39900, 277], [40200, 277], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-02.61|+00.99|+00.52"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [133, 108, 151, 129], "mask": [[32239, 7], [32538, 9], [32836, 12], [33136, 13], [33435, 15], [33734, 17], [34034, 17], [34333, 19], [34633, 19], [34933, 19], [35233, 19], [35533, 19], [35833, 19], [36133, 19], [36433, 19], [36733, 19], [37034, 17], [37334, 17], [37635, 15], [37936, 13], [38237, 11], [38539, 7]], "point": [142, 117]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 269], [25822, 270], [26122, 269], [26421, 270], [26720, 270], [27020, 270], [27319, 271], [27619, 270], [27918, 271], [28217, 271], [28517, 271], [28816, 271], [29115, 272], [29415, 273], [29714, 274], [30013, 275], [30313, 275], [30612, 276], [30911, 277], [31211, 277], [31510, 278], [31809, 279], [32109, 279], [32408, 280], [32708, 280], [33007, 280], [33306, 281], [33606, 281], [33905, 281], [34204, 282], [34504, 282], [34803, 282], [35102, 283], [35402, 282], [35701, 283], [36000, 283], [36300, 283], [36600, 282], [36900, 282], [37200, 281], [37500, 281], [37800, 280], [38100, 280], [38400, 280], [38700, 279], [39000, 279], [39300, 278], [39600, 278], [39900, 277], [40200, 277], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-02.61|+00.99|+00.52", "placeStationary": true, "receptacleObjectId": "DiningTable|-02.20|+00.99|+00.45"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 90, 299, 300], "mask": [[26739, 32], [26813, 103], [26937, 10], [26967, 14], [27038, 33], [27114, 102], [27236, 11], [27266, 16], [27338, 32], [27414, 102], [27536, 12], [27566, 17], [27637, 32], [27715, 100], [27836, 13], [27865, 18], [27937, 32], [28016, 99], [28135, 15], [28163, 21], [28236, 33], [28316, 99], [28435, 15], [28462, 23], [28535, 33], [28617, 98], [28735, 16], [28760, 25], [28835, 33], [28917, 98], [29034, 52], [29134, 34], [29217, 98], [29334, 53], [29434, 34], [29517, 99], [29634, 53], [29733, 35], [29818, 98], [29934, 54], [30033, 34], [30118, 99], [30233, 32], [30266, 23], [30332, 35], [30418, 101], [30541, 3], [30570, 19], [30631, 36], [30718, 100], [30872, 18], [30931, 36], [31019, 79], [31174, 17], [31230, 37], [31319, 77], [31479, 12], [31530, 38], [31619, 77], [31780, 12], [31829, 39], [31919, 77], [32004, 8], [32081, 12], [32129, 39], [32219, 81], [32301, 11], [32382, 11], [32428, 40], [32530, 81], [32683, 11], [32728, 40], [32834, 76], [32983, 12], [33027, 41], [33134, 76], [33283, 12], [33326, 42], [33434, 75], [33584, 12], [33626, 43], [33729, 32], [33767, 43], [33884, 13], [33925, 44], [34026, 34], [34068, 41], [34184, 13], [34225, 44], [34314, 1], [34323, 37], [34369, 39], [34484, 14], [34524, 46], [34613, 2], [34622, 38], [34668, 39], [34784, 15], [34824, 47], [34912, 4], [34921, 39], [34968, 39], [35085, 14], [35123, 49], [35210, 6], [35223, 37], [35268, 38], [35385, 15], [35423, 50], [35535, 25], [35568, 38], [35686, 14], [35722, 53], [35806, 1], [35835, 24], [35869, 36], [35987, 13], [36021, 56], [36104, 3], [36135, 23], [36170, 35], [36288, 12], [36321, 59], [36401, 5], [36435, 23], [36470, 35], [36588, 12], [36620, 64], [36696, 10], [36735, 23], [36770, 34], [36888, 12], [36920, 86], [37035, 23], [37070, 34], [37187, 13], [37219, 87], [37335, 23], [37370, 34], [37487, 13], [37519, 87], [37635, 23], [37670, 33], [37787, 13], [37818, 87], [37935, 22], [37970, 33], [38087, 13], [38118, 87], [38235, 22], [38270, 33], [38386, 14], [38417, 88], [38535, 22], [38570, 33], [38686, 14], [38716, 89], [38835, 22], [38870, 32], [38985, 15], [39016, 89], [39135, 22], [39170, 32], [39285, 15], [39315, 89], [39435, 22], [39470, 32], [39586, 14], [39615, 89], [39736, 20], [39770, 32], [39886, 14], [39914, 90], [40036, 20], [40070, 32], [40186, 14], [40214, 90], [40336, 20], [40371, 31], [40485, 15], [40513, 91], [40636, 20], [40671, 31], [40785, 15], [40813, 91], [40936, 20], [40971, 31], [41085, 15], [41112, 92], [41236, 20], [41271, 32], [41385, 15], [41411, 93], [41536, 20], [41571, 32], [41685, 15], [41711, 93], [41836, 20], [41870, 33], [41985, 15], [42010, 94], [42136, 21], [42170, 33], [42284, 15], [42310, 94], [42436, 22], [42469, 35], [42584, 15], [42609, 96], [42736, 24], [42767, 37], [42883, 15], [42909, 96], [43036, 27], [43064, 41], [43183, 14], [43208, 97], [43336, 69], [43483, 14], [43507, 93], [43636, 69], [43782, 14], [43807, 85], [43936, 70], [44082, 14], [44106, 80], [44236, 71], [44381, 14], [44406, 79], [44536, 72], [44681, 14], [44705, 79], [44836, 74], [44979, 15], [45005, 78], [45105, 1], [45136, 76], [45277, 17], [45304, 26], [45349, 33], [45405, 1], [45436, 79], [45528, 5], [45549, 8], [45559, 34], [45604, 12], [45677, 4], [45705, 1], [45736, 156], [45903, 13], [45949, 2], [46005, 2], [46036, 156], [46202, 13], [46240, 29], [46305, 2], [46336, 155], [46502, 79], [46605, 2], [46636, 155], [46801, 80], [46904, 3], [46936, 154], [47101, 80], [47204, 3], [47236, 154], [47400, 82], [47502, 5], [47536, 153], [47700, 82], [47795, 13], [47836, 153], [48000, 83], [48086, 22], [48136, 152], [48300, 108], [48436, 151], [48600, 108], [48735, 152], [48900, 108], [49035, 151], [49200, 108], [49335, 151], [49500, 109], [49635, 150], [49800, 285], [50100, 285], [50400, 288], [50700, 291], [51000, 290], [51300, 290], [51600, 289], [51900, 289], [52200, 289], [52500, 289], [52800, 289], [53100, 289], [53400, 290], [53700, 291], [54000, 292], [54300, 293], [54600, 294], [54900, 296], [55200, 163], [55381, 118], [55500, 159], [55682, 273], [55984, 268], [56285, 266], [56586, 264], [56886, 263], [57186, 262], [57487, 261], [57787, 260], [58087, 260], [58387, 260], [58687, 260], [58987, 260], [59287, 260], [59586, 262], [59886, 262], [60185, 264], [60484, 266], [60783, 268], [61082, 271], [61380, 274], [61677, 280], [61975, 286], [62273, 8673], [70953, 290], [71257, 284], [71559, 280], [71861, 277], [72162, 274], [72464, 136], [72644, 9], [72872, 11], [72944, 10], [73171, 11], [73245, 9], [73471, 10], [73546, 9], [73770, 10], [73846, 10], [74069, 10], [74147, 9], [74368, 11], [74448, 9], [74668, 10], [74748, 9], [74967, 10], [75049, 9], [75266, 10], [75349, 9], [75565, 10], [75650, 9], [75865, 10], [75951, 9], [76164, 10], [76251, 9], [76463, 10], [76552, 9], [76763, 9], [76853, 8], [77062, 9], [77153, 9], [77361, 10], [77454, 8], [77660, 10], [77755, 8], [77960, 9], [78055, 9], [78259, 9], [78356, 8], [78558, 9], [78656, 9], [78857, 10], [78957, 8], [79157, 9], [79258, 8], [79456, 9], [79558, 9], [79755, 9], [79859, 8], [80055, 9], [80160, 8], [80354, 9], [80460, 8], [80653, 9], [80761, 8], [80952, 9], [81062, 7], [81252, 8], [81362, 8], [81551, 9], [81663, 8], [81850, 9], [81963, 8], [82149, 9], [82264, 8], [82449, 8], [82565, 7], [82748, 8], [82865, 8], [83047, 9], [83166, 7], [83346, 9], [83467, 7], [83646, 8], [83767, 8], [83945, 8], [84068, 7], [84244, 8], [84368, 8], [84544, 8], [84669, 7], [84843, 8], [84970, 7], [85142, 8], [85270, 8], [85441, 8], [85571, 7], [85741, 7], [85872, 7], [86040, 8], [86172, 7], [86339, 8], [86473, 7], [86638, 8], [86774, 6], [86938, 7], [87074, 7], [87237, 7], [87375, 7], [87536, 8], [87675, 7], [87836, 7], [87976, 7], [88135, 7], [88277, 7], [88433, 8], [88577, 7], [88733, 8], [88878, 7], [89032, 8], [89178, 8], [89331, 8], [89479, 7], [89631, 8], [89779, 7], [89931, 7]], "point": [147, 192]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan11", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": 1.25, "y": 0.9009992, "z": -0.5}, "object_poses": [{"objectName": "Kettle_7922e01e", "position": {"x": -0.08284783, "y": 0.9133413, "z": -1.48903823}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_932ef12a", "position": {"x": -0.361999929, "y": 0.06976978, "z": 0.482676446}, "rotation": {"x": 0.0, "y": 89.99995, "z": 0.0}}, {"objectName": "Pot_da74902f", "position": {"x": 1.361, "y": 0.962147355, "z": 0.5617}, "rotation": {"x": 0.0, "y": 269.9999, "z": 0.0}}, {"objectName": "Fork_834895c5", "position": {"x": 0.3419488, "y": 0.783435047, "z": -1.64893007}, "rotation": {"x": 0.0, "y": 180.0, "z": -1.40334225e-14}}, {"objectName": "Spoon_9fcd93ca", "position": {"x": 0.3000576, "y": 0.9134294, "z": 0.209457338}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_b4e2a06a", "position": {"x": -2.61406946, "y": 0.9672249, "z": 0.345098317}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Egg_7c2e9408", "position": {"x": -0.267375469, "y": 0.9474633, "z": -1.40615737}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_923adba4", "position": {"x": 0.455792636, "y": 0.107122183, "z": -1.43450475}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Cup_923adba4", "position": {"x": 0.504299939, "y": 0.108622193, "z": 0.270902365}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c3590fed", "position": {"x": -0.586564064, "y": 0.111744404, "z": -1.50870013}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c3590fed", "position": {"x": -2.22249985, "y": 0.7816605, "z": -1.80632138}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_929aede3", "position": {"x": 0.564747, "y": 0.961345851, "z": 0.209457338}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_c96a3c07", "position": {"x": -0.09697643, "y": 0.9117321, "z": 0.3752191}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_42ce5824", "position": {"x": -0.04583584, "y": 0.107191741, "z": -1.39787245}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_213d034a", "position": {"x": 1.75472069, "y": 0.104848027, "z": 0.348659247}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_213d034a", "position": {"x": -0.6440377, "y": 1.325041, "z": -1.883143}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Bread_59b513fb", "position": {"x": -1.92895365, "y": 1.03509808, "z": 0.435047358}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_59b513fb", "position": {"x": 1.72404635, "y": 0.9951729, "z": 0.3752191}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_841024df", "position": {"x": -1.92895377, "y": 0.947225034, "z": 0.704894543}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_41d4caea", "position": {"x": -1.79193056, "y": 1.0193764, "z": 0.704894543}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_59b513fb", "position": {"x": -2.135, "y": 1.5754956, "z": -1.76346016}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_b9b64351", "position": {"x": -1.92895365, "y": 0.977574646, "z": 0.614945531}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Fork_834895c5", "position": {"x": 1.84613431, "y": 0.912848532, "z": 0.209457338}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_c96a3c07", "position": {"x": -0.4519031, "y": 0.9117321, "z": -1.82056189}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_7922e01e", "position": {"x": -2.47704625, "y": 0.9532663, "z": 0.614945531}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_923adba4", "position": {"x": -0.09697643, "y": 0.9107361, "z": 0.209457338}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_929aede3", "position": {"x": 0.697091639, "y": 0.961345851, "z": 0.209457338}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_09398926", "position": {"x": -2.340023, "y": 0.9509215, "z": 0.345098317}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_c3590fed", "position": {"x": 1.535548, "y": 0.913158834, "z": -1.5719192}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_da74902f", "position": {"x": 1.0071, "y": 0.962147355, "z": 0.5617}, "rotation": {"x": 0.0, "y": 269.9999, "z": 0.0}}, {"objectName": "Spatula_b4e2a06a", "position": {"x": -0.267375469, "y": 0.927299857, "z": -1.90344274}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_54f2d061", "position": {"x": -1.79193056, "y": 1.05651045, "z": 0.2551492}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_7c2e9408", "position": {"x": -2.61406946, "y": 0.9873883, "z": 0.524996459}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_22e053cf", "position": {"x": 0.851057053, "y": 1.0014, "z": -1.53786421}, "rotation": {"x": 0.0, "y": 231.4903, "z": 0.0}}, {"objectName": "SaltShaker_42ce5824", "position": {"x": 1.66300225, "y": 0.9086062, "z": 0.706742644}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_213d034a", "position": {"x": -2.203, "y": 0.9485312, "z": 0.4350474}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_41d4caea", "position": {"x": 0.8049864, "y": 0.9867013, "z": -1.71562362}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "ButterKnife_d4975865", "position": {"x": 1.412741, "y": 0.9123421, "z": -1.48903823}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_483d2614", "position": {"x": -2.203, "y": 0.9820612, "z": 0.165200174}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pan_4b842b85", "position": {"x": 1.022105, "y": 0.9591456, "z": 0.288545281}, "rotation": {"x": 0.0, "y": 219.19072, "z": 0.0}}, {"objectName": "DishSponge_932ef12a", "position": {"x": -0.6786371, "y": 0.7723391, "z": -1.53843045}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_9fcd93ca", "position": {"x": 0.8049865, "y": 0.9206794, "z": -1.64917481}, "rotation": {"x": 0.0, "y": 6.83018925e-06, "z": 0.0}}, {"objectName": "Mug_841024df", "position": {"x": -0.268934667, "y": 1.32373488, "z": -1.809647}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1363340278, "scene_num": 11}, "task_id": "trial_T20190907_125404_996063", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A3MLUEOP3CCLXL_3FTF2T8WLUZ0NNHAH8I1JX7MI2J9W3", "high_descs": ["Turn to your left and head to the door across the room then turn right and face the white table", "Pick up the egg that is sitting on the table.", "Turn to your right and hang a right once you pass the table, then walk to the microwave", "Open the microwave and place the egg inside then close the microwave and turn it on. Open the microwave and pick up the egg that is inside and then close the microwave", "Turn right and talk to the fridge, then turn right and walk to the white table.", "Place the egg on the table to the left of the potato."], "task_desc": "Place a cooked egg on a table", "votes": [1, 1]}, {"assignment_id": "A1RLO9LNUJIW5S_3J2UYBXQQOTKXEERQUM4SS7GRU660T", "high_descs": ["Turn left and walk to the door then turn right.", "Pick up the egg to the left of the teapot.", "Turn right and take a few steps so you're even with the red microwave on your right then turn right and walk to it.", "Cook the egg in the microwave and then get it back out and close the door.", "Go back to where you got the egg.", "Put the egg to the left of the potato near the edge."], "task_desc": "Put a microwaved egg on the table.", "votes": [1, 1]}, {"assignment_id": "AM2KK02JXXW48_3WRFBPLXRD5OLRJG8YQ4W1R9PY13NL", "high_descs": ["Turn left and head to the table next to the door. ", "Pick up the egg on the table. ", "Turn around and bring the egg to the microwave on the right. ", "Heat the egg in the microwave.", "Take the heated egg, turn around and go to the table on the left.", "Put the heated egg on the table. "], "task_desc": "Put a heated egg on the table. ", "votes": [1, 1]}]}}