{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000283.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000284.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000285.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000286.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000287.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000288.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000289.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000290.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000291.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000292.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000293.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000294.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000295.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000296.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000297.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000298.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000299.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000300.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000301.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000302.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000303.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000304.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000305.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000306.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000307.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000308.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000309.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000310.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000311.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000312.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000313.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000314.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000315.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000316.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000317.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000318.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000319.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000320.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000321.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000322.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000323.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000324.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000325.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000326.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000327.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000328.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000329.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000330.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000331.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000332.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000333.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000334.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000335.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000336.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000337.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000338.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000339.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000340.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000341.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000342.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000343.png", "low_idx": 67}, {"high_idx": 8, "image_name": "000000344.png", "low_idx": 67}, {"high_idx": 8, "image_name": "000000345.png", "low_idx": 67}, {"high_idx": 8, "image_name": "000000346.png", "low_idx": 67}, {"high_idx": 8, "image_name": "000000347.png", "low_idx": 67}, {"high_idx": 8, "image_name": "000000348.png", "low_idx": 67}, {"high_idx": 8, "image_name": "000000349.png", "low_idx": 67}, {"high_idx": 8, "image_name": "000000350.png", "low_idx": 67}, {"high_idx": 8, "image_name": "000000351.png", "low_idx": 67}, {"high_idx": 8, "image_name": "000000352.png", "low_idx": 67}, {"high_idx": 8, "image_name": "000000353.png", "low_idx": 67}, {"high_idx": 8, "image_name": "000000354.png", "low_idx": 68}, {"high_idx": 8, "image_name": "000000355.png", "low_idx": 68}, {"high_idx": 8, "image_name": "000000356.png", "low_idx": 69}, {"high_idx": 8, "image_name": "000000357.png", "low_idx": 69}, {"high_idx": 8, "image_name": "000000358.png", "low_idx": 70}, {"high_idx": 8, "image_name": "000000359.png", "low_idx": 70}, {"high_idx": 8, "image_name": "000000360.png", "low_idx": 71}, {"high_idx": 8, "image_name": "000000361.png", "low_idx": 71}, {"high_idx": 8, "image_name": "000000362.png", "low_idx": 71}, {"high_idx": 8, "image_name": "000000363.png", "low_idx": 71}, {"high_idx": 8, "image_name": "000000364.png", "low_idx": 71}, {"high_idx": 8, "image_name": "000000365.png", "low_idx": 71}, {"high_idx": 8, "image_name": "000000366.png", "low_idx": 71}, {"high_idx": 8, "image_name": "000000367.png", "low_idx": 71}, {"high_idx": 8, "image_name": "000000368.png", "low_idx": 71}, {"high_idx": 8, "image_name": "000000369.png", "low_idx": 71}, {"high_idx": 8, "image_name": "000000370.png", "low_idx": 71}, {"high_idx": 8, "image_name": "000000371.png", "low_idx": 72}, {"high_idx": 8, "image_name": "000000372.png", "low_idx": 72}, {"high_idx": 9, "image_name": "000000373.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000374.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000375.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000376.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000377.png", "low_idx": 74}, {"high_idx": 9, "image_name": "000000378.png", "low_idx": 74}, {"high_idx": 9, "image_name": "000000379.png", "low_idx": 74}, {"high_idx": 9, "image_name": "000000380.png", "low_idx": 74}, {"high_idx": 9, "image_name": "000000381.png", "low_idx": 74}, {"high_idx": 9, "image_name": "000000382.png", "low_idx": 74}, {"high_idx": 9, "image_name": "000000383.png", "low_idx": 74}, {"high_idx": 9, "image_name": "000000384.png", "low_idx": 74}, {"high_idx": 9, "image_name": "000000385.png", "low_idx": 74}, {"high_idx": 9, "image_name": "000000386.png", "low_idx": 74}, {"high_idx": 9, "image_name": "000000387.png", "low_idx": 74}, {"high_idx": 9, "image_name": "000000388.png", "low_idx": 74}, {"high_idx": 9, "image_name": "000000389.png", "low_idx": 74}, {"high_idx": 9, "image_name": "000000390.png", "low_idx": 74}, {"high_idx": 9, "image_name": "000000391.png", "low_idx": 74}, {"high_idx": 9, "image_name": "000000392.png", "low_idx": 75}, {"high_idx": 9, "image_name": "000000393.png", "low_idx": 75}, {"high_idx": 9, "image_name": "000000394.png", "low_idx": 75}, {"high_idx": 9, "image_name": "000000395.png", "low_idx": 75}, {"high_idx": 9, "image_name": "000000396.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000397.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000398.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000399.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000400.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000401.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000402.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000403.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000404.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000405.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000406.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000407.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000408.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000409.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000410.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000411.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000412.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000413.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000414.png", "low_idx": 77}, {"high_idx": 9, "image_name": "000000415.png", "low_idx": 77}, {"high_idx": 9, "image_name": "000000416.png", "low_idx": 77}, {"high_idx": 9, "image_name": "000000417.png", "low_idx": 77}, {"high_idx": 9, "image_name": "000000418.png", "low_idx": 77}, {"high_idx": 9, "image_name": "000000419.png", "low_idx": 77}, {"high_idx": 9, "image_name": "000000420.png", "low_idx": 78}, {"high_idx": 9, "image_name": "000000421.png", "low_idx": 78}, {"high_idx": 9, "image_name": "000000422.png", "low_idx": 78}, {"high_idx": 9, "image_name": "000000423.png", "low_idx": 78}, {"high_idx": 9, "image_name": "000000424.png", "low_idx": 79}, {"high_idx": 9, "image_name": "000000425.png", "low_idx": 79}, {"high_idx": 9, "image_name": "000000426.png", "low_idx": 79}, {"high_idx": 9, "image_name": "000000427.png", "low_idx": 79}, {"high_idx": 9, "image_name": "000000428.png", "low_idx": 79}, {"high_idx": 9, "image_name": "000000429.png", "low_idx": 79}, {"high_idx": 9, "image_name": "000000430.png", "low_idx": 79}, {"high_idx": 9, "image_name": "000000431.png", "low_idx": 79}, {"high_idx": 9, "image_name": "000000432.png", "low_idx": 79}, {"high_idx": 9, "image_name": "000000433.png", "low_idx": 79}, {"high_idx": 9, "image_name": "000000434.png", "low_idx": 79}, {"high_idx": 9, "image_name": "000000435.png", "low_idx": 79}, {"high_idx": 9, "image_name": "000000436.png", "low_idx": 79}, {"high_idx": 9, "image_name": "000000437.png", "low_idx": 79}, {"high_idx": 9, "image_name": "000000438.png", "low_idx": 79}, {"high_idx": 9, "image_name": "000000439.png", "low_idx": 80}, {"high_idx": 9, "image_name": "000000440.png", "low_idx": 80}, {"high_idx": 9, "image_name": "000000441.png", "low_idx": 80}, {"high_idx": 9, "image_name": "000000442.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000443.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000444.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000445.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000446.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000447.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000448.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000449.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000450.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000451.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000452.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000453.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000454.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000455.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000456.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000457.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000458.png", "low_idx": 84}, {"high_idx": 10, "image_name": "000000459.png", "low_idx": 84}, {"high_idx": 10, "image_name": "000000460.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000461.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000462.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000463.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000464.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000465.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000466.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000467.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000468.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000469.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000470.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000471.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000472.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000473.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000474.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000475.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000476.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000477.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000478.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000479.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000480.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000481.png", "low_idx": 91}, {"high_idx": 10, "image_name": "000000482.png", "low_idx": 91}, {"high_idx": 10, "image_name": "000000483.png", "low_idx": 92}, {"high_idx": 10, "image_name": "000000484.png", "low_idx": 92}, {"high_idx": 10, "image_name": "000000485.png", "low_idx": 93}, {"high_idx": 10, "image_name": "000000486.png", "low_idx": 93}, {"high_idx": 10, "image_name": "000000487.png", "low_idx": 94}, {"high_idx": 10, "image_name": "000000488.png", "low_idx": 94}, {"high_idx": 10, "image_name": "000000489.png", "low_idx": 95}, {"high_idx": 10, "image_name": "000000490.png", "low_idx": 95}, {"high_idx": 10, "image_name": "000000491.png", "low_idx": 96}, {"high_idx": 10, "image_name": "000000492.png", "low_idx": 96}, {"high_idx": 10, "image_name": "000000493.png", "low_idx": 97}, {"high_idx": 10, "image_name": "000000494.png", "low_idx": 97}, {"high_idx": 10, "image_name": "000000495.png", "low_idx": 98}, {"high_idx": 10, "image_name": "000000496.png", "low_idx": 98}, {"high_idx": 10, "image_name": "000000497.png", "low_idx": 99}, {"high_idx": 10, "image_name": "000000498.png", "low_idx": 99}, {"high_idx": 10, "image_name": "000000499.png", "low_idx": 99}, {"high_idx": 10, "image_name": "000000500.png", "low_idx": 99}, {"high_idx": 10, "image_name": "000000501.png", "low_idx": 99}, {"high_idx": 10, "image_name": "000000502.png", "low_idx": 99}, {"high_idx": 10, "image_name": "000000503.png", "low_idx": 99}, {"high_idx": 10, "image_name": "000000504.png", "low_idx": 99}, {"high_idx": 10, "image_name": "000000505.png", "low_idx": 99}, {"high_idx": 10, "image_name": "000000506.png", "low_idx": 99}, {"high_idx": 10, "image_name": "000000507.png", "low_idx": 99}, {"high_idx": 10, "image_name": "000000508.png", "low_idx": 100}, {"high_idx": 10, "image_name": "000000509.png", "low_idx": 100}, {"high_idx": 10, "image_name": "000000510.png", "low_idx": 101}, {"high_idx": 10, "image_name": "000000511.png", "low_idx": 101}, {"high_idx": 10, "image_name": "000000512.png", "low_idx": 102}, {"high_idx": 10, "image_name": "000000513.png", "low_idx": 102}, {"high_idx": 11, "image_name": "000000514.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000515.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000516.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000517.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000518.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000519.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000520.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000521.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000522.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000523.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000524.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000525.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000526.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000527.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000528.png", "low_idx": 103}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "SideTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|1|1|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["butterknife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ButterKnife", [3.367272616, 3.367272616, 2.522407772, 2.522407772, 2.6585684, 2.6585684]], "coordinateReceptacleObjectId": ["SideTable", [3.0924, 3.0924, 1.7924, 1.7924, 2.8004, 2.8004]], "forceVisible": true, "objectId": "ButterKnife|+00.84|+00.66|+00.63"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-5|-5|3|60"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-6.878397, -6.878397, -5.23177148, -5.23177148, 2.9810076, 2.9810076]], "forceVisible": true, "objectId": "Bread|-01.72|+00.75|-01.31"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-1|1|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["butterknife", "sidetable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ButterKnife", [3.367272616, 3.367272616, 2.522407772, 2.522407772, 2.6585684, 2.6585684]], "coordinateReceptacleObjectId": ["SideTable", [3.0924, 3.0924, 1.7924, 1.7924, 2.8004, 2.8004]], "forceVisible": true, "objectId": "ButterKnife|+00.84|+00.66|+00.63", "receptacleObjectId": "SideTable|+00.77|+00.70|+00.45"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-5|-5|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-6.878397, -6.878397, -5.23177148, -5.23177148, 2.9810076, 2.9810076]], "coordinateReceptacleObjectId": ["DiningTable", [-6.904, -6.904, -3.840824604, -3.840824604, -0.1301278172, -0.1301278172]], "forceVisible": true, "objectId": "Bread|-01.72|+00.75|-01.31|BreadSliced_1"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|-8|-11|2|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-8.052, -8.052, -14.756, -14.756, 2.761099816, 2.761099816]], "forceVisible": true, "objectId": "Microwave|-02.01|+00.69|-03.69"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|-1|1|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "sidetable"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-6.878397, -6.878397, -5.23177148, -5.23177148, 2.9810076, 2.9810076]], "coordinateReceptacleObjectId": ["SideTable", [3.0924, 3.0924, 1.7924, 1.7924, 2.8004, 2.8004]], "forceVisible": true, "objectId": "Bread|-01.72|+00.75|-01.31|BreadSliced_1", "receptacleObjectId": "SideTable|+00.77|+00.70|+00.45"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 12, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ButterKnife|+00.84|+00.66|+00.63"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [53, 107, 63, 139], "mask": [[31861, 3], [32161, 3], [32461, 3], [32760, 4], [33060, 4], [33360, 4], [33660, 4], [33959, 5], [34259, 5], [34559, 5], [34859, 5], [35158, 6], [35458, 6], [35758, 5], [36058, 5], [36357, 6], [36657, 5], [36957, 5], [37257, 5], [37557, 4], [37856, 5], [38156, 4], [38456, 4], [38756, 3], [39055, 4], [39355, 4], [39655, 3], [39955, 3], [40255, 3], [40554, 3], [40854, 3], [41154, 3], [41453, 1]], "point": [58, 122]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-01.72|+00.75|-01.31"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [94, 129, 173, 171], "mask": [[38532, 11], [38820, 29], [39116, 37], [39412, 44], [39710, 49], [40008, 53], [40306, 56], [40605, 59], [40903, 62], [41202, 65], [41500, 68], [41799, 70], [42098, 72], [42398, 73], [42697, 75], [42996, 76], [43296, 77], [43595, 78], [43895, 79], [44194, 80], [44494, 80], [44794, 80], [45094, 80], [45394, 80], [45694, 80], [45994, 80], [46294, 80], [46594, 80], [46894, 80], [47194, 80], [47495, 78], [47795, 78], [48096, 76], [48397, 74], [48698, 72], [48999, 70], [49301, 67], [49602, 65], [49904, 61], [50206, 57], [50508, 53], [50811, 46], [51116, 35]], "point": [133, 149]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ButterKnife|+00.84|+00.66|+00.63", "placeStationary": true, "receptacleObjectId": "SideTable|+00.77|+00.70|+00.45"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [53, 105, 160, 237], "mask": [[31280, 19], [31309, 6], [31334, 24], [31578, 21], [31609, 6], [31635, 23], [31877, 22], [31909, 5], [31935, 24], [32178, 21], [32209, 5], [32235, 24], [32478, 22], [32508, 6], [32535, 24], [32778, 23], [32807, 7], [32835, 24], [33080, 22], [33106, 8], [33135, 24], [33380, 17], [33399, 16], [33435, 24], [33682, 13], [33700, 15], [33735, 24], [33971, 1], [33983, 12], [34000, 15], [34034, 25], [34283, 12], [34300, 16], [34334, 25], [34587, 8], [34601, 15], [34633, 26], [34870, 1], [34889, 6], [34901, 16], [34933, 26], [35170, 1], [35191, 3], [35202, 16], [35232, 27], [35491, 3], [35502, 17], [35530, 29], [35792, 2], [35803, 18], [35828, 31], [36093, 2], [36103, 56], [36393, 2], [36403, 56], [36693, 2], [36703, 56], [36993, 2], [37004, 20], [37025, 34], [37293, 2], [37304, 20], [37325, 34], [37593, 2], [37606, 15], [37622, 2], [37625, 34], [37893, 2], [37907, 14], [37922, 2], [37925, 34], [38192, 3], [38208, 13], [38222, 2], [38225, 34], [38492, 3], [38509, 12], [38523, 1], [38525, 34], [38792, 2], [38810, 11], [38823, 1], [38825, 34], [39066, 1], [39092, 2], [39110, 12], [39123, 1], [39125, 1], [39127, 32], [39365, 3], [39392, 1], [39411, 9], [39427, 33], [39665, 4], [39692, 1], [39711, 9], [39727, 33], [39964, 5], [39992, 1], [40011, 9], [40026, 34], [40264, 6], [40292, 2], [40311, 10], [40325, 35], [40564, 6], [40592, 2], [40611, 11], [40624, 36], [40863, 8], [40893, 1], [40911, 11], [40923, 37], [41163, 8], [41193, 1], [41211, 11], [41223, 37], [41463, 9], [41493, 2], [41510, 12], [41523, 37], [41762, 10], [41793, 3], [41810, 12], [41823, 37], [42062, 10], [42093, 4], [42109, 13], [42123, 37], [42362, 11], [42394, 4], [42409, 12], [42423, 37], [42661, 12], [42694, 6], [42707, 14], [42723, 37], [42961, 12], [42993, 28], [43023, 37], [43261, 13], [43293, 28], [43323, 37], [43560, 14], [43593, 28], [43623, 37], [43860, 15], [43892, 28], [43923, 37], [44160, 15], [44191, 3], [44223, 37], [44459, 17], [44490, 4], [44523, 37], [44759, 19], [44788, 5], [44823, 37], [45058, 22], [45086, 7], [45123, 37], [45358, 35], [45423, 37], [45658, 35], [45723, 37], [45957, 36], [46023, 37], [46257, 35], [46323, 37], [46557, 35], [46623, 37], [46856, 36], [46923, 38], [47156, 36], [47223, 38], [47456, 36], [47522, 39], [47755, 37], [47822, 39], [48055, 38], [48122, 39], [48355, 106], [48654, 107], [48954, 107], [49254, 107], [49553, 107], [49854, 106], [50154, 106], [50454, 12], [50472, 88], [50755, 10], [50772, 88], [51055, 9], [51072, 88], [51356, 8], [51371, 89], [51656, 8], [51671, 89], [51956, 8], [51971, 89], [52257, 8], [52271, 89], [52557, 8], [52570, 90], [52858, 7], [52870, 90], [53158, 8], [53170, 90], [53458, 8], [53469, 91], [53759, 8], [53769, 91], [54059, 8], [54069, 91], [54360, 7], [54369, 91], [54660, 100], [54960, 100], [55261, 99], [55561, 99], [55861, 99], [56162, 97], [56462, 97], [56763, 96], [57063, 96], [57363, 8], [57380, 51], [57437, 2], [57448, 11], [57664, 7], [57680, 51], [57737, 2], [57748, 11], [57964, 8], [57980, 51], [58037, 1], [58048, 11], [58265, 7], [58280, 51], [58337, 1], [58349, 10], [58565, 7], [58580, 51], [58637, 1], [58648, 11], [58865, 8], [58879, 52], [58936, 2], [58948, 11], [59166, 7], [59179, 52], [59236, 2], [59248, 11], [59466, 7], [59479, 52], [59536, 3], [59548, 11], [59767, 7], [59779, 54], [59834, 7], [59842, 1], [59848, 11], [60067, 7], [60078, 81], [60367, 8], [60378, 81], [60668, 7], [60678, 81], [60968, 7], [60978, 81], [61269, 7], [61277, 82], [61569, 7], [61577, 82], [61869, 7], [61877, 82], [62170, 89], [62470, 89], [62771, 88], [63071, 88], [63371, 87], [63672, 86], [63972, 86], [64273, 85], [64573, 7], [64654, 4], [64873, 7], [64954, 4], [65174, 6], [65254, 4], [65474, 7], [65554, 4], [65775, 6], [65854, 4], [66075, 6], [66154, 4], [66375, 7], [66454, 4], [66676, 6], [66754, 4], [66976, 6], [67054, 4], [67277, 6], [67354, 4], [67577, 6], [67654, 4], [67877, 7], [67954, 4], [68178, 6], [68254, 4], [68478, 6], [68554, 4], [68779, 6], [68854, 4], [69079, 6], [69154, 4], [69379, 6], [69454, 4], [69680, 4], [69754, 4], [69980, 4], [70054, 4], [70281, 3], [70354, 3], [70584, 1], [70883, 2]], "point": [106, 170]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-01.72|+00.75|-01.31|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [125, 129, 133, 171], "mask": [[38530, 3], [38826, 7], [39126, 8], [39426, 8], [39726, 8], [40026, 8], [40326, 7], [40626, 7], [40925, 8], [41225, 8], [41525, 8], [41825, 8], [42125, 8], [42425, 8], [42725, 8], [43025, 8], [43325, 8], [43625, 8], [43925, 8], [44225, 8], [44525, 8], [44825, 8], [45125, 8], [45425, 8], [45725, 8], [46025, 8], [46325, 8], [46625, 8], [46925, 8], [47225, 8], [47525, 8], [47825, 8], [48125, 8], [48425, 8], [48725, 8], [49025, 8], [49325, 8], [49625, 8], [49925, 8], [50225, 8], [50526, 7], [50826, 7], [51126, 7]], "point": [129, 149]}}, "high_idx": 7}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-01.72|+00.75|-01.31|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 42, 242, 253], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25861, 181], [26160, 181], [26459, 182], [26759, 182], [27059, 182], [27358, 182], [27658, 182], [27957, 183], [28257, 182], [28556, 183], [28854, 185], [29154, 64], [29237, 102], [29453, 65], [29537, 101], [29752, 66], [29837, 101], [30052, 67], [30137, 101], [30351, 68], [30437, 101], [30651, 68], [30737, 101], [30950, 69], [31037, 101], [31250, 69], [31337, 101], [31550, 69], [31637, 101], [31849, 70], [31937, 101], [32149, 71], [32237, 101], [32448, 72], [32537, 101], [32748, 72], [32837, 102], [33047, 73], [33137, 102], [33347, 73], [33437, 102], [33646, 75], [33736, 103], [33946, 75], [34035, 103], [34245, 77], [34334, 104], [34545, 80], [34632, 106], [34844, 194], [35144, 194], [35443, 194], [35743, 194], [36043, 194], [36342, 195], [36642, 194], [36941, 195], [37241, 195], [37540, 195], [37840, 195], [38139, 196], [38439, 195], [38738, 196], [39038, 196], [39337, 197], [39637, 196], [39936, 197], [40236, 197], [40536, 196], [40835, 197], [41135, 197], [41434, 197], [41734, 197], [42033, 198], [42333, 198], [42632, 198], [42932, 198], [43231, 199], [43531, 198], [43830, 199], [44130, 199], [44429, 199], [44729, 199], [45029, 199], [45328, 200], [45628, 199], [45927, 200], [46227, 195], [46526, 197], [46826, 196], [47125, 197], [47425, 197], [47724, 60], [47785, 136], [48024, 60], [48085, 136], [48323, 61], [48385, 136], [48623, 61], [48922, 61], [49222, 61], [49522, 61], [49821, 62], [50121, 61], [50420, 62], [50720, 62], [51019, 63], [51319, 62], [51618, 63], [51918, 63], [52217, 64], [52517, 63], [52816, 64], [53116, 64], [53415, 65], [53715, 64], [54015, 64], [54314, 65], [54614, 65], [54913, 66], [55213, 65], [55512, 66], [55812, 66], [56111, 67], [56411, 66], [56710, 67], [57000, 3], [57010, 67], [57300, 3], [57309, 68], [57600, 3], [57609, 67], [57900, 4], [57908, 68], [58200, 76], [58500, 76], [58800, 75], [59100, 75], [59400, 75], [59700, 75], [60000, 74], [60300, 74], [60600, 74], [60901, 73], [61203, 70], [61503, 70], [61802, 71], [62102, 71], [62400, 1], [62402, 70], [62700, 72], [63000, 72], [63300, 72], [63600, 71], [63900, 71], [64200, 71], [64500, 71], [64800, 70], [65100, 70], [65400, 70], [65700, 70], [66000, 69], [66300, 69], [66600, 69], [66900, 69], [67200, 69], [67500, 68], [67800, 68], [68101, 67], [68402, 66], [68703, 64], [69004, 63], [69304, 63], [69605, 62], [69906, 60], [70207, 59], [70508, 58], [70809, 57], [71109, 56], [71410, 55], [71711, 54], [72012, 53], [72313, 51], [72613, 51], [72914, 50], [73215, 49], [73517, 46], [73819, 44], [74123, 42], [74427, 8], [74436, 30], [74730, 3], [74739, 28], [75043, 19], [75063, 3], [75345, 17], [75646, 16]], "point": [121, 146]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 42, 242, 253], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25861, 181], [26160, 181], [26459, 182], [26759, 182], [27059, 182], [27358, 182], [27658, 182], [27957, 183], [28257, 182], [28556, 183], [28854, 185], [29154, 64], [29237, 102], [29453, 65], [29537, 101], [29752, 66], [29837, 101], [30052, 67], [30137, 101], [30351, 68], [30437, 101], [30651, 68], [30737, 101], [30950, 69], [31037, 101], [31250, 69], [31337, 101], [31550, 69], [31637, 101], [31849, 70], [31937, 101], [32149, 71], [32237, 101], [32448, 72], [32537, 101], [32748, 72], [32837, 102], [33047, 73], [33137, 102], [33347, 73], [33437, 102], [33646, 75], [33736, 7], [33759, 80], [33946, 75], [34035, 4], [34063, 75], [34245, 77], [34334, 2], [34364, 74], [34545, 80], [34632, 3], [34665, 73], [34844, 90], [34966, 72], [35144, 90], [35267, 71], [35443, 90], [35568, 69], [35743, 90], [35868, 69], [36043, 90], [36168, 69], [36342, 90], [36468, 69], [36642, 90], [36769, 67], [36941, 91], [37069, 67], [37241, 91], [37369, 67], [37540, 92], [37669, 66], [37840, 92], [37969, 66], [38139, 93], [38269, 66], [38439, 93], [38569, 65], [38738, 95], [38868, 66], [39038, 95], [39168, 66], [39337, 97], [39467, 67], [39637, 98], [39766, 67], [39936, 101], [40065, 68], [40236, 197], [40536, 196], [40835, 197], [41135, 197], [41434, 197], [41734, 197], [42033, 198], [42333, 198], [42632, 198], [42932, 198], [43231, 199], [43531, 198], [43830, 199], [44130, 199], [44429, 199], [44729, 199], [45029, 199], [45328, 200], [45628, 199], [45927, 200], [46227, 195], [46526, 197], [46826, 196], [47125, 197], [47425, 197], [47724, 60], [47785, 136], [48024, 60], [48085, 136], [48323, 61], [48385, 136], [48623, 61], [48922, 61], [49222, 61], [49522, 61], [49821, 62], [50121, 61], [50420, 62], [50720, 62], [51019, 63], [51319, 62], [51618, 63], [51918, 63], [52217, 64], [52517, 63], [52816, 64], [53116, 64], [53415, 65], [53715, 64], [54015, 64], [54314, 65], [54614, 65], [54913, 66], [55213, 65], [55512, 66], [55812, 66], [56111, 67], [56411, 66], [56710, 67], [57000, 3], [57010, 67], [57300, 3], [57309, 68], [57600, 3], [57609, 67], [57900, 4], [57908, 68], [58200, 76], [58500, 76], [58800, 75], [59100, 75], [59400, 75], [59700, 75], [60000, 74], [60300, 74], [60600, 74], [60901, 73], [61203, 70], [61503, 70], [61802, 71], [62102, 71], [62400, 1], [62402, 70], [62700, 72], [63000, 72], [63300, 72], [63600, 71], [63900, 71], [64200, 71], [64500, 71], [64800, 70], [65100, 70], [65400, 70], [65700, 70], [66000, 69], [66300, 69], [66600, 69], [66900, 69], [67200, 69], [67500, 68], [67800, 68], [68101, 67], [68402, 66], [68703, 64], [69004, 63], [69304, 63], [69605, 62], [69906, 60], [70207, 59], [70508, 58], [70809, 57], [71109, 56], [71410, 55], [71711, 54], [72012, 53], [72313, 51], [72613, 51], [72914, 50], [73215, 49], [73517, 46], [73819, 44], [74123, 42], [74427, 8], [74436, 30], [74730, 3], [74739, 28], [75043, 19], [75063, 3], [75345, 17], [75646, 16]], "point": [121, 146]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-8.052, -8.052, -14.756, -14.756, 2.761099816, 2.761099816]], "forceVisible": true, "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-8.052, -8.052, -14.756, -14.756, 2.761099816, 2.761099816]], "forceVisible": true, "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-01.72|+00.75|-01.31|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [132, 113, 168, 134], "mask": [[33743, 16], [34039, 24], [34336, 28], [34635, 30], [34934, 32], [35234, 33], [35533, 35], [35833, 35], [36133, 35], [36432, 36], [36732, 37], [37032, 37], [37332, 37], [37632, 37], [37932, 37], [38232, 37], [38532, 37], [38833, 35], [39133, 35], [39434, 33], [39735, 31], [40037, 28]], "point": [150, 122]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 42, 242, 253], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25861, 181], [26160, 181], [26459, 182], [26759, 182], [27059, 182], [27358, 182], [27658, 182], [27957, 183], [28257, 182], [28556, 183], [28854, 185], [29154, 64], [29237, 102], [29453, 65], [29537, 101], [29752, 66], [29837, 101], [30052, 67], [30137, 101], [30351, 68], [30437, 101], [30651, 68], [30737, 101], [30950, 69], [31037, 101], [31250, 69], [31337, 101], [31550, 69], [31637, 101], [31849, 70], [31937, 101], [32149, 71], [32237, 101], [32448, 72], [32537, 101], [32748, 72], [32837, 102], [33047, 73], [33137, 102], [33347, 73], [33437, 102], [33646, 75], [33736, 103], [33946, 75], [34035, 103], [34245, 77], [34334, 104], [34545, 80], [34632, 106], [34844, 194], [35144, 194], [35443, 194], [35743, 194], [36043, 194], [36342, 195], [36642, 194], [36941, 195], [37241, 195], [37540, 195], [37840, 195], [38139, 196], [38439, 195], [38738, 196], [39038, 196], [39337, 197], [39637, 196], [39936, 197], [40236, 197], [40536, 196], [40835, 197], [41135, 197], [41434, 197], [41734, 197], [42033, 198], [42333, 198], [42632, 198], [42932, 198], [43231, 199], [43531, 198], [43830, 199], [44130, 199], [44429, 199], [44729, 199], [45029, 199], [45328, 200], [45628, 199], [45927, 200], [46227, 195], [46526, 197], [46826, 196], [47125, 197], [47425, 197], [47724, 60], [47785, 136], [48024, 60], [48085, 136], [48323, 61], [48385, 136], [48623, 61], [48922, 61], [49222, 61], [49522, 61], [49821, 62], [50121, 61], [50420, 62], [50720, 62], [51019, 63], [51319, 62], [51618, 63], [51918, 63], [52217, 64], [52517, 63], [52816, 64], [53116, 64], [53415, 65], [53715, 64], [54015, 64], [54314, 65], [54614, 65], [54913, 66], [55213, 65], [55512, 66], [55812, 66], [56111, 67], [56411, 66], [56710, 67], [57000, 3], [57010, 67], [57300, 3], [57309, 68], [57600, 3], [57609, 67], [57900, 4], [57908, 68], [58200, 76], [58500, 76], [58800, 75], [59100, 75], [59400, 75], [59700, 75], [60000, 74], [60300, 74], [60600, 74], [60901, 73], [61203, 70], [61503, 70], [61802, 71], [62102, 71], [62400, 1], [62402, 70], [62700, 72], [63000, 72], [63300, 72], [63600, 71], [63900, 71], [64200, 71], [64500, 71], [64800, 70], [65100, 70], [65400, 70], [65700, 70], [66000, 69], [66300, 69], [66600, 69], [66900, 69], [67200, 69], [67500, 68], [67800, 68], [68101, 67], [68402, 66], [68703, 64], [69004, 63], [69304, 63], [69605, 62], [69906, 60], [70207, 59], [70508, 58], [70809, 57], [71109, 56], [71410, 55], [71711, 54], [72012, 53], [72313, 51], [72613, 51], [72914, 50], [73215, 49], [73517, 46], [73819, 44], [74123, 42], [74427, 8], [74436, 30], [74730, 3], [74739, 28], [75043, 19], [75063, 3], [75345, 17], [75646, 16]], "point": [121, 146]}}, "high_idx": 9}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-01.72|+00.75|-01.31|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "SideTable|+00.77|+00.70|+00.45"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [53, 105, 160, 237], "mask": [[31280, 19], [31309, 6], [31334, 24], [31578, 21], [31609, 6], [31635, 23], [31877, 22], [31909, 5], [31935, 24], [32178, 21], [32209, 5], [32235, 24], [32478, 22], [32508, 6], [32535, 24], [32778, 23], [32807, 7], [32835, 24], [33080, 22], [33106, 8], [33135, 24], [33380, 17], [33399, 16], [33435, 24], [33682, 13], [33700, 15], [33735, 24], [33971, 1], [33983, 12], [34000, 15], [34034, 25], [34283, 12], [34300, 16], [34334, 25], [34587, 8], [34601, 15], [34633, 26], [34870, 1], [34889, 6], [34901, 16], [34933, 26], [35170, 1], [35191, 3], [35202, 16], [35232, 27], [35491, 3], [35502, 17], [35530, 29], [35792, 2], [35803, 18], [35828, 31], [36093, 2], [36103, 43], [36148, 11], [36393, 2], [36403, 43], [36449, 10], [36693, 2], [36703, 43], [36749, 10], [36993, 2], [37004, 20], [37025, 21], [37050, 9], [37293, 2], [37304, 20], [37325, 21], [37350, 9], [37593, 2], [37606, 15], [37622, 2], [37625, 21], [37650, 9], [37893, 2], [37907, 14], [37922, 2], [37925, 21], [37951, 8], [38192, 3], [38208, 13], [38222, 2], [38225, 21], [38251, 8], [38492, 3], [38509, 12], [38523, 1], [38525, 21], [38550, 9], [38792, 2], [38810, 11], [38823, 1], [38825, 21], [38850, 9], [39066, 1], [39092, 2], [39110, 12], [39123, 1], [39125, 1], [39127, 19], [39150, 9], [39365, 3], [39392, 1], [39411, 9], [39427, 19], [39450, 10], [39665, 4], [39692, 1], [39711, 9], [39727, 19], [39750, 10], [39964, 5], [39992, 1], [40011, 9], [40026, 20], [40049, 11], [40264, 6], [40292, 2], [40311, 10], [40325, 21], [40349, 11], [40564, 6], [40592, 2], [40611, 11], [40624, 22], [40649, 11], [40863, 8], [40893, 1], [40911, 11], [40923, 23], [40949, 11], [41163, 8], [41193, 1], [41211, 11], [41223, 23], [41249, 11], [41463, 9], [41493, 2], [41510, 12], [41523, 23], [41548, 12], [41762, 10], [41793, 3], [41810, 12], [41823, 23], [41848, 12], [42062, 10], [42093, 4], [42109, 13], [42123, 23], [42148, 12], [42362, 11], [42394, 4], [42409, 12], [42423, 23], [42448, 12], [42661, 12], [42694, 6], [42707, 14], [42723, 22], [42748, 12], [42961, 12], [42993, 28], [43023, 22], [43048, 12], [43261, 13], [43293, 28], [43323, 22], [43348, 12], [43560, 14], [43593, 28], [43623, 22], [43649, 11], [43860, 15], [43892, 28], [43923, 22], [43949, 11], [44160, 15], [44191, 3], [44223, 22], [44249, 11], [44459, 17], [44490, 4], [44523, 22], [44549, 11], [44759, 19], [44788, 5], [44823, 22], [44849, 11], [45058, 22], [45086, 7], [45123, 21], [45149, 11], [45358, 35], [45423, 21], [45449, 11], [45658, 35], [45723, 21], [45749, 11], [45957, 36], [46023, 21], [46049, 11], [46257, 35], [46323, 22], [46349, 11], [46557, 35], [46623, 22], [46648, 12], [46856, 36], [46923, 38], [47156, 36], [47223, 38], [47456, 36], [47522, 39], [47755, 37], [47822, 39], [48055, 38], [48122, 39], [48355, 106], [48654, 107], [48954, 107], [49254, 107], [49553, 107], [49854, 106], [50154, 106], [50454, 12], [50472, 88], [50755, 10], [50772, 88], [51055, 9], [51072, 88], [51356, 8], [51371, 89], [51656, 8], [51671, 89], [51956, 8], [51971, 89], [52257, 8], [52271, 89], [52557, 8], [52570, 90], [52858, 7], [52870, 90], [53158, 8], [53170, 90], [53458, 8], [53469, 91], [53759, 8], [53769, 91], [54059, 8], [54069, 91], [54360, 7], [54369, 91], [54660, 100], [54960, 100], [55261, 99], [55561, 99], [55861, 99], [56162, 97], [56462, 97], [56763, 96], [57063, 96], [57363, 8], [57380, 51], [57437, 2], [57448, 11], [57664, 7], [57680, 51], [57737, 2], [57748, 11], [57964, 8], [57980, 51], [58037, 1], [58048, 11], [58265, 7], [58280, 51], [58337, 1], [58349, 10], [58565, 7], [58580, 51], [58637, 1], [58648, 11], [58865, 8], [58879, 52], [58936, 2], [58948, 11], [59166, 7], [59179, 52], [59236, 2], [59248, 11], [59466, 7], [59479, 52], [59536, 3], [59548, 11], [59767, 7], [59779, 54], [59834, 7], [59842, 1], [59848, 11], [60067, 7], [60078, 81], [60367, 8], [60378, 81], [60668, 7], [60678, 81], [60968, 7], [60978, 81], [61269, 7], [61277, 82], [61569, 7], [61577, 82], [61869, 7], [61877, 82], [62170, 89], [62470, 89], [62771, 88], [63071, 88], [63371, 87], [63672, 86], [63972, 86], [64273, 85], [64573, 7], [64654, 4], [64873, 7], [64954, 4], [65174, 6], [65254, 4], [65474, 7], [65554, 4], [65775, 6], [65854, 4], [66075, 6], [66154, 4], [66375, 7], [66454, 4], [66676, 6], [66754, 4], [66976, 6], [67054, 4], [67277, 6], [67354, 4], [67577, 6], [67654, 4], [67877, 7], [67954, 4], [68178, 6], [68254, 4], [68478, 6], [68554, 4], [68779, 6], [68854, 4], [69079, 6], [69154, 4], [69379, 6], [69454, 4], [69680, 4], [69754, 4], [69980, 4], [70054, 4], [70281, 3], [70354, 3], [70584, 1], [70883, 2]], "point": [106, 170]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan21", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -0.75, "y": 0.869696259, "z": -0.75}, "object_poses": [{"objectName": "Potato_027e0a30", "position": {"x": -1.63595831, "y": 0.734842539, "z": -1.52409041}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_027e0a30", "position": {"x": 0.746874452, "y": 1.40768075, "z": -1.95800006}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "DishSponge_11e4138d", "position": {"x": 0.6015449, "y": 0.664960563, "z": 0.4481}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_11e4138d", "position": {"x": 0.62322855, "y": 0.759432137, "z": 0.00290554762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_0ad5c562", "position": {"x": 0.841818154, "y": 0.6646421, "z": 0.630601943}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_93a81026", "position": {"x": 0.8176422, "y": 0.7609973, "z": 0.00290554762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_34f12e87", "position": {"x": 0.7573, "y": 0.7731047, "z": -0.680573344}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Fork_34f12e87", "position": {"x": 0.721681535, "y": 0.665148556, "z": 0.387266}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_cde1951c", "position": {"x": -2.097, "y": 0.9306, "z": 0.785}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_c79ff911", "position": {"x": 0.9619548, "y": 0.701205254, "z": 0.508934}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_c79ff911", "position": {"x": 0.7994509, "y": 0.8091614, "z": -0.7906663}, "rotation": {"x": 7.58303337e-22, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Cup_cb0de209", "position": {"x": 0.8244617, "y": 1.37325728, "z": -2.11325026}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Cup_cb0de209", "position": {"x": -2.05416274, "y": 0.700419068, "z": -0.875647962}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_33d4fba0", "position": {"x": 1.070188, "y": 0.906680465, "z": -0.5358008}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_5a2cfc73", "position": {"x": -1.59924114, "y": 0.744963467, "z": -3.27692056}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_5a2cfc73", "position": {"x": -1.59924114, "y": 0.744963467, "z": -3.78265572}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_d7803c74", "position": {"x": 0.6616132, "y": 0.6646764, "z": 0.5697679}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_d7803c74", "position": {"x": -0.2527333, "y": 0.8999009, "z": 0.7581701}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "PepperShaker_87875872", "position": {"x": 0.8983273, "y": 0.01202476, "z": -0.702639937}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_87875872", "position": {"x": 0.6216665, "y": 0.01202476, "z": -0.7680024}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_886fcaf3", "position": {"x": -2.05416274, "y": 0.6980955, "z": -1.19986916}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_42314d78", "position": {"x": -1.71959925, "y": 0.7452519, "z": -1.30794287}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_42314d78", "position": {"x": 0.149083376, "y": 0.9440833, "z": 1.1012435}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Mug_288ff1e3", "position": {"x": 0.9949719, "y": 1.4588306, "z": 0.5966235}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_093f2b24", "position": {"x": -1.33408773, "y": 0.10573864, "z": -3.57439852}, "rotation": {"x": 0.0, "y": 269.999878, "z": 0.0}}, {"objectName": "Pot_cde1951c", "position": {"x": -1.555, "y": 0.9306, "z": 1.049}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_2b33f66e", "position": {"x": 0.6769987, "y": 0.019684732, "z": -0.119339228}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_34f12e87", "position": {"x": 0.7690388, "y": 0.7611036, "z": -0.1249657}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_d7803c74", "position": {"x": -0.654548049, "y": 0.8999009, "z": 0.7581724}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Apple_093f2b24", "position": {"x": 0.781749845, "y": 0.71809, "z": 0.387266}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_63693e0a", "position": {"x": 0.746874, "y": 1.1575557, "z": -2.11325}, "rotation": {"x": 0.0, "y": 180.000153, "z": 0.0}}, {"objectName": "Egg_c79ff911", "position": {"x": -1.22445631, "y": 0.0888538957, "z": -3.57439828}, "rotation": {"x": 0.0, "y": 269.999878, "z": 0.0}}, {"objectName": "Spatula_8142ce7b", "position": {"x": -0.05182302, "y": 0.9156206, "z": 1.27278233}, "rotation": {"x": 0.0, "y": 0.000327849062, "z": 0.0}}, {"objectName": "Potato_027e0a30", "position": {"x": 0.5821031, "y": 0.916653156, "z": -0.176930517}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_cb0de209", "position": {"x": 0.7151491, "y": 0.7711859, "z": -0.7356198}, "rotation": {"x": 7.58303337e-22, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "SaltShaker_886fcaf3", "position": {"x": 1.04054928, "y": 1.46013677, "z": -0.207824767}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_87875872", "position": {"x": 0.781749845, "y": 0.6609062, "z": 0.508934}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_0ad5c562", "position": {"x": 0.9509703, "y": 0.8836421, "z": -0.10800001}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_11e4138d", "position": {"x": 0.67299825, "y": 0.7721204, "z": -0.5704804}, "rotation": {"x": 7.58303337e-22, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Bread_42314d78", "position": {"x": -0.252732337, "y": 0.9440833, "z": 0.929707944}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Lettuce_0ad3089c", "position": {"x": -1.08536816, "y": 0.973258257, "z": 0.955294549}, "rotation": {"x": 41.3596764, "y": 2.45236778, "z": 4.27747965}}, {"objectName": "Tomato_5a2cfc73", "position": {"x": 0.901886463, "y": 0.712988734, "z": 0.387266}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_93a81026", "position": {"x": -1.97052181, "y": 0.702918768, "z": -1.41601658}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_33d4fba0", "position": {"x": -0.855455, "y": 0.923701346, "z": 0.8439424}, "rotation": {"x": 0.0, "y": 0.000327849062, "z": 0.0}}, {"objectName": "Mug_288ff1e3", "position": {"x": -1.89628434, "y": 0.7945044, "z": -3.77127218}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_737b77b7", "position": {"x": -0.855453849, "y": 0.899054945, "z": 1.01548016}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}], "object_toggles": [], "random_seed": 2185452267, "scene_num": 21}, "task_id": "trial_T20190907_211124_867772", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A30M9SZYJKFDS9_3COPXFW7XETUKA309JVVHCD60CUKP5", "high_descs": ["turn to the right go left ", "pick up the knife on the black table", "turn to the right go left across the room", "slice the loaf of bread", "turn to the right go right", "leave the knife on the black table", "turn to the right , go right", "pick up a slice of bread", "turn to the left face the microwave ", "put the slice of bread inside the microwave get it back out", "turn to the left go left go right", "face the black table put the slice of bread on the black table "], "task_desc": "slice the loaf of bread take couple of slices heat them up put them on the black table", "votes": [1, 1]}, {"assignment_id": "AO33H4GL9KZX9_3DYGAII7POPGVD1VMOFSDYPFZHYQPD", "high_descs": ["Move left to the small black table next to the sink.", "Pick up the gray knife behind the hand soap.", "Take the knife to the white table with a wooden chair.", "Slice a half the bread on the table. ", "Turn around and go back to the small black table.", "Place the knife back on the table. ", "Turn around and go back to the white table with sliced bread.", "Pick up one slice of bread from the table.", "To left to the front of the microwave.", "Heat the slice of bread in the microwave and take it out.", "Go back across the room to the small black table next to the sink. ", "Place the heated bread between fork and the knife on the table. "], "task_desc": "Put a heated slice of bread on the table.. ", "votes": [1, 1]}, {"assignment_id": "A2KAGFQU28JY43_3K9FOBBF2K051V95E4U4SNLQPWDLNQ", "high_descs": ["Turn to your left and walk to the white table against the wall, then turn right and walk to the small black table between the kitchen counter and the garbage bag in the corner. ", "Pick up the knife on the far left edge of the small black table to the left of the kitchen counter. ", "Turn around and walk to the white table with the wooden chair to the left of it. ", "Cut the bread on the white table into slices. ", "Turn around and walk back to the black table to the left of the kitchen counter. ", "Put the knife on the edge of the black table, closest to the kitchen counter. ", "Turn around and walk back to the white table with the sliced bread on it. ", "Pick up a slice of bread from the middle of the loaf. ", "Turn to your left and go to the microwave. ", "Place the slice of bread into the microwave, heat it up, then remove the slice of bread from the microwave. ", "Turn around and walk towards the stove and turn right once you reach the stove, then walk back to the black table to the left of the kitchen counter. ", "Place the slice of bread on the black table, between the fork and the knife, on the right side."], "task_desc": "Put a warm slice of bread on the black table.", "votes": [1, 1]}]}}