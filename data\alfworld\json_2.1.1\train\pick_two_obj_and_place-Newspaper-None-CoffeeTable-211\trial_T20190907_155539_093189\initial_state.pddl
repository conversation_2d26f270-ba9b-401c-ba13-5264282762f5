
(define (problem plan_trial_T20190907_155539_093189)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Box_bar__minus_00_dot_22_bar__plus_00_dot_12_bar__minus_02_dot_04 - object
        CellPhone_bar__plus_00_dot_17_bar__plus_00_dot_34_bar__plus_00_dot_14 - object
        CellPhone_bar__plus_03_dot_58_bar__plus_00_dot_64_bar__minus_00_dot_62 - object
        CellPhone_bar__minus_01_dot_74_bar__plus_00_dot_35_bar__plus_00_dot_73 - object
        CreditCard_bar__plus_01_dot_75_bar__plus_00_dot_62_bar__minus_00_dot_73 - object
        CreditCard_bar__minus_01_dot_70_bar__plus_00_dot_29_bar__minus_01_dot_45 - object
        FloorLamp_bar__plus_01_dot_69_bar__plus_00_dot_00_bar__plus_01_dot_78 - object
        HousePlant_bar__minus_00_dot_81_bar__plus_00_dot_00_bar__minus_02_dot_19 - object
        KeyChain_bar__plus_00_dot_25_bar__plus_00_dot_34_bar__plus_00_dot_27 - object
        KeyChain_bar__plus_01_dot_60_bar__plus_00_dot_48_bar__plus_00_dot_98 - object
        Laptop_bar__plus_03_dot_38_bar__plus_00_dot_64_bar__minus_00_dot_98 - object
        LightSwitch_bar__plus_02_dot_10_bar__plus_01_dot_35_bar__minus_00_dot_83 - object
        Newspaper_bar__plus_01_dot_42_bar__plus_00_dot_48_bar__plus_00_dot_98 - object
        Newspaper_bar__minus_00_dot_29_bar__plus_00_dot_55_bar__plus_01_dot_93 - object
        Newspaper_bar__minus_01_dot_81_bar__plus_00_dot_60_bar__plus_00_dot_74 - object
        Newspaper_bar__minus_01_dot_97_bar__plus_00_dot_59_bar__plus_00_dot_03 - object
        Painting_bar__plus_01_dot_98_bar__plus_01_dot_59_bar__plus_00_dot_33 - object
        Pillow_bar__plus_01_dot_44_bar__plus_00_dot_52_bar__minus_00_dot_13 - object
        Plate_bar__plus_00_dot_00_bar__plus_00_dot_34_bar__plus_00_dot_52 - object
        Plate_bar__plus_01_dot_69_bar__plus_00_dot_71_bar__minus_00_dot_71 - object
        Plate_bar__minus_01_dot_82_bar__plus_00_dot_10_bar__plus_00_dot_40 - object
        RemoteControl_bar__minus_01_dot_73_bar__plus_00_dot_60_bar__plus_00_dot_39 - object
        Statue_bar__plus_03_dot_69_bar__plus_00_dot_65_bar__minus_00_dot_80 - object
        Statue_bar__minus_01_dot_81_bar__plus_00_dot_60_bar__plus_00_dot_21 - object
        Statue_bar__minus_02_dot_04_bar__plus_00_dot_61_bar__plus_01_dot_10 - object
        Television_bar__minus_02_dot_03_bar__plus_01_dot_08_bar__plus_00_dot_56 - object
        Vase_bar__plus_00_dot_25_bar__plus_00_dot_34_bar__plus_00_dot_78 - object
        Vase_bar__plus_00_dot_33_bar__plus_00_dot_34_bar__plus_00_dot_02 - object
        Vase_bar__plus_00_dot_33_bar__plus_00_dot_34_bar__plus_00_dot_52 - object
        Window_bar__plus_02_dot_78_bar__plus_01_dot_28_bar__minus_02_dot_02 - object
        Window_bar__minus_00_dot_01_bar__plus_01_dot_29_bar__minus_02_dot_45 - object
        ArmChair_bar__minus_01_dot_76_bar__plus_00_dot_00_bar__minus_01_dot_49 - receptacle
        CoffeeTable_bar__plus_00_dot_17_bar_00_dot_00_bar__plus_00_dot_42 - receptacle
        Drawer_bar__plus_01_dot_52_bar__plus_00_dot_62_bar__minus_00_dot_76 - receptacle
        GarbageCan_bar__minus_01_dot_89_bar__plus_00_dot_00_bar__minus_00_dot_41 - receptacle
        Shelf_bar__minus_00_dot_20_bar__plus_00_dot_16_bar__plus_01_dot_93 - receptacle
        Shelf_bar__minus_00_dot_20_bar__plus_00_dot_54_bar__plus_01_dot_93 - receptacle
        Shelf_bar__minus_00_dot_20_bar__plus_00_dot_87_bar__plus_01_dot_93 - receptacle
        Shelf_bar__minus_01_dot_82_bar__plus_00_dot_38_bar__plus_00_dot_56 - receptacle
        Shelf_bar__minus_01_dot_83_bar__plus_00_dot_13_bar__plus_00_dot_56 - receptacle
        SideTable_bar__plus_01_dot_52_bar__plus_00_dot_00_bar__minus_00_dot_70 - receptacle
        SideTable_bar__plus_03_dot_58_bar__plus_00_dot_00_bar__minus_01_dot_17 - receptacle
        Sofa_bar__plus_01_dot_42_bar_00_dot_00_bar__plus_00_dot_39 - receptacle
        TVStand_bar__minus_01_dot_97_bar_00_dot_00_bar__plus_00_dot_56 - receptacle
        loc_bar__minus_2_bar_2_bar_3_bar_45 - location
        loc_bar__minus_4_bar_2_bar_3_bar_60 - location
        loc_bar_3_bar_2_bar_1_bar_60 - location
        loc_bar__minus_6_bar__minus_3_bar_3_bar_60 - location
        loc_bar_0_bar__minus_7_bar_2_bar_30 - location
        loc_bar__minus_3_bar__minus_6_bar_2_bar_60 - location
        loc_bar__minus_3_bar_3_bar_0_bar_45 - location
        loc_bar__minus_3_bar_3_bar_0_bar_30 - location
        loc_bar_5_bar__minus_6_bar_0_bar_60 - location
        loc_bar__minus_1_bar_6_bar_0_bar_60 - location
        loc_bar_4_bar_7_bar_1_bar_60 - location
        loc_bar_11_bar_0_bar_3_bar_0 - location
        loc_bar_10_bar__minus_5_bar_1_bar_60 - location
        loc_bar_11_bar__minus_3_bar_3_bar_30 - location
        loc_bar_11_bar__minus_6_bar_2_bar_30 - location
        loc_bar__minus_3_bar_2_bar_3_bar_60 - location
        loc_bar_0_bar__minus_7_bar_2_bar_60 - location
        loc_bar__minus_2_bar__minus_4_bar_3_bar_60 - location
        loc_bar__minus_4_bar_2_bar_1_bar_60 - location
        loc_bar_4_bar__minus_5_bar_2_bar_30 - location
        )
    

(:init


        (receptacleType Shelf_bar__minus_01_dot_83_bar__plus_00_dot_13_bar__plus_00_dot_56 ShelfType)
        (receptacleType SideTable_bar__plus_01_dot_52_bar__plus_00_dot_00_bar__minus_00_dot_70 SideTableType)
        (receptacleType ArmChair_bar__minus_01_dot_76_bar__plus_00_dot_00_bar__minus_01_dot_49 ArmChairType)
        (receptacleType Shelf_bar__minus_00_dot_20_bar__plus_00_dot_87_bar__plus_01_dot_93 ShelfType)
        (receptacleType Shelf_bar__minus_00_dot_20_bar__plus_00_dot_54_bar__plus_01_dot_93 ShelfType)
        (receptacleType SideTable_bar__plus_03_dot_58_bar__plus_00_dot_00_bar__minus_01_dot_17 SideTableType)
        (receptacleType Shelf_bar__minus_01_dot_82_bar__plus_00_dot_38_bar__plus_00_dot_56 ShelfType)
        (receptacleType Drawer_bar__plus_01_dot_52_bar__plus_00_dot_62_bar__minus_00_dot_76 DrawerType)
        (receptacleType Sofa_bar__plus_01_dot_42_bar_00_dot_00_bar__plus_00_dot_39 SofaType)
        (receptacleType GarbageCan_bar__minus_01_dot_89_bar__plus_00_dot_00_bar__minus_00_dot_41 GarbageCanType)
        (receptacleType Shelf_bar__minus_00_dot_20_bar__plus_00_dot_16_bar__plus_01_dot_93 ShelfType)
        (receptacleType CoffeeTable_bar__plus_00_dot_17_bar_00_dot_00_bar__plus_00_dot_42 CoffeeTableType)
        (receptacleType TVStand_bar__minus_01_dot_97_bar_00_dot_00_bar__plus_00_dot_56 TVStandType)
        (objectType Laptop_bar__plus_03_dot_38_bar__plus_00_dot_64_bar__minus_00_dot_98 LaptopType)
        (objectType Painting_bar__plus_01_dot_98_bar__plus_01_dot_59_bar__plus_00_dot_33 PaintingType)
        (objectType RemoteControl_bar__minus_01_dot_73_bar__plus_00_dot_60_bar__plus_00_dot_39 RemoteControlType)
        (objectType KeyChain_bar__plus_00_dot_25_bar__plus_00_dot_34_bar__plus_00_dot_27 KeyChainType)
        (objectType Plate_bar__plus_00_dot_00_bar__plus_00_dot_34_bar__plus_00_dot_52 PlateType)
        (objectType Newspaper_bar__minus_00_dot_29_bar__plus_00_dot_55_bar__plus_01_dot_93 NewspaperType)
        (objectType Window_bar__plus_02_dot_78_bar__plus_01_dot_28_bar__minus_02_dot_02 WindowType)
        (objectType Plate_bar__minus_01_dot_82_bar__plus_00_dot_10_bar__plus_00_dot_40 PlateType)
        (objectType FloorLamp_bar__plus_01_dot_69_bar__plus_00_dot_00_bar__plus_01_dot_78 FloorLampType)
        (objectType Newspaper_bar__minus_01_dot_81_bar__plus_00_dot_60_bar__plus_00_dot_74 NewspaperType)
        (objectType Pillow_bar__plus_01_dot_44_bar__plus_00_dot_52_bar__minus_00_dot_13 PillowType)
        (objectType Television_bar__minus_02_dot_03_bar__plus_01_dot_08_bar__plus_00_dot_56 TelevisionType)
        (objectType Plate_bar__plus_01_dot_69_bar__plus_00_dot_71_bar__minus_00_dot_71 PlateType)
        (objectType Newspaper_bar__minus_01_dot_97_bar__plus_00_dot_59_bar__plus_00_dot_03 NewspaperType)
        (objectType Window_bar__minus_00_dot_01_bar__plus_01_dot_29_bar__minus_02_dot_45 WindowType)
        (objectType Vase_bar__plus_00_dot_33_bar__plus_00_dot_34_bar__plus_00_dot_52 VaseType)
        (objectType CellPhone_bar__minus_01_dot_74_bar__plus_00_dot_35_bar__plus_00_dot_73 CellPhoneType)
        (objectType HousePlant_bar__minus_00_dot_81_bar__plus_00_dot_00_bar__minus_02_dot_19 HousePlantType)
        (objectType CellPhone_bar__plus_03_dot_58_bar__plus_00_dot_64_bar__minus_00_dot_62 CellPhoneType)
        (objectType Newspaper_bar__plus_01_dot_42_bar__plus_00_dot_48_bar__plus_00_dot_98 NewspaperType)
        (objectType Statue_bar__minus_01_dot_81_bar__plus_00_dot_60_bar__plus_00_dot_21 StatueType)
        (objectType CellPhone_bar__plus_00_dot_17_bar__plus_00_dot_34_bar__plus_00_dot_14 CellPhoneType)
        (objectType Vase_bar__plus_00_dot_25_bar__plus_00_dot_34_bar__plus_00_dot_78 VaseType)
        (objectType Vase_bar__plus_00_dot_33_bar__plus_00_dot_34_bar__plus_00_dot_02 VaseType)
        (objectType CreditCard_bar__plus_01_dot_75_bar__plus_00_dot_62_bar__minus_00_dot_73 CreditCardType)
        (objectType LightSwitch_bar__plus_02_dot_10_bar__plus_01_dot_35_bar__minus_00_dot_83 LightSwitchType)
        (objectType KeyChain_bar__plus_01_dot_60_bar__plus_00_dot_48_bar__plus_00_dot_98 KeyChainType)
        (objectType Statue_bar__minus_02_dot_04_bar__plus_00_dot_61_bar__plus_01_dot_10 StatueType)
        (objectType CreditCard_bar__minus_01_dot_70_bar__plus_00_dot_29_bar__minus_01_dot_45 CreditCardType)
        (objectType Box_bar__minus_00_dot_22_bar__plus_00_dot_12_bar__minus_02_dot_04 BoxType)
        (objectType Statue_bar__plus_03_dot_69_bar__plus_00_dot_65_bar__minus_00_dot_80 StatueType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PlateType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType VaseType)
        (canContain SideTableType BoxType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PlateType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType CellPhoneType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType NewspaperType)
        (canContain ArmChairType CreditCardType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PlateType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PlateType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType VaseType)
        (canContain SideTableType BoxType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PlateType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PlateType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType CellPhoneType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType NewspaperType)
        (canContain SofaType CreditCardType)
        (canContain GarbageCanType NewspaperType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PlateType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain CoffeeTableType NewspaperType)
        (canContain CoffeeTableType VaseType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType CellPhoneType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType PlateType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType StatueType)
        (pickupable Laptop_bar__plus_03_dot_38_bar__plus_00_dot_64_bar__minus_00_dot_98)
        (pickupable RemoteControl_bar__minus_01_dot_73_bar__plus_00_dot_60_bar__plus_00_dot_39)
        (pickupable KeyChain_bar__plus_00_dot_25_bar__plus_00_dot_34_bar__plus_00_dot_27)
        (pickupable Plate_bar__plus_00_dot_00_bar__plus_00_dot_34_bar__plus_00_dot_52)
        (pickupable Newspaper_bar__minus_00_dot_29_bar__plus_00_dot_55_bar__plus_01_dot_93)
        (pickupable Plate_bar__minus_01_dot_82_bar__plus_00_dot_10_bar__plus_00_dot_40)
        (pickupable Newspaper_bar__minus_01_dot_81_bar__plus_00_dot_60_bar__plus_00_dot_74)
        (pickupable Pillow_bar__plus_01_dot_44_bar__plus_00_dot_52_bar__minus_00_dot_13)
        (pickupable Plate_bar__plus_01_dot_69_bar__plus_00_dot_71_bar__minus_00_dot_71)
        (pickupable Newspaper_bar__minus_01_dot_97_bar__plus_00_dot_59_bar__plus_00_dot_03)
        (pickupable Vase_bar__plus_00_dot_33_bar__plus_00_dot_34_bar__plus_00_dot_52)
        (pickupable CellPhone_bar__minus_01_dot_74_bar__plus_00_dot_35_bar__plus_00_dot_73)
        (pickupable CellPhone_bar__plus_03_dot_58_bar__plus_00_dot_64_bar__minus_00_dot_62)
        (pickupable Newspaper_bar__plus_01_dot_42_bar__plus_00_dot_48_bar__plus_00_dot_98)
        (pickupable Statue_bar__minus_01_dot_81_bar__plus_00_dot_60_bar__plus_00_dot_21)
        (pickupable CellPhone_bar__plus_00_dot_17_bar__plus_00_dot_34_bar__plus_00_dot_14)
        (pickupable Vase_bar__plus_00_dot_25_bar__plus_00_dot_34_bar__plus_00_dot_78)
        (pickupable Vase_bar__plus_00_dot_33_bar__plus_00_dot_34_bar__plus_00_dot_02)
        (pickupable CreditCard_bar__plus_01_dot_75_bar__plus_00_dot_62_bar__minus_00_dot_73)
        (pickupable KeyChain_bar__plus_01_dot_60_bar__plus_00_dot_48_bar__plus_00_dot_98)
        (pickupable Statue_bar__minus_02_dot_04_bar__plus_00_dot_61_bar__plus_01_dot_10)
        (pickupable CreditCard_bar__minus_01_dot_70_bar__plus_00_dot_29_bar__minus_01_dot_45)
        (pickupable Box_bar__minus_00_dot_22_bar__plus_00_dot_12_bar__minus_02_dot_04)
        (pickupable Statue_bar__plus_03_dot_69_bar__plus_00_dot_65_bar__minus_00_dot_80)
        (isReceptacleObject Plate_bar__plus_00_dot_00_bar__plus_00_dot_34_bar__plus_00_dot_52)
        (isReceptacleObject Plate_bar__minus_01_dot_82_bar__plus_00_dot_10_bar__plus_00_dot_40)
        (isReceptacleObject Plate_bar__plus_01_dot_69_bar__plus_00_dot_71_bar__minus_00_dot_71)
        (isReceptacleObject Box_bar__minus_00_dot_22_bar__plus_00_dot_12_bar__minus_02_dot_04)
        (openable Drawer_bar__plus_01_dot_52_bar__plus_00_dot_62_bar__minus_00_dot_76)
        
        (atLocation agent1 loc_bar_4_bar__minus_5_bar_2_bar_30)
        
        (cleanable Plate_bar__plus_00_dot_00_bar__plus_00_dot_34_bar__plus_00_dot_52)
        (cleanable Plate_bar__minus_01_dot_82_bar__plus_00_dot_10_bar__plus_00_dot_40)
        (cleanable Plate_bar__plus_01_dot_69_bar__plus_00_dot_71_bar__minus_00_dot_71)
        
        (heatable Plate_bar__plus_00_dot_00_bar__plus_00_dot_34_bar__plus_00_dot_52)
        (heatable Plate_bar__minus_01_dot_82_bar__plus_00_dot_10_bar__plus_00_dot_40)
        (heatable Plate_bar__plus_01_dot_69_bar__plus_00_dot_71_bar__minus_00_dot_71)
        (coolable Plate_bar__plus_00_dot_00_bar__plus_00_dot_34_bar__plus_00_dot_52)
        (coolable Plate_bar__minus_01_dot_82_bar__plus_00_dot_10_bar__plus_00_dot_40)
        (coolable Plate_bar__plus_01_dot_69_bar__plus_00_dot_71_bar__minus_00_dot_71)
        
        
        (toggleable FloorLamp_bar__plus_01_dot_69_bar__plus_00_dot_00_bar__plus_01_dot_78)
        
        
        
        
        (inReceptacle RemoteControl_bar__minus_01_dot_73_bar__plus_00_dot_60_bar__plus_00_dot_39 TVStand_bar__minus_01_dot_97_bar_00_dot_00_bar__plus_00_dot_56)
        (inReceptacle Newspaper_bar__minus_01_dot_81_bar__plus_00_dot_60_bar__plus_00_dot_74 TVStand_bar__minus_01_dot_97_bar_00_dot_00_bar__plus_00_dot_56)
        (inReceptacle Television_bar__minus_02_dot_03_bar__plus_01_dot_08_bar__plus_00_dot_56 TVStand_bar__minus_01_dot_97_bar_00_dot_00_bar__plus_00_dot_56)
        (inReceptacle Statue_bar__minus_02_dot_04_bar__plus_00_dot_61_bar__plus_01_dot_10 TVStand_bar__minus_01_dot_97_bar_00_dot_00_bar__plus_00_dot_56)
        (inReceptacle Newspaper_bar__minus_01_dot_97_bar__plus_00_dot_59_bar__plus_00_dot_03 TVStand_bar__minus_01_dot_97_bar_00_dot_00_bar__plus_00_dot_56)
        (inReceptacle Statue_bar__minus_01_dot_81_bar__plus_00_dot_60_bar__plus_00_dot_21 TVStand_bar__minus_01_dot_97_bar_00_dot_00_bar__plus_00_dot_56)
        (inReceptacle CreditCard_bar__minus_01_dot_70_bar__plus_00_dot_29_bar__minus_01_dot_45 ArmChair_bar__minus_01_dot_76_bar__plus_00_dot_00_bar__minus_01_dot_49)
        (inReceptacle Vase_bar__plus_00_dot_25_bar__plus_00_dot_34_bar__plus_00_dot_78 CoffeeTable_bar__plus_00_dot_17_bar_00_dot_00_bar__plus_00_dot_42)
        (inReceptacle Vase_bar__plus_00_dot_33_bar__plus_00_dot_34_bar__plus_00_dot_52 CoffeeTable_bar__plus_00_dot_17_bar_00_dot_00_bar__plus_00_dot_42)
        (inReceptacle Vase_bar__plus_00_dot_33_bar__plus_00_dot_34_bar__plus_00_dot_02 CoffeeTable_bar__plus_00_dot_17_bar_00_dot_00_bar__plus_00_dot_42)
        (inReceptacle KeyChain_bar__plus_00_dot_25_bar__plus_00_dot_34_bar__plus_00_dot_27 CoffeeTable_bar__plus_00_dot_17_bar_00_dot_00_bar__plus_00_dot_42)
        (inReceptacle Plate_bar__plus_00_dot_00_bar__plus_00_dot_34_bar__plus_00_dot_52 CoffeeTable_bar__plus_00_dot_17_bar_00_dot_00_bar__plus_00_dot_42)
        (inReceptacle CellPhone_bar__plus_00_dot_17_bar__plus_00_dot_34_bar__plus_00_dot_14 CoffeeTable_bar__plus_00_dot_17_bar_00_dot_00_bar__plus_00_dot_42)
        (inReceptacle CellPhone_bar__plus_03_dot_58_bar__plus_00_dot_64_bar__minus_00_dot_62 SideTable_bar__plus_03_dot_58_bar__plus_00_dot_00_bar__minus_01_dot_17)
        (inReceptacle Laptop_bar__plus_03_dot_38_bar__plus_00_dot_64_bar__minus_00_dot_98 SideTable_bar__plus_03_dot_58_bar__plus_00_dot_00_bar__minus_01_dot_17)
        (inReceptacle Statue_bar__plus_03_dot_69_bar__plus_00_dot_65_bar__minus_00_dot_80 SideTable_bar__plus_03_dot_58_bar__plus_00_dot_00_bar__minus_01_dot_17)
        (inReceptacle CellPhone_bar__minus_01_dot_74_bar__plus_00_dot_35_bar__plus_00_dot_73 Shelf_bar__minus_01_dot_82_bar__plus_00_dot_38_bar__plus_00_dot_56)
        (inReceptacle Plate_bar__minus_01_dot_82_bar__plus_00_dot_10_bar__plus_00_dot_40 Shelf_bar__minus_01_dot_83_bar__plus_00_dot_13_bar__plus_00_dot_56)
        (inReceptacle Newspaper_bar__plus_01_dot_42_bar__plus_00_dot_48_bar__plus_00_dot_98 Sofa_bar__plus_01_dot_42_bar_00_dot_00_bar__plus_00_dot_39)
        (inReceptacle KeyChain_bar__plus_01_dot_60_bar__plus_00_dot_48_bar__plus_00_dot_98 Sofa_bar__plus_01_dot_42_bar_00_dot_00_bar__plus_00_dot_39)
        (inReceptacle Pillow_bar__plus_01_dot_44_bar__plus_00_dot_52_bar__minus_00_dot_13 Sofa_bar__plus_01_dot_42_bar_00_dot_00_bar__plus_00_dot_39)
        (inReceptacle Plate_bar__plus_01_dot_69_bar__plus_00_dot_71_bar__minus_00_dot_71 SideTable_bar__plus_01_dot_52_bar__plus_00_dot_00_bar__minus_00_dot_70)
        (inReceptacle Newspaper_bar__minus_00_dot_29_bar__plus_00_dot_55_bar__plus_01_dot_93 Shelf_bar__minus_00_dot_20_bar__plus_00_dot_54_bar__plus_01_dot_93)
        (inReceptacle CreditCard_bar__plus_01_dot_75_bar__plus_00_dot_62_bar__minus_00_dot_73 Drawer_bar__plus_01_dot_52_bar__plus_00_dot_62_bar__minus_00_dot_76)
        
        
        (receptacleAtLocation ArmChair_bar__minus_01_dot_76_bar__plus_00_dot_00_bar__minus_01_dot_49 loc_bar__minus_2_bar__minus_4_bar_3_bar_60)
        (receptacleAtLocation CoffeeTable_bar__plus_00_dot_17_bar_00_dot_00_bar__plus_00_dot_42 loc_bar__minus_4_bar_2_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_01_dot_52_bar__plus_00_dot_62_bar__minus_00_dot_76 loc_bar_5_bar__minus_6_bar_0_bar_60)
        (receptacleAtLocation GarbageCan_bar__minus_01_dot_89_bar__plus_00_dot_00_bar__minus_00_dot_41 loc_bar__minus_6_bar__minus_3_bar_3_bar_60)
        (receptacleAtLocation Shelf_bar__minus_00_dot_20_bar__plus_00_dot_16_bar__plus_01_dot_93 loc_bar__minus_1_bar_6_bar_0_bar_60)
        (receptacleAtLocation Shelf_bar__minus_00_dot_20_bar__plus_00_dot_54_bar__plus_01_dot_93 loc_bar__minus_3_bar_3_bar_0_bar_45)
        (receptacleAtLocation Shelf_bar__minus_00_dot_20_bar__plus_00_dot_87_bar__plus_01_dot_93 loc_bar__minus_3_bar_3_bar_0_bar_30)
        (receptacleAtLocation Shelf_bar__minus_01_dot_82_bar__plus_00_dot_38_bar__plus_00_dot_56 loc_bar__minus_2_bar_2_bar_3_bar_45)
        (receptacleAtLocation Shelf_bar__minus_01_dot_83_bar__plus_00_dot_13_bar__plus_00_dot_56 loc_bar__minus_3_bar_2_bar_3_bar_60)
        (receptacleAtLocation SideTable_bar__plus_01_dot_52_bar__plus_00_dot_00_bar__minus_00_dot_70 loc_bar_5_bar__minus_6_bar_0_bar_60)
        (receptacleAtLocation SideTable_bar__plus_03_dot_58_bar__plus_00_dot_00_bar__minus_01_dot_17 loc_bar_10_bar__minus_5_bar_1_bar_60)
        (receptacleAtLocation Sofa_bar__plus_01_dot_42_bar_00_dot_00_bar__plus_00_dot_39 loc_bar_3_bar_2_bar_1_bar_60)
        (receptacleAtLocation TVStand_bar__minus_01_dot_97_bar_00_dot_00_bar__plus_00_dot_56 loc_bar__minus_4_bar_2_bar_3_bar_60)
        (objectAtLocation Statue_bar__minus_01_dot_81_bar__plus_00_dot_60_bar__plus_00_dot_21 loc_bar__minus_4_bar_2_bar_3_bar_60)
        (objectAtLocation Vase_bar__plus_00_dot_25_bar__plus_00_dot_34_bar__plus_00_dot_78 loc_bar__minus_4_bar_2_bar_1_bar_60)
        (objectAtLocation CellPhone_bar__plus_00_dot_17_bar__plus_00_dot_34_bar__plus_00_dot_14 loc_bar__minus_4_bar_2_bar_1_bar_60)
        (objectAtLocation Plate_bar__plus_00_dot_00_bar__plus_00_dot_34_bar__plus_00_dot_52 loc_bar__minus_4_bar_2_bar_1_bar_60)
        (objectAtLocation KeyChain_bar__plus_00_dot_25_bar__plus_00_dot_34_bar__plus_00_dot_27 loc_bar__minus_4_bar_2_bar_1_bar_60)
        (objectAtLocation CreditCard_bar__minus_01_dot_70_bar__plus_00_dot_29_bar__minus_01_dot_45 loc_bar__minus_2_bar__minus_4_bar_3_bar_60)
        (objectAtLocation Newspaper_bar__plus_01_dot_42_bar__plus_00_dot_48_bar__plus_00_dot_98 loc_bar_3_bar_2_bar_1_bar_60)
        (objectAtLocation CellPhone_bar__plus_03_dot_58_bar__plus_00_dot_64_bar__minus_00_dot_62 loc_bar_10_bar__minus_5_bar_1_bar_60)
        (objectAtLocation Statue_bar__plus_03_dot_69_bar__plus_00_dot_65_bar__minus_00_dot_80 loc_bar_10_bar__minus_5_bar_1_bar_60)
        (objectAtLocation Newspaper_bar__minus_01_dot_81_bar__plus_00_dot_60_bar__plus_00_dot_74 loc_bar__minus_4_bar_2_bar_3_bar_60)
        (objectAtLocation Newspaper_bar__minus_00_dot_29_bar__plus_00_dot_55_bar__plus_01_dot_93 loc_bar__minus_3_bar_3_bar_0_bar_45)
        (objectAtLocation Plate_bar__minus_01_dot_82_bar__plus_00_dot_10_bar__plus_00_dot_40 loc_bar__minus_3_bar_2_bar_3_bar_60)
        (objectAtLocation Box_bar__minus_00_dot_22_bar__plus_00_dot_12_bar__minus_02_dot_04 loc_bar_0_bar__minus_7_bar_2_bar_60)
        (objectAtLocation Newspaper_bar__minus_01_dot_97_bar__plus_00_dot_59_bar__plus_00_dot_03 loc_bar__minus_4_bar_2_bar_3_bar_60)
        (objectAtLocation CreditCard_bar__plus_01_dot_75_bar__plus_00_dot_62_bar__minus_00_dot_73 loc_bar_5_bar__minus_6_bar_0_bar_60)
        (objectAtLocation Television_bar__minus_02_dot_03_bar__plus_01_dot_08_bar__plus_00_dot_56 loc_bar__minus_4_bar_2_bar_3_bar_60)
        (objectAtLocation Vase_bar__plus_00_dot_33_bar__plus_00_dot_34_bar__plus_00_dot_02 loc_bar__minus_4_bar_2_bar_1_bar_60)
        (objectAtLocation KeyChain_bar__plus_01_dot_60_bar__plus_00_dot_48_bar__plus_00_dot_98 loc_bar_3_bar_2_bar_1_bar_60)
        (objectAtLocation Plate_bar__plus_01_dot_69_bar__plus_00_dot_71_bar__minus_00_dot_71 loc_bar_5_bar__minus_6_bar_0_bar_60)
        (objectAtLocation LightSwitch_bar__plus_02_dot_10_bar__plus_01_dot_35_bar__minus_00_dot_83 loc_bar_11_bar__minus_3_bar_3_bar_30)
        (objectAtLocation Laptop_bar__plus_03_dot_38_bar__plus_00_dot_64_bar__minus_00_dot_98 loc_bar_10_bar__minus_5_bar_1_bar_60)
        (objectAtLocation HousePlant_bar__minus_00_dot_81_bar__plus_00_dot_00_bar__minus_02_dot_19 loc_bar__minus_3_bar__minus_6_bar_2_bar_60)
        (objectAtLocation FloorLamp_bar__plus_01_dot_69_bar__plus_00_dot_00_bar__plus_01_dot_78 loc_bar_4_bar_7_bar_1_bar_60)
        (objectAtLocation Painting_bar__plus_01_dot_98_bar__plus_01_dot_59_bar__plus_00_dot_33 loc_bar_11_bar_0_bar_3_bar_0)
        (objectAtLocation CellPhone_bar__minus_01_dot_74_bar__plus_00_dot_35_bar__plus_00_dot_73 loc_bar__minus_2_bar_2_bar_3_bar_45)
        (objectAtLocation RemoteControl_bar__minus_01_dot_73_bar__plus_00_dot_60_bar__plus_00_dot_39 loc_bar__minus_4_bar_2_bar_3_bar_60)
        (objectAtLocation Pillow_bar__plus_01_dot_44_bar__plus_00_dot_52_bar__minus_00_dot_13 loc_bar_3_bar_2_bar_1_bar_60)
        (objectAtLocation Vase_bar__plus_00_dot_33_bar__plus_00_dot_34_bar__plus_00_dot_52 loc_bar__minus_4_bar_2_bar_1_bar_60)
        (objectAtLocation Window_bar__plus_02_dot_78_bar__plus_01_dot_28_bar__minus_02_dot_02 loc_bar_11_bar__minus_6_bar_2_bar_30)
        (objectAtLocation Window_bar__minus_00_dot_01_bar__plus_01_dot_29_bar__minus_02_dot_45 loc_bar_0_bar__minus_7_bar_2_bar_30)
        (objectAtLocation Statue_bar__minus_02_dot_04_bar__plus_00_dot_61_bar__plus_01_dot_10 loc_bar__minus_4_bar_2_bar_3_bar_60)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 NewspaperType)
                                    (receptacleType ?r CoffeeTableType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 NewspaperType)
                                            (receptacleType ?r CoffeeTableType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            