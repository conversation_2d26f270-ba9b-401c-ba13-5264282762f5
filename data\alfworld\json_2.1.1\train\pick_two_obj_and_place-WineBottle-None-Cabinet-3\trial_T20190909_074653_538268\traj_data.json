{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000209.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000210.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000211.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000212.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000283.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000284.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000285.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000286.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000287.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000288.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000289.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000290.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000291.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000292.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000293.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000294.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000295.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000296.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000297.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000298.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000299.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000300.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000301.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000302.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000303.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000304.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000305.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000306.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000307.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000308.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000309.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000310.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000311.png", "low_idx": 44}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "WineBottle", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-7|3|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["winebottle"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["WineBottle", [-7.16126252, -7.16126252, -6.72589348, -6.72589348, 5.29571532, 5.29571532]], "coordinateReceptacleObjectId": ["CounterTop", [-7.236, -7.236, 4.728, 4.728, 5.434, 5.434]], "forceVisible": true, "objectId": "WineBottle|-01.79|+01.32|-01.68"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-2|-6|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["winebottle", "cabinet"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["WineBottle", [-7.16126252, -7.16126252, -6.72589348, -6.72589348, 5.29571532, 5.29571532]], "coordinateReceptacleObjectId": ["Cabinet", [-5.84518432, -5.84518432, -8.008472, -8.008472, 3.100034236, 3.100034236]], "forceVisible": true, "objectId": "WineBottle|-01.79|+01.32|-01.68", "receptacleObjectId": "Cabinet|-01.46|+00.78|-02.00"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-3|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["winebottle"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["WineBottle", [-5.454752, -5.454752, -3.2051612, -3.2051612, 5.29571532, 5.29571532]], "coordinateReceptacleObjectId": ["CounterTop", [-7.236, -7.236, 4.728, 4.728, 5.434, 5.434]], "forceVisible": true, "objectId": "WineBottle|-01.36|+01.32|-00.80"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-2|-6|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["winebottle", "cabinet"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["WineBottle", [-5.454752, -5.454752, -3.2051612, -3.2051612, 5.29571532, 5.29571532]], "coordinateReceptacleObjectId": ["Cabinet", [-5.84518432, -5.84518432, -8.008472, -8.008472, 3.100034236, 3.100034236]], "forceVisible": true, "objectId": "WineBottle|-01.36|+01.32|-00.80", "receptacleObjectId": "Cabinet|-01.46|+00.78|-02.00"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "WineBottle|-01.79|+01.32|-01.68"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [157, 59, 185, 160], "mask": [[17570, 7], [17868, 11], [18168, 10], [18468, 10], [18768, 10], [19068, 10], [19368, 10], [19668, 10], [19968, 10], [20268, 10], [20568, 10], [20868, 10], [21168, 10], [21468, 10], [21768, 10], [22068, 10], [22368, 10], [22668, 10], [22968, 10], [23268, 10], [23568, 10], [23868, 10], [24167, 11], [24467, 11], [24767, 11], [25067, 11], [25367, 11], [25667, 11], [25966, 12], [26265, 14], [26565, 15], [26864, 16], [27163, 18], [27463, 18], [27762, 20], [28061, 21], [28361, 22], [28660, 23], [28959, 25], [29259, 26], [29559, 26], [29858, 27], [30158, 27], [30458, 27], [30758, 28], [31058, 28], [31358, 28], [31658, 28], [31957, 28], [32257, 28], [32557, 28], [32857, 28], [33157, 28], [33457, 28], [33757, 28], [34057, 28], [34357, 28], [34657, 28], [34957, 28], [35257, 28], [35557, 28], [35857, 28], [36157, 27], [36457, 27], [36757, 27], [37057, 27], [37357, 27], [37657, 27], [37957, 27], [38257, 27], [38557, 27], [38857, 27], [39157, 27], [39457, 27], [39757, 26], [40057, 24], [40357, 22], [40657, 21], [40957, 20], [41257, 19], [41557, 18], [41857, 17], [42157, 16], [42457, 16], [42757, 15], [43057, 15], [43357, 15], [43657, 14], [43957, 14], [44257, 14], [44557, 14], [44857, 14], [45157, 14], [45457, 13], [45757, 13], [46057, 12], [46357, 11], [46657, 11], [46958, 9], [47259, 8], [47560, 6], [47862, 4]], "point": [171, 108]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-01.46|+00.78|-02.00"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 107, 113, 200], "mask": [[31800, 9], [32100, 19], [32400, 29], [32700, 39], [33000, 50], [33300, 65], [33600, 82], [33900, 110], [34200, 110], [34500, 110], [34800, 110], [35100, 110], [35400, 110], [35700, 110], [36000, 111], [36300, 111], [36600, 111], [36900, 111], [37200, 111], [37500, 111], [37800, 111], [38100, 111], [38400, 112], [38700, 112], [39000, 112], [39300, 111], [39600, 111], [39900, 111], [40200, 111], [40500, 111], [40800, 110], [41100, 110], [41400, 110], [41700, 110], [42000, 110], [42300, 110], [42600, 109], [42900, 109], [43200, 109], [43500, 109], [43800, 109], [44100, 109], [44400, 109], [44700, 109], [45000, 109], [45300, 109], [45600, 109], [45900, 110], [46200, 110], [46500, 110], [46800, 110], [47100, 110], [47400, 110], [47700, 110], [48000, 110], [48300, 110], [48600, 110], [48900, 110], [49200, 111], [49501, 110], [49802, 109], [50102, 109], [50403, 108], [50703, 108], [51004, 107], [51305, 106], [51605, 106], [51906, 105], [52206, 105], [52507, 105], [52808, 104], [53108, 104], [53409, 103], [53710, 102], [54010, 102], [54311, 101], [54611, 101], [54912, 100], [55213, 99], [55513, 99], [55814, 99], [56114, 99], [56415, 98], [56716, 97], [57016, 97], [57317, 96], [57617, 96], [57918, 95], [58219, 94], [58519, 94], [58820, 93], [59121, 92], [59421, 93], [59722, 92]], "point": [56, 152]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "WineBottle|-01.79|+01.32|-01.68", "placeStationary": true, "receptacleObjectId": "Cabinet|-01.46|+00.78|-02.00"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 107, 112, 255], "mask": [[31800, 9], [32100, 19], [32400, 29], [32700, 39], [33000, 50], [33300, 65], [33600, 82], [33900, 109], [34200, 109], [34500, 110], [34800, 110], [35100, 110], [35400, 110], [35700, 110], [36000, 110], [36300, 110], [36600, 110], [36900, 111], [37200, 111], [37500, 111], [37800, 111], [38100, 111], [38400, 111], [38700, 111], [39000, 112], [39300, 111], [39600, 111], [39900, 111], [40200, 111], [40500, 111], [40800, 110], [41100, 110], [41400, 110], [41700, 110], [42000, 110], [42300, 110], [42600, 109], [42900, 76], [42990, 19], [43200, 74], [43291, 18], [43500, 72], [43593, 16], [43800, 70], [43894, 15], [44100, 68], [44195, 14], [44400, 67], [44496, 13], [44700, 65], [44797, 12], [45000, 65], [45097, 12], [45300, 64], [45397, 12], [45600, 64], [45697, 12], [45900, 64], [45997, 13], [46200, 42], [46297, 13], [46500, 42], [46597, 13], [46801, 62], [46896, 14], [47102, 60], [47196, 14], [47402, 60], [47496, 14], [47703, 59], [47796, 14], [48000, 1], [48003, 60], [48095, 15], [48300, 1], [48304, 60], [48394, 16], [48600, 2], [48605, 60], [48693, 17], [48900, 3], [48905, 61], [48992, 18], [49200, 3], [49206, 61], [49290, 21], [49500, 4], [49506, 62], [49589, 22], [49800, 4], [49807, 62], [49887, 24], [50100, 5], [50107, 63], [50185, 26], [50400, 6], [50408, 103], [50700, 6], [50709, 102], [51000, 7], [51009, 102], [51300, 7], [51310, 101], [51600, 8], [51610, 101], [51900, 9], [51911, 100], [52200, 9], [52212, 99], [52500, 10], [52512, 100], [52800, 10], [52813, 99], [53100, 11], [53113, 99], [53400, 12], [53414, 98], [53700, 12], [53715, 97], [54000, 13], [54015, 59], [54094, 18], [54300, 13], [54316, 57], [54394, 18], [54600, 14], [54616, 57], [54694, 18], [54900, 15], [54917, 56], [54994, 18], [55200, 15], [55217, 56], [55294, 18], [55500, 16], [55518, 54], [55593, 19], [55800, 16], [55819, 53], [55893, 20], [56100, 17], [56119, 53], [56193, 20], [56400, 17], [56420, 52], [56493, 20], [56700, 18], [56720, 53], [56793, 20], [57000, 19], [57021, 92], [57300, 19], [57322, 91], [57600, 20], [57622, 91], [57900, 20], [57923, 90], [58200, 21], [58223, 90], [58500, 22], [58524, 89], [58800, 22], [59100, 23], [59400, 23], [59700, 23], [60000, 23], [60300, 22], [60600, 22], [60900, 21], [61200, 21], [61500, 21], [61800, 20], [62100, 20], [62400, 19], [62700, 19], [63000, 19], [63300, 18], [63600, 18], [63900, 17], [64200, 17], [64500, 17], [64800, 16], [65100, 16], [65400, 15], [65700, 15], [66000, 14], [66300, 14], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 12], [68100, 12], [68400, 11], [68700, 11], [69000, 10], [69300, 10], [69600, 10], [69900, 9], [70200, 9], [70500, 8], [70800, 8], [71100, 8], [71400, 7], [71700, 7], [72000, 6], [72300, 6], [72600, 5], [72900, 5], [73200, 5], [73500, 4], [73800, 4], [74100, 3], [74400, 3], [74700, 3], [75000, 2], [75300, 2], [75600, 1], [75900, 1], [76200, 1]], "point": [56, 180]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-01.46|+00.78|-02.00"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 107, 120, 255], "mask": [[31800, 9], [32100, 19], [32400, 29], [32700, 39], [33000, 50], [33300, 65], [33600, 82], [33900, 109], [34200, 109], [34500, 110], [34800, 110], [35100, 110], [35400, 110], [35700, 110], [36000, 110], [36300, 110], [36600, 110], [36900, 111], [37200, 111], [37500, 111], [37800, 111], [38100, 111], [38400, 111], [38700, 111], [39000, 112], [39300, 112], [39600, 112], [39900, 112], [40200, 112], [40500, 112], [40800, 112], [41100, 113], [41400, 91], [41496, 17], [41700, 91], [41796, 17], [42000, 91], [42096, 17], [42300, 91], [42396, 17], [42600, 92], [42696, 17], [42900, 76], [42990, 2], [42997, 16], [43200, 74], [43291, 1], [43297, 16], [43500, 72], [43597, 17], [43800, 70], [43897, 17], [44100, 68], [44199, 15], [44400, 67], [44500, 14], [44700, 65], [44801, 13], [45000, 65], [45102, 12], [45300, 64], [45403, 11], [45600, 64], [45703, 12], [45900, 64], [46003, 12], [46200, 42], [46303, 12], [46500, 42], [46604, 11], [46801, 62], [46904, 11], [47102, 60], [47204, 11], [47402, 60], [47504, 11], [47703, 59], [47804, 12], [48000, 1], [48003, 60], [48104, 12], [48300, 1], [48304, 60], [48405, 11], [48600, 2], [48605, 60], [48705, 11], [48900, 3], [48905, 61], [49005, 11], [49200, 3], [49206, 61], [49290, 2], [49305, 11], [49500, 4], [49506, 62], [49589, 3], [49605, 11], [49800, 4], [49807, 62], [49887, 5], [49905, 12], [50100, 5], [50107, 63], [50185, 8], [50206, 11], [50400, 6], [50408, 85], [50506, 11], [50700, 6], [50709, 84], [50806, 11], [51000, 7], [51009, 84], [51106, 11], [51300, 7], [51310, 84], [51406, 11], [51600, 8], [51610, 84], [51706, 11], [51900, 9], [51911, 83], [52005, 12], [52200, 9], [52212, 83], [52305, 13], [52500, 10], [52512, 84], [52603, 15], [52800, 10], [52813, 86], [52900, 18], [53100, 11], [53113, 105], [53400, 12], [53414, 104], [53700, 12], [53715, 103], [54000, 13], [54015, 59], [54094, 24], [54300, 13], [54316, 57], [54394, 25], [54600, 14], [54616, 57], [54694, 25], [54900, 15], [54917, 56], [54994, 25], [55200, 15], [55217, 56], [55294, 25], [55500, 16], [55518, 54], [55593, 26], [55800, 16], [55819, 53], [55893, 26], [56100, 17], [56119, 53], [56193, 26], [56400, 17], [56420, 52], [56493, 27], [56700, 18], [56720, 53], [56793, 27], [57000, 19], [57021, 99], [57300, 19], [57322, 98], [57600, 20], [57622, 98], [57900, 20], [57923, 97], [58200, 21], [58223, 97], [58500, 22], [58524, 97], [58800, 22], [59100, 23], [59400, 23], [59700, 23], [60000, 23], [60300, 22], [60600, 22], [60900, 21], [61200, 21], [61500, 21], [61800, 20], [62100, 20], [62400, 19], [62700, 19], [63000, 19], [63300, 18], [63600, 18], [63900, 17], [64200, 17], [64500, 17], [64800, 16], [65100, 16], [65400, 15], [65700, 15], [66000, 14], [66300, 14], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 12], [68100, 12], [68400, 11], [68700, 11], [69000, 10], [69300, 10], [69600, 10], [69900, 9], [70200, 9], [70500, 8], [70800, 8], [71100, 8], [71400, 7], [71700, 7], [72000, 6], [72300, 6], [72600, 5], [72900, 5], [73200, 5], [73500, 4], [73800, 4], [74100, 3], [74400, 3], [74700, 3], [75000, 2], [75300, 2], [75600, 1], [75900, 1], [76200, 1]], "point": [60, 180]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "WineBottle|-01.36|+01.32|-00.80"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [92, 1, 146, 132], "mask": [[102, 23], [402, 23], [703, 22], [1003, 22], [1303, 22], [1603, 22], [1903, 22], [2203, 22], [2503, 23], [2803, 23], [3103, 24], [3403, 25], [3703, 25], [4002, 27], [4302, 28], [4601, 30], [4901, 31], [5200, 32], [5500, 33], [5799, 35], [6099, 36], [6398, 38], [6698, 38], [6997, 40], [7297, 41], [7596, 43], [7896, 43], [8196, 44], [8495, 46], [8795, 47], [9094, 48], [9394, 49], [9693, 50], [9993, 50], [10292, 52], [10592, 52], [10892, 52], [11192, 53], [11492, 53], [11792, 53], [12092, 53], [12392, 53], [12692, 54], [12992, 54], [13292, 54], [13592, 54], [13892, 54], [14192, 54], [14492, 54], [14792, 54], [15092, 54], [15392, 54], [15693, 53], [15993, 53], [16293, 53], [16593, 53], [16894, 52], [17194, 52], [17494, 52], [17794, 52], [18094, 52], [18395, 51], [18695, 51], [18995, 51], [19295, 51], [19596, 50], [19896, 50], [20196, 51], [20496, 51], [20797, 50], [21097, 50], [21397, 50], [21697, 50], [21997, 50], [22298, 49], [22598, 49], [22898, 49], [23198, 49], [23499, 48], [23799, 48], [24099, 48], [24399, 48], [24700, 47], [25000, 47], [25300, 47], [25600, 47], [25900, 47], [26201, 46], [26501, 46], [26801, 46], [27101, 46], [27402, 45], [27702, 45], [28002, 45], [28302, 45], [28603, 44], [28903, 44], [29203, 44], [29503, 44], [29803, 44], [30104, 43], [30404, 43], [30704, 43], [31004, 43], [31305, 42], [31605, 42], [31905, 42], [32205, 42], [32506, 41], [32806, 41], [33106, 41], [33406, 41], [33706, 41], [34007, 40], [34307, 40], [34607, 40], [34907, 40], [35208, 39], [35508, 38], [35808, 38], [36109, 36], [36409, 36], [36709, 35], [37010, 34], [37311, 32], [37612, 30], [37913, 28], [38214, 26], [38516, 22], [38817, 19], [39120, 13], [39426, 2]], "point": [119, 65]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-01.46|+00.78|-02.00"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 107, 118, 200], "mask": [[31800, 9], [32100, 19], [32400, 29], [32700, 39], [33000, 50], [33300, 65], [33600, 82], [33900, 110], [34200, 110], [34500, 110], [34800, 110], [35100, 110], [35400, 110], [35700, 110], [36000, 111], [36300, 111], [36600, 111], [36900, 111], [37200, 111], [37500, 111], [37800, 111], [38100, 111], [38400, 112], [38700, 112], [39000, 112], [39300, 112], [39600, 112], [39900, 112], [40200, 112], [40500, 113], [40800, 113], [41100, 113], [41400, 113], [41700, 112], [42000, 113], [42300, 113], [42600, 114], [42900, 114], [43200, 114], [43500, 114], [43800, 114], [44100, 114], [44400, 114], [44700, 114], [45000, 115], [45300, 115], [45600, 115], [45900, 115], [46200, 115], [46500, 115], [46800, 115], [47100, 116], [47400, 116], [47700, 114], [47815, 1], [48000, 114], [48115, 1], [48300, 116], [48600, 115], [48900, 115], [49200, 115], [49316, 1], [49501, 114], [49802, 114], [50102, 114], [50403, 113], [50703, 113], [51004, 113], [51305, 112], [51605, 112], [51906, 111], [52206, 111], [52507, 111], [52808, 110], [53108, 110], [53409, 109], [53710, 109], [54010, 109], [54311, 108], [54611, 108], [54912, 107], [55213, 106], [55513, 105], [55814, 102], [56114, 101], [56415, 99], [56716, 97], [57016, 96], [57317, 94], [57617, 93], [57918, 91], [58219, 89], [58519, 89], [58820, 87], [59121, 85], [59421, 84], [59722, 83]], "point": [59, 152]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "WineBottle|-01.36|+01.32|-00.80", "placeStationary": true, "receptacleObjectId": "Cabinet|-01.46|+00.78|-02.00"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 107, 118, 255], "mask": [[31800, 9], [32100, 19], [32400, 29], [32700, 39], [33000, 50], [33300, 65], [33600, 82], [33900, 109], [34200, 109], [34500, 110], [34800, 110], [35100, 110], [35400, 110], [35700, 110], [36000, 110], [36300, 110], [36600, 110], [36900, 111], [37200, 111], [37500, 111], [37800, 111], [38100, 111], [38400, 111], [38700, 111], [39000, 112], [39300, 112], [39600, 112], [39900, 112], [40200, 112], [40500, 112], [40800, 112], [41100, 113], [41400, 91], [41496, 17], [41700, 91], [41796, 16], [42000, 91], [42096, 17], [42300, 91], [42396, 17], [42600, 92], [42696, 17], [42900, 76], [42990, 2], [42997, 16], [43200, 74], [43291, 1], [43297, 16], [43500, 72], [43597, 17], [43800, 70], [43897, 17], [44100, 68], [44199, 15], [44400, 67], [44500, 14], [44700, 65], [44801, 13], [45000, 65], [45102, 12], [45300, 64], [45403, 11], [45600, 64], [45703, 12], [45900, 64], [46003, 12], [46200, 42], [46303, 12], [46500, 42], [46604, 11], [46801, 62], [46904, 11], [47102, 60], [47204, 11], [47402, 60], [47504, 11], [47703, 59], [47804, 10], [47815, 1], [48000, 1], [48003, 60], [48104, 10], [48115, 1], [48300, 1], [48304, 60], [48405, 11], [48600, 2], [48605, 60], [48705, 10], [48900, 3], [48905, 61], [49005, 10], [49200, 3], [49206, 61], [49290, 2], [49305, 10], [49500, 4], [49506, 62], [49589, 3], [49605, 10], [49800, 4], [49807, 62], [49887, 5], [49905, 11], [50100, 5], [50107, 63], [50185, 8], [50206, 10], [50400, 6], [50408, 85], [50506, 10], [50700, 6], [50709, 84], [50806, 10], [51000, 7], [51009, 84], [51106, 11], [51300, 7], [51310, 84], [51406, 11], [51600, 8], [51610, 84], [51706, 11], [51900, 9], [51911, 83], [52005, 12], [52200, 9], [52212, 83], [52305, 12], [52500, 10], [52512, 84], [52603, 15], [52800, 10], [52813, 86], [52900, 18], [53100, 11], [53113, 105], [53400, 12], [53414, 104], [53700, 12], [53715, 103], [54000, 13], [54015, 59], [54094, 24], [54300, 13], [54316, 57], [54394, 25], [54600, 14], [54616, 57], [54694, 25], [54900, 15], [54917, 56], [54994, 25], [55200, 15], [55217, 56], [55294, 25], [55500, 16], [55518, 54], [55593, 25], [55800, 16], [55819, 53], [55893, 23], [56100, 17], [56119, 53], [56193, 22], [56400, 17], [56420, 52], [56493, 21], [56700, 18], [56720, 53], [56793, 20], [57000, 19], [57021, 91], [57300, 19], [57322, 89], [57600, 20], [57622, 88], [57900, 20], [57923, 86], [58200, 21], [58223, 85], [58500, 22], [58524, 84], [58800, 22], [59100, 23], [59400, 23], [59700, 23], [60000, 23], [60300, 22], [60600, 22], [60900, 21], [61200, 21], [61500, 21], [61800, 20], [62100, 20], [62400, 19], [62700, 19], [63000, 19], [63300, 18], [63600, 18], [63900, 17], [64200, 17], [64500, 17], [64800, 16], [65100, 16], [65400, 15], [65700, 15], [66000, 14], [66300, 14], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 12], [68100, 12], [68400, 11], [68700, 11], [69000, 10], [69300, 10], [69600, 10], [69900, 9], [70200, 9], [70500, 8], [70800, 8], [71100, 8], [71400, 7], [71700, 7], [72000, 6], [72300, 6], [72600, 5], [72900, 5], [73200, 5], [73500, 4], [73800, 4], [74100, 3], [74400, 3], [74700, 3], [75000, 2], [75300, 2], [75600, 1], [75900, 1], [76200, 1]], "point": [59, 180]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-01.46|+00.78|-02.00"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 107, 120, 255], "mask": [[31800, 9], [32100, 19], [32400, 29], [32700, 39], [33000, 50], [33300, 65], [33600, 82], [33900, 109], [34200, 109], [34500, 110], [34800, 110], [35100, 110], [35400, 110], [35700, 110], [36000, 110], [36300, 110], [36600, 110], [36900, 111], [37200, 111], [37500, 111], [37800, 111], [38100, 111], [38400, 111], [38700, 111], [39000, 112], [39300, 112], [39600, 112], [39900, 112], [40200, 112], [40500, 112], [40800, 112], [41100, 113], [41400, 91], [41496, 17], [41700, 91], [41796, 17], [42000, 91], [42096, 17], [42300, 91], [42396, 17], [42600, 92], [42696, 17], [42900, 76], [42990, 2], [42997, 16], [43200, 74], [43291, 1], [43297, 10], [43309, 4], [43500, 72], [43597, 9], [43610, 4], [43800, 70], [43897, 8], [43910, 4], [44100, 68], [44199, 7], [44210, 4], [44400, 67], [44500, 6], [44511, 3], [44700, 65], [44801, 5], [44811, 3], [45000, 65], [45102, 4], [45111, 3], [45300, 64], [45403, 3], [45411, 3], [45600, 64], [45703, 3], [45711, 4], [45900, 64], [46003, 3], [46012, 3], [46200, 42], [46303, 3], [46313, 2], [46500, 42], [46604, 2], [46801, 62], [46904, 1], [47102, 60], [47402, 60], [47703, 59], [48000, 1], [48003, 60], [48300, 1], [48304, 60], [48600, 2], [48605, 60], [48900, 3], [48905, 61], [49200, 3], [49206, 61], [49290, 2], [49500, 4], [49506, 62], [49589, 3], [49800, 4], [49807, 62], [49887, 5], [50100, 5], [50107, 63], [50185, 8], [50400, 6], [50408, 85], [50700, 6], [50709, 84], [51000, 7], [51009, 84], [51300, 7], [51310, 84], [51600, 8], [51610, 84], [51900, 9], [51911, 83], [52005, 1], [52200, 9], [52212, 83], [52305, 1], [52500, 10], [52512, 84], [52603, 3], [52800, 10], [52813, 86], [52900, 6], [53100, 11], [53113, 93], [53400, 12], [53414, 92], [53700, 12], [53715, 92], [54000, 13], [54015, 59], [54094, 13], [54300, 13], [54316, 57], [54394, 14], [54417, 2], [54600, 14], [54616, 57], [54694, 15], [54716, 3], [54900, 15], [54917, 56], [54994, 25], [55200, 15], [55217, 56], [55294, 25], [55500, 16], [55518, 54], [55593, 26], [55800, 16], [55819, 53], [55893, 26], [56100, 17], [56119, 53], [56193, 26], [56400, 17], [56420, 52], [56493, 27], [56700, 18], [56720, 53], [56793, 27], [57000, 19], [57021, 99], [57300, 19], [57322, 98], [57600, 20], [57622, 98], [57900, 20], [57923, 97], [58200, 21], [58223, 97], [58500, 22], [58524, 97], [58800, 22], [59100, 23], [59400, 23], [59700, 23], [60000, 23], [60300, 22], [60600, 22], [60900, 21], [61200, 21], [61500, 21], [61800, 20], [62100, 20], [62400, 19], [62700, 19], [63000, 19], [63300, 18], [63600, 18], [63900, 17], [64200, 17], [64500, 17], [64800, 16], [65100, 16], [65400, 15], [65700, 15], [66000, 14], [66300, 14], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 12], [68100, 12], [68400, 11], [68700, 11], [69000, 10], [69300, 10], [69600, 10], [69900, 9], [70200, 9], [70500, 8], [70800, 8], [71100, 8], [71400, 7], [71700, 7], [72000, 6], [72300, 6], [72600, 5], [72900, 5], [73200, 5], [73500, 4], [73800, 4], [74100, 3], [74400, 3], [74700, 3], [75000, 2], [75300, 2], [75600, 1], [75900, 1], [76200, 1]], "point": [60, 180]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan3", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -0.75, "y": 1.12401652, "z": -0.5}, "object_poses": [{"objectName": "Potato_e8912d85", "position": {"x": 1.21390212, "y": 1.35016418, "z": 0.861989737}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "DishSponge_a15664d1", "position": {"x": -1.82631838, "y": 1.050614, "z": -0.633444369}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a15664d1", "position": {"x": 0.900508642, "y": 0.344535828, "z": -1.85446763}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": -1.79031563, "y": 1.32392883, "z": -1.68147337}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": -1.363688, "y": 1.32392883, "z": -0.8012903}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": -0.749, "y": 1.3324, "z": -2.973}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": 0.896088362, "y": 1.32354856, "z": -0.942471862}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": -1.77246583, "y": 0.420569718, "z": -1.348922}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": 0.9420974, "y": 0.74398917, "z": 0.6820053}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.82652426, "y": 1.34814167, "z": -1.04922915}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": 0.6347654, "y": 1.36382449, "z": -2.67849255}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": -1.82631838, "y": 1.09187412, "z": -0.4510209}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_f306e730", "position": {"x": 0.896088362, "y": 1.32039762, "z": -2.05370474}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_f306e730", "position": {"x": -1.6980927, "y": 1.04844725, "z": -0.542232633}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": -2.04982066, "y": 1.323614, "z": -2.981163}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": 1.05303526, "y": 0.345585465, "z": -2.1643877}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": 0.6365833, "y": 1.35270047, "z": -2.05370474}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -1.73021924, "y": 0.420213044, "z": -0.233189613}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": 0.976771951, "y": 0.345163345, "z": -1.9577744}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "PepperShaker_c15f34b3", "position": {"x": -1.75051689, "y": 0.338814139, "z": 1.21397424}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": -1.66056311, "y": 1.39912164, "z": -2.72122478}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": 1.02584076, "y": 1.39912164, "z": -2.42411566}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": 1.02731991, "y": 1.20279992, "z": 1.23849058}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": 0.9710001, "y": 1.51951933, "z": 1.601027}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": 0.977774739, "y": 1.72400379, "z": 1.54570937}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": -1.61049628, "y": 0.32906872, "z": 2.151774}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": 0.420673847, "y": 1.39739525, "z": -3.12197924}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_930b560d", "position": {"x": -1.5771203, "y": 1.31039941, "z": -1.58406281}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -1.53080976, "y": 1.35270047, "z": 1.4197371}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": 0.9202187, "y": 1.52282739, "z": 1.78658581}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Potato_e8912d85", "position": {"x": -1.95454407, "y": 1.09080327, "z": -0.496626765}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": 0.77839607, "y": 1.20799184, "z": 1.23849058}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": -1.91546834, "y": 1.33600366, "z": -1.25536227}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_f306e730", "position": {"x": -1.82652426, "y": 1.33053935, "z": -0.9805181}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": -1.38003659, "y": 1.32404137, "z": -3.09262371}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": 1.07256269, "y": 1.56534374, "z": 1.477321}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pan_9d168802", "position": {"x": -1.88114953, "y": 0.327762932, "z": -2.03532434}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b13b6e3e", "position": {"x": 0.5634017, "y": 1.31800008, "z": -3.12197924}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": -2.04982138, "y": 1.32392883, "z": 1.4197371}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": -1.40105808, "y": 1.39912164, "z": -2.461287}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": 0.6365833, "y": 1.33799994, "z": -0.942471862}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_ddd73f57", "position": {"x": -1.53081059, "y": 1.42728543, "z": -1.94141138}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": 0.9777747, "y": 1.70904112, "z": 1.7224071}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": -1.79031563, "y": 1.323614, "z": 1.094923}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_c15f34b3", "position": {"x": 1.1555934, "y": 1.31800008, "z": -2.05370474}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a15664d1", "position": {"x": -1.602987, "y": 0.343378425, "z": -1.9633193}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": 0.492037773, "y": 1.32412946, "z": -2.78936434}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": 1.11572385, "y": 1.3592943, "z": 0.9650381}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": -1.0945816, "y": 1.318, "z": -3.31436586}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2963035192, "scene_num": 3}, "task_id": "trial_T20190909_074653_538268", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A3F7G1FSFWQPLE_31LVTDXBLA1Y1INV3J022TA2KNYLRV", "high_descs": ["Turn around and walk towards the oven, then turn right and walk up to the counter.", "Pick up the glass bottle off of the counter.", "Turn right and walk forward, then turn right and walk forward, then turn around.", "Open the black lower cabinet door in front of you and put the glass bottle inside, then close the cabinet.", "Walk up to the kitchen sink.", "Pick up the glass bottle off of the counter in front of the sink.", "Turn around and walk forward, then turn right and walk forward and turn right to face the counter.", "Open the black lower cabinet door in front of you and put the glass bottle inside, then close the cabinet."], "task_desc": "Move two glass bottles into a cabinet.", "votes": [1, 1, 1, 1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_39DD6S19JS2TX3ZUWE7BB1MHK6NEZZ", "high_descs": ["turn around and walk over to the kitchen counter on the left side of the kitchen sink to your right", "grab the bottle of wine off of the kitchen counter there", "turn around to back up a bit and face the kitchen counter again", "place the bottle of wine in the bottom left cabinet under the kitchen counter", "turn right and walk over to the kitchen sink on your left", "grab the bottle of wine off of the kitchen sink counter there", "turn around to back up a bit and face the kitchen counter to the left of the sink again", "place the bottle of wine inside of the kitchen cabinet to the bottom left of the sink"], "task_desc": "place two bottles of wine inside of the kitchen cabinet to the bottom left of the sink", "votes": [1, 1, 1, 1, 1]}, {"assignment_id": "A31681CCEVDIH3_3TMFV4NEPB58U7KY8O14KNWDS6G8W9", "high_descs": ["Make a right to right the countertop to the left of the sink.", "Pick up the green bottle behind the black pot.", "Turn to face the cabinet directly below the paper towel roll.", "Place the green bottle in the cabinet.", "Turn to face the sink.", "Pick up the green bottle in front of the sink.", "Turn left to face the cabinet directly below the paper towel roll.", "Put the other green bottle in the cabinet."], "task_desc": "Put two green bottles away in a cabinet below the roll of paper towels.", "votes": [1, 1, 0]}]}}