
(define (problem plan_trial_T20190908_175559_652704)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        AlarmClock_bar__plus_00_dot_36_bar__plus_00_dot_68_bar__minus_01_dot_79 - object
        BasketBall_bar__plus_01_dot_51_bar__plus_00_dot_12_bar__minus_00_dot_11 - object
        Book_bar__minus_01_dot_62_bar__plus_00_dot_68_bar__plus_00_dot_92 - object
        Book_bar__minus_01_dot_68_bar__plus_00_dot_51_bar__plus_01_dot_58 - object
        Book_bar__minus_01_dot_68_bar__plus_00_dot_67_bar__plus_01_dot_21 - object
        CD_bar__plus_01_dot_59_bar__plus_00_dot_71_bar__plus_00_dot_80 - object
        CellPhone_bar__plus_00_dot_51_bar__plus_00_dot_69_bar__minus_01_dot_66 - object
        CellPhone_bar__minus_00_dot_58_bar__plus_00_dot_54_bar__minus_00_dot_27 - object
        Chair_bar__minus_01_dot_46_bar__plus_00_dot_00_bar__plus_00_dot_93 - object
        CreditCard_bar__minus_01_dot_88_bar__plus_01_dot_07_bar__plus_00_dot_93 - object
        Curtains_bar__plus_00_dot_49_bar__plus_02_dot_04_bar__minus_01_dot_85 - object
        Curtains_bar__plus_01_dot_87_bar__plus_02_dot_04_bar__minus_00_dot_50 - object
        DeskLamp_bar__minus_01_dot_85_bar__plus_01_dot_06_bar__plus_00_dot_76 - object
        KeyChain_bar__plus_00_dot_79_bar__plus_00_dot_68_bar__minus_01_dot_71 - object
        KeyChain_bar__minus_01_dot_58_bar__plus_00_dot_29_bar__plus_01_dot_72 - object
        Laptop_bar__minus_01_dot_79_bar__plus_00_dot_63_bar__plus_00_dot_21 - object
        LightSwitch_bar__minus_00_dot_12_bar__plus_01_dot_32_bar__plus_01_dot_85 - object
        Mirror_bar__plus_00_dot_55_bar__plus_01_dot_49_bar__plus_01_dot_88 - object
        Pencil_bar__plus_01_dot_41_bar__plus_00_dot_72_bar__plus_00_dot_02 - object
        Pen_bar__plus_01_dot_59_bar__plus_00_dot_72_bar__plus_00_dot_99 - object
        Pen_bar__plus_01_dot_77_bar__plus_00_dot_72_bar__plus_00_dot_02 - object
        Pen_bar__minus_01_dot_83_bar__plus_00_dot_65_bar__plus_00_dot_45 - object
        Pillow_bar__minus_00_dot_34_bar__plus_00_dot_62_bar__minus_00_dot_55 - object
        Pillow_bar__minus_01_dot_30_bar__plus_00_dot_62_bar__minus_00_dot_69 - object
        Pillow_bar__minus_01_dot_76_bar__plus_00_dot_57_bar__minus_00_dot_77 - object
        TeddyBear_bar__minus_01_dot_70_bar__plus_00_dot_48_bar__minus_00_dot_33 - object
        TennisRacket_bar__plus_01_dot_55_bar__plus_00_dot_00_bar__plus_00_dot_65 - object
        Window_bar__plus_00_dot_48_bar__plus_01_dot_41_bar__minus_01_dot_93 - object
        Window_bar__plus_01_dot_93_bar__plus_01_dot_41_bar__minus_00_dot_51 - object
        Bed_bar__minus_01_dot_04_bar__plus_00_dot_01_bar__minus_00_dot_69 - receptacle
        Desk_bar__plus_01_dot_58_bar_00_dot_00_bar__plus_00_dot_39 - receptacle
        Desk_bar__minus_01_dot_72_bar__plus_00_dot_01_bar__plus_01_dot_11 - receptacle
        GarbageCan_bar__plus_01_dot_68_bar__plus_00_dot_00_bar__plus_01_dot_67 - receptacle
        Shelf_bar__plus_00_dot_63_bar__plus_00_dot_68_bar__minus_01_dot_71 - receptacle
        Shelf_bar__minus_01_dot_73_bar__plus_00_dot_06_bar__plus_01_dot_51 - receptacle
        Shelf_bar__minus_01_dot_73_bar__plus_00_dot_28_bar__plus_01_dot_51 - receptacle
        Shelf_bar__minus_01_dot_73_bar__plus_00_dot_50_bar__plus_01_dot_51 - receptacle
        Shelf_bar__minus_01_dot_88_bar__plus_01_dot_06_bar__plus_01_dot_19 - receptacle
        loc_bar__minus_4_bar_4_bar_3_bar_60 - location
        loc_bar_2_bar__minus_5_bar_2_bar_15 - location
        loc_bar_3_bar__minus_3_bar_2_bar_45 - location
        loc_bar_2_bar__minus_5_bar_2_bar__minus_30 - location
        loc_bar_3_bar_3_bar_1_bar_60 - location
        loc_bar_2_bar_5_bar_0_bar_15 - location
        loc_bar_5_bar__minus_3_bar_1_bar_15 - location
        loc_bar__minus_4_bar_5_bar_3_bar_60 - location
        loc_bar__minus_2_bar_4_bar_3_bar_45 - location
        loc_bar_3_bar_0_bar_1_bar_60 - location
        loc_bar__minus_3_bar_2_bar_2_bar_60 - location
        loc_bar_3_bar_2_bar_1_bar_60 - location
        loc_bar_3_bar_5_bar_1_bar_60 - location
        loc_bar_5_bar__minus_3_bar_1_bar__minus_30 - location
        loc_bar__minus_3_bar_5_bar_3_bar_60 - location
        loc_bar_0_bar_5_bar_0_bar_30 - location
        loc_bar__minus_4_bar_5_bar_3_bar_30 - location
        loc_bar__minus_4_bar_5_bar_3_bar_30 - location
        )
    

(:init


        (receptacleType Shelf_bar__minus_01_dot_73_bar__plus_00_dot_06_bar__plus_01_dot_51 ShelfType)
        (receptacleType Shelf_bar__minus_01_dot_73_bar__plus_00_dot_50_bar__plus_01_dot_51 ShelfType)
        (receptacleType Bed_bar__minus_01_dot_04_bar__plus_00_dot_01_bar__minus_00_dot_69 BedType)
        (receptacleType GarbageCan_bar__plus_01_dot_68_bar__plus_00_dot_00_bar__plus_01_dot_67 GarbageCanType)
        (receptacleType Desk_bar__minus_01_dot_72_bar__plus_00_dot_01_bar__plus_01_dot_11 DeskType)
        (receptacleType Shelf_bar__minus_01_dot_88_bar__plus_01_dot_06_bar__plus_01_dot_19 ShelfType)
        (receptacleType Shelf_bar__minus_01_dot_73_bar__plus_00_dot_28_bar__plus_01_dot_51 ShelfType)
        (receptacleType Shelf_bar__plus_00_dot_63_bar__plus_00_dot_68_bar__minus_01_dot_71 ShelfType)
        (receptacleType Desk_bar__plus_01_dot_58_bar_00_dot_00_bar__plus_00_dot_39 DeskType)
        (objectType Laptop_bar__minus_01_dot_79_bar__plus_00_dot_63_bar__plus_00_dot_21 LaptopType)
        (objectType Pencil_bar__plus_01_dot_41_bar__plus_00_dot_72_bar__plus_00_dot_02 PencilType)
        (objectType Book_bar__minus_01_dot_62_bar__plus_00_dot_68_bar__plus_00_dot_92 BookType)
        (objectType Book_bar__minus_01_dot_68_bar__plus_00_dot_51_bar__plus_01_dot_58 BookType)
        (objectType KeyChain_bar__minus_01_dot_58_bar__plus_00_dot_29_bar__plus_01_dot_72 KeyChainType)
        (objectType Mirror_bar__plus_00_dot_55_bar__plus_01_dot_49_bar__plus_01_dot_88 MirrorType)
        (objectType Pen_bar__minus_01_dot_83_bar__plus_00_dot_65_bar__plus_00_dot_45 PenType)
        (objectType Pillow_bar__minus_01_dot_76_bar__plus_00_dot_57_bar__minus_00_dot_77 PillowType)
        (objectType AlarmClock_bar__plus_00_dot_36_bar__plus_00_dot_68_bar__minus_01_dot_79 AlarmClockType)
        (objectType KeyChain_bar__plus_00_dot_79_bar__plus_00_dot_68_bar__minus_01_dot_71 KeyChainType)
        (objectType TeddyBear_bar__minus_01_dot_70_bar__plus_00_dot_48_bar__minus_00_dot_33 TeddyBearType)
        (objectType Curtains_bar__plus_01_dot_87_bar__plus_02_dot_04_bar__minus_00_dot_50 CurtainsType)
        (objectType Curtains_bar__plus_00_dot_49_bar__plus_02_dot_04_bar__minus_01_dot_85 CurtainsType)
        (objectType TennisRacket_bar__plus_01_dot_55_bar__plus_00_dot_00_bar__plus_00_dot_65 TennisRacketType)
        (objectType CreditCard_bar__minus_01_dot_88_bar__plus_01_dot_07_bar__plus_00_dot_93 CreditCardType)
        (objectType Pillow_bar__minus_01_dot_30_bar__plus_00_dot_62_bar__minus_00_dot_69 PillowType)
        (objectType BasketBall_bar__plus_01_dot_51_bar__plus_00_dot_12_bar__minus_00_dot_11 BasketBallType)
        (objectType CellPhone_bar__plus_00_dot_51_bar__plus_00_dot_69_bar__minus_01_dot_66 CellPhoneType)
        (objectType Book_bar__minus_01_dot_68_bar__plus_00_dot_67_bar__plus_01_dot_21 BookType)
        (objectType Window_bar__plus_00_dot_48_bar__plus_01_dot_41_bar__minus_01_dot_93 WindowType)
        (objectType Chair_bar__minus_01_dot_46_bar__plus_00_dot_00_bar__plus_00_dot_93 ChairType)
        (objectType Window_bar__plus_01_dot_93_bar__plus_01_dot_41_bar__minus_00_dot_51 WindowType)
        (objectType CD_bar__plus_01_dot_59_bar__plus_00_dot_71_bar__plus_00_dot_80 CDType)
        (objectType DeskLamp_bar__minus_01_dot_85_bar__plus_01_dot_06_bar__plus_00_dot_76 DeskLampType)
        (objectType LightSwitch_bar__minus_00_dot_12_bar__plus_01_dot_32_bar__plus_01_dot_85 LightSwitchType)
        (objectType Pen_bar__plus_01_dot_77_bar__plus_00_dot_72_bar__plus_00_dot_02 PenType)
        (objectType Pen_bar__plus_01_dot_59_bar__plus_00_dot_72_bar__plus_00_dot_99 PenType)
        (objectType Pillow_bar__minus_00_dot_34_bar__plus_00_dot_62_bar__minus_00_dot_55 PillowType)
        (objectType CellPhone_bar__minus_00_dot_58_bar__plus_00_dot_54_bar__minus_00_dot_27 CellPhoneType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain BedType BasketBallType)
        (canContain BedType TennisRacketType)
        (canContain BedType LaptopType)
        (canContain BedType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType PillowType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType CDType)
        (canContain GarbageCanType PencilType)
        (canContain DeskType PenType)
        (canContain DeskType BookType)
        (canContain DeskType CDType)
        (canContain DeskType CellPhoneType)
        (canContain DeskType KeyChainType)
        (canContain DeskType CreditCardType)
        (canContain DeskType BasketBallType)
        (canContain DeskType TennisRacketType)
        (canContain DeskType LaptopType)
        (canContain DeskType PencilType)
        (canContain DeskType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain DeskType PenType)
        (canContain DeskType BookType)
        (canContain DeskType CDType)
        (canContain DeskType CellPhoneType)
        (canContain DeskType KeyChainType)
        (canContain DeskType CreditCardType)
        (canContain DeskType BasketBallType)
        (canContain DeskType TennisRacketType)
        (canContain DeskType LaptopType)
        (canContain DeskType PencilType)
        (canContain DeskType AlarmClockType)
        (pickupable Laptop_bar__minus_01_dot_79_bar__plus_00_dot_63_bar__plus_00_dot_21)
        (pickupable Pencil_bar__plus_01_dot_41_bar__plus_00_dot_72_bar__plus_00_dot_02)
        (pickupable Book_bar__minus_01_dot_62_bar__plus_00_dot_68_bar__plus_00_dot_92)
        (pickupable Book_bar__minus_01_dot_68_bar__plus_00_dot_51_bar__plus_01_dot_58)
        (pickupable KeyChain_bar__minus_01_dot_58_bar__plus_00_dot_29_bar__plus_01_dot_72)
        (pickupable Pen_bar__minus_01_dot_83_bar__plus_00_dot_65_bar__plus_00_dot_45)
        (pickupable Pillow_bar__minus_01_dot_76_bar__plus_00_dot_57_bar__minus_00_dot_77)
        (pickupable AlarmClock_bar__plus_00_dot_36_bar__plus_00_dot_68_bar__minus_01_dot_79)
        (pickupable KeyChain_bar__plus_00_dot_79_bar__plus_00_dot_68_bar__minus_01_dot_71)
        (pickupable TeddyBear_bar__minus_01_dot_70_bar__plus_00_dot_48_bar__minus_00_dot_33)
        (pickupable TennisRacket_bar__plus_01_dot_55_bar__plus_00_dot_00_bar__plus_00_dot_65)
        (pickupable CreditCard_bar__minus_01_dot_88_bar__plus_01_dot_07_bar__plus_00_dot_93)
        (pickupable Pillow_bar__minus_01_dot_30_bar__plus_00_dot_62_bar__minus_00_dot_69)
        (pickupable BasketBall_bar__plus_01_dot_51_bar__plus_00_dot_12_bar__minus_00_dot_11)
        (pickupable CellPhone_bar__plus_00_dot_51_bar__plus_00_dot_69_bar__minus_01_dot_66)
        (pickupable Book_bar__minus_01_dot_68_bar__plus_00_dot_67_bar__plus_01_dot_21)
        (pickupable CD_bar__plus_01_dot_59_bar__plus_00_dot_71_bar__plus_00_dot_80)
        (pickupable Pen_bar__plus_01_dot_77_bar__plus_00_dot_72_bar__plus_00_dot_02)
        (pickupable Pen_bar__plus_01_dot_59_bar__plus_00_dot_72_bar__plus_00_dot_99)
        (pickupable Pillow_bar__minus_00_dot_34_bar__plus_00_dot_62_bar__minus_00_dot_55)
        (pickupable CellPhone_bar__minus_00_dot_58_bar__plus_00_dot_54_bar__minus_00_dot_27)
        
        
        
        (atLocation agent1 loc_bar__minus_4_bar_5_bar_3_bar_30)
        
        
        
        
        
        
        
        (toggleable DeskLamp_bar__minus_01_dot_85_bar__plus_01_dot_06_bar__plus_00_dot_76)
        
        
        
        
        (inReceptacle Pen_bar__plus_01_dot_77_bar__plus_00_dot_72_bar__plus_00_dot_02 Desk_bar__plus_01_dot_58_bar_00_dot_00_bar__plus_00_dot_39)
        (inReceptacle Pencil_bar__plus_01_dot_41_bar__plus_00_dot_72_bar__plus_00_dot_02 Desk_bar__plus_01_dot_58_bar_00_dot_00_bar__plus_00_dot_39)
        (inReceptacle CD_bar__plus_01_dot_59_bar__plus_00_dot_71_bar__plus_00_dot_80 Desk_bar__plus_01_dot_58_bar_00_dot_00_bar__plus_00_dot_39)
        (inReceptacle Pen_bar__plus_01_dot_59_bar__plus_00_dot_72_bar__plus_00_dot_99 Desk_bar__plus_01_dot_58_bar_00_dot_00_bar__plus_00_dot_39)
        (inReceptacle Laptop_bar__minus_01_dot_79_bar__plus_00_dot_63_bar__plus_00_dot_21 Desk_bar__minus_01_dot_72_bar__plus_00_dot_01_bar__plus_01_dot_11)
        (inReceptacle Pen_bar__minus_01_dot_83_bar__plus_00_dot_65_bar__plus_00_dot_45 Desk_bar__minus_01_dot_72_bar__plus_00_dot_01_bar__plus_01_dot_11)
        (inReceptacle Book_bar__minus_01_dot_62_bar__plus_00_dot_68_bar__plus_00_dot_92 Desk_bar__minus_01_dot_72_bar__plus_00_dot_01_bar__plus_01_dot_11)
        (inReceptacle Book_bar__minus_01_dot_68_bar__plus_00_dot_67_bar__plus_01_dot_21 Desk_bar__minus_01_dot_72_bar__plus_00_dot_01_bar__plus_01_dot_11)
        (inReceptacle TeddyBear_bar__minus_01_dot_70_bar__plus_00_dot_48_bar__minus_00_dot_33 Bed_bar__minus_01_dot_04_bar__plus_00_dot_01_bar__minus_00_dot_69)
        (inReceptacle Pillow_bar__minus_01_dot_76_bar__plus_00_dot_57_bar__minus_00_dot_77 Bed_bar__minus_01_dot_04_bar__plus_00_dot_01_bar__minus_00_dot_69)
        (inReceptacle Pillow_bar__minus_00_dot_34_bar__plus_00_dot_62_bar__minus_00_dot_55 Bed_bar__minus_01_dot_04_bar__plus_00_dot_01_bar__minus_00_dot_69)
        (inReceptacle Pillow_bar__minus_01_dot_30_bar__plus_00_dot_62_bar__minus_00_dot_69 Bed_bar__minus_01_dot_04_bar__plus_00_dot_01_bar__minus_00_dot_69)
        (inReceptacle CellPhone_bar__minus_00_dot_58_bar__plus_00_dot_54_bar__minus_00_dot_27 Bed_bar__minus_01_dot_04_bar__plus_00_dot_01_bar__minus_00_dot_69)
        (inReceptacle CellPhone_bar__plus_00_dot_51_bar__plus_00_dot_69_bar__minus_01_dot_66 Shelf_bar__plus_00_dot_63_bar__plus_00_dot_68_bar__minus_01_dot_71)
        (inReceptacle KeyChain_bar__plus_00_dot_79_bar__plus_00_dot_68_bar__minus_01_dot_71 Shelf_bar__plus_00_dot_63_bar__plus_00_dot_68_bar__minus_01_dot_71)
        (inReceptacle AlarmClock_bar__plus_00_dot_36_bar__plus_00_dot_68_bar__minus_01_dot_79 Shelf_bar__plus_00_dot_63_bar__plus_00_dot_68_bar__minus_01_dot_71)
        (inReceptacle Book_bar__minus_01_dot_68_bar__plus_00_dot_51_bar__plus_01_dot_58 Shelf_bar__minus_01_dot_73_bar__plus_00_dot_50_bar__plus_01_dot_51)
        (inReceptacle CreditCard_bar__minus_01_dot_88_bar__plus_01_dot_07_bar__plus_00_dot_93 Shelf_bar__minus_01_dot_88_bar__plus_01_dot_06_bar__plus_01_dot_19)
        (inReceptacle DeskLamp_bar__minus_01_dot_85_bar__plus_01_dot_06_bar__plus_00_dot_76 Shelf_bar__minus_01_dot_88_bar__plus_01_dot_06_bar__plus_01_dot_19)
        (inReceptacle KeyChain_bar__minus_01_dot_58_bar__plus_00_dot_29_bar__plus_01_dot_72 Shelf_bar__minus_01_dot_73_bar__plus_00_dot_28_bar__plus_01_dot_51)
        
        
        (receptacleAtLocation Bed_bar__minus_01_dot_04_bar__plus_00_dot_01_bar__minus_00_dot_69 loc_bar__minus_3_bar_2_bar_2_bar_60)
        (receptacleAtLocation Desk_bar__plus_01_dot_58_bar_00_dot_00_bar__plus_00_dot_39 loc_bar_3_bar_2_bar_1_bar_60)
        (receptacleAtLocation Desk_bar__minus_01_dot_72_bar__plus_00_dot_01_bar__plus_01_dot_11 loc_bar__minus_4_bar_5_bar_3_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_01_dot_68_bar__plus_00_dot_00_bar__plus_01_dot_67 loc_bar_3_bar_5_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__plus_00_dot_63_bar__plus_00_dot_68_bar__minus_01_dot_71 loc_bar_3_bar__minus_3_bar_2_bar_45)
        (receptacleAtLocation Shelf_bar__minus_01_dot_73_bar__plus_00_dot_06_bar__plus_01_dot_51 loc_bar__minus_2_bar_4_bar_3_bar_45)
        (receptacleAtLocation Shelf_bar__minus_01_dot_73_bar__plus_00_dot_28_bar__plus_01_dot_51 loc_bar__minus_3_bar_5_bar_3_bar_60)
        (receptacleAtLocation Shelf_bar__minus_01_dot_73_bar__plus_00_dot_50_bar__plus_01_dot_51 loc_bar__minus_2_bar_4_bar_3_bar_45)
        (receptacleAtLocation Shelf_bar__minus_01_dot_88_bar__plus_01_dot_06_bar__plus_01_dot_19 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation Pillow_bar__minus_01_dot_76_bar__plus_00_dot_57_bar__minus_00_dot_77 loc_bar__minus_3_bar_2_bar_2_bar_60)
        (objectAtLocation CellPhone_bar__minus_00_dot_58_bar__plus_00_dot_54_bar__minus_00_dot_27 loc_bar__minus_3_bar_2_bar_2_bar_60)
        (objectAtLocation KeyChain_bar__minus_01_dot_58_bar__plus_00_dot_29_bar__plus_01_dot_72 loc_bar__minus_3_bar_5_bar_3_bar_60)
        (objectAtLocation Pen_bar__plus_01_dot_59_bar__plus_00_dot_72_bar__plus_00_dot_99 loc_bar_3_bar_2_bar_1_bar_60)
        (objectAtLocation Book_bar__minus_01_dot_68_bar__plus_00_dot_51_bar__plus_01_dot_58 loc_bar__minus_2_bar_4_bar_3_bar_45)
        (objectAtLocation Pillow_bar__minus_00_dot_34_bar__plus_00_dot_62_bar__minus_00_dot_55 loc_bar__minus_3_bar_2_bar_2_bar_60)
        (objectAtLocation Book_bar__minus_01_dot_62_bar__plus_00_dot_68_bar__plus_00_dot_92 loc_bar__minus_4_bar_5_bar_3_bar_60)
        (objectAtLocation Pen_bar__plus_01_dot_77_bar__plus_00_dot_72_bar__plus_00_dot_02 loc_bar_3_bar_2_bar_1_bar_60)
        (objectAtLocation Chair_bar__minus_01_dot_46_bar__plus_00_dot_00_bar__plus_00_dot_93 loc_bar__minus_4_bar_4_bar_3_bar_60)
        (objectAtLocation Curtains_bar__plus_01_dot_87_bar__plus_02_dot_04_bar__minus_00_dot_50 loc_bar_5_bar__minus_3_bar_1_bar__minus_30)
        (objectAtLocation Book_bar__minus_01_dot_68_bar__plus_00_dot_67_bar__plus_01_dot_21 loc_bar__minus_4_bar_5_bar_3_bar_60)
        (objectAtLocation Mirror_bar__plus_00_dot_55_bar__plus_01_dot_49_bar__plus_01_dot_88 loc_bar_2_bar_5_bar_0_bar_15)
        (objectAtLocation Curtains_bar__plus_00_dot_49_bar__plus_02_dot_04_bar__minus_01_dot_85 loc_bar_2_bar__minus_5_bar_2_bar__minus_30)
        (objectAtLocation AlarmClock_bar__plus_00_dot_36_bar__plus_00_dot_68_bar__minus_01_dot_79 loc_bar_3_bar__minus_3_bar_2_bar_45)
        (objectAtLocation BasketBall_bar__plus_01_dot_51_bar__plus_00_dot_12_bar__minus_00_dot_11 loc_bar_3_bar_0_bar_1_bar_60)
        (objectAtLocation LightSwitch_bar__minus_00_dot_12_bar__plus_01_dot_32_bar__plus_01_dot_85 loc_bar_0_bar_5_bar_0_bar_30)
        (objectAtLocation TeddyBear_bar__minus_01_dot_70_bar__plus_00_dot_48_bar__minus_00_dot_33 loc_bar__minus_3_bar_2_bar_2_bar_60)
        (objectAtLocation TennisRacket_bar__plus_01_dot_55_bar__plus_00_dot_00_bar__plus_00_dot_65 loc_bar_3_bar_3_bar_1_bar_60)
        (objectAtLocation Pen_bar__minus_01_dot_83_bar__plus_00_dot_65_bar__plus_00_dot_45 loc_bar__minus_4_bar_5_bar_3_bar_60)
        (objectAtLocation Pencil_bar__plus_01_dot_41_bar__plus_00_dot_72_bar__plus_00_dot_02 loc_bar_3_bar_2_bar_1_bar_60)
        (objectAtLocation KeyChain_bar__plus_00_dot_79_bar__plus_00_dot_68_bar__minus_01_dot_71 loc_bar_3_bar__minus_3_bar_2_bar_45)
        (objectAtLocation DeskLamp_bar__minus_01_dot_85_bar__plus_01_dot_06_bar__plus_00_dot_76 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation CreditCard_bar__minus_01_dot_88_bar__plus_01_dot_07_bar__plus_00_dot_93 loc_bar__minus_4_bar_5_bar_3_bar_30)
        (objectAtLocation Laptop_bar__minus_01_dot_79_bar__plus_00_dot_63_bar__plus_00_dot_21 loc_bar__minus_4_bar_5_bar_3_bar_60)
        (objectAtLocation CellPhone_bar__plus_00_dot_51_bar__plus_00_dot_69_bar__minus_01_dot_66 loc_bar_3_bar__minus_3_bar_2_bar_45)
        (objectAtLocation Pillow_bar__minus_01_dot_30_bar__plus_00_dot_62_bar__minus_00_dot_69 loc_bar__minus_3_bar_2_bar_2_bar_60)
        (objectAtLocation CD_bar__plus_01_dot_59_bar__plus_00_dot_71_bar__plus_00_dot_80 loc_bar_3_bar_2_bar_1_bar_60)
        (objectAtLocation Window_bar__plus_01_dot_93_bar__plus_01_dot_41_bar__minus_00_dot_51 loc_bar_5_bar__minus_3_bar_1_bar_15)
        (objectAtLocation Window_bar__plus_00_dot_48_bar__plus_01_dot_41_bar__minus_01_dot_93 loc_bar_2_bar__minus_5_bar_2_bar_15)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 PenType)
                                    (receptacleType ?r ShelfType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 PenType)
                                            (receptacleType ?r ShelfType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            