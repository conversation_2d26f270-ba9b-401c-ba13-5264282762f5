{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000267.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000268.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000269.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000270.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000271.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000272.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000273.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000274.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000275.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000276.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000277.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000278.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000279.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000280.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000281.png", "low_idx": 55}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "DiningTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-7|-13|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-6.82909964, -6.82909964, -14.9888, -14.9888, 3.305827856, 3.305827856]], "coordinateReceptacleObjectId": ["SinkBasin", [-7.1452, -7.1452, -14.9888, -14.9888, 3.12, 3.12]], "forceVisible": true, "objectId": "Egg|-01.71|+00.83|-03.75"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-3|1|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-0.576, -0.576, -3.632, -3.632, 3.615618228, 3.615618228]], "forceVisible": true, "objectId": "Microwave|-00.14|+00.90|-00.91"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-11|-6|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "diningtable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-6.82909964, -6.82909964, -14.9888, -14.9888, 3.305827856, 3.305827856]], "coordinateReceptacleObjectId": ["DiningTable", [-11.476, -11.476, -2.024, -2.024, 2.708, 2.708]], "forceVisible": true, "objectId": "Egg|-01.71|+00.83|-03.75", "receptacleObjectId": "DiningTable|-02.87|+00.68|-00.51"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.71|+00.83|-03.75"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [130, 127, 144, 144], "mask": [[37935, 5], [38233, 9], [38532, 11], [38832, 11], [39131, 13], [39431, 14], [39730, 15], [40030, 15], [40330, 15], [40630, 15], [40930, 15], [41230, 15], [41531, 14], [41831, 13], [42132, 12], [42433, 10], [42734, 8], [43036, 4]], "point": [137, 134]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.71|+00.83|-03.75", "placeStationary": true, "receptacleObjectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [59, 46, 299, 242], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17795, 205], [18094, 206], [18394, 206], [18694, 206], [18993, 207], [19293, 207], [19593, 207], [19892, 208], [20192, 208], [20492, 208], [20791, 209], [21091, 209], [21391, 209], [21690, 210], [21990, 83], [22079, 121], [22290, 80], [22382, 118], [22589, 80], [22683, 117], [22889, 79], [22984, 116], [23189, 78], [23285, 114], [23488, 79], [23585, 114], [23788, 78], [23886, 112], [24088, 78], [24186, 112], [24387, 79], [24486, 112], [24687, 79], [24786, 111], [24987, 79], [25086, 111], [25286, 80], [25386, 110], [25586, 80], [25686, 110], [25886, 81], [25985, 110], [26185, 82], [26285, 110], [26485, 83], [26584, 110], [26785, 84], [26883, 111], [27084, 86], [27182, 111], [27384, 87], [27481, 112], [27684, 90], [27779, 114], [27983, 209], [28283, 209], [28583, 208], [28883, 208], [29182, 208], [29482, 208], [29782, 207], [30081, 208], [30381, 208], [30681, 207], [30980, 208], [31280, 207], [31580, 207], [31879, 207], [32179, 207], [32479, 206], [32778, 207], [33078, 206], [33378, 206], [33677, 207], [33977, 206], [34277, 206], [34576, 206], [34876, 206], [35176, 205], [35475, 206], [35775, 205], [36075, 205], [36374, 206], [36674, 205], [36974, 205], [37273, 205], [37573, 205], [37873, 204], [38172, 205], [38472, 201], [38772, 201], [39071, 48], [39239, 34], [39371, 48], [39539, 34], [39671, 48], [39840, 33], [39970, 48], [40140, 32], [40270, 48], [40440, 32], [40570, 48], [40741, 30], [40869, 49], [41169, 49], [41469, 49], [41768, 50], [42068, 49], [42368, 49], [42667, 50], [42967, 50], [43267, 50], [43566, 51], [43866, 51], [44166, 51], [44465, 51], [44765, 51], [45065, 51], [45365, 51], [45664, 52], [45964, 52], [46264, 52], [46563, 53], [46863, 52], [47163, 52], [47462, 53], [47762, 53], [48062, 53], [48361, 54], [48661, 54], [48961, 53], [49260, 54], [49560, 54], [49860, 54], [50159, 55], [50460, 54], [50760, 54], [51060, 54], [51361, 52], [51661, 52], [51961, 52], [52262, 51], [52563, 50], [52864, 49], [53166, 47], [53468, 45], [53769, 43], [54071, 41], [54372, 40], [54674, 38], [54974, 38], [55275, 37], [55575, 37], [55875, 36], [56176, 35], [56476, 35], [56776, 35], [57077, 34], [57377, 34], [57677, 34], [57978, 33], [58278, 32], [58578, 32], [58879, 31], [59179, 31], [59479, 31], [59780, 30], [60080, 30], [60380, 30], [60681, 28], [60981, 28], [61281, 28], [61582, 27], [61882, 27], [62182, 27], [62483, 26], [62783, 26], [63083, 25], [63384, 24], [63684, 24], [63984, 24], [64285, 23], [64585, 23], [64885, 23], [65186, 21], [65486, 21], [65786, 21], [66087, 20], [66387, 20], [66687, 20], [66988, 19], [67288, 19], [67587, 19], [67887, 19], [68187, 19], [68487, 19], [68787, 19], [69086, 20], [69386, 20], [69686, 20], [69986, 22], [70286, 25], [70586, 26], [70885, 20], [70908, 4], [71185, 20], [71208, 5], [71486, 19], [71509, 3], [71786, 19], [72087, 17], [72387, 17]], "point": [179, 129]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [59, 46, 299, 242], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17795, 205], [18094, 206], [18394, 206], [18694, 206], [18993, 207], [19293, 207], [19593, 207], [19892, 208], [20192, 208], [20492, 208], [20791, 209], [21091, 209], [21391, 209], [21690, 210], [21990, 83], [22079, 121], [22290, 80], [22382, 118], [22589, 80], [22683, 117], [22889, 79], [22984, 116], [23189, 78], [23285, 114], [23488, 79], [23585, 114], [23788, 78], [23886, 112], [24088, 78], [24186, 112], [24387, 79], [24486, 112], [24687, 79], [24786, 111], [24987, 79], [25086, 111], [25286, 80], [25386, 110], [25586, 80], [25686, 110], [25886, 81], [25985, 110], [26185, 82], [26285, 110], [26485, 83], [26584, 110], [26785, 78], [26867, 2], [26883, 111], [27084, 77], [27168, 2], [27182, 111], [27384, 76], [27469, 2], [27481, 112], [27684, 76], [27770, 4], [27779, 114], [27983, 76], [28071, 121], [28283, 76], [28371, 121], [28583, 75], [28671, 120], [28883, 75], [28972, 119], [29182, 76], [29272, 118], [29482, 75], [29572, 118], [29782, 75], [29872, 117], [30081, 76], [30172, 117], [30381, 76], [30472, 117], [30681, 76], [30772, 116], [30980, 78], [31072, 116], [31280, 78], [31371, 116], [31580, 78], [31671, 116], [31879, 80], [31970, 116], [32179, 80], [32270, 116], [32479, 81], [32569, 116], [32778, 84], [32867, 118], [33078, 206], [33378, 206], [33677, 207], [33977, 206], [34277, 206], [34576, 206], [34876, 206], [35176, 205], [35475, 206], [35775, 205], [36075, 205], [36374, 206], [36674, 205], [36974, 205], [37273, 205], [37573, 205], [37873, 204], [38172, 205], [38472, 201], [38772, 201], [39071, 48], [39239, 34], [39371, 48], [39539, 34], [39671, 48], [39840, 33], [39970, 48], [40140, 32], [40270, 48], [40440, 32], [40570, 48], [40741, 30], [40869, 49], [41169, 49], [41469, 49], [41768, 50], [42068, 49], [42368, 49], [42667, 50], [42967, 50], [43267, 50], [43566, 51], [43866, 51], [44166, 51], [44465, 51], [44765, 51], [45065, 51], [45365, 51], [45664, 52], [45964, 52], [46264, 52], [46563, 53], [46863, 52], [47163, 52], [47462, 53], [47762, 53], [48062, 53], [48361, 54], [48661, 54], [48961, 53], [49260, 54], [49560, 54], [49860, 54], [50159, 55], [50460, 54], [50760, 54], [51060, 54], [51361, 52], [51661, 52], [51961, 52], [52262, 51], [52563, 50], [52864, 49], [53166, 47], [53468, 45], [53769, 43], [54071, 41], [54372, 40], [54674, 38], [54974, 38], [55275, 37], [55575, 37], [55875, 36], [56176, 35], [56476, 35], [56776, 35], [57077, 34], [57377, 34], [57677, 34], [57978, 33], [58278, 32], [58578, 32], [58879, 31], [59179, 31], [59479, 31], [59780, 30], [60080, 30], [60380, 30], [60681, 28], [60981, 28], [61281, 28], [61582, 27], [61882, 27], [62182, 27], [62483, 26], [62783, 26], [63083, 25], [63384, 24], [63684, 24], [63984, 24], [64285, 23], [64585, 23], [64885, 23], [65186, 21], [65486, 21], [65786, 21], [66087, 20], [66387, 20], [66687, 20], [66988, 19], [67288, 19], [67587, 19], [67887, 19], [68187, 19], [68487, 19], [68787, 19], [69086, 20], [69386, 20], [69686, 20], [69986, 22], [70286, 25], [70586, 26], [70885, 20], [70908, 4], [71185, 20], [71208, 5], [71486, 19], [71509, 3], [71786, 19], [72087, 17], [72387, 17]], "point": [179, 129]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-0.576, -0.576, -3.632, -3.632, 3.615618228, 3.615618228]], "forceVisible": true, "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-0.576, -0.576, -3.632, -3.632, 3.615618228, 3.615618228]], "forceVisible": true, "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.71|+00.83|-03.75"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [157, 90, 171, 110], "mask": [[26863, 4], [27161, 7], [27460, 9], [27760, 10], [28059, 12], [28359, 12], [28658, 13], [28958, 14], [29258, 14], [29557, 15], [29857, 15], [30157, 15], [30457, 15], [30757, 15], [31058, 14], [31358, 13], [31658, 13], [31959, 11], [32259, 11], [32560, 9], [32862, 5]], "point": [164, 99]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [59, 46, 299, 242], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17795, 205], [18094, 206], [18394, 206], [18694, 206], [18993, 207], [19293, 207], [19593, 207], [19892, 208], [20192, 208], [20492, 208], [20791, 209], [21091, 209], [21391, 209], [21690, 210], [21990, 83], [22079, 121], [22290, 80], [22382, 118], [22589, 80], [22683, 117], [22889, 79], [22984, 116], [23189, 78], [23285, 114], [23488, 79], [23585, 114], [23788, 78], [23886, 112], [24088, 78], [24186, 112], [24387, 79], [24486, 112], [24687, 79], [24786, 111], [24987, 79], [25086, 111], [25286, 80], [25386, 110], [25586, 80], [25686, 110], [25886, 81], [25985, 110], [26185, 82], [26285, 110], [26485, 83], [26584, 110], [26785, 84], [26883, 111], [27084, 86], [27182, 111], [27384, 87], [27481, 112], [27684, 90], [27779, 114], [27983, 209], [28283, 209], [28583, 208], [28883, 208], [29182, 208], [29482, 208], [29782, 207], [30081, 208], [30381, 208], [30681, 207], [30980, 208], [31280, 207], [31580, 207], [31879, 207], [32179, 207], [32479, 206], [32778, 207], [33078, 206], [33378, 206], [33677, 207], [33977, 206], [34277, 206], [34576, 206], [34876, 206], [35176, 205], [35475, 206], [35775, 205], [36075, 205], [36374, 206], [36674, 205], [36974, 205], [37273, 205], [37573, 205], [37873, 204], [38172, 205], [38472, 201], [38772, 201], [39071, 48], [39239, 34], [39371, 48], [39539, 34], [39671, 48], [39840, 33], [39970, 48], [40140, 32], [40270, 48], [40440, 32], [40570, 48], [40741, 30], [40869, 49], [41169, 49], [41469, 49], [41768, 50], [42068, 49], [42368, 49], [42667, 50], [42967, 50], [43267, 50], [43566, 51], [43866, 51], [44166, 51], [44465, 51], [44765, 51], [45065, 51], [45365, 51], [45664, 52], [45964, 52], [46264, 52], [46563, 53], [46863, 52], [47163, 52], [47462, 53], [47762, 53], [48062, 53], [48361, 54], [48661, 54], [48961, 53], [49260, 54], [49560, 54], [49860, 54], [50159, 55], [50460, 54], [50760, 54], [51060, 54], [51361, 52], [51661, 52], [51961, 52], [52262, 51], [52563, 50], [52864, 49], [53166, 47], [53468, 45], [53769, 43], [54071, 41], [54372, 40], [54674, 38], [54974, 38], [55275, 37], [55575, 37], [55875, 36], [56176, 35], [56476, 35], [56776, 35], [57077, 34], [57377, 34], [57677, 34], [57978, 33], [58278, 32], [58578, 32], [58879, 31], [59179, 31], [59479, 31], [59780, 30], [60080, 30], [60380, 30], [60681, 28], [60981, 28], [61281, 28], [61582, 27], [61882, 27], [62182, 27], [62483, 26], [62783, 26], [63083, 25], [63384, 24], [63684, 24], [63984, 24], [64285, 23], [64585, 23], [64885, 23], [65186, 21], [65486, 21], [65786, 21], [66087, 20], [66387, 20], [66687, 20], [66988, 19], [67288, 19], [67587, 19], [67887, 19], [68187, 19], [68487, 19], [68787, 19], [69086, 20], [69386, 20], [69686, 20], [69986, 22], [70286, 25], [70586, 26], [70885, 20], [70908, 4], [71185, 20], [71208, 5], [71486, 19], [71509, 3], [71786, 19], [72087, 17], [72387, 17]], "point": [179, 129]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.71|+00.83|-03.75", "placeStationary": true, "receptacleObjectId": "DiningTable|-02.87|+00.68|-00.51"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 74, 252, 257], "mask": [[22032, 2], [22318, 31], [22618, 40], [22918, 45], [23218, 50], [23518, 55], [23819, 59], [24119, 62], [24419, 64], [24719, 67], [25020, 69], [25320, 71], [25620, 74], [25923, 74], [26224, 51], [26289, 10], [26524, 47], [26591, 10], [26824, 45], [26892, 11], [27125, 42], [27193, 12], [27427, 38], [27493, 13], [27654, 3], [27729, 34], [27794, 14], [27953, 4], [28030, 32], [28094, 16], [28251, 6], [28332, 28], [28394, 17], [28549, 9], [28634, 25], [28694, 19], [28847, 11], [28936, 21], [28994, 21], [29145, 14], [29237, 19], [29294, 22], [29443, 17], [29539, 16], [29593, 25], [29742, 20], [29763, 1], [29841, 13], [29893, 27], [30040, 25], [30142, 11], [30192, 17], [30212, 9], [30339, 27], [30444, 8], [30492, 15], [30514, 8], [30638, 30], [30746, 5], [30791, 15], [30816, 7], [30936, 33], [31047, 3], [31090, 14], [31118, 6], [31235, 36], [31348, 1], [31389, 14], [31420, 5], [31534, 38], [31648, 1], [31688, 13], [31720, 6], [31833, 40], [31987, 13], [32020, 7], [32131, 44], [32285, 13], [32320, 8], [32430, 46], [32584, 13], [32619, 11], [32729, 48], [32883, 13], [32918, 13], [33028, 49], [33181, 14], [33216, 16], [33326, 52], [33480, 15], [33515, 18], [33625, 54], [33779, 16], [33814, 20], [33924, 56], [34045, 1], [34077, 19], [34112, 23], [34223, 59], [34344, 2], [34376, 21], [34411, 25], [34522, 60], [34642, 4], [34674, 25], [34710, 27], [34821, 62], [34940, 6], [34973, 28], [35008, 29], [35120, 65], [35239, 8], [35271, 32], [35307, 31], [35419, 67], [35536, 11], [35569, 70], [35718, 69], [35834, 14], [35866, 73], [36018, 71], [36132, 18], [36163, 77], [36317, 73], [36431, 21], [36458, 82], [36616, 75], [36729, 112], [36915, 78], [37027, 80], [37114, 28], [37215, 79], [37326, 76], [37419, 23], [37514, 81], [37624, 76], [37722, 21], [37813, 84], [37922, 76], [38025, 18], [38112, 86], [38222, 74], [38326, 18], [38411, 88], [38521, 74], [38628, 17], [38711, 47], [38768, 33], [38819, 75], [38929, 16], [39010, 45], [39070, 32], [39117, 60], [39180, 14], [39231, 15], [39309, 45], [39372, 31], [39416, 57], [39484, 9], [39532, 15], [39608, 45], [39673, 32], [39714, 58], [39786, 7], [39833, 14], [39907, 45], [39974, 32], [40012, 59], [40088, 4], [40133, 15], [40207, 45], [40275, 32], [40311, 11], [40328, 24], [40359, 2], [40388, 4], [40434, 14], [40506, 45], [40576, 44], [40630, 21], [40689, 3], [40734, 14], [40806, 45], [40877, 42], [40931, 1], [40989, 3], [41035, 14], [41106, 44], [41177, 41], [41248, 1], [41290, 3], [41335, 14], [41405, 45], [41477, 40], [41542, 6], [41590, 3], [41635, 14], [41705, 45], [41777, 40], [41833, 15], [41863, 2], [41867, 2], [41889, 4], [41935, 14], [42004, 46], [42077, 40], [42133, 14], [42164, 6], [42189, 5], [42236, 14], [42304, 46], [42377, 39], [42433, 14], [42464, 6], [42489, 5], [42535, 15], [42604, 46], [42677, 39], [42733, 14], [42764, 6], [42789, 6], [42835, 15], [42903, 47], [42977, 39], [43033, 14], [43064, 6], [43090, 6], [43135, 15], [43203, 47], [43276, 41], [43333, 15], [43364, 5], [43390, 7], [43435, 15], [43502, 48], [43576, 41], [43633, 15], [43663, 6], [43691, 7], [43734, 17], [43802, 49], [43875, 43], [43932, 17], [43963, 6], [43988, 1], [43991, 8], [44033, 18], [44102, 49], [44175, 44], [44231, 18], [44262, 7], [44288, 1], [44291, 10], [44332, 19], [44401, 51], [44474, 46], [44530, 20], [44561, 8], [44587, 1], [44590, 13], [44631, 20], [44701, 52], [44773, 48], [44829, 23], [44859, 10], [44889, 16], [44929, 22], [45000, 54], [45072, 53], [45126, 29], [45156, 13], [45189, 18], [45227, 25], [45300, 56], [45370, 99], [45488, 23], [45524, 28], [45600, 58], [45668, 101], [45787, 65], [45900, 169], [46086, 66], [46200, 170], [46386, 66], [46500, 170], [46686, 67], [46800, 171], [46985, 68], [47100, 172], [47284, 69], [47400, 174], [47583, 70], [47700, 252], [48000, 252], [48300, 252], [48600, 252], [48900, 252], [49200, 252], [49500, 251], [49800, 251], [50100, 251], [50400, 251], [50700, 251], [51000, 250], [51300, 250], [51600, 250], [51900, 250], [52200, 250], [52500, 250], [52800, 249], [53100, 249], [53400, 249], [53700, 249], [54000, 249], [54300, 248], [54600, 248], [54900, 248], [55200, 247], [55500, 247], [55800, 246], [56100, 246], [56401, 244], [56701, 244], [57001, 243], [57302, 241], [57602, 241], [57903, 239], [58203, 238], [58504, 237], [58804, 236], [59105, 171], [59278, 62], [59405, 160], [59574, 65], [59705, 154], [59866, 7], [59877, 61], [60006, 112], [60172, 66], [60306, 110], [60450, 3], [60466, 6], [60476, 61], [60607, 109], [60740, 17], [60771, 5], [60778, 58], [60907, 110], [61030, 28], [61066, 4], [61074, 62], [61208, 152], [61370, 65], [61508, 155], [61664, 71], [61809, 225], [62109, 224], [62410, 223], [62710, 222], [63011, 220], [63312, 219], [63612, 218], [63913, 216], [64215, 213], [64516, 210], [64817, 208], [65118, 206], [65419, 204], [65720, 201], [66021, 199], [66322, 197], [66623, 195], [66924, 192], [67225, 190], [67526, 188], [67827, 186], [68128, 183], [68429, 181], [68730, 179], [69031, 177], [69333, 173], [69634, 171], [69935, 169], [70236, 167], [70537, 164], [70839, 160], [71142, 154], [71444, 150], [71746, 99], [71855, 37], [72048, 95], [72157, 32], [72351, 90], [72459, 28], [72653, 87], [72760, 24], [72955, 84], [73061, 21], [73257, 81], [73362, 17], [73560, 77], [73663, 14], [73862, 74], [73964, 11], [74164, 71], [74265, 7], [74466, 69], [74565, 5], [74768, 66], [74866, 1], [75071, 63], [75373, 61], [75681, 52], [75988, 45], [76296, 37], [76603, 28], [76911, 13]], "point": [126, 164]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan19", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -1.5, "y": 0.9016907, "z": -0.25}, "object_poses": [{"objectName": "Potato_2b74ee3a", "position": {"x": -1.84543049, "y": 0.8343529, "z": -3.790042}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_78f219bc", "position": {"x": -2.43159223, "y": 0.713334441, "z": -0.3269574}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "ButterKnife_a7531534", "position": {"x": -0.338365942, "y": 0.9160421, "z": -1.3709687}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_a7531534", "position": {"x": -0.338365972, "y": 0.91604203, "z": -0.45571363}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_fca8a224", "position": {"x": -0.4470415, "y": 0.9286998, "z": -3.804}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_94d2125c", "position": {"x": -3.16700172, "y": 0.9148294, "z": -3.72987866}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_1cd5bb8d", "position": {"x": -0.5301752, "y": 0.7474362, "z": -3.07936954}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_b0ce4484", "position": {"x": -1.70727491, "y": 0.826456964, "z": -3.7472}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_410beacd", "position": {"x": -0.41940093, "y": 0.9163254, "z": -1.30962193}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_bdd88192", "position": {"x": -0.244556189, "y": 0.9118503, "z": -2.91177487}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_bdd88192", "position": {"x": -3.1309557, "y": 0.6393903, "z": -1.87947655}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Tomato_f34461c5", "position": {"x": -3.15017486, "y": 0.7633537, "z": -0.620999932}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_f34461c5", "position": {"x": -0.223363563, "y": 0.129380241, "z": -0.07261489}, "rotation": {"x": 0.0, "y": 269.999969, "z": 0.0}}, {"objectName": "SoapBottle_dd4bbd23", "position": {"x": -0.176295966, "y": 0.9155963, "z": -1.67770243}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_dd4bbd23", "position": {"x": -0.4525291, "y": 0.0800885558, "z": -1.64754391}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_2ce80e75", "position": {"x": -0.450745076, "y": 0.0784063339, "z": -3.348648}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_5fac94a5", "position": {"x": -0.6926557, "y": 0.0783962, "z": -3.6358695}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_5fac94a5", "position": {"x": -0.01422596, "y": 0.9123061, "z": -0.524823248}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_77e17ce3", "position": {"x": -2.66471648, "y": 0.75464195, "z": -0.29491657}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Mug_f4b8ca2f", "position": {"x": -0.3343274, "y": 0.07710022, "z": -3.31729841}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f4b8ca2f", "position": {"x": -2.6205225, "y": 0.7119674, "z": -0.652889848}, "rotation": {"x": 0.0, "y": 225.000092, "z": 0.0}}, {"objectName": "Apple_1634f71e", "position": {"x": -2.72361231, "y": 0.7596072, "z": -0.621}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_1634f71e", "position": {"x": -0.09631693, "y": 1.055421, "z": -0.851532}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Pot_fd5c5c80", "position": {"x": -0.3767, "y": 0.9759, "z": -2.0314}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Egg_b0ce4484", "position": {"x": -1.6381973, "y": 0.827780664, "z": -3.875726}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_410beacd", "position": {"x": -3.1309545, "y": 1.39922929, "z": -2.18732548}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bread_77e17ce3", "position": {"x": -0.327003837, "y": 0.948121846, "z": -3.681027}, "rotation": {"x": 0.00382397557, "y": 298.2228, "z": 0.0431113131}}, {"objectName": "Fork_1cd5bb8d", "position": {"x": -2.7408, "y": 0.714318752, "z": -0.9225625}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_4966d0cc", "position": {"x": -0.3946991, "y": 0.9731094, "z": -2.41475344}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_2b74ee3a", "position": {"x": -1.91450834, "y": 0.8343529, "z": -3.66151619}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_908150ce", "position": {"x": -1.32413423, "y": 0.9881281, "z": -3.75902057}, "rotation": {"x": 1.46825719, "y": 253.69043, "z": 356.7306}}, {"objectName": "Tomato_f34461c5", "position": {"x": -2.62912345, "y": 0.9632835, "z": -3.65575743}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_1634f71e", "position": {"x": -2.8658, "y": 0.7596072, "z": -0.621}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_dd4bbd23", "position": {"x": -0.8649154, "y": 1.2925024, "z": -3.84997988}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_fca8a224", "position": {"x": -0.0391509533, "y": 0.9286998, "z": -3.95224237}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_2ce80e75", "position": {"x": -3.38854647, "y": 1.288454, "z": -3.25951815}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_46bffc5f", "position": {"x": -0.471112043, "y": 0.778778732, "z": -0.6097589}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "PepperShaker_5fac94a5", "position": {"x": -0.144137025, "y": 1.29072857, "z": -0.6200673}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_a7531534", "position": {"x": -2.98770881, "y": 0.913742065, "z": -3.65575743}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_bdd88192", "position": {"x": -2.43923759, "y": 0.711920559, "z": -0.558500051}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "DishSponge_78f219bc", "position": {"x": -0.471111983, "y": 0.7464519, "z": -1.17647409}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_94d2125c", "position": {"x": -2.72361231, "y": 0.7180968, "z": -0.5585}, "rotation": {"x": 0.0, "y": 4.829673e-06, "z": 0.0}}, {"objectName": "Mug_f4b8ca2f", "position": {"x": -2.93075848, "y": 0.7624276, "z": -0.358240426}, "rotation": {"x": -0.002125355, "y": 134.9997, "z": -0.00129139412}}, {"objectName": "Bowl_697b561f", "position": {"x": -0.373344034, "y": 0.0785600543, "z": -2.91026783}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 25809120, "scene_num": 19}, "task_id": "trial_T20190909_151935_770234", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A10AVWALIHR4UQ_38F5OAUN5Q3MX3KQ3KNY5KQV22PH7V", "high_descs": ["Turn left and move to the sink in front of the window.", "Pick up the brown egg closest to the left of the potato from the sink.", "Turn around, move to the gray door, then turn right and move to the black microwave at the end of the counter.", "Open the microwave, place the egg in front of the apple on the plate, microwave the egg, then open the microwave, pick up the egg from the plate, and then close the microwave door.", "Turn around, move to the black refrigerator, then turn to the right and face the wooden table in the corner of the room.", "Place the egg to the left of the fork on the table."], "task_desc": "Place a cooked egg on the table.", "votes": [1, 1]}, {"assignment_id": "A19LVWX8ZLO6CS_3IRIK4HM3DBCCVV5YI45OFTJUZ26CE", "high_descs": ["Step forward, turn left and walk to the kitchen sink.", "Pick up the first egg to the left of the potato.", "Turn around, walk to the door, turn right and go to the microwave.", "Open the microwave, place the egg inside, close the door and turn on the microwave. Wait till the microwave is off, then remove the egg.", "Turn right , walk forward, turn right and walk to the edge of the round table.", "Place the egg to the left of the fork handle."], "task_desc": "Heat up an egg, place on kitchen table.", "votes": [1, 1]}, {"assignment_id": "A1K5ILJMG439M5_3TVRFO09GN6IYE97CI6URBWQLBXXLV", "high_descs": ["Turn to your left and cross the kitchen to face the sink.", "Grab an egg from in the sink.", "With the egg in hand, go over to face the black microwave.", "put the egg in the microwave, cook it, then take it out again.", "With the egg in hand again, turn around and move to face the large brown table so that the black mug is right of the tomatoes when you look at it.", "Put the egg down to the left of the fork."], "task_desc": "Put a cooked egg on the table.", "votes": [1, 1]}]}}