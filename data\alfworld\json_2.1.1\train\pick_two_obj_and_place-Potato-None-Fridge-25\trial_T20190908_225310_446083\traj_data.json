{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000187.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000188.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000189.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000190.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000191.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000192.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000193.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000194.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000195.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000196.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000255.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000256.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000257.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000258.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000259.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000260.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000261.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000262.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000263.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000264.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000265.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000266.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000267.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000268.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000269.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000270.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000271.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000272.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000273.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000274.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000275.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000277.png", "low_idx": 43}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Potato", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-8|6|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["potato"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Potato", [-11.5088854, -11.5088854, 5.56331684, 5.56331684, 3.840898, 3.840898]], "coordinateReceptacleObjectId": ["Microwave", [-11.18, -11.18, 5.396, 5.396, 3.2488436, 3.2488436]], "forceVisible": true, "objectId": "Potato|-02.88|+00.96|+01.39"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-8|9|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["potato", "fridge"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Potato", [-11.5088854, -11.5088854, 5.56331684, 5.56331684, 3.840898, 3.840898]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-11.128, -11.128, 8.8868, 8.8868, 0.0, 0.0]], "forceVisible": true, "objectId": "Potato|-02.88|+00.96|+01.39", "receptacleObjectId": "<PERSON><PERSON>|-02.78|+00.00|+02.22"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-6|4|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["potato"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Potato", [-4.60731792, -4.60731792, 1.112, 1.112, 3.4495324, 3.4495324]], "coordinateReceptacleObjectId": ["CounterTop", [-6.232, -6.232, 1.112, 1.112, 3.4364, 3.4364]], "forceVisible": true, "objectId": "Potato|-01.15|+00.86|+00.28"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-8|9|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["potato", "fridge"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Potato", [-4.60731792, -4.60731792, 1.112, 1.112, 3.4495324, 3.4495324]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-11.128, -11.128, 8.8868, 8.8868, 0.0, 0.0]], "forceVisible": true, "objectId": "Potato|-01.15|+00.86|+00.28", "receptacleObjectId": "<PERSON><PERSON>|-02.78|+00.00|+02.22"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 34, 204, 174], "mask": [[9925, 166], [10225, 166], [10524, 168], [10823, 169], [11122, 170], [11421, 171], [11720, 173], [12019, 174], [12319, 174], [12618, 176], [12917, 177], [13216, 178], [13515, 179], [13814, 181], [14113, 182], [14412, 183], [14712, 184], [15011, 185], [15310, 186], [15609, 187], [15908, 189], [16207, 190], [16506, 191], [16806, 192], [17105, 193], [17404, 194], [17703, 195], [18002, 197], [18301, 198], [18600, 199], [18900, 200], [19200, 200], [19500, 200], [19800, 200], [20100, 201], [20400, 201], [20700, 201], [21000, 202], [21300, 202], [21600, 202], [21900, 203], [22200, 203], [22500, 203], [22800, 203], [23100, 204], [23400, 204], [23700, 204], [24000, 205], [24300, 205], [24600, 204], [24900, 204], [25200, 204], [25500, 204], [25800, 204], [26100, 204], [26400, 203], [26700, 203], [27000, 203], [27300, 203], [27600, 203], [27900, 203], [28200, 202], [28500, 202], [28800, 202], [29100, 202], [29400, 202], [29700, 202], [30000, 202], [30300, 202], [30600, 202], [30900, 202], [31200, 202], [31500, 202], [31800, 202], [32100, 202], [32400, 202], [32700, 202], [33000, 201], [33300, 201], [33600, 201], [33900, 201], [34200, 201], [34500, 200], [34800, 200], [35100, 200], [35400, 200], [35700, 200], [36000, 200], [36300, 199], [36600, 199], [36900, 199], [37200, 199], [37500, 199], [37801, 198], [38102, 196], [38402, 196], [38703, 195], [39003, 195], [39304, 194], [39604, 194], [39905, 192], [40205, 192], [40506, 191], [40806, 191], [41107, 190], [41407, 190], [41708, 188], [42008, 188], [42309, 187], [42609, 187], [42910, 186], [43210, 186], [43511, 184], [43812, 183], [44112, 183], [44413, 182], [44713, 182], [45014, 180], [45314, 180], [45615, 179], [45915, 179], [46216, 178], [46516, 178], [46817, 176], [47117, 176], [47418, 175], [47718, 175], [48019, 174], [48319, 174], [48620, 172], [48920, 172], [49221, 171], [49521, 171], [49822, 170], [50123, 168], [50429, 162], [50729, 161], [51030, 160], [51330, 160], [51631, 159], [51931, 159]], "point": [102, 103]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Potato|-02.88|+00.96|+01.39"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [113, 99, 132, 115], "mask": [[29515, 16], [29814, 17], [30114, 18], [30414, 18], [30713, 20], [31013, 20], [31313, 20], [31613, 20], [31913, 20], [32214, 19], [32514, 18], [32815, 17], [33115, 16], [33416, 15], [33717, 12], [34019, 9], [34321, 4]], "point": [122, 106]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 34, 204, 234], "mask": [[9925, 166], [10225, 166], [10524, 168], [10823, 169], [11122, 170], [11421, 171], [11720, 173], [12019, 174], [12319, 174], [12618, 176], [12917, 177], [13216, 178], [13515, 179], [13814, 181], [14113, 182], [14412, 183], [14712, 184], [15011, 185], [15310, 186], [15609, 187], [15908, 189], [16207, 190], [16506, 191], [16806, 192], [17105, 193], [17404, 194], [17703, 195], [18002, 197], [18301, 198], [18600, 199], [18900, 200], [19200, 200], [19500, 200], [19800, 200], [20100, 201], [20400, 201], [20700, 201], [21000, 202], [21300, 202], [21600, 202], [21900, 203], [22200, 203], [22500, 203], [22800, 203], [23100, 204], [23400, 204], [23700, 204], [24000, 205], [24300, 205], [24600, 204], [24900, 204], [25200, 204], [25500, 204], [25800, 204], [26100, 204], [26400, 203], [26700, 203], [27000, 203], [27300, 203], [27600, 203], [27900, 203], [28200, 202], [28500, 202], [28800, 202], [29100, 202], [29400, 50], [29473, 129], [29700, 49], [29774, 128], [30000, 48], [30075, 127], [30300, 47], [30376, 126], [30600, 47], [30676, 126], [30900, 47], [30977, 125], [31200, 46], [31277, 125], [31500, 46], [31578, 124], [31800, 45], [31878, 124], [32100, 45], [32178, 124], [32400, 45], [32478, 124], [32700, 45], [32778, 124], [33000, 45], [33079, 122], [33300, 45], [33379, 122], [33600, 45], [33679, 122], [33900, 45], [33979, 122], [34200, 45], [34279, 122], [34500, 45], [34579, 121], [34800, 46], [34879, 121], [35100, 46], [35178, 122], [35400, 47], [35478, 122], [35700, 48], [35778, 122], [36000, 48], [36078, 122], [36300, 49], [36377, 122], [36600, 50], [36677, 122], [36900, 51], [36976, 123], [37200, 52], [37276, 123], [37500, 54], [37575, 124], [37800, 56], [37874, 125], [38100, 58], [38173, 125], [38400, 60], [38472, 126], [38700, 63], [38770, 128], [39000, 198], [39300, 198], [39600, 198], [39900, 197], [40200, 197], [40500, 197], [40800, 197], [41100, 197], [41400, 197], [41700, 196], [42000, 196], [42300, 196], [42600, 196], [42900, 196], [43200, 196], [43500, 195], [43800, 195], [44100, 195], [44400, 195], [44700, 195], [45000, 194], [45300, 194], [45600, 194], [45900, 194], [46200, 194], [46500, 194], [46800, 193], [47100, 193], [47400, 193], [47700, 193], [48000, 193], [48300, 193], [48600, 192], [48900, 192], [49200, 192], [49500, 192], [49800, 192], [50100, 191], [50400, 191], [50700, 28], [50729, 161], [51000, 28], [51030, 160], [51300, 28], [51330, 160], [51600, 27], [51631, 159], [51900, 27], [51931, 159], [52200, 26], [52500, 26], [52800, 25], [53100, 25], [53400, 24], [53700, 24], [54000, 24], [54300, 23], [54600, 23], [54900, 22], [55200, 22], [55500, 21], [55800, 21], [56100, 21], [56400, 20], [56700, 20], [57000, 19], [57300, 19], [57600, 18], [57900, 18], [58200, 18], [58500, 17], [58800, 17], [59100, 16], [59400, 16], [59700, 15], [60000, 15], [60300, 14], [60600, 14], [60900, 14], [61200, 13], [61500, 13], [61800, 12], [62100, 12], [62400, 11], [62700, 11], [63000, 11], [63300, 10], [63600, 10], [63900, 9], [64200, 9], [64500, 8], [64800, 8], [65100, 7], [65400, 7], [65700, 7], [66000, 6], [66300, 6], [66600, 5], [66900, 5], [67200, 4], [67500, 4], [67800, 4], [68100, 3], [68400, 3], [68700, 2], [69000, 2], [69300, 1], [69600, 1], [69900, 1]], "point": [102, 133]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.78|+00.00|+02.22"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 226], "mask": [[0, 27899], [27900, 298], [28200, 298], [28500, 297], [28800, 296], [29100, 296], [29400, 295], [29700, 294], [30000, 293], [30300, 293], [30600, 292], [30900, 291], [31200, 291], [31500, 290], [31800, 289], [32100, 288], [32400, 288], [32700, 287], [33000, 286], [33300, 285], [33600, 285], [33900, 284], [34200, 283], [34500, 283], [34800, 282], [35100, 281], [35400, 280], [35700, 280], [36001, 278], [36301, 277], [36602, 276], [36903, 274], [37204, 272], [37505, 270], [37806, 269], [38106, 268], [38407, 266], [38708, 265], [39009, 263], [39310, 261], [39611, 259], [39911, 259], [40212, 257], [40513, 255], [40814, 254], [41115, 252], [41416, 250], [41716, 249], [42017, 248], [42318, 246], [42619, 244], [42920, 242], [43221, 241], [43521, 240], [43822, 238], [44123, 237], [44424, 235], [44725, 233], [45026, 231], [45326, 231], [45627, 229], [45928, 227], [46229, 226], [46530, 224], [46831, 222], [47131, 221], [47432, 220], [47733, 218], [48034, 216], [48335, 215], [48636, 213], [48936, 212], [49237, 210], [49538, 209], [49839, 207], [50140, 205], [50441, 204], [50741, 203], [51042, 201], [51343, 199], [51644, 198], [51945, 196], [52246, 194], [52546, 193], [52847, 192], [53148, 190], [53449, 188], [53750, 187], [54051, 185], [54351, 184], [54652, 182], [54953, 181], [55254, 179], [55555, 177], [55856, 176], [56156, 175], [56457, 173], [56758, 171], [57059, 170], [57360, 168], [57661, 166], [57961, 166], [58262, 164], [58563, 162], [58864, 160], [59165, 159], [59465, 158], [59766, 156], [60067, 155], [60368, 153], [60669, 151], [60970, 149], [61270, 149], [61571, 147], [61872, 145], [62173, 143], [62474, 142], [62775, 140], [63075, 139], [63376, 138], [63677, 136], [63978, 134], [64279, 132], [64580, 131], [64880, 130], [65182, 127], [65487, 117], [65792, 107], [66097, 97], [66402, 86], [66707, 76], [67013, 64], [67319, 52], [67628, 33]], "point": [149, 112]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Potato|-02.88|+00.96|+01.39", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.78|+00.00|+02.22"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 16292], [16349, 243], [16649, 243], [16949, 243], [17249, 243], [17549, 12], [17571, 128], [17700, 91], [17849, 10], [17874, 124], [18000, 91], [18149, 8], [18176, 122], [18300, 91], [18449, 7], [18477, 120], [18600, 91], [18749, 5], [18778, 119], [18900, 91], [19049, 5], [19079, 117], [19200, 90], [19349, 4], [19380, 115], [19500, 90], [19649, 4], [19680, 115], [19800, 90], [19949, 4], [19980, 114], [20100, 90], [20249, 4], [20285, 108], [20400, 90], [20549, 4], [20586, 107], [20700, 90], [20849, 4], [20886, 106], [21000, 90], [21149, 4], [21186, 106], [21300, 89], [21449, 4], [21480, 4], [21487, 104], [21600, 89], [21749, 4], [21780, 4], [21787, 103], [21900, 89], [22049, 4], [22080, 4], [22086, 104], [22200, 89], [22349, 4], [22380, 4], [22386, 103], [22500, 89], [22649, 4], [22679, 4], [22686, 103], [22800, 89], [22948, 5], [22979, 4], [22986, 102], [23100, 89], [23248, 5], [23279, 3], [23286, 101], [23400, 90], [23548, 5], [23579, 2], [23585, 102], [23700, 90], [23847, 6], [23884, 102], [24000, 90], [24147, 6], [24184, 101], [24300, 90], [24446, 6], [24483, 102], [24600, 90], [24746, 6], [24782, 102], [24900, 91], [25046, 6], [25080, 104], [25200, 93], [25344, 8], [25378, 105], [25500, 98], [25638, 14], [25678, 104], [25800, 104], [25932, 20], [25978, 104], [26100, 153], [26278, 103], [26400, 153], [26578, 102], [26700, 153], [26878, 102], [27000, 153], [27177, 102], [27300, 154], [27477, 102], [27600, 155], [27776, 102], [27900, 156], [28075, 102], [28200, 157], [28373, 104], [28500, 159], [28672, 104], [28800, 162], [28969, 107], [29100, 275], [29400, 274], [29700, 274], [30000, 273], [30300, 272], [30600, 272], [30900, 271], [31200, 271], [31500, 270], [31800, 269], [32100, 269], [32400, 268], [32700, 268], [33000, 267], [33300, 266], [33600, 266], [33900, 265], [34200, 264], [34500, 264], [34800, 263], [35100, 263], [35400, 262], [35700, 261], [36000, 261], [36300, 260], [36600, 260], [36900, 259], [37200, 258], [37500, 258], [37800, 257], [38100, 256], [38400, 256], [38700, 255], [39000, 255], [39300, 254], [39600, 253], [39900, 253], [40200, 252], [40500, 252], [40800, 251], [41100, 250], [41400, 250], [41700, 249], [42000, 248], [42300, 248], [42600, 247], [42900, 247], [43200, 246], [43500, 245], [43800, 245], [44100, 244], [44400, 243], [44700, 243], [45000, 242], [45300, 242], [45600, 241], [45900, 240], [46200, 240], [46500, 239], [46800, 239], [47100, 238], [47400, 237], [47700, 237], [48000, 236], [48300, 235], [48600, 235], [48900, 234], [49200, 234], [49500, 233], [49800, 232], [50100, 232], [50400, 231], [50700, 231], [51000, 230], [51300, 229], [51600, 229], [51900, 228], [52200, 227], [52500, 227], [52800, 226], [53100, 226], [53400, 225], [53700, 224], [54000, 224], [54300, 223], [54600, 223], [54900, 222], [55200, 221], [55500, 221], [55800, 220], [56100, 219], [56400, 219], [56700, 218], [57000, 218], [57300, 217], [57600, 216], [57900, 216], [58200, 215], [58500, 214], [58800, 214], [59100, 213], [59400, 213], [59700, 212], [60000, 211], [60300, 211], [60600, 210], [60900, 210], [61200, 209], [61500, 206], [61800, 87], [62100, 87], [62400, 87], [62700, 87], [63000, 86], [63300, 86], [63600, 86], [63900, 86], [64200, 86], [64500, 86], [64800, 86], [65100, 86], [65400, 85], [65700, 85], [66000, 85], [66300, 85], [66600, 85], [66900, 85], [67200, 85], [67500, 85], [67800, 84], [68100, 84], [68400, 84], [68700, 84], [69000, 84], [69300, 84], [69600, 84], [69900, 84], [70200, 83], [70500, 83], [70800, 83], [71100, 83], [71400, 83], [71700, 83], [72000, 83], [72300, 83], [72600, 82], [72900, 82], [73200, 82], [73500, 82], [73800, 82], [74100, 82], [74400, 82], [74700, 82], [75000, 81], [75300, 81], [75600, 81], [75900, 81], [76200, 81], [76500, 81], [76800, 81], [77100, 81], [77400, 80], [77700, 80], [78000, 80], [78300, 80], [78600, 80], [78900, 80], [79200, 80], [79500, 80], [79800, 80], [80100, 79], [80400, 79], [80700, 79], [81000, 79], [81300, 79], [81600, 79], [81900, 79], [82200, 79], [82500, 78], [82800, 78], [83100, 78], [83400, 78], [83700, 78], [84000, 78], [84300, 78], [84600, 78], [84900, 77], [85200, 77], [85500, 77], [85800, 77], [86100, 77], [86400, 77], [86700, 77], [87000, 77], [87300, 76], [87600, 76], [87900, 76], [88200, 76], [88500, 76], [88800, 76], [89100, 76], [89400, 76], [89700, 75]], "point": [149, 149]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.78|+00.00|+02.22"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 16292], [16349, 243], [16649, 243], [16949, 243], [17249, 243], [17549, 12], [17571, 128], [17700, 91], [17849, 10], [17874, 124], [18000, 91], [18149, 8], [18176, 122], [18300, 91], [18449, 7], [18477, 120], [18600, 91], [18749, 5], [18778, 119], [18900, 91], [19049, 5], [19079, 117], [19200, 90], [19349, 4], [19380, 115], [19500, 90], [19649, 4], [19680, 115], [19800, 90], [19949, 4], [19980, 114], [20100, 90], [20249, 4], [20285, 108], [20400, 90], [20549, 4], [20586, 107], [20700, 90], [20849, 4], [20886, 106], [21000, 90], [21149, 4], [21186, 106], [21300, 89], [21449, 4], [21480, 4], [21487, 104], [21600, 89], [21749, 4], [21780, 4], [21787, 103], [21900, 89], [22049, 4], [22080, 4], [22086, 104], [22200, 89], [22349, 4], [22380, 4], [22386, 103], [22500, 89], [22649, 4], [22679, 4], [22686, 103], [22800, 89], [22948, 5], [22979, 4], [22986, 102], [23100, 89], [23248, 5], [23279, 3], [23286, 101], [23400, 90], [23548, 5], [23579, 2], [23585, 102], [23700, 90], [23847, 6], [23884, 102], [24000, 90], [24147, 6], [24184, 101], [24300, 90], [24446, 6], [24483, 102], [24600, 90], [24746, 6], [24782, 102], [24900, 91], [25046, 6], [25080, 104], [25200, 93], [25344, 8], [25378, 105], [25500, 98], [25638, 14], [25678, 104], [25800, 104], [25932, 20], [25978, 104], [26100, 153], [26278, 103], [26400, 153], [26578, 102], [26700, 137], [26845, 8], [26878, 102], [27000, 135], [27147, 6], [27177, 102], [27300, 134], [27449, 5], [27477, 102], [27600, 133], [27750, 5], [27776, 102], [27900, 132], [28051, 5], [28075, 102], [28200, 132], [28351, 6], [28373, 104], [28500, 132], [28652, 7], [28672, 104], [28800, 131], [28953, 9], [28969, 107], [29100, 131], [29253, 122], [29400, 131], [29553, 121], [29700, 131], [29853, 121], [30000, 131], [30153, 120], [30300, 131], [30453, 119], [30600, 131], [30753, 119], [30900, 131], [31053, 118], [31200, 132], [31353, 118], [31500, 132], [31652, 118], [31800, 133], [31952, 117], [32100, 134], [32251, 118], [32400, 134], [32550, 118], [32700, 136], [32849, 119], [33000, 137], [33147, 120], [33300, 139], [33445, 121], [33600, 266], [33900, 265], [34200, 264], [34500, 264], [34800, 263], [35100, 263], [35400, 262], [35700, 261], [36000, 261], [36300, 260], [36600, 260], [36900, 259], [37200, 258], [37500, 258], [37800, 257], [38100, 256], [38400, 256], [38700, 255], [39000, 255], [39300, 254], [39600, 253], [39900, 253], [40200, 252], [40500, 252], [40800, 251], [41100, 250], [41400, 250], [41700, 249], [42000, 248], [42300, 248], [42600, 247], [42900, 247], [43200, 246], [43500, 245], [43800, 245], [44100, 244], [44400, 243], [44700, 243], [45000, 242], [45300, 242], [45600, 241], [45900, 240], [46200, 240], [46500, 239], [46800, 239], [47100, 238], [47400, 237], [47700, 237], [48000, 236], [48300, 235], [48600, 235], [48900, 234], [49200, 234], [49500, 233], [49800, 232], [50100, 232], [50400, 231], [50700, 231], [51000, 230], [51300, 229], [51600, 229], [51900, 228], [52200, 227], [52500, 227], [52800, 226], [53100, 226], [53400, 225], [53700, 224], [54000, 224], [54300, 223], [54600, 223], [54900, 222], [55200, 221], [55500, 221], [55800, 220], [56100, 219], [56400, 219], [56700, 218], [57000, 218], [57300, 217], [57600, 216], [57900, 216], [58200, 215], [58500, 214], [58800, 214], [59100, 213], [59400, 213], [59700, 212], [60000, 211], [60300, 211], [60600, 210], [60900, 210], [61200, 209], [61500, 206], [61800, 87], [62100, 87], [62400, 87], [62700, 87], [63000, 86], [63300, 86], [63600, 86], [63900, 86], [64200, 86], [64500, 86], [64800, 86], [65100, 86], [65400, 85], [65700, 85], [66000, 85], [66300, 85], [66600, 85], [66900, 85], [67200, 85], [67500, 85], [67800, 84], [68100, 84], [68400, 84], [68700, 84], [69000, 84], [69300, 84], [69600, 84], [69900, 84], [70200, 83], [70500, 83], [70800, 83], [71100, 83], [71400, 83], [71700, 83], [72000, 83], [72300, 83], [72600, 82], [72900, 82], [73200, 82], [73500, 82], [73800, 82], [74100, 82], [74400, 82], [74700, 82], [75000, 81], [75300, 81], [75600, 81], [75900, 81], [76200, 81], [76500, 81], [76800, 81], [77100, 81], [77400, 80], [77700, 80], [78000, 80], [78300, 80], [78600, 80], [78900, 80], [79200, 80], [79500, 80], [79800, 80], [80100, 79], [80400, 79], [80700, 79], [81000, 79], [81300, 79], [81600, 79], [81900, 79], [82200, 79], [82500, 78], [82800, 78], [83100, 78], [83400, 78], [83700, 78], [84000, 78], [84300, 78], [84600, 78], [84900, 77], [85200, 77], [85500, 77], [85800, 77], [86100, 77], [86400, 77], [86700, 77], [87000, 77], [87300, 76], [87600, 76], [87900, 76], [88200, 76], [88500, 76], [88800, 76], [89100, 76], [89400, 76], [89700, 75]], "point": [149, 149]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Potato|-01.15|+00.86|+00.28"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [50, 139, 70, 159], "mask": [[41458, 4], [41755, 10], [42054, 13], [42353, 15], [42652, 17], [42951, 19], [43251, 19], [43550, 21], [43850, 21], [44150, 21], [44450, 21], [44750, 21], [45050, 21], [45350, 21], [45650, 21], [45951, 19], [46252, 18], [46553, 16], [46854, 14], [47155, 11], [47456, 8]], "point": [60, 148]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.78|+00.00|+02.22"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 226], "mask": [[0, 27899], [27900, 298], [28200, 298], [28500, 297], [28800, 296], [29100, 296], [29400, 295], [29700, 294], [30000, 293], [30300, 293], [30600, 292], [30900, 291], [31200, 291], [31500, 290], [31800, 289], [32100, 288], [32400, 288], [32700, 287], [33000, 286], [33300, 285], [33600, 285], [33900, 284], [34200, 283], [34500, 283], [34800, 282], [35100, 281], [35400, 280], [35700, 280], [36001, 278], [36301, 277], [36602, 276], [36903, 274], [37204, 272], [37505, 270], [37806, 269], [38106, 268], [38407, 266], [38708, 265], [39009, 263], [39310, 261], [39611, 259], [39911, 259], [40212, 257], [40513, 255], [40814, 254], [41115, 252], [41416, 250], [41716, 249], [42017, 248], [42318, 246], [42619, 244], [42920, 242], [43221, 241], [43521, 240], [43822, 238], [44123, 237], [44424, 235], [44725, 233], [45026, 231], [45326, 231], [45627, 229], [45928, 227], [46229, 226], [46530, 224], [46831, 222], [47131, 221], [47432, 220], [47733, 218], [48034, 216], [48335, 215], [48636, 213], [48936, 212], [49237, 210], [49538, 209], [49839, 207], [50140, 205], [50441, 204], [50741, 203], [51042, 201], [51343, 199], [51644, 198], [51945, 196], [52246, 194], [52546, 193], [52847, 192], [53148, 190], [53449, 188], [53750, 187], [54051, 185], [54351, 184], [54652, 182], [54953, 181], [55254, 179], [55555, 177], [55856, 176], [56156, 175], [56457, 173], [56758, 171], [57059, 170], [57360, 168], [57661, 166], [57961, 166], [58262, 164], [58563, 162], [58864, 160], [59165, 159], [59465, 158], [59766, 156], [60067, 155], [60368, 153], [60669, 151], [60970, 149], [61270, 149], [61571, 147], [61872, 145], [62173, 143], [62474, 142], [62775, 140], [63075, 139], [63376, 138], [63677, 136], [63978, 134], [64279, 132], [64580, 131], [64880, 130], [65182, 127], [65487, 117], [65792, 107], [66097, 97], [66402, 86], [66707, 76], [67013, 64], [67319, 52], [67628, 33]], "point": [149, 112]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Potato|-01.15|+00.86|+00.28", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.78|+00.00|+02.22"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 16292], [16349, 243], [16649, 243], [16949, 243], [17249, 243], [17549, 12], [17571, 128], [17700, 91], [17849, 10], [17874, 124], [18000, 91], [18149, 8], [18176, 122], [18300, 91], [18449, 7], [18477, 120], [18600, 91], [18749, 5], [18778, 119], [18900, 91], [19049, 5], [19079, 117], [19200, 90], [19349, 4], [19380, 115], [19500, 90], [19649, 4], [19680, 115], [19800, 90], [19949, 4], [19980, 114], [20100, 90], [20249, 4], [20285, 108], [20400, 90], [20549, 4], [20586, 107], [20700, 90], [20849, 4], [20886, 106], [21000, 90], [21149, 4], [21186, 106], [21300, 89], [21449, 4], [21480, 4], [21487, 104], [21600, 89], [21749, 4], [21780, 4], [21787, 103], [21900, 89], [22049, 4], [22080, 4], [22086, 104], [22200, 89], [22349, 4], [22380, 4], [22386, 103], [22500, 89], [22649, 4], [22679, 4], [22686, 103], [22800, 89], [22948, 5], [22979, 4], [22986, 102], [23100, 89], [23248, 5], [23279, 3], [23286, 101], [23400, 90], [23548, 5], [23579, 2], [23585, 102], [23700, 90], [23847, 6], [23884, 102], [24000, 90], [24147, 6], [24184, 101], [24300, 90], [24446, 6], [24483, 102], [24600, 90], [24746, 6], [24782, 102], [24900, 91], [25046, 6], [25080, 104], [25200, 93], [25344, 8], [25378, 105], [25500, 98], [25638, 14], [25678, 104], [25800, 104], [25932, 20], [25978, 104], [26100, 153], [26278, 103], [26400, 153], [26578, 102], [26700, 137], [26845, 8], [26878, 102], [27000, 135], [27147, 6], [27177, 102], [27300, 134], [27449, 5], [27477, 102], [27600, 133], [27750, 5], [27776, 102], [27900, 132], [28051, 5], [28075, 102], [28200, 132], [28351, 6], [28373, 104], [28500, 132], [28652, 7], [28672, 104], [28800, 131], [28953, 9], [28969, 107], [29100, 131], [29253, 122], [29400, 131], [29553, 121], [29700, 131], [29853, 121], [30000, 131], [30153, 120], [30300, 131], [30453, 119], [30600, 131], [30753, 119], [30900, 131], [31053, 118], [31200, 132], [31353, 118], [31500, 132], [31652, 118], [31800, 133], [31952, 117], [32100, 134], [32251, 118], [32400, 134], [32550, 118], [32700, 136], [32849, 119], [33000, 137], [33147, 120], [33300, 139], [33445, 121], [33600, 266], [33900, 265], [34200, 264], [34500, 264], [34800, 263], [35100, 263], [35400, 262], [35700, 261], [36000, 261], [36300, 260], [36600, 260], [36900, 259], [37200, 258], [37500, 258], [37800, 257], [38100, 256], [38400, 256], [38700, 255], [39000, 255], [39300, 254], [39600, 253], [39900, 253], [40200, 252], [40500, 252], [40800, 251], [41100, 250], [41400, 250], [41700, 249], [42000, 248], [42300, 248], [42600, 247], [42900, 247], [43200, 246], [43500, 245], [43800, 245], [44100, 244], [44400, 243], [44700, 243], [45000, 242], [45300, 242], [45600, 241], [45900, 240], [46200, 240], [46500, 239], [46800, 239], [47100, 238], [47400, 237], [47700, 237], [48000, 236], [48300, 235], [48600, 235], [48900, 234], [49200, 234], [49500, 233], [49800, 232], [50100, 232], [50400, 231], [50700, 231], [51000, 230], [51300, 229], [51600, 229], [51900, 228], [52200, 227], [52500, 227], [52800, 226], [53100, 226], [53400, 225], [53700, 224], [54000, 224], [54300, 223], [54600, 223], [54900, 222], [55200, 221], [55500, 221], [55800, 220], [56100, 219], [56400, 219], [56700, 218], [57000, 218], [57300, 217], [57600, 216], [57900, 216], [58200, 215], [58500, 214], [58800, 214], [59100, 213], [59400, 213], [59700, 212], [60000, 211], [60300, 211], [60600, 210], [60900, 210], [61200, 209], [61500, 206], [61800, 87], [62100, 87], [62400, 87], [62700, 87], [63000, 86], [63300, 86], [63600, 86], [63900, 86], [64200, 86], [64500, 86], [64800, 86], [65100, 86], [65400, 85], [65700, 85], [66000, 85], [66300, 85], [66600, 85], [66900, 85], [67200, 85], [67500, 85], [67800, 84], [68100, 84], [68400, 84], [68700, 84], [69000, 84], [69300, 84], [69600, 84], [69900, 84], [70200, 83], [70500, 83], [70800, 83], [71100, 83], [71400, 83], [71700, 83], [72000, 83], [72300, 83], [72600, 82], [72900, 82], [73200, 82], [73500, 82], [73800, 82], [74100, 82], [74400, 82], [74700, 82], [75000, 81], [75300, 81], [75600, 81], [75900, 81], [76200, 81], [76500, 81], [76800, 81], [77100, 81], [77400, 80], [77700, 80], [78000, 80], [78300, 80], [78600, 80], [78900, 80], [79200, 80], [79500, 80], [79800, 80], [80100, 79], [80400, 79], [80700, 79], [81000, 79], [81300, 79], [81600, 79], [81900, 79], [82200, 79], [82500, 78], [82800, 78], [83100, 78], [83400, 78], [83700, 78], [84000, 78], [84300, 78], [84600, 78], [84900, 77], [85200, 77], [85500, 77], [85800, 77], [86100, 77], [86400, 77], [86700, 77], [87000, 77], [87300, 76], [87600, 76], [87900, 76], [88200, 76], [88500, 76], [88800, 76], [89100, 76], [89400, 76], [89700, 75]], "point": [149, 149]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.78|+00.00|+02.22"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 16292], [16349, 243], [16649, 243], [16949, 243], [17249, 243], [17549, 12], [17571, 128], [17700, 91], [17849, 10], [17874, 124], [18000, 91], [18149, 8], [18176, 122], [18300, 91], [18449, 7], [18477, 120], [18600, 91], [18749, 5], [18778, 119], [18900, 91], [19049, 5], [19079, 117], [19200, 90], [19349, 4], [19380, 115], [19500, 90], [19649, 4], [19680, 115], [19800, 90], [19949, 4], [19980, 114], [20100, 90], [20249, 4], [20285, 108], [20400, 90], [20549, 4], [20586, 107], [20700, 90], [20849, 4], [20886, 106], [21000, 90], [21149, 4], [21186, 106], [21300, 89], [21449, 4], [21480, 4], [21487, 104], [21600, 89], [21749, 4], [21780, 4], [21787, 103], [21900, 89], [22049, 4], [22080, 4], [22086, 104], [22200, 89], [22349, 4], [22380, 4], [22386, 103], [22500, 89], [22649, 4], [22679, 4], [22686, 103], [22800, 89], [22948, 5], [22979, 4], [22986, 102], [23100, 89], [23248, 5], [23279, 3], [23286, 101], [23400, 90], [23548, 5], [23579, 2], [23585, 102], [23700, 90], [23847, 6], [23884, 102], [24000, 90], [24147, 6], [24184, 101], [24300, 90], [24446, 6], [24483, 102], [24600, 90], [24746, 6], [24782, 102], [24900, 91], [25046, 6], [25080, 104], [25200, 93], [25344, 8], [25378, 105], [25500, 98], [25638, 14], [25678, 104], [25800, 104], [25932, 20], [25978, 104], [26100, 153], [26278, 103], [26400, 153], [26578, 102], [26700, 137], [26845, 8], [26878, 102], [27000, 135], [27147, 6], [27177, 102], [27300, 134], [27449, 5], [27477, 102], [27600, 133], [27750, 5], [27776, 102], [27900, 132], [28051, 5], [28076, 101], [28200, 132], [28351, 6], [28376, 101], [28500, 132], [28652, 4], [28677, 99], [28800, 131], [28953, 3], [28977, 99], [29100, 131], [29253, 3], [29278, 97], [29400, 131], [29553, 3], [29578, 96], [29700, 131], [29853, 2], [29878, 96], [30000, 131], [30153, 2], [30178, 95], [30300, 131], [30453, 3], [30478, 94], [30600, 131], [30753, 3], [30778, 94], [30900, 131], [31053, 3], [31078, 93], [31200, 132], [31353, 3], [31377, 94], [31500, 132], [31652, 5], [31677, 93], [31800, 133], [31952, 5], [31976, 93], [32100, 134], [32251, 7], [32275, 94], [32400, 134], [32550, 9], [32575, 93], [32700, 136], [32849, 11], [32873, 95], [33000, 137], [33147, 14], [33172, 95], [33300, 139], [33445, 18], [33470, 96], [33600, 266], [33900, 265], [34200, 264], [34500, 264], [34800, 263], [35100, 263], [35400, 262], [35700, 261], [36000, 261], [36300, 260], [36600, 260], [36900, 259], [37200, 258], [37500, 258], [37800, 257], [38100, 256], [38400, 256], [38700, 255], [39000, 255], [39300, 254], [39600, 253], [39900, 253], [40200, 252], [40500, 252], [40800, 251], [41100, 250], [41400, 250], [41700, 249], [42000, 248], [42300, 248], [42600, 247], [42900, 247], [43200, 246], [43500, 245], [43800, 245], [44100, 244], [44400, 243], [44700, 243], [45000, 242], [45300, 242], [45600, 241], [45900, 240], [46200, 240], [46500, 239], [46800, 239], [47100, 238], [47400, 237], [47700, 237], [48000, 236], [48300, 235], [48600, 235], [48900, 234], [49200, 234], [49500, 233], [49800, 232], [50100, 232], [50400, 231], [50700, 231], [51000, 230], [51300, 229], [51600, 229], [51900, 228], [52200, 227], [52500, 227], [52800, 226], [53100, 226], [53400, 225], [53700, 224], [54000, 224], [54300, 223], [54600, 223], [54900, 222], [55200, 221], [55500, 221], [55800, 220], [56100, 219], [56400, 219], [56700, 218], [57000, 218], [57300, 217], [57600, 216], [57900, 216], [58200, 215], [58500, 214], [58800, 214], [59100, 213], [59400, 213], [59700, 212], [60000, 211], [60300, 211], [60600, 210], [60900, 210], [61200, 209], [61500, 206], [61800, 87], [62100, 87], [62400, 87], [62700, 87], [63000, 86], [63300, 86], [63600, 86], [63900, 86], [64200, 86], [64500, 86], [64800, 86], [65100, 86], [65400, 85], [65700, 85], [66000, 85], [66300, 85], [66600, 85], [66900, 85], [67200, 85], [67500, 85], [67800, 84], [68100, 84], [68400, 84], [68700, 84], [69000, 84], [69300, 84], [69600, 84], [69900, 84], [70200, 83], [70500, 83], [70800, 83], [71100, 83], [71400, 83], [71700, 83], [72000, 83], [72300, 83], [72600, 82], [72900, 82], [73200, 82], [73500, 82], [73800, 82], [74100, 82], [74400, 82], [74700, 82], [75000, 81], [75300, 81], [75600, 81], [75900, 81], [76200, 81], [76500, 81], [76800, 81], [77100, 81], [77400, 80], [77700, 80], [78000, 80], [78300, 80], [78600, 80], [78900, 80], [79200, 80], [79500, 80], [79800, 80], [80100, 79], [80400, 79], [80700, 79], [81000, 79], [81300, 79], [81600, 79], [81900, 79], [82200, 79], [82500, 78], [82800, 78], [83100, 78], [83400, 78], [83700, 78], [84000, 78], [84300, 78], [84600, 78], [84900, 77], [85200, 77], [85500, 77], [85800, 77], [86100, 77], [86400, 77], [86700, 77], [87000, 77], [87300, 76], [87600, 76], [87900, 76], [88200, 76], [88500, 76], [88800, 76], [89100, 76], [89400, 76], [89700, 75]], "point": [149, 149]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan25", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -1.75, "y": 0.9009992, "z": 2.0}, "object_poses": [{"objectName": "Potato_a3c14b3a", "position": {"x": -2.87722135, "y": 0.9602245, "z": 1.39082921}, "rotation": {"x": 0.0, "y": 89.99987, "z": 0.0}}, {"objectName": "Potato_a3c14b3a", "position": {"x": -2.375244, "y": 0.8623832, "z": 0.0556365252}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_3943cbe8", "position": {"x": -2.60917473, "y": 0.165891767, "z": 1.43180728}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_eb848da8", "position": {"x": -0.1476208, "y": 0.823164165, "z": 0.5003637}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_df0734fa", "position": {"x": -0.832649052, "y": 0.7113168, "z": 0.445048451}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_df0734fa", "position": {"x": -2.375244, "y": 0.8247294, "z": 0.4262423}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_77ce7117", "position": {"x": -2.49167442, "y": 0.838599861, "z": 0.20387885}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_61785f6c", "position": {"x": -2.88190246, "y": 1.43610919, "z": 0.6745089}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_61785f6c", "position": {"x": -2.6190834, "y": 1.84939921, "z": 2.42067075}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_29dab58d", "position": {"x": -1.48173487, "y": 0.7072133, "z": 0.295923918}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_4c742e05", "position": {"x": -2.71943927, "y": 1.43164122, "z": 0.228156239}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "SaltShaker_69e12ad1", "position": {"x": -2.49167442, "y": 0.818599939, "z": 0.0556365252}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_407d7c3b", "position": {"x": -0.314988971, "y": 0.8185999, "z": 0.5003637}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_407d7c3b", "position": {"x": -0.8170933, "y": 0.8185999, "z": 0.20387876}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b2f1312c", "position": {"x": -2.837208, "y": 1.22766507, "z": 2.39759}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Lettuce_b2f1312c", "position": {"x": -2.78199983, "y": 1.529565, "z": 2.133755}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Mug_57da2f86", "position": {"x": -2.72679329, "y": 0.8198504, "z": 2.30964565}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Mug_57da2f86", "position": {"x": -2.83720779, "y": 1.44061768, "z": 2.309645}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_0dd786e0", "position": {"x": -2.25881338, "y": 0.8965656, "z": 0.500363469}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_0dd786e0", "position": {"x": -2.77757359, "y": 0.9944069, "z": 1.178651}, "rotation": {"x": 0.0, "y": 89.99987, "z": 0.0}}, {"objectName": "Apple_0dd786e0", "position": {"x": -0.362648815, "y": 0.129467249, "z": 2.64073944}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_2c8fe53a", "position": {"x": -0.430375367, "y": 0.697826743, "z": 0.9493869}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Ladle_3943cbe8", "position": {"x": -2.54089117, "y": 1.89342153, "z": 1.97887111}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_e0764688", "position": {"x": -0.2442, "y": 0.8297, "z": 1.684}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_3a4a4d0d", "position": {"x": -2.05799365, "y": 0.123089075, "z": 0.362672359}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_ccf3345d", "position": {"x": -0.531748533, "y": 0.8226794, "z": 2.09645438}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_2ee69558", "position": {"x": -2.67158437, "y": 1.49514139, "z": 1.95786524}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Potato_a3c14b3a", "position": {"x": -1.15182948, "y": 0.8623831, "z": 0.278}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_69e12ad1", "position": {"x": -2.813009, "y": 0.8185999, "z": 0.4622569}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_29dab58d", "position": {"x": -1.8856113, "y": 0.130002677, "z": 0.440719754}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_77ce7117", "position": {"x": -2.60810471, "y": 0.838599861, "z": 0.20387888}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_298dd93a", "position": {"x": -2.211642, "y": 0.8492183, "z": 0.5084426}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Egg_68224885", "position": {"x": -0.429344922, "y": 0.08077991, "z": 2.53909016}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_61785f6c", "position": {"x": -0.400792, "y": 0.823938131, "z": 1.32607329}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_d1baac51", "position": {"x": -2.4939487, "y": 0.812210739, "z": 0.476511866}, "rotation": {"x": 0.0, "y": 50.7348976, "z": 0.0}}, {"objectName": "Lettuce_b2f1312c", "position": {"x": -1.8365, "y": 0.7415, "z": 0.238453716}, "rotation": {"x": 0.0, "y": 55.91092, "z": 0.0}}, {"objectName": "PepperShaker_407d7c3b", "position": {"x": -1.52280354, "y": 0.1203081, "z": 0.362672359}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_eb848da8", "position": {"x": -2.504354, "y": 0.823164165, "z": 0.7895261}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_db4f73f8", "position": {"x": -0.979999542, "y": 0.8554753, "z": 0.249999955}, "rotation": {"x": -5.270255e-06, "y": 29.9999123, "z": -4.99874841e-06}}, {"objectName": "Spoon_df0734fa", "position": {"x": -2.56398416, "y": 0.693495333, "z": 1.47479427}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_57da2f86", "position": {"x": -0.646999955, "y": 0.8882107, "z": 0.3674}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_4c742e05", "position": {"x": -2.78199983, "y": 0.8214564, "z": 2.13375449}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}], "object_toggles": [], "random_seed": 1083547144, "scene_num": 25}, "task_id": "trial_T20190908_225310_446083", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A19LVWX8ZLO6CS_37XITHEISZ05YETPQ673ZAKFQB7CRN", "high_descs": ["Turn left, step forward, then turn right and walk to the microwave.", "Open the microwave, remove the potato in the back, close the microwave.", "Turn right and step forward, then turn left to face the fridge.", "Open the fridge, place the potato to the left of the cup and close the door.", "Turn around and step forward, then turn right and walk to the kitchen sink.", "Take the potato to the left of the sink next to the bread.", "Return to the fridge.", "Open the fridge, place the potato to the right of the other potato."], "task_desc": "Place two potatoes in the fridge.", "votes": [1, 1, 1, 1, 1, 1, 1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3TS1AR6UQT52J7TK8UZY2ZEIMBU7FH", "high_descs": ["Turn left and walk towards the sink, then turn right and walk up to the microwave.", "Open the microwave and take the potato out of the microwave then close the microwave.", "Turn right and walk over to the fridge.", "Put the sweet potato in the fridge on the top shelf then close the door.", "Turn around and walk towards the oven, then turn right and walk up to the counter.", "Pick up the potato off of the counter to the left of the sink.", "Turn around and walk towards the door, turn left and walk up to the fridge.", "Open the fridge and put the potato inside next to the potato that is already in there."], "task_desc": "Put two potatoes in the fridge.", "votes": [1, 1, 1, 1, 1, 1, 1, 1]}, {"assignment_id": "A2A4UAFZ5LW71K_33CUSNVVNQ3X46OGALLY67TRBIJ883", "high_descs": ["take two steps to the left to face microwave", "open microwave, take potato out, close microwave", "turn right, take a few steps, turn left to face fridge", "put potato in the fridge", "turn around, take a few steps, turn left, walk to sink", "pick up the potato that is on the counter", "turn around, walk across room, turn left, walk to fridge", "put potato in fridge"], "task_desc": "put two potatoes in fridge", "votes": [1, 1]}]}}