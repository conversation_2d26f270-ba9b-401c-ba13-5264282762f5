{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 36}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-2|3|3|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-5.05565644, -5.05565644, 2.6957504, 2.6957504, 5.290096, 5.290096]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-5.204, -5.204, 3.952, 3.952, 0.032000004, 0.032000004]], "forceVisible": true, "objectId": "Egg|-01.26|+01.32|+00.67"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-2|0|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-2|4|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-5.05565644, -5.05565644, 2.6957504, 2.6957504, 5.290096, 5.290096]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-5.204, -5.204, 3.952, 3.952, 0.032000004, 0.032000004]], "forceVisible": true, "objectId": "Egg|-01.26|+01.32|+00.67", "receptacleObjectId": "<PERSON><PERSON>|-01.30|+00.01|+00.99"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-01.30|+00.01|+00.99"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 73800], [73801, 299], [74101, 299], [74401, 299], [74702, 298], [75002, 298], [75303, 297], [75603, 297], [75904, 296], [76204, 296], [76504, 296], [76805, 295], [77105, 295], [77406, 294], [77706, 294], [78006, 294], [78307, 293], [78607, 293], [78908, 292], [79208, 292], [79509, 291], [79809, 291], [80109, 291], [80410, 290], [80710, 290], [81011, 289], [81311, 289], [81612, 288], [81912, 288], [82212, 288], [82513, 287], [82813, 287], [83114, 286], [83414, 286], [83715, 285], [84015, 285], [84315, 285], [84616, 284], [84916, 284], [85217, 283], [85517, 283], [85817, 283], [86118, 282], [86418, 282], [86719, 281], [87019, 281], [87320, 280], [87620, 280], [87920, 280], [88221, 279], [88521, 279], [88822, 278], [89122, 278], [89423, 277], [89723, 277]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.26|+01.32|+00.67"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [125, 103, 144, 113], "mask": [[30725, 20], [31025, 20], [31325, 20], [31625, 20], [31925, 19], [32225, 19], [32526, 17], [32826, 16], [33127, 14], [33428, 11], [33730, 7]], "point": [134, 107]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-01.30|+00.01|+00.99"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 4040], [4085, 251], [4390, 245], [4690, 246], [4990, 246], [5290, 246], [5590, 246], [5890, 246], [6190, 246], [6490, 246], [6790, 246], [7090, 246], [7390, 246], [7690, 246], [7990, 246], [8290, 246], [8590, 56], [8666, 170], [8890, 43], [8986, 151], [9188, 41], [9297, 141], [9488, 41], [9600, 138], [9787, 41], [9900, 138], [10087, 42], [10200, 138], [10387, 42], [10500, 139], [10686, 43], [10800, 139], [10986, 43], [11100, 139], [11286, 43], [11400, 139], [11586, 43], [11700, 139], [11885, 45], [12000, 140], [12185, 45], [12300, 140], [12485, 45], [12600, 140], [12785, 45], [12900, 140], [13084, 46], [13200, 141], [13384, 46], [13500, 141], [13684, 47], [13800, 141], [13983, 48], [14100, 141], [14283, 48], [14400, 142], [14583, 48], [14700, 142], [14883, 49], [15000, 142], [15182, 50], [15300, 142], [15482, 50], [15600, 142], [15782, 50], [15900, 143], [16082, 50], [16200, 143], [16381, 51], [16500, 143], [16681, 52], [16799, 144], [16981, 52], [17098, 146], [17280, 53], [17398, 146], [17580, 53], [17697, 147], [17880, 53], [17996, 148], [18180, 54], [18296, 148], [18479, 55], [18595, 150], [18779, 57], [18895, 150], [19079, 60], [19194, 151], [19379, 63], [19493, 152], [19678, 68], [19792, 154], [19978, 74], [20090, 156], [20278, 83], [20384, 162], [20577, 269], [20877, 270], [21177, 270], [21477, 270], [21776, 271], [22076, 271], [22376, 272], [22675, 273], [22975, 274], [23274, 277], [23572, 285], [23867, 4633], [28501, 299], [28801, 299], [29101, 299], [29401, 299], [29702, 298], [30002, 298], [30302, 298], [30603, 297], [30903, 297], [31203, 297], [31504, 296], [31804, 296], [32104, 296], [32404, 296], [32705, 295], [33005, 295], [33305, 295], [33606, 294], [33906, 294], [34206, 294], [34507, 293], [34807, 293], [35107, 293], [35407, 293], [35708, 292], [36008, 292], [36308, 292], [36609, 291], [36909, 291], [37209, 291], [37509, 291], [37810, 290], [38110, 290], [38410, 290], [38711, 289], [39011, 289], [39311, 289], [39612, 288], [39912, 288], [40212, 288], [40512, 288], [40813, 287], [41113, 287], [41413, 287], [41714, 286], [42014, 286], [42314, 286], [42615, 285], [42915, 285], [43215, 285], [43515, 285], [43816, 284], [44116, 284], [44416, 284], [44717, 283], [45017, 283], [45317, 283], [45617, 283], [45918, 282], [46218, 282], [46518, 282], [46819, 281], [47119, 281], [47419, 281], [47720, 280], [48020, 280], [48320, 280], [48620, 280], [48921, 279], [49221, 279], [49521, 279], [49822, 278], [50122, 278], [50422, 278], [50722, 278], [51023, 277], [51323, 277], [51623, 277], [51924, 276], [52224, 276], [52524, 276], [52825, 275], [53125, 275], [53425, 275], [53725, 275], [54026, 274], [54326, 274], [54626, 274], [54927, 273], [55227, 273], [55527, 273], [55828, 272], [56128, 272], [56430, 270], [56729, 271], [57029, 271], [57329, 271], [57629, 271], [57930, 270], [58230, 270], [58530, 270], [58830, 270], [59131, 269], [59431, 269], [59731, 269], [60032, 268], [60332, 268], [60632, 268], [60933, 267], [61233, 267], [61533, 267], [61833, 267], [62134, 266], [62434, 266], [62734, 266], [63035, 265], [63335, 265], [63635, 265], [63936, 264], [64236, 83], [64342, 101], [64467, 33], [64536, 84], [64642, 101], [64767, 33], [64836, 84], [64942, 101], [65067, 33], [65137, 83], [65242, 101], [65366, 34], [65437, 83], [65542, 101], [65666, 34], [65737, 83], [65842, 101], [65966, 34], [66038, 82], [66142, 101], [66265, 35], [66338, 82], [66442, 101], [66565, 35], [66638, 82], [66742, 102], [66865, 35], [66938, 82], [67042, 103], [67165, 35], [67239, 81], [67342, 103], [67464, 36], [67539, 81], [67642, 105], [67764, 36], [67839, 81], [67942, 106], [68063, 37], [68140, 81], [68242, 108], [68362, 38], [68440, 81], [68542, 111], [68660, 40], [68740, 81], [68842, 158], [69041, 80], [69142, 158], [69341, 80], [69442, 158], [69641, 80], [69742, 158], [69941, 80], [70042, 158], [70242, 80], [70341, 159], [70542, 80], [70641, 159], [70842, 81], [70940, 160], [71143, 81], [71239, 6], [71255, 145], [71443, 82], [71537, 6], [71557, 143], [71743, 85], [71834, 7], [71859, 141], [72044, 96], [72160, 140], [72344, 95], [72461, 139], [72644, 94], [72762, 138], [72944, 93], [73063, 137], [73245, 92], [73363, 137], [73545, 91], [73664, 136], [73845, 91], [73964, 136], [74146, 89], [74265, 135], [74446, 88], [74566, 134], [74746, 88], [74866, 134], [75046, 87], [75167, 133], [75347, 86], [75467, 133], [75647, 86], [75767, 133], [75947, 86], [76067, 133], [76248, 84], [76368, 132], [76548, 84], [76668, 132], [76848, 84], [76968, 132], [77149, 83], [77268, 132], [77449, 83], [77568, 132], [77749, 83], [77868, 132], [78049, 83], [78168, 132], [78350, 82], [78468, 132], [78650, 82], [78768, 132], [78950, 82], [79068, 132], [79251, 81], [79368, 132], [79551, 81], [79668, 132], [79851, 81], [79968, 132], [80152, 81], [80267, 133], [80452, 81], [80567, 133], [80752, 81], [80867, 133], [81052, 82], [81166, 134], [81353, 81], [81466, 134], [81653, 81], [81766, 134], [81953, 82], [82065, 135], [82254, 82], [82364, 136], [82554, 82], [82664, 136], [82854, 83], [82963, 137], [83154, 84], [83262, 138], [83455, 84], [83561, 139], [83755, 85], [83860, 140], [84055, 87], [84159, 141], [84356, 88], [84456, 144], [84656, 91], [84754, 146], [84956, 244], [85257, 243], [85557, 243], [85857, 243], [86157, 243], [86458, 242], [86758, 242], [87058, 242], [87359, 241], [87659, 241], [87959, 241], [88260, 240], [88560, 240], [88860, 240], [89160, 240], [89461, 239], [89761, 239]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.26|+01.32|+00.67", "placeStationary": true, "receptacleObjectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 236], [20744, 236], [21044, 236], [21343, 236], [21643, 236], [21942, 237], [22241, 237], [22541, 237], [22840, 237], [23140, 237], [23439, 238], [23739, 237], [24038, 238], [24337, 239], [24637, 238], [24936, 239], [25236, 238], [25535, 239], [25834, 240], [26134, 239], [26433, 240], [26733, 239], [27032, 240], [27331, 241], [27631, 240], [27930, 241], [28230, 241], [28529, 241], [28829, 241], [29128, 241], [29427, 242], [29727, 242], [30026, 242], [30326, 242], [30625, 243], [30924, 243], [31224, 243], [31523, 243], [31823, 243], [32122, 244], [32421, 244], [32721, 244], [33020, 244], [33320, 244], [33619, 245], [33918, 245], [34218, 245], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 236], [20744, 236], [21044, 236], [21343, 236], [21643, 236], [21942, 237], [22241, 237], [22541, 237], [22840, 237], [23140, 237], [23439, 238], [23739, 237], [24038, 238], [24337, 239], [24637, 238], [24936, 239], [25236, 238], [25535, 239], [25834, 240], [26134, 239], [26433, 240], [26733, 239], [27032, 240], [27331, 241], [27631, 240], [27930, 241], [28230, 116], [28351, 120], [28529, 116], [28652, 118], [28829, 115], [28953, 117], [29128, 115], [29254, 115], [29427, 115], [29554, 115], [29727, 115], [29855, 114], [30026, 115], [30155, 113], [30326, 115], [30456, 112], [30625, 116], [30756, 112], [30924, 117], [31056, 111], [31224, 117], [31356, 111], [31523, 118], [31656, 110], [31823, 118], [31956, 110], [32122, 119], [32256, 110], [32421, 121], [32555, 110], [32721, 121], [32855, 110], [33020, 123], [33154, 110], [33320, 123], [33453, 111], [33619, 126], [33752, 112], [33918, 128], [34050, 113], [34218, 245], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.26|+01.32|+00.67"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [141, 95, 155, 114], "mask": [[28346, 5], [28645, 7], [28944, 9], [29243, 11], [29542, 12], [29842, 13], [30141, 14], [30441, 15], [30741, 15], [31041, 15], [31341, 15], [31641, 15], [31941, 15], [32241, 15], [32542, 13], [32842, 13], [33143, 11], [33443, 10], [33745, 7], [34046, 4]], "point": [148, 103]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 236], [20744, 236], [21044, 236], [21343, 236], [21643, 236], [21942, 237], [22241, 237], [22541, 237], [22840, 237], [23140, 237], [23439, 238], [23739, 237], [24038, 238], [24337, 239], [24637, 238], [24936, 239], [25236, 238], [25535, 239], [25834, 240], [26134, 239], [26433, 240], [26733, 239], [27032, 240], [27331, 241], [27631, 240], [27930, 241], [28230, 241], [28529, 241], [28829, 241], [29128, 241], [29427, 242], [29727, 242], [30026, 242], [30326, 242], [30625, 243], [30924, 243], [31224, 243], [31523, 243], [31823, 243], [32122, 244], [32421, 244], [32721, 244], [33020, 244], [33320, 244], [33619, 245], [33918, 245], [34218, 245], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-01.30|+00.01|+00.99"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 233], "mask": [[0, 56399], [56400, 298], [56700, 297], [57000, 295], [57300, 294], [57601, 292], [57902, 289], [58204, 286], [58505, 284], [58807, 280], [59108, 278], [59409, 276], [59711, 272], [60012, 270], [60314, 267], [60615, 264], [60916, 262], [61218, 259], [61519, 256], [61821, 253], [62122, 251], [62423, 248], [62725, 245], [63026, 243], [63327, 240], [63629, 237], [63930, 235], [64232, 231], [64533, 229], [64834, 227], [65136, 223], [65437, 221], [65739, 218], [66040, 215], [66341, 213], [66643, 210], [66944, 207], [67246, 204], [67547, 202], [67848, 199], [68150, 196], [68451, 194], [68752, 191], [69054, 188], [69355, 186], [69657, 182]], "point": [149, 116]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.26|+01.32|+00.67", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-01.30|+00.01|+00.99"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 22254], [22279, 107], [22408, 146], [22579, 107], [22708, 147], [22879, 107], [23008, 147], [23180, 106], [23307, 149], [23480, 106], [23607, 149], [23780, 105], [23907, 150], [24081, 104], [24206, 151], [24381, 104], [24506, 151], [24681, 105], [24806, 152], [24982, 104], [25106, 152], [25282, 104], [25405, 154], [25582, 105], [25705, 154], [25883, 105], [26004, 155], [26183, 106], [26303, 157], [26483, 108], [26601, 159], [26783, 112], [26897, 164], [27083, 278], [27383, 279], [27683, 279], [27983, 279], [28282, 281], [28582, 282], [28881, 284], [29179, 287], [29478, 291], [29775, 13725], [43501, 299], [43802, 298], [44103, 297], [44404, 296], [44705, 295], [45006, 294], [45307, 293], [45608, 292], [45909, 291], [46210, 290], [46511, 289], [46812, 288], [47113, 287], [47414, 286], [47715, 285], [48016, 284], [48317, 283], [48618, 282], [48919, 281], [49220, 280], [49521, 279], [49822, 278], [50123, 277], [50424, 276], [50724, 276], [51025, 275], [51326, 274], [51627, 273], [51928, 272], [52229, 271], [52530, 270], [52831, 269], [53132, 268], [53433, 267], [53734, 266], [54035, 265], [54336, 264], [54637, 263], [54938, 262], [55239, 261], [55540, 260], [55841, 259], [56142, 258], [56443, 257], [56744, 256], [57045, 255], [57346, 254], [57647, 253], [57948, 252], [58249, 251], [58549, 251], [58850, 250], [59151, 249], [59452, 248], [59753, 247], [60054, 246], [60355, 245], [60656, 244], [60957, 243], [61258, 242], [61559, 241], [61860, 240], [62162, 172], [62335, 65], [62635, 65], [62935, 65], [63235, 65], [63535, 65], [63835, 65], [64135, 65], [64436, 64], [64736, 64], [65036, 64], [65336, 64], [65636, 64], [65936, 64], [66237, 63], [66537, 63], [66837, 63], [67137, 63], [67437, 63], [67737, 63], [68038, 62], [68338, 62], [68638, 62], [68938, 62], [69238, 62], [69538, 62], [69839, 61], [70139, 61], [70439, 61], [70739, 61], [71039, 61], [71339, 61], [71640, 60], [71940, 60], [72240, 60], [72540, 60], [72840, 60], [73140, 60], [73441, 59], [73741, 59], [74041, 59], [74341, 59], [74641, 59], [74941, 59], [75242, 58], [75542, 58], [75842, 58], [76142, 58], [76442, 58], [76742, 58], [77043, 57], [77343, 57], [77643, 57], [77943, 57], [78243, 57], [78543, 57], [78844, 56], [79144, 56], [79444, 56], [79744, 56], [80044, 56], [80344, 56], [80645, 55], [80945, 55], [81245, 55], [81545, 55], [81845, 55], [82145, 55], [82446, 54], [82746, 54], [83046, 54], [83346, 54], [83646, 54], [83946, 54], [84247, 53], [84547, 53], [84847, 53], [85147, 53], [85447, 53], [85747, 53], [86048, 52], [86348, 52], [86648, 52], [86948, 52], [87248, 52], [87548, 52], [87849, 51], [88149, 51], [88449, 51], [88749, 51], [89049, 51], [89349, 51], [89650, 50], [89950, 50]], "point": [149, 149]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-01.30|+00.01|+00.99"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 22254], [22279, 107], [22408, 146], [22579, 107], [22708, 147], [22879, 107], [23008, 147], [23180, 106], [23307, 149], [23480, 106], [23607, 149], [23780, 105], [23907, 150], [24081, 104], [24206, 151], [24381, 104], [24506, 151], [24681, 105], [24806, 152], [24982, 104], [25106, 152], [25282, 104], [25405, 154], [25582, 105], [25705, 154], [25883, 105], [26004, 155], [26183, 106], [26303, 157], [26483, 108], [26601, 159], [26783, 112], [26897, 164], [27083, 278], [27383, 279], [27683, 279], [27983, 279], [28282, 281], [28582, 282], [28881, 284], [29179, 287], [29478, 291], [29775, 3969], [33750, 293], [34051, 291], [34352, 289], [34653, 288], [34953, 287], [35253, 287], [35554, 286], [35854, 286], [36154, 286], [36454, 286], [36754, 286], [37053, 288], [37353, 289], [37652, 291], [37951, 293], [38250, 5250], [43501, 299], [43802, 298], [44103, 297], [44404, 296], [44705, 295], [45006, 294], [45307, 293], [45608, 292], [45909, 291], [46210, 290], [46511, 289], [46812, 288], [47113, 287], [47414, 286], [47715, 285], [48016, 284], [48317, 283], [48618, 282], [48919, 281], [49220, 280], [49521, 279], [49822, 278], [50123, 277], [50424, 276], [50724, 276], [51025, 275], [51326, 274], [51627, 273], [51928, 272], [52229, 271], [52530, 270], [52831, 269], [53132, 268], [53433, 267], [53734, 266], [54035, 265], [54336, 264], [54637, 263], [54938, 262], [55239, 261], [55540, 260], [55841, 259], [56142, 258], [56443, 257], [56744, 256], [57045, 255], [57346, 254], [57647, 253], [57948, 252], [58249, 251], [58549, 251], [58850, 250], [59151, 249], [59452, 248], [59753, 247], [60054, 246], [60355, 245], [60656, 244], [60957, 243], [61258, 242], [61559, 241], [61860, 240], [62162, 172], [62335, 65], [62635, 65], [62935, 65], [63235, 65], [63535, 65], [63835, 65], [64135, 65], [64436, 64], [64736, 64], [65036, 64], [65336, 64], [65636, 64], [65936, 64], [66237, 63], [66537, 63], [66837, 63], [67137, 63], [67437, 63], [67737, 63], [68038, 62], [68338, 62], [68638, 62], [68938, 62], [69238, 62], [69538, 62], [69839, 61], [70139, 61], [70439, 61], [70739, 61], [71039, 61], [71339, 61], [71640, 60], [71940, 60], [72240, 60], [72540, 60], [72840, 60], [73140, 60], [73441, 59], [73741, 59], [74041, 59], [74341, 59], [74641, 59], [74941, 59], [75242, 58], [75542, 58], [75842, 58], [76142, 58], [76442, 58], [76742, 58], [77043, 57], [77343, 57], [77643, 57], [77943, 57], [78243, 57], [78543, 57], [78844, 56], [79144, 56], [79444, 56], [79744, 56], [80044, 56], [80344, 56], [80645, 55], [80945, 55], [81245, 55], [81545, 55], [81845, 55], [82145, 55], [82446, 54], [82746, 54], [83046, 54], [83346, 54], [83646, 54], [83946, 54], [84247, 53], [84547, 53], [84847, 53], [85147, 53], [85447, 53], [85747, 53], [86048, 52], [86348, 52], [86648, 52], [86948, 52], [87248, 52], [87548, 52], [87849, 51], [88149, 51], [88449, 51], [88749, 51], [89049, 51], [89349, 51], [89650, 50], [89950, 50]], "point": [149, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan17", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -0.5, "y": 0.908999562, "z": 2.5}, "object_poses": [{"objectName": "SprayBottle_4f8d0eee", "position": {"x": 1.52327764, "y": 0.9109041, "z": -0.4283477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_4f8d0eee", "position": {"x": 1.21904433, "y": 0.115259349, "z": 1.00600934}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_e3d6b27e", "position": {"x": -1.19785714, "y": 0.746611655, "z": 2.273428}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_e3d6b27e", "position": {"x": 1.09901655, "y": 0.773710251, "z": 0.7332704}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": -1.12367022, "y": 0.9121421, "z": -0.5099766}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_6b7178a8", "position": {"x": -1.26391411, "y": 0.752086341, "z": 0.6739376}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Fork_9f8771c2", "position": {"x": 1.117845, "y": 0.886848569, "z": 2.58481264}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_9f8771c2", "position": {"x": 1.03472006, "y": 0.886848569, "z": 2.692625}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": 1.36534023, "y": 0.9264999, "z": 0.7067195}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": 0.726627469, "y": 2.15623665, "z": -0.7877707}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": -1.29051554, "y": 0.7497855, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_268c1c72", "position": {"x": -0.6586952, "y": 0.9340927, "z": -0.5894883}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_268c1c72", "position": {"x": 1.444309, "y": 0.9334927, "z": -0.4283477}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_e0c21b7c", "position": {"x": 1.117845, "y": 0.8876286, "z": 2.80043745}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_74101882", "position": {"x": 1.28637147, "y": 0.9749149, "z": 0.1391859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6438ff8b", "position": {"x": -1.43764663, "y": 0.983068466, "z": -0.477608353}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_8429d040", "position": {"x": 1.20740271, "y": 0.9078062, "z": 0.7067195}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b6eaf348", "position": {"x": 1.3580929, "y": 1.65683758, "z": -0.04280874}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_7daf87a0", "position": {"x": -1.26391411, "y": 1.69716144, "z": 0.680034637}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_7daf87a0", "position": {"x": 1.36534023, "y": 0.971704, "z": 0.4229527}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_74101882", "position": {"x": -1.01254034, "y": 0.812314868, "z": 2.08785558}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_6018293c", "position": {"x": 1.30178607, "y": 0.0869684, "z": 1.3777349}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_9f8771c2", "position": {"x": 1.36722, "y": 0.886848569, "z": 2.477}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Kettle_c40d8d1f", "position": {"x": 0.802, "y": 0.9393, "z": -0.7406}, "rotation": {"x": 0.0, "y": 89.9998245, "z": 0.0}}, {"objectName": "Tomato_57296338", "position": {"x": 1.128434, "y": 0.961228848, "z": -0.144580841}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": 1.325258, "y": 1.65666533, "z": -0.393620074}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_e3d6b27e", "position": {"x": -1.10519874, "y": 0.747337937, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6438ff8b", "position": {"x": -1.10519874, "y": 0.8205948, "z": 2.459}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_92502562", "position": {"x": 0.5029, "y": 0.9393, "z": -0.7406}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_e0c21b7c", "position": {"x": 0.951595068, "y": 0.8876286, "z": 2.80043745}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_6b7178a8", "position": {"x": -1.32097566, "y": 0.752086341, "z": 1.19737482}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": 1.36722, "y": 0.9012999, "z": 2.15356255}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_7e041f70", "position": {"x": -1.3209753, "y": 1.41969812, "z": 1.09268737}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_7daf87a0", "position": {"x": 1.117845, "y": 0.946504, "z": 2.261375}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_572193ee", "position": {"x": -1.14979446, "y": 1.4205054, "z": 0.778625369}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Knife_268c1c72", "position": {"x": -1.01254034, "y": 0.7708927, "z": 2.459}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_1ede3fc6", "position": {"x": -1.26391411, "y": 1.322524, "z": 0.6739376}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_8429d040", "position": {"x": -0.314492047, "y": 0.908406138, "z": -0.933621049}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b6eaf348", "position": {"x": 1.28637147, "y": 0.9078062, "z": -0.4283477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_edec52fd", "position": {"x": 1.03472006, "y": 0.9312309, "z": 2.477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": -0.6586952, "y": 0.9121421, "z": -0.430464923}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_4b57442b", "position": {"x": 1.26269484, "y": 0.115424275, "z": 0.732311}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_a0065d6c", "position": {"x": -1.29051554, "y": 0.749235749, "z": 3.015717}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_1d6d1e51", "position": {"x": 1.284095, "y": 0.8874294, "z": 2.261375}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SprayBottle_4f8d0eee", "position": {"x": -1.3881619, "y": 2.15289426, "z": 0.763267636}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c297f602", "position": {"x": -0.0203592032, "y": 0.7535562, "z": -0.65188694}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 852337661, "scene_num": 17}, "task_id": "trial_T20190909_130835_721326", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2TBXASXZIRNNW_382M9COHEK6CB45KQR83U972IWDUEB", "high_descs": ["Walk over to the fridge", "Grab the egg out of the fridge", "Go to the microwave", "Heat the egg up in the microwave", "Head to the fridge again", "Place the egg in the bottom shelf of the fridge"], "task_desc": "Heat an egg and put it back in the fridge", "votes": [1, 1]}, {"assignment_id": "AO33H4GL9KZX9_3DHE4R9OCZ21B6S9D2OLOTXMCM42GF", "high_descs": ["Go to the front of the refrigerator on the right. ", "Remove the egg from the freezer. ", "Turn left and go to the microwave on the left of the refrigerator. ", "Remove the egg from the microwave after heating it. ", "Turn right and go back to the front of the refrigerator. ", "Place the egg into the refrigerator. "], "task_desc": "Put the heated frozen egg into the refrigerator. ", "votes": [1, 1]}, {"assignment_id": "A1OWHPMKE7YAGL_3UXUOQ9OKHO7NGCCXED6OY3RP7P7A0", "high_descs": ["Move forward, go to the fridge that is on the right.", "Take the egg out of the fridge.", "Turn to the left, go to the microwave that is on the right.", "Put the egg inside the microwave, turn it on to cook, remove the egg once it is done cooking.", "Turn to the right, go to the fridge that is on the left.", "Put the egg inside the fridge."], "task_desc": "Put a cooked egg inside the fridge.", "votes": [1, 1]}]}}