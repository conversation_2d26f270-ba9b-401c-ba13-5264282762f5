{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000267.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000268.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000269.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000270.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000271.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000272.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000273.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000274.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000275.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000276.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000277.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000278.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000279.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000280.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000281.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000282.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000283.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000284.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000285.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000286.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000287.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000288.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000289.png", "low_idx": 57}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-7|-13|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-6.5527892, -6.5527892, -14.47469808, -14.47469808, 3.311880588, 3.311880588]], "coordinateReceptacleObjectId": ["SinkBasin", [-7.1452, -7.1452, -14.9888, -14.9888, 3.12, 3.12]], "forceVisible": true, "objectId": "Egg|-01.64|+00.83|-03.62"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-3|1|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-0.576, -0.576, -3.632, -3.632, 3.615618228, 3.615618228]], "forceVisible": true, "objectId": "Microwave|-00.14|+00.90|-00.91"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-10|-9|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-6.5527892, -6.5527892, -14.47469808, -14.47469808, 3.311880588, 3.311880588]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-12.75556944, -12.75556944, -8.74, -8.74, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|-01.64|+00.83|-03.62", "receptacleObjectId": "<PERSON><PERSON>|-03.19|+00.00|-02.19"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.64|+00.83|-03.62"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [110, 147, 125, 170], "mask": [[43916, 4], [44215, 7], [44514, 9], [44813, 10], [45112, 12], [45412, 12], [45712, 13], [46011, 14], [46311, 15], [46611, 15], [46911, 15], [47210, 16], [47510, 16], [47810, 16], [48110, 16], [48411, 15], [48711, 15], [49011, 15], [49311, 14], [49612, 13], [49912, 12], [50213, 10], [50514, 9], [50815, 6]], "point": [117, 157]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.64|+00.83|-03.62", "placeStationary": true, "receptacleObjectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [59, 46, 299, 242], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17795, 205], [18094, 206], [18394, 206], [18694, 206], [18993, 207], [19293, 207], [19593, 207], [19892, 208], [20192, 208], [20492, 208], [20791, 209], [21091, 209], [21391, 209], [21690, 210], [21990, 210], [22290, 210], [22589, 211], [22889, 211], [23189, 210], [23488, 211], [23788, 210], [24088, 210], [24387, 211], [24687, 210], [24987, 210], [25286, 210], [25586, 210], [25886, 209], [26185, 210], [26485, 209], [26785, 209], [27084, 209], [27384, 209], [27684, 209], [27983, 209], [28283, 209], [28583, 208], [28883, 208], [29182, 208], [29482, 208], [29782, 207], [30081, 208], [30381, 208], [30681, 207], [30980, 208], [31280, 207], [31580, 207], [31879, 207], [32179, 207], [32479, 206], [32778, 207], [33078, 206], [33378, 206], [33677, 207], [33977, 206], [34277, 206], [34576, 206], [34876, 206], [35176, 205], [35475, 206], [35775, 205], [36075, 205], [36374, 206], [36674, 205], [36974, 205], [37273, 205], [37573, 205], [37873, 204], [38172, 205], [38472, 201], [38772, 201], [39071, 48], [39239, 34], [39371, 48], [39539, 34], [39671, 48], [39840, 33], [39970, 48], [40140, 32], [40270, 48], [40440, 32], [40570, 48], [40741, 30], [40869, 49], [41169, 49], [41469, 49], [41768, 50], [42068, 49], [42368, 49], [42667, 50], [42967, 50], [43267, 50], [43566, 51], [43866, 51], [44166, 51], [44465, 51], [44765, 51], [45065, 51], [45365, 51], [45664, 52], [45964, 52], [46264, 52], [46563, 53], [46863, 52], [47163, 52], [47462, 53], [47762, 53], [48062, 53], [48361, 54], [48661, 54], [48961, 53], [49260, 54], [49560, 54], [49860, 54], [50159, 55], [50460, 54], [50760, 54], [51060, 54], [51361, 52], [51661, 52], [51961, 52], [52262, 51], [52563, 50], [52864, 49], [53166, 47], [53468, 45], [53769, 43], [54071, 41], [54372, 40], [54674, 38], [54974, 38], [55275, 37], [55575, 37], [55875, 36], [56176, 35], [56476, 35], [56776, 35], [57077, 34], [57377, 34], [57677, 34], [57978, 33], [58278, 32], [58578, 32], [58879, 31], [59179, 31], [59479, 31], [59780, 30], [60080, 30], [60380, 30], [60681, 28], [60981, 28], [61281, 28], [61582, 27], [61882, 27], [62182, 27], [62483, 26], [62783, 26], [63083, 25], [63384, 24], [63684, 24], [63984, 24], [64285, 23], [64585, 23], [64885, 23], [65186, 21], [65486, 21], [65786, 21], [66087, 20], [66387, 20], [66687, 20], [66988, 19], [67288, 19], [67587, 19], [67887, 19], [68187, 19], [68487, 19], [68787, 19], [69086, 20], [69386, 20], [69686, 20], [69986, 22], [70286, 25], [70586, 26], [70885, 20], [70908, 4], [71185, 20], [71208, 5], [71486, 19], [71509, 3], [71786, 19], [72087, 17], [72387, 17]], "point": [179, 129]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [59, 46, 299, 242], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17795, 205], [18094, 206], [18394, 206], [18694, 206], [18993, 207], [19293, 207], [19593, 207], [19892, 208], [20192, 208], [20492, 208], [20791, 209], [21091, 209], [21391, 209], [21690, 210], [21990, 210], [22290, 210], [22589, 211], [22889, 211], [23189, 210], [23488, 211], [23788, 210], [24088, 210], [24387, 211], [24687, 210], [24987, 210], [25286, 210], [25586, 210], [25886, 209], [26185, 210], [26485, 209], [26785, 78], [26867, 127], [27084, 77], [27168, 125], [27384, 76], [27469, 124], [27684, 76], [27770, 123], [27983, 76], [28071, 121], [28283, 76], [28371, 121], [28583, 75], [28671, 120], [28883, 75], [28972, 119], [29182, 76], [29272, 118], [29482, 75], [29572, 118], [29782, 75], [29872, 117], [30081, 76], [30172, 117], [30381, 76], [30472, 117], [30681, 76], [30772, 116], [30980, 78], [31072, 116], [31280, 78], [31371, 116], [31580, 78], [31671, 116], [31879, 80], [31970, 116], [32179, 80], [32270, 116], [32479, 81], [32569, 116], [32778, 84], [32867, 118], [33078, 206], [33378, 206], [33677, 207], [33977, 206], [34277, 206], [34576, 206], [34876, 206], [35176, 205], [35475, 206], [35775, 205], [36075, 205], [36374, 206], [36674, 205], [36974, 205], [37273, 205], [37573, 205], [37873, 204], [38172, 205], [38472, 201], [38772, 201], [39071, 48], [39239, 34], [39371, 48], [39539, 34], [39671, 48], [39840, 33], [39970, 48], [40140, 32], [40270, 48], [40440, 32], [40570, 48], [40741, 30], [40869, 49], [41169, 49], [41469, 49], [41768, 50], [42068, 49], [42368, 49], [42667, 50], [42967, 50], [43267, 50], [43566, 51], [43866, 51], [44166, 51], [44465, 51], [44765, 51], [45065, 51], [45365, 51], [45664, 52], [45964, 52], [46264, 52], [46563, 53], [46863, 52], [47163, 52], [47462, 53], [47762, 53], [48062, 53], [48361, 54], [48661, 54], [48961, 53], [49260, 54], [49560, 54], [49860, 54], [50159, 55], [50460, 54], [50760, 54], [51060, 54], [51361, 52], [51661, 52], [51961, 52], [52262, 51], [52563, 50], [52864, 49], [53166, 47], [53468, 45], [53769, 43], [54071, 41], [54372, 40], [54674, 38], [54974, 38], [55275, 37], [55575, 37], [55875, 36], [56176, 35], [56476, 35], [56776, 35], [57077, 34], [57377, 34], [57677, 34], [57978, 33], [58278, 32], [58578, 32], [58879, 31], [59179, 31], [59479, 31], [59780, 30], [60080, 30], [60380, 30], [60681, 28], [60981, 28], [61281, 28], [61582, 27], [61882, 27], [62182, 27], [62483, 26], [62783, 26], [63083, 25], [63384, 24], [63684, 24], [63984, 24], [64285, 23], [64585, 23], [64885, 23], [65186, 21], [65486, 21], [65786, 21], [66087, 20], [66387, 20], [66687, 20], [66988, 19], [67288, 19], [67587, 19], [67887, 19], [68187, 19], [68487, 19], [68787, 19], [69086, 20], [69386, 20], [69686, 20], [69986, 22], [70286, 25], [70586, 26], [70885, 20], [70908, 4], [71185, 20], [71208, 5], [71486, 19], [71509, 3], [71786, 19], [72087, 17], [72387, 17]], "point": [179, 129]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-0.576, -0.576, -3.632, -3.632, 3.615618228, 3.615618228]], "forceVisible": true, "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-0.576, -0.576, -3.632, -3.632, 3.615618228, 3.615618228]], "forceVisible": true, "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.64|+00.83|-03.62"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [157, 90, 171, 110], "mask": [[26863, 4], [27161, 7], [27460, 9], [27760, 10], [28059, 12], [28359, 12], [28658, 13], [28958, 14], [29258, 14], [29557, 15], [29857, 15], [30157, 15], [30457, 15], [30757, 15], [31058, 14], [31358, 13], [31658, 13], [31959, 11], [32259, 11], [32560, 9], [32862, 5]], "point": [164, 99]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [59, 46, 299, 242], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17795, 205], [18094, 206], [18394, 206], [18694, 206], [18993, 207], [19293, 207], [19593, 207], [19892, 208], [20192, 208], [20492, 208], [20791, 209], [21091, 209], [21391, 209], [21690, 210], [21990, 210], [22290, 210], [22589, 211], [22889, 211], [23189, 210], [23488, 211], [23788, 210], [24088, 210], [24387, 211], [24687, 210], [24987, 210], [25286, 210], [25586, 210], [25886, 209], [26185, 210], [26485, 209], [26785, 209], [27084, 209], [27384, 209], [27684, 209], [27983, 209], [28283, 209], [28583, 208], [28883, 208], [29182, 208], [29482, 208], [29782, 207], [30081, 208], [30381, 208], [30681, 207], [30980, 208], [31280, 207], [31580, 207], [31879, 207], [32179, 207], [32479, 206], [32778, 207], [33078, 206], [33378, 206], [33677, 207], [33977, 206], [34277, 206], [34576, 206], [34876, 206], [35176, 205], [35475, 206], [35775, 205], [36075, 205], [36374, 206], [36674, 205], [36974, 205], [37273, 205], [37573, 205], [37873, 204], [38172, 205], [38472, 201], [38772, 201], [39071, 48], [39239, 34], [39371, 48], [39539, 34], [39671, 48], [39840, 33], [39970, 48], [40140, 32], [40270, 48], [40440, 32], [40570, 48], [40741, 30], [40869, 49], [41169, 49], [41469, 49], [41768, 50], [42068, 49], [42368, 49], [42667, 50], [42967, 50], [43267, 50], [43566, 51], [43866, 51], [44166, 51], [44465, 51], [44765, 51], [45065, 51], [45365, 51], [45664, 52], [45964, 52], [46264, 52], [46563, 53], [46863, 52], [47163, 52], [47462, 53], [47762, 53], [48062, 53], [48361, 54], [48661, 54], [48961, 53], [49260, 54], [49560, 54], [49860, 54], [50159, 55], [50460, 54], [50760, 54], [51060, 54], [51361, 52], [51661, 52], [51961, 52], [52262, 51], [52563, 50], [52864, 49], [53166, 47], [53468, 45], [53769, 43], [54071, 41], [54372, 40], [54674, 38], [54974, 38], [55275, 37], [55575, 37], [55875, 36], [56176, 35], [56476, 35], [56776, 35], [57077, 34], [57377, 34], [57677, 34], [57978, 33], [58278, 32], [58578, 32], [58879, 31], [59179, 31], [59479, 31], [59780, 30], [60080, 30], [60380, 30], [60681, 28], [60981, 28], [61281, 28], [61582, 27], [61882, 27], [62182, 27], [62483, 26], [62783, 26], [63083, 25], [63384, 24], [63684, 24], [63984, 24], [64285, 23], [64585, 23], [64885, 23], [65186, 21], [65486, 21], [65786, 21], [66087, 20], [66387, 20], [66687, 20], [66988, 19], [67288, 19], [67587, 19], [67887, 19], [68187, 19], [68487, 19], [68787, 19], [69086, 20], [69386, 20], [69686, 20], [69986, 22], [70286, 25], [70586, 26], [70885, 20], [70908, 4], [71185, 20], [71208, 5], [71486, 19], [71509, 3], [71786, 19], [72087, 17], [72387, 17]], "point": [179, 129]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-03.19|+00.00|-02.19"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 236], "mask": [[0, 48300], [48301, 299], [48602, 298], [48903, 297], [49204, 296], [49505, 295], [49806, 294], [50107, 293], [50409, 291], [50710, 290], [51011, 289], [51312, 288], [51613, 287], [51914, 286], [52215, 285], [52516, 284], [52817, 283], [53118, 282], [53419, 281], [53720, 280], [54021, 279], [54323, 277], [54624, 276], [54925, 275], [55226, 274], [55527, 273], [55828, 272], [56129, 271], [56430, 270], [56731, 269], [57032, 268], [57333, 267], [57634, 266], [57935, 265], [58237, 263], [58538, 262], [58839, 261], [59140, 258], [59441, 256], [59742, 253], [60043, 251], [60344, 248], [60645, 246], [60946, 244], [61247, 241], [61548, 239], [61849, 236], [62151, 233], [62452, 230], [62753, 228], [63054, 225], [63355, 223], [63656, 221], [63957, 218], [64258, 216], [64559, 213], [64860, 211], [65161, 208], [65462, 206], [65763, 203], [66064, 201], [66366, 198], [66667, 195], [66968, 193], [67269, 190], [67570, 188], [67871, 185], [68172, 183], [68473, 180], [68774, 178], [69075, 176], [69376, 173], [69677, 171], [69978, 168], [70280, 165], [70581, 162]], "point": [149, 117]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.64|+00.83|-03.62", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-03.19|+00.00|-02.19"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 38624], [38653, 271], [38953, 271], [39253, 271], [39552, 273], [39852, 273], [40152, 274], [40451, 275], [40751, 276], [41050, 278], [41348, 281], [41647, 284], [41945, 288], [42243, 24596], [66840, 299], [67140, 299], [67440, 299], [67740, 300], [68041, 299], [68341, 299], [68641, 299], [68941, 299], [69241, 300], [69542, 299], [69842, 296], [70142, 295], [70442, 295], [70742, 143], [71042, 143], [71343, 142], [71643, 142], [71943, 142], [72243, 142], [72543, 141], [72843, 141], [73144, 140], [73444, 140], [73744, 140], [74044, 140], [74344, 140], [74644, 140], [74945, 138], [75245, 138], [75545, 138], [75845, 138], [76145, 138], [76445, 138], [76746, 137], [77046, 137], [77346, 136], [77646, 136], [77946, 136], [78247, 135], [78547, 135], [78847, 135], [79147, 135], [79447, 135], [79747, 135], [80048, 133], [80348, 133], [80648, 133], [80948, 133], [81248, 133], [81548, 133], [81849, 132], [82149, 132], [82449, 131], [82749, 131], [83049, 131], [83349, 131], [83650, 130], [83950, 130], [84250, 130], [84550, 130], [84850, 129], [85150, 129], [85451, 128], [85751, 126], [86051, 124], [86351, 124], [86651, 124], [86951, 124], [87252, 122], [87552, 48], [87607, 67], [87852, 48], [87926, 48], [88152, 48], [88245, 28], [88452, 48], [88753, 47], [89053, 47], [89353, 47], [89653, 47], [89953, 47]], "point": [149, 149]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-03.19|+00.00|-02.19"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 24472], [24476, 294], [24778, 291], [25080, 288], [25380, 287], [25681, 285], [25982, 284], [26282, 283], [26582, 283], [26883, 281], [27183, 281], [27483, 281], [27783, 281], [28083, 281], [28383, 281], [28683, 281], [28983, 281], [29283, 281], [29583, 281], [29882, 282], [30182, 283], [30481, 284], [30781, 285], [31080, 287], [31379, 289], [31678, 293], [31975, 6649], [38653, 271], [38953, 271], [39253, 271], [39552, 273], [39852, 273], [40152, 274], [40451, 275], [40751, 276], [41050, 278], [41348, 281], [41647, 284], [41945, 288], [42243, 24596], [66840, 299], [67140, 299], [67440, 299], [67740, 300], [68041, 299], [68341, 299], [68641, 299], [68941, 299], [69241, 300], [69542, 299], [69842, 296], [70142, 295], [70442, 295], [70742, 143], [71042, 143], [71343, 142], [71643, 142], [71943, 142], [72243, 142], [72543, 141], [72843, 141], [73144, 140], [73444, 140], [73744, 140], [74044, 140], [74344, 140], [74644, 140], [74945, 138], [75245, 138], [75545, 138], [75845, 138], [76145, 138], [76445, 138], [76746, 137], [77046, 137], [77346, 136], [77646, 136], [77946, 136], [78247, 135], [78547, 135], [78847, 135], [79147, 135], [79447, 135], [79747, 135], [80048, 133], [80348, 133], [80648, 133], [80948, 133], [81248, 133], [81548, 133], [81849, 132], [82149, 132], [82449, 131], [82749, 131], [83049, 131], [83349, 131], [83650, 130], [83950, 130], [84250, 130], [84550, 130], [84850, 129], [85150, 129], [85451, 128], [85751, 126], [86051, 124], [86351, 124], [86651, 124], [86951, 124], [87252, 122], [87552, 48], [87607, 67], [87852, 48], [87926, 48], [88152, 48], [88245, 28], [88452, 48], [88753, 47], [89053, 47], [89353, 47], [89653, 47], [89953, 47]], "point": [149, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan19", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -1.75, "y": 0.9016907, "z": -0.5}, "object_poses": [{"objectName": "Potato_2b74ee3a", "position": {"x": -0.176295966, "y": 0.9597968, "z": -1.55500889}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Potato_2b74ee3a", "position": {"x": -0.257330954, "y": 0.9597968, "z": -1.3709687}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_78f219bc", "position": {"x": -3.15527177, "y": 0.713334441, "z": -0.6086949}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "DishSponge_78f219bc", "position": {"x": -0.450453877, "y": 0.08052623, "z": -2.96746945}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_a7531534", "position": {"x": -3.16700172, "y": 0.913742065, "z": -3.87812114}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_a7531534", "position": {"x": -0.257330954, "y": 0.9160421, "z": -1.49366212}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_94d2125c", "position": {"x": -1.63819706, "y": 0.7916855, "z": -3.704358}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_94d2125c", "position": {"x": -3.29236221, "y": 0.7148996, "z": -0.558499932}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Fork_1cd5bb8d", "position": {"x": -1.91450834, "y": 0.7911047, "z": -3.790042}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Cup_410beacd", "position": {"x": -2.66072655, "y": 1.29573941, "z": -3.966442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_46bffc5f", "position": {"x": -3.082638, "y": 0.7790452, "z": -2.91282749}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_46bffc5f", "position": {"x": -0.530175269, "y": 0.7793249, "z": -0.7613697}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_2ce80e75", "position": {"x": -0.936858, "y": 1.28921223, "z": -4.04470634}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_2ce80e75", "position": {"x": -1.01682663, "y": 0.07642335, "z": -3.6739974}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_5fac94a5", "position": {"x": -3.47741938, "y": 0.910006046, "z": -3.12682819}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_5fac94a5", "position": {"x": -0.471112043, "y": 0.7431938, "z": -0.912980556}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_908150ce", "position": {"x": -2.65256357, "y": 0.8015844, "z": -0.8860142}, "rotation": {"x": 0.0, "y": 135.000092, "z": 0.0}}, {"objectName": "Apple_1634f71e", "position": {"x": -0.223363534, "y": 0.122436538, "z": -0.115229815}, "rotation": {"x": 0.0, "y": 269.999969, "z": 0.0}}, {"objectName": "Apple_1634f71e", "position": {"x": -3.23445058, "y": 0.683879733, "z": -2.00261641}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pot_fd5c5c80", "position": {"x": -0.1653, "y": 0.9759, "z": -2.0309}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Egg_b0ce4484", "position": {"x": -1.6381973, "y": 0.827970147, "z": -3.61867452}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_410beacd", "position": {"x": -0.335649729, "y": 0.08081764, "z": -0.760731459}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_77e17ce3", "position": {"x": -0.327003837, "y": 0.948122, "z": -3.681027}, "rotation": {"x": 0.003907472, "y": 298.22287, "z": 0.0431618057}}, {"objectName": "Fork_1cd5bb8d", "position": {"x": -3.39329171, "y": 0.914248466, "z": -3.428062}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_4966d0cc", "position": {"x": -3.39329171, "y": 0.91207397, "z": -2.92600584}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Potato_2b74ee3a", "position": {"x": -2.7408, "y": 0.757567, "z": -0.0694375038}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_908150ce", "position": {"x": -2.9283, "y": 0.8015844, "z": -0.780375}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_f34461c5", "position": {"x": -3.1309557, "y": 0.690823436, "z": -1.87947655}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_1634f71e", "position": {"x": -0.1930272, "y": 0.122436538, "z": 0.0552299134}, "rotation": {"x": 0.0, "y": 269.999969, "z": 0.0}}, {"objectName": "SoapBottle_dd4bbd23", "position": {"x": -0.102595106, "y": 1.86775422, "z": -2.58695078}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_fca8a224", "position": {"x": -2.27053785, "y": 0.928699851, "z": -3.87812114}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_2ce80e75", "position": {"x": -0.8940726, "y": 0.0783962, "z": -3.67508984}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_46bffc5f", "position": {"x": -2.66471648, "y": 0.7462074, "z": -0.29491657}, "rotation": {"x": 0.0, "y": 315.000031, "z": 0.0}}, {"objectName": "PepperShaker_5fac94a5", "position": {"x": -1.5692029, "y": 0.0792369246, "z": -3.555806}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_a7531534", "position": {"x": -1.26282239, "y": 0.913742, "z": -3.804}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_bdd88192", "position": {"x": -2.80841613, "y": 0.9118502, "z": -3.95224237}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_78f219bc", "position": {"x": -0.334789127, "y": 0.08052623, "z": -2.91026783}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_94d2125c", "position": {"x": -3.5615468, "y": 0.9148293, "z": -3.22723937}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_f4b8ca2f", "position": {"x": -2.93075848, "y": 0.7624276, "z": -0.358240336}, "rotation": {"x": -0.00207028, "y": 134.999619, "z": -0.00125878968}}, {"objectName": "Bowl_697b561f", "position": {"x": -2.76525831, "y": 0.714565456, "z": -0.596542}, "rotation": {"x": 0.0, "y": 135.000092, "z": 0.0}}], "object_toggles": [], "random_seed": 1375540740, "scene_num": 19}, "task_id": "trial_T20190908_130410_448245", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2KAGFQU28JY43_388U7OUMFASL4MY9EQEJ49IEHI9R00", "high_descs": ["Turn to your left and walk across the room to the kitchen sink.", "Remove the egg from the sink that is in front of the spoon. ", "Turn around and walk to the microwave, on your right, at the end of the room.", "Place the egg inside the microwave, heat it up, and remove the egg from the microwave. ", "Turn around and walk across the kitchen to the refrigerator. ", "Place the egg on the left side of the shelf above the apple and tomato, in the refrigerator. "], "task_desc": "Place a warm egg in the refrigerator. ", "votes": [1, 1, 1]}, {"assignment_id": "A1NWXL4QO30M8U_3A0EX8ZRNBFVLAD5Q132WM7UB8ZYB7", "high_descs": ["Turn to the left and walk across the room.", "Pick up the egg in the sink.", "Turn around and walk across the room then turn right and walk to the counter.", "Put the egg in the microwave then turn the microwave on for three seconds, then take the egg out of the microwave.", "Turn to the right and take two steps, then turn to the right and walk to the fridge.", "Put the egg inside of the fridge."], "task_desc": "<PERSON> the brown egg.", "votes": [1, 1, 1]}, {"assignment_id": "ARB8SJAWXUWTD_333U7HK6IC6Y5IGQIXT5HPG61PLDJI", "high_descs": ["Go to the sink", "Pick up an egg from the sink", "Go to the microwave", "Heat the egg", "Go to the fridge", "Put the egg in the fridge"], "task_desc": "Put a heated egg in the fridge", "votes": [1, 0, 1]}]}}