{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000082.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000083.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000084.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000085.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000086.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000087.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000088.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000089.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000090.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000093.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000094.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000207.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000208.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000209.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000210.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000211.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000212.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 36}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-2|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-0.705183864, -0.705183864, -2.099292992, -2.099292992, 3.8076032, 3.8076032]], "coordinateReceptacleObjectId": ["CounterTop", [-1.076, -1.076, -4.3592, -4.3592, 3.806, 3.806]], "forceVisible": true, "objectId": "Egg|-00.18|+00.95|-00.52"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-3|1|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-0.576, -0.576, -3.632, -3.632, 3.615618228, 3.615618228]], "forceVisible": true, "objectId": "Microwave|-00.14|+00.90|-00.91"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-10|-9|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-0.705183864, -0.705183864, -2.099292992, -2.099292992, 3.8076032, 3.8076032]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-12.75556944, -12.75556944, -8.74, -8.74, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|-00.18|+00.95|-00.52", "receptacleObjectId": "<PERSON><PERSON>|-03.19|+00.00|-02.19"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.18|+00.95|-00.52"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [150, 108, 162, 125], "mask": [[32254, 5], [32552, 8], [32852, 9], [33151, 11], [33451, 11], [33750, 12], [34050, 13], [34350, 13], [34650, 13], [34950, 13], [35250, 13], [35550, 13], [35850, 12], [36150, 12], [36451, 10], [36752, 9], [37053, 7], [37354, 4]], "point": [156, 115]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.18|+00.95|-00.52", "placeStationary": true, "receptacleObjectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [59, 46, 299, 242], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17795, 205], [18094, 206], [18394, 206], [18694, 206], [18993, 207], [19293, 207], [19593, 101], [19707, 93], [19892, 101], [20007, 93], [20192, 101], [20308, 92], [20492, 101], [20609, 91], [20791, 101], [20909, 91], [21091, 101], [21210, 90], [21391, 101], [21511, 89], [21690, 102], [21811, 89], [21990, 101], [22112, 88], [22290, 101], [22412, 88], [22589, 102], [22712, 88], [22889, 102], [23013, 87], [23189, 102], [23313, 86], [23488, 103], [23613, 86], [23788, 103], [23914, 84], [24088, 102], [24214, 84], [24387, 104], [24514, 84], [24687, 104], [24814, 83], [24987, 104], [25114, 83], [25286, 105], [25414, 82], [25586, 105], [25714, 82], [25886, 106], [26014, 81], [26185, 107], [26314, 81], [26485, 108], [26614, 80], [26785, 109], [26914, 80], [27084, 111], [27213, 80], [27384, 113], [27513, 80], [27684, 114], [27811, 82], [27983, 116], [28110, 82], [28283, 209], [28583, 208], [28883, 208], [29182, 208], [29482, 208], [29782, 207], [30081, 208], [30381, 208], [30681, 207], [30980, 208], [31280, 207], [31580, 207], [31879, 207], [32179, 207], [32479, 206], [32778, 207], [33078, 206], [33378, 206], [33677, 207], [33977, 206], [34277, 206], [34576, 206], [34876, 206], [35176, 205], [35475, 206], [35775, 205], [36075, 205], [36374, 206], [36674, 205], [36974, 205], [37273, 205], [37573, 205], [37873, 204], [38172, 205], [38472, 201], [38772, 201], [39071, 48], [39239, 34], [39371, 48], [39539, 34], [39671, 48], [39840, 33], [39970, 48], [40140, 32], [40270, 48], [40440, 32], [40570, 48], [40741, 30], [40869, 49], [41169, 49], [41469, 49], [41768, 50], [42068, 49], [42368, 49], [42667, 50], [42967, 50], [43267, 50], [43566, 51], [43866, 51], [44166, 51], [44465, 51], [44765, 51], [45065, 51], [45365, 51], [45664, 52], [45964, 52], [46264, 52], [46563, 53], [46863, 52], [47163, 52], [47462, 53], [47762, 53], [48062, 53], [48361, 54], [48661, 54], [48961, 53], [49260, 54], [49560, 54], [49860, 54], [50159, 55], [50460, 54], [50760, 54], [51060, 54], [51361, 52], [51661, 52], [51961, 52], [52262, 51], [52563, 50], [52864, 49], [53166, 47], [53468, 45], [53769, 43], [54071, 41], [54372, 40], [54674, 38], [54974, 38], [55275, 37], [55575, 37], [55875, 36], [56176, 35], [56476, 35], [56776, 35], [57077, 34], [57377, 34], [57677, 34], [57978, 33], [58278, 32], [58578, 32], [58879, 31], [59179, 31], [59479, 31], [59780, 30], [60080, 30], [60380, 30], [60681, 28], [60981, 28], [61281, 28], [61582, 27], [61882, 27], [62182, 27], [62483, 26], [62783, 26], [63083, 25], [63384, 24], [63684, 24], [63984, 24], [64285, 23], [64585, 23], [64885, 23], [65186, 21], [65486, 21], [65786, 21], [66087, 20], [66387, 20], [66687, 20], [66988, 19], [67288, 19], [67587, 19], [67887, 19], [68187, 19], [68487, 19], [68787, 19], [69086, 20], [69386, 20], [69686, 20], [69986, 22], [70286, 25], [70586, 26], [70885, 20], [70908, 4], [71185, 20], [71208, 5], [71486, 19], [71509, 3], [71786, 19], [72087, 17], [72387, 17]], "point": [179, 129]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [59, 46, 299, 242], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17795, 205], [18094, 206], [18394, 206], [18694, 206], [18993, 207], [19293, 207], [19593, 101], [19707, 93], [19892, 101], [20007, 93], [20192, 101], [20308, 92], [20492, 101], [20609, 91], [20791, 101], [20909, 91], [21091, 101], [21210, 90], [21391, 101], [21511, 89], [21690, 102], [21811, 89], [21990, 101], [22112, 88], [22290, 101], [22412, 88], [22589, 102], [22712, 88], [22889, 102], [23013, 87], [23189, 102], [23313, 86], [23488, 103], [23613, 86], [23788, 103], [23914, 84], [24088, 102], [24214, 84], [24387, 104], [24514, 84], [24687, 104], [24814, 83], [24987, 104], [25114, 83], [25286, 105], [25414, 82], [25586, 105], [25714, 82], [25886, 106], [26014, 81], [26185, 107], [26314, 81], [26485, 108], [26614, 80], [26785, 78], [26867, 27], [26914, 80], [27084, 77], [27168, 27], [27213, 80], [27384, 76], [27469, 28], [27513, 80], [27684, 76], [27770, 28], [27811, 82], [27983, 76], [28071, 28], [28110, 82], [28283, 76], [28371, 121], [28583, 75], [28671, 120], [28883, 75], [28972, 119], [29182, 76], [29272, 118], [29482, 75], [29572, 118], [29782, 75], [29872, 117], [30081, 76], [30172, 117], [30381, 76], [30472, 117], [30681, 76], [30772, 116], [30980, 78], [31072, 116], [31280, 78], [31371, 116], [31580, 78], [31671, 116], [31879, 80], [31970, 116], [32179, 80], [32270, 116], [32479, 81], [32569, 116], [32778, 84], [32867, 118], [33078, 206], [33378, 206], [33677, 207], [33977, 206], [34277, 206], [34576, 206], [34876, 206], [35176, 205], [35475, 206], [35775, 205], [36075, 205], [36374, 206], [36674, 205], [36974, 205], [37273, 205], [37573, 205], [37873, 204], [38172, 205], [38472, 201], [38772, 201], [39071, 48], [39239, 34], [39371, 48], [39539, 34], [39671, 48], [39840, 33], [39970, 48], [40140, 32], [40270, 48], [40440, 32], [40570, 48], [40741, 30], [40869, 49], [41169, 49], [41469, 49], [41768, 50], [42068, 49], [42368, 49], [42667, 50], [42967, 50], [43267, 50], [43566, 51], [43866, 51], [44166, 51], [44465, 51], [44765, 51], [45065, 51], [45365, 51], [45664, 52], [45964, 52], [46264, 52], [46563, 53], [46863, 52], [47163, 52], [47462, 53], [47762, 53], [48062, 53], [48361, 54], [48661, 54], [48961, 53], [49260, 54], [49560, 54], [49860, 54], [50159, 55], [50460, 54], [50760, 54], [51060, 54], [51361, 52], [51661, 52], [51961, 52], [52262, 51], [52563, 50], [52864, 49], [53166, 47], [53468, 45], [53769, 43], [54071, 41], [54372, 40], [54674, 38], [54974, 38], [55275, 37], [55575, 37], [55875, 36], [56176, 35], [56476, 35], [56776, 35], [57077, 34], [57377, 34], [57677, 34], [57978, 33], [58278, 32], [58578, 32], [58879, 31], [59179, 31], [59479, 31], [59780, 30], [60080, 30], [60380, 30], [60681, 28], [60981, 28], [61281, 28], [61582, 27], [61882, 27], [62182, 27], [62483, 26], [62783, 26], [63083, 25], [63384, 24], [63684, 24], [63984, 24], [64285, 23], [64585, 23], [64885, 23], [65186, 21], [65486, 21], [65786, 21], [66087, 20], [66387, 20], [66687, 20], [66988, 19], [67288, 19], [67587, 19], [67887, 19], [68187, 19], [68487, 19], [68787, 19], [69086, 20], [69386, 20], [69686, 20], [69986, 22], [70286, 25], [70586, 26], [70885, 20], [70908, 4], [71185, 20], [71208, 5], [71486, 19], [71509, 3], [71786, 19], [72087, 17], [72387, 17]], "point": [179, 129]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-0.576, -0.576, -3.632, -3.632, 3.615618228, 3.615618228]], "forceVisible": true, "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-0.576, -0.576, -3.632, -3.632, 3.615618228, 3.615618228]], "forceVisible": true, "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.18|+00.95|-00.52"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [157, 90, 171, 110], "mask": [[26863, 4], [27161, 7], [27460, 9], [27760, 10], [28059, 12], [28359, 12], [28658, 13], [28958, 14], [29258, 14], [29557, 15], [29857, 15], [30157, 15], [30457, 15], [30757, 15], [31058, 14], [31358, 13], [31658, 13], [31959, 11], [32259, 11], [32560, 9], [32862, 5]], "point": [164, 99]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [59, 46, 299, 242], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17795, 205], [18094, 206], [18394, 206], [18694, 206], [18993, 207], [19293, 207], [19593, 101], [19707, 93], [19892, 101], [20007, 93], [20192, 101], [20308, 92], [20492, 101], [20609, 91], [20791, 101], [20909, 91], [21091, 101], [21210, 90], [21391, 101], [21511, 89], [21690, 102], [21811, 89], [21990, 101], [22112, 88], [22290, 101], [22412, 88], [22589, 102], [22712, 88], [22889, 102], [23013, 87], [23189, 102], [23313, 86], [23488, 103], [23613, 86], [23788, 103], [23914, 84], [24088, 102], [24214, 84], [24387, 104], [24514, 84], [24687, 104], [24814, 83], [24987, 104], [25114, 83], [25286, 105], [25414, 82], [25586, 105], [25714, 82], [25886, 106], [26014, 81], [26185, 107], [26314, 81], [26485, 108], [26614, 80], [26785, 109], [26914, 80], [27084, 111], [27213, 80], [27384, 113], [27513, 80], [27684, 114], [27811, 82], [27983, 116], [28110, 82], [28283, 209], [28583, 208], [28883, 208], [29182, 208], [29482, 208], [29782, 207], [30081, 208], [30381, 208], [30681, 207], [30980, 208], [31280, 207], [31580, 207], [31879, 207], [32179, 207], [32479, 206], [32778, 207], [33078, 206], [33378, 206], [33677, 207], [33977, 206], [34277, 206], [34576, 206], [34876, 206], [35176, 205], [35475, 206], [35775, 205], [36075, 205], [36374, 206], [36674, 205], [36974, 205], [37273, 205], [37573, 205], [37873, 204], [38172, 205], [38472, 201], [38772, 201], [39071, 48], [39239, 34], [39371, 48], [39539, 34], [39671, 48], [39840, 33], [39970, 48], [40140, 32], [40270, 48], [40440, 32], [40570, 48], [40741, 30], [40869, 49], [41169, 49], [41469, 49], [41768, 50], [42068, 49], [42368, 49], [42667, 50], [42967, 50], [43267, 50], [43566, 51], [43866, 51], [44166, 51], [44465, 51], [44765, 51], [45065, 51], [45365, 51], [45664, 52], [45964, 52], [46264, 52], [46563, 53], [46863, 52], [47163, 52], [47462, 53], [47762, 53], [48062, 53], [48361, 54], [48661, 54], [48961, 53], [49260, 54], [49560, 54], [49860, 54], [50159, 55], [50460, 54], [50760, 54], [51060, 54], [51361, 52], [51661, 52], [51961, 52], [52262, 51], [52563, 50], [52864, 49], [53166, 47], [53468, 45], [53769, 43], [54071, 41], [54372, 40], [54674, 38], [54974, 38], [55275, 37], [55575, 37], [55875, 36], [56176, 35], [56476, 35], [56776, 35], [57077, 34], [57377, 34], [57677, 34], [57978, 33], [58278, 32], [58578, 32], [58879, 31], [59179, 31], [59479, 31], [59780, 30], [60080, 30], [60380, 30], [60681, 28], [60981, 28], [61281, 28], [61582, 27], [61882, 27], [62182, 27], [62483, 26], [62783, 26], [63083, 25], [63384, 24], [63684, 24], [63984, 24], [64285, 23], [64585, 23], [64885, 23], [65186, 21], [65486, 21], [65786, 21], [66087, 20], [66387, 20], [66687, 20], [66988, 19], [67288, 19], [67587, 19], [67887, 19], [68187, 19], [68487, 19], [68787, 19], [69086, 20], [69386, 20], [69686, 20], [69986, 22], [70286, 25], [70586, 26], [70885, 20], [70908, 4], [71185, 20], [71208, 5], [71486, 19], [71509, 3], [71786, 19], [72087, 17], [72387, 17]], "point": [179, 129]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-03.19|+00.00|-02.19"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 236], "mask": [[0, 48300], [48301, 299], [48602, 298], [48903, 297], [49204, 296], [49505, 295], [49806, 294], [50107, 293], [50409, 291], [50710, 290], [51011, 289], [51312, 288], [51613, 287], [51914, 286], [52215, 285], [52516, 284], [52817, 283], [53118, 282], [53419, 281], [53720, 280], [54021, 279], [54323, 277], [54624, 276], [54925, 275], [55226, 274], [55527, 273], [55828, 272], [56129, 271], [56430, 270], [56731, 269], [57032, 268], [57333, 267], [57634, 266], [57935, 265], [58237, 263], [58538, 262], [58839, 261], [59140, 258], [59441, 256], [59742, 253], [60043, 251], [60344, 248], [60645, 246], [60946, 244], [61247, 241], [61548, 239], [61849, 236], [62151, 233], [62452, 230], [62753, 228], [63054, 225], [63355, 223], [63656, 221], [63957, 218], [64258, 216], [64559, 213], [64860, 211], [65161, 208], [65462, 206], [65763, 203], [66064, 201], [66366, 198], [66667, 195], [66968, 193], [67269, 190], [67570, 188], [67871, 185], [68172, 183], [68473, 180], [68774, 178], [69075, 176], [69376, 173], [69677, 171], [69978, 168], [70280, 165], [70581, 162]], "point": [149, 117]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.18|+00.95|-00.52", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-03.19|+00.00|-02.19"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 3826], [3829, 295], [4131, 291], [4432, 290], [4732, 289], [5033, 287], [5333, 286], [5633, 286], [5933, 285], [6233, 285], [6534, 283], [6834, 283], [7133, 284], [7433, 284], [7733, 283], [8033, 283], [8333, 283], [8632, 284], [8932, 284], [9231, 286], [9531, 286], [9830, 288], [10129, 289], [10428, 291], [10727, 293], [11025, 2963], [14000, 286], [14303, 281], [14606, 277], [14908, 273], [15209, 271], [15509, 270], [15810, 268], [16111, 267], [16411, 266], [16712, 265], [17012, 264], [17313, 263], [17613, 263], [17913, 262], [18213, 262], [18514, 261], [18814, 260], [19114, 260], [19414, 260], [19714, 260], [20014, 260], [20314, 260], [20614, 260], [20914, 260], [21214, 260], [21514, 260], [21814, 260], [22113, 261], [22413, 262], [22712, 263], [23012, 263], [23312, 264], [23611, 265], [23910, 267], [24209, 269], [24508, 270], [24807, 272], [25106, 274], [25405, 277], [25704, 280], [26002, 285], [26299, 11068], [37371, 297], [37682, 287], [37989, 280], [38288, 283], [38587, 285], [38885, 290], [39183, 27656], [66840, 299], [67140, 299], [67440, 299], [67740, 300], [68041, 299], [68341, 299], [68641, 299], [68941, 299], [69241, 300], [69542, 299], [69842, 296], [70142, 295], [70442, 295], [70742, 143], [71042, 143], [71343, 142], [71643, 142], [71943, 142], [72243, 142], [72543, 141], [72843, 141], [73144, 140], [73444, 140], [73744, 140], [74044, 140], [74344, 140], [74644, 140], [74945, 138], [75245, 138], [75545, 138], [75845, 138], [76145, 138], [76445, 138], [76746, 137], [77046, 137], [77346, 136], [77646, 136], [77946, 136], [78247, 135], [78547, 135], [78847, 135], [79147, 135], [79447, 135], [79747, 135], [80048, 133], [80348, 133], [80648, 133], [80948, 133], [81248, 133], [81548, 133], [81849, 132], [82149, 132], [82449, 131], [82749, 131], [83049, 131], [83349, 131], [83650, 130], [83950, 130], [84250, 130], [84550, 130], [84850, 129], [85150, 129], [85451, 128], [85751, 126], [86051, 124], [86351, 124], [86651, 124], [86951, 124], [87252, 122], [87552, 48], [87607, 67], [87852, 48], [87926, 48], [88152, 48], [88245, 28], [88452, 48], [88753, 47], [89053, 47], [89353, 47], [89653, 47], [89953, 47]], "point": [149, 149]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-03.19|+00.00|-02.19"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 3826], [3829, 295], [4131, 291], [4432, 290], [4732, 289], [5033, 287], [5333, 286], [5633, 286], [5933, 285], [6233, 285], [6534, 283], [6834, 283], [7133, 284], [7433, 284], [7733, 283], [8033, 283], [8333, 283], [8632, 284], [8932, 284], [9231, 286], [9531, 286], [9830, 288], [10129, 289], [10428, 291], [10727, 293], [11025, 2963], [14000, 286], [14303, 281], [14606, 277], [14908, 273], [15209, 271], [15509, 270], [15810, 268], [16111, 267], [16411, 266], [16712, 265], [17012, 264], [17313, 263], [17613, 263], [17913, 262], [18213, 262], [18514, 261], [18814, 260], [19114, 260], [19414, 260], [19714, 260], [20014, 260], [20314, 260], [20614, 260], [20914, 260], [21214, 260], [21514, 260], [21814, 260], [22113, 261], [22413, 262], [22712, 263], [23012, 263], [23312, 264], [23611, 265], [23910, 267], [24209, 263], [24476, 2], [24508, 262], [24807, 262], [25106, 262], [25405, 262], [25681, 1], [25704, 262], [25982, 2], [26002, 264], [26282, 5], [26299, 266], [26582, 283], [26883, 281], [27183, 281], [27483, 281], [27783, 281], [28083, 281], [28383, 281], [28683, 281], [28983, 281], [29283, 281], [29583, 281], [29882, 282], [30182, 283], [30481, 284], [30781, 285], [31080, 287], [31379, 289], [31678, 293], [31975, 5392], [37371, 297], [37682, 287], [37989, 280], [38288, 283], [38587, 285], [38885, 290], [39183, 27656], [66840, 299], [67140, 299], [67440, 299], [67740, 300], [68041, 299], [68341, 299], [68641, 299], [68941, 299], [69241, 300], [69542, 299], [69842, 296], [70142, 295], [70442, 295], [70742, 143], [71042, 143], [71343, 142], [71643, 142], [71943, 142], [72243, 142], [72543, 141], [72843, 141], [73144, 140], [73444, 140], [73744, 140], [74044, 140], [74344, 140], [74644, 140], [74945, 138], [75245, 138], [75545, 138], [75845, 138], [76145, 138], [76445, 138], [76746, 137], [77046, 137], [77346, 136], [77646, 136], [77946, 136], [78247, 135], [78547, 135], [78847, 135], [79147, 135], [79447, 135], [79747, 135], [80048, 133], [80348, 133], [80648, 133], [80948, 133], [81248, 133], [81548, 133], [81849, 132], [82149, 132], [82449, 131], [82749, 131], [83049, 131], [83349, 131], [83650, 130], [83950, 130], [84250, 130], [84550, 130], [84850, 129], [85150, 129], [85451, 128], [85751, 126], [86051, 124], [86351, 124], [86651, 124], [86951, 124], [87252, 122], [87552, 48], [87607, 67], [87852, 48], [87926, 48], [88152, 48], [88245, 28], [88452, 48], [88753, 47], [89053, 47], [89353, 47], [89653, 47], [89953, 47]], "point": [149, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan19", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -1.0, "y": 0.9016907, "z": -1.5}, "object_poses": [{"objectName": "Potato_2b74ee3a", "position": {"x": -1.84543049, "y": 0.8343529, "z": -3.83288383}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_78f219bc", "position": {"x": -0.3343274, "y": 0.08166444, "z": -3.348648}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_fca8a224", "position": {"x": -3.11174822, "y": 0.7615969, "z": -3.07884526}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_fca8a224", "position": {"x": -3.12323117, "y": 0.7319673, "z": -0.8418192}, "rotation": {"x": 0.0, "y": 45.000042, "z": 0.0}}, {"objectName": "Spoon_94d2125c", "position": {"x": -3.47741938, "y": 0.9148293, "z": -2.82559443}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_94d2125c", "position": {"x": -2.9283, "y": 0.7180968, "z": -0.780375}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_1cd5bb8d", "position": {"x": -0.412048727, "y": 0.7474362, "z": -3.07936954}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_1cd5bb8d", "position": {"x": -2.27053785, "y": 0.9142485, "z": -3.72987866}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_b0ce4484", "position": {"x": -3.28619742, "y": 1.04940462, "z": -2.00261545}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Egg_b0ce4484", "position": {"x": -0.176295966, "y": 0.9519008, "z": -0.524823248}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_bdd88192", "position": {"x": -3.07920766, "y": 1.39705408, "z": -1.94104481}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_bdd88192", "position": {"x": -3.15017486, "y": 0.71192044, "z": -0.558499932}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_f34461c5", "position": {"x": -3.07920718, "y": 1.06308722, "z": -2.125755}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Tomato_f34461c5", "position": {"x": -3.1827023, "y": 0.6908234, "z": -2.12575555}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_2ce80e75", "position": {"x": -0.441580355, "y": 0.742647648, "z": -1.17647409}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_2ce80e75", "position": {"x": -2.8033, "y": 0.7132735, "z": -0.780375}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_908150ce", "position": {"x": -0.0763015747, "y": 1.0015142, "z": -3.112597}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_1634f71e", "position": {"x": -2.8033, "y": 0.75641, "z": -0.9225625}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_1634f71e", "position": {"x": -3.0533, "y": 0.7596072, "z": -0.780375}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_fd5c5c80", "position": {"x": -0.1653, "y": 0.9759, "z": -2.0309}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Egg_b0ce4484", "position": {"x": -1.56911922, "y": 0.8266152, "z": -3.7900424}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_410beacd", "position": {"x": -0.01422599, "y": 0.9163254, "z": -1.61635566}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_77e17ce3", "position": {"x": -2.7408, "y": 0.75464195, "z": -0.638187468}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_1cd5bb8d", "position": {"x": -2.581425, "y": 0.717515945, "z": -0.371000022}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pan_4966d0cc", "position": {"x": -0.3946991, "y": 0.9731094, "z": -2.41475344}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_2b74ee3a", "position": {"x": -0.0963172, "y": 1.05657792, "z": -0.950173}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Lettuce_908150ce", "position": {"x": -1.323777, "y": 0.9882513, "z": -3.75811124}, "rotation": {"x": 1.03954041, "y": 253.596725, "z": 356.1309}}, {"objectName": "Tomato_f34461c5", "position": {"x": -0.253700048, "y": 0.129380181, "z": 0.05522987}, "rotation": {"x": 0.0, "y": 269.999969, "z": 0.0}}, {"objectName": "Apple_1634f71e", "position": {"x": -0.500435948, "y": 0.9586398, "z": -1.67770243}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_dd4bbd23", "position": {"x": -0.135803968, "y": 1.29672146, "z": -3.681419}, "rotation": {"x": 0.0, "y": 315.0, "z": 0.0}}, {"objectName": "Spatula_fca8a224", "position": {"x": -0.8549319, "y": 0.9286998, "z": -3.58163619}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_2ce80e75", "position": {"x": -0.128153652, "y": 1.864464, "z": -2.47257113}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_46bffc5f", "position": {"x": -0.09526098, "y": 0.9484372, "z": -1.49366212}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PepperShaker_5fac94a5", "position": {"x": -3.0079875, "y": 0.710076332, "z": -0.6835}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "ButterKnife_a7531534", "position": {"x": -0.8549319, "y": 0.913742, "z": -3.804}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_bdd88192", "position": {"x": -2.608369, "y": 0.711920559, "z": -0.841820061}, "rotation": {"x": 0.0, "y": 135.000092, "z": 0.0}}, {"objectName": "DishSponge_78f219bc", "position": {"x": -0.412048757, "y": 0.745905757, "z": -0.5339535}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_94d2125c", "position": {"x": -2.9908, "y": 0.7148996, "z": -0.9225625}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f4b8ca2f", "position": {"x": -0.24455595, "y": 0.9086999, "z": -3.2130084}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_697b561f", "position": {"x": -0.257330954, "y": 0.913598061, "z": -1.3709687}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3837425287, "scene_num": 19}, "task_id": "trial_T20190908_130450_106025", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AJQGWGESKQT4Y_3B2X28YI3Z6T2Q3ZKI8ADVN1CXI6BP", "high_descs": ["Go right and then right again to face the counter to the left of the microwave.", "Pick the egg up from the counter.", "Move to the right to face the microwave.", "Put the egg in the microwave and shut the door and then open the door and pick the egg up again.", "Go right and then to the right again to stand in front of the fridge.", "Put the egg in the fridge and shut the door."], "task_desc": "Put a heated egg into the fridge.", "votes": [1, 1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_3G5F9DBFOSOO8TLMLIQEBY6YO3FVHM", "high_descs": ["turn around and walk to the kitchen counter to the left of the microwave", "grab the egg off of the counter in front of you", "turn to move to the right just a bit and face the microwave again", "place the egg inside the microwave, turn on the microwave, take the egg back out", "turn around and walk over to the fridge at the other end of the room", "place the egg inside the fridge"], "task_desc": "place a microwaved egg inside the egg", "votes": [1, 1, 1]}, {"assignment_id": "A31681CCEVDIH3_30H4UDGLT59XVNP1Q3XSXBPCJ17MPV", "high_descs": ["Turn around to face the microwave.", "Pick up an egg from the left of the microwave.", "Walk to the front of the microwave.", "Cook the egg in the microwave on a plate, removing it afterwards.", "Turn around and walk to the fridge. ", "Put the egg on the top shelf of the fridge in front of the tomato."], "task_desc": "Heat up an egg to put in the fridge.", "votes": [1, 1, 0]}]}}