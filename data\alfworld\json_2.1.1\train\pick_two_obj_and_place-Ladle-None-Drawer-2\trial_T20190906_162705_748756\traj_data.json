{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000265.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000266.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000267.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000268.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000269.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000270.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000271.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000272.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000273.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000274.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000275.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000277.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000278.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000279.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000280.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000281.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000282.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000284.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000285.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000286.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000287.png", "low_idx": 59}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Ladle", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-3|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["ladle"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Ladle", [-7.497024, -7.497024, -5.27575016, -5.27575016, 3.8234496, 3.8234496]], "coordinateReceptacleObjectId": ["CounterTop", [0.008, 0.008, -6.052, -6.052, 3.788, 3.788]], "forceVisible": true, "objectId": "Ladle|-01.87|+00.96|-01.32"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|3|6|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["ladle", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Ladle", [-7.497024, -7.497024, -5.27575016, -5.27575016, 3.8234496, 3.8234496]], "coordinateReceptacleObjectId": ["Drawer", [6.0, 6.0, 4.88, 4.88, 3.1304, 3.1304]], "forceVisible": true, "objectId": "Ladle|-01.87|+00.96|-01.32", "receptacleObjectId": "Drawer|+01.50|+00.78|+01.22"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|3|5|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["ladle"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Ladle", [0.001645565032, 0.001645565032, 4.80920456, 4.80920456, 3.8798496, 3.8798496]], "coordinateReceptacleObjectId": ["CounterTop", [-0.06, -0.06, 2.024, 2.024, 3.8444, 3.8444]], "forceVisible": true, "objectId": "Ladle|+00.00|+00.97|+01.20"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|3|6|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["ladle", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Ladle", [0.001645565032, 0.001645565032, 4.80920456, 4.80920456, 3.8798496, 3.8798496]], "coordinateReceptacleObjectId": ["Drawer", [6.0, 6.0, 4.88, 4.88, 3.1304, 3.1304]], "forceVisible": true, "objectId": "Ladle|+00.00|+00.97|+01.20", "receptacleObjectId": "Drawer|+01.50|+00.78|+01.22"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Ladle|-01.87|+00.96|-01.32"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [1, 90, 28, 138], "mask": [[26718, 5], [27015, 11], [27313, 14], [27611, 17], [27910, 18], [28209, 20], [28508, 21], [28808, 21], [29108, 21], [29408, 21], [29708, 21], [30008, 21], [30309, 19], [30609, 19], [30910, 17], [31211, 15], [31513, 11], [31814, 8], [32113, 3], [32413, 3], [32712, 3], [33012, 3], [33311, 3], [33611, 3], [33910, 3], [34209, 4], [34509, 4], [34808, 4], [35108, 4], [35407, 4], [35707, 4], [36007, 3], [36306, 4], [36606, 4], [36905, 4], [37205, 4], [37504, 4], [37804, 4], [38104, 4], [38403, 4], [38703, 4], [39003, 4], [39302, 4], [39602, 4], [39902, 1], [39905, 1], [40201, 1], [40204, 1], [40501, 4], [40801, 4], [41101, 3]], "point": [13, 112]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|+01.50|+00.78|+01.22"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [145, 151, 294, 167], "mask": [[45145, 150], [45445, 150], [45745, 149], [46045, 149], [46345, 148], [46645, 147], [46945, 147], [47245, 146], [47545, 146], [47845, 145], [48145, 145], [48445, 144], [48745, 144], [49045, 143], [49345, 142], [49645, 142], [49945, 141]], "point": [219, 158]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Ladle|-01.87|+00.96|-01.32", "placeStationary": true, "receptacleObjectId": "Drawer|+01.50|+00.78|+01.22"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [143, 151, 299, 263], "mask": [[45149, 141], [45449, 142], [45749, 142], [46049, 143], [46349, 143], [46649, 144], [46949, 144], [47249, 145], [47549, 145], [47849, 146], [48149, 147], [48449, 147], [48749, 148], [49049, 148], [49349, 149], [49649, 149], [49949, 150], [50249, 150], [50549, 151], [50849, 151], [51149, 151], [51449, 151], [51749, 151], [52049, 151], [52349, 151], [52649, 151], [52949, 151], [53249, 151], [53549, 151], [53849, 151], [54149, 151], [54449, 151], [54749, 151], [55049, 151], [55349, 151], [55649, 151], [55949, 151], [56249, 151], [56549, 151], [56849, 151], [57149, 151], [57449, 151], [57749, 151], [58049, 151], [58349, 151], [58649, 151], [58949, 151], [59249, 151], [59549, 151], [59849, 151], [60149, 151], [60449, 151], [60749, 151], [61049, 151], [61349, 151], [61649, 151], [61949, 151], [62249, 151], [62549, 151], [62849, 151], [63149, 151], [63449, 151], [63749, 151], [64049, 151], [64349, 151], [64649, 151], [64949, 151], [65249, 151], [65549, 151], [65849, 151], [66149, 151], [66449, 151], [66749, 151], [67049, 151], [67349, 151], [67649, 151], [67949, 151], [68249, 151], [68549, 151], [68849, 151], [69149, 151], [69449, 151], [69749, 151], [70049, 151], [70349, 151], [70649, 151], [70949, 151], [71249, 151], [71549, 151], [71849, 151], [72149, 151], [72449, 151], [72749, 151], [73043, 157], [73343, 157], [73643, 157], [73943, 157], [74243, 157], [74543, 157], [74843, 157], [75143, 157], [75443, 157], [75743, 45], [75833, 67], [76043, 26], [76137, 63], [76343, 11], [76438, 62], [76643, 3], [76723, 5], [76738, 62], [77023, 6], [77038, 62], [77323, 7], [77339, 61], [77624, 6], [77639, 61], [77924, 6], [77939, 61], [78227, 2], [78239, 61], [78539, 61], [78839, 61]], "point": [221, 206]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|+01.50|+00.78|+01.22"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [143, 151, 299, 263], "mask": [[45149, 141], [45449, 142], [45749, 142], [46049, 143], [46349, 143], [46649, 144], [46949, 144], [47249, 145], [47549, 145], [47849, 146], [48149, 147], [48449, 147], [48749, 148], [49049, 148], [49349, 149], [49649, 149], [49949, 150], [50249, 150], [50549, 151], [50849, 151], [51149, 151], [51449, 151], [51749, 151], [52049, 151], [52349, 151], [52649, 151], [52949, 151], [53249, 151], [53549, 151], [53849, 151], [54149, 151], [54449, 151], [54749, 151], [55049, 151], [55349, 151], [55649, 151], [55949, 151], [56249, 151], [56549, 151], [56849, 151], [57149, 151], [57449, 151], [57749, 151], [58049, 151], [58349, 151], [58649, 151], [58949, 151], [59249, 151], [59549, 151], [59849, 151], [60149, 151], [60449, 151], [60749, 151], [61049, 151], [61349, 151], [61649, 151], [61949, 83], [62042, 58], [62249, 81], [62344, 56], [62549, 80], [62646, 54], [62849, 79], [62948, 52], [63149, 79], [63249, 51], [63449, 78], [63550, 50], [63749, 78], [63851, 49], [64049, 37], [64091, 36], [64152, 48], [64349, 25], [64408, 19], [64452, 48], [64649, 25], [64678, 3], [64718, 8], [64753, 47], [64949, 25], [64978, 2], [65053, 47], [65249, 25], [65353, 47], [65549, 66], [65653, 47], [65849, 78], [65954, 46], [66149, 78], [66254, 46], [66449, 79], [66554, 46], [66749, 79], [66853, 47], [67049, 80], [67153, 47], [67349, 80], [67453, 47], [67649, 81], [67752, 48], [67949, 82], [68051, 49], [68249, 83], [68350, 50], [68549, 85], [68649, 51], [68849, 87], [68947, 53], [69149, 91], [69243, 57], [69449, 151], [69749, 151], [70049, 151], [70349, 151], [70649, 151], [70949, 151], [71249, 151], [71549, 151], [71849, 151], [72149, 151], [72449, 151], [72749, 151], [73043, 157], [73343, 157], [73643, 157], [73943, 157], [74243, 157], [74543, 157], [74843, 157], [75143, 157], [75443, 157], [75743, 157], [76043, 157], [76343, 157], [76643, 157], [76943, 157], [77244, 156], [77544, 156], [77844, 156], [78144, 156], [78444, 156], [78744, 156]], "point": [221, 206]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Ladle|+00.00|+00.97|+01.20"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [127, 104, 149, 159], "mask": [[31034, 8], [31331, 14], [31630, 16], [31929, 18], [32228, 20], [32528, 21], [32828, 21], [33127, 22], [33427, 23], [33727, 22], [34028, 21], [34328, 21], [34628, 21], [34929, 19], [35229, 19], [35530, 17], [35831, 15], [36132, 12], [36434, 9], [36737, 3], [37037, 3], [37336, 4], [37636, 4], [37936, 3], [38236, 3], [38536, 3], [38836, 3], [39136, 3], [39436, 3], [39736, 3], [40036, 3], [40336, 3], [40636, 3], [40935, 4], [41235, 4], [41535, 4], [41835, 4], [42135, 4], [42435, 4], [42735, 4], [43035, 4], [43335, 4], [43635, 4], [43935, 4], [44235, 4], [44535, 4], [44835, 4], [45134, 5], [45434, 5], [45734, 5], [46034, 1], [46038, 1], [46334, 1], [46338, 1], [46634, 5], [46934, 5], [47234, 5], [47535, 3]], "point": [138, 130]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|+01.50|+00.78|+01.22"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [145, 151, 294, 167], "mask": [[45145, 150], [45445, 150], [45745, 149], [46045, 149], [46345, 148], [46645, 147], [46945, 147], [47245, 146], [47545, 146], [47845, 145], [48145, 145], [48445, 144], [48745, 144], [49045, 143], [49345, 142], [49645, 142], [49945, 141]], "point": [219, 158]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Ladle|+00.00|+00.97|+01.20", "placeStationary": true, "receptacleObjectId": "Drawer|+01.50|+00.78|+01.22"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [143, 151, 299, 263], "mask": [[45149, 141], [45449, 142], [45749, 142], [46049, 143], [46349, 143], [46649, 144], [46949, 144], [47249, 145], [47549, 145], [47849, 146], [48149, 147], [48449, 147], [48749, 148], [49049, 148], [49349, 149], [49649, 149], [49949, 150], [50249, 150], [50549, 151], [50849, 151], [51149, 151], [51449, 151], [51749, 151], [52049, 151], [52349, 151], [52649, 151], [52949, 151], [53249, 151], [53549, 151], [53849, 151], [54149, 151], [54449, 151], [54749, 151], [55049, 151], [55349, 151], [55649, 151], [55949, 151], [56249, 151], [56549, 151], [56849, 151], [57149, 151], [57449, 151], [57749, 151], [58049, 151], [58349, 151], [58649, 151], [58949, 151], [59249, 151], [59549, 151], [59849, 151], [60149, 151], [60449, 151], [60749, 151], [61049, 151], [61349, 151], [61649, 151], [61949, 83], [62042, 58], [62249, 81], [62344, 56], [62549, 80], [62646, 54], [62849, 79], [62948, 52], [63149, 79], [63249, 51], [63449, 78], [63550, 50], [63749, 78], [63851, 49], [64049, 37], [64091, 36], [64152, 48], [64349, 25], [64408, 19], [64452, 48], [64649, 25], [64678, 3], [64718, 8], [64753, 47], [64949, 25], [64978, 2], [65053, 47], [65249, 25], [65353, 47], [65549, 66], [65653, 47], [65849, 78], [65954, 46], [66149, 78], [66254, 46], [66449, 79], [66554, 46], [66749, 79], [66853, 47], [67049, 80], [67153, 47], [67349, 80], [67453, 47], [67649, 81], [67752, 48], [67949, 82], [68051, 49], [68249, 83], [68350, 50], [68549, 85], [68649, 51], [68849, 87], [68947, 53], [69149, 91], [69243, 57], [69449, 151], [69749, 151], [70049, 151], [70349, 151], [70649, 151], [70949, 151], [71249, 151], [71549, 151], [71849, 151], [72149, 151], [72449, 151], [72749, 151], [73043, 157], [73343, 157], [73643, 157], [73943, 157], [74243, 157], [74543, 157], [74843, 157], [75143, 157], [75443, 157], [75743, 45], [75833, 67], [76043, 26], [76137, 63], [76343, 11], [76438, 62], [76643, 3], [76723, 5], [76738, 62], [77023, 6], [77038, 62], [77323, 7], [77339, 61], [77624, 6], [77639, 61], [77924, 6], [77939, 61], [78227, 2], [78239, 61], [78539, 61], [78839, 61]], "point": [221, 206]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|+01.50|+00.78|+01.22"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [143, 151, 299, 263], "mask": [[45149, 141], [45449, 142], [45749, 142], [46049, 143], [46349, 143], [46649, 144], [46949, 144], [47249, 145], [47549, 145], [47849, 146], [48149, 147], [48449, 147], [48749, 148], [49049, 148], [49349, 149], [49649, 149], [49949, 150], [50249, 150], [50549, 151], [50849, 151], [51149, 151], [51449, 151], [51749, 151], [52049, 151], [52349, 151], [52649, 151], [52949, 151], [53249, 151], [53549, 151], [53849, 151], [54149, 80], [54231, 69], [54449, 76], [54536, 64], [54749, 74], [54838, 62], [55049, 73], [55139, 61], [55349, 72], [55441, 59], [55649, 72], [55742, 58], [55949, 72], [56043, 57], [56249, 23], [56301, 19], [56343, 57], [56549, 23], [56575, 3], [56610, 10], [56644, 56], [56849, 23], [56944, 56], [57149, 23], [57175, 23], [57245, 55], [57449, 64], [57545, 55], [57749, 71], [57845, 55], [58049, 72], [58145, 55], [58349, 72], [58445, 55], [58649, 73], [58744, 56], [58949, 73], [59044, 56], [59249, 74], [59343, 57], [59549, 75], [59642, 58], [59849, 76], [59941, 59], [60149, 78], [60240, 60], [60449, 80], [60537, 63], [60749, 151], [61049, 151], [61349, 151], [61649, 151], [61949, 83], [62042, 58], [62249, 81], [62344, 56], [62549, 80], [62646, 54], [62849, 79], [62948, 52], [63149, 79], [63249, 51], [63449, 78], [63550, 50], [63749, 78], [63851, 49], [64049, 37], [64091, 36], [64152, 48], [64349, 25], [64408, 19], [64452, 48], [64649, 25], [64678, 3], [64718, 8], [64753, 47], [64949, 25], [64978, 2], [65053, 47], [65249, 25], [65353, 47], [65549, 66], [65653, 47], [65849, 78], [65954, 46], [66149, 78], [66254, 46], [66449, 79], [66554, 46], [66749, 79], [66853, 47], [67049, 80], [67153, 47], [67349, 80], [67453, 47], [67649, 81], [67752, 48], [67949, 82], [68051, 49], [68249, 83], [68350, 50], [68549, 85], [68649, 51], [68849, 87], [68947, 53], [69149, 91], [69243, 57], [69449, 151], [69749, 151], [70049, 151], [70349, 151], [70649, 151], [70949, 151], [71249, 151], [71549, 151], [71849, 151], [72149, 151], [72449, 151], [72749, 151], [73043, 157], [73343, 157], [73643, 157], [73943, 157], [74243, 157], [74543, 157], [74843, 157], [75143, 157], [75443, 157], [75743, 157], [76043, 157], [76343, 157], [76643, 157], [76943, 157], [77244, 156], [77544, 156], [77844, 156], [78144, 156], [78444, 156], [78744, 156]], "point": [221, 206]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan2", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": 1.0, "y": 0.9009992, "z": 2.75}, "object_poses": [{"objectName": "Pan_6d10ab8d", "position": {"x": 0.1074348, "y": 0.925209463, "z": 0.273899674}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_5e728867", "position": {"x": 1.50984621, "y": 0.9352317, "z": 0.6395407}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_ec6bb035", "position": {"x": 0.000411391258, "y": 0.9699624, "z": 1.20230114}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_c80d57de", "position": {"x": 0.000411391258, "y": 0.926148534, "z": 0.506000042}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_c80d57de", "position": {"x": 0.000411391258, "y": 0.926148534, "z": 0.0417993069}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_de267f63", "position": {"x": 0.0837812647, "y": 0.7754439, "z": -1.38022017}, "rotation": {"x": 0.0, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Pot_1bc23cfd", "position": {"x": 1.9753, "y": 0.938299954, "z": 0.8192}, "rotation": {"x": 0.0, "y": 180.000214, "z": 0.0}}, {"objectName": "Knife_e0496240", "position": {"x": 1.76047993, "y": 0.7173935, "z": 0.543363}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_448791c6", "position": {"x": 2.03011179, "y": 0.9666673, "z": -1.70706248}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_448791c6", "position": {"x": -1.55163574, "y": 0.966667235, "z": -0.9641914}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_253fa632", "position": {"x": 1.19580007, "y": 0.107557893, "z": -1.47949946}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_aa2e533f", "position": {"x": 0.1074348, "y": 1.00230384, "z": 0.7381004}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_b045e42b", "position": {"x": 0.250071466, "y": 0.107787192, "z": -1.28296089}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_b045e42b", "position": {"x": -0.361994565, "y": 0.1098519, "z": -1.222}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_487a7950", "position": {"x": -1.69785666, "y": 1.52249694, "z": 0.107331038}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_487a7950", "position": {"x": 0.889933, "y": 0.9647827, "z": -1.70706248}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_7ce82db0", "position": {"x": -1.114, "y": 1.68317306, "z": -1.61474991}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_4e364eea", "position": {"x": 0.321481615, "y": 0.923730552, "z": 0.506000042}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_1bc23cfd", "position": {"x": 1.9753, "y": 0.938299954, "z": 0.3846}, "rotation": {"x": 0.0, "y": 180.000214, "z": 0.0}}, {"objectName": "Fork_c80d57de", "position": {"x": -0.106612019, "y": 0.926148534, "z": 0.9702008}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_ec6bb035", "position": {"x": -1.874256, "y": 0.9558624, "z": -1.31893754}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_233c7df0", "position": {"x": 1.746321, "y": 0.9125413, "z": -0.131973028}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7f5e18ec", "position": {"x": -1.77133465, "y": 1.08623946, "z": -0.08101666}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_d852c2bd", "position": {"x": -1.2440033, "y": 0.112174153, "z": -1.36015964}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_6d10ab8d", "position": {"x": -1.28641355, "y": 0.9, "z": -1.34873831}, "rotation": {"x": 0.0, "y": 79.76979, "z": 0.0}}, {"objectName": "Lettuce_aa2e533f", "position": {"x": -0.08891334, "y": 0.8371549, "z": -1.49077988}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_de267f63", "position": {"x": 0.889933, "y": 0.9264999, "z": -1.41596878}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_487a7950", "position": {"x": 0.170128569, "y": 0.8138537, "z": -1.38022017}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_e0496240", "position": {"x": -0.106612019, "y": 0.9475927, "z": -0.190301061}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_586649ce", "position": {"x": -1.95747948, "y": 1.658286, "z": -1.35827529}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PepperShaker_14add4df", "position": {"x": -0.6997603, "y": 0.6678068, "z": -1.24005008}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_6cb12d41", "position": {"x": 1.713, "y": 0.2243176, "z": 0.462655365}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_4f8c70c0", "position": {"x": 1.71401417, "y": 0.763346, "z": 1.22007179}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}, {"objectName": "Bread_48e00ad2", "position": {"x": 1.802076, "y": 0.9920797, "z": -1.70706248}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_448791c6", "position": {"x": -0.21363543, "y": 0.98076725, "z": 0.7381004}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5e728867", "position": {"x": -1.81868362, "y": 0.06506693, "z": 1.32999992}, "rotation": {"x": 0.0, "y": 269.999939, "z": 0.0}}, {"objectName": "DishSponge_e1c57c26", "position": {"x": 1.82451987, "y": 0.7617755, "z": 1.292941}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_bfeb74cb", "position": {"x": 0.8533346, "y": 0.67263, "z": -1.27077508}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_b045e42b", "position": {"x": 2.03995848, "y": 1.65437984, "z": -1.20452523}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}, {"objectName": "Bowl_253fa632", "position": {"x": -0.3059555, "y": 0.113292515, "z": -1.4095}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 4037889622, "scene_num": 2}, "task_id": "trial_T20190906_162705_748756", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A3D61CCA0LVMWV_3DUZQ9U6SP55DDD404DWEQTX0LSSVC", "high_descs": ["Walk to the fridge.", "Pick up a spoon from on the counter to the left of the fridge.", "Walk back to the range.", "Open the top drawer to the left of the range and place the spoon inside.", "Close the drawer and turn around.", "Pick up the spoon from the counter.", "Turn around and face the drawers again.", "Open the top drawer to the left of the range and place the second spoon inside, then close the drawer."], "task_desc": "Pick up two spoons and place them in the top drawer to the left of the range.", "votes": [1, 1, 1, 1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3R9WASFE22XDISDSVH20SXNEZCPZFX", "high_descs": ["Turn around, then walk straight to the counter, turn right, and walk over to the spoon on the counter.", "Pick up the spoon with a blue handle.", "Turn around and walk towards the counter, turn left at the dishwasher and stop at the last drawer on the right.", "Open the drawer, place the spoon inside, then close it.", "Turn around and take a step towards the table.", "Pick up the golden spoon with a blue handle.", "Turn back around and step towards the last drawer to the left of the stove.", "Open the drawer, place the spoon inside, then close it."], "task_desc": "Put two spoons into a drawer.", "votes": [1, 1, 1, 1, 1]}, {"assignment_id": "A1MKCTVKE7J0ZP_3I33IC7ZWIJSGQT8VU7URL2HJ5FA2T", "high_descs": ["Turn around, go straight toward the coffee maker, then turn right, then go straight to the counter.", "Pick up the spoon on the counter, to the left of the fridge.", "Turn around, go straight, turn left at the dishwasher, go straight, then turn right at the leftmost counter.", "Put the spoon in the top, leftmost drawer under the counter.", "Turn right, then turn right at the kitchen island.", "Pick up the spoon on the kitchen island, to the right of the fork.", "Turn around and return to the leftmost counter.", "Place the spoon in the top drawer of the counter."], "task_desc": "Put two spoons in a drawer.", "votes": [1, 1, 1, 1]}, {"assignment_id": "ADXRJFEHIDXRE_3SB5N7Y3O6LMU73X27VQ4JK5LPI0GW", "high_descs": ["Turn around and walk forward then turn right until you reach the counter.", "Pick up the blue and gold spoon from the counter.", "Turn around and walk forward then turn right, walk to the end of the counter and face it.", "Open the top drawer and put the spoon inside it.", "Turn around and face the table.", "Pick up the blue and gold spoon from the table.", "Turn around and face the counter.", "Open the top drawer and put the spoon inside it."], "task_desc": "Put both blue and gold spoons inside the top drawer of the counter.", "votes": [1, 1]}, {"assignment_id": "A1AKL5YH9NLD2V_33PPO7FECYWUPIAM47JO42H4UHDID9", "high_descs": ["Turn around and walk to the counter to the left of the fridge.", "Pick up the spoon with the blue handle from the counter.", "Turn around and walk to the drawer to the left of the stove.", "Place the spoon in the top drawer to the left of the stove then close the drawer.", "Turn around to face the spoon on the table.", "Pick up the spoon with the blue handle from the table.", "Turn around to face the drawer to the left of the stove.", "Place the spoon above the other spoon in the top drawer to the left of the stove."], "task_desc": "Place two spoons with blue handles in the top drawer to the left of the stove.", "votes": [1, 1]}]}}