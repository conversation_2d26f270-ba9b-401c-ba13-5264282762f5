{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000243.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000244.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000245.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000246.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000247.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000248.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000249.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000254.png", "low_idx": 46}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "CounterTop", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|3|-2|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [3.3236084, 3.3236084, 0.68612194, 0.68612194, 3.8539804, 3.8539804]], "coordinateReceptacleObjectId": ["CounterTop", [4.844, 4.844, 1.856, 1.856, 3.7488, 3.7488]], "forceVisible": true, "objectId": "Egg|+00.83|+00.96|+00.17"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-1|5|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-4.136, -4.136, 5.716, 5.716, 3.5567708, 3.5567708]], "forceVisible": true, "objectId": "Microwave|-01.03|+00.89|+01.43"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-1|0|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "countertop"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [3.3236084, 3.3236084, 0.68612194, 0.68612194, 3.8539804, 3.8539804]], "coordinateReceptacleObjectId": ["CounterTop", [-4.0316868, -4.0316868, -0.1576, -0.1576, 3.7432, 3.7432]], "forceVisible": true, "objectId": "Egg|+00.83|+00.96|+00.17", "receptacleObjectId": "CounterTop|-01.01|+00.94|-00.04"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+00.83|+00.96|+00.17"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [161, 133, 183, 159], "mask": [[39770, 6], [40068, 10], [40366, 13], [40665, 15], [40965, 16], [41264, 18], [41563, 19], [41863, 20], [42162, 21], [42462, 21], [42761, 23], [43061, 23], [43361, 23], [43661, 23], [43961, 23], [44261, 23], [44561, 23], [44861, 23], [45161, 22], [45462, 21], [45762, 21], [46063, 19], [46363, 18], [46664, 16], [46965, 14], [47267, 11], [47569, 7]], "point": [172, 145]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [103, 21, 299, 168], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22304, 196], [22604, 196], [22904, 196], [23204, 196], [23504, 196], [23804, 196], [24104, 196], [24405, 195], [24705, 195], [25005, 195], [25305, 195], [25605, 195], [25905, 195], [26205, 195], [26505, 195], [26805, 195], [27106, 194], [27406, 194], [27706, 194], [28006, 194], [28306, 194], [28606, 194], [28906, 194], [29206, 194], [29507, 193], [29807, 193], [30107, 193], [30407, 193], [30707, 193], [31007, 193], [31307, 193], [31608, 192], [31908, 192], [32208, 192], [32508, 192], [32808, 192], [33108, 192], [33408, 192], [33709, 191], [34009, 191], [34309, 191], [34609, 191], [34909, 191], [35209, 191], [35509, 191], [35810, 190], [36110, 190], [36410, 190], [36710, 190], [37010, 190], [37310, 190], [37610, 190], [37911, 189], [38211, 189], [38511, 189], [38811, 189], [39111, 189], [39411, 189], [39711, 189], [40012, 188], [40312, 188], [40612, 187], [40912, 187], [41212, 186], [41512, 186], [41812, 185], [42112, 184], [42413, 183], [42713, 182], [43013, 182], [43313, 181], [43613, 181], [43913, 180], [44213, 180], [44514, 178], [44814, 178], [45114, 177], [45414, 176], [45714, 176], [46014, 175], [46314, 175], [46615, 173], [46915, 173], [47215, 172], [47515, 172], [47815, 171], [48115, 171], [48416, 169], [48716, 169], [49016, 168], [49316, 168], [49616, 167], [49916, 167], [50217, 165]], "point": [201, 93]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+00.83|+00.96|+00.17", "placeStationary": true, "receptacleObjectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [37, 21, 299, 294], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22294, 206], [22593, 207], [22893, 207], [23193, 207], [23492, 208], [23792, 208], [24092, 208], [24391, 209], [24691, 209], [24991, 209], [25291, 209], [25590, 210], [25890, 100], [26001, 99], [26190, 98], [26304, 96], [26489, 97], [26606, 94], [26789, 95], [26908, 92], [27089, 57], [27159, 24], [27209, 91], [27388, 55], [27462, 20], [27510, 90], [27688, 53], [27764, 17], [27811, 89], [27988, 52], [28065, 15], [28112, 88], [28287, 52], [28366, 14], [28412, 88], [28587, 51], [28667, 12], [28713, 87], [28887, 50], [28968, 11], [29013, 87], [29187, 50], [29268, 10], [29313, 87], [29486, 50], [29569, 9], [29614, 86], [29786, 50], [29869, 9], [29914, 86], [30086, 50], [30169, 9], [30214, 86], [30385, 51], [30469, 9], [30513, 87], [30685, 50], [30770, 8], [30813, 87], [30985, 50], [31070, 8], [31113, 87], [31284, 51], [31370, 8], [31413, 87], [31584, 51], [31670, 8], [31713, 87], [31884, 51], [31970, 9], [32013, 87], [32183, 52], [32270, 9], [32312, 88], [32483, 52], [32570, 9], [32612, 88], [32783, 52], [32869, 11], [32911, 89], [33083, 53], [33169, 11], [33211, 89], [33382, 54], [33469, 12], [33510, 90], [33682, 54], [33769, 12], [33809, 91], [33982, 55], [34068, 14], [34109, 91], [34281, 56], [34368, 15], [34408, 92], [34581, 56], [34667, 17], [34706, 94], [34881, 57], [34967, 18], [35005, 95], [35180, 58], [35267, 20], [35304, 96], [35480, 59], [35566, 23], [35602, 98], [35780, 60], [35865, 29], [35900, 100], [36080, 61], [36164, 136], [36379, 63], [36463, 137], [36679, 64], [36762, 138], [36979, 66], [37059, 141], [37278, 70], [37357, 143], [37578, 222], [37878, 222], [38177, 223], [38477, 223], [38777, 223], [39076, 224], [39376, 224], [39676, 224], [39976, 224], [40275, 225], [40575, 224], [40875, 224], [41174, 224], [41474, 224], [41774, 223], [42073, 223], [42373, 223], [42673, 222], [42972, 223], [43272, 222], [43572, 222], [43872, 221], [44171, 222], [44471, 221], [44771, 221], [45070, 221], [45370, 220], [45670, 220], [45969, 220], [46269, 220], [46569, 219], [46868, 220], [47168, 219], [47468, 219], [47768, 218], [48067, 219], [48367, 218], [48667, 218], [48966, 218], [49266, 218], [49566, 217], [49865, 218], [50165, 217], [50465, 52], [50765, 52], [51064, 53], [51364, 53], [51664, 53], [51963, 54], [52263, 53], [52563, 53], [52862, 54], [53162, 54], [53462, 54], [53761, 55], [54061, 55], [54361, 55], [54661, 55], [54960, 55], [55260, 55], [55560, 55], [55859, 56], [56159, 56], [56459, 56], [56758, 57], [57058, 57], [57358, 56], [57657, 57], [57957, 57], [58257, 57], [58557, 57], [58856, 58], [59156, 58], [59456, 58], [59755, 59], [60055, 58], [60355, 58], [60654, 59], [60954, 59], [61254, 59], [61553, 60], [61853, 60], [62153, 60], [62453, 59], [62752, 60], [63052, 60], [63352, 60], [63651, 61], [63951, 61], [64251, 61], [64550, 62], [64841, 71], [65141, 70], [65440, 71], [65740, 71], [66040, 71], [66339, 72], [66639, 72], [66939, 72], [67238, 73], [67538, 72], [67838, 72], [68138, 72], [68438, 72], [68738, 72], [69037, 73], [69337, 73], [69637, 73], [69938, 72], [70238, 71], [70538, 71], [70838, 71], [71138, 71], [71438, 71], [71738, 71], [72038, 71], [72338, 71], [72639, 69], [72939, 69], [73239, 69], [73539, 69], [73839, 69], [74139, 69], [74440, 68], [74740, 68], [75040, 68], [75341, 66], [75641, 66], [75941, 66], [76241, 66], [76542, 65], [76842, 65], [77142, 65], [77443, 64], [77743, 63], [78044, 62], [78344, 62], [78644, 62], [78945, 61], [79245, 61], [79546, 60], [79847, 59], [80148, 57], [80448, 57], [80749, 56], [81050, 55], [81351, 54], [81653, 52], [81954, 51], [82255, 50], [82556, 49], [82858, 46], [83159, 45], [83460, 44], [83762, 42], [84064, 40], [84365, 39], [84667, 37], [84969, 35], [85271, 32], [85573, 30], [85878, 28], [86179, 29], [86479, 31], [86780, 23], [86804, 6], [87080, 23], [87105, 5], [87381, 22], [87682, 21], [87982, 20]], "point": [168, 156]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [37, 21, 299, 294], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22294, 206], [22593, 207], [22893, 207], [23193, 207], [23492, 208], [23792, 208], [24092, 208], [24391, 209], [24691, 209], [24991, 209], [25291, 209], [25590, 210], [25890, 100], [26001, 99], [26190, 98], [26304, 96], [26489, 97], [26606, 94], [26789, 95], [26908, 92], [27089, 57], [27159, 24], [27209, 91], [27388, 55], [27462, 20], [27510, 90], [27688, 53], [27764, 17], [27811, 89], [27988, 52], [28065, 15], [28112, 88], [28287, 52], [28366, 14], [28412, 88], [28587, 51], [28667, 12], [28713, 87], [28887, 50], [28968, 11], [29013, 87], [29187, 50], [29268, 10], [29313, 87], [29486, 50], [29569, 9], [29614, 86], [29786, 50], [29869, 9], [29914, 12], [29934, 66], [30086, 50], [30169, 9], [30214, 10], [30235, 65], [30385, 51], [30469, 9], [30513, 10], [30537, 63], [30685, 50], [30770, 8], [30813, 9], [30837, 63], [30985, 50], [31070, 8], [31113, 8], [31138, 62], [31284, 51], [31370, 8], [31413, 7], [31439, 61], [31584, 51], [31670, 8], [31713, 6], [31739, 61], [31884, 51], [31970, 9], [32013, 5], [32040, 60], [32183, 52], [32270, 9], [32312, 6], [32340, 60], [32483, 52], [32570, 9], [32612, 5], [32640, 60], [32783, 52], [32869, 11], [32911, 6], [32940, 60], [33083, 53], [33169, 11], [33211, 5], [33240, 60], [33382, 54], [33469, 12], [33510, 6], [33541, 59], [33682, 54], [33769, 12], [33809, 7], [33841, 59], [33982, 55], [34068, 14], [34109, 7], [34140, 60], [34281, 56], [34368, 15], [34408, 8], [34440, 60], [34581, 56], [34667, 17], [34706, 10], [34740, 60], [34881, 57], [34967, 18], [35005, 11], [35040, 60], [35180, 58], [35267, 20], [35304, 12], [35340, 60], [35480, 59], [35566, 23], [35602, 14], [35639, 61], [35780, 60], [35865, 29], [35900, 16], [35939, 61], [36080, 61], [36164, 52], [36238, 62], [36379, 63], [36463, 54], [36538, 62], [36679, 64], [36762, 55], [36837, 63], [36979, 66], [37059, 59], [37136, 64], [37278, 70], [37357, 62], [37435, 65], [37578, 142], [37734, 66], [37878, 143], [38033, 67], [38177, 146], [38330, 70], [38477, 223], [38777, 223], [39076, 224], [39376, 224], [39676, 224], [39976, 224], [40275, 225], [40575, 224], [40875, 224], [41174, 224], [41474, 224], [41774, 223], [42073, 223], [42373, 223], [42673, 222], [42972, 223], [43272, 222], [43572, 222], [43872, 221], [44171, 222], [44471, 221], [44771, 221], [45070, 221], [45370, 220], [45670, 220], [45969, 220], [46269, 220], [46569, 219], [46868, 220], [47168, 219], [47468, 219], [47768, 218], [48067, 219], [48367, 218], [48667, 218], [48966, 218], [49266, 218], [49566, 217], [49865, 218], [50165, 217], [50465, 52], [50765, 52], [51064, 53], [51364, 53], [51664, 53], [51963, 54], [52263, 53], [52563, 53], [52862, 54], [53162, 54], [53462, 54], [53761, 55], [54061, 55], [54361, 55], [54661, 55], [54960, 55], [55260, 55], [55560, 55], [55859, 56], [56159, 56], [56459, 56], [56758, 57], [57058, 57], [57358, 56], [57657, 57], [57957, 57], [58257, 57], [58557, 57], [58856, 58], [59156, 58], [59456, 58], [59755, 59], [60055, 58], [60355, 58], [60654, 59], [60954, 59], [61254, 59], [61553, 60], [61853, 60], [62153, 60], [62453, 59], [62752, 60], [63052, 60], [63352, 60], [63651, 61], [63951, 61], [64251, 61], [64550, 62], [64841, 71], [65141, 70], [65440, 71], [65740, 71], [66040, 71], [66339, 72], [66639, 72], [66939, 72], [67238, 73], [67538, 72], [67838, 72], [68138, 72], [68438, 72], [68738, 72], [69037, 73], [69337, 73], [69637, 73], [69938, 72], [70238, 71], [70538, 71], [70838, 71], [71138, 71], [71438, 71], [71738, 71], [72038, 71], [72338, 71], [72639, 69], [72939, 69], [73239, 69], [73539, 69], [73839, 69], [74139, 69], [74440, 68], [74740, 68], [75040, 68], [75341, 66], [75641, 66], [75941, 66], [76241, 66], [76542, 65], [76842, 65], [77142, 65], [77443, 64], [77743, 63], [78044, 62], [78344, 62], [78644, 62], [78945, 61], [79245, 61], [79546, 60], [79847, 59], [80148, 57], [80448, 57], [80749, 56], [81050, 55], [81351, 54], [81653, 52], [81954, 51], [82255, 50], [82556, 49], [82858, 46], [83159, 45], [83460, 44], [83762, 42], [84064, 40], [84365, 39], [84667, 37], [84969, 35], [85271, 32], [85573, 30], [85878, 28], [86179, 29], [86479, 31], [86780, 23], [86804, 6], [87080, 23], [87105, 5], [87381, 22], [87682, 21], [87982, 20]], "point": [168, 156]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-4.136, -4.136, 5.716, 5.716, 3.5567708, 3.5567708]], "forceVisible": true, "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [103, 21, 299, 168], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22304, 196], [22604, 196], [22904, 196], [23204, 196], [23504, 196], [23804, 196], [24104, 196], [24405, 195], [24705, 195], [25005, 195], [25305, 195], [25605, 195], [25905, 195], [26205, 195], [26505, 195], [26805, 195], [27106, 194], [27406, 194], [27706, 194], [28006, 194], [28306, 194], [28606, 194], [28906, 194], [29206, 194], [29507, 193], [29807, 193], [30107, 193], [30407, 193], [30707, 193], [31007, 193], [31307, 193], [31608, 192], [31908, 192], [32208, 192], [32508, 192], [32808, 192], [33108, 192], [33408, 192], [33709, 191], [34009, 191], [34309, 191], [34609, 191], [34909, 191], [35209, 191], [35509, 191], [35810, 190], [36110, 190], [36410, 190], [36710, 190], [37010, 190], [37310, 190], [37610, 190], [37911, 189], [38211, 189], [38511, 189], [38811, 189], [39111, 189], [39411, 189], [39711, 189], [40012, 188], [40312, 188], [40612, 187], [40912, 187], [41212, 186], [41512, 186], [41812, 185], [42112, 184], [42413, 183], [42713, 182], [43013, 182], [43313, 181], [43613, 181], [43913, 180], [44213, 180], [44514, 178], [44814, 178], [45114, 177], [45414, 176], [45714, 176], [46014, 175], [46314, 175], [46615, 173], [46915, 173], [47215, 172], [47515, 172], [47815, 171], [48115, 171], [48416, 169], [48716, 169], [49016, 168], [49316, 168], [49616, 167], [49916, 167], [50217, 165]], "point": [201, 93]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-4.136, -4.136, 5.716, 5.716, 3.5567708, 3.5567708]], "forceVisible": true, "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [103, 21, 299, 168], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22304, 196], [22604, 196], [22904, 196], [23204, 196], [23504, 196], [23804, 196], [24104, 196], [24405, 195], [24705, 195], [25005, 195], [25305, 195], [25605, 195], [25905, 195], [26205, 195], [26505, 195], [26805, 195], [27106, 194], [27406, 194], [27706, 194], [28006, 194], [28306, 194], [28606, 194], [28906, 194], [29206, 194], [29507, 193], [29807, 193], [30107, 193], [30407, 193], [30707, 193], [31007, 193], [31307, 193], [31608, 192], [31908, 192], [32208, 192], [32508, 192], [32808, 192], [33108, 192], [33408, 192], [33709, 191], [34009, 191], [34309, 191], [34609, 191], [34909, 191], [35209, 191], [35509, 191], [35810, 190], [36110, 190], [36410, 190], [36710, 190], [37010, 190], [37310, 190], [37610, 190], [37911, 189], [38211, 189], [38511, 189], [38811, 189], [39111, 189], [39411, 189], [39711, 189], [40012, 188], [40312, 188], [40612, 187], [40912, 187], [41212, 186], [41512, 186], [41812, 185], [42112, 184], [42413, 183], [42713, 182], [43013, 182], [43313, 181], [43613, 181], [43913, 180], [44213, 180], [44514, 178], [44814, 178], [45114, 177], [45414, 176], [45714, 176], [46014, 175], [46314, 175], [46615, 173], [46915, 173], [47215, 172], [47515, 172], [47815, 171], [48115, 171], [48416, 169], [48716, 169], [49016, 168], [49316, 168], [49616, 167], [49916, 167], [50217, 165]], "point": [201, 93]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [103, 21, 299, 168], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22304, 196], [22604, 196], [22904, 196], [23204, 196], [23504, 196], [23804, 196], [24104, 196], [24405, 195], [24705, 195], [25005, 195], [25305, 195], [25605, 195], [25905, 195], [26205, 195], [26505, 195], [26805, 195], [27106, 194], [27406, 194], [27706, 194], [28006, 194], [28306, 194], [28606, 194], [28906, 194], [29206, 194], [29507, 193], [29807, 193], [30107, 193], [30407, 193], [30707, 193], [31007, 193], [31307, 193], [31608, 192], [31908, 192], [32208, 192], [32508, 192], [32808, 192], [33108, 192], [33408, 192], [33709, 191], [34009, 191], [34309, 191], [34609, 191], [34909, 191], [35209, 191], [35509, 191], [35810, 190], [36110, 190], [36410, 190], [36710, 190], [37010, 190], [37310, 190], [37610, 190], [37911, 189], [38211, 189], [38511, 189], [38811, 189], [39111, 189], [39411, 189], [39711, 189], [40012, 188], [40312, 188], [40612, 187], [40912, 187], [41212, 186], [41512, 186], [41812, 185], [42112, 184], [42413, 183], [42713, 182], [43013, 182], [43313, 181], [43613, 181], [43913, 180], [44213, 180], [44514, 178], [44814, 178], [45114, 177], [45414, 176], [45714, 176], [46014, 175], [46314, 175], [46615, 173], [46915, 173], [47215, 172], [47515, 172], [47815, 171], [48115, 171], [48416, 169], [48716, 169], [49016, 168], [49316, 168], [49616, 167], [49916, 167], [50217, 165]], "point": [201, 93]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+00.83|+00.96|+00.17"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [216, 100, 240, 128], "mask": [[29926, 8], [30224, 11], [30523, 14], [30822, 15], [31121, 17], [31420, 19], [31719, 20], [32018, 22], [32318, 22], [32617, 23], [32917, 23], [33216, 24], [33516, 25], [33816, 25], [34116, 24], [34416, 24], [34716, 24], [35016, 24], [35316, 24], [35616, 23], [35916, 23], [36216, 22], [36517, 21], [36817, 20], [37118, 18], [37419, 16], [37720, 14], [38021, 12], [38323, 7]], "point": [228, 113]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [37, 21, 299, 294], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22294, 206], [22593, 207], [22893, 207], [23193, 207], [23492, 208], [23792, 208], [24092, 208], [24391, 209], [24691, 209], [24991, 209], [25291, 209], [25590, 210], [25890, 100], [26001, 99], [26190, 98], [26304, 96], [26489, 97], [26606, 94], [26789, 95], [26908, 92], [27089, 57], [27159, 24], [27209, 91], [27388, 55], [27462, 20], [27510, 90], [27688, 53], [27764, 17], [27811, 89], [27988, 52], [28065, 15], [28112, 88], [28287, 52], [28366, 14], [28412, 88], [28587, 51], [28667, 12], [28713, 87], [28887, 50], [28968, 11], [29013, 87], [29187, 50], [29268, 10], [29313, 87], [29486, 50], [29569, 9], [29614, 86], [29786, 50], [29869, 9], [29914, 86], [30086, 50], [30169, 9], [30214, 86], [30385, 51], [30469, 9], [30513, 87], [30685, 50], [30770, 8], [30813, 87], [30985, 50], [31070, 8], [31113, 87], [31284, 51], [31370, 8], [31413, 87], [31584, 51], [31670, 8], [31713, 87], [31884, 51], [31970, 9], [32013, 87], [32183, 52], [32270, 9], [32312, 88], [32483, 52], [32570, 9], [32612, 88], [32783, 52], [32869, 11], [32911, 89], [33083, 53], [33169, 11], [33211, 89], [33382, 54], [33469, 12], [33510, 90], [33682, 54], [33769, 12], [33809, 91], [33982, 55], [34068, 14], [34109, 91], [34281, 56], [34368, 15], [34408, 92], [34581, 56], [34667, 17], [34706, 94], [34881, 57], [34967, 18], [35005, 95], [35180, 58], [35267, 20], [35304, 96], [35480, 59], [35566, 23], [35602, 98], [35780, 60], [35865, 29], [35900, 100], [36080, 61], [36164, 136], [36379, 63], [36463, 137], [36679, 64], [36762, 138], [36979, 66], [37059, 141], [37278, 70], [37357, 143], [37578, 222], [37878, 222], [38177, 223], [38477, 223], [38777, 223], [39076, 224], [39376, 224], [39676, 224], [39976, 224], [40275, 225], [40575, 224], [40875, 224], [41174, 224], [41474, 224], [41774, 223], [42073, 223], [42373, 223], [42673, 222], [42972, 223], [43272, 222], [43572, 222], [43872, 221], [44171, 222], [44471, 221], [44771, 221], [45070, 221], [45370, 220], [45670, 220], [45969, 220], [46269, 220], [46569, 219], [46868, 220], [47168, 219], [47468, 219], [47768, 218], [48067, 219], [48367, 218], [48667, 218], [48966, 218], [49266, 218], [49566, 217], [49865, 218], [50165, 217], [50465, 52], [50765, 52], [51064, 53], [51364, 53], [51664, 53], [51963, 54], [52263, 53], [52563, 53], [52862, 54], [53162, 54], [53462, 54], [53761, 55], [54061, 55], [54361, 55], [54661, 55], [54960, 55], [55260, 55], [55560, 55], [55859, 56], [56159, 56], [56459, 56], [56758, 57], [57058, 57], [57358, 56], [57657, 57], [57957, 57], [58257, 57], [58557, 57], [58856, 58], [59156, 58], [59456, 58], [59755, 59], [60055, 58], [60355, 58], [60654, 59], [60954, 59], [61254, 59], [61553, 60], [61853, 60], [62153, 60], [62453, 59], [62752, 60], [63052, 60], [63352, 60], [63651, 61], [63951, 61], [64251, 61], [64550, 62], [64841, 71], [65141, 70], [65440, 71], [65740, 71], [66040, 71], [66339, 72], [66639, 72], [66939, 72], [67238, 73], [67538, 72], [67838, 72], [68138, 72], [68438, 72], [68738, 72], [69037, 73], [69337, 73], [69637, 73], [69938, 72], [70238, 71], [70538, 71], [70838, 71], [71138, 71], [71438, 71], [71738, 71], [72038, 71], [72338, 71], [72639, 69], [72939, 69], [73239, 69], [73539, 69], [73839, 69], [74139, 69], [74440, 68], [74740, 68], [75040, 68], [75341, 66], [75641, 66], [75941, 66], [76241, 66], [76542, 65], [76842, 65], [77142, 65], [77443, 64], [77743, 63], [78044, 62], [78344, 62], [78644, 62], [78945, 61], [79245, 61], [79546, 60], [79847, 59], [80148, 57], [80448, 57], [80749, 56], [81050, 55], [81351, 54], [81653, 52], [81954, 51], [82255, 50], [82556, 49], [82858, 46], [83159, 45], [83460, 44], [83762, 42], [84064, 40], [84365, 39], [84667, 37], [84969, 35], [85271, 32], [85573, 30], [85878, 28], [86179, 29], [86479, 31], [86780, 23], [86804, 6], [87080, 23], [87105, 5], [87381, 22], [87682, 21], [87982, 20]], "point": [168, 156]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+00.83|+00.96|+00.17", "placeStationary": true, "receptacleObjectId": "CounterTop|-01.01|+00.94|-00.04"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 113, 299, 223], "mask": [[33600, 2], [33696, 28], [33752, 149], [33996, 28], [34052, 148], [34296, 29], [34352, 148], [34596, 29], [34652, 148], [34896, 29], [34952, 85], [35083, 17], [35196, 29], [35251, 80], [35390, 10], [35496, 29], [35551, 78], [35693, 7], [35796, 29], [35851, 76], [35996, 4], [36095, 30], [36151, 75], [36298, 2], [36395, 31], [36451, 74], [36599, 1], [36695, 31], [36751, 74], [36994, 32], [37050, 74], [37294, 33], [37350, 74], [37594, 33], [37650, 74], [37894, 33], [37950, 74], [38193, 34], [38250, 74], [38493, 35], [38549, 75], [38793, 35], [38849, 76], [39093, 36], [39148, 38], [39189, 36], [39393, 36], [39448, 37], [39492, 33], [39692, 38], [39747, 38], [39795, 30], [39992, 40], [40045, 39], [40098, 27], [40292, 42], [40343, 42], [40400, 25], [40592, 93], [40703, 22], [40891, 94], [41005, 19], [41191, 95], [41308, 16], [41491, 96], [41610, 14], [41791, 99], [41913, 11], [42090, 104], [42216, 7], [42390, 107], [42519, 4], [42690, 109], [42990, 112], [43289, 115], [43589, 118], [43889, 120], [44189, 123], [44488, 126], [44788, 128], [45088, 131], [45388, 133], [45688, 136], [45987, 138], [46287, 138], [46587, 138], [46887, 139], [47186, 140], [47486, 140], [47786, 141], [48086, 141], [48385, 142], [48685, 142], [48985, 143], [49285, 143], [49585, 143], [49885, 144], [50100, 1], [50185, 144], [50400, 1], [50408, 70], [50484, 145], [50700, 2], [50707, 71], [50784, 145], [51000, 230], [51300, 230], [51600, 230], [51900, 231], [52200, 231], [52500, 231], [52800, 231], [53100, 232], [53400, 232], [53700, 232], [54000, 233], [54300, 233], [54600, 233], [54900, 233], [55200, 234], [55500, 234], [55800, 234], [56100, 235], [56400, 235], [56700, 236], [57000, 236], [57300, 237], [57600, 238], [57900, 240], [58200, 8700]], "point": [149, 167]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan30", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 2.5, "y": 0.9304675, "z": 0.75}, "object_poses": [{"objectName": "Potato_ed324e47", "position": {"x": 1.24237132, "y": 1.28645647, "z": -1.44805622}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_3cec4766", "position": {"x": 3.27791429, "y": 0.9003642, "z": -0.8978308}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_db774b98", "position": {"x": 0.0125830695, "y": 0.900342047, "z": -1.33757544}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "ButterKnife_db774b98", "position": {"x": 1.09886408, "y": 0.9110021, "z": 0.6057565}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_20771abe", "position": {"x": 3.02811241, "y": 0.177958727, "z": 0.489106685}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_7a7d333f", "position": {"x": -0.6002, "y": 0.902399957, "z": -1.0901}, "rotation": {"x": 0.0, "y": 134.999832, "z": 0.0}}, {"objectName": "Spatula_64c860f2", "position": {"x": 0.5336, "y": 0.9259599, "z": 0.6273035}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Spatula_64c860f2", "position": {"x": -0.909206867, "y": 0.7217773, "z": 0.926687658}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_0fe289e3", "position": {"x": -0.123990819, "y": 0.707411051, "z": -1.25940931}, "rotation": {"x": 0.0, "y": 135.0, "z": 0.0}}, {"objectName": "Fork_12a59a81", "position": {"x": 2.98839331, "y": 0.713133156, "z": 0.8290036}, "rotation": {"x": 0.0, "y": 180.000046, "z": 0.0}}, {"objectName": "Mug_ba5c7501", "position": {"x": 1.16424751, "y": 0.8810964, "z": -1.50391865}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_ba5c7501", "position": {"x": 3.04919958, "y": 0.966699958, "z": 0.591800451}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Apple_1e1bef89", "position": {"x": -0.6552614, "y": 0.9741304, "z": -0.5712531}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Apple_1e1bef89", "position": {"x": -1.05215728, "y": 1.07148921, "z": 1.41790485}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_c39806f6", "position": {"x": 3.02804923, "y": 0.172024786, "z": -0.5017505}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_c39806f6", "position": {"x": -0.6592519, "y": 0.703051865, "z": -0.681551933}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "PepperShaker_6d2fd9f8", "position": {"x": 3.19845176, "y": 0.172026992, "z": -0.6608123}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_6d2fd9f8", "position": {"x": -0.249650389, "y": 1.25991642, "z": -1.485105}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Bowl_46f611f6", "position": {"x": 0.798875034, "y": 0.9052153, "z": 0.7275939}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_46f611f6", "position": {"x": 3.031842, "y": 1.46167791, "z": -1.18682539}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c285ee2", "position": {"x": 2.98707986, "y": 0.7998927, "z": -0.100778744}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_be22aed5", "position": {"x": -0.8946521, "y": 1.25624526, "z": -0.8351037}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Cup_be22aed5", "position": {"x": -1.01872778, "y": 0.9954912, "z": 1.2588}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_bf5eb4b5", "position": {"x": 0.8366609, "y": 0.921021, "z": 0.410926}, "rotation": {"x": 0.0, "y": 36.8105049, "z": 0.0}}, {"objectName": "Plate_bf5eb4b5", "position": {"x": 3.08444834, "y": 0.190163016, "z": 0.7706443}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_20771abe", "position": {"x": -1.08090544, "y": 0.9010167, "z": -0.047219038}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_5121dd32", "position": {"x": 3.08485031, "y": 0.184823871, "z": -0.8700396}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_ed324e47", "position": {"x": -0.7796327, "y": 0.93876, "z": -0.7497666}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Fork_12a59a81", "position": {"x": -0.8683, "y": 0.70712316, "z": 1.34340417}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_ff5a3b19", "position": {"x": 0.8088945, "y": 1.71181929, "z": -1.55020607}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_7a7d333f", "position": {"x": -0.86270237, "y": 0.9004437, "z": 0.399594069}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_449c396f", "position": {"x": -0.192771435, "y": 0.901341259, "z": -1.54293}, "rotation": {"x": 0.0, "y": 315.0, "z": 0.0}}, {"objectName": "Spatula_64c860f2", "position": {"x": 3.12221074, "y": 0.915799856, "z": -0.5990837}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_3e58a77d", "position": {"x": -0.717233658, "y": 0.925918341, "z": 0.623000562}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_99ab2e9c", "position": {"x": 3.12221074, "y": 0.966390967, "z": -0.79824847}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_be22aed5", "position": {"x": 1.16424751, "y": 1.0713098, "z": -1.50391865}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_c39806f6", "position": {"x": -0.186272085, "y": 1.25991642, "z": -1.54848325}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Egg_7c285ee2", "position": {"x": 0.8309021, "y": 0.9634951, "z": 0.171530485}, "rotation": {"x": 0.0, "y": 36.8105049, "z": 0.0}}, {"objectName": "PepperShaker_6d2fd9f8", "position": {"x": -0.8683, "y": 0.7030836, "z": -0.36138764}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_db774b98", "position": {"x": -0.601119339, "y": 0.900342047, "z": -0.625395238}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Apple_1e1bef89", "position": {"x": 1.22986388, "y": 0.9819304, "z": 0.8892696}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_000ae861", "position": {"x": 0.341, "y": 0.8731569, "z": -1.649}, "rotation": {"x": 0.0, "y": 167.065247, "z": 0.0}}, {"objectName": "Bread_1e8ab5c8", "position": {"x": -0.00216567516, "y": 0.9791737, "z": -1.63085854}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "CellPhone_bf099a5c", "position": {"x": 3.07020664, "y": 0.7099818, "z": -0.8238139}, "rotation": {"x": 0.0, "y": 180.000046, "z": 0.0}}, {"objectName": "DishSponge_3cec4766", "position": {"x": 0.415472031, "y": 0.8998642, "z": -1.44522238}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_bfd17fb4", "position": {"x": 1.0861243, "y": 0.7482796, "z": -1.35491741}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_0fe289e3", "position": {"x": 3.07, "y": 0.7541666, "z": 0.0987239555}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_46f611f6", "position": {"x": 1.22986388, "y": 0.9080754, "z": 0.6057565}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_ba5c7501", "position": {"x": 0.929876268, "y": 1.06847739, "z": -1.53184986}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1161414120, "scene_num": 30}, "task_id": "trial_T20190909_130447_271507", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AM2KK02JXXW48_3C2NJ6JBKD87LY9V061GED6JDYFN2X", "high_descs": ["Turn right, head to the other side of the island on the right, turn right to face the egg on the island.", "Pick up the egg on the island. ", "Bring the to the microwave on the counter to the left.", "Heat the egg in the microwave. ", "Bring the heated egg to the left, to the counter with the toaster on it.", "Put the egg on the counter, right of the toaster. "], "task_desc": "Put a heated egg on the counter near the toaster. ", "votes": [1, 1, 1]}, {"assignment_id": "A1MKCTVKE7J0ZP_3YJ6NA41JE77UFJGN6K0HJXVFO3PJJ", "high_descs": ["Turn right and move forward, then turn right and stop at the fridge, then turn right to face the kitchen island.", "Pick up the egg from the kitchen island.", "Go around the kitchen island to go to the microwave across the room.", "Open the microwave, put the egg inside and heat it, then take it out.", "Turn left, move forward, then turn right to face the counter with the toaster on it.", "Put the egg on the counter."], "task_desc": "Put a warm egg on the counter.", "votes": [1, 1, 1]}, {"assignment_id": "A20FCMWP43CVIU_3MYYFCXHJ6YBEK7WHG79H8UKW0P4G2", "high_descs": ["walk to face kitchen island", "pick up egg from island", "walk to face microwave", "heat and remove egg from microwave", "walk to face counter with to<PERSON>", "put egg on counter to the right of toaster"], "task_desc": "put heated egg on counter top", "votes": [1, 0, 1]}]}}