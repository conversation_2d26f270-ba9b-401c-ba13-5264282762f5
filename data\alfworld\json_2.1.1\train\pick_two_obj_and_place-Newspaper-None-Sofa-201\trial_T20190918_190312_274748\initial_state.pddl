
(define (problem plan_trial_T20190918_190312_274748)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Book_bar__minus_01_dot_87_bar__plus_00_dot_68_bar__plus_01_dot_17 - object
        Bowl_bar__minus_01_dot_97_bar__plus_00_dot_75_bar__minus_00_dot_12 - object
        Bowl_bar__minus_02_dot_40_bar__plus_00_dot_10_bar__plus_04_dot_73 - object
        Box_bar__minus_01_dot_60_bar__plus_00_dot_89_bar__plus_01_dot_67 - object
        Box_bar__minus_01_dot_61_bar__plus_00_dot_68_bar__plus_03_dot_35 - object
        Chair_bar__minus_01_dot_42_bar__minus_00_dot_01_bar__plus_01_dot_43 - object
        Chair_bar__minus_01_dot_86_bar_00_dot_00_bar__plus_01_dot_14 - object
        Chair_bar__minus_01_dot_87_bar__minus_00_dot_01_bar__plus_01_dot_75 - object
        Chair_bar__minus_02_dot_48_bar__plus_00_dot_00_bar__plus_01_dot_75 - object
        Chair_bar__minus_02_dot_49_bar__minus_00_dot_01_bar__plus_01_dot_11 - object
        Chair_bar__minus_03_dot_03_bar__minus_00_dot_01_bar__plus_01_dot_40 - object
        CreditCard_bar__minus_00_dot_32_bar__plus_00_dot_71_bar__plus_03_dot_29 - object
        CreditCard_bar__minus_02_dot_16_bar__plus_00_dot_10_bar__plus_04_dot_73 - object
        Curtains_bar__minus_00_dot_08_bar__plus_02_dot_42_bar__plus_00_dot_97 - object
        FloorLamp_bar__minus_00_dot_57_bar__plus_00_dot_00_bar__plus_00_dot_03 - object
        HousePlant_bar__minus_03_dot_02_bar__plus_00_dot_59_bar__minus_00_dot_02 - object
        KeyChain_bar__minus_02_dot_39_bar__plus_00_dot_24_bar__plus_06_dot_16 - object
        KeyChain_bar__minus_04_dot_21_bar__plus_00_dot_43_bar__plus_05_dot_65 - object
        Lamp_bar__minus_00_dot_28_bar__plus_00_dot_70_bar__plus_03_dot_61 - object
        Laptop_bar__minus_02_dot_14_bar__plus_00_dot_48_bar__plus_03_dot_62 - object
        Laptop_bar__minus_02_dot_27_bar__plus_00_dot_69_bar__plus_01_dot_18 - object
        Laptop_bar__minus_04_dot_33_bar__plus_00_dot_45_bar__plus_05_dot_88 - object
        LightSwitch_bar__minus_03_dot_31_bar__plus_01_dot_34_bar__minus_00_dot_32 - object
        Newspaper_bar__minus_02_dot_47_bar__plus_00_dot_60_bar__plus_04_dot_73 - object
        Newspaper_bar__minus_02_dot_64_bar__plus_00_dot_24_bar__plus_06_dot_05 - object
        Painting_bar__minus_02_dot_05_bar__plus_01_dot_62_bar__minus_00_dot_30 - object
        Pencil_bar__minus_00_dot_25_bar__plus_00_dot_71_bar__plus_03_dot_12 - object
        Pen_bar__minus_02_dot_32_bar__plus_00_dot_35_bar__plus_04_dot_81 - object
        Pillow_bar__minus_02_dot_67_bar__plus_00_dot_56_bar__plus_03_dot_62 - object
        Pillow_bar__minus_03_dot_27_bar__plus_00_dot_66_bar__plus_03_dot_44 - object
        Plate_bar__minus_02_dot_25_bar__plus_00_dot_75_bar__minus_00_dot_07 - object
        Plate_bar__minus_02_dot_40_bar__plus_00_dot_68_bar__plus_01_dot_68 - object
        RemoteControl_bar__minus_02_dot_16_bar__plus_00_dot_10_bar__plus_05_dot_04 - object
        RemoteControl_bar__minus_02_dot_26_bar__plus_00_dot_24_bar__plus_06_dot_12 - object
        RemoteControl_bar__minus_02_dot_67_bar__plus_00_dot_48_bar__plus_03_dot_35 - object
        Statue_bar__minus_01_dot_76_bar__plus_00_dot_74_bar__minus_00_dot_13 - object
        Statue_bar__minus_02_dot_53_bar__plus_00_dot_75_bar__minus_00_dot_12 - object
        Television_bar__minus_02_dot_36_bar__plus_01_dot_21_bar__plus_06_dot_28 - object
        TissueBox_bar__minus_01_dot_60_bar__plus_00_dot_69_bar__plus_01_dot_30 - object
        TissueBox_bar__minus_02_dot_01_bar__plus_00_dot_24_bar__plus_06_dot_08 - object
        TissueBox_bar__minus_02_dot_49_bar__plus_00_dot_69_bar__plus_01_dot_42 - object
        Vase_bar__minus_00_dot_27_bar__plus_00_dot_70_bar__plus_03_dot_41 - object
        Watch_bar__minus_02_dot_09_bar__plus_00_dot_60_bar__plus_05_dot_04 - object
        Window_bar__minus_00_dot_06_bar__plus_00_dot_93_bar__plus_01_dot_76 - object
        Window_bar__minus_00_dot_06_bar__plus_01_dot_13_bar__plus_04_dot_89 - object
        ArmChair_bar__minus_00_dot_78_bar__plus_00_dot_01_bar__plus_06_dot_08 - receptacle
        ArmChair_bar__minus_04_dot_41_bar__plus_00_dot_01_bar__plus_06_dot_01 - receptacle
        CoffeeTable_bar__minus_02_dot_32_bar__plus_00_dot_00_bar__plus_04_dot_89 - receptacle
        DiningTable_bar__minus_02_dot_27_bar__minus_00_dot_02_bar__plus_01_dot_42 - receptacle
        DiningTable_bar__minus_03_dot_20_bar__plus_00_dot_59_bar__plus_00_dot_21 - receptacle
        Drawer_bar__minus_00_dot_30_bar__plus_00_dot_62_bar__plus_03_dot_37 - receptacle
        Drawer_bar__minus_02_dot_11_bar__plus_00_dot_66_bar__minus_00_dot_07 - receptacle
        GarbageCan_bar__minus_04_dot_87_bar__plus_00_dot_00_bar__plus_00_dot_27 - receptacle
        SideTable_bar__minus_00_dot_24_bar__plus_00_dot_00_bar__plus_03_dot_37 - receptacle
        SideTable_bar__minus_02_dot_11_bar__plus_00_dot_01_bar__minus_00_dot_14 - receptacle
        Sofa_bar__minus_02_dot_40_bar__minus_00_dot_07_bar__plus_03_dot_42 - receptacle
        TVStand_bar__minus_02_dot_39_bar__plus_00_dot_00_bar__plus_06_dot_30 - receptacle
        loc_bar__minus_17_bar_20_bar_0_bar_60 - location
        loc_bar__minus_2_bar_20_bar_1_bar_45 - location
        loc_bar__minus_10_bar_22_bar_0_bar_60 - location
        loc_bar__minus_8_bar_2_bar_2_bar_60 - location
        loc_bar__minus_12_bar_2_bar_1_bar_45 - location
        loc_bar__minus_10_bar_2_bar_0_bar_60 - location
        loc_bar__minus_6_bar_9_bar_2_bar_60 - location
        loc_bar__minus_16_bar_2_bar_1_bar_60 - location
        loc_bar__minus_9_bar_17_bar_2_bar_60 - location
        loc_bar__minus_18_bar_2_bar_3_bar_60 - location
        loc_bar__minus_8_bar_2_bar_2_bar_0 - location
        loc_bar__minus_15_bar_6_bar_1_bar_60 - location
        loc_bar__minus_14_bar_1_bar_2_bar_30 - location
        loc_bar__minus_3_bar_6_bar_3_bar_60 - location
        loc_bar__minus_14_bar_18_bar_1_bar_60 - location
        loc_bar__minus_4_bar_20_bar_0_bar_60 - location
        loc_bar__minus_3_bar_9_bar_0_bar_60 - location
        loc_bar__minus_4_bar_17_bar_2_bar_45 - location
        loc_bar__minus_2_bar_7_bar_1_bar_60 - location
        loc_bar__minus_10_bar_10_bar_2_bar_60 - location
        loc_bar__minus_7_bar_2_bar_0_bar_60 - location
        loc_bar__minus_5_bar_1_bar_1_bar_60 - location
        loc_bar__minus_12_bar_23_bar_0_bar_30 - location
        )
    

(:init


        (receptacleType TVStand_bar__minus_02_dot_39_bar__plus_00_dot_00_bar__plus_06_dot_30 TVStandType)
        (receptacleType ArmChair_bar__minus_00_dot_78_bar__plus_00_dot_01_bar__plus_06_dot_08 ArmChairType)
        (receptacleType DiningTable_bar__minus_03_dot_20_bar__plus_00_dot_59_bar__plus_00_dot_21 DiningTableType)
        (receptacleType ArmChair_bar__minus_04_dot_41_bar__plus_00_dot_01_bar__plus_06_dot_01 ArmChairType)
        (receptacleType Drawer_bar__minus_00_dot_30_bar__plus_00_dot_62_bar__plus_03_dot_37 DrawerType)
        (receptacleType Sofa_bar__minus_02_dot_40_bar__minus_00_dot_07_bar__plus_03_dot_42 SofaType)
        (receptacleType CoffeeTable_bar__minus_02_dot_32_bar__plus_00_dot_00_bar__plus_04_dot_89 CoffeeTableType)
        (receptacleType SideTable_bar__minus_00_dot_24_bar__plus_00_dot_00_bar__plus_03_dot_37 SideTableType)
        (receptacleType GarbageCan_bar__minus_04_dot_87_bar__plus_00_dot_00_bar__plus_00_dot_27 GarbageCanType)
        (receptacleType DiningTable_bar__minus_02_dot_27_bar__minus_00_dot_02_bar__plus_01_dot_42 DiningTableType)
        (receptacleType SideTable_bar__minus_02_dot_11_bar__plus_00_dot_01_bar__minus_00_dot_14 SideTableType)
        (receptacleType Drawer_bar__minus_02_dot_11_bar__plus_00_dot_66_bar__minus_00_dot_07 DrawerType)
        (objectType Laptop_bar__minus_04_dot_33_bar__plus_00_dot_45_bar__plus_05_dot_88 LaptopType)
        (objectType Chair_bar__minus_01_dot_87_bar__minus_00_dot_01_bar__plus_01_dot_75 ChairType)
        (objectType Statue_bar__minus_01_dot_76_bar__plus_00_dot_74_bar__minus_00_dot_13 StatueType)
        (objectType TissueBox_bar__minus_01_dot_60_bar__plus_00_dot_69_bar__plus_01_dot_30 TissueBoxType)
        (objectType Chair_bar__minus_01_dot_42_bar__minus_00_dot_01_bar__plus_01_dot_43 ChairType)
        (objectType Vase_bar__minus_00_dot_27_bar__plus_00_dot_70_bar__plus_03_dot_41 VaseType)
        (objectType Chair_bar__minus_03_dot_03_bar__minus_00_dot_01_bar__plus_01_dot_40 ChairType)
        (objectType Pencil_bar__minus_00_dot_25_bar__plus_00_dot_71_bar__plus_03_dot_12 PencilType)
        (objectType KeyChain_bar__minus_02_dot_39_bar__plus_00_dot_24_bar__plus_06_dot_16 KeyChainType)
        (objectType RemoteControl_bar__minus_02_dot_67_bar__plus_00_dot_48_bar__plus_03_dot_35 RemoteControlType)
        (objectType Watch_bar__minus_02_dot_09_bar__plus_00_dot_60_bar__plus_05_dot_04 WatchType)
        (objectType Bowl_bar__minus_02_dot_40_bar__plus_00_dot_10_bar__plus_04_dot_73 BowlType)
        (objectType Chair_bar__minus_01_dot_86_bar_00_dot_00_bar__plus_01_dot_14 ChairType)
        (objectType Bowl_bar__minus_01_dot_97_bar__plus_00_dot_75_bar__minus_00_dot_12 BowlType)
        (objectType KeyChain_bar__minus_04_dot_21_bar__plus_00_dot_43_bar__plus_05_dot_65 KeyChainType)
        (objectType Chair_bar__minus_02_dot_48_bar__plus_00_dot_00_bar__plus_01_dot_75 ChairType)
        (objectType CreditCard_bar__minus_00_dot_32_bar__plus_00_dot_71_bar__plus_03_dot_29 CreditCardType)
        (objectType Window_bar__minus_00_dot_06_bar__plus_00_dot_93_bar__plus_01_dot_76 WindowType)
        (objectType Pillow_bar__minus_02_dot_67_bar__plus_00_dot_56_bar__plus_03_dot_62 PillowType)
        (objectType Window_bar__minus_00_dot_06_bar__plus_01_dot_13_bar__plus_04_dot_89 WindowType)
        (objectType Chair_bar__minus_02_dot_49_bar__minus_00_dot_01_bar__plus_01_dot_11 ChairType)
        (objectType FloorLamp_bar__minus_00_dot_57_bar__plus_00_dot_00_bar__plus_00_dot_03 FloorLampType)
        (objectType Box_bar__minus_01_dot_60_bar__plus_00_dot_89_bar__plus_01_dot_67 BoxType)
        (objectType Television_bar__minus_02_dot_36_bar__plus_01_dot_21_bar__plus_06_dot_28 TelevisionType)
        (objectType TissueBox_bar__minus_02_dot_01_bar__plus_00_dot_24_bar__plus_06_dot_08 TissueBoxType)
        (objectType LightSwitch_bar__minus_03_dot_31_bar__plus_01_dot_34_bar__minus_00_dot_32 LightSwitchType)
        (objectType Newspaper_bar__minus_02_dot_47_bar__plus_00_dot_60_bar__plus_04_dot_73 NewspaperType)
        (objectType CreditCard_bar__minus_02_dot_16_bar__plus_00_dot_10_bar__plus_04_dot_73 CreditCardType)
        (objectType Book_bar__minus_01_dot_87_bar__plus_00_dot_68_bar__plus_01_dot_17 BookType)
        (objectType Plate_bar__minus_02_dot_25_bar__plus_00_dot_75_bar__minus_00_dot_07 PlateType)
        (objectType Laptop_bar__minus_02_dot_14_bar__plus_00_dot_48_bar__plus_03_dot_62 LaptopType)
        (objectType Pen_bar__minus_02_dot_32_bar__plus_00_dot_35_bar__plus_04_dot_81 PenType)
        (objectType Laptop_bar__minus_02_dot_27_bar__plus_00_dot_69_bar__plus_01_dot_18 LaptopType)
        (objectType Pillow_bar__minus_03_dot_27_bar__plus_00_dot_66_bar__plus_03_dot_44 PillowType)
        (objectType RemoteControl_bar__minus_02_dot_16_bar__plus_00_dot_10_bar__plus_05_dot_04 RemoteControlType)
        (objectType Plate_bar__minus_02_dot_40_bar__plus_00_dot_68_bar__plus_01_dot_68 PlateType)
        (objectType RemoteControl_bar__minus_02_dot_26_bar__plus_00_dot_24_bar__plus_06_dot_12 RemoteControlType)
        (objectType Curtains_bar__minus_00_dot_08_bar__plus_02_dot_42_bar__plus_00_dot_97 CurtainsType)
        (objectType HousePlant_bar__minus_03_dot_02_bar__plus_00_dot_59_bar__minus_00_dot_02 HousePlantType)
        (objectType Newspaper_bar__minus_02_dot_64_bar__plus_00_dot_24_bar__plus_06_dot_05 NewspaperType)
        (objectType Statue_bar__minus_02_dot_53_bar__plus_00_dot_75_bar__minus_00_dot_12 StatueType)
        (objectType Box_bar__minus_01_dot_61_bar__plus_00_dot_68_bar__plus_03_dot_35 BoxType)
        (objectType TissueBox_bar__minus_02_dot_49_bar__plus_00_dot_69_bar__plus_01_dot_42 TissueBoxType)
        (objectType Painting_bar__minus_02_dot_05_bar__plus_01_dot_62_bar__minus_00_dot_30 PaintingType)
        (canContain TVStandType TissueBoxType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType BookType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType NewspaperType)
        (canContain ArmChairType CreditCardType)
        (canContain DiningTableType PenType)
        (canContain DiningTableType BookType)
        (canContain DiningTableType WatchType)
        (canContain DiningTableType NewspaperType)
        (canContain DiningTableType BowlType)
        (canContain DiningTableType VaseType)
        (canContain DiningTableType BoxType)
        (canContain DiningTableType KeyChainType)
        (canContain DiningTableType CreditCardType)
        (canContain DiningTableType LaptopType)
        (canContain DiningTableType PencilType)
        (canContain DiningTableType PlateType)
        (canContain DiningTableType RemoteControlType)
        (canContain DiningTableType TissueBoxType)
        (canContain DiningTableType StatueType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType BookType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType NewspaperType)
        (canContain ArmChairType CreditCardType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType TissueBoxType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType BookType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType NewspaperType)
        (canContain SofaType CreditCardType)
        (canContain CoffeeTableType PenType)
        (canContain CoffeeTableType BookType)
        (canContain CoffeeTableType WatchType)
        (canContain CoffeeTableType NewspaperType)
        (canContain CoffeeTableType BowlType)
        (canContain CoffeeTableType VaseType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType PencilType)
        (canContain CoffeeTableType PlateType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType TissueBoxType)
        (canContain CoffeeTableType StatueType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType WatchType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType BowlType)
        (canContain SideTableType VaseType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType PlateType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType TissueBoxType)
        (canContain SideTableType StatueType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType NewspaperType)
        (canContain GarbageCanType PencilType)
        (canContain GarbageCanType TissueBoxType)
        (canContain DiningTableType PenType)
        (canContain DiningTableType BookType)
        (canContain DiningTableType WatchType)
        (canContain DiningTableType NewspaperType)
        (canContain DiningTableType BowlType)
        (canContain DiningTableType VaseType)
        (canContain DiningTableType BoxType)
        (canContain DiningTableType KeyChainType)
        (canContain DiningTableType CreditCardType)
        (canContain DiningTableType LaptopType)
        (canContain DiningTableType PencilType)
        (canContain DiningTableType PlateType)
        (canContain DiningTableType RemoteControlType)
        (canContain DiningTableType TissueBoxType)
        (canContain DiningTableType StatueType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType WatchType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType BowlType)
        (canContain SideTableType VaseType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType PlateType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType TissueBoxType)
        (canContain SideTableType StatueType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType TissueBoxType)
        (pickupable Laptop_bar__minus_04_dot_33_bar__plus_00_dot_45_bar__plus_05_dot_88)
        (pickupable Statue_bar__minus_01_dot_76_bar__plus_00_dot_74_bar__minus_00_dot_13)
        (pickupable TissueBox_bar__minus_01_dot_60_bar__plus_00_dot_69_bar__plus_01_dot_30)
        (pickupable Vase_bar__minus_00_dot_27_bar__plus_00_dot_70_bar__plus_03_dot_41)
        (pickupable Pencil_bar__minus_00_dot_25_bar__plus_00_dot_71_bar__plus_03_dot_12)
        (pickupable KeyChain_bar__minus_02_dot_39_bar__plus_00_dot_24_bar__plus_06_dot_16)
        (pickupable RemoteControl_bar__minus_02_dot_67_bar__plus_00_dot_48_bar__plus_03_dot_35)
        (pickupable Watch_bar__minus_02_dot_09_bar__plus_00_dot_60_bar__plus_05_dot_04)
        (pickupable Bowl_bar__minus_02_dot_40_bar__plus_00_dot_10_bar__plus_04_dot_73)
        (pickupable Bowl_bar__minus_01_dot_97_bar__plus_00_dot_75_bar__minus_00_dot_12)
        (pickupable KeyChain_bar__minus_04_dot_21_bar__plus_00_dot_43_bar__plus_05_dot_65)
        (pickupable CreditCard_bar__minus_00_dot_32_bar__plus_00_dot_71_bar__plus_03_dot_29)
        (pickupable Pillow_bar__minus_02_dot_67_bar__plus_00_dot_56_bar__plus_03_dot_62)
        (pickupable Box_bar__minus_01_dot_60_bar__plus_00_dot_89_bar__plus_01_dot_67)
        (pickupable TissueBox_bar__minus_02_dot_01_bar__plus_00_dot_24_bar__plus_06_dot_08)
        (pickupable Newspaper_bar__minus_02_dot_47_bar__plus_00_dot_60_bar__plus_04_dot_73)
        (pickupable CreditCard_bar__minus_02_dot_16_bar__plus_00_dot_10_bar__plus_04_dot_73)
        (pickupable Book_bar__minus_01_dot_87_bar__plus_00_dot_68_bar__plus_01_dot_17)
        (pickupable Plate_bar__minus_02_dot_25_bar__plus_00_dot_75_bar__minus_00_dot_07)
        (pickupable Laptop_bar__minus_02_dot_14_bar__plus_00_dot_48_bar__plus_03_dot_62)
        (pickupable Pen_bar__minus_02_dot_32_bar__plus_00_dot_35_bar__plus_04_dot_81)
        (pickupable Laptop_bar__minus_02_dot_27_bar__plus_00_dot_69_bar__plus_01_dot_18)
        (pickupable Pillow_bar__minus_03_dot_27_bar__plus_00_dot_66_bar__plus_03_dot_44)
        (pickupable RemoteControl_bar__minus_02_dot_16_bar__plus_00_dot_10_bar__plus_05_dot_04)
        (pickupable Plate_bar__minus_02_dot_40_bar__plus_00_dot_68_bar__plus_01_dot_68)
        (pickupable RemoteControl_bar__minus_02_dot_26_bar__plus_00_dot_24_bar__plus_06_dot_12)
        (pickupable Newspaper_bar__minus_02_dot_64_bar__plus_00_dot_24_bar__plus_06_dot_05)
        (pickupable Statue_bar__minus_02_dot_53_bar__plus_00_dot_75_bar__minus_00_dot_12)
        (pickupable Box_bar__minus_01_dot_61_bar__plus_00_dot_68_bar__plus_03_dot_35)
        (pickupable TissueBox_bar__minus_02_dot_49_bar__plus_00_dot_69_bar__plus_01_dot_42)
        (isReceptacleObject Bowl_bar__minus_02_dot_40_bar__plus_00_dot_10_bar__plus_04_dot_73)
        (isReceptacleObject Bowl_bar__minus_01_dot_97_bar__plus_00_dot_75_bar__minus_00_dot_12)
        (isReceptacleObject Box_bar__minus_01_dot_60_bar__plus_00_dot_89_bar__plus_01_dot_67)
        (isReceptacleObject Plate_bar__minus_02_dot_25_bar__plus_00_dot_75_bar__minus_00_dot_07)
        (isReceptacleObject Plate_bar__minus_02_dot_40_bar__plus_00_dot_68_bar__plus_01_dot_68)
        (isReceptacleObject Box_bar__minus_01_dot_61_bar__plus_00_dot_68_bar__plus_03_dot_35)
        (openable Drawer_bar__minus_00_dot_30_bar__plus_00_dot_62_bar__plus_03_dot_37)
        (openable Drawer_bar__minus_02_dot_11_bar__plus_00_dot_66_bar__minus_00_dot_07)
        
        (atLocation agent1 loc_bar__minus_12_bar_23_bar_0_bar_30)
        
        (cleanable Bowl_bar__minus_02_dot_40_bar__plus_00_dot_10_bar__plus_04_dot_73)
        (cleanable Bowl_bar__minus_01_dot_97_bar__plus_00_dot_75_bar__minus_00_dot_12)
        (cleanable Plate_bar__minus_02_dot_25_bar__plus_00_dot_75_bar__minus_00_dot_07)
        (cleanable Plate_bar__minus_02_dot_40_bar__plus_00_dot_68_bar__plus_01_dot_68)
        
        (heatable Plate_bar__minus_02_dot_25_bar__plus_00_dot_75_bar__minus_00_dot_07)
        (heatable Plate_bar__minus_02_dot_40_bar__plus_00_dot_68_bar__plus_01_dot_68)
        (coolable Bowl_bar__minus_02_dot_40_bar__plus_00_dot_10_bar__plus_04_dot_73)
        (coolable Bowl_bar__minus_01_dot_97_bar__plus_00_dot_75_bar__minus_00_dot_12)
        (coolable Plate_bar__minus_02_dot_25_bar__plus_00_dot_75_bar__minus_00_dot_07)
        (coolable Plate_bar__minus_02_dot_40_bar__plus_00_dot_68_bar__plus_01_dot_68)
        
        
        (toggleable FloorLamp_bar__minus_00_dot_57_bar__plus_00_dot_00_bar__plus_00_dot_03)
        
        
        
        
        (inReceptacle HousePlant_bar__minus_03_dot_02_bar__plus_00_dot_59_bar__minus_00_dot_02 DiningTable_bar__minus_03_dot_20_bar__plus_00_dot_59_bar__plus_00_dot_21)
        (inReceptacle TissueBox_bar__minus_02_dot_01_bar__plus_00_dot_24_bar__plus_06_dot_08 TVStand_bar__minus_02_dot_39_bar__plus_00_dot_00_bar__plus_06_dot_30)
        (inReceptacle RemoteControl_bar__minus_02_dot_26_bar__plus_00_dot_24_bar__plus_06_dot_12 TVStand_bar__minus_02_dot_39_bar__plus_00_dot_00_bar__plus_06_dot_30)
        (inReceptacle Newspaper_bar__minus_02_dot_64_bar__plus_00_dot_24_bar__plus_06_dot_05 TVStand_bar__minus_02_dot_39_bar__plus_00_dot_00_bar__plus_06_dot_30)
        (inReceptacle KeyChain_bar__minus_02_dot_39_bar__plus_00_dot_24_bar__plus_06_dot_16 TVStand_bar__minus_02_dot_39_bar__plus_00_dot_00_bar__plus_06_dot_30)
        (inReceptacle Television_bar__minus_02_dot_36_bar__plus_01_dot_21_bar__plus_06_dot_28 TVStand_bar__minus_02_dot_39_bar__plus_00_dot_00_bar__plus_06_dot_30)
        (inReceptacle RemoteControl_bar__minus_02_dot_16_bar__plus_00_dot_10_bar__plus_05_dot_04 CoffeeTable_bar__minus_02_dot_32_bar__plus_00_dot_00_bar__plus_04_dot_89)
        (inReceptacle Newspaper_bar__minus_02_dot_47_bar__plus_00_dot_60_bar__plus_04_dot_73 CoffeeTable_bar__minus_02_dot_32_bar__plus_00_dot_00_bar__plus_04_dot_89)
        (inReceptacle CreditCard_bar__minus_02_dot_16_bar__plus_00_dot_10_bar__plus_04_dot_73 CoffeeTable_bar__minus_02_dot_32_bar__plus_00_dot_00_bar__plus_04_dot_89)
        (inReceptacle Pen_bar__minus_02_dot_32_bar__plus_00_dot_35_bar__plus_04_dot_81 CoffeeTable_bar__minus_02_dot_32_bar__plus_00_dot_00_bar__plus_04_dot_89)
        (inReceptacle Watch_bar__minus_02_dot_09_bar__plus_00_dot_60_bar__plus_05_dot_04 CoffeeTable_bar__minus_02_dot_32_bar__plus_00_dot_00_bar__plus_04_dot_89)
        (inReceptacle Bowl_bar__minus_02_dot_40_bar__plus_00_dot_10_bar__plus_04_dot_73 CoffeeTable_bar__minus_02_dot_32_bar__plus_00_dot_00_bar__plus_04_dot_89)
        (inReceptacle Laptop_bar__minus_04_dot_33_bar__plus_00_dot_45_bar__plus_05_dot_88 ArmChair_bar__minus_04_dot_41_bar__plus_00_dot_01_bar__plus_06_dot_01)
        (inReceptacle KeyChain_bar__minus_04_dot_21_bar__plus_00_dot_43_bar__plus_05_dot_65 ArmChair_bar__minus_04_dot_41_bar__plus_00_dot_01_bar__plus_06_dot_01)
        (inReceptacle Statue_bar__minus_01_dot_76_bar__plus_00_dot_74_bar__minus_00_dot_13 SideTable_bar__minus_02_dot_11_bar__plus_00_dot_01_bar__minus_00_dot_14)
        (inReceptacle Plate_bar__minus_02_dot_25_bar__plus_00_dot_75_bar__minus_00_dot_07 SideTable_bar__minus_02_dot_11_bar__plus_00_dot_01_bar__minus_00_dot_14)
        (inReceptacle Bowl_bar__minus_01_dot_97_bar__plus_00_dot_75_bar__minus_00_dot_12 SideTable_bar__minus_02_dot_11_bar__plus_00_dot_01_bar__minus_00_dot_14)
        (inReceptacle Statue_bar__minus_02_dot_53_bar__plus_00_dot_75_bar__minus_00_dot_12 SideTable_bar__minus_02_dot_11_bar__plus_00_dot_01_bar__minus_00_dot_14)
        (inReceptacle Pencil_bar__minus_00_dot_25_bar__plus_00_dot_71_bar__plus_03_dot_12 SideTable_bar__minus_00_dot_24_bar__plus_00_dot_00_bar__plus_03_dot_37)
        (inReceptacle Vase_bar__minus_00_dot_27_bar__plus_00_dot_70_bar__plus_03_dot_41 SideTable_bar__minus_00_dot_24_bar__plus_00_dot_00_bar__plus_03_dot_37)
        (inReceptacle Curtains_bar__minus_00_dot_08_bar__plus_02_dot_42_bar__plus_00_dot_97 SideTable_bar__minus_00_dot_24_bar__plus_00_dot_00_bar__plus_03_dot_37)
        (inReceptacle CreditCard_bar__minus_00_dot_32_bar__plus_00_dot_71_bar__plus_03_dot_29 SideTable_bar__minus_00_dot_24_bar__plus_00_dot_00_bar__plus_03_dot_37)
        (inReceptacle Pillow_bar__minus_02_dot_67_bar__plus_00_dot_56_bar__plus_03_dot_62 Sofa_bar__minus_02_dot_40_bar__minus_00_dot_07_bar__plus_03_dot_42)
        (inReceptacle Laptop_bar__minus_02_dot_14_bar__plus_00_dot_48_bar__plus_03_dot_62 Sofa_bar__minus_02_dot_40_bar__minus_00_dot_07_bar__plus_03_dot_42)
        (inReceptacle RemoteControl_bar__minus_02_dot_67_bar__plus_00_dot_48_bar__plus_03_dot_35 Sofa_bar__minus_02_dot_40_bar__minus_00_dot_07_bar__plus_03_dot_42)
        (inReceptacle Pillow_bar__minus_03_dot_27_bar__plus_00_dot_66_bar__plus_03_dot_44 Sofa_bar__minus_02_dot_40_bar__minus_00_dot_07_bar__plus_03_dot_42)
        (inReceptacle Box_bar__minus_01_dot_61_bar__plus_00_dot_68_bar__plus_03_dot_35 Sofa_bar__minus_02_dot_40_bar__minus_00_dot_07_bar__plus_03_dot_42)
        (inReceptacle Box_bar__minus_01_dot_60_bar__plus_00_dot_89_bar__plus_01_dot_67 DiningTable_bar__minus_02_dot_27_bar__minus_00_dot_02_bar__plus_01_dot_42)
        (inReceptacle Plate_bar__minus_02_dot_40_bar__plus_00_dot_68_bar__plus_01_dot_68 DiningTable_bar__minus_02_dot_27_bar__minus_00_dot_02_bar__plus_01_dot_42)
        (inReceptacle Book_bar__minus_01_dot_87_bar__plus_00_dot_68_bar__plus_01_dot_17 DiningTable_bar__minus_02_dot_27_bar__minus_00_dot_02_bar__plus_01_dot_42)
        (inReceptacle TissueBox_bar__minus_01_dot_60_bar__plus_00_dot_69_bar__plus_01_dot_30 DiningTable_bar__minus_02_dot_27_bar__minus_00_dot_02_bar__plus_01_dot_42)
        (inReceptacle Laptop_bar__minus_02_dot_27_bar__plus_00_dot_69_bar__plus_01_dot_18 DiningTable_bar__minus_02_dot_27_bar__minus_00_dot_02_bar__plus_01_dot_42)
        (inReceptacle TissueBox_bar__minus_02_dot_49_bar__plus_00_dot_69_bar__plus_01_dot_42 DiningTable_bar__minus_02_dot_27_bar__minus_00_dot_02_bar__plus_01_dot_42)
        
        
        (receptacleAtLocation ArmChair_bar__minus_00_dot_78_bar__plus_00_dot_01_bar__plus_06_dot_08 loc_bar__minus_4_bar_20_bar_0_bar_60)
        (receptacleAtLocation ArmChair_bar__minus_04_dot_41_bar__plus_00_dot_01_bar__plus_06_dot_01 loc_bar__minus_17_bar_20_bar_0_bar_60)
        (receptacleAtLocation CoffeeTable_bar__minus_02_dot_32_bar__plus_00_dot_00_bar__plus_04_dot_89 loc_bar__minus_14_bar_18_bar_1_bar_60)
        (receptacleAtLocation DiningTable_bar__minus_02_dot_27_bar__minus_00_dot_02_bar__plus_01_dot_42 loc_bar__minus_10_bar_10_bar_2_bar_60)
        (receptacleAtLocation DiningTable_bar__minus_03_dot_20_bar__plus_00_dot_59_bar__plus_00_dot_21 loc_bar__minus_16_bar_2_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__minus_00_dot_30_bar__plus_00_dot_62_bar__plus_03_dot_37 loc_bar__minus_4_bar_17_bar_2_bar_45)
        (receptacleAtLocation Drawer_bar__minus_02_dot_11_bar__plus_00_dot_66_bar__minus_00_dot_07 loc_bar__minus_12_bar_2_bar_1_bar_45)
        (receptacleAtLocation GarbageCan_bar__minus_04_dot_87_bar__plus_00_dot_00_bar__plus_00_dot_27 loc_bar__minus_18_bar_2_bar_3_bar_60)
        (receptacleAtLocation SideTable_bar__minus_00_dot_24_bar__plus_00_dot_00_bar__plus_03_dot_37 loc_bar__minus_3_bar_9_bar_0_bar_60)
        (receptacleAtLocation SideTable_bar__minus_02_dot_11_bar__plus_00_dot_01_bar__minus_00_dot_14 loc_bar__minus_8_bar_2_bar_2_bar_60)
        (receptacleAtLocation Sofa_bar__minus_02_dot_40_bar__minus_00_dot_07_bar__plus_03_dot_42 loc_bar__minus_9_bar_17_bar_2_bar_60)
        (receptacleAtLocation TVStand_bar__minus_02_dot_39_bar__plus_00_dot_00_bar__plus_06_dot_30 loc_bar__minus_10_bar_22_bar_0_bar_60)
        (objectAtLocation Newspaper_bar__minus_02_dot_47_bar__plus_00_dot_60_bar__plus_04_dot_73 loc_bar__minus_14_bar_18_bar_1_bar_60)
        (objectAtLocation RemoteControl_bar__minus_02_dot_16_bar__plus_00_dot_10_bar__plus_05_dot_04 loc_bar__minus_14_bar_18_bar_1_bar_60)
        (objectAtLocation Bowl_bar__minus_01_dot_97_bar__plus_00_dot_75_bar__minus_00_dot_12 loc_bar__minus_8_bar_2_bar_2_bar_60)
        (objectAtLocation Pillow_bar__minus_03_dot_27_bar__plus_00_dot_66_bar__plus_03_dot_44 loc_bar__minus_9_bar_17_bar_2_bar_60)
        (objectAtLocation KeyChain_bar__minus_04_dot_21_bar__plus_00_dot_43_bar__plus_05_dot_65 loc_bar__minus_17_bar_20_bar_0_bar_60)
        (objectAtLocation Plate_bar__minus_02_dot_40_bar__plus_00_dot_68_bar__plus_01_dot_68 loc_bar__minus_10_bar_10_bar_2_bar_60)
        (objectAtLocation CreditCard_bar__minus_02_dot_16_bar__plus_00_dot_10_bar__plus_04_dot_73 loc_bar__minus_14_bar_18_bar_1_bar_60)
        (objectAtLocation TissueBox_bar__minus_02_dot_49_bar__plus_00_dot_69_bar__plus_01_dot_42 loc_bar__minus_10_bar_10_bar_2_bar_60)
        (objectAtLocation Laptop_bar__minus_02_dot_14_bar__plus_00_dot_48_bar__plus_03_dot_62 loc_bar__minus_9_bar_17_bar_2_bar_60)
        (objectAtLocation Statue_bar__minus_01_dot_76_bar__plus_00_dot_74_bar__minus_00_dot_13 loc_bar__minus_8_bar_2_bar_2_bar_60)
        (objectAtLocation Box_bar__minus_01_dot_61_bar__plus_00_dot_68_bar__plus_03_dot_35 loc_bar__minus_9_bar_17_bar_2_bar_60)
        (objectAtLocation Laptop_bar__minus_04_dot_33_bar__plus_00_dot_45_bar__plus_05_dot_88 loc_bar__minus_17_bar_20_bar_0_bar_60)
        (objectAtLocation RemoteControl_bar__minus_02_dot_26_bar__plus_00_dot_24_bar__plus_06_dot_12 loc_bar__minus_10_bar_22_bar_0_bar_60)
        (objectAtLocation TissueBox_bar__minus_01_dot_60_bar__plus_00_dot_69_bar__plus_01_dot_30 loc_bar__minus_10_bar_10_bar_2_bar_60)
        (objectAtLocation Painting_bar__minus_02_dot_05_bar__plus_01_dot_62_bar__minus_00_dot_30 loc_bar__minus_8_bar_2_bar_2_bar_0)
        (objectAtLocation Chair_bar__minus_01_dot_87_bar__minus_00_dot_01_bar__plus_01_dot_75 loc_bar__minus_6_bar_9_bar_2_bar_60)
        (objectAtLocation Book_bar__minus_01_dot_87_bar__plus_00_dot_68_bar__plus_01_dot_17 loc_bar__minus_10_bar_10_bar_2_bar_60)
        (objectAtLocation Chair_bar__minus_02_dot_48_bar__plus_00_dot_00_bar__plus_01_dot_75 loc_bar__minus_10_bar_10_bar_2_bar_60)
        (objectAtLocation Box_bar__minus_01_dot_60_bar__plus_00_dot_89_bar__plus_01_dot_67 loc_bar__minus_10_bar_10_bar_2_bar_60)
        (objectAtLocation Chair_bar__minus_01_dot_42_bar__minus_00_dot_01_bar__plus_01_dot_43 loc_bar__minus_3_bar_6_bar_3_bar_60)
        (objectAtLocation Chair_bar__minus_03_dot_03_bar__minus_00_dot_01_bar__plus_01_dot_40 loc_bar__minus_15_bar_6_bar_1_bar_60)
        (objectAtLocation Chair_bar__minus_01_dot_86_bar_00_dot_00_bar__plus_01_dot_14 loc_bar__minus_7_bar_2_bar_0_bar_60)
        (objectAtLocation Curtains_bar__minus_00_dot_08_bar__plus_02_dot_42_bar__plus_00_dot_97 loc_bar__minus_3_bar_9_bar_0_bar_60)
        (objectAtLocation Statue_bar__minus_02_dot_53_bar__plus_00_dot_75_bar__minus_00_dot_12 loc_bar__minus_8_bar_2_bar_2_bar_60)
        (objectAtLocation Laptop_bar__minus_02_dot_27_bar__plus_00_dot_69_bar__plus_01_dot_18 loc_bar__minus_10_bar_10_bar_2_bar_60)
        (objectAtLocation Television_bar__minus_02_dot_36_bar__plus_01_dot_21_bar__plus_06_dot_28 loc_bar__minus_10_bar_22_bar_0_bar_60)
        (objectAtLocation HousePlant_bar__minus_03_dot_02_bar__plus_00_dot_59_bar__minus_00_dot_02 loc_bar__minus_16_bar_2_bar_1_bar_60)
        (objectAtLocation TissueBox_bar__minus_02_dot_01_bar__plus_00_dot_24_bar__plus_06_dot_08 loc_bar__minus_10_bar_22_bar_0_bar_60)
        (objectAtLocation CreditCard_bar__minus_00_dot_32_bar__plus_00_dot_71_bar__plus_03_dot_29 loc_bar__minus_3_bar_9_bar_0_bar_60)
        (objectAtLocation Plate_bar__minus_02_dot_25_bar__plus_00_dot_75_bar__minus_00_dot_07 loc_bar__minus_8_bar_2_bar_2_bar_60)
        (objectAtLocation KeyChain_bar__minus_02_dot_39_bar__plus_00_dot_24_bar__plus_06_dot_16 loc_bar__minus_10_bar_22_bar_0_bar_60)
        (objectAtLocation FloorLamp_bar__minus_00_dot_57_bar__plus_00_dot_00_bar__plus_00_dot_03 loc_bar__minus_5_bar_1_bar_1_bar_60)
        (objectAtLocation Vase_bar__minus_00_dot_27_bar__plus_00_dot_70_bar__plus_03_dot_41 loc_bar__minus_3_bar_9_bar_0_bar_60)
        (objectAtLocation Chair_bar__minus_02_dot_49_bar__minus_00_dot_01_bar__plus_01_dot_11 loc_bar__minus_10_bar_2_bar_0_bar_60)
        (objectAtLocation Pencil_bar__minus_00_dot_25_bar__plus_00_dot_71_bar__plus_03_dot_12 loc_bar__minus_3_bar_9_bar_0_bar_60)
        (objectAtLocation Pillow_bar__minus_02_dot_67_bar__plus_00_dot_56_bar__plus_03_dot_62 loc_bar__minus_9_bar_17_bar_2_bar_60)
        (objectAtLocation Bowl_bar__minus_02_dot_40_bar__plus_00_dot_10_bar__plus_04_dot_73 loc_bar__minus_14_bar_18_bar_1_bar_60)
        (objectAtLocation RemoteControl_bar__minus_02_dot_67_bar__plus_00_dot_48_bar__plus_03_dot_35 loc_bar__minus_9_bar_17_bar_2_bar_60)
        (objectAtLocation Watch_bar__minus_02_dot_09_bar__plus_00_dot_60_bar__plus_05_dot_04 loc_bar__minus_14_bar_18_bar_1_bar_60)
        (objectAtLocation Pen_bar__minus_02_dot_32_bar__plus_00_dot_35_bar__plus_04_dot_81 loc_bar__minus_14_bar_18_bar_1_bar_60)
        (objectAtLocation Newspaper_bar__minus_02_dot_64_bar__plus_00_dot_24_bar__plus_06_dot_05 loc_bar__minus_10_bar_22_bar_0_bar_60)
        (objectAtLocation Window_bar__minus_00_dot_06_bar__plus_00_dot_93_bar__plus_01_dot_76 loc_bar__minus_2_bar_7_bar_1_bar_60)
        (objectAtLocation Window_bar__minus_00_dot_06_bar__plus_01_dot_13_bar__plus_04_dot_89 loc_bar__minus_2_bar_20_bar_1_bar_45)
        (objectAtLocation LightSwitch_bar__minus_03_dot_31_bar__plus_01_dot_34_bar__minus_00_dot_32 loc_bar__minus_14_bar_1_bar_2_bar_30)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 NewspaperType)
                                    (receptacleType ?r SofaType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 NewspaperType)
                                            (receptacleType ?r SofaType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            