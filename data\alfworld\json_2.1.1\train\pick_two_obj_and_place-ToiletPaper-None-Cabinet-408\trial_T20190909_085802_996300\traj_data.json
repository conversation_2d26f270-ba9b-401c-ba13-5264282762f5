{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000207.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000208.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000209.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000210.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000211.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000212.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000261.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000262.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000263.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000264.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000265.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000266.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000267.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000268.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000269.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000270.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000271.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000272.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000273.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000274.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000275.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000277.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000278.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000279.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000280.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000281.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000282.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 39}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "ToiletPaper", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["toiletpaperhanger"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-6|0|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [-5.664, -5.664, 2.6615996, 2.6615996, 3.2648, 3.2648]], "coordinateReceptacleObjectId": ["ToiletPaperHanger", [-5.94, -5.94, 2.768, 2.768, 3.22, 3.22]], "forceVisible": true, "objectId": "ToiletPaper|-01.42|+00.82|+00.67"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-10|-2|0|60"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "cabinet"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [-5.664, -5.664, 2.6615996, 2.6615996, 3.2648, 3.2648]], "coordinateReceptacleObjectId": ["Cabinet", [-12.37948324, -12.37948324, 1.685427664, 1.685427664, 1.5627072, 1.5627072]], "forceVisible": true, "objectId": "ToiletPaper|-01.42|+00.82|+00.67", "receptacleObjectId": "Cabinet|-03.09|+00.39|+00.42"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-10|-5|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [-11.96416856, -11.96416856, -6.33844996, -6.33844996, 0.7190428, 0.7190428]], "coordinateReceptacleObjectId": ["GarbageCan", [-11.696, -11.696, -6.24, -6.24, 0.00419187544, 0.00419187544]], "forceVisible": true, "objectId": "ToiletPaper|-02.99|+00.18|-01.58"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-10|-2|0|60"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "cabinet"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [-11.96416856, -11.96416856, -6.33844996, -6.33844996, 0.7190428, 0.7190428]], "coordinateReceptacleObjectId": ["Cabinet", [-12.37948324, -12.37948324, 1.685427664, 1.685427664, 1.5627072, 1.5627072]], "forceVisible": true, "objectId": "ToiletPaper|-02.99|+00.18|-01.58", "receptacleObjectId": "Cabinet|-03.09|+00.39|+00.42"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|-01.42|+00.82|+00.67"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [162, 160, 187, 201], "mask": [[47868, 10], [48165, 15], [48463, 18], [48762, 21], [49062, 22], [49362, 22], [49662, 22], [49962, 23], [50262, 23], [50562, 23], [50862, 23], [51163, 22], [51463, 22], [51763, 22], [52063, 23], [52363, 23], [52663, 23], [52963, 23], [53263, 23], [53563, 23], [53863, 23], [54163, 23], [54463, 24], [54763, 24], [55063, 24], [55363, 24], [55663, 24], [55963, 24], [56263, 24], [56563, 24], [56863, 25], [57163, 25], [57463, 8], [57477, 11], [57764, 6], [57777, 11], [58064, 6], [58077, 11], [58364, 6], [58377, 10], [58664, 6], [58677, 9], [58964, 6], [58977, 9], [59264, 6], [59277, 8], [59564, 6], [59578, 6], [59865, 4], [59878, 3], [60167, 2]], "point": [174, 179]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-03.09|+00.39|+00.42"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [22, 70, 118, 145], "mask": [[20722, 82], [21022, 83], [21322, 83], [21622, 83], [21922, 83], [22223, 82], [22523, 83], [22824, 82], [23125, 81], [23425, 81], [23726, 80], [24026, 81], [24327, 80], [24627, 80], [24928, 79], [25228, 79], [25529, 79], [25830, 78], [26130, 78], [26431, 77], [26731, 77], [27032, 76], [27332, 77], [27633, 76], [27934, 75], [28234, 75], [28535, 74], [28835, 75], [29136, 74], [29436, 74], [29737, 73], [30037, 73], [30338, 73], [30639, 72], [30939, 72], [31240, 71], [31540, 71], [31841, 71], [32141, 71], [32442, 70], [32743, 69], [33043, 69], [33344, 69], [33644, 69], [33945, 68], [34245, 68], [34546, 67], [34847, 67], [35147, 67], [35448, 66], [35748, 66], [36049, 65], [36349, 66], [36650, 65], [36950, 65], [37251, 64], [37552, 63], [37852, 64], [38153, 63], [38453, 63], [38754, 62], [39054, 62], [39355, 62], [39656, 61], [39956, 61], [40257, 60], [40557, 60], [40858, 60], [41158, 60], [41459, 59], [41759, 59], [42060, 58], [42361, 58], [42661, 58], [42962, 57], [43262, 57]], "point": [70, 106]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|-01.42|+00.82|+00.67", "placeStationary": true, "receptacleObjectId": "Cabinet|-03.09|+00.39|+00.42"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 72, 116, 198], "mask": [[21320, 5], [21327, 75], [21620, 5], [21627, 76], [21919, 7], [21928, 75], [22219, 7], [22228, 75], [22519, 8], [22529, 74], [22818, 9], [22829, 74], [23118, 10], [23130, 74], [23417, 12], [23430, 74], [23717, 12], [23731, 73], [24017, 13], [24031, 73], [24316, 14], [24332, 72], [24616, 15], [24632, 73], [24916, 15], [24933, 72], [25215, 17], [25234, 71], [25515, 17], [25534, 71], [25815, 18], [25835, 70], [26114, 19], [26135, 71], [26414, 20], [26436, 70], [26714, 21], [26736, 70], [27013, 22], [27037, 69], [27313, 23], [27337, 69], [27613, 23], [27638, 69], [27912, 25], [27938, 69], [28212, 25], [28239, 68], [28512, 26], [28539, 68], [28811, 27], [28840, 68], [29111, 28], [29141, 67], [29410, 29], [29441, 67], [29710, 30], [29742, 66], [30010, 31], [30042, 66], [30309, 32], [30343, 66], [30609, 33], [30643, 66], [30909, 33], [30944, 65], [31208, 35], [31244, 65], [31508, 35], [31545, 64], [31808, 36], [31845, 65], [32107, 37], [32146, 64], [32407, 38], [32446, 64], [32707, 38], [32747, 63], [33006, 40], [33048, 62], [33306, 41], [33348, 63], [33606, 41], [33649, 37], [33687, 24], [33905, 43], [33949, 35], [33988, 23], [34205, 43], [34250, 33], [34289, 22], [34505, 44], [34550, 32], [34589, 22], [34804, 45], [34851, 31], [34890, 22], [35104, 46], [35151, 21], [35177, 5], [35191, 21], [35403, 47], [35452, 18], [35478, 4], [35491, 21], [35703, 48], [35752, 17], [35780, 2], [35791, 21], [36003, 49], [36053, 15], [36091, 22], [36302, 50], [36354, 14], [36391, 22], [36602, 51], [36654, 16], [36691, 22], [36902, 51], [36955, 16], [36990, 23], [37201, 53], [37255, 17], [37290, 23], [37501, 53], [37556, 17], [37591, 23], [37801, 54], [37856, 13], [37892, 22], [38100, 55], [38157, 10], [38192, 22], [38400, 56], [38457, 10], [38493, 21], [38700, 56], [38758, 9], [38793, 21], [39000, 57], [39058, 9], [39092, 23], [39300, 58], [39359, 8], [39391, 24], [39600, 58], [39659, 9], [39690, 25], [39900, 59], [39960, 9], [39989, 26], [40200, 59], [40261, 8], [40288, 27], [40500, 60], [40561, 9], [40579, 2], [40587, 29], [40800, 60], [40862, 9], [40872, 10], [40886, 30], [41100, 61], [41162, 21], [41185, 31], [41400, 61], [41463, 53], [41700, 62], [41763, 53], [42000, 62], [42064, 53], [42300, 63], [42364, 53], [42600, 64], [42900, 64], [43200, 64], [43501, 63], [43801, 63], [44102, 62], [44403, 61], [44704, 60], [45005, 58], [45306, 57], [45607, 56], [45908, 55], [46209, 54], [46510, 52], [46811, 51], [47112, 50], [47413, 49], [47714, 48], [48015, 46], [48316, 45], [48617, 44], [48918, 43], [49219, 42], [49520, 40], [49821, 39], [50122, 38], [50423, 37], [50724, 36], [51025, 34], [51326, 33], [51627, 32], [51928, 31], [52229, 30], [52530, 29], [52830, 28], [53131, 27], [53432, 26], [53733, 25], [54034, 24], [54335, 22], [54636, 21], [54937, 20], [55238, 19], [55539, 18], [55840, 16], [56141, 15], [56442, 14], [56743, 13], [57044, 12], [57345, 10], [57646, 9], [57947, 8], [58248, 7], [58549, 6], [58850, 4], [59151, 3]], "point": [58, 134]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-03.09|+00.39|+00.42"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 116, 198], "mask": [[21320, 5], [21327, 75], [21620, 5], [21627, 76], [21919, 7], [21928, 75], [22219, 7], [22228, 75], [22519, 8], [22529, 74], [22818, 9], [22829, 74], [23118, 10], [23130, 74], [23417, 12], [23430, 74], [23717, 12], [23731, 73], [24017, 13], [24031, 73], [24316, 14], [24332, 72], [24616, 15], [24632, 73], [24916, 15], [24933, 72], [25215, 17], [25234, 71], [25515, 17], [25534, 71], [25815, 18], [25835, 70], [26114, 19], [26135, 71], [26414, 20], [26436, 70], [26714, 21], [26736, 70], [27013, 22], [27037, 69], [27313, 23], [27337, 69], [27613, 23], [27638, 69], [27912, 25], [27938, 69], [28212, 25], [28239, 68], [28512, 26], [28539, 68], [28811, 27], [28840, 68], [29111, 28], [29141, 67], [29410, 29], [29441, 67], [29710, 30], [29742, 66], [30010, 31], [30042, 66], [30309, 32], [30343, 66], [30609, 33], [30643, 66], [30909, 33], [30944, 65], [31208, 35], [31244, 65], [31508, 35], [31545, 64], [31808, 36], [31845, 65], [32107, 37], [32146, 64], [32407, 38], [32446, 64], [32707, 38], [32747, 63], [33006, 40], [33048, 62], [33306, 41], [33348, 63], [33606, 41], [33649, 37], [33687, 24], [33905, 43], [33949, 35], [33988, 23], [34205, 43], [34250, 33], [34289, 22], [34505, 44], [34550, 32], [34589, 22], [34804, 45], [34851, 31], [34890, 12], [34904, 8], [35104, 46], [35151, 21], [35177, 5], [35191, 7], [35207, 5], [35403, 47], [35452, 18], [35478, 4], [35491, 6], [35508, 4], [35703, 48], [35752, 17], [35780, 2], [35791, 5], [35809, 3], [36003, 49], [36053, 15], [36091, 4], [36109, 4], [36302, 50], [36354, 14], [36391, 4], [36410, 3], [36602, 51], [36654, 16], [36691, 3], [36710, 3], [36902, 51], [36955, 16], [36990, 4], [37010, 3], [37201, 53], [37255, 17], [37290, 4], [37311, 2], [37501, 53], [37556, 17], [37591, 4], [37611, 3], [37801, 54], [37856, 13], [37892, 3], [37911, 3], [38100, 55], [38157, 10], [38192, 3], [38211, 3], [38400, 56], [38457, 10], [38493, 2], [38512, 2], [38700, 56], [38758, 9], [38793, 3], [38812, 2], [39000, 57], [39058, 9], [39092, 4], [39112, 3], [39300, 58], [39359, 8], [39391, 5], [39412, 3], [39600, 58], [39659, 9], [39690, 7], [39712, 3], [39900, 59], [39960, 9], [39989, 8], [40012, 3], [40200, 59], [40261, 8], [40288, 9], [40312, 3], [40500, 60], [40561, 9], [40579, 2], [40587, 11], [40611, 5], [40800, 60], [40862, 9], [40872, 10], [40886, 13], [40910, 6], [41100, 61], [41162, 21], [41185, 14], [41209, 7], [41400, 61], [41463, 39], [41506, 10], [41700, 62], [41763, 53], [42000, 62], [42064, 53], [42300, 63], [42364, 53], [42600, 64], [42900, 64], [43200, 64], [43501, 63], [43801, 63], [44102, 62], [44403, 61], [44704, 60], [45005, 58], [45306, 57], [45607, 56], [45908, 55], [46209, 54], [46510, 52], [46811, 51], [47112, 50], [47413, 49], [47714, 48], [48015, 46], [48316, 45], [48617, 44], [48918, 43], [49219, 42], [49520, 40], [49821, 39], [50122, 38], [50423, 37], [50724, 36], [51025, 34], [51326, 33], [51627, 32], [51928, 31], [52229, 30], [52530, 29], [52830, 28], [53131, 27], [53432, 26], [53733, 25], [54034, 24], [54335, 22], [54636, 21], [54937, 20], [55238, 19], [55539, 18], [55840, 16], [56141, 15], [56442, 14], [56743, 13], [57044, 12], [57345, 10], [57646, 9], [57947, 8], [58248, 7], [58549, 6], [58850, 4], [59151, 3]], "point": [58, 134]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|-02.99|+00.18|-01.58"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [71, 190, 91, 201], "mask": [[56774, 1], [57072, 5], [57371, 9], [57671, 11], [57971, 14], [58271, 17], [58571, 19], [58874, 18], [59177, 10], [59190, 2], [59479, 8], [59490, 2], [59782, 5], [59790, 2], [60085, 2]], "point": [81, 194]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-03.09|+00.39|+00.42"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [22, 70, 118, 145], "mask": [[20722, 82], [21022, 83], [21322, 83], [21622, 83], [21922, 83], [22223, 82], [22523, 83], [22824, 82], [23125, 81], [23425, 81], [23726, 80], [24026, 81], [24327, 80], [24627, 80], [24928, 79], [25228, 79], [25529, 79], [25830, 78], [26130, 78], [26431, 77], [26731, 77], [27032, 76], [27332, 77], [27633, 76], [27934, 75], [28234, 75], [28535, 74], [28835, 75], [29136, 74], [29436, 74], [29737, 73], [30037, 73], [30338, 73], [30639, 72], [30939, 72], [31240, 71], [31540, 71], [31841, 71], [32141, 71], [32442, 70], [32743, 69], [33043, 69], [33344, 69], [33644, 69], [33945, 68], [34245, 68], [34546, 67], [34847, 67], [35147, 67], [35448, 66], [35748, 66], [36049, 65], [36349, 66], [36650, 65], [36950, 65], [37251, 64], [37552, 63], [37852, 64], [38153, 63], [38453, 63], [38754, 62], [39054, 62], [39355, 62], [39656, 61], [39956, 61], [40257, 60], [40557, 60], [40858, 60], [41158, 60], [41459, 59], [41759, 59], [42060, 58], [42361, 58], [42661, 58], [42962, 57], [43262, 57]], "point": [70, 106]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|-02.99|+00.18|-01.58", "placeStationary": true, "receptacleObjectId": "Cabinet|-03.09|+00.39|+00.42"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 72, 116, 198], "mask": [[21320, 5], [21327, 75], [21620, 5], [21627, 76], [21919, 7], [21928, 75], [22219, 7], [22228, 75], [22519, 8], [22529, 74], [22818, 9], [22829, 74], [23118, 10], [23130, 74], [23417, 12], [23430, 74], [23717, 12], [23731, 73], [24017, 13], [24031, 73], [24316, 14], [24332, 72], [24616, 15], [24632, 73], [24916, 15], [24933, 72], [25215, 17], [25234, 71], [25515, 17], [25534, 71], [25815, 18], [25835, 70], [26114, 19], [26135, 71], [26414, 20], [26436, 70], [26714, 21], [26736, 70], [27013, 22], [27037, 69], [27313, 23], [27337, 69], [27613, 23], [27638, 69], [27912, 25], [27938, 69], [28212, 25], [28239, 68], [28512, 26], [28539, 68], [28811, 27], [28840, 68], [29111, 28], [29141, 67], [29410, 29], [29441, 67], [29710, 30], [29742, 66], [30010, 31], [30042, 66], [30309, 32], [30343, 66], [30609, 33], [30643, 66], [30909, 33], [30944, 65], [31208, 35], [31244, 65], [31508, 35], [31545, 64], [31808, 36], [31845, 65], [32107, 37], [32146, 64], [32407, 38], [32446, 64], [32707, 38], [32747, 63], [33006, 40], [33048, 62], [33306, 41], [33348, 63], [33606, 41], [33649, 37], [33687, 24], [33905, 43], [33949, 35], [33988, 23], [34205, 43], [34250, 33], [34289, 22], [34505, 44], [34550, 32], [34589, 22], [34804, 45], [34851, 31], [34890, 12], [34904, 8], [35104, 46], [35151, 21], [35177, 5], [35191, 7], [35207, 5], [35403, 47], [35452, 18], [35478, 4], [35491, 6], [35508, 4], [35703, 48], [35752, 17], [35780, 2], [35791, 5], [35809, 3], [36003, 49], [36053, 15], [36091, 4], [36109, 4], [36302, 50], [36354, 14], [36391, 4], [36410, 3], [36602, 51], [36654, 16], [36691, 3], [36710, 3], [36902, 51], [36955, 16], [36990, 4], [37010, 3], [37201, 53], [37255, 17], [37290, 4], [37311, 2], [37501, 53], [37556, 17], [37591, 4], [37611, 3], [37801, 54], [37856, 13], [37892, 3], [37911, 3], [38100, 55], [38157, 10], [38192, 3], [38211, 3], [38400, 56], [38457, 10], [38493, 2], [38512, 2], [38700, 56], [38758, 9], [38793, 3], [38812, 2], [39000, 57], [39058, 9], [39092, 4], [39112, 3], [39300, 58], [39359, 8], [39391, 5], [39412, 3], [39600, 58], [39659, 9], [39690, 7], [39712, 3], [39900, 59], [39960, 9], [39989, 8], [40012, 3], [40200, 59], [40261, 8], [40288, 9], [40312, 3], [40500, 60], [40561, 9], [40579, 2], [40587, 11], [40611, 5], [40800, 60], [40862, 9], [40872, 10], [40886, 13], [40910, 6], [41100, 61], [41162, 21], [41185, 14], [41209, 7], [41400, 61], [41463, 39], [41506, 10], [41700, 62], [41763, 53], [42000, 62], [42064, 53], [42300, 63], [42364, 53], [42600, 64], [42900, 64], [43200, 64], [43501, 63], [43801, 63], [44102, 62], [44403, 61], [44704, 60], [45005, 58], [45306, 57], [45607, 56], [45908, 55], [46209, 54], [46510, 52], [46811, 51], [47112, 50], [47413, 49], [47714, 48], [48015, 46], [48316, 45], [48617, 44], [48918, 43], [49219, 42], [49520, 40], [49821, 39], [50122, 38], [50423, 37], [50724, 36], [51025, 34], [51326, 33], [51627, 32], [51928, 31], [52229, 30], [52530, 29], [52830, 28], [53131, 27], [53432, 26], [53733, 25], [54034, 24], [54335, 22], [54636, 21], [54937, 20], [55238, 19], [55539, 18], [55840, 16], [56141, 15], [56442, 14], [56743, 13], [57044, 12], [57345, 10], [57646, 9], [57947, 8], [58248, 7], [58549, 6], [58850, 4], [59151, 3]], "point": [58, 134]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-03.09|+00.39|+00.42"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 116, 198], "mask": [[21320, 5], [21327, 75], [21620, 5], [21627, 76], [21919, 7], [21928, 75], [22219, 7], [22228, 75], [22519, 8], [22529, 74], [22818, 9], [22829, 74], [23118, 10], [23130, 74], [23417, 12], [23430, 74], [23717, 12], [23731, 73], [24017, 13], [24031, 73], [24316, 14], [24332, 72], [24616, 15], [24632, 73], [24916, 15], [24933, 72], [25215, 17], [25234, 71], [25515, 17], [25534, 71], [25815, 18], [25835, 70], [26114, 19], [26135, 71], [26414, 20], [26436, 70], [26714, 21], [26736, 70], [27013, 22], [27037, 69], [27313, 23], [27337, 69], [27613, 23], [27638, 69], [27912, 25], [27938, 69], [28212, 25], [28239, 68], [28512, 26], [28539, 68], [28811, 27], [28840, 68], [29111, 28], [29141, 67], [29410, 29], [29441, 62], [29507, 1], [29710, 30], [29742, 60], [29807, 1], [30010, 31], [30042, 60], [30107, 1], [30309, 32], [30343, 59], [30408, 1], [30609, 33], [30643, 60], [30708, 1], [30909, 33], [30944, 59], [31008, 1], [31208, 35], [31244, 59], [31308, 1], [31508, 35], [31545, 58], [31608, 1], [31808, 36], [31845, 59], [31909, 1], [32107, 37], [32146, 58], [32209, 1], [32407, 38], [32446, 58], [32509, 1], [32707, 38], [32747, 57], [32809, 1], [33006, 40], [33048, 57], [33109, 1], [33306, 41], [33348, 57], [33409, 2], [33606, 41], [33649, 37], [33687, 19], [33707, 4], [33905, 43], [33949, 35], [33988, 23], [34205, 43], [34250, 33], [34289, 22], [34505, 44], [34550, 32], [34589, 22], [34804, 45], [34851, 31], [34890, 12], [34904, 8], [35104, 46], [35151, 21], [35177, 5], [35191, 7], [35207, 5], [35403, 47], [35452, 18], [35478, 4], [35491, 6], [35508, 4], [35703, 48], [35752, 17], [35780, 2], [35791, 5], [35809, 3], [36003, 49], [36053, 15], [36091, 4], [36109, 4], [36302, 50], [36354, 14], [36391, 4], [36410, 3], [36602, 51], [36654, 16], [36691, 3], [36710, 3], [36902, 51], [36955, 16], [36990, 4], [37010, 3], [37201, 53], [37255, 17], [37290, 4], [37311, 2], [37501, 53], [37556, 17], [37591, 4], [37611, 3], [37801, 54], [37856, 13], [37892, 3], [37911, 3], [38100, 55], [38157, 10], [38192, 3], [38211, 3], [38400, 56], [38457, 10], [38493, 2], [38512, 2], [38700, 56], [38758, 9], [38793, 3], [38812, 2], [39000, 57], [39058, 9], [39092, 4], [39112, 3], [39300, 58], [39359, 8], [39391, 5], [39412, 3], [39600, 58], [39659, 9], [39690, 7], [39712, 3], [39900, 59], [39960, 9], [39989, 8], [40012, 3], [40200, 59], [40261, 8], [40288, 9], [40312, 3], [40500, 60], [40561, 9], [40579, 2], [40587, 11], [40611, 5], [40800, 60], [40862, 9], [40872, 10], [40886, 13], [40910, 6], [41100, 61], [41162, 21], [41185, 14], [41209, 7], [41400, 61], [41463, 39], [41506, 10], [41700, 62], [41763, 53], [42000, 62], [42064, 53], [42300, 63], [42364, 53], [42600, 64], [42900, 64], [43200, 64], [43501, 63], [43801, 63], [44102, 62], [44403, 61], [44704, 60], [45005, 58], [45306, 57], [45607, 56], [45908, 55], [46209, 54], [46510, 52], [46811, 51], [47112, 50], [47413, 49], [47714, 48], [48015, 46], [48316, 45], [48617, 44], [48918, 43], [49219, 42], [49520, 40], [49821, 39], [50122, 38], [50423, 37], [50724, 36], [51025, 34], [51326, 33], [51627, 32], [51928, 31], [52229, 30], [52530, 29], [52830, 28], [53131, 27], [53432, 26], [53733, 25], [54034, 24], [54335, 22], [54636, 21], [54937, 20], [55238, 19], [55539, 18], [55840, 16], [56141, 15], [56442, 14], [56743, 13], [57044, 12], [57345, 10], [57646, 9], [57947, 8], [58248, 7], [58549, 6], [58850, 4], [59151, 3]], "point": [58, 134]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan408", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -1.75, "y": 0.9020473, "z": -0.75}, "object_poses": [{"objectName": "Cloth_6a80aa54", "position": {"x": -2.99230146, "y": 0.08189297, "z": 0.567163}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cloth_6a80aa54", "position": {"x": -1.77720869, "y": 0.08189297, "z": 0.69622767}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBar_3fe03e51", "position": {"x": -2.823437, "y": 0.1828304, "z": -1.58461249}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBar_3fe03e51", "position": {"x": -0.9918691, "y": 1.04581511, "z": 0.937266469}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "SprayBottle_0d562139", "position": {"x": -2.890479, "y": 0.184164762, "z": -1.5353874}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_0d562139", "position": {"x": -0.99187, "y": 1.04714942, "z": 0.7840994}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Towel_b839ade7", "position": {"x": -0.490000516, "y": 1.3670001, "z": 0.9026544}, "rotation": {"x": 0.0, "y": 180.000259, "z": 0.0}}, {"objectName": "Candle_785b187e", "position": {"x": -2.45178366, "y": 0.122595191, "z": 0.889824569}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "HandTowel_f391da11", "position": {"x": -3.09641314, "y": 1.587, "z": 0.744}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBar_3fe03e51", "position": {"x": -0.188565314, "y": 0.5171323, "z": 0.178573072}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_3de2642c", "position": {"x": -1.416, "y": 0.8162, "z": 0.6653999}, "rotation": {"x": 0.0, "y": 90.00002, "z": 269.999969}}, {"objectName": "Cloth_6a80aa54", "position": {"x": -2.40701818, "y": 0.08189297, "z": 0.63169533}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "ToiletPaper_94faa429", "position": {"x": -2.99104214, "y": 0.1797607, "z": -1.58461249}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_0d562139", "position": {"x": -2.1386652, "y": 0.08258498, "z": 0.567163}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ScrubBrush_ca1847f9", "position": {"x": -0.4850965, "y": 0.00104796886, "z": 0.873910666}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_e993ea70", "position": {"x": -2.0939, "y": 0.0830844045, "z": 0.69622767}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plunger_6b4e4cec", "position": {"x": -0.710990548, "y": 0.000584982336, "z": 0.828180134}, "rotation": {"x": -0.00118286943, "y": 0.0004387156, "z": 0.0007826461}}], "object_toggles": [], "random_seed": 3906348219, "scene_num": 408}, "task_id": "trial_T20190909_085802_996300", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "AFU00NU09CFXE_3GNCZX450LEWTMGCT07NOKUCPSSAPP", "high_descs": ["Turn around and move to the sink.", "Remove the roll of toilet paper from the toilet paper holder.", "Move back from the vanity so you have space to open a cupboard.", "Open the leftmost cupboard next to the wall and place the roll of toilet paper inside.", "Turn around and move to the trash bin in the corner.", "Pick up the empty toilet paper roll from the trash bin.", "Turn around and move closer to the vanity again.", "Open the leftmost cupboard by the wall and place the empty toilet paper roll inside."], "task_desc": "Move two toilet paper rolls to a cupboard below the sink.", "votes": [1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_3VHP9MDGRQB8VOOGZB79IL6WNQ2CF4", "high_descs": ["turn around and walk forwards to the space in between the toilet and the bathroom sink cabinet", "grab the toilet paper roll off of the toilet paper holder", "back up a bit and move to the left to face the cabinet again", "place the toilet paper inside the cabinet to the bottom left of the left side sink", "turn around and walk over to the left side of the garbage bin in the corner of the room on the right", "grab the toilet paper roller tube out of the trash there", "turn right and walk over to the left side of the bathroom cabinet again", "place the tube inside of the bottom left cabinet where you put the toilet paper"], "task_desc": "put two toilet paper objects inside the bottom left cabinet of the left side sink", "votes": [1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3X73LLYYQ45B0OCDJR4N1HCL29WHNS", "high_descs": ["Walk over to the right side of the counter behind you.", "Pick up the toilet paper roll off of the toilet paper holder in front of you.", "Turn left and walk over to the wall, then turn left and walk forward and turn around to face the counter.", "Open the lower left cabinet door and put the toilet paper roll inside, then close the cabinet.", "Turn around and walk over to the wall, then turn right and look down at the small silver bin.", "Pick up the toilet paper roll out of the small silver bin.", "Turn right and walk over to the counter.", "Open the leftmost cabinet door below the counter and put the toilet paper roll inside, then close the cabinet."], "task_desc": "Move two toilet paper rolls into a cabinet.", "votes": [1, 1]}]}}