{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000256.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000258.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000259.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000260.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000261.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000262.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000263.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000264.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000265.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000266.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000267.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000268.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000269.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000270.png", "low_idx": 36}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "CounterTop", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-8|10|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-8.10551928, -8.10551928, 8.10603048, 8.10603048, 3.689438, 3.689438]], "coordinateReceptacleObjectId": ["CounterTop", [-7.4884, -7.4884, 7.2, 7.2, 3.836, 3.836]], "forceVisible": true, "objectId": "Cup|-02.03|+00.92|+02.03"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-11|10|2|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-7|10|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "countertop"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-8.10551928, -8.10551928, 8.10603048, 8.10603048, 3.689438, 3.689438]], "coordinateReceptacleObjectId": ["CounterTop", [-7.4884, -7.4884, 7.2, 7.2, 3.836, 3.836]], "forceVisible": true, "objectId": "Cup|-02.03|+00.92|+02.03", "receptacleObjectId": "CounterTop|-01.87|+00.96|+01.80"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-02.03|+00.92|+02.03"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [137, 83, 182, 134], "mask": [[24757, 5], [25051, 17], [25349, 21], [25647, 25], [25945, 29], [26243, 33], [26542, 35], [26841, 36], [27140, 38], [27440, 39], [27739, 41], [28038, 43], [28338, 43], [28638, 43], [28938, 44], [29237, 45], [29537, 45], [29837, 46], [30137, 45], [30437, 45], [30738, 44], [31038, 44], [31338, 44], [31638, 44], [31938, 43], [32239, 42], [32539, 41], [32839, 41], [33140, 40], [33440, 39], [33740, 39], [34040, 38], [34341, 37], [34642, 35], [34942, 34], [35243, 33], [35544, 31], [35844, 30], [36144, 30], [36445, 28], [36745, 28], [37045, 27], [37345, 27], [37645, 27], [37946, 26], [38246, 25], [38547, 23], [38848, 22], [39149, 20], [39450, 17], [39752, 13], [40055, 8]], "point": [159, 107]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-02.03|+00.92|+02.03", "placeStationary": true, "receptacleObjectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 125], [14226, 138], [14400, 109], [14544, 120], [14700, 106], [14849, 115], [15000, 105], [15150, 114], [15300, 105], [15450, 114], [15600, 106], [15750, 114], [15900, 106], [16050, 114], [16200, 106], [16350, 114], [16500, 106], [16650, 114], [16800, 106], [16949, 114], [17100, 107], [17249, 114], [17400, 107], [17549, 114], [17700, 107], [17849, 114], [18000, 107], [18149, 114], [18300, 107], [18449, 114], [18600, 107], [18749, 114], [18900, 108], [19048, 115], [19200, 108], [19348, 115], [19500, 108], [19648, 114], [19800, 109], [19947, 115], [20100, 109], [20247, 115], [20400, 110], [20546, 116], [20700, 110], [20846, 116], [21000, 111], [21145, 117], [21300, 111], [21445, 117], [21600, 111], [21745, 117], [21900, 111], [22044, 118], [22200, 112], [22344, 117], [22500, 112], [22644, 117], [22800, 112], [22944, 117], [23100, 112], [23244, 117], [23400, 112], [23544, 117], [23700, 113], [23843, 118], [24000, 113], [24143, 118], [24300, 113], [24443, 118], [24600, 113], [24743, 118], [24900, 113], [25043, 118], [25200, 114], [25343, 117], [25500, 114], [25643, 117], [25800, 114], [25943, 117], [26100, 114], [26243, 117], [26400, 114], [26543, 117], [26700, 114], [26843, 117], [27000, 114], [27143, 117], [27300, 124], [27431, 129], [27600, 260], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [145, 70]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-02.03|+00.92|+02.03"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [105, 48, 149, 92], "mask": [[14225, 1], [14509, 35], [14806, 43], [15105, 45], [15405, 45], [15706, 44], [16006, 44], [16306, 44], [16606, 44], [16906, 43], [17207, 42], [17507, 42], [17807, 42], [18107, 42], [18407, 42], [18707, 42], [19008, 40], [19308, 40], [19608, 40], [19909, 38], [20209, 38], [20510, 36], [20810, 36], [21111, 34], [21411, 34], [21711, 34], [22011, 33], [22312, 32], [22612, 32], [22912, 32], [23212, 32], [23512, 32], [23813, 30], [24113, 30], [24413, 30], [24713, 30], [25013, 30], [25314, 29], [25614, 29], [25914, 29], [26214, 29], [26514, 29], [26814, 29], [27114, 29], [27424, 7]], "point": [127, 69]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-02.03|+00.92|+02.03", "placeStationary": true, "receptacleObjectId": "CounterTop|-01.87|+00.96|+01.80"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [74, 99, 298, 212], "mask": [[29512, 4], [29522, 25], [29558, 55], [29618, 5], [29813, 2], [29859, 51], [29920, 2], [30114, 1], [30159, 49], [30459, 48], [30760, 46], [31060, 46], [31360, 45], [31660, 44], [31961, 43], [32261, 42], [32561, 41], [32861, 40], [33161, 39], [33461, 39], [33761, 39], [33852, 1], [34061, 38], [34152, 1], [34361, 38], [34452, 2], [34661, 38], [34751, 3], [34961, 39], [35051, 4], [35261, 39], [35350, 5], [35598, 2], [35650, 6], [35949, 7], [36249, 8], [36548, 9], [36847, 11], [37103, 2], [37113, 1], [37146, 12], [37360, 1], [37395, 13], [37411, 3], [37446, 13], [37660, 1], [37685, 29], [37745, 14], [37959, 3], [37970, 45], [38044, 15], [38257, 58], [38343, 17], [38552, 63], [38643, 17], [38820, 4], [38846, 70], [38942, 19], [39120, 96], [39241, 18], [39420, 96], [39540, 18], [39720, 97], [39839, 19], [40020, 96], [40138, 20], [40321, 94], [40438, 19], [40621, 57], [40693, 21], [40738, 18], [40892, 1], [40921, 55], [40995, 18], [41039, 16], [41192, 1], [41221, 53], [41298, 15], [41339, 15], [41491, 2], [41521, 50], [41600, 12], [41639, 15], [41791, 2], [41821, 50], [41901, 11], [41939, 14], [42091, 2], [42121, 49], [42202, 10], [42239, 14], [42391, 3], [42452, 17], [42503, 9], [42539, 13], [42691, 3], [42752, 16], [42805, 8], [42838, 14], [42865, 2], [42990, 4], [43022, 45], [43105, 10], [43137, 14], [43165, 2], [43290, 4], [43322, 45], [43406, 14], [43433, 17], [43465, 3], [43590, 4], [43622, 45], [43706, 44], [43765, 3], [43890, 5], [43922, 45], [44007, 42], [44064, 5], [44189, 6], [44222, 44], [44307, 42], [44364, 5], [44489, 6], [44522, 44], [44608, 40], [44664, 6], [44789, 6], [44822, 44], [44908, 40], [44964, 6], [45089, 6], [45122, 44], [45208, 39], [45263, 8], [45388, 8], [45422, 45], [45508, 39], [45563, 8], [45688, 8], [45722, 45], [45808, 38], [45863, 9], [45988, 8], [46023, 44], [46108, 38], [46162, 10], [46288, 8], [46323, 44], [46408, 38], [46462, 10], [46587, 10], [46622, 45], [46707, 39], [46762, 11], [46887, 10], [46922, 45], [47007, 39], [47062, 11], [47187, 10], [47222, 45], [47306, 41], [47361, 13], [47487, 10], [47521, 47], [47606, 42], [47661, 13], [47787, 11], [47820, 48], [47905, 44], [47961, 14], [48086, 13], [48119, 49], [48205, 46], [48259, 16], [48386, 14], [48418, 51], [48505, 50], [48556, 20], [48686, 15], [48716, 53], [48804, 72], [48986, 17], [49014, 55], [49103, 74], [49285, 85], [49403, 74], [49585, 86], [49702, 76], [49885, 86], [50001, 77], [50185, 86], [50301, 77], [50484, 87], [50600, 79], [50784, 87], [50899, 80], [51084, 87], [51199, 81], [51384, 87], [51498, 82], [51683, 88], [51798, 83], [51983, 88], [52097, 84], [52283, 56], [52361, 10], [52397, 85], [52583, 49], [52668, 4], [52697, 85], [52883, 44], [52996, 87], [53182, 41], [53295, 88], [53482, 38], [53595, 89], [53782, 36], [53894, 90], [54082, 34], [54192, 92], [54381, 33], [54490, 95], [54681, 30], [54789, 96], [54981, 29], [55090, 96], [55281, 28], [55391, 95], [55580, 28], [55692, 95], [55880, 27], [56000, 87], [56180, 26], [56307, 39], [56349, 39], [56480, 26], [56617, 12], [56655, 33], [56779, 26], [56957, 32], [57079, 25], [57257, 32], [57379, 25], [57557, 33], [57679, 25], [57796, 32], [57857, 33], [57979, 25], [58096, 48], [58152, 38], [58278, 26], [58396, 95], [58578, 26], [58696, 95], [58878, 26], [58996, 96], [59178, 26], [59296, 96], [59477, 27], [59596, 97], [59777, 28], [59895, 98], [60077, 28], [60195, 99], [60377, 28], [60495, 99], [60676, 29], [60795, 100], [60976, 29], [61095, 100], [61276, 30], [61394, 102], [61576, 30], [61694, 102], [61875, 31], [61994, 103], [62175, 31], [62294, 103], [62475, 32], [62593, 104], [62775, 32], [62893, 105], [63075, 32], [63193, 105], [63374, 33], [63493, 106]], "point": [201, 166]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan13", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -2.5, "y": 0.8995012, "z": 3.75}, "object_poses": [{"objectName": "Pencil_282a316d", "position": {"x": -1.69388, "y": 0.92379117, "z": 1.8}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_9933ef2d", "position": {"x": -0.218674153, "y": 1.61432481, "z": 3.53052664}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -3.807036, "y": 0.0790891051, "z": 5.11644936}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "WineBottle_eafa1419", "position": {"x": -3.639459, "y": 0.06963661, "z": 6.29268551}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pot_04c898c4", "position": {"x": -2.92400026, "y": 0.9303, "z": 1.712}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Cup_ba2c4270", "position": {"x": -0.258274853, "y": 0.923959553, "z": 5.27366638}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_ba2c4270", "position": {"x": -1.86012983, "y": 0.9223595, "z": 1.9510051}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_ba2c4270", "position": {"x": -2.02637982, "y": 0.9223595, "z": 2.02650762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_56566bc4", "position": {"x": -2.02637982, "y": 0.9232217, "z": 1.8}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9c9ac9d5", "position": {"x": -0.218672842, "y": 1.39299977, "z": 3.97424936}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Tomato_9c9ac9d5", "position": {"x": -0.401855022, "y": 1.12759972, "z": 3.47925}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "PepperShaker_ddab876f", "position": {"x": -4.210629, "y": 0.7519817, "z": 2.32430434}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_ddab876f", "position": {"x": -2.109505, "y": 0.9198061, "z": 1.87550259}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_67328aa7", "position": {"x": -4.233714, "y": 0.8863857, "z": 3.34761643}, "rotation": {"x": -1.44077641e-20, "y": 269.999939, "z": -1.40334191e-14}}, {"objectName": "Bread_b10200c3", "position": {"x": -0.30111903, "y": 1.66041136, "z": 3.34944344}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Bread_b10200c3", "position": {"x": -0.30111897, "y": 1.41551137, "z": 3.35549974}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Mug_19433bed", "position": {"x": -0.3487285, "y": 1.06555676, "z": 3.97425079}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Apple_d89eec12", "position": {"x": -3.784541, "y": 0.122178406, "z": 6.375}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Egg_ab4d2337", "position": {"x": -3.74827051, "y": 0.104547471, "z": 6.45731449}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_d89eec12", "position": {"x": -1.94325483, "y": 0.976970434, "z": 1.72449732}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_52085e6b", "position": {"x": -1.777005, "y": 0.9532003, "z": 1.72449732}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_9933ef2d", "position": {"x": -0.411640465, "y": 0.958568037, "z": 5.932334}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_1dc44411", "position": {"x": -0.488323241, "y": 0.92564857, "z": 5.27366638}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Bowl_f227e8eb", "position": {"x": -4.102533, "y": 0.92044735, "z": 2.64985013}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_53fee662", "position": {"x": -1.69388, "y": 0.924325943, "z": 1.6489948}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_67328aa7", "position": {"x": -3.49410057, "y": 1.01892853, "z": 5.259397}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_b10200c3", "position": {"x": -0.565006, "y": 1.00465465, "z": 5.27366638}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9c9ac9d5", "position": {"x": -0.488323241, "y": 0.982143, "z": 4.944333}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_eafa1419", "position": {"x": -1.610755, "y": 0.924428642, "z": 1.87550259}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_ebd9a920", "position": {"x": -0.258274853, "y": 0.9400999, "z": 4.944333}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_aa4cec24", "position": {"x": -4.517821, "y": 1.0277853, "z": 2.965943}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_ba2c4270", "position": {"x": -0.383563042, "y": 1.68471634, "z": 3.97536588}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Pencil_282a316d", "position": {"x": -4.102533, "y": 0.9237911, "z": 2.8829937}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_94f6c891", "position": {"x": -2.45436978, "y": 0.9335794, "z": 2.0230875}, "rotation": {"x": 0.0, "y": 240.000244, "z": 0.0}}, {"objectName": "Pot_04c898c4", "position": {"x": -2.469, "y": 0.9303, "z": 1.712}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SaltShaker_c6eeef1e", "position": {"x": -4.24220753, "y": 0.919806063, "z": 2.53327823}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_56566bc4", "position": {"x": -3.85256, "y": 0.923221648, "z": 5.259397}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PepperShaker_ddab876f", "position": {"x": -3.80725646, "y": 1.45515418, "z": 5.54820061}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "ButterKnife_8b572f1d", "position": {"x": -1.94325483, "y": 0.9235421, "z": 2.02650762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -0.532724559, "y": 0.7455661, "z": 4.849569}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_8612b3fc", "position": {"x": -1.777005, "y": 0.9246294, "z": 2.02650762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_19433bed", "position": {"x": -0.2598961, "y": 1.33095682, "z": 3.60299969}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}], "object_toggles": [], "random_seed": 3903802858, "scene_num": 13}, "task_id": "trial_T20190907_013449_796900", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1MKCTVKE7J0ZP_3AQF3RZ55BZBUITHTC5PVZUKW1D6FO", "high_descs": ["Go to the counter behind you.", "Pick up the cup next to the salt.", "Go to the microwave above the stove.", "Heat the cup in the microwave.", "Return to the counter to the left of the stove. ", "Place the cup on the counter and behind the knife."], "task_desc": "Heat up a cup. ", "votes": [1, 1]}, {"assignment_id": "A2L22HZ50T83UK_3B837J3LDRDDK6WNRK8MDT4VHE7RSC", "high_descs": ["Turn around behind you and stand in front of the cabinet that is to the left of the cooker. ", "Pick up the glass on the right. ", "Move right and stand in front of the cooker beside you. ", "Reach up and put the glass in the microwave, heat it and remove it. ", "Move back to the counter on your left. ", "Leave the glass at the back of the counter top "], "task_desc": "Leave the glass back on the counter after it has been microwaved. ", "votes": [1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_3JNQLM5FT739C9KCSLWK0ZISDU2L20", "high_descs": ["turn and walk to the kitchen counter to the left of the stove", "pick up the black cup on the right", "walk a bit to the right to face the microwave", "place the black cup inside of the microwave, microwave it, and then take it back out", "walk to the kitchen counter again", "place the black cup on the counter behind the other black cup"], "task_desc": "microwave a black cup", "votes": [1, 1]}]}}