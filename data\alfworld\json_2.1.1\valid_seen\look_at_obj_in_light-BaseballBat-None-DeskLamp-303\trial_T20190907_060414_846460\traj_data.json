{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 27}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "BaseballBat", "parent_target": "", "toggle_target": "DeskLamp"}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["baseballbat"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-5|1|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["baseballbat"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["BaseballBat", [-6.5179186, -6.5179186, 1.664, 1.664, 2.596, 2.596]], "forceVisible": true, "objectId": "BaseballBat|-01.63|+00.65|+00.42"}}, {"discrete_action": {"action": "GotoLocation", "args": ["desklamp"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-5|-4|3|60"}}, {"discrete_action": {"action": "ToggleObject", "args": ["desklamp"]}, "high_idx": 3, "planner_action": {"action": "ToggleObject", "coordinateObjectId": ["DeskLamp", [-7.3960004, -7.3960004, -3.1439984, -3.1439984, 2.367104, 2.367104]], "coordinateReceptacleObjectId": ["DeskLamp", [-7.3960004, -7.3960004, -3.1439984, -3.1439984, 2.367104, 2.367104]], "forceVisible": true, "objectId": "DeskLamp|-01.85|+00.59|-00.79"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 4, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "BaseballBat|-01.63|+00.65|+00.42"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [145, 145, 234, 233], "mask": [[43423, 7], [43721, 11], [44020, 13], [44320, 14], [44619, 15], [44919, 15], [45219, 15], [45519, 15], [45819, 16], [46119, 16], [46419, 16], [46718, 17], [47017, 18], [47316, 19], [47616, 19], [47915, 20], [48214, 21], [48513, 22], [48812, 16], [48829, 5], [49111, 15], [49410, 15], [49709, 14], [50008, 14], [50307, 14], [50606, 14], [50905, 13], [51204, 13], [51503, 13], [51802, 13], [52101, 13], [52400, 13], [52699, 13], [52998, 13], [53297, 12], [53596, 12], [53895, 12], [54194, 12], [54493, 12], [54792, 12], [55091, 12], [55390, 11], [55689, 11], [55988, 11], [56287, 11], [56585, 12], [56884, 12], [57183, 12], [57482, 12], [57781, 12], [58080, 12], [58378, 13], [58677, 12], [58976, 12], [59275, 12], [59573, 13], [59872, 14], [60171, 14], [60469, 15], [60768, 15], [61067, 15], [61366, 15], [61665, 15], [61963, 16], [62262, 17], [62561, 17], [62860, 17], [63158, 18], [63457, 18], [63756, 18], [64055, 18], [64354, 18], [64653, 18], [64952, 18], [65251, 18], [65550, 18], [65849, 19], [66148, 18], [66447, 18], [66746, 18], [67045, 18], [67345, 17], [67645, 16], [67945, 15], [68245, 14], [68545, 13], [68846, 11], [69146, 10], [69447, 7], [69749, 4]], "point": [189, 188]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "ToggleObjectOn", "objectId": "DeskLamp|-01.85|+00.59|-00.79"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [180, 68, 245, 155], "mask": [[20305, 14], [20601, 23], [20899, 28], [21197, 32], [21495, 36], [21793, 40], [22092, 43], [22391, 45], [22690, 47], [22989, 49], [23289, 50], [23588, 52], [23887, 54], [24187, 55], [24487, 56], [24787, 56], [25086, 58], [25386, 58], [25686, 59], [25986, 59], [26286, 60], [26586, 60], [26885, 61], [27185, 61], [27485, 61], [27785, 61], [28085, 61], [28385, 61], [28684, 62], [28984, 62], [29284, 61], [29584, 14], [29604, 1], [29608, 37], [29884, 10], [29904, 2], [29907, 37], [30184, 10], [30204, 2], [30207, 9], [30217, 27], [30483, 12], [30504, 2], [30507, 9], [30519, 24], [30783, 13], [30804, 2], [30807, 9], [30821, 22], [31083, 14], [31104, 2], [31107, 8], [31122, 20], [31383, 16], [31405, 1], [31407, 8], [31423, 19], [31683, 18], [31705, 1], [31707, 6], [31724, 17], [31982, 21], [32005, 8], [32026, 15], [32282, 31], [32327, 13], [32582, 30], [32628, 12], [32882, 31], [32926, 13], [33182, 57], [33482, 56], [33781, 57], [34081, 57], [34381, 56], [34681, 56], [34981, 55], [35281, 55], [35582, 53], [35882, 53], [36182, 52], [36483, 51], [36783, 50], [37083, 50], [37384, 48], [37685, 47], [37986, 45], [38287, 43], [38580, 50], [38880, 49], [39180, 49], [39480, 48], [39780, 46], [40080, 45], [40380, 43], [40680, 41], [40980, 39], [41281, 36], [41581, 36], [41881, 36], [42181, 36], [42481, 36], [42781, 37], [43081, 37], [43381, 37], [43681, 37], [43981, 37], [44281, 37], [44581, 37], [44881, 38], [45181, 38], [45482, 37], [45782, 37], [46082, 37], [46382, 37]], "point": [212, 110]}}, "high_idx": 3}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan303", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -0.5, "y": 0.901000142, "z": -0.75}, "object_poses": [{"objectName": "Mug_79472253", "position": {"x": 1.320924, "y": 1.17697549, "z": -2.27765536}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_79472253", "position": {"x": 1.66057765, "y": 1.57567549, "z": -2.31954455}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Vase_1bd7addd", "position": {"x": 1.19420123, "y": 0.356198341, "z": -2.3032}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_1bd7addd", "position": {"x": -1.64807248, "y": 0.5997989, "z": -0.5338481}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_194b8b51", "position": {"x": 1.9813447, "y": 0.9831283, "z": -2.235766}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_15d77e20", "position": {"x": 0.7227947, "y": 0.113801874, "z": -2.49353147}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "AlarmClock_b5103f04", "position": {"x": 0.07691851, "y": 0.7290902, "z": -2.58861017}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Box_ddebce5c", "position": {"x": 1.996, "y": 0.316, "z": -1.042}, "rotation": {"x": 0.0, "y": 254.839432, "z": 0.0}}, {"objectName": "Book_bd6507e1", "position": {"x": 0.339, "y": 0.7234131, "z": -2.65}, "rotation": {"x": 0.0, "y": 165.000366, "z": 0.0}}, {"objectName": "Pillow_0d1fa29c", "position": {"x": -1.54828632, "y": 0.457, "z": -1.83189642}, "rotation": {"x": 0.0, "y": 89.9999847, "z": 0.0}}, {"objectName": "CellPhone_31865df4", "position": {"x": -1.02475691, "y": 0.417449743, "z": -1.61096263}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_1bd7addd", "position": {"x": 0.766442, "y": 0.729498863, "z": -2.70558977}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "BaseballBat_a983eb51", "position": {"x": -1.62947965, "y": 0.649, "z": 0.416}, "rotation": {"x": 14.4914894, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_15d77e20", "position": {"x": -1.79164815, "y": 0.50899595, "z": -1.14891553}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "TennisRacket_2d36a6ad", "position": {"x": 2.244, "y": 0.279, "z": -1.901}, "rotation": {"x": 335.40213, "y": 251.97406, "z": 201.7148}}, {"objectName": "KeyChain_27a5504d", "position": {"x": -1.937785, "y": 0.5098357, "z": -1.14891553}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "AlarmClock_b5103f04", "position": {"x": 0.07691854, "y": 0.730703354, "z": -2.76407933}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Cloth_d24bf320", "position": {"x": 2.17615461, "y": 0.0, "z": -0.5715845}, "rotation": {"x": 0.0, "y": 335.474518, "z": 0.0}}, {"objectName": "Laptop_20fa9a67", "position": {"x": -1.764, "y": 0.5911203, "z": -0.112}, "rotation": {"x": 0.0, "y": 113.510445, "z": 0.0}}, {"objectName": "CD_10494eb8", "position": {"x": 2.216, "y": 0.0447977223, "z": 0.311}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "CD_ecad3d62", "position": {"x": 1.38428545, "y": 0.054426346, "z": -2.28225541}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_194b8b51", "position": {"x": -1.86471653, "y": 0.5146528, "z": -1.06529272}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pencil_7cb80570", "position": {"x": -1.714127, "y": 0.6027754, "z": -0.6753009}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_79472253", "position": {"x": 0.5366008, "y": 0.726799965, "z": -2.471631}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}], "object_toggles": [{"isOn": false, "objectType": "DeskLamp"}], "random_seed": 2762627153, "scene_num": 303}, "task_id": "trial_T20190907_060414_846460", "task_type": "look_at_obj_in_light", "turk_annotations": {"anns": [{"assignment_id": "A320QA9HJFUOZO_3E337GFOLCPPF3V43DXOFVGIT1HNGK", "high_descs": ["Turn around and walk up to the corner to the right of the desk", "Pick up the baseball bat from the floor near the desk", "Turn left and approach the table lamp on the left side of the desk", "Turn on the table lamp on the desk"], "task_desc": "Examine the baseball bat under the light of the table lamp", "votes": [1, 1]}, {"assignment_id": "A1GVTH5YS3WOK0_3Z4AIRP3C9UXNIJ5N0ZU6A3ATP6X1A", "high_descs": ["Turn left, walk towards the door, turn left and walk towards the baseball bat on the floor.", "Pick up the baseball bat from the floor.", "Turn left and walk towards the end of the desk on your right.", "Turn on the lamp on the desk"], "task_desc": "Examine the baseball bat next to the light of the lamp on the desk.", "votes": [1, 1]}, {"assignment_id": "AJQGWGESKQT4Y_3ZSY5X72N0SYME4UK3RDTOUULJAROA", "high_descs": ["Turn around and go left to the bat in the corner of the room.", "Pick the bat up from the floor.", "Go left and stand in front of the night stand to the right of the bed.", "Turn the light on that is on the left edge of the larger table."], "task_desc": "Turn a light on with a bat in hand.", "votes": [1, 1]}]}}