{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000292.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000293.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000294.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000295.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000296.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000297.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000298.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000299.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000300.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000301.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000302.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000303.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000304.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000305.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000306.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000307.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000308.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000309.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000310.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000311.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000312.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000313.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000314.png", "low_idx": 63}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON>g", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|0|-8|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["mug"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON>g", [3.1426724, 3.1426724, -8.1425, -8.1425, 1.84371102, 1.84371102]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [2.72, 2.72, -7.832, -7.832, -0.1252121924, -0.1252121924]], "forceVisible": true, "objectId": "Mug|+00.79|+00.46|-02.04"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-8|-11|2|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["mug"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-8.052, -8.052, -14.756, -14.756, 2.761099816, 2.761099816]], "forceVisible": true, "objectId": "Microwave|-02.01|+00.69|-03.69"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-2|-2|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["mug", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON>g", [3.1426724, 3.1426724, -8.1425, -8.1425, 1.84371102, 1.84371102]], "coordinateReceptacleObjectId": ["Cabinet", [2.0289164, 2.0289164, 0.627202988, 0.627202988, 1.383646, 1.383646]], "forceVisible": true, "objectId": "Mug|+00.79|+00.46|-02.04", "receptacleObjectId": "Cabinet|+00.51|+00.35|+00.16"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|+00.68|-00.03|-01.96"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 232], "mask": [[0, 32099], [32100, 299], [32400, 298], [32700, 296], [33000, 296], [33300, 295], [33600, 295], [33900, 294], [34200, 291], [34500, 288], [34800, 287], [35100, 286], [35400, 286], [35700, 285], [36000, 284], [36300, 283], [36600, 283], [36900, 282], [37200, 281], [37500, 280], [37800, 280], [38100, 279], [38400, 278], [38700, 277], [39000, 277], [39300, 276], [39600, 275], [39901, 273], [40202, 272], [40503, 270], [40803, 269], [41104, 267], [41405, 266], [41706, 264], [42007, 262], [42308, 260], [42609, 259], [42910, 257], [43211, 255], [43511, 254], [43812, 253], [44113, 251], [44414, 249], [44715, 247], [45016, 246], [45317, 244], [45618, 242], [45919, 240], [46220, 239], [46520, 238], [46821, 236], [47122, 234], [47423, 233], [47724, 231], [48025, 229], [48326, 227], [48627, 226], [48928, 224], [49229, 224], [49529, 225], [49830, 223], [50131, 222], [50432, 220], [50733, 215], [51034, 213], [51335, 211], [51636, 209], [51937, 207], [52238, 206], [52538, 205], [52839, 203], [53140, 201], [53441, 200], [53742, 198], [54043, 196], [54344, 194], [54645, 193], [54946, 191], [55246, 190], [55547, 189], [55848, 187], [56149, 185], [56450, 183], [56751, 182], [57052, 180], [57353, 178], [57654, 176], [57955, 175], [58255, 174], [58556, 172], [58857, 170], [59158, 169], [59459, 167], [59760, 165], [60061, 163], [60362, 162], [60663, 160], [60964, 158], [61264, 157], [61565, 156], [61866, 154], [62167, 152], [62468, 150], [62769, 149], [63070, 147], [63371, 145], [63672, 143], [63972, 143], [64272, 143], [64572, 142], [64872, 142], [65172, 142], [65472, 141], [65773, 140], [66074, 138], [66375, 136], [66676, 134], [66980, 126], [67283, 118], [67586, 112], [67889, 104], [68195, 92], [68500, 81], [68807, 66], [69114, 50], [69425, 18]], "point": [149, 115]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Mug|+00.79|+00.46|-02.04"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [148, 105, 167, 130], "mask": [[31354, 3], [31359, 2], [31653, 4], [31659, 3], [31950, 1], [31953, 4], [31959, 3], [32249, 2], [32253, 4], [32259, 3], [32549, 2], [32553, 4], [32559, 3], [32565, 1], [32848, 3], [32853, 4], [32859, 3], [32865, 1], [33148, 3], [33154, 3], [33159, 4], [33165, 2], [33448, 3], [33454, 3], [33459, 4], [33465, 3], [33748, 3], [33754, 3], [33759, 4], [33765, 3], [34048, 3], [34054, 3], [34059, 4], [34065, 1], [34348, 3], [34354, 3], [34359, 4], [34365, 1], [34648, 3], [34654, 3], [34659, 4], [34665, 1], [34949, 2], [34954, 3], [34959, 4], [34965, 1], [35249, 2], [35254, 3], [35259, 4], [35265, 1], [35549, 2], [35554, 3], [35559, 4], [35565, 1], [35567, 1], [35849, 2], [35854, 3], [35859, 4], [35866, 2], [36149, 2], [36154, 3], [36159, 4], [36165, 2], [36449, 2], [36454, 3], [36459, 4], [36465, 1], [36749, 2], [36754, 3], [36759, 4], [37049, 2], [37054, 3], [37059, 4], [37349, 2], [37354, 3], [37359, 4], [37650, 1], [37654, 3], [37659, 4], [37950, 1], [37954, 3], [37959, 4], [38254, 3], [38259, 4], [38554, 3], [38560, 2], [38854, 3]], "point": [156, 116]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|+00.68|-00.03|-01.96"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 18899], [18900, 298], [19200, 298], [19500, 297], [19800, 297], [20100, 296], [20400, 295], [20700, 295], [21000, 294], [21300, 293], [21600, 293], [21900, 292], [22200, 292], [22500, 291], [22800, 290], [23100, 290], [23400, 289], [23700, 288], [24000, 288], [24300, 287], [24600, 287], [24900, 286], [25200, 285], [25500, 285], [25800, 284], [26100, 283], [26400, 283], [26700, 282], [27000, 282], [27300, 281], [27600, 280], [27900, 280], [28200, 279], [28500, 278], [28800, 278], [29100, 277], [29400, 277], [29700, 276], [30000, 275], [30300, 275], [30600, 274], [30900, 273], [31200, 273], [31500, 272], [31800, 271], [32101, 270], [32400, 1], [32402, 268], [32700, 2], [32703, 267], [33000, 269], [33300, 3], [33304, 264], [33600, 4], [33605, 263], [33900, 5], [33906, 261], [34200, 6], [34207, 259], [34500, 266], [34800, 7], [34808, 257], [35100, 8], [35109, 256], [35400, 9], [35410, 254], [35700, 263], [36000, 10], [36011, 252], [36300, 11], [36312, 250], [36600, 12], [36613, 248], [36900, 261], [37200, 260], [37500, 14], [37515, 245], [37800, 15], [37816, 243], [38100, 16], [38117, 241], [38400, 258], [38700, 17], [38718, 239], [39000, 18], [39019, 237], [39300, 19], [39320, 236], [39600, 255], [39900, 255], [40200, 21], [40222, 232], [40500, 22], [40523, 230], [40800, 23], [40824, 229], [41100, 252], [41400, 24], [41425, 226], [41700, 25], [41726, 225], [42000, 26], [42027, 223], [42300, 250], [42600, 249], [42900, 28], [42929, 219], [43200, 29], [43230, 218], [43500, 247], [43800, 246], [44100, 31], [44132, 214], [44400, 32], [44433, 212], [44700, 33], [44734, 210], [45000, 244], [45300, 243], [45600, 35], [45636, 207], [45900, 36], [45937, 205], [46200, 241], [46500, 241], [46800, 38], [46839, 201], [47100, 39], [47140, 199], [47400, 40], [47441, 198], [47700, 238], [48000, 238], [48300, 42], [48343, 194], [48600, 43], [48644, 192], [48900, 236], [49200, 235], [49500, 45], [49546, 188], [49800, 46], [49847, 187], [50100, 47], [50148, 185], [50400, 233], [50700, 232], [51000, 49], [51050, 181], [51300, 50], [51351, 180], [51600, 230], [51900, 229], [52200, 52], [52253, 176], [52500, 53], [52554, 174], [52800, 228], [53100, 227], [53400, 226], [53700, 56], [53757, 169], [54000, 57], [54058, 167], [54300, 224], [54600, 224], [54900, 59], [54960, 163], [55200, 60], [55261, 162], [55500, 222], [55800, 221], [56100, 221], [56400, 63], [56464, 156], [56700, 64], [56765, 154], [57000, 219], [57300, 218], [57600, 66], [57667, 150], [57900, 67], [57968, 149], [58200, 216], [58500, 216], [58800, 215], [59100, 70], [59171, 143], [59400, 214], [59700, 213], [60000, 212], [60300, 212], [60600, 212], [60900, 212], [61200, 212], [61500, 212], [61800, 212], [62100, 212], [62400, 146], [62554, 59], [62700, 140], [62860, 53], [63000, 137], [63163, 50], [63300, 135], [63465, 48], [63600, 132], [63768, 45], [63900, 130], [64070, 43], [64200, 129], [64371, 42], [64500, 128], [64672, 41], [64800, 126], [64974, 40], [65100, 125], [65275, 39], [65400, 124], [65576, 37], [65700, 72], [65773, 50], [65877, 36], [66000, 71], [66074, 48], [66178, 34], [66300, 71], [66375, 46], [66479, 32], [66600, 71], [66676, 44], [66780, 30], [66900, 71], [66980, 39], [67081, 25], [67200, 71], [67283, 36], [67381, 20], [67500, 71], [67586, 32], [67682, 16], [67800, 70], [67889, 28], [67983, 10], [68100, 70], [68195, 21], [68284, 3], [68400, 70], [68500, 16], [68700, 70], [68807, 8], [69000, 70], [69114, 1], [69300, 70], [69600, 69], [69900, 69], [70200, 69], [70500, 69], [70800, 69], [71100, 69], [71400, 68], [71700, 68], [72000, 68], [72300, 68], [72600, 68], [72900, 68], [73200, 68], [73500, 67], [73800, 67], [74100, 67], [74400, 67], [74700, 67], [75000, 67], [75300, 66], [75600, 66], [75900, 66], [76200, 66], [76500, 66], [76800, 66], [77100, 65], [77400, 65], [77700, 65], [78000, 65], [78300, 65], [78600, 65], [78900, 64], [79200, 64], [79500, 64], [79800, 64], [80100, 64], [80400, 64], [80700, 63], [81000, 63], [81300, 63], [81600, 63], [81900, 63], [82200, 63], [82500, 63], [82800, 62], [83100, 62], [83400, 62], [83700, 62], [84000, 62], [84300, 62], [84600, 61], [84900, 61], [85200, 61], [85500, 61], [85800, 61], [86100, 61], [86400, 60], [86700, 60], [87000, 60], [87300, 60], [87600, 60], [87900, 60], [88200, 59], [88500, 59], [88800, 59], [89100, 59], [89400, 59], [89700, 59]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|+00.79|+00.46|-02.04", "placeStationary": true, "receptacleObjectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 42, 242, 253], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25861, 181], [26160, 181], [26459, 182], [26759, 182], [27059, 182], [27358, 182], [27658, 182], [27957, 183], [28257, 182], [28556, 183], [28854, 185], [29154, 185], [29453, 185], [29752, 186], [30052, 186], [30351, 187], [30651, 187], [30950, 188], [31250, 97], [31353, 85], [31550, 95], [31655, 83], [31849, 95], [31956, 82], [32149, 95], [32257, 81], [32448, 95], [32557, 81], [32748, 95], [32858, 81], [33047, 96], [33158, 81], [33347, 95], [33458, 81], [33646, 96], [33759, 80], [33946, 97], [34058, 80], [34245, 98], [34358, 80], [34545, 98], [34658, 80], [34844, 100], [34958, 80], [35144, 100], [35257, 81], [35443, 102], [35556, 81], [35743, 104], [35854, 83], [36043, 107], [36151, 86], [36342, 195], [36642, 194], [36941, 195], [37241, 195], [37540, 195], [37840, 195], [38139, 196], [38439, 195], [38738, 196], [39038, 196], [39337, 197], [39637, 196], [39936, 197], [40236, 197], [40536, 196], [40835, 197], [41135, 197], [41434, 197], [41734, 197], [42033, 198], [42333, 198], [42632, 198], [42932, 198], [43231, 199], [43531, 198], [43830, 199], [44130, 199], [44429, 199], [44729, 199], [45029, 199], [45328, 200], [45628, 199], [45927, 200], [46227, 195], [46526, 197], [46826, 196], [47125, 197], [47425, 197], [47724, 60], [47785, 136], [48024, 60], [48085, 136], [48323, 61], [48385, 136], [48623, 61], [48922, 61], [49222, 61], [49522, 61], [49821, 62], [50121, 61], [50420, 62], [50720, 62], [51019, 63], [51319, 62], [51618, 63], [51918, 63], [52217, 64], [52517, 63], [52816, 64], [53116, 64], [53415, 65], [53715, 64], [54015, 64], [54314, 65], [54614, 65], [54913, 66], [55213, 65], [55512, 66], [55812, 66], [56111, 67], [56411, 66], [56710, 67], [57000, 3], [57010, 67], [57300, 3], [57309, 68], [57600, 3], [57609, 67], [57900, 4], [57908, 68], [58200, 76], [58500, 76], [58800, 75], [59100, 75], [59400, 75], [59700, 75], [60000, 74], [60300, 74], [60600, 74], [60901, 73], [61203, 70], [61503, 70], [61802, 71], [62102, 71], [62400, 1], [62402, 70], [62700, 72], [63000, 72], [63300, 72], [63600, 71], [63900, 71], [64200, 71], [64500, 71], [64800, 70], [65100, 70], [65400, 70], [65700, 70], [66000, 69], [66300, 69], [66600, 69], [66900, 69], [67200, 69], [67500, 68], [67800, 68], [68101, 67], [68402, 66], [68703, 64], [69004, 63], [69304, 63], [69605, 62], [69906, 60], [70207, 59], [70508, 58], [70809, 57], [71109, 56], [71410, 55], [71711, 54], [72012, 53], [72313, 51], [72613, 51], [72914, 50], [73215, 49], [73517, 46], [73819, 44], [74123, 42], [74427, 8], [74436, 30], [74730, 3], [74739, 28], [75043, 19], [75063, 3], [75345, 17], [75646, 16]], "point": [121, 146]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 42, 242, 253], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25861, 181], [26160, 181], [26459, 182], [26759, 182], [27059, 182], [27358, 182], [27658, 182], [27957, 183], [28257, 182], [28556, 183], [28854, 185], [29154, 185], [29453, 185], [29752, 186], [30052, 186], [30351, 187], [30651, 187], [30950, 71], [31031, 107], [31250, 68], [31333, 14], [31353, 85], [31550, 67], [31634, 11], [31655, 83], [31849, 67], [31935, 9], [31956, 82], [32149, 66], [32236, 8], [32257, 81], [32448, 67], [32536, 7], [32557, 81], [32748, 67], [32838, 5], [32858, 81], [33047, 68], [33140, 3], [33158, 81], [33347, 68], [33436, 1], [33440, 2], [33458, 81], [33646, 69], [33736, 3], [33741, 1], [33759, 80], [33946, 69], [34036, 3], [34041, 2], [34058, 80], [34245, 70], [34336, 3], [34341, 2], [34358, 80], [34545, 70], [34636, 3], [34641, 2], [34658, 80], [34844, 72], [34936, 3], [34941, 3], [34958, 80], [35144, 72], [35236, 3], [35240, 4], [35257, 81], [35443, 73], [35536, 2], [35540, 5], [35556, 81], [35743, 73], [35836, 2], [35840, 7], [35854, 83], [36043, 73], [36136, 1], [36139, 11], [36151, 86], [36342, 75], [36436, 1], [36439, 98], [36642, 75], [36738, 98], [36941, 76], [37037, 99], [37241, 76], [37336, 100], [37540, 77], [37636, 99], [37840, 77], [37936, 99], [38139, 79], [38236, 99], [38439, 79], [38535, 99], [38738, 80], [38835, 99], [39038, 81], [39134, 100], [39337, 83], [39433, 101], [39637, 84], [39731, 102], [39936, 197], [40236, 197], [40536, 196], [40835, 197], [41135, 197], [41434, 197], [41734, 197], [42033, 198], [42333, 198], [42632, 198], [42932, 198], [43231, 199], [43531, 198], [43830, 199], [44130, 199], [44429, 199], [44729, 199], [45029, 199], [45328, 200], [45628, 199], [45927, 200], [46227, 195], [46526, 197], [46826, 196], [47125, 197], [47425, 197], [47724, 60], [47785, 136], [48024, 60], [48085, 136], [48323, 61], [48385, 136], [48623, 61], [48922, 61], [49222, 61], [49522, 61], [49821, 62], [50121, 61], [50420, 62], [50720, 62], [51019, 63], [51319, 62], [51618, 63], [51918, 63], [52217, 64], [52517, 63], [52816, 64], [53116, 64], [53415, 65], [53715, 64], [54015, 64], [54314, 65], [54614, 65], [54913, 66], [55213, 65], [55512, 66], [55812, 66], [56111, 67], [56411, 66], [56710, 67], [57000, 3], [57010, 67], [57300, 3], [57309, 68], [57600, 3], [57609, 67], [57900, 4], [57908, 68], [58200, 76], [58500, 76], [58800, 75], [59100, 75], [59400, 75], [59700, 75], [60000, 74], [60300, 74], [60600, 74], [60901, 73], [61203, 70], [61503, 70], [61802, 71], [62102, 71], [62400, 1], [62402, 70], [62700, 72], [63000, 72], [63300, 72], [63600, 71], [63900, 71], [64200, 71], [64500, 71], [64800, 70], [65100, 70], [65400, 70], [65700, 70], [66000, 69], [66300, 69], [66600, 69], [66900, 69], [67200, 69], [67500, 68], [67800, 68], [68101, 67], [68402, 66], [68703, 64], [69004, 63], [69304, 63], [69605, 62], [69906, 60], [70207, 59], [70508, 58], [70809, 57], [71109, 56], [71410, 55], [71711, 54], [72012, 53], [72313, 51], [72613, 51], [72914, 50], [73215, 49], [73517, 46], [73819, 44], [74123, 42], [74427, 8], [74436, 30], [74730, 3], [74739, 28], [75043, 19], [75063, 3], [75345, 17], [75646, 16]], "point": [121, 146]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-8.052, -8.052, -14.756, -14.756, 2.761099816, 2.761099816]], "forceVisible": true, "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-8.052, -8.052, -14.756, -14.756, 2.761099816, 2.761099816]], "forceVisible": true, "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Mug|+00.79|+00.46|-02.04"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [115, 104, 140, 133], "mask": [[31021, 10], [31318, 15], [31617, 17], [31916, 19], [32215, 21], [32515, 21], [32815, 23], [33115, 25], [33415, 21], [33437, 3], [33715, 21], [33739, 2], [34015, 21], [34039, 2], [34315, 21], [34339, 2], [34615, 21], [34639, 2], [34916, 20], [34939, 2], [35216, 20], [35239, 1], [35516, 20], [35538, 2], [35816, 20], [35838, 2], [36116, 20], [36137, 2], [36417, 19], [36437, 2], [36717, 21], [37017, 20], [37317, 19], [37617, 19], [37917, 19], [38218, 18], [38518, 17], [38818, 17], [39119, 15], [39420, 13], [39721, 10]], "point": [127, 117]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 42, 242, 253], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25861, 181], [26160, 181], [26459, 182], [26759, 182], [27059, 182], [27358, 182], [27658, 182], [27957, 183], [28257, 182], [28556, 183], [28854, 185], [29154, 185], [29453, 185], [29752, 186], [30052, 186], [30351, 187], [30651, 187], [30950, 188], [31250, 97], [31353, 85], [31550, 95], [31655, 83], [31849, 95], [31956, 82], [32149, 95], [32257, 81], [32448, 95], [32557, 81], [32748, 95], [32858, 81], [33047, 96], [33158, 81], [33347, 95], [33458, 81], [33646, 96], [33759, 80], [33946, 97], [34058, 80], [34245, 98], [34358, 80], [34545, 98], [34658, 80], [34844, 100], [34958, 80], [35144, 100], [35257, 81], [35443, 102], [35556, 81], [35743, 104], [35854, 83], [36043, 107], [36151, 86], [36342, 195], [36642, 194], [36941, 195], [37241, 195], [37540, 195], [37840, 195], [38139, 196], [38439, 195], [38738, 196], [39038, 196], [39337, 197], [39637, 196], [39936, 197], [40236, 197], [40536, 196], [40835, 197], [41135, 197], [41434, 197], [41734, 197], [42033, 198], [42333, 198], [42632, 198], [42932, 198], [43231, 199], [43531, 198], [43830, 199], [44130, 199], [44429, 199], [44729, 199], [45029, 199], [45328, 200], [45628, 199], [45927, 200], [46227, 195], [46526, 197], [46826, 196], [47125, 197], [47425, 197], [47724, 60], [47785, 136], [48024, 60], [48085, 136], [48323, 61], [48385, 136], [48623, 61], [48922, 61], [49222, 61], [49522, 61], [49821, 62], [50121, 61], [50420, 62], [50720, 62], [51019, 63], [51319, 62], [51618, 63], [51918, 63], [52217, 64], [52517, 63], [52816, 64], [53116, 64], [53415, 65], [53715, 64], [54015, 64], [54314, 65], [54614, 65], [54913, 66], [55213, 65], [55512, 66], [55812, 66], [56111, 67], [56411, 66], [56710, 67], [57000, 3], [57010, 67], [57300, 3], [57309, 68], [57600, 3], [57609, 67], [57900, 4], [57908, 68], [58200, 76], [58500, 76], [58800, 75], [59100, 75], [59400, 75], [59700, 75], [60000, 74], [60300, 74], [60600, 74], [60901, 73], [61203, 70], [61503, 70], [61802, 71], [62102, 71], [62400, 1], [62402, 70], [62700, 72], [63000, 72], [63300, 72], [63600, 71], [63900, 71], [64200, 71], [64500, 71], [64800, 70], [65100, 70], [65400, 70], [65700, 70], [66000, 69], [66300, 69], [66600, 69], [66900, 69], [67200, 69], [67500, 68], [67800, 68], [68101, 67], [68402, 66], [68703, 64], [69004, 63], [69304, 63], [69605, 62], [69906, 60], [70207, 59], [70508, 58], [70809, 57], [71109, 56], [71410, 55], [71711, 54], [72012, 53], [72313, 51], [72613, 51], [72914, 50], [73215, 49], [73517, 46], [73819, 44], [74123, 42], [74427, 8], [74436, 30], [74730, 3], [74739, 28], [75043, 19], [75063, 3], [75345, 17], [75646, 16]], "point": [121, 146]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|+00.51|+00.35|+00.16"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [19, 126, 135, 207], "mask": [[37519, 113], [37819, 113], [38119, 113], [38420, 112], [38720, 112], [39021, 111], [39321, 111], [39622, 110], [39922, 110], [40223, 109], [40523, 109], [40823, 109], [41124, 108], [41424, 108], [41725, 108], [42025, 108], [42326, 107], [42626, 107], [42927, 106], [43227, 106], [43528, 105], [43828, 105], [44129, 104], [44429, 104], [44729, 104], [45030, 103], [45330, 103], [45631, 102], [45931, 102], [46232, 102], [46532, 102], [46833, 101], [47133, 101], [47434, 100], [47734, 100], [48035, 99], [48335, 99], [48635, 99], [48936, 98], [49236, 98], [49537, 97], [49837, 97], [50138, 96], [50438, 96], [50739, 95], [51039, 96], [51340, 95], [51640, 95], [51941, 94], [52241, 94], [52541, 94], [52842, 93], [53142, 93], [53443, 92], [53743, 92], [54044, 91], [54344, 91], [54645, 90], [54945, 90], [55246, 89], [55546, 90], [55847, 89], [56147, 89], [56448, 88], [56748, 88], [57048, 88], [57349, 87], [57649, 87], [57950, 86], [58250, 86], [58551, 85], [58851, 82], [59152, 79], [59452, 77], [59753, 75], [60053, 73], [60354, 71], [60654, 70], [60954, 69], [61255, 67], [61555, 66], [61887, 33]], "point": [77, 165]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|+00.79|+00.46|-02.04", "placeStationary": true, "receptacleObjectId": "Cabinet|+00.51|+00.35|+00.16"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 126, 132, 294], "mask": [[37518, 2], [37817, 3], [37823, 104], [38117, 4], [38122, 105], [38416, 5], [38423, 104], [38716, 6], [38723, 104], [39015, 7], [39024, 104], [39315, 8], [39324, 104], [39614, 9], [39624, 104], [39913, 10], [39925, 103], [40213, 11], [40225, 103], [40512, 12], [40526, 102], [40812, 13], [40826, 102], [41111, 14], [41127, 101], [41411, 15], [41427, 101], [41710, 16], [41728, 100], [42010, 17], [42028, 100], [42309, 18], [42329, 99], [42608, 20], [42629, 99], [42908, 20], [42929, 71], [43009, 20], [43207, 21], [43230, 68], [43309, 20], [43507, 22], [43530, 71], [43608, 21], [43806, 23], [43831, 73], [43908, 21], [44106, 24], [44131, 74], [44209, 20], [44405, 25], [44432, 72], [44510, 19], [44704, 27], [44732, 72], [44812, 17], [45004, 27], [45033, 69], [45114, 15], [45303, 29], [45333, 68], [45415, 14], [45603, 29], [45633, 67], [45716, 13], [45902, 31], [45934, 66], [46016, 13], [46202, 31], [46234, 65], [46316, 13], [46501, 33], [46535, 64], [46616, 14], [46801, 33], [46835, 64], [46916, 14], [47100, 34], [47136, 63], [47215, 15], [47400, 35], [47436, 64], [47515, 15], [47700, 35], [47737, 64], [47815, 15], [48000, 36], [48037, 65], [48115, 15], [48300, 36], [48338, 64], [48415, 15], [48600, 37], [48638, 64], [48715, 15], [48900, 37], [48938, 65], [49015, 15], [49200, 38], [49239, 64], [49315, 15], [49500, 38], [49539, 64], [49615, 15], [49800, 39], [49840, 63], [49915, 15], [50100, 39], [50140, 64], [50215, 16], [50400, 39], [50441, 63], [50514, 17], [50700, 40], [50741, 65], [50813, 18], [51000, 40], [51042, 66], [51110, 21], [51300, 41], [51342, 89], [51600, 41], [51643, 88], [51900, 42], [51943, 88], [52200, 42], [52243, 88], [52500, 43], [52544, 87], [52800, 43], [52844, 87], [53100, 44], [53145, 86], [53400, 44], [53445, 86], [53700, 45], [53746, 85], [54000, 45], [54046, 86], [54300, 45], [54347, 85], [54600, 46], [54647, 85], [54900, 46], [54948, 84], [55200, 47], [55248, 84], [55500, 47], [55548, 84], [55800, 48], [55849, 83], [56100, 48], [56149, 83], [56400, 49], [56450, 82], [56700, 49], [56750, 82], [57000, 50], [57051, 81], [57300, 50], [57351, 81], [57600, 50], [57652, 81], [57900, 51], [57952, 81], [58200, 51], [58253, 80], [58500, 52], [58553, 80], [58800, 52], [58853, 80], [59100, 53], [59154, 77], [59400, 53], [59454, 75], [59700, 54], [59755, 73], [60000, 54], [60055, 71], [60300, 55], [60356, 69], [60600, 55], [60656, 68], [60900, 55], [60957, 66], [61200, 56], [61257, 65], [61500, 56], [61800, 56], [62100, 56], [62400, 55], [62700, 55], [63000, 55], [63300, 54], [63600, 54], [63900, 54], [64200, 54], [64500, 53], [64800, 53], [65100, 53], [65400, 52], [65700, 52], [66000, 52], [66300, 51], [66600, 51], [66900, 51], [67200, 51], [67500, 50], [67800, 50], [68100, 50], [68400, 49], [68700, 49], [69000, 49], [69300, 49], [69600, 48], [69900, 48], [70200, 48], [70500, 47], [70800, 47], [71100, 47], [71400, 46], [71700, 46], [72000, 46], [72300, 46], [72600, 45], [72900, 45], [73200, 45], [73500, 44], [73800, 44], [74100, 44], [74400, 43], [74700, 43], [75000, 43], [75300, 43], [75600, 42], [75900, 42], [76200, 42], [76500, 41], [76800, 42], [77100, 42], [77400, 41], [77700, 41], [78000, 41], [78300, 41], [78600, 40], [78900, 40], [79200, 40], [79500, 39], [79800, 39], [80100, 39], [80401, 37], [80702, 36], [81003, 35], [81304, 34], [81605, 32], [81906, 31], [82207, 30], [82508, 28], [82809, 27], [83110, 26], [83411, 24], [83712, 23], [84013, 22], [84315, 20], [84616, 18], [84917, 17], [85218, 16], [85519, 14], [85820, 13], [86121, 12], [86422, 11], [86723, 9], [87024, 8], [87325, 6], [87626, 4], [87927, 3]], "point": [66, 204]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|+00.51|+00.35|+00.16"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 126, 133, 294], "mask": [[37518, 2], [37817, 3], [37823, 104], [38117, 4], [38122, 105], [38416, 5], [38423, 104], [38716, 6], [38723, 104], [39015, 7], [39024, 104], [39315, 8], [39324, 104], [39614, 9], [39624, 104], [39913, 10], [39925, 103], [40213, 11], [40225, 103], [40512, 12], [40526, 102], [40812, 13], [40826, 102], [41111, 14], [41127, 101], [41411, 15], [41427, 101], [41710, 16], [41728, 100], [42010, 17], [42028, 100], [42309, 18], [42329, 99], [42608, 20], [42629, 99], [42908, 20], [42929, 71], [43009, 20], [43207, 21], [43230, 68], [43309, 20], [43507, 22], [43530, 71], [43608, 21], [43806, 23], [43831, 73], [43908, 21], [44106, 24], [44131, 74], [44209, 20], [44405, 25], [44432, 72], [44510, 19], [44704, 27], [44732, 72], [44812, 17], [45004, 27], [45033, 69], [45114, 15], [45303, 29], [45333, 68], [45415, 14], [45603, 29], [45633, 67], [45716, 13], [45902, 31], [45934, 66], [46016, 13], [46202, 31], [46234, 65], [46316, 13], [46501, 33], [46535, 64], [46616, 14], [46801, 33], [46835, 64], [46916, 14], [47100, 34], [47136, 63], [47215, 15], [47400, 35], [47436, 64], [47515, 15], [47700, 35], [47737, 64], [47815, 15], [48000, 36], [48037, 65], [48115, 15], [48300, 36], [48338, 64], [48415, 15], [48600, 37], [48638, 64], [48715, 15], [48900, 37], [48938, 65], [49015, 15], [49200, 38], [49239, 64], [49315, 15], [49500, 38], [49539, 64], [49615, 15], [49800, 39], [49840, 63], [49915, 15], [50100, 39], [50140, 64], [50215, 16], [50400, 39], [50441, 63], [50514, 17], [50700, 40], [50741, 65], [50813, 18], [51000, 40], [51042, 66], [51110, 21], [51300, 41], [51342, 89], [51600, 41], [51643, 88], [51900, 42], [51943, 88], [52200, 42], [52243, 88], [52500, 43], [52544, 87], [52800, 43], [52844, 87], [53100, 44], [53145, 75], [53228, 3], [53400, 44], [53445, 74], [53529, 2], [53700, 45], [53746, 72], [53829, 2], [54000, 45], [54046, 71], [54130, 2], [54300, 45], [54347, 70], [54430, 2], [54600, 46], [54647, 70], [54730, 2], [54900, 46], [54948, 69], [55030, 2], [55200, 47], [55248, 69], [55330, 2], [55500, 47], [55548, 70], [55630, 2], [55800, 48], [55849, 69], [55930, 2], [56100, 48], [56149, 69], [56230, 2], [56400, 49], [56450, 68], [56530, 2], [56700, 49], [56750, 69], [56830, 2], [57000, 50], [57051, 68], [57130, 2], [57300, 50], [57351, 68], [57430, 2], [57600, 50], [57652, 67], [57729, 4], [57900, 51], [57952, 68], [58028, 5], [58200, 51], [58253, 69], [58326, 7], [58500, 52], [58553, 80], [58800, 52], [58853, 80], [59100, 53], [59154, 79], [59400, 53], [59454, 79], [59700, 54], [59755, 78], [60000, 54], [60055, 78], [60300, 55], [60356, 77], [60600, 55], [60656, 77], [60900, 55], [60957, 76], [61200, 56], [61257, 77], [61500, 56], [61800, 56], [62100, 56], [62400, 55], [62700, 55], [63000, 55], [63300, 54], [63600, 54], [63900, 54], [64200, 54], [64500, 53], [64800, 53], [65100, 53], [65400, 52], [65700, 52], [66000, 52], [66300, 51], [66600, 51], [66900, 51], [67200, 51], [67500, 50], [67800, 50], [68100, 50], [68400, 49], [68700, 49], [69000, 49], [69300, 49], [69600, 48], [69900, 48], [70200, 48], [70500, 47], [70800, 47], [71100, 47], [71400, 46], [71700, 46], [72000, 46], [72300, 46], [72600, 45], [72900, 45], [73200, 45], [73500, 44], [73800, 44], [74100, 44], [74400, 43], [74700, 43], [75000, 43], [75300, 43], [75600, 42], [75900, 42], [76200, 42], [76500, 41], [76800, 42], [77100, 42], [77400, 41], [77700, 41], [78000, 41], [78300, 41], [78600, 40], [78900, 40], [79200, 40], [79500, 39], [79800, 39], [80100, 39], [80401, 37], [80702, 36], [81003, 35], [81304, 34], [81605, 32], [81906, 31], [82207, 30], [82508, 28], [82809, 27], [83110, 26], [83411, 24], [83712, 23], [84013, 22], [84315, 20], [84616, 18], [84917, 17], [85218, 16], [85519, 14], [85820, 13], [86121, 12], [86422, 11], [86723, 9], [87024, 8], [87325, 6], [87626, 4], [87927, 3]], "point": [66, 204]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan21", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -1.0, "y": 0.869696259, "z": 0.25}, "object_poses": [{"objectName": "Potato_027e0a30", "position": {"x": -1.53555322, "y": 0.7296279, "z": -3.529788}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_027e0a30", "position": {"x": -2.00200438, "y": 0.832557559, "z": -3.70596051}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_93a81026", "position": {"x": -1.05636024, "y": 0.901750147, "z": 1.18701911}, "rotation": {"x": 0.0, "y": 0.000327849062, "z": 0.0}}, {"objectName": "Spoon_93a81026", "position": {"x": 0.671831965, "y": 0.7609973, "z": -0.061030075}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_34f12e87", "position": {"x": 0.9509703, "y": 0.884148538, "z": -0.245861}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_cb0de209", "position": {"x": -1.59924114, "y": 0.698904634, "z": -3.61407733}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_cb0de209", "position": {"x": -1.80324006, "y": 0.700419068, "z": -1.41601658}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_d7803c74", "position": {"x": -0.2527308, "y": 0.8999009, "z": 1.18701458}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Bowl_737b77b7", "position": {"x": -0.8554534, "y": 0.899054945, "z": 1.1012491}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Bread_42314d78", "position": {"x": 0.901886463, "y": 0.708858848, "z": 0.508934}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_42314d78", "position": {"x": 0.149081826, "y": 0.9440833, "z": 0.843936741}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Mug_288ff1e3", "position": {"x": 0.7856681, "y": 0.460927755, "z": -2.035625}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Pot_cde1951c", "position": {"x": -1.555, "y": 0.9306, "z": 1.049}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_2b33f66e", "position": {"x": 0.6769987, "y": 0.019684732, "z": -0.702639937}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_34f12e87", "position": {"x": 0.781749845, "y": 0.665148556, "z": 0.4481}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_d7803c74", "position": {"x": 0.842995167, "y": 0.0149986744, "z": -0.184701681}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_093f2b24", "position": {"x": 0.67299825, "y": 0.826046169, "z": -0.515433967}, "rotation": {"x": 7.58303337e-22, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Pan_63693e0a", "position": {"x": -2.097, "y": 0.9306, "z": 1.049}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_c79ff911", "position": {"x": 0.8837527, "y": 0.8091614, "z": -0.8457127}, "rotation": {"x": 7.58303337e-22, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Spatula_8142ce7b", "position": {"x": -1.88688087, "y": 0.716789246, "z": -0.983721733}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_027e0a30", "position": {"x": 0.8837527, "y": 0.805609345, "z": -0.735619843}, "rotation": {"x": 7.58303337e-22, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Cup_cb0de209", "position": {"x": 0.7151491, "y": 0.7711859, "z": -0.7906662}, "rotation": {"x": 7.58303337e-22, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "SaltShaker_886fcaf3", "position": {"x": 0.6216665, "y": 0.0120247006, "z": -0.8987274}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_87875872", "position": {"x": -1.88688087, "y": 0.69809556, "z": -1.30794287}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_0ad5c562", "position": {"x": -0.453640223, "y": 0.900662839, "z": 0.843940139}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "DishSponge_11e4138d", "position": {"x": 0.62322855, "y": 0.759432137, "z": -0.252836943}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_42314d78", "position": {"x": 0.6616132, "y": 0.7080625, "z": 0.387266}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_0ad3089c", "position": {"x": -1.26099992, "y": 0.1388545, "z": -3.611}, "rotation": {"x": 0.0, "y": 269.999878, "z": 0.0}}, {"objectName": "Tomato_5a2cfc73", "position": {"x": -2.05416274, "y": 0.7501781, "z": -1.41601658}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_93a81026", "position": {"x": 0.877196848, "y": 0.8847294, "z": 0.09879146}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_33d4fba0", "position": {"x": 0.8034234, "y": 0.9066806, "z": -0.176930517}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_288ff1e3", "position": {"x": 0.698199749, "y": 0.941000044, "z": -1.36019969}, "rotation": {"x": 0.0, "y": 270.000122, "z": 0.0}}, {"objectName": "Bowl_737b77b7", "position": {"x": 0.8582397, "y": 1.46226478, "z": 0.411470354}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2068316017, "scene_num": 21}, "task_id": "trial_T20190909_063813_353229", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A3PPLDHC3CG0YN_39U1BHVTDOIU1TAY121X6MH38LXT3T", "high_descs": ["Turn and walk to the fridge on the left. ", "Take the mug out of the fridge. ", "Turn around and walk to the window, turn left, face the microwave. ", "Put the mug in the microwave, heat the mug, take the mug out of the microwave. ", "Turn around and walk to the counter to the left of the sink. ", "Open the cabinet door under the counter to the left, put the mug in the cabinet on the right side , close the door. "], "task_desc": "To heat a mug and put it in the cabinet. ", "votes": [1, 1]}, {"assignment_id": "A16335MOISDG1F_3AAPLD8UCF89V1HXGNQZAGIDRQNTH8", "high_descs": ["Turn left and walk toward the grey trash can. Turn left and go to the refrigerator.", "Look in the refrigerator and take out the cup. ", "Turn around and head toward the microwave on your left", "Place the cup inside the microwave and turn it on. Pick up the cup from the microwave.", "Turn around and head to the stove. Turn to the right and walk over to the sink.", "Open the left cabinet under the sink. Put the cup inside the cabinet and close it."], "task_desc": "Take a cup out of the refrigerator, warm it in the microwave and put it under the sink.", "votes": [1, 1]}, {"assignment_id": "A2A028LRDJB7ZB_3SNVL38CI7JJBAGU0MY6YYY1VLSCKD", "high_descs": ["Turn left then head to the fridge", "Open the fridge take out the mug then close the fridge", "Turn right then head to the microwave", "Open the microwave then put in and out the mug in the microwave then close it again", "Turn left then head to the left side of the cabinet", "Open the left side of cabinet then put in the mug then close it "], "task_desc": "Put the heated mug in the cabinet", "votes": [1, 1]}]}}