{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 25}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "CounterTop", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|4|-3|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [6.6253524, 6.6253524, -5.1554866, -5.1554866, 3.6379384, 3.6379384]], "coordinateReceptacleObjectId": ["CounterTop", [3.956, 3.956, -6.064, -6.064, 3.7868, 3.7868]], "forceVisible": true, "objectId": "Cup|+01.66|+00.91|-01.29"}}, {"discrete_action": {"action": "HeatObject", "args": [""]}, "high_idx": 2, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [7.008, 7.008, -3.376, -3.376, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.75|+00.90|-00.84"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 3, "planner_action": {"action": "GotoLocation", "location": "loc|4|1|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "countertop"]}, "high_idx": 4, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [6.6253524, 6.6253524, -5.1554866, -5.1554866, 3.6379384, 3.6379384]], "coordinateReceptacleObjectId": ["CounterTop", [3.956, 3.956, 4.068, 4.068, 3.7844, 3.7844]], "forceVisible": true, "objectId": "Cup|+01.66|+00.91|-01.29", "receptacleObjectId": "CounterTop|+00.99|+00.95|+01.02"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 5, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+01.66|+00.91|-01.29"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [287, 112, 299, 158], "mask": [[33599, 1], [33899, 1], [34198, 2], [34498, 2], [34797, 3], [35097, 3], [35396, 4], [35696, 4], [35997, 3], [36297, 3], [36597, 3], [36896, 4], [37196, 4], [37496, 4], [37795, 5], [38095, 5], [38395, 5], [38694, 6], [38994, 6], [39294, 6], [39593, 7], [39893, 7], [40193, 7], [40492, 8], [40792, 8], [41092, 8], [41391, 9], [41691, 9], [41991, 9], [42290, 10], [42590, 10], [42889, 11], [43189, 11], [43489, 11], [43788, 12], [44088, 12], [44388, 12], [44687, 13], [44987, 13], [45288, 12], [45588, 12], [45889, 11], [46189, 11], [46490, 10], [46791, 4], [46797, 3], [47093, 1], [47394, 1]], "point": [293, 134]}}, "high_idx": 1}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [55, 26, 299, 160], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20457, 243], [20757, 243], [21057, 243], [21358, 242], [21658, 242], [21958, 242], [22258, 242], [22559, 241], [22859, 241], [23159, 241], [23459, 241], [23760, 240], [24060, 240], [24360, 240], [24661, 239], [24961, 239], [25261, 239], [25561, 239], [25862, 238], [26162, 238], [26462, 238], [26762, 238], [27063, 237], [27363, 237], [27663, 237], [27964, 236], [28264, 236], [28564, 236], [28864, 236], [29165, 235], [29465, 234], [29765, 234], [30066, 232], [30366, 232], [30666, 231], [30966, 231], [31267, 229], [31567, 229], [31867, 228], [32167, 228], [32468, 226], [32768, 226], [33068, 225], [33369, 224], [33669, 223], [33969, 223], [34269, 223], [34570, 221], [34870, 221], [35170, 220], [35470, 220], [35771, 218], [36071, 218], [36371, 217], [36672, 216], [36972, 215], [37272, 215], [37572, 214], [37873, 213], [38173, 212], [38473, 212], [38773, 211], [39074, 210], [39374, 209], [39674, 209], [39975, 207], [40275, 207], [40575, 206], [40875, 206], [41176, 204], [41476, 204], [41776, 204], [42076, 203], [42377, 202], [42677, 201], [42977, 201], [43278, 199], [43578, 199], [43878, 198], [44178, 198], [44485, 189], [44786, 187], [45086, 187], [45386, 186], [45686, 186], [45987, 184], [46287, 184], [46587, 183], [46887, 183], [47188, 59], [47253, 117], [47488, 52], [47560, 109], [47789, 44], [47867, 102]], "point": [177, 92]}}, "high_idx": 2}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+01.66|+00.91|-01.29", "placeStationary": true, "receptacleObjectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 26, 299, 300], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20456, 244], [20756, 244], [21055, 245], [21355, 245], [21654, 246], [21954, 246], [22253, 247], [22553, 247], [22852, 248], [23152, 248], [23451, 249], [23751, 249], [24050, 250], [24349, 251], [24649, 251], [24948, 252], [25248, 252], [25547, 253], [25847, 253], [26146, 254], [26446, 79], [26533, 167], [26745, 78], [26835, 165], [27045, 77], [27137, 163], [27344, 77], [27438, 162], [27644, 76], [27739, 161], [27943, 77], [28039, 161], [28243, 76], [28340, 160], [28542, 77], [28641, 159], [28842, 76], [28941, 159], [29141, 77], [29242, 158], [29441, 76], [29542, 157], [29740, 76], [29843, 156], [30039, 77], [30143, 155], [30339, 77], [30444, 154], [30638, 77], [30743, 154], [30938, 77], [31043, 154], [31237, 78], [31343, 153], [31537, 78], [31643, 153], [31836, 79], [31943, 152], [32136, 79], [32243, 152], [32435, 80], [32542, 152], [32735, 81], [32842, 152], [33034, 83], [33142, 151], [33334, 83], [33441, 152], [33633, 85], [33741, 151], [33933, 86], [34040, 152], [34232, 87], [34340, 152], [34532, 88], [34639, 152], [34831, 90], [34938, 153], [35131, 91], [35237, 153], [35430, 94], [35536, 154], [35730, 96], [35833, 156], [36029, 260], [36328, 260], [36628, 260], [36927, 260], [37227, 260], [37526, 260], [37826, 260], [38125, 260], [38425, 260], [38724, 260], [39024, 260], [39323, 260], [39623, 260], [39922, 260], [40222, 260], [40521, 260], [40821, 260], [41120, 260], [41420, 260], [41719, 261], [42018, 261], [42318, 261], [42617, 261], [42917, 261], [43216, 261], [43516, 261], [43815, 261], [44115, 261], [44414, 260], [44714, 259], [45013, 260], [45313, 79], [45531, 41], [45612, 80], [45831, 41], [45912, 80], [46132, 39], [46211, 81], [46432, 39], [46511, 80], [46732, 38], [46810, 81], [47033, 37], [47110, 81], [47333, 37], [47409, 82], [47633, 36], [47708, 82], [47934, 35], [48008, 82], [48307, 83], [48607, 83], [48906, 84], [49206, 83], [49505, 84], [49805, 84], [50104, 85], [50404, 84], [50703, 85], [51003, 85], [51302, 86], [51602, 86], [51901, 86], [52201, 86], [52500, 87], [52800, 87], [53100, 86], [53400, 86], [53700, 86], [54000, 86], [54300, 86], [54600, 85], [54900, 85], [55200, 85], [55500, 85], [55800, 84], [56100, 84], [56400, 84], [56700, 84], [57000, 84], [57300, 83], [57600, 83], [57900, 83], [58200, 83], [58500, 83], [58800, 82], [59100, 82], [59400, 82], [59700, 82], [60000, 81], [60300, 81], [60600, 81], [60900, 81], [61200, 81], [61500, 80], [61800, 80], [62100, 80], [62400, 80], [62700, 79], [63000, 79], [63300, 79], [63600, 79], [63900, 79], [64200, 78], [64500, 78], [64800, 78], [65100, 78], [65400, 77], [65700, 77], [66000, 77], [66300, 77], [66600, 77], [66900, 76], [67200, 76], [67500, 76], [67800, 76], [68100, 75], [68400, 75], [68700, 75], [69000, 75], [69300, 75], [69600, 74], [69900, 74], [70200, 74], [70500, 74], [70800, 73], [71100, 73], [71400, 73], [71700, 73], [72000, 73], [72300, 72], [72600, 72], [72900, 72], [73200, 72], [73500, 71], [73800, 71], [74100, 71], [74400, 71], [74700, 71], [75000, 70], [75300, 70], [75600, 70], [75900, 70], [76200, 69], [76500, 69], [76800, 69], [77100, 69], [77400, 69], [77700, 68], [78000, 68], [78300, 68], [78600, 68], [78900, 67], [79200, 67], [79500, 67], [79800, 67], [80100, 67], [80400, 66], [80700, 66], [81000, 66], [81300, 66], [81600, 65], [81900, 65], [82200, 65], [82500, 65], [82800, 65], [83100, 64], [83402, 62], [83703, 61], [84004, 60], [84305, 58], [84606, 57], [84907, 56], [85208, 55], [85509, 54], [85811, 51], [86112, 50], [86413, 49], [86714, 48], [87015, 47], [87316, 45], [87617, 44], [87919, 42], [88220, 41], [88521, 39], [88822, 39], [89125, 38], [89429, 36], [89730, 36]], "point": [149, 150]}}, "high_idx": 2}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 26, 299, 300], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20456, 244], [20756, 244], [21055, 245], [21355, 245], [21654, 246], [21954, 246], [22253, 247], [22553, 247], [22852, 248], [23152, 248], [23451, 249], [23751, 124], [23915, 85], [24050, 125], [24215, 85], [24349, 126], [24516, 84], [24649, 126], [24816, 84], [24948, 127], [25116, 84], [25248, 127], [25416, 84], [25547, 128], [25716, 84], [25847, 128], [26016, 84], [26146, 130], [26315, 85], [26446, 79], [26533, 43], [26615, 85], [26745, 78], [26835, 42], [26915, 85], [27045, 77], [27137, 40], [27214, 86], [27344, 77], [27438, 39], [27513, 87], [27644, 76], [27739, 38], [27812, 88], [27943, 77], [28039, 38], [28112, 88], [28243, 76], [28340, 38], [28411, 89], [28542, 77], [28641, 37], [28711, 89], [28842, 76], [28941, 37], [29011, 89], [29141, 77], [29242, 36], [29310, 90], [29441, 76], [29542, 36], [29610, 89], [29740, 76], [29843, 35], [29910, 89], [30039, 77], [30143, 35], [30209, 89], [30339, 77], [30444, 34], [30509, 89], [30638, 77], [30743, 35], [30809, 88], [30938, 77], [31043, 35], [31108, 89], [31237, 78], [31343, 35], [31408, 88], [31537, 78], [31643, 35], [31708, 88], [31836, 79], [31943, 35], [32007, 88], [32136, 79], [32243, 35], [32307, 88], [32435, 80], [32542, 36], [32607, 87], [32735, 81], [32842, 36], [32906, 88], [33034, 83], [33142, 36], [33206, 87], [33334, 83], [33441, 37], [33506, 87], [33633, 85], [33741, 37], [33805, 87], [33933, 86], [34040, 38], [34105, 87], [34232, 87], [34340, 38], [34405, 87], [34532, 88], [34639, 39], [34704, 87], [34831, 90], [34938, 40], [35004, 87], [35131, 91], [35237, 41], [35304, 86], [35430, 94], [35536, 42], [35603, 87], [35730, 96], [35833, 45], [35903, 86], [36029, 149], [36203, 86], [36328, 150], [36502, 86], [36628, 151], [36802, 86], [36927, 152], [37102, 85], [37227, 154], [37401, 86], [37526, 156], [37700, 86], [37826, 158], [37998, 88], [38125, 161], [38296, 89], [38425, 260], [38724, 260], [39024, 260], [39323, 260], [39623, 260], [39922, 260], [40222, 260], [40521, 260], [40821, 260], [41120, 260], [41420, 260], [41719, 261], [42018, 261], [42318, 261], [42617, 261], [42917, 261], [43216, 261], [43516, 261], [43815, 261], [44115, 261], [44414, 260], [44714, 259], [45013, 260], [45313, 79], [45531, 41], [45612, 80], [45831, 41], [45912, 80], [46132, 39], [46211, 81], [46432, 39], [46511, 80], [46732, 38], [46810, 81], [47033, 37], [47110, 81], [47333, 37], [47409, 82], [47633, 36], [47708, 82], [47934, 35], [48008, 82], [48307, 83], [48607, 83], [48906, 84], [49206, 83], [49505, 84], [49805, 84], [50104, 85], [50404, 84], [50703, 85], [51003, 85], [51302, 86], [51602, 86], [51901, 86], [52201, 86], [52500, 87], [52800, 87], [53100, 86], [53400, 86], [53700, 86], [54000, 86], [54300, 86], [54600, 85], [54900, 85], [55200, 85], [55500, 85], [55800, 84], [56100, 84], [56400, 84], [56700, 84], [57000, 84], [57300, 83], [57600, 83], [57900, 83], [58200, 83], [58500, 83], [58800, 82], [59100, 82], [59400, 82], [59700, 82], [60000, 81], [60300, 81], [60600, 81], [60900, 81], [61200, 81], [61500, 80], [61800, 80], [62100, 80], [62400, 80], [62700, 79], [63000, 79], [63300, 79], [63600, 79], [63900, 79], [64200, 78], [64500, 78], [64800, 78], [65100, 78], [65400, 77], [65700, 77], [66000, 77], [66300, 77], [66600, 77], [66900, 76], [67200, 76], [67500, 76], [67800, 76], [68100, 75], [68400, 75], [68700, 75], [69000, 75], [69300, 75], [69600, 74], [69900, 74], [70200, 74], [70500, 74], [70800, 73], [71100, 73], [71400, 73], [71700, 73], [72000, 73], [72300, 72], [72600, 72], [72900, 72], [73200, 72], [73500, 71], [73800, 71], [74100, 71], [74400, 71], [74700, 71], [75000, 70], [75300, 70], [75600, 70], [75900, 70], [76200, 69], [76500, 69], [76800, 69], [77100, 69], [77400, 69], [77700, 68], [78000, 68], [78300, 68], [78600, 68], [78900, 67], [79200, 67], [79500, 67], [79800, 67], [80100, 67], [80400, 66], [80700, 66], [81000, 66], [81300, 66], [81600, 65], [81900, 65], [82200, 65], [82500, 65], [82800, 65], [83100, 64], [83402, 62], [83703, 61], [84004, 60], [84305, 58], [84606, 57], [84907, 56], [85208, 55], [85509, 54], [85811, 51], [86112, 50], [86413, 49], [86714, 48], [87015, 47], [87316, 45], [87617, 44], [87919, 42], [88220, 41], [88521, 39], [88822, 39], [89125, 38], [89429, 36], [89730, 36]], "point": [149, 150]}}, "high_idx": 2}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [7.008, 7.008, -3.376, -3.376, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [55, 26, 299, 160], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20457, 243], [20757, 243], [21057, 243], [21358, 242], [21658, 242], [21958, 242], [22258, 242], [22559, 241], [22859, 241], [23159, 241], [23459, 241], [23760, 240], [24060, 240], [24360, 240], [24661, 239], [24961, 239], [25261, 239], [25561, 239], [25862, 238], [26162, 238], [26462, 238], [26762, 238], [27063, 237], [27363, 237], [27663, 237], [27964, 236], [28264, 236], [28564, 236], [28864, 236], [29165, 235], [29465, 234], [29765, 234], [30066, 232], [30366, 232], [30666, 231], [30966, 231], [31267, 229], [31567, 229], [31867, 228], [32167, 228], [32468, 226], [32768, 226], [33068, 225], [33369, 224], [33669, 223], [33969, 223], [34269, 223], [34570, 221], [34870, 221], [35170, 220], [35470, 220], [35771, 218], [36071, 218], [36371, 217], [36672, 216], [36972, 215], [37272, 215], [37572, 214], [37873, 213], [38173, 212], [38473, 212], [38773, 211], [39074, 210], [39374, 209], [39674, 209], [39975, 207], [40275, 207], [40575, 206], [40875, 206], [41176, 204], [41476, 204], [41776, 204], [42076, 203], [42377, 202], [42677, 201], [42977, 201], [43278, 199], [43578, 199], [43878, 198], [44178, 198], [44485, 189], [44786, 187], [45086, 187], [45386, 186], [45686, 186], [45987, 184], [46287, 184], [46587, 183], [46887, 183], [47188, 182], [47488, 181], [47789, 180]], "point": [177, 92]}}, "high_idx": 2}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [7.008, 7.008, -3.376, -3.376, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [55, 26, 299, 160], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20457, 243], [20757, 243], [21057, 243], [21358, 242], [21658, 242], [21958, 242], [22258, 242], [22559, 241], [22859, 241], [23159, 241], [23459, 241], [23760, 240], [24060, 240], [24360, 240], [24661, 239], [24961, 239], [25261, 239], [25561, 239], [25862, 238], [26162, 238], [26462, 238], [26762, 238], [27063, 237], [27363, 237], [27663, 237], [27964, 236], [28264, 236], [28564, 236], [28864, 236], [29165, 235], [29465, 234], [29765, 234], [30066, 232], [30366, 232], [30666, 231], [30966, 231], [31267, 229], [31567, 229], [31867, 228], [32167, 228], [32468, 226], [32768, 226], [33068, 225], [33369, 224], [33669, 223], [33969, 223], [34269, 223], [34570, 221], [34870, 221], [35170, 220], [35470, 220], [35771, 218], [36071, 218], [36371, 217], [36672, 216], [36972, 215], [37272, 215], [37572, 214], [37873, 213], [38173, 212], [38473, 212], [38773, 211], [39074, 210], [39374, 209], [39674, 209], [39975, 207], [40275, 207], [40575, 206], [40875, 206], [41176, 204], [41476, 204], [41776, 204], [42076, 203], [42377, 202], [42677, 201], [42977, 201], [43278, 199], [43578, 199], [43878, 198], [44178, 198], [44485, 189], [44786, 187], [45086, 187], [45386, 186], [45686, 186], [45987, 184], [46287, 184], [46587, 183], [46887, 183], [47188, 182], [47488, 181], [47789, 180]], "point": [177, 92]}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [55, 26, 299, 160], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20457, 243], [20757, 243], [21057, 243], [21358, 242], [21658, 242], [21958, 242], [22258, 242], [22559, 241], [22859, 241], [23159, 241], [23459, 241], [23760, 240], [24060, 240], [24360, 240], [24661, 239], [24961, 239], [25261, 239], [25561, 239], [25862, 238], [26162, 238], [26462, 238], [26762, 238], [27063, 237], [27363, 237], [27663, 237], [27964, 236], [28264, 236], [28564, 236], [28864, 236], [29165, 235], [29465, 234], [29765, 234], [30066, 232], [30366, 232], [30666, 231], [30966, 231], [31267, 229], [31567, 229], [31867, 228], [32167, 228], [32468, 226], [32768, 226], [33068, 225], [33369, 224], [33669, 223], [33969, 223], [34269, 223], [34570, 221], [34870, 221], [35170, 220], [35470, 220], [35771, 218], [36071, 218], [36371, 217], [36672, 216], [36972, 215], [37272, 215], [37572, 214], [37873, 213], [38173, 212], [38473, 212], [38773, 211], [39074, 210], [39374, 209], [39674, 209], [39975, 207], [40275, 207], [40575, 206], [40875, 206], [41176, 204], [41476, 204], [41776, 204], [42076, 203], [42377, 202], [42677, 201], [42977, 201], [43278, 199], [43578, 199], [43878, 198], [44178, 198], [44485, 189], [44786, 187], [45086, 187], [45386, 186], [45686, 186], [45987, 184], [46287, 184], [46587, 183], [46887, 183], [47188, 182], [47488, 181], [47789, 180]], "point": [177, 92]}}, "high_idx": 2}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+01.66|+00.91|-01.29"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [175, 80, 215, 128], "mask": [[23875, 40], [24175, 40], [24475, 41], [24775, 41], [25075, 41], [25375, 41], [25675, 41], [25975, 41], [26276, 39], [26576, 39], [26877, 38], [27177, 37], [27477, 36], [27777, 35], [28077, 35], [28378, 33], [28678, 33], [28978, 33], [29278, 32], [29578, 32], [29878, 32], [30178, 31], [30478, 31], [30778, 31], [31078, 30], [31378, 30], [31678, 30], [31978, 29], [32278, 29], [32578, 29], [32878, 28], [33178, 28], [33478, 28], [33778, 27], [34078, 27], [34378, 27], [34678, 26], [34978, 26], [35278, 26], [35578, 25], [35878, 25], [36178, 25], [36478, 24], [36779, 23], [37079, 23], [37381, 20], [37682, 18], [37984, 14], [38286, 10]], "point": [195, 103]}}, "high_idx": 2}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 26, 299, 300], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20456, 244], [20756, 244], [21055, 245], [21355, 245], [21654, 246], [21954, 246], [22253, 247], [22553, 247], [22852, 248], [23152, 248], [23451, 249], [23751, 249], [24050, 250], [24349, 251], [24649, 251], [24948, 252], [25248, 252], [25547, 253], [25847, 253], [26146, 254], [26446, 79], [26533, 167], [26745, 78], [26835, 165], [27045, 77], [27137, 163], [27344, 77], [27438, 162], [27644, 76], [27739, 161], [27943, 77], [28039, 161], [28243, 76], [28340, 160], [28542, 77], [28641, 159], [28842, 76], [28941, 159], [29141, 77], [29242, 158], [29441, 76], [29542, 157], [29740, 76], [29843, 156], [30039, 77], [30143, 155], [30339, 77], [30444, 154], [30638, 77], [30743, 154], [30938, 77], [31043, 154], [31237, 78], [31343, 153], [31537, 78], [31643, 153], [31836, 79], [31943, 152], [32136, 79], [32243, 152], [32435, 80], [32542, 152], [32735, 81], [32842, 152], [33034, 83], [33142, 151], [33334, 83], [33441, 152], [33633, 85], [33741, 151], [33933, 86], [34040, 152], [34232, 87], [34340, 152], [34532, 88], [34639, 152], [34831, 90], [34938, 153], [35131, 91], [35237, 153], [35430, 94], [35536, 154], [35730, 96], [35833, 156], [36029, 260], [36328, 260], [36628, 260], [36927, 260], [37227, 260], [37526, 260], [37826, 260], [38125, 260], [38425, 260], [38724, 260], [39024, 260], [39323, 260], [39623, 260], [39922, 260], [40222, 260], [40521, 260], [40821, 260], [41120, 260], [41420, 260], [41719, 261], [42018, 261], [42318, 261], [42617, 261], [42917, 261], [43216, 261], [43516, 261], [43815, 261], [44115, 261], [44414, 260], [44714, 259], [45013, 260], [45313, 79], [45531, 41], [45612, 80], [45831, 41], [45912, 80], [46132, 39], [46211, 81], [46432, 39], [46511, 80], [46732, 38], [46810, 81], [47033, 37], [47110, 81], [47333, 37], [47409, 82], [47633, 36], [47708, 82], [47934, 35], [48008, 82], [48307, 83], [48607, 83], [48906, 84], [49206, 83], [49505, 84], [49805, 84], [50104, 85], [50404, 84], [50703, 85], [51003, 85], [51302, 86], [51602, 86], [51901, 86], [52201, 86], [52500, 87], [52800, 87], [53100, 86], [53400, 86], [53700, 86], [54000, 86], [54300, 86], [54600, 85], [54900, 85], [55200, 85], [55500, 85], [55800, 84], [56100, 84], [56400, 84], [56700, 84], [57000, 84], [57300, 83], [57600, 83], [57900, 83], [58200, 83], [58500, 83], [58800, 82], [59100, 82], [59400, 82], [59700, 82], [60000, 81], [60300, 81], [60600, 81], [60900, 81], [61200, 81], [61500, 80], [61800, 80], [62100, 80], [62400, 80], [62700, 79], [63000, 79], [63300, 79], [63600, 79], [63900, 79], [64200, 78], [64500, 78], [64800, 78], [65100, 78], [65400, 77], [65700, 77], [66000, 77], [66300, 77], [66600, 77], [66900, 76], [67200, 76], [67500, 76], [67800, 76], [68100, 75], [68400, 75], [68700, 75], [69000, 75], [69300, 75], [69600, 74], [69900, 74], [70200, 74], [70500, 74], [70800, 73], [71100, 73], [71400, 73], [71700, 73], [72000, 73], [72300, 72], [72600, 72], [72900, 72], [73200, 72], [73500, 71], [73800, 71], [74100, 71], [74400, 71], [74700, 71], [75000, 70], [75300, 70], [75600, 70], [75900, 70], [76200, 69], [76500, 69], [76800, 69], [77100, 69], [77400, 69], [77700, 68], [78000, 68], [78300, 68], [78600, 68], [78900, 67], [79200, 67], [79500, 67], [79800, 67], [80100, 67], [80400, 66], [80700, 66], [81000, 66], [81300, 66], [81600, 65], [81900, 65], [82200, 65], [82500, 65], [82800, 65], [83100, 64], [83402, 62], [83703, 61], [84004, 60], [84305, 58], [84606, 57], [84907, 56], [85208, 55], [85509, 54], [85811, 51], [86112, 50], [86413, 49], [86714, 48], [87015, 47], [87316, 45], [87617, 44], [87919, 42], [88220, 41], [88521, 39], [88822, 39], [89125, 38], [89429, 36], [89730, 36]], "point": [149, 150]}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 3}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 3}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 3}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 3}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+01.66|+00.91|-01.29", "placeStationary": true, "receptacleObjectId": "CounterTop|+00.99|+00.95|+01.02"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 89, 299, 273], "mask": [[26400, 92], [26558, 37], [26629, 67], [26700, 38], [26739, 52], [26858, 34], [26931, 65], [27000, 36], [27040, 49], [27158, 33], [27233, 62], [27300, 35], [27340, 48], [27457, 33], [27535, 60], [27600, 35], [27639, 48], [27757, 32], [27837, 58], [27900, 34], [27939, 47], [28056, 32], [28138, 56], [28200, 33], [28238, 47], [28355, 32], [28440, 54], [28500, 33], [28538, 46], [28654, 33], [28741, 50], [28800, 33], [28837, 47], [28952, 35], [29042, 41], [29100, 32], [29136, 48], [29243, 1], [29249, 37], [29343, 38], [29400, 32], [29436, 48], [29543, 43], [29643, 37], [29700, 31], [29735, 49], [29843, 43], [29944, 35], [30000, 31], [30034, 50], [30143, 43], [30244, 35], [30300, 30], [30334, 50], [30443, 43], [30545, 34], [30600, 30], [30633, 51], [30743, 44], [30845, 33], [30900, 29], [30932, 52], [31043, 44], [31145, 33], [31200, 29], [31232, 52], [31343, 44], [31445, 32], [31500, 28], [31531, 54], [31643, 44], [31745, 32], [31800, 27], [31830, 55], [31942, 46], [32045, 32], [32100, 26], [32130, 55], [32242, 46], [32345, 31], [32400, 26], [32429, 57], [32542, 46], [32645, 31], [32700, 25], [32729, 58], [32841, 48], [32944, 31], [33000, 24], [33028, 60], [33141, 48], [33244, 31], [33300, 23], [33328, 61], [33441, 49], [33543, 31], [33600, 22], [33627, 63], [33740, 51], [33842, 32], [33900, 21], [33927, 64], [34039, 53], [34142, 32], [34200, 21], [34226, 67], [34339, 54], [34441, 32], [34500, 20], [34526, 68], [34638, 56], [34740, 33], [34800, 19], [34825, 71], [34937, 59], [35039, 33], [35100, 19], [35125, 72], [35236, 61], [35338, 34], [35400, 18], [35424, 75], [35533, 66], [35637, 35], [35700, 18], [35723, 78], [35830, 71], [35935, 36], [36000, 18], [36023, 80], [36127, 77], [36233, 38], [36300, 17], [36322, 84], [36425, 82], [36530, 40], [36600, 17], [36622, 92], [36721, 89], [36826, 44], [36900, 17], [36921, 189], [37114, 7], [37126, 43], [37200, 17], [37221, 190], [37414, 7], [37426, 43], [37500, 17], [37520, 191], [37714, 7], [37727, 42], [37800, 17], [37820, 191], [38015, 6], [38027, 42], [38100, 211], [38315, 7], [38327, 41], [38400, 212], [38615, 7], [38628, 40], [38699, 213], [38916, 6], [38928, 40], [38999, 213], [39216, 6], [39228, 41], [39298, 215], [39516, 7], [39529, 40], [39598, 215], [39817, 6], [39829, 40], [39897, 216], [40117, 6], [40129, 40], [40197, 216], [40417, 7], [40430, 40], [40496, 218], [40717, 7], [40730, 41], [40795, 219], [41017, 7], [41030, 41], [41095, 220], [41317, 7], [41331, 41], [41394, 221], [41617, 8], [41627, 1], [41631, 42], [41694, 222], [41918, 7], [41928, 1], [41932, 42], [41993, 223], [42218, 7], [42232, 43], [42293, 223], [42518, 9], [42530, 46], [42592, 224], [42818, 60], [42891, 225], [43119, 61], [43191, 226], [43419, 63], [43489, 228], [43719, 298], [44019, 298], [44320, 297], [44620, 298], [44920, 298], [45220, 298], [45521, 297], [45821, 297], [46121, 298], [46422, 297], [46722, 297], [47022, 225], [47253, 66], [47322, 218], [47560, 56], [47626, 207], [47867, 47], [47929, 200], [48171, 42], [48231, 196], [48473, 40], [48531, 194], [48775, 38], [48831, 192], [49077, 37], [49132, 189], [49379, 35], [49432, 187], [49681, 33], [49733, 184], [49983, 32], [50033, 181], [50286, 29], [50334, 178], [50588, 27], [50634, 177], [50889, 27], [50934, 176], [51190, 26], [51235, 174], [51491, 25], [51535, 173], [51792, 25], [51835, 172], [52093, 25], [52135, 171], [52394, 211], [52695, 209], [52996, 207], [53297, 146], [53453, 49], [53598, 136], [53753, 48], [53899, 133], [54053, 47], [54200, 131], [54352, 47], [54501, 129], [54652, 46], [54802, 127], [54952, 46], [55102, 126], [55251, 46], [55403, 124], [55551, 46], [55710, 90], [55851, 45], [56019, 81], [56150, 46], [56326, 89], [56450, 46], [56604, 4], [56633, 92], [56750, 45], [56905, 11], [56936, 89], [57049, 46], [57205, 17], [57237, 88], [57349, 45], [57506, 22], [57537, 89], [57649, 45], [57806, 27], [57835, 91], [57947, 47], [58106, 120], [58240, 53], [58407, 120], [58530, 63], [58707, 185], [59008, 184], [59308, 183], [59609, 182], [59909, 182], [60209, 182], [60509, 182], [60809, 182], [61109, 182], [61409, 182], [61709, 182], [62009, 182], [62309, 182], [62609, 182], [62909, 182], [63209, 182], [63509, 182], [63809, 182], [64109, 182], [64409, 182], [64709, 91], [65077, 23], [65378, 22], [65678, 22], [65979, 21], [66279, 21], [66579, 21], [66880, 20], [67180, 20], [67480, 20], [67781, 19], [68081, 19], [68382, 18], [68682, 18], [68982, 18], [69283, 17], [69583, 17], [69884, 16], [70184, 16], [70484, 16], [70785, 15], [71085, 15], [71386, 14], [71686, 14], [71986, 14], [72287, 13], [72587, 13], [72887, 13], [73188, 12], [73488, 12], [73789, 11], [74089, 11], [74389, 11], [74690, 10], [74990, 10], [75291, 9], [75591, 9], [75891, 9], [76192, 8], [76492, 8], [76793, 7], [77093, 7], [77393, 7], [77694, 6], [77994, 6], [78295, 5], [78595, 5], [78895, 5], [79196, 4], [79496, 4], [79796, 4], [80097, 3], [80397, 3], [80698, 2], [80998, 2], [81298, 2], [81599, 1], [81899, 1]], "point": [146, 157]}}, "high_idx": 4}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan14", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -1.25, "y": 0.9009992, "z": -0.75}, "object_poses": [{"objectName": "Potato_6d0d5c3c", "position": {"x": 1.73873091, "y": 1.055655, "z": -0.6760015}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "ButterKnife_38537d23", "position": {"x": 0.476415873, "y": 0.9106421, "z": 1.18092084}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "DishSponge_00484768", "position": {"x": 1.80037653, "y": 0.06976977, "z": 1.82753634}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "DishSponge_00484768", "position": {"x": 1.0978719, "y": 0.910764158, "z": -1.230383}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Fork_192d20c7", "position": {"x": 1.03216958, "y": 0.7609978, "z": -1.54422021}, "rotation": {"x": 0.0, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Spoon_89592edf", "position": {"x": 1.656339, "y": 0.9123293, "z": -1.63161}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "Spatula_83c41bbf", "position": {"x": 1.58645487, "y": 0.9261998, "z": -1.46024108}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spatula_83c41bbf", "position": {"x": 1.24529183, "y": 0.9255999, "z": 1.00428259}, "rotation": {"x": 0.0, "y": 269.999817, "z": 0.0}}, {"objectName": "Cup_5ecc05ec", "position": {"x": -0.6929714, "y": 0.4587565, "z": -1.67017055}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_5ecc05ec", "position": {"x": -0.790139735, "y": 1.3871752, "z": -1.55943775}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_06a240e2", "position": {"x": 1.75788581, "y": 0.9324337, "z": 0.9117683}, "rotation": {"x": 359.9535, "y": 90.1122055, "z": 353.578857}}, {"objectName": "Knife_06a240e2", "position": {"x": 0.2201252, "y": 0.9430372, "z": 0.8276391}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_fd471042", "position": {"x": 1.50158358, "y": 0.9087641, "z": 1.00428343}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_fd471042", "position": {"x": 1.80037653, "y": 0.06836966, "z": 1.63615465}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bowl_7266264e", "position": {"x": 1.607002, "y": 0.909407, "z": 0.4152559}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_b3498f3e", "position": {"x": 1.35536587, "y": 0.906199932, "z": -1.26489353}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_b3498f3e", "position": {"x": 1.91716409, "y": 0.905599952, "z": 0.5189743}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "PepperShaker_3caefad1", "position": {"x": 1.8097, "y": 2.13104033, "z": 1.08300924}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_3caefad1", "position": {"x": 1.757875, "y": 0.9056, "z": 1.18092465}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Mug_f9995e03", "position": {"x": 0.502, "y": 0.940499961, "z": -1.4606}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f9995e03", "position": {"x": -0.8387238, "y": 1.08652008, "z": -1.55943775}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_5b80d804", "position": {"x": 0.988999248, "y": 0.967907, "z": 1.26924264}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_e8bf9352", "position": {"x": 1.80289912, "y": 1.647132, "z": -0.0005006564}, "rotation": {"x": 1.76055928e-05, "y": -0.000203620235, "z": 6.826887e-05}}, {"objectName": "SoapBottle_fd471042", "position": {"x": 1.7333883, "y": 2.13546228, "z": 0.8429922}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_5ecc05ec", "position": {"x": 1.6563381, "y": 0.9094846, "z": -1.28887165}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Egg_85efe221", "position": {"x": -0.6929714, "y": 0.4956351, "z": -1.34809732}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_192d20c7", "position": {"x": 1.72622216, "y": 0.911748469, "z": -1.37455606}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "Ladle_4b0a72cf", "position": {"x": 1.20112586, "y": 0.9266982, "z": 0.7435301}, "rotation": {"x": 3.3463583, "y": 0.08807942, "z": 346.984375}}, {"objectName": "Apple_5b80d804", "position": {"x": -0.6929716, "y": 0.807950854, "z": -1.66452444}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b3498f3e", "position": {"x": 1.77044845, "y": 1.62814248, "z": -0.9739291}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_7013969f", "position": {"x": 1.28159308, "y": 0.900000036, "z": 1.19070256}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_817a54e2", "position": {"x": 0.220123827, "y": 0.974455357, "z": 1.26924038}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spatula_83c41bbf", "position": {"x": 0.476417363, "y": 0.9255999, "z": 0.7393196}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "Potato_6d0d5c3c", "position": {"x": -0.295593828, "y": 1.09940088, "z": -1.38275528}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_06a240e2", "position": {"x": 1.86598992, "y": 0.943637133, "z": -1.46024013}, "rotation": {"x": 0.0, "y": -0.000160509444, "z": 0.0}}, {"objectName": "ButterKnife_38537d23", "position": {"x": 0.296, "y": 0.9, "z": -1.296}, "rotation": {"x": 0.0, "y": 14.5689316, "z": 0.0}}, {"objectName": "Bread_8f8a19bf", "position": {"x": 1.61379027, "y": 0.977087, "z": 0.7593968}, "rotation": {"x": 359.0393, "y": 50.75687, "z": 0.178034008}}, {"objectName": "Lettuce_e61d6e1d", "position": {"x": 0.85669595, "y": 0.989343941, "z": 1.12062061}, "rotation": {"x": 0.194773674, "y": 270.009369, "z": 1.529223}}, {"objectName": "PepperShaker_3caefad1", "position": {"x": 1.26953459, "y": 0.906199932, "z": -1.24763823}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pot_bb5e3511", "position": {"x": 1.807, "y": 0.9098, "z": -0.3203}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "DishSponge_00484768", "position": {"x": 1.79610693, "y": 0.910764158, "z": -1.7172941}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spoon_89592edf", "position": {"x": 0.894013643, "y": 0.7618923, "z": -1.5995}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Bowl_7266264e", "position": {"x": 1.80555415, "y": 1.65271318, "z": -0.449611485}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f9995e03", "position": {"x": 1.86598945, "y": 0.906199932, "z": -1.288871}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}], "object_toggles": [], "random_seed": 1150160440, "scene_num": 14}, "task_id": "trial_T20190908_215907_972581", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A3MLUEOP3CCLXL_31EUONYN2YUY0A79PVL4V73Z0TVVOK", "high_descs": ["Turn right and go to the microwave", "Pick up the green cup that is sitting to the right of the microwave", "Open the microwave and place the green cup inside, to the right of the potato then close the door and turn it on. Open the microwave and pick up the green cup and close the door", "Turn left and walk to the counter in front of you", "Place the cup on the counter in front of the head of lettuce"], "task_desc": "Place a heated cup onto a counter", "votes": [1, 1]}, {"assignment_id": "A272X64FOZFYLB_3AUQQEL7U8KDXTFQNAJT567H2QH0V1", "high_descs": ["Turn right and walk across the room to the microwave.", "Pick up the green cup that's to the right of the microwave.", "Heat the cup in the microwave for a few seconds, then take it out and close the microwave.", "Turn left and walk forward to the counter containing the head of lettuce.", "Put the cup on the counter, in front of the lettuce."], "task_desc": "Place a hot cup on a counter top.", "votes": [1, 1]}, {"assignment_id": "A2A028LRDJB7ZB_3L2IS5HSFD9G5AIXJQBMKW1BWCGNU7", "high_descs": ["Turn right then head to the counter beside the microwave", "Pick up the green cup on the right side of the counter", "Open the microwave put in the cup, take it out then close the microwave ", "Turn left head forward on the left counter", "Put the cup on the counter beside the cabbage"], "task_desc": "Put the heated cup on the counter", "votes": [1, 1]}]}}