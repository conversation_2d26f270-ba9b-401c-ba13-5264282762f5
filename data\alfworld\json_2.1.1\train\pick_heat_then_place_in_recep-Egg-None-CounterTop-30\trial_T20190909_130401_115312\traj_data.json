{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 1, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000033.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000034.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 2}, {"high_idx": 2, "image_name": "000000037.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000038.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000039.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000040.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000041.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000042.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000043.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000044.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000045.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000046.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000047.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000048.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000049.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 7}, {"high_idx": 3, "image_name": "000000074.png", "low_idx": 8}, {"high_idx": 3, "image_name": "000000075.png", "low_idx": 8}, {"high_idx": 3, "image_name": "000000076.png", "low_idx": 8}, {"high_idx": 3, "image_name": "000000077.png", "low_idx": 8}, {"high_idx": 3, "image_name": "000000078.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000079.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000080.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000081.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000082.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000083.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000084.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000085.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000086.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000087.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000088.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000089.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000090.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000091.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000092.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000093.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000094.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000176.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000177.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000178.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000179.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000187.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000188.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000189.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000190.png", "low_idx": 23}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "CounterTop", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-1|3|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-3.159872, -3.159872, 3.385628224, 3.385628224, 3.8113404, 3.8113404]], "coordinateReceptacleObjectId": ["CounterTop", [-4.0316868, -4.0316868, -0.1576, -0.1576, 3.7432, 3.7432]], "forceVisible": true, "objectId": "Egg|-00.79|+00.95|+00.85"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-1|5|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-4.136, -4.136, 5.716, 5.716, 3.5567708, 3.5567708]], "forceVisible": true, "objectId": "Microwave|-01.03|+00.89|+01.43"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-1|0|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "countertop"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-3.159872, -3.159872, 3.385628224, 3.385628224, 3.8113404, 3.8113404]], "coordinateReceptacleObjectId": ["CounterTop", [-4.0316868, -4.0316868, -0.1576, -0.1576, 3.7432, 3.7432]], "forceVisible": true, "objectId": "Egg|-00.79|+00.95|+00.85", "receptacleObjectId": "CounterTop|-01.01|+00.94|-00.04"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.79|+00.95|+00.85"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [167, 97, 191, 125], "mask": [[28977, 6], [29275, 10], [29573, 14], [29872, 16], [30171, 17], [30470, 19], [30770, 20], [31069, 21], [31368, 23], [31668, 23], [31968, 23], [32267, 25], [32567, 25], [32867, 25], [33167, 25], [33467, 25], [33767, 25], [34067, 25], [34367, 24], [34667, 24], [34967, 24], [35268, 22], [35568, 22], [35869, 20], [36169, 19], [36470, 18], [36771, 15], [37073, 12], [37375, 8]], "point": [179, 110]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [103, 21, 299, 168], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22304, 196], [22604, 196], [22904, 196], [23204, 196], [23504, 196], [23804, 196], [24104, 196], [24405, 195], [24705, 195], [25005, 195], [25305, 195], [25605, 195], [25905, 195], [26205, 195], [26505, 195], [26805, 195], [27106, 194], [27406, 194], [27706, 194], [28006, 194], [28306, 194], [28606, 194], [28906, 194], [29206, 194], [29507, 193], [29807, 193], [30107, 193], [30407, 193], [30707, 193], [31007, 193], [31307, 193], [31608, 192], [31908, 192], [32208, 192], [32508, 192], [32808, 192], [33108, 192], [33408, 192], [33709, 191], [34009, 191], [34309, 191], [34609, 191], [34909, 191], [35209, 191], [35509, 191], [35810, 190], [36110, 190], [36410, 190], [36710, 190], [37010, 190], [37310, 190], [37610, 190], [37911, 189], [38211, 189], [38511, 189], [38811, 189], [39111, 189], [39411, 189], [39711, 189], [40012, 188], [40312, 188], [40612, 187], [40912, 187], [41212, 186], [41512, 186], [41812, 185], [42112, 184], [42413, 183], [42713, 182], [43013, 182], [43313, 181], [43613, 181], [43913, 180], [44213, 180], [44514, 178], [44814, 178], [45114, 177], [45414, 176], [45714, 176], [46014, 175], [46314, 175], [46615, 173], [46915, 173], [47215, 172], [47515, 172], [47815, 171], [48115, 171], [48416, 169], [48716, 169], [49016, 168], [49316, 168], [49616, 167], [49916, 167], [50217, 165]], "point": [201, 93]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.79|+00.95|+00.85", "placeStationary": true, "receptacleObjectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [37, 21, 299, 294], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22294, 206], [22593, 207], [22893, 207], [23193, 207], [23492, 208], [23792, 208], [24092, 208], [24391, 209], [24691, 209], [24991, 209], [25291, 209], [25590, 210], [25890, 63], [25980, 21], [26019, 81], [26190, 62], [26281, 18], [26321, 79], [26489, 62], [26581, 17], [26623, 77], [26789, 62], [26882, 15], [26924, 76], [27089, 61], [27182, 14], [27225, 75], [27388, 62], [27483, 13], [27526, 74], [27688, 62], [27783, 12], [27827, 73], [27988, 61], [28083, 12], [28127, 73], [28287, 62], [28383, 11], [28427, 73], [28587, 62], [28683, 11], [28728, 72], [28887, 62], [28983, 11], [29028, 72], [29187, 62], [29283, 11], [29328, 72], [29486, 63], [29583, 11], [29628, 72], [29786, 63], [29883, 11], [29928, 72], [30086, 64], [30183, 10], [30228, 72], [30385, 65], [30482, 11], [30528, 72], [30685, 65], [30782, 11], [30828, 72], [30985, 66], [31082, 11], [31127, 73], [31284, 67], [31381, 12], [31427, 73], [31584, 68], [31681, 12], [31727, 73], [31884, 69], [31980, 14], [32027, 73], [32183, 70], [32280, 14], [32326, 74], [32483, 71], [32579, 15], [32626, 74], [32783, 72], [32878, 16], [32925, 75], [33083, 73], [33177, 18], [33224, 76], [33382, 75], [33476, 19], [33524, 76], [33682, 77], [33774, 22], [33823, 77], [33982, 80], [34072, 25], [34122, 78], [34281, 116], [34421, 79], [34581, 118], [34720, 80], [34881, 119], [35019, 81], [35180, 122], [35317, 83], [35480, 125], [35615, 85], [35780, 220], [36080, 220], [36379, 221], [36679, 221], [36979, 221], [37278, 222], [37578, 222], [37878, 222], [38177, 223], [38477, 223], [38777, 223], [39076, 224], [39376, 224], [39676, 224], [39976, 224], [40275, 225], [40575, 224], [40875, 224], [41174, 224], [41474, 224], [41774, 223], [42073, 223], [42373, 223], [42673, 222], [42972, 223], [43272, 222], [43572, 222], [43872, 221], [44171, 222], [44471, 221], [44771, 221], [45070, 221], [45370, 220], [45670, 220], [45969, 220], [46269, 220], [46569, 219], [46868, 220], [47168, 219], [47468, 219], [47768, 218], [48067, 219], [48367, 218], [48667, 218], [48966, 218], [49266, 218], [49566, 217], [49865, 218], [50165, 217], [50465, 52], [50765, 52], [51064, 53], [51364, 53], [51664, 53], [51963, 54], [52263, 53], [52563, 53], [52862, 54], [53162, 54], [53462, 54], [53761, 55], [54061, 55], [54361, 55], [54661, 55], [54960, 55], [55260, 55], [55560, 55], [55859, 56], [56159, 56], [56459, 56], [56758, 57], [57058, 57], [57358, 56], [57657, 57], [57957, 57], [58257, 57], [58557, 57], [58856, 58], [59156, 58], [59456, 58], [59755, 59], [60055, 58], [60355, 58], [60654, 59], [60954, 59], [61254, 59], [61553, 60], [61853, 60], [62153, 60], [62453, 59], [62752, 60], [63052, 60], [63352, 60], [63651, 61], [63951, 61], [64251, 61], [64550, 62], [64841, 71], [65141, 70], [65440, 71], [65740, 71], [66040, 71], [66339, 72], [66639, 72], [66939, 72], [67238, 73], [67538, 72], [67838, 72], [68138, 72], [68438, 72], [68738, 72], [69037, 73], [69337, 73], [69637, 73], [69938, 72], [70238, 71], [70538, 71], [70838, 71], [71138, 71], [71438, 71], [71738, 71], [72038, 71], [72338, 71], [72639, 69], [72939, 69], [73239, 69], [73539, 69], [73839, 69], [74139, 69], [74440, 68], [74740, 68], [75040, 68], [75341, 66], [75641, 66], [75941, 66], [76241, 66], [76542, 65], [76842, 65], [77142, 65], [77443, 64], [77743, 63], [78044, 62], [78344, 62], [78644, 62], [78945, 61], [79245, 61], [79546, 60], [79847, 59], [80148, 57], [80448, 57], [80749, 56], [81050, 55], [81351, 54], [81653, 52], [81954, 51], [82255, 50], [82556, 49], [82858, 46], [83159, 45], [83460, 44], [83762, 42], [84064, 40], [84365, 39], [84667, 37], [84969, 35], [85271, 32], [85573, 30], [85878, 28], [86179, 29], [86479, 31], [86780, 23], [86804, 6], [87080, 23], [87105, 5], [87381, 22], [87682, 21], [87982, 20]], "point": [168, 156]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [37, 21, 299, 294], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22294, 206], [22593, 207], [22893, 207], [23193, 207], [23492, 208], [23792, 208], [24092, 208], [24391, 209], [24691, 209], [24991, 209], [25291, 209], [25590, 210], [25890, 63], [25980, 21], [26019, 81], [26190, 62], [26281, 18], [26321, 79], [26489, 62], [26581, 17], [26623, 77], [26789, 62], [26882, 15], [26924, 76], [27089, 61], [27182, 14], [27225, 75], [27388, 62], [27483, 13], [27526, 74], [27688, 62], [27783, 12], [27827, 73], [27988, 61], [28083, 12], [28127, 73], [28287, 62], [28383, 11], [28427, 73], [28587, 62], [28683, 11], [28728, 72], [28887, 62], [28983, 11], [29028, 72], [29187, 62], [29283, 11], [29328, 72], [29486, 63], [29583, 11], [29628, 72], [29786, 48], [29841, 8], [29883, 11], [29928, 72], [30086, 46], [30142, 8], [30183, 10], [30228, 72], [30385, 46], [30444, 6], [30482, 11], [30528, 72], [30685, 45], [30745, 5], [30782, 11], [30828, 72], [30985, 44], [31045, 6], [31082, 11], [31127, 73], [31284, 44], [31346, 5], [31381, 12], [31427, 73], [31584, 44], [31647, 5], [31681, 12], [31727, 73], [31884, 43], [31947, 6], [31980, 14], [32027, 73], [32183, 44], [32248, 5], [32280, 14], [32326, 74], [32483, 43], [32548, 6], [32579, 15], [32626, 74], [32783, 43], [32848, 7], [32878, 16], [32925, 75], [33083, 43], [33149, 7], [33177, 18], [33224, 76], [33382, 44], [33449, 8], [33476, 19], [33524, 76], [33682, 44], [33749, 10], [33774, 22], [33823, 77], [33982, 44], [34049, 13], [34072, 25], [34122, 78], [34281, 45], [34349, 48], [34421, 79], [34581, 45], [34649, 50], [34720, 80], [34881, 45], [34949, 51], [35019, 81], [35180, 46], [35249, 53], [35317, 83], [35480, 46], [35549, 56], [35615, 85], [35780, 46], [35848, 152], [36080, 47], [36148, 152], [36379, 48], [36448, 152], [36679, 49], [36747, 153], [36979, 50], [37046, 154], [37278, 52], [37345, 155], [37578, 53], [37644, 156], [37878, 54], [37943, 157], [38177, 57], [38241, 159], [38477, 223], [38777, 223], [39076, 224], [39376, 224], [39676, 224], [39976, 224], [40275, 225], [40575, 224], [40875, 224], [41174, 224], [41474, 224], [41774, 223], [42073, 223], [42373, 223], [42673, 222], [42972, 223], [43272, 222], [43572, 222], [43872, 221], [44171, 222], [44471, 221], [44771, 221], [45070, 221], [45370, 220], [45670, 220], [45969, 220], [46269, 220], [46569, 219], [46868, 220], [47168, 219], [47468, 219], [47768, 218], [48067, 219], [48367, 218], [48667, 218], [48966, 218], [49266, 218], [49566, 217], [49865, 218], [50165, 217], [50465, 52], [50765, 52], [51064, 53], [51364, 53], [51664, 53], [51963, 54], [52263, 53], [52563, 53], [52862, 54], [53162, 54], [53462, 54], [53761, 55], [54061, 55], [54361, 55], [54661, 55], [54960, 55], [55260, 55], [55560, 55], [55859, 56], [56159, 56], [56459, 56], [56758, 57], [57058, 57], [57358, 56], [57657, 57], [57957, 57], [58257, 57], [58557, 57], [58856, 58], [59156, 58], [59456, 58], [59755, 59], [60055, 58], [60355, 58], [60654, 59], [60954, 59], [61254, 59], [61553, 60], [61853, 60], [62153, 60], [62453, 59], [62752, 60], [63052, 60], [63352, 60], [63651, 61], [63951, 61], [64251, 61], [64550, 62], [64841, 71], [65141, 70], [65440, 71], [65740, 71], [66040, 71], [66339, 72], [66639, 72], [66939, 72], [67238, 73], [67538, 72], [67838, 72], [68138, 72], [68438, 72], [68738, 72], [69037, 73], [69337, 73], [69637, 73], [69938, 72], [70238, 71], [70538, 71], [70838, 71], [71138, 71], [71438, 71], [71738, 71], [72038, 71], [72338, 71], [72639, 69], [72939, 69], [73239, 69], [73539, 69], [73839, 69], [74139, 69], [74440, 68], [74740, 68], [75040, 68], [75341, 66], [75641, 66], [75941, 66], [76241, 66], [76542, 65], [76842, 65], [77142, 65], [77443, 64], [77743, 63], [78044, 62], [78344, 62], [78644, 62], [78945, 61], [79245, 61], [79546, 60], [79847, 59], [80148, 57], [80448, 57], [80749, 56], [81050, 55], [81351, 54], [81653, 52], [81954, 51], [82255, 50], [82556, 49], [82858, 46], [83159, 45], [83460, 44], [83762, 42], [84064, 40], [84365, 39], [84667, 37], [84969, 35], [85271, 32], [85573, 30], [85878, 28], [86179, 29], [86479, 31], [86780, 23], [86804, 6], [87080, 23], [87105, 5], [87381, 22], [87682, 21], [87982, 20]], "point": [168, 156]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-4.136, -4.136, 5.716, 5.716, 3.5567708, 3.5567708]], "forceVisible": true, "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [103, 21, 299, 168], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22304, 196], [22604, 196], [22904, 196], [23204, 196], [23504, 196], [23804, 196], [24104, 196], [24405, 195], [24705, 195], [25005, 195], [25305, 195], [25605, 195], [25905, 195], [26205, 195], [26505, 195], [26805, 195], [27106, 194], [27406, 194], [27706, 194], [28006, 194], [28306, 194], [28606, 194], [28906, 194], [29206, 194], [29507, 193], [29807, 193], [30107, 193], [30407, 193], [30707, 193], [31007, 193], [31307, 193], [31608, 192], [31908, 192], [32208, 192], [32508, 192], [32808, 192], [33108, 192], [33408, 192], [33709, 191], [34009, 191], [34309, 191], [34609, 191], [34909, 191], [35209, 191], [35509, 191], [35810, 190], [36110, 190], [36410, 190], [36710, 190], [37010, 190], [37310, 190], [37610, 190], [37911, 189], [38211, 189], [38511, 189], [38811, 189], [39111, 189], [39411, 189], [39711, 189], [40012, 188], [40312, 188], [40612, 187], [40912, 187], [41212, 186], [41512, 186], [41812, 185], [42112, 184], [42413, 183], [42713, 182], [43013, 182], [43313, 181], [43613, 181], [43913, 180], [44213, 180], [44514, 178], [44814, 178], [45114, 177], [45414, 176], [45714, 176], [46014, 175], [46314, 175], [46615, 173], [46915, 173], [47215, 172], [47515, 172], [47815, 171], [48115, 171], [48416, 169], [48716, 169], [49016, 168], [49316, 168], [49616, 167], [49916, 167], [50217, 165]], "point": [201, 93]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-4.136, -4.136, 5.716, 5.716, 3.5567708, 3.5567708]], "forceVisible": true, "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [103, 21, 299, 168], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22304, 196], [22604, 196], [22904, 196], [23204, 196], [23504, 196], [23804, 196], [24104, 196], [24405, 195], [24705, 195], [25005, 195], [25305, 195], [25605, 195], [25905, 195], [26205, 195], [26505, 195], [26805, 195], [27106, 194], [27406, 194], [27706, 194], [28006, 194], [28306, 194], [28606, 194], [28906, 194], [29206, 194], [29507, 193], [29807, 193], [30107, 193], [30407, 193], [30707, 193], [31007, 193], [31307, 193], [31608, 192], [31908, 192], [32208, 192], [32508, 192], [32808, 192], [33108, 192], [33408, 192], [33709, 191], [34009, 191], [34309, 191], [34609, 191], [34909, 191], [35209, 191], [35509, 191], [35810, 190], [36110, 190], [36410, 190], [36710, 190], [37010, 190], [37310, 190], [37610, 190], [37911, 189], [38211, 189], [38511, 189], [38811, 189], [39111, 189], [39411, 189], [39711, 189], [40012, 188], [40312, 188], [40612, 187], [40912, 187], [41212, 186], [41512, 186], [41812, 185], [42112, 184], [42413, 183], [42713, 182], [43013, 182], [43313, 181], [43613, 181], [43913, 180], [44213, 180], [44514, 178], [44814, 178], [45114, 177], [45414, 176], [45714, 176], [46014, 175], [46314, 175], [46615, 173], [46915, 173], [47215, 172], [47515, 172], [47815, 171], [48115, 171], [48416, 169], [48716, 169], [49016, 168], [49316, 168], [49616, 167], [49916, 167], [50217, 165]], "point": [201, 93]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [103, 21, 299, 168], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22304, 196], [22604, 196], [22904, 196], [23204, 196], [23504, 196], [23804, 196], [24104, 196], [24405, 195], [24705, 195], [25005, 195], [25305, 195], [25605, 195], [25905, 195], [26205, 195], [26505, 195], [26805, 195], [27106, 194], [27406, 194], [27706, 194], [28006, 194], [28306, 194], [28606, 194], [28906, 194], [29206, 194], [29507, 193], [29807, 193], [30107, 193], [30407, 193], [30707, 193], [31007, 193], [31307, 193], [31608, 192], [31908, 192], [32208, 192], [32508, 192], [32808, 192], [33108, 192], [33408, 192], [33709, 191], [34009, 191], [34309, 191], [34609, 191], [34909, 191], [35209, 191], [35509, 191], [35810, 190], [36110, 190], [36410, 190], [36710, 190], [37010, 190], [37310, 190], [37610, 190], [37911, 189], [38211, 189], [38511, 189], [38811, 189], [39111, 189], [39411, 189], [39711, 189], [40012, 188], [40312, 188], [40612, 187], [40912, 187], [41212, 186], [41512, 186], [41812, 185], [42112, 184], [42413, 183], [42713, 182], [43013, 182], [43313, 181], [43613, 181], [43913, 180], [44213, 180], [44514, 178], [44814, 178], [45114, 177], [45414, 176], [45714, 176], [46014, 175], [46314, 175], [46615, 173], [46915, 173], [47215, 172], [47515, 172], [47815, 171], [48115, 171], [48416, 169], [48716, 169], [49016, 168], [49316, 168], [49616, 167], [49916, 167], [50217, 165]], "point": [201, 93]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.79|+00.95|+00.85"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [126, 100, 148, 128], "mask": [[29834, 7], [30132, 10], [30431, 13], [30730, 15], [31029, 16], [31328, 18], [31628, 19], [31927, 20], [32227, 21], [32526, 22], [32826, 22], [33126, 23], [33426, 23], [33726, 23], [34026, 23], [34326, 23], [34626, 23], [34926, 23], [35226, 23], [35526, 23], [35826, 22], [36127, 21], [36427, 21], [36728, 19], [37029, 17], [37330, 15], [37631, 13], [37932, 11], [38234, 7]], "point": [137, 113]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [37, 21, 299, 294], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22294, 206], [22593, 207], [22893, 207], [23193, 207], [23492, 208], [23792, 208], [24092, 208], [24391, 209], [24691, 209], [24991, 209], [25291, 209], [25590, 210], [25890, 63], [25980, 21], [26019, 81], [26190, 62], [26281, 18], [26321, 79], [26489, 62], [26581, 17], [26623, 77], [26789, 62], [26882, 15], [26924, 76], [27089, 61], [27182, 14], [27225, 75], [27388, 62], [27483, 13], [27526, 74], [27688, 62], [27783, 12], [27827, 73], [27988, 61], [28083, 12], [28127, 73], [28287, 62], [28383, 11], [28427, 73], [28587, 62], [28683, 11], [28728, 72], [28887, 62], [28983, 11], [29028, 72], [29187, 62], [29283, 11], [29328, 72], [29486, 63], [29583, 11], [29628, 72], [29786, 63], [29883, 11], [29928, 72], [30086, 64], [30183, 10], [30228, 72], [30385, 65], [30482, 11], [30528, 72], [30685, 65], [30782, 11], [30828, 72], [30985, 66], [31082, 11], [31127, 73], [31284, 67], [31381, 12], [31427, 73], [31584, 68], [31681, 12], [31727, 73], [31884, 69], [31980, 14], [32027, 73], [32183, 70], [32280, 14], [32326, 74], [32483, 71], [32579, 15], [32626, 74], [32783, 72], [32878, 16], [32925, 75], [33083, 73], [33177, 18], [33224, 76], [33382, 75], [33476, 19], [33524, 76], [33682, 77], [33774, 22], [33823, 77], [33982, 80], [34072, 25], [34122, 78], [34281, 116], [34421, 79], [34581, 118], [34720, 80], [34881, 119], [35019, 81], [35180, 122], [35317, 83], [35480, 125], [35615, 85], [35780, 220], [36080, 220], [36379, 221], [36679, 221], [36979, 221], [37278, 222], [37578, 222], [37878, 222], [38177, 223], [38477, 223], [38777, 223], [39076, 224], [39376, 224], [39676, 224], [39976, 224], [40275, 225], [40575, 224], [40875, 224], [41174, 224], [41474, 224], [41774, 223], [42073, 223], [42373, 223], [42673, 222], [42972, 223], [43272, 222], [43572, 222], [43872, 221], [44171, 222], [44471, 221], [44771, 221], [45070, 221], [45370, 220], [45670, 220], [45969, 220], [46269, 220], [46569, 219], [46868, 220], [47168, 219], [47468, 219], [47768, 218], [48067, 219], [48367, 218], [48667, 218], [48966, 218], [49266, 218], [49566, 217], [49865, 218], [50165, 217], [50465, 52], [50765, 52], [51064, 53], [51364, 53], [51664, 53], [51963, 54], [52263, 53], [52563, 53], [52862, 54], [53162, 54], [53462, 54], [53761, 55], [54061, 55], [54361, 55], [54661, 55], [54960, 55], [55260, 55], [55560, 55], [55859, 56], [56159, 56], [56459, 56], [56758, 57], [57058, 57], [57358, 56], [57657, 57], [57957, 57], [58257, 57], [58557, 57], [58856, 58], [59156, 58], [59456, 58], [59755, 59], [60055, 58], [60355, 58], [60654, 59], [60954, 59], [61254, 59], [61553, 60], [61853, 60], [62153, 60], [62453, 59], [62752, 60], [63052, 60], [63352, 60], [63651, 61], [63951, 61], [64251, 61], [64550, 62], [64841, 71], [65141, 70], [65440, 71], [65740, 71], [66040, 71], [66339, 72], [66639, 72], [66939, 72], [67238, 73], [67538, 72], [67838, 72], [68138, 72], [68438, 72], [68738, 72], [69037, 73], [69337, 73], [69637, 73], [69938, 72], [70238, 71], [70538, 71], [70838, 71], [71138, 71], [71438, 71], [71738, 71], [72038, 71], [72338, 71], [72639, 69], [72939, 69], [73239, 69], [73539, 69], [73839, 69], [74139, 69], [74440, 68], [74740, 68], [75040, 68], [75341, 66], [75641, 66], [75941, 66], [76241, 66], [76542, 65], [76842, 65], [77142, 65], [77443, 64], [77743, 63], [78044, 62], [78344, 62], [78644, 62], [78945, 61], [79245, 61], [79546, 60], [79847, 59], [80148, 57], [80448, 57], [80749, 56], [81050, 55], [81351, 54], [81653, 52], [81954, 51], [82255, 50], [82556, 49], [82858, 46], [83159, 45], [83460, 44], [83762, 42], [84064, 40], [84365, 39], [84667, 37], [84969, 35], [85271, 32], [85573, 30], [85878, 28], [86179, 29], [86479, 31], [86780, 23], [86804, 6], [87080, 23], [87105, 5], [87381, 22], [87682, 21], [87982, 20]], "point": [168, 156]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.79|+00.95|+00.85", "placeStationary": true, "receptacleObjectId": "CounterTop|-01.01|+00.94|-00.04"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 113, 299, 223], "mask": [[33600, 2], [33696, 30], [33730, 171], [33996, 30], [34042, 158], [34296, 30], [34343, 157], [34596, 30], [34643, 157], [34896, 30], [34943, 157], [35196, 31], [35243, 75], [35366, 34], [35496, 31], [35543, 74], [35668, 32], [35796, 31], [35843, 72], [35970, 30], [36095, 33], [36142, 72], [36272, 28], [36395, 37], [36440, 73], [36574, 26], [36695, 37], [36741, 70], [36876, 24], [36994, 37], [37041, 70], [37178, 22], [37294, 37], [37341, 69], [37479, 21], [37594, 37], [37641, 70], [37779, 21], [37894, 36], [37944, 67], [38080, 20], [38193, 35], [38245, 66], [38380, 20], [38493, 34], [38546, 65], [38681, 19], [38793, 33], [38847, 65], [38982, 18], [39093, 32], [39148, 64], [39282, 18], [39393, 32], [39449, 63], [39583, 17], [39692, 33], [39749, 63], [39883, 17], [39992, 32], [40049, 64], [40184, 16], [40292, 32], [40349, 64], [40484, 16], [40592, 32], [40650, 63], [40785, 15], [40891, 33], [40950, 63], [41085, 15], [41191, 33], [41250, 64], [41386, 14], [41491, 33], [41550, 64], [41687, 13], [41791, 33], [41850, 64], [41987, 13], [42090, 34], [42150, 64], [42288, 12], [42390, 34], [42450, 65], [42588, 12], [42690, 34], [42750, 65], [42889, 11], [42990, 35], [43050, 65], [43189, 11], [43289, 36], [43350, 65], [43490, 10], [43589, 36], [43650, 66], [43790, 10], [43889, 36], [43950, 66], [44091, 9], [44189, 36], [44250, 66], [44391, 9], [44488, 37], [44550, 66], [44692, 8], [44788, 37], [44850, 67], [44993, 7], [45088, 37], [45150, 67], [45293, 7], [45388, 37], [45450, 41], [45498, 19], [45594, 6], [45688, 37], [45750, 40], [45800, 18], [45894, 6], [45987, 39], [46049, 40], [46101, 17], [46195, 5], [46287, 39], [46349, 39], [46401, 17], [46495, 5], [46587, 39], [46649, 39], [46702, 16], [46796, 4], [46887, 39], [46948, 40], [47003, 16], [47096, 4], [47186, 41], [47248, 39], [47303, 16], [47397, 3], [47486, 42], [47547, 40], [47603, 17], [47697, 3], [47786, 43], [47846, 41], [47904, 16], [47996, 4], [48086, 44], [48144, 43], [48204, 18], [48295, 5], [48385, 48], [48441, 46], [48504, 20], [48595, 5], [48685, 102], [48804, 21], [48894, 6], [48985, 102], [49104, 23], [49193, 7], [49285, 103], [49404, 25], [49492, 8], [49585, 103], [49704, 26], [49791, 9], [49885, 103], [50003, 29], [50091, 10], [50185, 104], [50303, 98], [50408, 70], [50484, 105], [50603, 99], [50707, 71], [50784, 106], [50902, 289], [51201, 291], [51500, 294], [51799, 298], [52099, 298], [52399, 298], [52699, 298], [52999, 298], [53299, 299], [53600, 298], [53900, 298], [54200, 298], [54500, 298], [54800, 298], [55101, 298], [55401, 298], [55701, 298], [56001, 298], [56301, 298], [56602, 297], [56902, 297], [57202, 297], [57502, 298], [57802, 298], [58102, 298], [58403, 297], [58703, 297], [59003, 297], [59303, 298], [59603, 298], [59904, 297], [60204, 297], [60504, 297], [60804, 297], [61104, 298], [61404, 298], [61705, 297], [62005, 297], [62305, 297], [62605, 297], [62906, 296], [63206, 296], [63506, 297], [63806, 297], [64107, 296], [64407, 296], [64707, 296], [65004, 2], [65008, 295], [65304, 3], [65308, 295], [65604, 3], [65608, 295], [65908, 296], [66208, 296], [66509, 295], [66809, 91]], "point": [149, 167]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan30", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -0.25, "y": 0.9304675, "z": 0.75}, "object_poses": [{"objectName": "Potato_ed324e47", "position": {"x": 1.24237132, "y": 0.924556434, "z": -1.559781}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_ff5a3b19", "position": {"x": 0.0223874375, "y": 0.937239945, "z": -1.731678}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_3cec4766", "position": {"x": 3.113, "y": 1.4641279, "z": -1.697775}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_3cec4766", "position": {"x": -0.8273932, "y": 0.130453318, "z": 0.07609176}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_db774b98", "position": {"x": -0.8683, "y": 0.7066167, "z": -0.09889999}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_20771abe", "position": {"x": -0.1900449, "y": 1.26432693, "z": -1.59450829}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Kettle_449c396f", "position": {"x": -0.2394, "y": 0.902399957, "z": -1.0895}, "rotation": {"x": 0.0, "y": 315.000153, "z": 0.0}}, {"objectName": "Kettle_449c396f", "position": {"x": 0.967864335, "y": 0.9091413, "z": 0.464}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_7a7d333f", "position": {"x": 1.49186349, "y": 0.908243656, "z": 0.322243452}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_64c860f2", "position": {"x": -0.950113654, "y": 0.5331918, "z": 0.489208221}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_0fe289e3", "position": {"x": 3.11145973, "y": 0.748487055, "z": 0.03222303}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_0fe289e3", "position": {"x": 0.6028458, "y": 0.9092294, "z": 0.0984982848}, "rotation": {"x": 0.0, "y": 36.8105049, "z": 0.0}}, {"objectName": "Fork_12a59a81", "position": {"x": -0.9910205, "y": 0.5187405, "z": 1.34340417}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "CellPhone_bf099a5c", "position": {"x": -0.7864864, "y": 0.128489271, "z": 0.401712358}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_1e1bef89", "position": {"x": -0.6479388, "y": 0.9741304, "z": -0.780498862}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Apple_1e1bef89", "position": {"x": -1.08558583, "y": 1.07148921, "z": 1.31183481}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_c39806f6", "position": {"x": -0.9893264, "y": 1.254719, "z": -0.7902281}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "SaltShaker_c39806f6", "position": {"x": 1.303802, "y": 1.67118812, "z": -1.41078925}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_6d2fd9f8", "position": {"x": 3.37393451, "y": 0.897106051, "z": 0.3608464}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_6d2fd9f8", "position": {"x": -1.01178062, "y": 0.896606147, "z": -0.825490355}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "SoapBottle_5121dd32", "position": {"x": -0.9354367, "y": 0.909403, "z": -0.047219038}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_5121dd32", "position": {"x": 0.35675, "y": 0.91720295, "z": 0.3264321}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c285ee2", "position": {"x": 2.98708, "y": 0.7998927, "z": 0.165224791}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_7c285ee2", "position": {"x": 3.11145973, "y": 0.7998927, "z": -0.100778744}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_7c285ee2", "position": {"x": 0.967864335, "y": 0.960635066, "z": 0.0387303829}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_be22aed5", "position": {"x": 2.9665072, "y": 0.8986324, "z": -0.499501377}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_bf5eb4b5", "position": {"x": -1.00817108, "y": 0.913221, "z": 0.399594069}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_3e58a77d", "position": {"x": -0.885295749, "y": 0.9259183, "z": -0.643943548}, "rotation": {"x": 0.0, "y": 135.0, "z": 0.0}}, {"objectName": "Knife_3e58a77d", "position": {"x": -0.9910205, "y": 0.1565074, "z": 0.7516959}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_bf5eb4b5", "position": {"x": 3.02804923, "y": 0.188639641, "z": -0.404700667}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_20771abe", "position": {"x": 0.8567908, "y": 1.675596, "z": -1.41078711}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_5121dd32", "position": {"x": 0.8368646, "y": 0.917203, "z": 0.180486917}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_ed324e47", "position": {"x": 1.16424751, "y": 1.1119374, "z": -1.50391865}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_12a59a81", "position": {"x": -0.8683, "y": 0.326937616, "z": -0.09889999}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_ff5a3b19", "position": {"x": -0.789968, "y": 0.93724, "z": 0.176187515}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_7a7d333f", "position": {"x": 3.031842, "y": 1.46470618, "z": -1.2358}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Kettle_449c396f", "position": {"x": -0.4203, "y": 0.902399957, "z": -0.9203}, "rotation": {"x": 0.0, "y": 134.999832, "z": 0.0}}, {"objectName": "Spatula_64c860f2", "position": {"x": 0.105427817, "y": 0.915299833, "z": -1.43042028}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Knife_3e58a77d", "position": {"x": 0.710450053, "y": 0.933718264, "z": 0.4267226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_99ab2e9c", "position": {"x": 0.415472031, "y": 0.965890944, "z": -1.44522238}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_be22aed5", "position": {"x": -1.0521574, "y": 0.9954912, "z": 1.47093987}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_c39806f6", "position": {"x": 3.39334869, "y": 0.897106051, "z": 0.104750693}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c285ee2", "position": {"x": -0.789968, "y": 0.9528351, "z": 0.846407056}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_6d2fd9f8", "position": {"x": 0.20406355, "y": 1.25605667, "z": -1.74189985}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_db774b98", "position": {"x": 0.967864335, "y": 0.9081421, "z": 0.8892696}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_1e1bef89", "position": {"x": 1.22986388, "y": 0.9847904, "z": 0.464}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_000ae861", "position": {"x": -0.4198, "y": 0.902399957, "z": -1.2699}, "rotation": {"x": 0.0, "y": 315.000153, "z": 0.0}}, {"objectName": "Bread_1e8ab5c8", "position": {"x": 0.798875034, "y": 0.9869737, "z": 0.7275939}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_bf099a5c", "position": {"x": 0.422373831, "y": 0.509523034, "z": -1.43432426}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_3cec4766", "position": {"x": 0.422373831, "y": 0.511689961, "z": -1.55638778}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_bfd17fb4", "position": {"x": 0.101004355, "y": 0.9372893, "z": -1.731678}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_0fe289e3", "position": {"x": 0.101004355, "y": 0.901429355, "z": -1.51683629}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_46f611f6", "position": {"x": 0.5336, "y": 0.9080754, "z": 0.7275939}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_ba5c7501", "position": {"x": 2.98707986, "y": 0.7423577, "z": -0.0342778563}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3357381131, "scene_num": 30}, "task_id": "trial_T20190909_130401_115312", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2TBXASXZIRNNW_39GAF6DQWURD4I08JTNTJBQR7VSV1U", "high_descs": ["Look down at the counter", "Grab the egg.", "Go over to the microwave", "Heat the egg up", "Grab the egg back and go over to the counter", "Place the egg on the counter"], "task_desc": "Heat an egg up and put it on the counter", "votes": [1, 1]}, {"assignment_id": "A1ELPYAFO7MANS_3HFNH7HEMK5I3PUUFDEMKBVPSSHQGG", "high_descs": ["Look down.", "Pick up the egg on the counter.", "Take a step right.", "Heat the egg in the microwave.", "Take a couple steps left and face the counter.", "Place the egg on the counter, to the left of the spoon and in front of the bottle."], "task_desc": "Place a heated egg on a counter.", "votes": [1, 1]}, {"assignment_id": "A20FCMWP43CVIU_3PS7W85Z82TGGVZEIB27XZKP8MQT9B", "high_descs": ["turn to face counter top with egg", "pick up egg from counter", "turn right to face microwave", "heat and remove egg from microwave", "walk to face counter with toaster", "put egg on counter top"], "task_desc": "put heated egg on counter top", "votes": [1, 1]}]}}