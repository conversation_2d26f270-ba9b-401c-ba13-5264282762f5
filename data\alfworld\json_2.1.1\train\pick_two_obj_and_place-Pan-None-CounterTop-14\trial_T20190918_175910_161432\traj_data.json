{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000091.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000092.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000103.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000104.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000105.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000106.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000107.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000187.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000188.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000189.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000190.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000191.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000192.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000193.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000194.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000195.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000196.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000197.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000198.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000199.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000200.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000216.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000217.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000218.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000219.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000220.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000221.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000222.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000223.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000224.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000225.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000226.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000227.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000228.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000229.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000230.png", "low_idx": 39}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Pan", "parent_target": "CounterTop", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["stoveburner"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|4|-1|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["pan"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Pan", [6.1732, 6.1732, -1.2812, -1.2812, 3.6392, 3.6392]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", [6.1732, 6.1732, -1.2812, -1.2812, 3.628, 3.628]], "forceVisible": true, "objectId": "Pan|+01.54|+00.91|-00.32"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|1|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["pan", "countertop"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Pan", [6.1732, 6.1732, -1.2812, -1.2812, 3.6392, 3.6392]], "coordinateReceptacleObjectId": ["CounterTop", [3.956, 3.956, 4.068, 4.068, 3.7844, 3.7844]], "forceVisible": true, "objectId": "Pan|+01.54|+00.91|-00.32", "receptacleObjectId": "CounterTop|+00.99|+00.95|+01.02"}}, {"discrete_action": {"action": "GotoLocation", "args": ["stoveburner"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|4|-1|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["pan"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Pan", [7.228, 7.228, -1.2812, -1.2812, 3.6392, 3.6392]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", [7.228, 7.228, -1.2812, -1.2812, 3.628, 3.628]], "forceVisible": true, "objectId": "Pan|+01.81|+00.91|-00.32"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|4|1|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["pan", "countertop"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Pan", [7.228, 7.228, -1.2812, -1.2812, 3.6392, 3.6392]], "coordinateReceptacleObjectId": ["CounterTop", [3.956, 3.956, 4.068, 4.068, 3.7844, 3.7844]], "forceVisible": true, "objectId": "Pan|+01.81|+00.91|-00.32", "receptacleObjectId": "CounterTop|+00.99|+00.95|+01.02"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Pan|+01.54|+00.91|-00.32"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [133, 73, 211, 177], "mask": [[21766, 9], [22060, 22], [22356, 30], [22653, 36], [22951, 40], [23249, 44], [23547, 48], [23846, 51], [24144, 54], [24443, 57], [24742, 59], [25041, 61], [25340, 63], [25639, 65], [25938, 67], [26237, 69], [26537, 70], [26836, 71], [27136, 72], [27435, 74], [27735, 74], [28034, 76], [28334, 76], [28634, 77], [28933, 78], [29233, 78], [29533, 78], [29833, 79], [30133, 79], [30433, 79], [30733, 79], [31033, 79], [31333, 79], [31633, 79], [31933, 79], [32233, 79], [32534, 78], [32834, 78], [33134, 77], [33435, 76], [33735, 76], [34035, 76], [34336, 74], [34637, 73], [34937, 72], [35238, 71], [35538, 70], [35839, 68], [36139, 67], [36440, 66], [36741, 64], [37042, 62], [37343, 60], [37644, 58], [37945, 56], [38246, 54], [38547, 51], [38849, 48], [39150, 46], [39452, 42], [39754, 38], [40056, 34], [40359, 29], [40663, 21], [40968, 10], [41271, 7], [41571, 7], [41871, 7], [42172, 6], [42472, 6], [42772, 6], [43072, 6], [43371, 8], [43671, 8], [43971, 8], [44272, 7], [44572, 7], [44872, 7], [45172, 7], [45472, 7], [45772, 7], [46072, 7], [46372, 7], [46672, 7], [46972, 7], [47272, 8], [47572, 8], [47872, 8], [48172, 8], [48472, 8], [48772, 8], [49072, 8], [49372, 8], [49672, 8], [49972, 8], [50272, 8], [50572, 8], [50872, 9], [51172, 4], [51177, 4], [51472, 4], [51477, 4], [51772, 3], [51778, 3], [52072, 9], [52372, 9], [52673, 7], [52976, 2]], "point": [172, 124]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Pan|+01.54|+00.91|-00.32", "placeStationary": true, "receptacleObjectId": "CounterTop|+00.99|+00.95|+01.02"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 89, 299, 273], "mask": [[26400, 7], [26436, 56], [26541, 167], [26736, 55], [26841, 167], [27036, 53], [27142, 167], [27337, 51], [27442, 167], [27637, 49], [27742, 168], [27937, 49], [28042, 169], [28237, 48], [28343, 168], [28538, 46], [28643, 169], [28838, 46], [28943, 169], [29138, 46], [29243, 170], [29438, 46], [29543, 171], [29738, 46], [29843, 171], [30039, 45], [30143, 172], [30339, 45], [30443, 172], [30639, 45], [30743, 173], [30939, 45], [31043, 174], [31240, 44], [31343, 174], [31540, 45], [31643, 175], [31839, 46], [31942, 155], [32100, 18], [32139, 46], [32242, 151], [32400, 19], [32438, 48], [32542, 151], [32700, 19], [32737, 50], [32841, 153], [32996, 24], [33035, 53], [33141, 180], [33333, 56], [33441, 183], [33630, 60], [33740, 251], [34039, 254], [34339, 69], [34410, 184], [34638, 69], [34711, 185], [34937, 70], [35012, 185], [35236, 71], [35312, 187], [35533, 74], [35612, 189], [35830, 78], [35913, 190], [36127, 81], [36213, 194], [36425, 84], [36513, 201], [36721, 88], [36813, 297], [37114, 296], [37414, 297], [37714, 297], [38014, 298], [38314, 298], [38614, 299], [38915, 298], [39215, 299], [39516, 298], [39816, 298], [40116, 298], [40417, 296], [40718, 294], [41019, 292], [41320, 291], [41621, 290], [41921, 207], [42137, 74], [42222, 198], [42437, 74], [42522, 195], [42736, 4], [42744, 67], [42822, 155], [42978, 38], [43057, 55], [43122, 153], [43280, 35], [43372, 41], [43422, 149], [43585, 30], [43674, 41], [43721, 134], [43909, 5], [43976, 42], [44019, 135], [44185, 3], [44278, 176], [44472, 1], [44479, 27], [44579, 194], [44779, 33], [44881, 192], [45078, 33], [45181, 192], [45378, 32], [45482, 190], [45677, 32], [45783, 189], [45977, 31], [46084, 188], [46277, 31], [46385, 187], [46576, 31], [46685, 186], [46876, 30], [46986, 185], [47175, 31], [47286, 185], [47474, 32], [47586, 185], [47774, 32], [47887, 183], [48073, 32], [48187, 183], [48373, 32], [48487, 183], [48672, 33], [48788, 182], [48972, 33], [49088, 181], [49272, 32], [49388, 181], [49571, 33], [49688, 180], [49871, 33], [49988, 180], [50170, 34], [50288, 180], [50470, 34], [50588, 179], [50770, 34], [50888, 178], [51071, 33], [51188, 177], [51371, 33], [51488, 176], [51672, 32], [51788, 175], [51973, 31], [52088, 175], [52273, 31], [52388, 174], [52574, 30], [52688, 173], [52874, 30], [52989, 172], [53174, 30], [53289, 172], [53473, 31], [53589, 171], [53773, 31], [53889, 171], [54073, 31], [54189, 171], [54372, 31], [54490, 170], [54671, 32], [54792, 168], [54970, 33], [55094, 167], [55269, 34], [55396, 166], [55567, 36], [55697, 204], [55999, 201], [56300, 198], [56602, 195], [56903, 193], [57204, 190], [57506, 187], [57807, 184], [58109, 181], [58410, 179], [58711, 178], [59011, 177], [59312, 175], [59613, 173], [59914, 171], [60215, 169], [60516, 167], [60817, 165], [61118, 163], [61419, 161], [61720, 159], [62021, 157], [62322, 156], [62622, 155], [62923, 153], [63224, 152], [63524, 151], [63825, 149], [64126, 147], [64427, 146], [64727, 73], [65077, 23], [65378, 22], [65678, 22], [65979, 21], [66279, 21], [66579, 21], [66880, 20], [67180, 20], [67480, 20], [67781, 19], [68081, 19], [68382, 18], [68682, 18], [68982, 18], [69283, 17], [69583, 17], [69884, 16], [70184, 16], [70484, 16], [70785, 15], [71085, 15], [71386, 14], [71686, 14], [71986, 14], [72287, 13], [72587, 13], [72887, 13], [73188, 12], [73488, 12], [73789, 11], [74089, 11], [74389, 11], [74690, 10], [74990, 10], [75291, 9], [75591, 9], [75891, 9], [76192, 8], [76492, 8], [76793, 7], [77093, 7], [77393, 7], [77694, 6], [77994, 6], [78295, 5], [78595, 5], [78895, 5], [79196, 4], [79496, 4], [79796, 4], [80097, 3], [80397, 3], [80698, 2], [80998, 2], [81298, 2], [81599, 1], [81899, 1]], "point": [157, 143]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Pan|+01.81|+00.91|-00.32"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [101, 101, 199, 144], "mask": [[30159, 15], [30454, 25], [30751, 32], [31049, 36], [31346, 41], [31645, 44], [31943, 48], [32242, 50], [32541, 52], [32840, 55], [33139, 57], [33438, 58], [33738, 59], [34037, 61], [34337, 62], [34602, 33], [34637, 62], [34901, 98], [35201, 99], [35501, 99], [35834, 66], [36135, 65], [36436, 64], [36736, 64], [37037, 63], [37337, 63], [37638, 61], [37938, 61], [38238, 60], [38539, 59], [38839, 58], [39140, 57], [39440, 56], [39741, 55], [40042, 53], [40343, 51], [40644, 49], [40945, 47], [41246, 45], [41548, 42], [41850, 38], [42152, 34], [42454, 30], [42757, 24], [43062, 14]], "point": [150, 121]}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Pan|+01.81|+00.91|-00.32", "placeStationary": true, "receptacleObjectId": "CounterTop|+00.99|+00.95|+01.02"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 89, 299, 273], "mask": [[26400, 7], [26436, 56], [26541, 167], [26736, 55], [26841, 167], [27036, 53], [27142, 167], [27337, 51], [27442, 167], [27637, 49], [27742, 168], [27937, 49], [28042, 169], [28237, 48], [28343, 168], [28538, 46], [28643, 169], [28838, 46], [28943, 169], [29138, 46], [29243, 170], [29438, 46], [29543, 171], [29738, 46], [29843, 171], [30039, 45], [30143, 172], [30339, 45], [30443, 172], [30639, 45], [30743, 173], [30939, 45], [31043, 174], [31240, 44], [31343, 174], [31540, 45], [31643, 175], [31839, 46], [31942, 155], [32100, 18], [32139, 46], [32242, 151], [32400, 19], [32438, 48], [32542, 151], [32700, 19], [32737, 50], [32841, 153], [32996, 24], [33035, 53], [33141, 180], [33333, 56], [33441, 183], [33630, 60], [33740, 251], [34039, 254], [34339, 69], [34410, 184], [34638, 69], [34711, 185], [34937, 70], [35012, 185], [35236, 71], [35312, 187], [35533, 74], [35612, 189], [35830, 78], [35913, 190], [36127, 81], [36213, 194], [36425, 84], [36513, 201], [36721, 88], [36813, 297], [37114, 296], [37414, 297], [37714, 297], [38014, 298], [38314, 298], [38614, 299], [38915, 298], [39215, 299], [39516, 298], [39816, 298], [40116, 298], [40417, 296], [40718, 294], [41019, 62], [41097, 214], [41320, 56], [41400, 211], [41621, 53], [41700, 211], [41921, 51], [42000, 128], [42137, 74], [42222, 48], [42300, 120], [42437, 74], [42522, 47], [42600, 117], [42736, 4], [42744, 67], [42822, 46], [42900, 77], [42978, 38], [43057, 55], [43122, 45], [43200, 75], [43280, 35], [43372, 41], [43422, 44], [43500, 71], [43585, 30], [43674, 41], [43721, 44], [43800, 55], [43909, 5], [43976, 42], [44019, 45], [44100, 54], [44185, 3], [44278, 86], [44400, 54], [44472, 1], [44479, 27], [44579, 85], [44700, 73], [44779, 33], [44881, 82], [45000, 73], [45078, 33], [45181, 82], [45300, 73], [45378, 32], [45482, 81], [45600, 72], [45677, 32], [45783, 80], [45900, 72], [45977, 31], [46084, 79], [46200, 72], [46277, 31], [46385, 78], [46500, 72], [46576, 31], [46685, 78], [46800, 71], [46876, 30], [46986, 77], [47100, 71], [47175, 31], [47286, 38], [47330, 33], [47400, 71], [47474, 32], [47586, 37], [47700, 71], [47774, 32], [47887, 36], [48000, 70], [48073, 32], [48187, 36], [48226, 1], [48300, 70], [48373, 32], [48487, 36], [48600, 70], [48672, 33], [48788, 37], [48854, 9], [48900, 70], [48972, 33], [49088, 75], [49200, 69], [49272, 32], [49388, 76], [49500, 69], [49571, 33], [49688, 76], [49800, 68], [49871, 33], [49988, 76], [50100, 68], [50170, 34], [50288, 76], [50400, 68], [50470, 34], [50588, 77], [50700, 67], [50770, 34], [50888, 78], [51000, 66], [51071, 33], [51188, 78], [51300, 65], [51371, 33], [51488, 79], [51600, 64], [51672, 32], [51788, 79], [51900, 63], [51973, 31], [52088, 80], [52200, 63], [52273, 31], [52388, 81], [52500, 62], [52574, 30], [52688, 82], [52800, 61], [52874, 30], [52989, 82], [53100, 61], [53174, 30], [53289, 82], [53400, 61], [53473, 31], [53589, 83], [53700, 60], [53773, 31], [53889, 85], [54000, 60], [54073, 31], [54189, 86], [54300, 60], [54372, 31], [54489, 87], [54600, 60], [54671, 32], [54789, 88], [54900, 60], [54970, 33], [55090, 88], [55200, 61], [55269, 34], [55390, 90], [55500, 62], [55567, 36], [55690, 91], [55800, 103], [55991, 92], [56100, 103], [56293, 92], [56400, 103], [56594, 93], [56700, 103], [56896, 92], [57000, 102], [57198, 92], [57300, 101], [57499, 94], [57600, 99], [57801, 95], [57900, 97], [58103, 96], [58200, 96], [58404, 190], [58706, 187], [59007, 185], [59308, 183], [59609, 181], [59910, 178], [60212, 175], [60513, 173], [60814, 171], [61115, 169], [61416, 167], [61717, 165], [62018, 163], [62319, 161], [62620, 159], [62921, 158], [63221, 157], [63522, 155], [63823, 153], [64124, 151], [64425, 149], [64726, 74], [65077, 23], [65378, 22], [65678, 22], [65979, 21], [66279, 21], [66579, 21], [66880, 20], [67180, 20], [67480, 20], [67781, 19], [68081, 19], [68382, 18], [68682, 18], [68982, 18], [69283, 17], [69583, 17], [69884, 16], [70184, 16], [70484, 16], [70785, 15], [71085, 15], [71386, 14], [71686, 14], [71986, 14], [72287, 13], [72587, 13], [72887, 13], [73188, 12], [73488, 12], [73789, 11], [74089, 11], [74389, 11], [74690, 10], [74990, 10], [75291, 9], [75591, 9], [75891, 9], [76192, 8], [76492, 8], [76793, 7], [77093, 7], [77393, 7], [77694, 6], [77994, 6], [78295, 5], [78595, 5], [78895, 5], [79196, 4], [79496, 4], [79796, 4], [80097, 3], [80397, 3], [80698, 2], [80998, 2], [81298, 2], [81599, 1], [81899, 1]], "point": [157, 143]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan14", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -1.5, "y": 0.9009992, "z": 2.0}, "object_poses": [{"objectName": "Pan_7013969f", "position": {"x": 1.5433, "y": 0.9098, "z": -0.3203}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Potato_6d0d5c3c", "position": {"x": -0.6929716, "y": 0.7908461, "z": -1.59446669}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_00484768", "position": {"x": 1.03216934, "y": 0.7602068, "z": -1.71005964}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_192d20c7", "position": {"x": 1.68454242, "y": 0.9111485, "z": 0.5189736}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "Spoon_89592edf", "position": {"x": 1.24529183, "y": 0.9117294, "z": 1.00428259}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spatula_83c41bbf", "position": {"x": 1.516571, "y": 0.9261998, "z": -1.4602412}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Egg_85efe221", "position": {"x": 1.93587351, "y": 0.9463632, "z": -1.37455559}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_e8bf9352", "position": {"x": 1.80571866, "y": 1.63482726, "z": -0.9011388}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_06a240e2", "position": {"x": 1.7262224, "y": 0.943637133, "z": -1.4602406}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Knife_06a240e2", "position": {"x": 0.2201252, "y": 0.9430372, "z": 0.8276391}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_b3498f3e", "position": {"x": 1.80513167, "y": 1.6407578, "z": -1.76552045}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b3498f3e", "position": {"x": 1.84431911, "y": 2.13104033, "z": 1.23699057}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_3caefad1", "position": {"x": 1.73332167, "y": 1.64890611, "z": -0.449611485}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_3caefad1", "position": {"x": 1.58645439, "y": 0.906199932, "z": -1.28887177}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Mug_f9995e03", "position": {"x": 1.86599088, "y": 0.906199932, "z": -1.80297852}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_5b80d804", "position": {"x": 0.9630915, "y": 0.817963839, "z": -1.48894024}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_e8bf9352", "position": {"x": 1.80289924, "y": 1.64713418, "z": -0.0005005805}, "rotation": {"x": 6.388199e-06, "y": -0.000208329075, "z": 0.000100033976}}, {"objectName": "SoapBottle_fd471042", "position": {"x": 1.86598945, "y": 0.909364045, "z": -1.288871}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_5ecc05ec", "position": {"x": 0.476415873, "y": 0.9088847, "z": 1.18092084}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Egg_85efe221", "position": {"x": 1.80037653, "y": 0.105368823, "z": 1.82753634}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Fork_192d20c7", "position": {"x": 1.60700154, "y": 0.9111485, "z": 0.5708322}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "Ladle_4b0a72cf", "position": {"x": 1.757875, "y": 0.954962432, "z": 1.18092465}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "Apple_5b80d804", "position": {"x": 1.72622263, "y": 0.968506932, "z": -1.54592526}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_b3498f3e", "position": {"x": 1.7333883, "y": 2.13229823, "z": 0.880853951}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_7013969f", "position": {"x": 1.807, "y": 0.9098, "z": -0.3203}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Tomato_817a54e2", "position": {"x": 0.356795847, "y": 0.975055337, "z": -1.26375937}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spatula_83c41bbf", "position": {"x": 0.7327086, "y": 0.9255999, "z": 0.915960848}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "Potato_6d0d5c3c", "position": {"x": 1.8034842, "y": 1.055655, "z": -0.888098836}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Knife_06a240e2", "position": {"x": 1.13966608, "y": 0.7610327, "z": -1.5848788}, "rotation": {"x": 271.5572, "y": 81.71272, "z": 278.4412}}, {"objectName": "ButterKnife_38537d23", "position": {"x": 0.296, "y": 0.9, "z": -1.296}, "rotation": {"x": 0.0, "y": 14.5689316, "z": 0.0}}, {"objectName": "Bread_8f8a19bf", "position": {"x": 0.9890009, "y": 0.9874766, "z": 0.7393211}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Lettuce_e61d6e1d", "position": {"x": 0.856707752, "y": 0.9893426, "z": 1.12070835}, "rotation": {"x": 0.187187061, "y": 270.009521, "z": 1.47289824}}, {"objectName": "PepperShaker_3caefad1", "position": {"x": 1.75787473, "y": 0.9056, "z": 1.26924491}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pot_bb5e3511", "position": {"x": 1.5345701, "y": 0.9084982, "z": 0.05875536}, "rotation": {"x": 0.0, "y": 195.000244, "z": 0.0}}, {"objectName": "DishSponge_00484768", "position": {"x": 1.73962343, "y": 0.06976977, "z": 1.54046369}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_89592edf", "position": {"x": 0.7327088, "y": 0.9117294, "z": 0.8276406}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_7266264e", "position": {"x": -0.692971647, "y": 1.38769758, "z": -1.59446669}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f9995e03", "position": {"x": 1.91716385, "y": 0.905599952, "z": 0.5708331}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}], "object_toggles": [], "random_seed": 2652701631, "scene_num": 14}, "task_id": "trial_T20190918_175910_161432", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "ARL7HOWLEHNOP_3OCHAWUVGR1ZTJ5RVA5CBPZ91BCKX1", "high_descs": ["Turn around, walk across he room, turn left, then walk forward to the stove.", "Pick up the pan on the front right burner of the stove.", "Turn left and face towards the counter.", "Place the pan on top of the counter to the right of the bread load.", "Turn around, walk forward, turn left to face the stove.", "Pick up the pan on the back right burner of the stove.", "Turn left and walk to the counter.", "Place the pan on the far left side of the counter in front of the green cup."], "task_desc": "Place the pans on the counter", "votes": [1, 1]}, {"assignment_id": "A320QA9HJFUOZO_3SUWZRL0M1UJO4TLY1LJDH9KKCWE60", "high_descs": ["Turn around and walk into the kitchen over to the stove", "Pick up the pan from the front right stove burner", "Turn left and approach the counter with bread on it", "Put the pan down on the counter to the right of the bread", "Turn right and walk back to the stove", "Pick up the pan from the back right stove burner", "Turn left and walk back to the counter with bread on it", "Put the pan down on the counter left of the bread loaf"], "task_desc": "Put the pans on the kitchen counter", "votes": [1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_3U8YCDAGXSXD7WXB5302YXO7Q22Q07", "high_descs": ["walk over to the kitchen stove", "grab a sauce pan off of the stove", "turn left to face the kitchen counter", "place the sauce pan on the kitchen counter there", "turn right to face the stove again", "grab another sauce pan off of the stove", "turn left to face the counter again", "put the sauce pan down on the counter"], "task_desc": "pick up the two sauce pans off the stove to put them on the counter to the left", "votes": [1, 1]}]}}