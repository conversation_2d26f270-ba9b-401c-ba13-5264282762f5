{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 8}, {"high_idx": 3, "image_name": "000000085.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000086.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000087.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000088.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000089.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000090.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000091.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000092.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000093.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000094.png", "low_idx": 9}, {"high_idx": 4, "image_name": "000000095.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000096.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000097.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000098.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000099.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000100.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000101.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000102.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000103.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000104.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000105.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000106.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000107.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000108.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000109.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000110.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000111.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000112.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000113.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000114.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000115.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000116.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000117.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000118.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000119.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000120.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000121.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000122.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000123.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 15}, {"high_idx": 5, "image_name": "000000125.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000126.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000127.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000128.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000129.png", "low_idx": 17}, {"high_idx": 5, "image_name": "000000130.png", "low_idx": 17}, {"high_idx": 5, "image_name": "000000131.png", "low_idx": 17}, {"high_idx": 5, "image_name": "000000132.png", "low_idx": 17}, {"high_idx": 5, "image_name": "000000133.png", "low_idx": 17}, {"high_idx": 5, "image_name": "000000134.png", "low_idx": 17}, {"high_idx": 5, "image_name": "000000135.png", "low_idx": 17}, {"high_idx": 5, "image_name": "000000136.png", "low_idx": 17}, {"high_idx": 5, "image_name": "000000137.png", "low_idx": 17}, {"high_idx": 5, "image_name": "000000138.png", "low_idx": 17}, {"high_idx": 5, "image_name": "000000139.png", "low_idx": 17}, {"high_idx": 5, "image_name": "000000140.png", "low_idx": 17}, {"high_idx": 5, "image_name": "000000141.png", "low_idx": 17}, {"high_idx": 5, "image_name": "000000142.png", "low_idx": 17}, {"high_idx": 5, "image_name": "000000143.png", "low_idx": 17}, {"high_idx": 5, "image_name": "000000144.png", "low_idx": 18}, {"high_idx": 5, "image_name": "000000145.png", "low_idx": 18}, {"high_idx": 5, "image_name": "000000146.png", "low_idx": 18}, {"high_idx": 5, "image_name": "000000147.png", "low_idx": 18}, {"high_idx": 6, "image_name": "000000148.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000149.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000150.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000151.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000152.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000153.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000154.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000155.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000156.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000157.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000158.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000159.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000160.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000161.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000162.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000163.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000164.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000165.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000166.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000167.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000168.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000169.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000170.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000171.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000172.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000173.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000174.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000175.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000176.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000177.png", "low_idx": 24}, {"high_idx": 7, "image_name": "000000178.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000179.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000180.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000181.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000182.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000183.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000184.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000185.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000186.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000187.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000188.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000189.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000190.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000191.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000192.png", "low_idx": 25}, {"high_idx": 8, "image_name": "000000193.png", "low_idx": 26}, {"high_idx": 8, "image_name": "000000194.png", "low_idx": 26}, {"high_idx": 8, "image_name": "000000195.png", "low_idx": 26}, {"high_idx": 8, "image_name": "000000196.png", "low_idx": 26}, {"high_idx": 8, "image_name": "000000197.png", "low_idx": 26}, {"high_idx": 8, "image_name": "000000198.png", "low_idx": 26}, {"high_idx": 8, "image_name": "000000199.png", "low_idx": 26}, {"high_idx": 8, "image_name": "000000200.png", "low_idx": 26}, {"high_idx": 8, "image_name": "000000201.png", "low_idx": 26}, {"high_idx": 8, "image_name": "000000202.png", "low_idx": 26}, {"high_idx": 8, "image_name": "000000203.png", "low_idx": 26}, {"high_idx": 8, "image_name": "000000204.png", "low_idx": 27}, {"high_idx": 8, "image_name": "000000205.png", "low_idx": 27}, {"high_idx": 8, "image_name": "000000206.png", "low_idx": 28}, {"high_idx": 8, "image_name": "000000207.png", "low_idx": 28}, {"high_idx": 8, "image_name": "000000208.png", "low_idx": 29}, {"high_idx": 8, "image_name": "000000209.png", "low_idx": 29}, {"high_idx": 8, "image_name": "000000210.png", "low_idx": 30}, {"high_idx": 8, "image_name": "000000211.png", "low_idx": 30}, {"high_idx": 8, "image_name": "000000212.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000213.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000214.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000215.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000216.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000217.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000218.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000219.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000220.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000221.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000222.png", "low_idx": 31}, {"high_idx": 9, "image_name": "000000223.png", "low_idx": 32}, {"high_idx": 9, "image_name": "000000224.png", "low_idx": 32}, {"high_idx": 9, "image_name": "000000225.png", "low_idx": 32}, {"high_idx": 9, "image_name": "000000226.png", "low_idx": 32}, {"high_idx": 9, "image_name": "000000227.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000228.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000229.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000230.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000231.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000232.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000233.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000234.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000235.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000236.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000237.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000238.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000239.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000240.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000241.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000242.png", "low_idx": 34}, {"high_idx": 9, "image_name": "000000243.png", "low_idx": 34}, {"high_idx": 9, "image_name": "000000244.png", "low_idx": 34}, {"high_idx": 9, "image_name": "000000245.png", "low_idx": 34}, {"high_idx": 9, "image_name": "000000246.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000247.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000248.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000249.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000250.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000251.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000252.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000253.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000254.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000255.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000256.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000257.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000258.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000259.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000260.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000261.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000262.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000263.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000264.png", "low_idx": 36}, {"high_idx": 9, "image_name": "000000265.png", "low_idx": 36}, {"high_idx": 9, "image_name": "000000266.png", "low_idx": 36}, {"high_idx": 9, "image_name": "000000267.png", "low_idx": 36}, {"high_idx": 9, "image_name": "000000268.png", "low_idx": 36}, {"high_idx": 9, "image_name": "000000269.png", "low_idx": 36}, {"high_idx": 9, "image_name": "000000270.png", "low_idx": 37}, {"high_idx": 9, "image_name": "000000271.png", "low_idx": 37}, {"high_idx": 9, "image_name": "000000272.png", "low_idx": 37}, {"high_idx": 9, "image_name": "000000273.png", "low_idx": 37}, {"high_idx": 9, "image_name": "000000274.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000275.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000276.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000277.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000278.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000279.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000280.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000281.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000282.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000283.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000284.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000285.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000286.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000287.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000288.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000289.png", "low_idx": 39}, {"high_idx": 9, "image_name": "000000290.png", "low_idx": 39}, {"high_idx": 9, "image_name": "000000291.png", "low_idx": 39}, {"high_idx": 9, "image_name": "000000292.png", "low_idx": 39}, {"high_idx": 10, "image_name": "000000293.png", "low_idx": 40}, {"high_idx": 10, "image_name": "000000294.png", "low_idx": 40}, {"high_idx": 10, "image_name": "000000295.png", "low_idx": 40}, {"high_idx": 10, "image_name": "000000296.png", "low_idx": 40}, {"high_idx": 10, "image_name": "000000297.png", "low_idx": 40}, {"high_idx": 10, "image_name": "000000298.png", "low_idx": 40}, {"high_idx": 10, "image_name": "000000299.png", "low_idx": 40}, {"high_idx": 10, "image_name": "000000300.png", "low_idx": 40}, {"high_idx": 10, "image_name": "000000301.png", "low_idx": 40}, {"high_idx": 10, "image_name": "000000302.png", "low_idx": 40}, {"high_idx": 10, "image_name": "000000303.png", "low_idx": 40}, {"high_idx": 10, "image_name": "000000304.png", "low_idx": 41}, {"high_idx": 10, "image_name": "000000305.png", "low_idx": 41}, {"high_idx": 10, "image_name": "000000306.png", "low_idx": 41}, {"high_idx": 10, "image_name": "000000307.png", "low_idx": 41}, {"high_idx": 10, "image_name": "000000308.png", "low_idx": 41}, {"high_idx": 10, "image_name": "000000309.png", "low_idx": 41}, {"high_idx": 10, "image_name": "000000310.png", "low_idx": 41}, {"high_idx": 10, "image_name": "000000311.png", "low_idx": 41}, {"high_idx": 10, "image_name": "000000312.png", "low_idx": 41}, {"high_idx": 10, "image_name": "000000313.png", "low_idx": 41}, {"high_idx": 10, "image_name": "000000314.png", "low_idx": 41}, {"high_idx": 10, "image_name": "000000315.png", "low_idx": 42}, {"high_idx": 10, "image_name": "000000316.png", "low_idx": 42}, {"high_idx": 10, "image_name": "000000317.png", "low_idx": 43}, {"high_idx": 10, "image_name": "000000318.png", "low_idx": 43}, {"high_idx": 10, "image_name": "000000319.png", "low_idx": 43}, {"high_idx": 10, "image_name": "000000320.png", "low_idx": 43}, {"high_idx": 10, "image_name": "000000321.png", "low_idx": 43}, {"high_idx": 10, "image_name": "000000322.png", "low_idx": 43}, {"high_idx": 10, "image_name": "000000323.png", "low_idx": 43}, {"high_idx": 10, "image_name": "000000324.png", "low_idx": 43}, {"high_idx": 10, "image_name": "000000325.png", "low_idx": 43}, {"high_idx": 10, "image_name": "000000326.png", "low_idx": 43}, {"high_idx": 10, "image_name": "000000327.png", "low_idx": 43}, {"high_idx": 10, "image_name": "000000328.png", "low_idx": 44}, {"high_idx": 10, "image_name": "000000329.png", "low_idx": 44}, {"high_idx": 10, "image_name": "000000330.png", "low_idx": 45}, {"high_idx": 10, "image_name": "000000331.png", "low_idx": 45}, {"high_idx": 10, "image_name": "000000332.png", "low_idx": 46}, {"high_idx": 10, "image_name": "000000333.png", "low_idx": 46}, {"high_idx": 10, "image_name": "000000334.png", "low_idx": 47}, {"high_idx": 10, "image_name": "000000335.png", "low_idx": 47}, {"high_idx": 10, "image_name": "000000336.png", "low_idx": 48}, {"high_idx": 10, "image_name": "000000337.png", "low_idx": 48}, {"high_idx": 10, "image_name": "000000338.png", "low_idx": 49}, {"high_idx": 10, "image_name": "000000339.png", "low_idx": 49}, {"high_idx": 10, "image_name": "000000340.png", "low_idx": 50}, {"high_idx": 10, "image_name": "000000341.png", "low_idx": 50}, {"high_idx": 10, "image_name": "000000342.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000343.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000344.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000345.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000346.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000347.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000348.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000349.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000350.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000351.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000352.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000353.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000354.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000355.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000356.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000357.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000358.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000359.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000360.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000361.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000362.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000363.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000364.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000365.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000366.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000367.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000368.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000369.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000370.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000371.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000372.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000373.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000374.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000375.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000376.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000377.png", "low_idx": 59}, {"high_idx": 11, "image_name": "000000378.png", "low_idx": 60}, {"high_idx": 11, "image_name": "000000379.png", "low_idx": 60}, {"high_idx": 11, "image_name": "000000380.png", "low_idx": 60}, {"high_idx": 11, "image_name": "000000381.png", "low_idx": 60}, {"high_idx": 11, "image_name": "000000382.png", "low_idx": 61}, {"high_idx": 11, "image_name": "000000383.png", "low_idx": 61}, {"high_idx": 11, "image_name": "000000384.png", "low_idx": 61}, {"high_idx": 11, "image_name": "000000385.png", "low_idx": 61}, {"high_idx": 11, "image_name": "000000386.png", "low_idx": 61}, {"high_idx": 11, "image_name": "000000387.png", "low_idx": 61}, {"high_idx": 11, "image_name": "000000388.png", "low_idx": 61}, {"high_idx": 11, "image_name": "000000389.png", "low_idx": 61}, {"high_idx": 11, "image_name": "000000390.png", "low_idx": 61}, {"high_idx": 11, "image_name": "000000391.png", "low_idx": 61}, {"high_idx": 11, "image_name": "000000392.png", "low_idx": 61}, {"high_idx": 11, "image_name": "000000393.png", "low_idx": 61}, {"high_idx": 11, "image_name": "000000394.png", "low_idx": 61}, {"high_idx": 11, "image_name": "000000395.png", "low_idx": 61}, {"high_idx": 11, "image_name": "000000396.png", "low_idx": 61}, {"high_idx": 11, "image_name": "000000397.png", "low_idx": 62}, {"high_idx": 11, "image_name": "000000398.png", "low_idx": 62}, {"high_idx": 11, "image_name": "000000399.png", "low_idx": 62}, {"high_idx": 11, "image_name": "000000400.png", "low_idx": 62}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-7|7|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["butterknife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ButterKnife", [-9.157936, -9.157936, 7.32242, 7.32242, 3.6437684, 3.6437684]], "coordinateReceptacleObjectId": ["CounterTop", [-5.94, -5.94, 5.276, 5.276, 3.7856, 3.7856]], "forceVisible": true, "objectId": "ButterKnife|-02.29|+00.91|+01.83"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-7|6|3|45"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-9.157936, -9.157936, 6.12309648, 6.12309648, 3.986878632, 3.986878632]], "forceVisible": true, "objectId": "Bread|-02.29|+01.00|+01.53"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-7|10|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["butterknife", "microwave"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ButterKnife", [-9.157936, -9.157936, 7.32242, 7.32242, 3.6437684, 3.6437684]], "coordinateReceptacleObjectId": ["Microwave", [-10.3232, -10.3232, 9.764, 9.764, 3.6, 3.6]], "forceVisible": true, "objectId": "ButterKnife|-02.29|+00.91|+01.83", "receptacleObjectId": "Microwave|-02.58|+00.90|+02.44"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-7|6|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-9.157936, -9.157936, 6.12309648, 6.12309648, 3.986878632, 3.986878632]], "coordinateReceptacleObjectId": ["CounterTop", [-5.94, -5.94, 5.276, 5.276, 3.7856, 3.7856]], "forceVisible": true, "objectId": "Bread|-02.29|+01.00|+01.53|BreadSliced_1"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|-7|10|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-10.3232, -10.3232, 9.764, 9.764, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-02.58|+00.90|+02.44"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|-7|-3|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "fridge"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-9.157936, -9.157936, 6.12309648, 6.12309648, 3.986878632, 3.986878632]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-9.90399932, -9.90399932, -3.132, -3.132, 0.0, 0.0]], "forceVisible": true, "objectId": "Bread|-02.29|+01.00|+01.53|BreadSliced_1", "receptacleObjectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 12, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ButterKnife|-02.29|+00.91|+01.83"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [170, 79, 177, 144], "mask": [[23572, 3], [23871, 4], [24170, 6], [24470, 6], [24770, 6], [25070, 6], [25370, 7], [25670, 7], [25970, 7], [26271, 6], [26571, 6], [26871, 6], [27171, 6], [27471, 6], [27771, 6], [28071, 5], [28372, 4], [28672, 4], [28972, 4], [29272, 4], [29572, 4], [29872, 4], [30172, 4], [30472, 4], [30772, 4], [31073, 3], [31372, 4], [31672, 4], [31972, 4], [32272, 4], [32572, 4], [32872, 4], [33172, 4], [33472, 5], [33771, 6], [34071, 6], [34371, 6], [34671, 6], [34971, 6], [35270, 7], [35570, 7], [35870, 7], [36170, 7], [36470, 7], [36770, 7], [37070, 7], [37370, 8], [37670, 8], [37970, 8], [38270, 8], [38570, 8], [38870, 8], [39170, 8], [39471, 7], [39771, 7], [40071, 7], [40371, 7], [40672, 6], [40972, 6], [41272, 6], [41573, 5], [41873, 5], [42173, 5], [42474, 4], [42774, 4], [43075, 3]], "point": [173, 110]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-02.29|+01.00|+01.53"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [118, 138, 202, 194], "mask": [[41242, 37], [41286, 1], [41524, 69], [41823, 74], [42122, 76], [42421, 77], [42721, 78], [43021, 78], [43321, 79], [43620, 80], [43920, 80], [44220, 80], [44520, 80], [44820, 81], [45120, 81], [45420, 81], [45720, 81], [46020, 81], [46320, 81], [46619, 83], [46919, 83], [47219, 83], [47519, 83], [47819, 83], [48119, 83], [48419, 83], [48718, 84], [49018, 84], [49318, 84], [49618, 84], [49918, 84], [50218, 85], [50518, 85], [50818, 85], [51118, 85], [51418, 85], [51718, 85], [52018, 85], [52318, 85], [52618, 85], [52918, 85], [53218, 84], [53519, 83], [53819, 83], [54119, 83], [54419, 83], [54719, 82], [55019, 82], [55319, 82], [55619, 82], [55919, 82], [56219, 82], [56520, 80], [56820, 80], [57120, 79], [57421, 78], [57723, 74], [58029, 65]], "point": [160, 165]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ButterKnife|-02.29|+00.91|+01.83", "placeStationary": true, "receptacleObjectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 237], [20100, 237], [20400, 237], [20700, 237], [21000, 236], [21300, 236], [21600, 236], [21900, 236], [22200, 235], [22500, 235], [22800, 235], [23100, 235], [23400, 234], [23700, 234], [24000, 234], [24300, 234], [24600, 233], [24900, 233], [25200, 233], [25500, 233], [25800, 232], [26100, 232], [26400, 232], [26700, 232], [27000, 231], [27300, 231], [27600, 231], [27900, 231], [28200, 230], [28500, 230], [28800, 230], [29100, 230], [29400, 229], [29700, 229], [30000, 229], [30300, 229], [30600, 228], [30900, 228], [31200, 228], [31500, 228], [31800, 227], [32100, 227], [32400, 227], [32700, 227], [33000, 226], [33300, 226], [33600, 226], [33900, 225], [34200, 225], [34500, 225], [34800, 225], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 237], [20100, 237], [20400, 237], [20700, 237], [21000, 236], [21300, 236], [21600, 236], [21900, 236], [22200, 235], [22500, 235], [22800, 235], [23100, 235], [23400, 234], [23700, 234], [24000, 234], [24300, 234], [24600, 233], [24900, 233], [25200, 233], [25500, 233], [25800, 232], [26100, 232], [26400, 232], [26700, 232], [27000, 231], [27300, 231], [27600, 231], [27900, 231], [28200, 230], [28500, 230], [28800, 230], [29100, 230], [29400, 229], [29700, 229], [30000, 229], [30300, 229], [30600, 228], [30900, 228], [31200, 228], [31500, 228], [31800, 227], [32100, 227], [32400, 76], [32493, 134], [32700, 69], [32805, 13], [32839, 88], [33000, 65], [33139, 87], [33300, 64], [33439, 87], [33600, 120], [33737, 89], [33900, 225], [34200, 225], [34500, 225], [34800, 225], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-02.29|+01.00|+01.53|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [145, 138, 159, 194], "mask": [[41245, 14], [41545, 14], [41845, 14], [42145, 14], [42445, 14], [42745, 14], [43045, 14], [43345, 15], [43645, 15], [43945, 15], [44245, 15], [44545, 15], [44845, 15], [45145, 15], [45445, 15], [45745, 15], [46045, 15], [46345, 15], [46645, 15], [46945, 15], [47245, 15], [47545, 15], [47845, 15], [48145, 15], [48445, 15], [48745, 15], [49045, 15], [49345, 15], [49645, 15], [49945, 15], [50245, 15], [50545, 15], [50845, 15], [51145, 15], [51445, 15], [51745, 15], [52045, 15], [52345, 15], [52645, 15], [52945, 15], [53245, 14], [53545, 14], [53845, 14], [54145, 14], [54445, 14], [54745, 14], [55045, 14], [55345, 14], [55645, 14], [55945, 14], [56245, 14], [56545, 14], [56845, 14], [57145, 14], [57445, 14], [57745, 14], [58045, 14]], "point": [152, 165]}}, "high_idx": 7}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-02.29|+01.00|+01.53|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 237], [20100, 237], [20400, 237], [20700, 237], [21000, 236], [21300, 236], [21600, 236], [21900, 236], [22200, 235], [22500, 235], [22800, 235], [23100, 235], [23400, 234], [23700, 234], [24000, 234], [24300, 234], [24600, 233], [24900, 233], [25200, 233], [25500, 233], [25800, 232], [26100, 232], [26400, 232], [26700, 232], [27000, 231], [27300, 231], [27600, 231], [27900, 231], [28200, 230], [28500, 230], [28800, 230], [29100, 230], [29400, 229], [29700, 229], [30000, 229], [30300, 229], [30600, 228], [30900, 228], [31200, 228], [31500, 228], [31800, 227], [32100, 227], [32400, 76], [32493, 134], [32700, 69], [32805, 13], [32839, 88], [33000, 65], [33139, 87], [33300, 64], [33439, 87], [33600, 120], [33737, 89], [33900, 225], [34200, 225], [34500, 225], [34800, 225], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 130], [19962, 75], [20100, 130], [20262, 75], [20400, 129], [20562, 75], [20700, 129], [20862, 75], [21000, 128], [21162, 74], [21300, 128], [21462, 74], [21600, 128], [21762, 74], [21900, 128], [22063, 73], [22200, 128], [22363, 72], [22500, 127], [22664, 71], [22800, 127], [22964, 71], [23100, 127], [23264, 71], [23400, 126], [23564, 70], [23700, 126], [23864, 70], [24000, 126], [24164, 70], [24300, 126], [24464, 70], [24600, 126], [24763, 70], [24900, 126], [25063, 70], [25200, 126], [25363, 70], [25500, 126], [25664, 69], [25800, 126], [25964, 68], [26100, 126], [26264, 68], [26400, 126], [26564, 68], [26700, 126], [26864, 68], [27000, 126], [27164, 67], [27300, 126], [27464, 67], [27600, 126], [27764, 67], [27900, 126], [28064, 67], [28200, 126], [28364, 66], [28500, 126], [28664, 66], [28800, 126], [28964, 66], [29100, 126], [29264, 66], [29400, 127], [29564, 65], [29700, 127], [29864, 65], [30000, 127], [30163, 66], [30300, 127], [30463, 66], [30600, 127], [30763, 65], [30900, 128], [31062, 66], [31200, 131], [31359, 69], [31500, 228], [31800, 227], [32100, 227], [32400, 76], [32493, 134], [32700, 69], [32805, 13], [32839, 88], [33000, 65], [33139, 87], [33300, 64], [33439, 87], [33600, 120], [33737, 89], [33900, 225], [34200, 225], [34500, 225], [34800, 225], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-10.3232, -10.3232, 9.764, 9.764, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-10.3232, -10.3232, 9.764, 9.764, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-02.29|+01.00|+01.53|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [126, 67, 163, 105], "mask": [[19930, 32], [20230, 32], [20529, 33], [20829, 33], [21128, 34], [21428, 34], [21728, 34], [22028, 35], [22328, 35], [22627, 37], [22927, 37], [23227, 37], [23526, 38], [23826, 38], [24126, 38], [24426, 38], [24726, 37], [25026, 37], [25326, 37], [25626, 38], [25926, 38], [26226, 38], [26526, 38], [26826, 38], [27126, 38], [27426, 38], [27726, 38], [28026, 38], [28326, 38], [28626, 38], [28926, 38], [29226, 38], [29527, 37], [29827, 37], [30127, 36], [30427, 36], [30727, 36], [31028, 34], [31331, 28]], "point": [144, 85]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 237], [20100, 237], [20400, 237], [20700, 237], [21000, 236], [21300, 236], [21600, 236], [21900, 236], [22200, 235], [22500, 235], [22800, 235], [23100, 235], [23400, 234], [23700, 234], [24000, 234], [24300, 234], [24600, 233], [24900, 233], [25200, 233], [25500, 233], [25800, 232], [26100, 232], [26400, 232], [26700, 232], [27000, 231], [27300, 231], [27600, 231], [27900, 231], [28200, 230], [28500, 230], [28800, 230], [29100, 230], [29400, 229], [29700, 229], [30000, 229], [30300, 229], [30600, 228], [30900, 228], [31200, 228], [31500, 228], [31800, 227], [32100, 227], [32400, 76], [32493, 134], [32700, 69], [32805, 13], [32839, 88], [33000, 65], [33139, 87], [33300, 64], [33439, 87], [33600, 120], [33737, 89], [33900, 225], [34200, 225], [34500, 225], [34800, 225], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 9}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 241], "mask": [[0, 48599], [48600, 298], [48900, 299], [49200, 299], [49500, 299], [49800, 299], [50100, 299], [50400, 299], [50700, 298], [51000, 297], [51300, 296], [51600, 295], [51901, 293], [52202, 290], [52503, 288], [52805, 285], [53106, 282], [53407, 280], [53708, 278], [54009, 275], [54311, 272], [54612, 270], [54913, 267], [55214, 265], [55515, 262], [55816, 260], [56118, 257], [56419, 254], [56720, 252], [57021, 249], [57322, 247], [57624, 244], [57925, 241], [58226, 239], [58527, 236], [58828, 234], [59129, 232], [59431, 229], [59732, 226], [60033, 224], [60334, 222], [60635, 220], [60936, 218], [61238, 215], [61539, 213], [61840, 211], [62141, 209], [62442, 207], [62744, 204], [63045, 202], [63346, 200], [63647, 197], [63948, 195], [64249, 193], [64551, 190], [64852, 188], [65153, 186], [65454, 184], [65755, 84], [65866, 71], [66056, 74], [66171, 65], [66358, 64], [66475, 60], [66658, 60], [66778, 56], [66958, 57], [67080, 53], [67258, 55], [67382, 50], [67557, 54], [67684, 48], [67857, 51], [67985, 47], [68157, 51], [68286, 45], [68457, 50], [68587, 44], [68758, 49], [68888, 42], [69059, 47], [69189, 40], [69360, 46], [69489, 39], [69662, 44], [69789, 38], [69966, 40], [70090, 33], [70269, 37], [70390, 29], [70573, 33], [70690, 26], [70876, 30], [70990, 22], [71180, 26], [71292, 16], [71486, 19], [71592, 10], [71792, 13], [71892, 4], [72100, 5]], "point": [149, 120]}}, "high_idx": 11}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-02.29|+01.00|+01.53|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 197], [215, 279], [518, 275], [820, 272], [1121, 270], [1423, 267], [1724, 265], [2025, 264], [2325, 264], [2626, 262], [2927, 261], [3227, 261], [3527, 261], [3827, 261], [4127, 261], [4427, 261], [4727, 261], [5026, 263], [5326, 263], [5626, 263], [5925, 264], [6224, 265], [6523, 266], [6823, 266], [7122, 267], [7421, 268], [7721, 268], [8020, 270], [8320, 270], [8619, 271], [8919, 271], [9218, 272], [9518, 272], [9817, 273], [10117, 273], [10416, 274], [10716, 274], [11015, 276], [11315, 276], [11614, 277], [11914, 277], [12213, 279], [12513, 279], [12812, 280], [13112, 282], [13411, 284], [13710, 287], [14008, 293], [14304, 24695], [39000, 298], [39300, 297], [39600, 296], [39900, 295], [40200, 295], [40500, 294], [40800, 293], [41100, 292], [41400, 291], [41700, 290], [42000, 289], [42300, 289], [42600, 288], [42900, 287], [43200, 286], [43500, 285], [43800, 284], [44100, 283], [44400, 282], [44700, 282], [45000, 281], [45300, 280], [45600, 279], [45900, 278], [46200, 277], [46500, 276], [46800, 275], [47100, 275], [47400, 274], [47700, 273], [48000, 272], [48300, 271], [48600, 270], [48900, 269], [49200, 268], [49500, 268], [49800, 267], [50100, 266], [50400, 265], [50700, 264], [51000, 263], [51300, 262], [51600, 261], [51900, 261], [52200, 260], [52500, 259], [52800, 258], [53100, 257], [53400, 256], [53700, 255], [54000, 254], [54300, 254], [54600, 253], [54900, 252], [55200, 251], [55500, 250], [55800, 249], [56100, 248], [56400, 247], [56700, 247], [57000, 246], [57300, 245], [57600, 244], [57900, 243], [58200, 242], [58500, 241], [58800, 240], [59100, 240], [59400, 239], [59700, 238], [60000, 237], [60300, 236], [60600, 235], [60900, 234], [61200, 233], [61500, 233], [61800, 232], [62100, 231], [62400, 230], [62700, 229], [63000, 229], [63300, 229], [63600, 229], [63900, 229], [64200, 229], [64500, 229], [64800, 229], [65100, 230], [65400, 230], [65700, 139], [65866, 64], [66000, 130], [66171, 59], [66300, 122], [66475, 55], [66600, 118], [66778, 52], [66900, 115], [67080, 51], [67200, 113], [67382, 49], [67500, 111], [67684, 47], [67800, 108], [67985, 46], [68100, 108], [68286, 45], [68400, 107], [68587, 44], [68700, 107], [68888, 42], [69000, 58], [69059, 47], [69189, 40], [69300, 57], [69360, 46], [69489, 39], [69600, 57], [69662, 44], [69789, 38], [69900, 57], [69966, 40], [70090, 33], [70200, 57], [70269, 37], [70390, 29], [70500, 57], [70573, 33], [70690, 26], [70800, 56], [70876, 30], [70990, 22], [71100, 56], [71180, 26], [71292, 16], [71400, 56], [71486, 19], [71592, 10], [71700, 56], [71792, 13], [71892, 4], [72000, 56], [72100, 5], [72300, 56], [72600, 55], [72900, 55], [73200, 55], [73500, 55], [73800, 55], [74100, 54], [74400, 54], [74700, 54], [75000, 54], [75300, 54], [75600, 54], [75900, 53], [76200, 53], [76500, 53], [76800, 53], [77100, 53], [77400, 52], [77700, 52], [78000, 52], [78300, 52], [78600, 52], [78900, 51], [79200, 51], [79500, 51], [79800, 51], [80100, 51], [80400, 51], [80700, 50], [81000, 50], [81300, 50], [81600, 50], [81900, 50], [82200, 49], [82500, 49], [82800, 49], [83100, 49], [83400, 49], [83700, 49], [84000, 48], [84300, 48], [84600, 48], [84900, 48], [85200, 48], [85500, 47], [85800, 47], [86100, 47], [86400, 47], [86700, 47], [87000, 47], [87300, 46], [87600, 46], [87900, 46], [88200, 46], [88500, 46], [88800, 45], [89100, 45], [89400, 45], [89700, 45]], "point": [149, 149]}}, "high_idx": 11}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 112], [157, 40], [215, 196], [458, 36], [518, 193], [758, 35], [820, 190], [1058, 34], [1121, 189], [1358, 33], [1423, 187], [1658, 32], [1724, 185], [1958, 31], [2025, 184], [2258, 31], [2325, 184], [2558, 31], [2626, 183], [2858, 30], [2927, 182], [3159, 29], [3227, 182], [3459, 29], [3527, 182], [3760, 28], [3827, 182], [4060, 28], [4127, 181], [4360, 28], [4427, 181], [4660, 28], [4727, 180], [4960, 28], [5026, 181], [5260, 29], [5326, 181], [5560, 29], [5626, 181], [5860, 29], [5925, 182], [6160, 29], [6224, 183], [6460, 29], [6523, 184], [6760, 29], [6823, 184], [7060, 29], [7122, 185], [7360, 29], [7421, 186], [7660, 29], [7721, 186], [7960, 29], [8020, 187], [8260, 30], [8320, 187], [8560, 30], [8619, 188], [8860, 30], [8919, 188], [9160, 30], [9218, 189], [9460, 30], [9518, 189], [9760, 30], [9817, 190], [10060, 30], [10117, 190], [10360, 30], [10416, 191], [10660, 30], [10716, 191], [10960, 30], [11015, 192], [11260, 31], [11315, 192], [11560, 31], [11614, 193], [11860, 31], [11914, 193], [12160, 31], [12213, 195], [12460, 32], [12513, 195], [12760, 32], [12812, 196], [13060, 32], [13112, 196], [13360, 34], [13411, 197], [13660, 35], [13710, 199], [13960, 37], [14008, 201], [14260, 41], [14304, 205], [14560, 249], [14860, 250], [15160, 250], [15460, 250], [15760, 250], [16059, 252], [16359, 253], [16658, 257], [16955, 22044], [39000, 298], [39300, 297], [39600, 296], [39900, 295], [40200, 295], [40500, 294], [40800, 293], [41100, 292], [41400, 291], [41700, 290], [42000, 289], [42300, 289], [42600, 288], [42900, 287], [43200, 286], [43500, 285], [43800, 284], [44100, 283], [44400, 282], [44700, 282], [45000, 281], [45300, 280], [45600, 279], [45900, 278], [46200, 277], [46500, 276], [46800, 275], [47100, 275], [47400, 274], [47700, 273], [48000, 272], [48300, 271], [48600, 270], [48900, 269], [49200, 268], [49500, 268], [49800, 267], [50100, 266], [50400, 265], [50700, 264], [51000, 263], [51300, 262], [51600, 261], [51900, 261], [52200, 260], [52500, 259], [52800, 258], [53100, 257], [53400, 256], [53700, 255], [54000, 254], [54300, 254], [54600, 253], [54900, 252], [55200, 251], [55500, 250], [55800, 249], [56100, 248], [56400, 247], [56700, 247], [57000, 246], [57300, 245], [57600, 244], [57900, 243], [58200, 242], [58500, 241], [58800, 240], [59100, 240], [59400, 239], [59700, 238], [60000, 237], [60300, 236], [60600, 235], [60900, 234], [61200, 233], [61500, 233], [61800, 232], [62100, 231], [62400, 230], [62700, 229], [63000, 229], [63300, 229], [63600, 229], [63900, 229], [64200, 229], [64500, 229], [64800, 229], [65100, 230], [65400, 230], [65700, 230], [66000, 230], [66300, 230], [66600, 230], [66900, 231], [67200, 231], [67500, 231], [67800, 231], [68100, 231], [68400, 231], [68700, 230], [69000, 58], [69059, 170], [69300, 57], [69360, 168], [69600, 57], [69662, 165], [69900, 57], [69966, 157], [70200, 57], [70269, 150], [70500, 57], [70573, 143], [70800, 56], [70876, 136], [71100, 56], [71180, 128], [71400, 56], [71486, 116], [71700, 56], [71792, 104], [72000, 56], [72100, 89], [72300, 56], [72408, 72], [72600, 55], [72718, 52], [72900, 55], [73200, 55], [73500, 55], [73800, 55], [74100, 54], [74400, 54], [74700, 54], [75000, 54], [75300, 54], [75600, 54], [75900, 53], [76200, 53], [76500, 53], [76800, 53], [77100, 53], [77400, 52], [77700, 52], [78000, 52], [78300, 52], [78600, 52], [78900, 51], [79200, 51], [79500, 51], [79800, 51], [80100, 51], [80400, 51], [80700, 50], [81000, 50], [81300, 50], [81600, 50], [81900, 50], [82200, 49], [82500, 49], [82800, 49], [83100, 49], [83400, 49], [83700, 49], [84000, 48], [84300, 48], [84600, 48], [84900, 48], [85200, 48], [85500, 47], [85800, 47], [86100, 47], [86400, 47], [86700, 47], [87000, 47], [87300, 46], [87600, 46], [87900, 46], [88200, 46], [88500, 46], [88800, 45], [89100, 45], [89400, 45], [89700, 45]], "point": [149, 149]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan6", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -1.75, "y": 0.9009992, "z": 1.5}, "object_poses": [{"objectName": "Pan_adb25946", "position": {"x": 1.4123, "y": 0.9295, "z": -0.3359}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_b563bb4b", "position": {"x": 1.27555382, "y": 0.9549633, "z": -1.10730672}, "rotation": {"x": 0.0, "y": 315.000031, "z": 0.0}}, {"objectName": "DishSponge_1f5d88fd", "position": {"x": -0.08153126, "y": 0.9104642, "z": 1.31733882}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_1f5d88fd", "position": {"x": -2.2879, "y": 0.7729672, "z": 0.391211122}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f05a14b3", "position": {"x": -2.289484, "y": 0.9109421, "z": 1.830605}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f05a14b3", "position": {"x": -0.471179664, "y": 0.9109421, "z": 1.31733882}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_850ba9bc", "position": {"x": 1.41873252, "y": 0.919178665, "z": -1.03414083}, "rotation": {"x": 0.0, "y": 45.0000343, "z": 0.0}}, {"objectName": "Fork_850ba9bc", "position": {"x": -2.62175035, "y": 0.911448538, "z": 0.6312814}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_3e5dadd0", "position": {"x": -2.34871125, "y": 0.788402855, "z": 1.46587777}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_3e5dadd0", "position": {"x": 0.6832595, "y": 0.925899863, "z": -1.5399915}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_2802642b", "position": {"x": -2.53202271, "y": 1.33104134, "z": -0.5077681}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_7b0d90af", "position": {"x": -2.62175035, "y": 0.913369656, "z": 1.53077412}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9b863229", "position": {"x": 1.60406375, "y": 0.227560729, "z": 0.679}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9b863229", "position": {"x": -2.45561719, "y": 0.9532293, "z": 0.6312814}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_b57df01c", "position": {"x": -0.341296852, "y": 0.912865162, "z": 0.423983335}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_988570b5", "position": {"x": -2.605, "y": 1.53480625, "z": 1.48311257}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_988570b5", "position": {"x": -2.31330013, "y": 0.112172425, "z": 0.48987487}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_e9198ae5", "position": {"x": -0.6010624, "y": 0.905899942, "z": 1.094}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_e9198ae5", "position": {"x": 1.772001, "y": 1.6587497, "z": -0.263052762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_81e9b3dd", "position": {"x": 1.66249847, "y": 1.6587497, "z": -0.407017648}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6f8b6e53", "position": {"x": -0.08153126, "y": 0.994053841, "z": 0.423983335}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_209e2ea9", "position": {"x": -2.289484, "y": 0.996719658, "z": 1.53077412}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_adb25946", "position": {"x": 1.6916, "y": 0.9295, "z": -0.3359}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_f4ae1c7a", "position": {"x": 0.29285, "y": 0.9362496, "z": -1.795017}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_57ff6e2f", "position": {"x": 1.73979115, "y": 0.972511232, "z": -0.9306413}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_988570b5", "position": {"x": -2.6099, "y": 1.9309833, "z": -1.03440011}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_850ba9bc", "position": {"x": 1.51372623, "y": 0.9114485, "z": -0.8100381}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_209e2ea9", "position": {"x": -0.6010624, "y": 0.996719658, "z": 0.870661139}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_7b0d90af", "position": {"x": -0.341296852, "y": 0.913369656, "z": 0.6473222}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_2802642b", "position": {"x": -0.211414054, "y": 0.94152844, "z": 1.094}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_0884e440", "position": {"x": 0.8544904, "y": 0.900000036, "z": -1.790359}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_b563bb4b", "position": {"x": -2.466795, "y": 0.6311115, "z": -0.783}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_81e9b3dd", "position": {"x": -2.53026128, "y": 1.93354988, "z": -0.6343242}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6f8b6e53", "position": {"x": -2.45561719, "y": 0.994053841, "z": 0.9311123}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_b0ad5ef5", "position": {"x": -2.39333987, "y": 1.08702564, "z": -0.6021707}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_b57df01c", "position": {"x": 1.34556663, "y": 0.9205953, "z": -1.17731965}, "rotation": {"x": 0.0, "y": 135.000031, "z": 0.0}}, {"objectName": "Spatula_3e5dadd0", "position": {"x": -2.704817, "y": 0.925899863, "z": 0.9311123}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_40552007", "position": {"x": 1.771809, "y": 0.289516717, "z": 0.706531167}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f05a14b3", "position": {"x": 1.68401384, "y": 0.9111421, "z": 0.4652969}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_e9198ae5", "position": {"x": 1.80850184, "y": 1.6587497, "z": -0.5509825}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_1f5d88fd", "position": {"x": -2.31830549, "y": 0.7729672, "z": 0.5348777}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_bc458cba", "position": {"x": -2.45561719, "y": 0.9146199, "z": 0.03161955}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_0b8e1a97", "position": {"x": -0.7309452, "y": 0.9120294, "z": 0.6473222}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9b863229", "position": {"x": 1.56861067, "y": 0.953229249, "z": -0.76722765}, "rotation": {"x": 0.0, "y": 44.9999161, "z": 0.0}}, {"objectName": "Mug_db7f1139", "position": {"x": 0.4640001, "y": 0.9558, "z": -1.5935}, "rotation": {"x": 0.0, "y": 3.24433968e-05, "z": 0.0}}], "object_toggles": [], "random_seed": 3508741460, "scene_num": 6}, "task_id": "trial_T20190909_132620_261591", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1O3TWBUDONVLO_3GS6S824STOTXE72EQQN2KM8H6SWN8", "high_descs": ["Turn left to face the counter.", "Pick up the knife on the counter.", "Look to the left to face the bread on the counter.", "Cut the bread on the counter into slices.", "Step right to face the microwave.", "Place the knife in the microwave.", "Step left to face the bread on the counter.", "Pick up a slice of bread on the counter.", "Step to the right to face the microwave.", "Heat the bread slice in the microwave and remove it.", "Turn left and walk forward and turn right to face the fridge.", "Place the bread slice in the fridge."], "task_desc": "To heat a bread slice and place it in the fridge.", "votes": [1, 1]}, {"assignment_id": "A12HZGOZQD5YK7_3QJOXOW4XMITYW0BHPHH3ZQ85B8EM3", "high_descs": ["Turn left towards the toaster.", "Pick up the knife in front of the toaster.", "Move left to the bread on the counter.", "Cut the bread on the counter.", "Turn right and go to the microwave.", "Put the knife in the microwave.", "Turn left and go back to the bread on the counter.", "Pick up a slice of bread off the counter.", "Turn right and go to the microwave.", "Heat up the slice of bread in the microwave and pick it back up.", "Turn left and go to the fridge.", "Put the slice of bread in the freezer to the left of the glass."], "task_desc": "Put a warm slice of bread in the freezer.", "votes": [1, 1]}, {"assignment_id": "A29ZQYDG9Q6TPK_3TR2532VISLZK91B8ALRBJ6EKJTJ6N", "high_descs": ["Turn left and move to the cabinet.", "Pick up the knife from the edge of the cabinet.", "Turn around right and face the bread on the cabinet.", "Slice the bread with the knife.", "Move the microwave on the cabinet.", "Open the microwave and put the knife in it.", "Move to the slice bread.", "Pick up a slice of the bread.", "Turn to your right and move to the microwave on the cabinet.", "Open the microwave put the slice of bread inside and turn on the microwave.", "Open the microwave and pick up the slice of bread, then turn around left and head towards the refrigerator on your right.", "Open the refrigerator, put the slice of bread inside and close it."], "task_desc": "Put a hot slice of bread in the refrigerator.", "votes": [1, 1]}]}}