{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000207.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000208.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000209.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000210.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000211.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000212.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 36}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|0|0|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-0.3297932, -0.3297932, -2.2924524, -2.2924524, 3.170890568, 3.170890568]], "coordinateReceptacleObjectId": ["SinkBasin", [-0.1172, -0.1172, -2.45, -2.45, 2.992, 2.992]], "forceVisible": true, "objectId": "Egg|-00.08|+00.79|-00.57"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-2|0|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-2|4|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-0.3297932, -0.3297932, -2.2924524, -2.2924524, 3.170890568, 3.170890568]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-5.204, -5.204, 3.952, 3.952, 0.032000004, 0.032000004]], "forceVisible": true, "objectId": "Egg|-00.08|+00.79|-00.57", "receptacleObjectId": "<PERSON><PERSON>|-01.30|+00.01|+00.99"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.08|+00.79|-00.57"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [171, 124, 188, 139], "mask": [[37076, 4], [37374, 8], [37672, 12], [37972, 13], [38271, 15], [38571, 16], [38871, 16], [39171, 17], [39472, 16], [39772, 17], [40072, 17], [40373, 16], [40674, 15], [40975, 14], [41276, 12], [41578, 9]], "point": [179, 130]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.08|+00.79|-00.57", "placeStationary": true, "receptacleObjectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 74], [20522, 159], [20744, 72], [20825, 155], [21044, 71], [21126, 154], [21343, 71], [21427, 152], [21643, 71], [21728, 151], [21942, 71], [22029, 150], [22241, 72], [22329, 149], [22541, 71], [22630, 148], [22840, 72], [22930, 147], [23140, 71], [23231, 146], [23439, 72], [23531, 42], [23582, 95], [23739, 72], [23831, 39], [23886, 90], [24038, 73], [24132, 36], [24188, 88], [24337, 73], [24432, 35], [24489, 87], [24637, 74], [24732, 34], [24790, 85], [24936, 75], [25032, 34], [25091, 84], [25236, 75], [25332, 34], [25391, 83], [25535, 76], [25632, 34], [25694, 80], [25834, 77], [25932, 34], [25996, 78], [26134, 78], [26232, 34], [26291, 2], [26296, 77], [26433, 79], [26531, 35], [26591, 3], [26597, 76], [26733, 80], [26831, 35], [26891, 3], [26896, 76], [27032, 82], [27131, 35], [27190, 3], [27196, 76], [27331, 84], [27430, 36], [27490, 3], [27496, 76], [27631, 84], [27729, 37], [27790, 2], [27795, 76], [27930, 86], [28028, 38], [28090, 2], [28095, 76], [28230, 89], [28326, 40], [28389, 2], [28394, 77], [28529, 137], [28689, 2], [28694, 76], [28829, 137], [28989, 1], [28993, 77], [29128, 138], [29289, 1], [29293, 76], [29427, 139], [29592, 77], [29727, 139], [29891, 78], [30026, 140], [30191, 77], [30326, 140], [30490, 78], [30625, 141], [30789, 79], [30924, 142], [31088, 79], [31224, 142], [31388, 79], [31523, 143], [31688, 78], [31823, 143], [31988, 78], [32122, 144], [32287, 79], [32421, 145], [32587, 78], [32721, 145], [32887, 78], [33020, 146], [33187, 77], [33320, 147], [33486, 78], [33619, 149], [33785, 79], [33918, 152], [34084, 79], [34218, 154], [34382, 81], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 74], [20522, 159], [20744, 72], [20825, 155], [21044, 71], [21126, 154], [21343, 71], [21427, 152], [21643, 71], [21728, 151], [21942, 71], [22029, 150], [22241, 72], [22329, 149], [22541, 71], [22630, 148], [22840, 72], [22930, 147], [23140, 71], [23231, 146], [23439, 72], [23531, 42], [23582, 95], [23739, 72], [23831, 39], [23886, 90], [24038, 73], [24132, 36], [24188, 88], [24337, 73], [24432, 35], [24489, 87], [24637, 74], [24732, 34], [24790, 85], [24936, 75], [25032, 34], [25091, 84], [25236, 75], [25332, 34], [25391, 83], [25535, 76], [25632, 34], [25694, 80], [25834, 77], [25932, 34], [25996, 78], [26134, 78], [26232, 34], [26291, 2], [26296, 77], [26433, 79], [26531, 35], [26591, 3], [26597, 76], [26733, 80], [26831, 35], [26891, 3], [26896, 76], [27032, 82], [27131, 35], [27190, 3], [27196, 76], [27331, 84], [27430, 36], [27490, 3], [27496, 76], [27631, 84], [27729, 37], [27790, 2], [27795, 76], [27930, 86], [28028, 38], [28090, 2], [28095, 76], [28230, 89], [28326, 20], [28351, 15], [28389, 2], [28394, 77], [28529, 116], [28652, 14], [28689, 2], [28694, 76], [28829, 115], [28953, 13], [28989, 1], [28993, 77], [29128, 115], [29254, 12], [29289, 1], [29293, 76], [29427, 115], [29554, 12], [29592, 77], [29727, 115], [29855, 11], [29891, 78], [30026, 115], [30155, 11], [30191, 77], [30326, 115], [30456, 10], [30490, 78], [30625, 116], [30756, 10], [30789, 79], [30924, 117], [31056, 10], [31088, 79], [31224, 117], [31356, 10], [31388, 79], [31523, 118], [31656, 10], [31688, 78], [31823, 118], [31956, 10], [31988, 78], [32122, 119], [32256, 10], [32287, 79], [32421, 121], [32555, 11], [32587, 78], [32721, 121], [32855, 11], [32887, 78], [33020, 123], [33154, 12], [33187, 77], [33320, 123], [33453, 14], [33486, 78], [33619, 126], [33752, 16], [33785, 79], [33918, 128], [34050, 20], [34084, 79], [34218, 154], [34382, 81], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.08|+00.79|-00.57"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [141, 95, 155, 114], "mask": [[28346, 5], [28645, 7], [28944, 9], [29243, 11], [29542, 12], [29842, 13], [30141, 14], [30441, 15], [30741, 15], [31041, 15], [31341, 15], [31641, 15], [31941, 15], [32241, 15], [32542, 13], [32842, 13], [33143, 11], [33443, 10], [33745, 7], [34046, 4]], "point": [148, 103]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 74], [20522, 159], [20744, 72], [20825, 155], [21044, 71], [21126, 154], [21343, 71], [21427, 152], [21643, 71], [21728, 151], [21942, 71], [22029, 150], [22241, 72], [22329, 149], [22541, 71], [22630, 148], [22840, 72], [22930, 147], [23140, 71], [23231, 146], [23439, 72], [23531, 42], [23582, 95], [23739, 72], [23831, 39], [23886, 90], [24038, 73], [24132, 36], [24188, 88], [24337, 73], [24432, 35], [24489, 87], [24637, 74], [24732, 34], [24790, 85], [24936, 75], [25032, 34], [25091, 84], [25236, 75], [25332, 34], [25391, 83], [25535, 76], [25632, 34], [25694, 80], [25834, 77], [25932, 34], [25996, 78], [26134, 78], [26232, 34], [26291, 2], [26296, 77], [26433, 79], [26531, 35], [26591, 3], [26597, 76], [26733, 80], [26831, 35], [26891, 3], [26896, 76], [27032, 82], [27131, 35], [27190, 3], [27196, 76], [27331, 84], [27430, 36], [27490, 3], [27496, 76], [27631, 84], [27729, 37], [27790, 2], [27795, 76], [27930, 86], [28028, 38], [28090, 2], [28095, 76], [28230, 89], [28326, 40], [28389, 2], [28394, 77], [28529, 137], [28689, 2], [28694, 76], [28829, 137], [28989, 1], [28993, 77], [29128, 138], [29289, 1], [29293, 76], [29427, 139], [29592, 77], [29727, 139], [29891, 78], [30026, 140], [30191, 77], [30326, 140], [30490, 78], [30625, 141], [30789, 79], [30924, 142], [31088, 79], [31224, 142], [31388, 79], [31523, 143], [31688, 78], [31823, 143], [31988, 78], [32122, 144], [32287, 79], [32421, 145], [32587, 78], [32721, 145], [32887, 78], [33020, 146], [33187, 77], [33320, 147], [33486, 78], [33619, 149], [33785, 79], [33918, 152], [34084, 79], [34218, 154], [34382, 81], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-01.30|+00.01|+00.99"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 233], "mask": [[0, 56399], [56400, 298], [56700, 297], [57000, 295], [57300, 294], [57601, 292], [57902, 289], [58204, 286], [58505, 284], [58807, 280], [59108, 278], [59409, 276], [59711, 272], [60012, 270], [60314, 267], [60615, 264], [60916, 262], [61218, 259], [61519, 256], [61821, 253], [62122, 251], [62423, 248], [62725, 245], [63026, 243], [63327, 240], [63629, 237], [63930, 235], [64232, 231], [64533, 229], [64834, 227], [65136, 223], [65437, 221], [65739, 218], [66040, 215], [66341, 213], [66643, 210], [66944, 207], [67246, 204], [67547, 202], [67848, 199], [68150, 196], [68451, 194], [68752, 191], [69054, 188], [69355, 186], [69657, 182]], "point": [149, 116]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.08|+00.79|-00.57", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-01.30|+00.01|+00.99"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 22339], [22354, 23], [22417, 222], [22655, 22], [22717, 221], [22955, 22], [23018, 219], [23256, 21], [23318, 218], [23557, 20], [23618, 217], [23858, 19], [23919, 216], [24158, 18], [24219, 216], [24459, 17], [24519, 215], [24759, 17], [24819, 215], [25059, 17], [25119, 214], [25360, 16], [25419, 214], [25660, 16], [25720, 213], [25960, 17], [26020, 213], [26261, 16], [26320, 212], [26561, 16], [26620, 212], [26861, 16], [26920, 212], [27161, 16], [27220, 212], [27461, 16], [27520, 212], [27762, 15], [27821, 211], [28062, 15], [28121, 210], [28362, 15], [28421, 211], [28662, 15], [28721, 211], [28962, 15], [29021, 211], [29262, 16], [29322, 210], [29561, 17], [29622, 210], [29861, 17], [29922, 210], [30161, 17], [30222, 211], [30461, 17], [30523, 210], [30761, 17], [30823, 210], [31061, 17], [31123, 210], [31361, 17], [31423, 210], [31661, 17], [31723, 210], [31960, 18], [32024, 210], [32260, 18], [32324, 210], [32560, 18], [32624, 210], [32860, 18], [32924, 210], [33159, 19], [33224, 211], [33459, 19], [33524, 211], [33759, 19], [33824, 211], [34058, 20], [34124, 212], [34358, 21], [34424, 212], [34658, 21], [34724, 213], [34957, 22], [35024, 213], [35257, 22], [35324, 214], [35556, 23], [35624, 215], [35855, 24], [35924, 217], [36153, 26], [36224, 220], [36450, 29], [36524, 255], [36823, 256], [37122, 257], [37422, 258], [37722, 258], [38022, 258], [38322, 258], [38622, 258], [38921, 260], [39221, 260], [39520, 3859], [43395, 105], [43501, 178], [43695, 105], [43802, 177], [43996, 104], [44103, 176], [44296, 104], [44404, 174], [44596, 104], [44705, 173], [44896, 104], [45006, 173], [45196, 104], [45307, 172], [45497, 103], [45608, 171], [45796, 104], [45909, 171], [46096, 104], [46210, 170], [46396, 104], [46511, 170], [46696, 104], [46812, 170], [46996, 104], [47113, 169], [47296, 104], [47414, 169], [47595, 105], [47715, 169], [47894, 106], [48016, 171], [48193, 107], [48317, 283], [48618, 282], [48919, 281], [49220, 280], [49521, 279], [49822, 278], [50123, 277], [50424, 276], [50724, 276], [51025, 275], [51326, 274], [51627, 273], [51928, 272], [52229, 271], [52530, 270], [52831, 269], [53132, 268], [53433, 267], [53734, 266], [54035, 265], [54336, 264], [54637, 263], [54938, 262], [55239, 261], [55540, 260], [55841, 259], [56142, 258], [56443, 257], [56744, 256], [57045, 255], [57346, 254], [57647, 253], [57948, 252], [58249, 251], [58549, 251], [58850, 250], [59151, 249], [59452, 248], [59753, 247], [60054, 246], [60355, 245], [60656, 244], [60957, 243], [61258, 242], [61559, 241], [61860, 240], [62162, 172], [62335, 65], [62635, 65], [62935, 65], [63235, 65], [63535, 65], [63835, 65], [64135, 65], [64436, 64], [64736, 64], [65036, 64], [65336, 64], [65636, 64], [65936, 64], [66237, 63], [66537, 63], [66837, 63], [67137, 63], [67437, 63], [67737, 63], [68038, 62], [68338, 62], [68638, 62], [68938, 62], [69238, 62], [69538, 62], [69839, 61], [70139, 61], [70439, 61], [70739, 61], [71039, 61], [71339, 61], [71640, 60], [71940, 60], [72240, 60], [72540, 60], [72840, 60], [73140, 60], [73441, 59], [73741, 59], [74041, 59], [74341, 59], [74641, 59], [74941, 59], [75242, 58], [75542, 58], [75842, 58], [76142, 58], [76442, 58], [76742, 58], [77043, 57], [77343, 57], [77643, 57], [77943, 57], [78243, 57], [78543, 57], [78844, 56], [79144, 56], [79444, 56], [79744, 56], [80044, 56], [80344, 56], [80645, 55], [80945, 55], [81245, 55], [81545, 55], [81845, 55], [82145, 55], [82446, 54], [82746, 54], [83046, 54], [83346, 54], [83646, 54], [83946, 54], [84247, 53], [84547, 53], [84847, 53], [85147, 53], [85447, 53], [85747, 53], [86048, 52], [86348, 52], [86648, 52], [86948, 52], [87248, 52], [87548, 52], [87849, 51], [88149, 51], [88449, 51], [88749, 51], [89049, 51], [89349, 51], [89650, 50], [89950, 50]], "point": [149, 149]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-01.30|+00.01|+00.99"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 22339], [22354, 23], [22417, 222], [22655, 22], [22717, 221], [22955, 22], [23018, 219], [23256, 21], [23318, 218], [23557, 20], [23618, 217], [23858, 19], [23919, 216], [24158, 18], [24219, 216], [24459, 17], [24519, 215], [24759, 17], [24819, 215], [25059, 17], [25119, 214], [25360, 16], [25419, 214], [25660, 16], [25720, 213], [25960, 17], [26020, 213], [26261, 16], [26320, 212], [26561, 16], [26620, 212], [26861, 16], [26920, 212], [27161, 16], [27220, 212], [27461, 16], [27520, 212], [27762, 15], [27821, 211], [28062, 15], [28121, 210], [28362, 15], [28421, 211], [28662, 15], [28721, 211], [28962, 15], [29021, 211], [29262, 16], [29322, 210], [29561, 17], [29622, 210], [29861, 17], [29922, 210], [30161, 17], [30222, 211], [30461, 17], [30523, 210], [30761, 17], [30823, 210], [31061, 17], [31123, 210], [31361, 17], [31423, 210], [31661, 17], [31723, 210], [31960, 18], [32024, 210], [32260, 18], [32324, 210], [32560, 18], [32624, 210], [32860, 18], [32924, 210], [33159, 19], [33224, 211], [33459, 19], [33524, 192], [33722, 13], [33759, 19], [33824, 191], [34023, 12], [34058, 20], [34124, 190], [34324, 12], [34358, 21], [34424, 189], [34625, 11], [34658, 21], [34724, 189], [34925, 12], [34957, 22], [35024, 188], [35226, 11], [35257, 22], [35324, 188], [35526, 12], [35556, 23], [35624, 188], [35826, 13], [35855, 24], [35924, 188], [36126, 15], [36153, 26], [36224, 188], [36426, 18], [36450, 29], [36524, 189], [36726, 53], [36823, 190], [37026, 53], [37122, 192], [37326, 53], [37422, 192], [37625, 55], [37722, 193], [37924, 56], [38022, 195], [38223, 57], [38322, 258], [38622, 258], [38921, 260], [39221, 260], [39520, 3859], [43395, 105], [43501, 178], [43695, 105], [43802, 177], [43996, 104], [44103, 176], [44296, 104], [44404, 174], [44596, 104], [44705, 173], [44896, 104], [45006, 173], [45196, 104], [45307, 172], [45497, 103], [45608, 171], [45796, 104], [45909, 171], [46096, 104], [46210, 170], [46396, 104], [46511, 170], [46696, 104], [46812, 170], [46996, 104], [47113, 169], [47296, 104], [47414, 169], [47595, 105], [47715, 169], [47894, 106], [48016, 171], [48193, 107], [48317, 283], [48618, 282], [48919, 281], [49220, 280], [49521, 279], [49822, 278], [50123, 277], [50424, 276], [50724, 276], [51025, 275], [51326, 274], [51627, 273], [51928, 272], [52229, 271], [52530, 270], [52831, 269], [53132, 268], [53433, 267], [53734, 266], [54035, 265], [54336, 264], [54637, 263], [54938, 262], [55239, 261], [55540, 260], [55841, 259], [56142, 258], [56443, 257], [56744, 256], [57045, 255], [57346, 254], [57647, 253], [57948, 252], [58249, 251], [58549, 251], [58850, 250], [59151, 249], [59452, 248], [59753, 247], [60054, 246], [60355, 245], [60656, 244], [60957, 243], [61258, 242], [61559, 241], [61860, 240], [62162, 172], [62335, 65], [62635, 65], [62935, 65], [63235, 65], [63535, 65], [63835, 65], [64135, 65], [64436, 64], [64736, 64], [65036, 64], [65336, 64], [65636, 64], [65936, 64], [66237, 63], [66537, 63], [66837, 63], [67137, 63], [67437, 63], [67737, 63], [68038, 62], [68338, 62], [68638, 62], [68938, 62], [69238, 62], [69538, 62], [69839, 61], [70139, 61], [70439, 61], [70739, 61], [71039, 61], [71339, 61], [71640, 60], [71940, 60], [72240, 60], [72540, 60], [72840, 60], [73140, 60], [73441, 59], [73741, 59], [74041, 59], [74341, 59], [74641, 59], [74941, 59], [75242, 58], [75542, 58], [75842, 58], [76142, 58], [76442, 58], [76742, 58], [77043, 57], [77343, 57], [77643, 57], [77943, 57], [78243, 57], [78543, 57], [78844, 56], [79144, 56], [79444, 56], [79744, 56], [80044, 56], [80344, 56], [80645, 55], [80945, 55], [81245, 55], [81545, 55], [81845, 55], [82145, 55], [82446, 54], [82746, 54], [83046, 54], [83346, 54], [83646, 54], [83946, 54], [84247, 53], [84547, 53], [84847, 53], [85147, 53], [85447, 53], [85747, 53], [86048, 52], [86348, 52], [86648, 52], [86948, 52], [87248, 52], [87548, 52], [87849, 51], [88149, 51], [88449, 51], [88749, 51], [89049, 51], [89349, 51], [89650, 50], [89950, 50]], "point": [149, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan17", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": 0.25, "y": 0.908999562, "z": 2.5}, "object_poses": [{"objectName": "SprayBottle_4f8d0eee", "position": {"x": 1.128434, "y": 0.9109041, "z": 0.1391859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_edec52fd", "position": {"x": -1.20685518, "y": 1.46618843, "z": 0.9880003}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "CellPhone_e3d6b27e", "position": {"x": -1.29051554, "y": 0.747337937, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_e3d6b27e", "position": {"x": -1.47583234, "y": 0.746611655, "z": 1.90228331}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": -1.10519874, "y": 0.7489421, "z": 2.459}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": 1.36722, "y": 0.8863421, "z": 2.80043745}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_6018293c", "position": {"x": -1.14979517, "y": 0.7518743, "z": 0.9880004}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Glassbottle_6018293c", "position": {"x": 1.450345, "y": 0.8870167, "z": 2.15356255}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_6b7178a8", "position": {"x": -1.47583234, "y": 0.7498287, "z": 3.015717}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_6b7178a8", "position": {"x": 1.117845, "y": 0.8872287, "z": 2.58481264}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_c40d8d1f", "position": {"x": 1.128434, "y": 0.91254133, "z": -0.4283477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_1d6d1e51", "position": {"x": 0.103819013, "y": 0.7597073, "z": -0.6125}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_1d6d1e51", "position": {"x": 0.0417298935, "y": 0.7596856, "z": -0.65188694}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Spatula_2f163330", "position": {"x": -1.01254034, "y": 0.763899863, "z": 2.273428}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": 1.28637147, "y": 0.9264999, "z": 0.1391859}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pot_92502562", "position": {"x": 0.5029, "y": 0.9393, "z": -0.4699}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_1ede3fc6", "position": {"x": -0.0824483, "y": 0.792722642, "z": -0.5731131}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_572193ee", "position": {"x": -1.37803507, "y": 1.4205054, "z": 0.9879998}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": 1.444309, "y": 0.9123855, "z": 0.4229527}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": 1.21878171, "y": 0.117943287, "z": 0.0302069336}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_268c1c72", "position": {"x": 0.0541234, "y": 0.934092641, "z": -0.3711987}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Knife_268c1c72", "position": {"x": -0.0380308032, "y": 0.934092641, "z": -0.354357183}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_57296338", "position": {"x": 1.18936872, "y": 0.135980517, "z": 1.44079888}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_57296338", "position": {"x": -1.19785714, "y": 0.7993551, "z": 1.90228331}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_74101882", "position": {"x": -1.20685577, "y": 1.70037234, "z": 1.18811452}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bread_74101882", "position": {"x": 1.117845, "y": 0.9497149, "z": 2.261375}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b6eaf348", "position": {"x": -1.10519874, "y": 0.7452062, "z": 2.08785558}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b6eaf348", "position": {"x": 1.20740271, "y": 0.9078062, "z": 0.1391859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c297f602", "position": {"x": -0.91988194, "y": 0.7446263, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c297f602", "position": {"x": -1.29628551, "y": 1.01045263, "z": 0.09851481}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_7daf87a0", "position": {"x": -0.0203592032, "y": 0.822128, "z": -0.65188694}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_74101882", "position": {"x": -1.20685577, "y": 0.8145725, "z": 1.19737518}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Glassbottle_6018293c", "position": {"x": 1.20740271, "y": 0.9122167, "z": 0.7067195}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_9f8771c2", "position": {"x": 1.284095, "y": 0.886848569, "z": 2.36918736}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Kettle_c40d8d1f", "position": {"x": -1.32594109, "y": 1.6567651, "z": 0.222244546}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_57296338", "position": {"x": -1.32097411, "y": 1.47098637, "z": 0.67393744}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": 0.9274771, "y": 2.15623665, "z": -0.7877707}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_e3d6b27e", "position": {"x": -1.12754714, "y": 0.773710251, "z": -0.205843091}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Lettuce_6438ff8b", "position": {"x": 1.450345, "y": 0.957268536, "z": 2.58481264}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_92502562", "position": {"x": 0.802, "y": 0.9393, "z": -0.7406}, "rotation": {"x": 0.0, "y": 89.9998245, "z": 0.0}}, {"objectName": "SoapBottle_e0c21b7c", "position": {"x": -0.6649605, "y": 1.657526, "z": -0.786445856}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_6b7178a8", "position": {"x": -1.47583234, "y": 0.7498287, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": 1.52327764, "y": 0.9264999, "z": -0.4283477}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_7e041f70", "position": {"x": -1.19785714, "y": 0.7480669, "z": 3.015717}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_7daf87a0", "position": {"x": -1.394439, "y": 1.07565665, "z": -0.109643027}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_572193ee", "position": {"x": 1.444309, "y": 0.910747945, "z": 0.7067195}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_268c1c72", "position": {"x": -1.01254034, "y": 0.7708927, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_1ede3fc6", "position": {"x": -1.383174, "y": 0.783066452, "z": 2.459}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_8429d040", "position": {"x": -1.30815339, "y": 0.113363981, "z": 0.330922544}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "PepperShaker_b6eaf348", "position": {"x": -1.3881619, "y": 2.14979625, "z": 0.481857359}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_edec52fd", "position": {"x": -1.19416916, "y": 0.386491656, "z": 1.19737518}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": -1.19785714, "y": 0.7489421, "z": 2.83014464}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_4b57442b", "position": {"x": -1.43764663, "y": 0.9116642, "z": -0.6474203}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_a0065d6c", "position": {"x": 0.5029, "y": 0.9393, "z": -0.7406}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spoon_1d6d1e51", "position": {"x": 1.212944, "y": 0.777128, "z": -0.0499332249}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SprayBottle_4f8d0eee", "position": {"x": 1.32554936, "y": 2.156122, "z": 0.68623805}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c297f602", "position": {"x": -1.43509555, "y": 1.4162575, "z": 1.19737446}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}], "object_toggles": [], "random_seed": 2289073573, "scene_num": 17}, "task_id": "trial_T20190909_130902_238935", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A31681CCEVDIH3_31Z0PCVWUN6C2CBL3E4NJ3TJDWX7T8", "high_descs": ["Make a left to walk to the sink.", "Pick up the white egg from the sink.", "Make a right to walk to the microwave.", "Heat up the egg in the microwave, removing it afterwards.", "Turn right to walk to the fridge.", "Put the egg in the fridge."], "task_desc": "Heat up an egg in the microwave to put in the fridge.", "votes": [1, 1]}, {"assignment_id": "A1ELPYAFO7MANS_34HJIJKLP8NUWHXR6B6VH9XUJFAV4F", "high_descs": ["Turn left and walk to the sink.", "Pick up the egg in the sink.", "Turn right and walk to microwave.", "Heat the egg in the microwave.", "Take a step right to the fridge.", "Place the egg in the fridge to the left of the glass."], "task_desc": "Place a heated egg in the fridge.", "votes": [1, 1]}, {"assignment_id": "A2871R3LEPWMMK_3PDJHANYK873T36LKAI6Z6QZ7RIH69", "high_descs": ["Take a left at the white table and walk to the sink.", "Pick up the egg from inside the sink.", "Turn right and walk to the microwave on the counter.", "Put the egg inside the microwave, heat it, remove it and close the door.", "Turn right and walk to the black fridge on the left.", "Put the heated egg on the top shelf on the left side inside the fridge and close the door."], "task_desc": "Place a heated egg in a fridge.", "votes": [1, 1]}]}}