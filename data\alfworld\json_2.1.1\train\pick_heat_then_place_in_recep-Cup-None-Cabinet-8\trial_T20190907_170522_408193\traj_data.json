{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000091.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000092.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000093.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000094.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000103.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000104.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000105.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000106.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000107.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000108.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000109.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000186.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000187.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000188.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000189.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000190.png", "low_idx": 48}, {"high_idx": 2, "image_name": "000000191.png", "low_idx": 48}, {"high_idx": 2, "image_name": "000000192.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000193.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000194.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000195.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000196.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000197.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000198.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000199.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000200.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000201.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000202.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000245.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000246.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000247.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000248.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000249.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000250.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000251.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000252.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000253.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000254.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000255.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000256.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000257.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000258.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000259.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000260.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000261.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000262.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000263.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000264.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000265.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000266.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000267.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000268.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000269.png", "low_idx": 57}, {"high_idx": 3, "image_name": "000000270.png", "low_idx": 57}, {"high_idx": 3, "image_name": "000000271.png", "low_idx": 57}, {"high_idx": 3, "image_name": "000000272.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000310.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000311.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000312.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000313.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000314.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000315.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000316.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000317.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000318.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000319.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000320.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000321.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000322.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000323.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000324.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000325.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000326.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000327.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000328.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000329.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000330.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000331.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000332.png", "low_idx": 65}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-10|-7|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-7.2365856, -7.2365856, -7.86299896, -7.86299896, 3.643915416, 3.643915416]], "coordinateReceptacleObjectId": ["CounterTop", [-7.896, -7.896, -6.828, -6.828, 3.784, 3.784]], "forceVisible": true, "objectId": "Cup|-01.81|+00.91|-01.97"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|2|0|1|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [5.688, 5.688, 0.092, 0.092, 4.6, 4.6]], "forceVisible": true, "objectId": "Microwave|+01.42|+01.15|+00.02"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|2|-2|2|60"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-7.2365856, -7.2365856, -7.86299896, -7.86299896, 3.643915416, 3.643915416]], "coordinateReceptacleObjectId": ["Cabinet", [-0.8108, -0.8108, -5.5664, -5.5664, 1.6076, 1.6076]], "forceVisible": true, "objectId": "Cup|-01.81|+00.91|-01.97", "receptacleObjectId": "Cabinet|-00.20|+00.40|-01.39"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.81|+00.91|-01.97"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [198, 105, 230, 139], "mask": [[31408, 11], [31705, 17], [32004, 20], [32303, 23], [32602, 25], [32901, 27], [33201, 28], [33500, 30], [33800, 30], [34100, 30], [34399, 31], [34699, 32], [34998, 33], [35298, 33], [35598, 33], [35898, 33], [36198, 33], [36498, 32], [36798, 32], [37098, 32], [37398, 32], [37698, 31], [37998, 31], [38299, 30], [38599, 29], [38899, 28], [39200, 27], [39500, 26], [39801, 24], [40101, 23], [40401, 22], [40701, 21], [41001, 20], [41300, 21], [41600, 20]], "point": [214, 121]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.81|+00.91|-01.97", "placeStationary": true, "receptacleObjectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 24, 252, 209], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9622, 226], [9918, 233], [10214, 239], [10510, 243], [10806, 247], [11102, 251], [11400, 253], [11700, 252], [12000, 252], [12300, 252], [12600, 252], [12900, 252], [13200, 251], [13500, 251], [13800, 251], [14100, 251], [14400, 251], [14700, 251], [15000, 250], [15300, 250], [15600, 250], [15900, 250], [16200, 250], [16500, 249], [16800, 249], [17100, 249], [17400, 249], [17700, 249], [18000, 248], [18300, 248], [18600, 248], [18900, 248], [19200, 248], [19500, 248], [19800, 247], [20100, 247], [20400, 247], [20700, 247], [21000, 247], [21300, 246], [21600, 246], [21900, 126], [22030, 116], [22200, 124], [22331, 115], [22500, 122], [22633, 113], [22800, 121], [22934, 112], [23100, 120], [23235, 110], [23400, 119], [23536, 109], [23700, 119], [23836, 109], [24000, 118], [24137, 108], [24300, 118], [24438, 107], [24600, 117], [24738, 106], [24900, 117], [25039, 105], [25200, 117], [25339, 105], [25500, 117], [25639, 105], [25800, 117], [25939, 105], [26100, 117], [26239, 105], [26400, 117], [26539, 104], [26700, 117], [26839, 104], [27000, 117], [27139, 104], [27300, 118], [27439, 104], [27600, 119], [27739, 104], [27900, 119], [28039, 103], [28200, 119], [28338, 104], [28500, 118], [28638, 104], [28800, 118], [28938, 104], [29100, 118], [29237, 105], [29400, 118], [29536, 106], [29700, 118], [29834, 107], [30000, 118], [30132, 109], [30300, 118], [30432, 109], [30600, 118], [30732, 109], [30900, 119], [31032, 109], [31200, 119], [31331, 109], [31500, 120], [31631, 109], [31800, 121], [31930, 110], [32100, 122], [32229, 111], [32400, 124], [32526, 114], [32700, 240], [33000, 239], [33300, 239], [33600, 239], [33900, 239], [34200, 239], [34500, 238], [34800, 238], [35100, 238], [35400, 238], [35700, 238], [36000, 238], [36300, 237], [36600, 237], [36900, 237], [37200, 237], [37500, 237], [37800, 236], [38100, 236], [38400, 236], [38700, 236], [39000, 236], [39300, 236], [39600, 55], [39677, 6], [39798, 37], [39900, 54], [39977, 4], [40098, 37], [40200, 54], [40399, 36], [40500, 53], [40800, 52], [41100, 52], [41400, 51], [41700, 50], [42000, 49], [42300, 49], [42600, 48], [42900, 47], [43200, 47], [43500, 46], [43800, 45], [44100, 44], [44400, 44], [44700, 43], [45000, 42], [45300, 41], [45600, 41], [45900, 40], [46200, 39], [46500, 39], [46800, 38], [47100, 37], [47400, 36], [47700, 36], [48000, 35], [48300, 34], [48600, 34], [48900, 33], [49200, 32], [49500, 31], [49800, 31], [50100, 30], [50400, 29], [50700, 29], [51000, 28], [51300, 27], [51600, 26], [51900, 26], [52200, 25], [52500, 24], [52800, 24], [53100, 23], [53400, 22], [53700, 21], [54000, 21], [54300, 20], [54600, 19], [54900, 19], [55200, 18], [55500, 17], [55800, 16], [56100, 16], [56400, 15], [56700, 14], [57000, 14], [57300, 13], [57600, 12], [57900, 11], [58200, 11], [58500, 10], [58800, 9], [59100, 9], [59400, 8], [59700, 7], [60000, 6], [60300, 6], [60600, 5], [60900, 4], [61200, 4], [61500, 3], [61800, 2], [62100, 1], [62400, 1]], "point": [126, 115]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 24, 252, 209], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9622, 226], [9918, 233], [10214, 239], [10510, 243], [10806, 247], [11102, 251], [11400, 253], [11700, 252], [12000, 252], [12300, 252], [12600, 252], [12900, 252], [13200, 251], [13500, 251], [13800, 251], [14100, 251], [14400, 251], [14700, 251], [15000, 250], [15300, 250], [15600, 250], [15900, 250], [16200, 250], [16500, 83], [16604, 145], [16800, 78], [16906, 143], [17100, 77], [17207, 142], [17400, 76], [17507, 142], [17700, 76], [17807, 142], [18000, 76], [18107, 141], [18300, 76], [18408, 140], [18600, 76], [18708, 140], [18900, 76], [19008, 140], [19200, 76], [19309, 139], [19500, 76], [19609, 139], [19800, 76], [19909, 138], [20100, 76], [20209, 138], [20400, 76], [20509, 138], [20700, 76], [20809, 138], [21000, 76], [21109, 138], [21300, 77], [21409, 137], [21600, 77], [21709, 137], [21900, 77], [22009, 17], [22030, 116], [22200, 78], [22308, 16], [22331, 115], [22500, 78], [22608, 14], [22633, 113], [22800, 78], [22908, 13], [22934, 112], [23100, 79], [23208, 12], [23235, 110], [23400, 79], [23508, 11], [23536, 109], [23700, 80], [23807, 12], [23836, 109], [24000, 80], [24107, 11], [24137, 108], [24300, 81], [24406, 12], [24438, 107], [24600, 82], [24706, 11], [24738, 106], [24900, 82], [25006, 11], [25039, 105], [25200, 83], [25305, 12], [25339, 105], [25500, 84], [25605, 12], [25639, 105], [25800, 84], [25905, 12], [25939, 105], [26100, 84], [26205, 12], [26239, 105], [26400, 84], [26505, 12], [26539, 104], [26700, 85], [26805, 12], [26839, 104], [27000, 85], [27105, 12], [27139, 104], [27300, 85], [27405, 13], [27439, 104], [27600, 85], [27706, 13], [27739, 104], [27900, 85], [28006, 13], [28039, 103], [28200, 85], [28306, 13], [28338, 104], [28500, 85], [28606, 12], [28638, 104], [28800, 85], [28907, 11], [28938, 104], [29100, 85], [29207, 11], [29237, 105], [29400, 85], [29507, 11], [29536, 106], [29700, 85], [29807, 11], [29834, 107], [30000, 85], [30107, 11], [30132, 109], [30300, 85], [30407, 11], [30432, 109], [30600, 85], [30706, 12], [30732, 109], [30900, 86], [31005, 14], [31032, 109], [31200, 88], [31302, 17], [31331, 109], [31500, 120], [31631, 109], [31800, 121], [31930, 110], [32100, 122], [32229, 111], [32400, 124], [32526, 114], [32700, 240], [33000, 239], [33300, 239], [33600, 239], [33900, 239], [34200, 239], [34500, 238], [34800, 238], [35100, 238], [35400, 238], [35700, 238], [36000, 238], [36300, 237], [36600, 237], [36900, 237], [37200, 237], [37500, 237], [37800, 236], [38100, 236], [38400, 236], [38700, 236], [39000, 236], [39300, 236], [39600, 55], [39677, 6], [39798, 37], [39900, 54], [39977, 4], [40098, 37], [40200, 54], [40399, 36], [40500, 53], [40800, 52], [41100, 52], [41400, 51], [41700, 50], [42000, 49], [42300, 49], [42600, 48], [42900, 47], [43200, 47], [43500, 46], [43800, 45], [44100, 44], [44400, 44], [44700, 43], [45000, 42], [45300, 41], [45600, 41], [45900, 40], [46200, 39], [46500, 39], [46800, 38], [47100, 37], [47400, 36], [47700, 36], [48000, 35], [48300, 34], [48600, 34], [48900, 33], [49200, 32], [49500, 31], [49800, 31], [50100, 30], [50400, 29], [50700, 29], [51000, 28], [51300, 27], [51600, 26], [51900, 26], [52200, 25], [52500, 24], [52800, 24], [53100, 23], [53400, 22], [53700, 21], [54000, 21], [54300, 20], [54600, 19], [54900, 19], [55200, 18], [55500, 17], [55800, 16], [56100, 16], [56400, 15], [56700, 14], [57000, 14], [57300, 13], [57600, 12], [57900, 11], [58200, 11], [58500, 10], [58800, 9], [59100, 9], [59400, 8], [59700, 7], [60000, 6], [60300, 6], [60600, 5], [60900, 4], [61200, 4], [61500, 3], [61800, 2], [62100, 1], [62400, 1]], "point": [126, 115]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [5.688, 5.688, 0.092, 0.092, 4.6, 4.6]], "forceVisible": true, "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [5.688, 5.688, 0.092, 0.092, 4.6, 4.6]], "forceVisible": true, "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.81|+00.91|-01.97"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [76, 56, 108, 105], "mask": [[16583, 21], [16878, 28], [17177, 30], [17476, 31], [17776, 31], [18076, 31], [18376, 32], [18676, 32], [18976, 32], [19276, 33], [19576, 33], [19876, 33], [20176, 33], [20476, 33], [20776, 33], [21076, 33], [21377, 32], [21677, 32], [21977, 32], [22278, 30], [22578, 30], [22878, 30], [23179, 29], [23479, 29], [23780, 27], [24080, 27], [24381, 25], [24682, 24], [24982, 24], [25283, 22], [25584, 21], [25884, 21], [26184, 21], [26484, 21], [26785, 20], [27085, 20], [27385, 20], [27685, 21], [27985, 21], [28285, 21], [28585, 21], [28885, 22], [29185, 22], [29485, 22], [29785, 22], [30085, 22], [30385, 22], [30685, 21], [30986, 19], [31288, 14]], "point": [92, 79]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 24, 252, 209], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9622, 226], [9918, 233], [10214, 239], [10510, 243], [10806, 247], [11102, 251], [11400, 253], [11700, 252], [12000, 252], [12300, 252], [12600, 252], [12900, 252], [13200, 251], [13500, 251], [13800, 251], [14100, 251], [14400, 251], [14700, 251], [15000, 250], [15300, 250], [15600, 250], [15900, 250], [16200, 250], [16500, 249], [16800, 249], [17100, 249], [17400, 249], [17700, 249], [18000, 248], [18300, 248], [18600, 248], [18900, 248], [19200, 248], [19500, 248], [19800, 247], [20100, 247], [20400, 247], [20700, 247], [21000, 247], [21300, 246], [21600, 246], [21900, 126], [22030, 116], [22200, 124], [22331, 115], [22500, 122], [22633, 113], [22800, 121], [22934, 112], [23100, 120], [23235, 110], [23400, 119], [23536, 109], [23700, 119], [23836, 109], [24000, 118], [24137, 108], [24300, 118], [24438, 107], [24600, 117], [24738, 106], [24900, 117], [25039, 105], [25200, 117], [25339, 105], [25500, 117], [25639, 105], [25800, 117], [25939, 105], [26100, 117], [26239, 105], [26400, 117], [26539, 104], [26700, 117], [26839, 104], [27000, 117], [27139, 104], [27300, 118], [27439, 104], [27600, 119], [27739, 104], [27900, 119], [28039, 103], [28200, 119], [28338, 104], [28500, 118], [28638, 104], [28800, 118], [28938, 104], [29100, 118], [29237, 105], [29400, 118], [29536, 106], [29700, 118], [29834, 107], [30000, 118], [30132, 109], [30300, 118], [30432, 109], [30600, 118], [30732, 109], [30900, 119], [31032, 109], [31200, 119], [31331, 109], [31500, 120], [31631, 109], [31800, 121], [31930, 110], [32100, 122], [32229, 111], [32400, 124], [32526, 114], [32700, 240], [33000, 239], [33300, 239], [33600, 239], [33900, 239], [34200, 239], [34500, 238], [34800, 238], [35100, 238], [35400, 238], [35700, 238], [36000, 238], [36300, 237], [36600, 237], [36900, 237], [37200, 237], [37500, 237], [37800, 236], [38100, 236], [38400, 236], [38700, 236], [39000, 236], [39300, 236], [39600, 55], [39677, 6], [39798, 37], [39900, 54], [39977, 4], [40098, 37], [40200, 54], [40399, 36], [40500, 53], [40800, 52], [41100, 52], [41400, 51], [41700, 50], [42000, 49], [42300, 49], [42600, 48], [42900, 47], [43200, 47], [43500, 46], [43800, 45], [44100, 44], [44400, 44], [44700, 43], [45000, 42], [45300, 41], [45600, 41], [45900, 40], [46200, 39], [46500, 39], [46800, 38], [47100, 37], [47400, 36], [47700, 36], [48000, 35], [48300, 34], [48600, 34], [48900, 33], [49200, 32], [49500, 31], [49800, 31], [50100, 30], [50400, 29], [50700, 29], [51000, 28], [51300, 27], [51600, 26], [51900, 26], [52200, 25], [52500, 24], [52800, 24], [53100, 23], [53400, 22], [53700, 21], [54000, 21], [54300, 20], [54600, 19], [54900, 19], [55200, 18], [55500, 17], [55800, 16], [56100, 16], [56400, 15], [56700, 14], [57000, 14], [57300, 13], [57600, 12], [57900, 11], [58200, 11], [58500, 10], [58800, 9], [59100, 9], [59400, 8], [59700, 7], [60000, 6], [60300, 6], [60600, 5], [60900, 4], [61200, 4], [61500, 3], [61800, 2], [62100, 1], [62400, 1]], "point": [126, 115]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-00.20|+00.40|-01.39"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [181, 79, 299, 147], "mask": [[23594, 106], [23894, 106], [24194, 106], [24494, 106], [24793, 107], [25093, 106], [25393, 105], [25693, 105], [25993, 104], [26292, 104], [26592, 104], [26892, 103], [27192, 102], [27492, 102], [27791, 102], [28091, 101], [28391, 101], [28691, 100], [28991, 99], [29290, 100], [29590, 99], [29890, 98], [30190, 97], [30490, 97], [30789, 97], [31089, 96], [31389, 96], [31689, 95], [31989, 94], [32288, 95], [32588, 94], [32888, 93], [33188, 93], [33488, 92], [33787, 92], [34087, 92], [34387, 91], [34687, 90], [34987, 89], [35286, 90], [35586, 89], [35886, 88], [36186, 88], [36486, 87], [36785, 87], [37085, 87], [37385, 86], [37685, 85], [37985, 85], [38284, 85], [38584, 84], [38884, 83], [39184, 83], [39484, 82], [39783, 82], [40083, 82], [40383, 81], [40683, 80], [40983, 80], [41282, 80], [41582, 79], [41882, 79], [42182, 78], [42482, 77], [42781, 78], [43081, 77], [43381, 76], [43681, 75], [43981, 75]], "point": [240, 112]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.81|+00.91|-01.97", "placeStationary": true, "receptacleObjectId": "Cabinet|-00.20|+00.40|-01.39"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [177, 79, 298, 196], "mask": [[23590, 109], [23890, 108], [24189, 109], [24489, 108], [24789, 107], [25089, 107], [25388, 107], [25688, 106], [25988, 106], [26288, 105], [26587, 105], [26887, 105], [27187, 104], [27487, 103], [27787, 103], [28086, 103], [28386, 102], [28686, 102], [28986, 101], [29286, 100], [29586, 100], [29885, 100], [30185, 99], [30485, 99], [30785, 98], [31085, 97], [31385, 97], [31684, 97], [31984, 96], [32284, 96], [32584, 95], [32884, 94], [33183, 95], [33483, 94], [33783, 93], [34083, 93], [34383, 92], [34683, 91], [34982, 92], [35282, 91], [35582, 90], [35882, 89], [36182, 89], [36482, 88], [36781, 88], [37081, 88], [37381, 87], [37681, 86], [37981, 86], [38280, 86], [38580, 85], [38880, 85], [39180, 84], [39480, 83], [39780, 83], [40079, 83], [40379, 82], [40679, 82], [40979, 81], [41279, 80], [41579, 80], [41878, 80], [42178, 79], [42478, 79], [42778, 78], [43078, 77], [43377, 25], [43677, 25], [43977, 25], [44277, 26], [44577, 26], [44877, 26], [45177, 26], [45478, 25], [45778, 25], [46078, 25], [46378, 25], [46678, 25], [46978, 26], [47278, 26], [47578, 26], [47878, 26], [48178, 26], [48478, 26], [48779, 25], [49081, 23], [49382, 23], [49683, 22], [49984, 21], [50285, 20], [50586, 19], [50887, 18], [51188, 17], [51488, 16], [51789, 15], [52089, 14], [52390, 13], [52690, 13], [52991, 11], [53291, 11], [53591, 10], [53891, 10], [54191, 10], [54491, 9], [54792, 8], [55092, 7], [55392, 7], [55692, 6], [55992, 6], [56293, 5], [56593, 4], [56893, 4], [57193, 3], [57493, 3], [57793, 2], [58093, 2], [58393, 2], [58693, 1]], "point": [237, 136]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-00.20|+00.40|-01.39"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [177, 79, 298, 215], "mask": [[23590, 109], [23890, 108], [24189, 109], [24489, 108], [24789, 107], [25089, 107], [25388, 107], [25688, 106], [25988, 106], [26288, 105], [26587, 105], [26887, 105], [27187, 104], [27487, 103], [27787, 103], [28086, 103], [28386, 102], [28686, 102], [28986, 101], [29286, 100], [29586, 100], [29885, 100], [30185, 99], [30485, 99], [30785, 98], [31085, 97], [31385, 97], [31684, 97], [31984, 96], [32284, 96], [32584, 95], [32884, 94], [33183, 95], [33483, 94], [33783, 26], [33814, 62], [34083, 24], [34116, 60], [34383, 23], [34418, 57], [34683, 22], [34718, 56], [34982, 22], [35019, 55], [35282, 21], [35319, 54], [35582, 21], [35620, 52], [35882, 21], [35920, 51], [36182, 21], [36220, 51], [36482, 20], [36520, 50], [36781, 21], [36820, 49], [37081, 22], [37119, 50], [37381, 22], [37419, 49], [37681, 22], [37718, 49], [37981, 23], [38018, 49], [38280, 23], [38317, 49], [38580, 23], [38616, 49], [38880, 22], [38914, 51], [39180, 22], [39214, 50], [39480, 22], [39513, 50], [39780, 22], [39813, 50], [40079, 24], [40113, 49], [40379, 25], [40412, 49], [40679, 26], [40711, 50], [40979, 81], [41279, 80], [41579, 80], [41878, 80], [42178, 79], [42478, 79], [42778, 78], [43078, 77], [43377, 25], [43677, 25], [43977, 25], [44277, 26], [44577, 26], [44877, 26], [45177, 26], [45478, 25], [45778, 25], [46078, 25], [46378, 25], [46678, 25], [46978, 26], [47278, 26], [47578, 26], [47878, 26], [48178, 26], [48478, 26], [48778, 26], [49078, 26], [49378, 27], [49678, 27], [49978, 27], [50278, 27], [50579, 26], [50879, 26], [51179, 26], [51479, 25], [51779, 25], [52079, 24], [52379, 24], [52679, 24], [52979, 23], [53279, 23], [53579, 22], [53879, 22], [54179, 22], [54479, 21], [54779, 21], [55079, 20], [55380, 19], [55680, 18], [55980, 18], [56280, 18], [56580, 17], [56880, 17], [57180, 16], [57480, 16], [57780, 15], [58080, 15], [58380, 15], [58680, 14], [58980, 14], [59280, 13], [59580, 13], [59880, 12], [60181, 11], [60481, 11], [60781, 10], [61081, 10], [61381, 9], [61681, 9], [61981, 9], [62281, 8], [62581, 8], [62882, 6], [63182, 6], [63482, 5], [63782, 5], [64082, 5], [64382, 4]], "point": [237, 143]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan8", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -0.25, "y": 0.9009992, "z": 1.0}, "object_poses": [{"objectName": "Pan_c0048524", "position": {"x": -1.431, "y": 0.949799955, "z": -1.81}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_1cf505ed", "position": {"x": 1.44122839, "y": 1.30101478, "z": 0.08376157}, "rotation": {"x": 0.0, "y": 270.000153, "z": 0.0}}, {"objectName": "Kettle_e75d4607", "position": {"x": -2.20158482, "y": 0.9115413, "z": 0.463499784}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_7cca838f", "position": {"x": -1.88603711, "y": 0.9105421, "z": -1.44825029}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_b308514b", "position": {"x": -2.0419457, "y": 0.112481117, "z": -1.53563261}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_b308514b", "position": {"x": -0.342157066, "y": 0.117470026, "z": -1.48949575}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_1d30af71", "position": {"x": 0.664081335, "y": 0.9262998, "z": -1.61925447}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_1d30af71", "position": {"x": 0.969732344, "y": 0.7884345, "z": 0.323113024}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_e08d0eeb", "position": {"x": 1.381, "y": 0.243868053, "z": 1.16694057}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_9b9c5535", "position": {"x": 1.40199959, "y": 1.656733, "z": -0.5001875}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_c69c5340", "position": {"x": 1.26597214, "y": 0.117764354, "z": 0.37244302}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bowl_a0ece05a", "position": {"x": -1.8091464, "y": 0.911050856, "z": -1.707}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_a0ece05a", "position": {"x": -0.996799052, "y": 1.85109913, "z": -1.83929992}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1de14ecd", "position": {"x": -0.4018854, "y": 0.906299949, "z": -1.53404069}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1de14ecd", "position": {"x": -1.835049, "y": 0.905500054, "z": 0.0322503448}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_a2768593", "position": {"x": -0.751154363, "y": 0.106764436, "z": -1.48706532}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_a2768593", "position": {"x": -0.7033658, "y": 0.906299949, "z": -1.44882691}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_9b638b9f", "position": {"x": 1.37600017, "y": 1.44560862, "z": 2.10100031}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Mug_49b86ad0", "position": {"x": 1.37599945, "y": 1.36246753, "z": 1.851}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Apple_b66a32b5", "position": {"x": 1.494119, "y": 1.196883, "z": -1.00233626}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_b66a32b5", "position": {"x": 0.251858443, "y": 0.9542329, "z": -1.75846994}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_441a8bda", "position": {"x": 1.32553208, "y": 1.407585, "z": 2.35100031}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Glassbottle_b308514b", "position": {"x": -1.9629277, "y": 0.9112167, "z": -1.53450024}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_af290bda", "position": {"x": -1.88603711, "y": 0.940200269, "z": -1.79324985}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_9b9c5535", "position": {"x": -2.32376337, "y": 0.911168754, "z": 0.204750121}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_7aaf3771", "position": {"x": 0.320022345, "y": 0.920098543, "z": -1.70802462}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Ladle_238538e2", "position": {"x": 1.34259534, "y": 1.7004267, "z": -1.50081253}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_1cf505ed", "position": {"x": 0.5698826, "y": 0.9530963, "z": -1.78968167}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_0f08525d", "position": {"x": -0.477255583, "y": 0.9804653, "z": -1.78968191}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_e75d4607", "position": {"x": -1.0811, "y": 0.949799955, "z": -1.81}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_73e168e3", "position": {"x": 1.015, "y": 0.7758153, "z": -1.013817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_a2768593", "position": {"x": 1.09030032, "y": 0.109924793, "z": -0.7362524}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_bfcb0336", "position": {"x": -1.8091464, "y": 0.910978854, "z": -1.96574974}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_e08d0eeb", "position": {"x": 1.34429383, "y": 1.29478192, "z": 0.08376183}, "rotation": {"x": 0.0, "y": 270.000153, "z": 0.0}}, {"objectName": "Spatula_1d30af71", "position": {"x": -1.95722759, "y": 0.925499856, "z": 0.291}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_a0ece05a", "position": {"x": 1.52080822, "y": 1.65661514, "z": -1.26409376}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_abf5c390", "position": {"x": -0.7787359, "y": 0.910704, "z": -1.96010935}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_c0048524", "position": {"x": 1.41487837, "y": 1.16180956, "z": -1.68895817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_c69c5340", "position": {"x": -0.7033658, "y": 0.9112034, "z": -1.70446813}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pot_316d55f0", "position": {"x": 0.9673953, "y": 0.9, "z": 0.249277115}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_7cca838f", "position": {"x": 1.093845, "y": 0.9113421, "z": 0.031709373}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1de14ecd", "position": {"x": 1.33563781, "y": 1.1572001, "z": 0.3709073}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_b7024f32", "position": {"x": 0.710482836, "y": 0.116625071, "z": -1.63311362}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_9b638b9f", "position": {"x": 1.41487837, "y": 1.24034131, "z": -1.34564734}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_b66a32b5", "position": {"x": 0.14961265, "y": 0.9542329, "z": -1.65757918}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_6f7c42a7", "position": {"x": 0.697499931, "y": 0.7745643, "z": -1.56669915}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_49b86ad0", "position": {"x": 1.42647, "y": 1.36246753, "z": 2.476}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}], "object_toggles": [], "random_seed": 3621849465, "scene_num": 8}, "task_id": "trial_T20190907_170522_408193", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A24RGLS3BRZ60J_32SCWG5HIKLNLVIB7F1U7T8OZASP69", "high_descs": ["Turn to the left and go back to the counter. Turn right to go around the far side and face the toaster.", "Pick up the glass from behind the toaster.", "Hold the glass and turn right around the counter and go across the room to find the microwave.", "Put the glass in the microwave and turn in on. Take the glass out of the microwave.", "Carry the glass and turn right, to face the sink.", "Open the bottom cabinet in the corner, and put the glass inside. Shut the door."], "task_desc": "Heat a glass and put it in the cabinet.", "votes": [1, 1]}, {"assignment_id": "AKW57KYG90X61_36TFCYNS47R8RT4JPP2FYXJHAN3HXC", "high_descs": ["move left towards the cupboard", "pickup the glass cup from the top of the cupboard", "turn left and locate the microwave ", "place the glass in the microwave to heat", "move right towards the cupboard", "place the glass cup in the cupboard"], "task_desc": "place a hot glass in the cupboard", "votes": [1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3G0WWMR1UY1GSSPI692099KKZVZQN5", "high_descs": ["<PERSON><PERSON> walking across the room, then hang a left and start walking to the oven, hang a right and walk over to toaster on the counter.", "Pick up the glass that is on the counter next to the toaster.", "Turn left and make your way across the room to the microwave.", "Microwave the glass for a couple second, then remove the glass from the microwave and close the door.", "Turn right and walk over to the counter and look down at the cabinets.", "Open the second cabinet door from the left in front of you and place the heated glass inside."], "task_desc": "Put a heated glass in a kitchen cabinet.", "votes": [1, 1]}]}}