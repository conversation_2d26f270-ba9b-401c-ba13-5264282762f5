{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000173.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000174.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000175.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000176.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000177.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000178.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000179.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000187.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000188.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000189.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000190.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000191.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000192.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000193.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000194.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000195.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000196.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000197.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000198.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000199.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000200.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000201.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000202.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000203.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000204.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000205.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000206.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000208.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000209.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000210.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000211.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000212.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000245.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000246.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000247.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000248.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000249.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000250.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000251.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000252.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000253.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000254.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000255.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000256.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000257.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000258.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000259.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000260.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000261.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000262.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000263.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000264.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000265.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000266.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000267.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000268.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000269.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000270.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000271.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000272.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000273.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000274.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000275.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000277.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000278.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000279.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000280.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000281.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000282.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000284.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000285.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000286.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000287.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000288.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000289.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000290.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000291.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000292.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000293.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000294.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000295.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000296.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000297.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000298.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000299.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000300.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000301.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000302.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000303.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000304.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000305.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000306.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000307.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000308.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000309.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000310.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000311.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000312.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000313.png", "low_idx": 52}, {"high_idx": 7, "image_name": "000000314.png", "low_idx": 52}, {"high_idx": 7, "image_name": "000000315.png", "low_idx": 53}, {"high_idx": 7, "image_name": "000000316.png", "low_idx": 53}, {"high_idx": 7, "image_name": "000000317.png", "low_idx": 54}, {"high_idx": 7, "image_name": "000000318.png", "low_idx": 54}, {"high_idx": 7, "image_name": "000000319.png", "low_idx": 55}, {"high_idx": 7, "image_name": "000000320.png", "low_idx": 55}, {"high_idx": 7, "image_name": "000000321.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000322.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000323.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000324.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000325.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000326.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000327.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000328.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000329.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000330.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000331.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000332.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000333.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000334.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000335.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000336.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000337.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000338.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000339.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000340.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000341.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000342.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000343.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000344.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000345.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000346.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000347.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000348.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000349.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000350.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000351.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000352.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000353.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000354.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000355.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000356.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000357.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000358.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000359.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000360.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000361.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000362.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000363.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000364.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000365.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000366.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000367.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000368.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000369.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000370.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000371.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000372.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000373.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000374.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000375.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000376.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000377.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000378.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000379.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000380.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000381.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000382.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000383.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000384.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000385.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000386.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000387.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000388.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000389.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000390.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000391.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000392.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000393.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000394.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000395.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000396.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000397.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000398.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000399.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000400.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000401.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000402.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000403.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000404.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000405.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000406.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000407.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000408.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000409.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000410.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000411.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000412.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000413.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000414.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000415.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000416.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000417.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000418.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000419.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000420.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000421.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000422.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000423.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000424.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000425.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000426.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000427.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000428.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000429.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000430.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000431.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000432.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000433.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000434.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000435.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000436.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000437.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000438.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000439.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000440.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000441.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000442.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000443.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000444.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000445.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000446.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000447.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000448.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000449.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000450.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000451.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000452.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000453.png", "low_idx": 70}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "SideTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-9|2|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["knife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Knife", [-4.66378164, -4.66378164, -13.25746344, -13.25746344, 5.41080188, 5.41080188]], "coordinateReceptacleObjectId": ["CounterTop", [-7.236, -7.236, 4.728, 4.728, 5.434, 5.434]], "forceVisible": true, "objectId": "Knife|-01.17|+01.35|-03.31"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-5.60423232, -5.60423232, -12.96440316, -12.96440316, 5.589581, 5.589581]], "forceVisible": true, "objectId": "Bread|-01.40|+01.40|-03.24"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 3, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-8|0|15"}}, {"discrete_action": {"action": "PutObject", "args": ["knife", "countertop"]}, "high_idx": 4, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Knife", [-4.66378164, -4.66378164, -13.25746344, -13.25746344, 5.41080188, 5.41080188]], "coordinateReceptacleObjectId": ["CounterTop", [-7.236, -7.236, 4.728, 4.728, 5.434, 5.434]], "forceVisible": true, "objectId": "Knife|-01.17|+01.35|-03.31", "receptacleObjectId": "CounterTop|-01.81|+01.36|+01.18"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 5, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-9|2|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 6, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-5.60423232, -5.60423232, -12.96440316, -12.96440316, 5.589581, 5.589581]], "coordinateReceptacleObjectId": ["CounterTop", [-7.236, -7.236, 4.728, 4.728, 5.434, 5.434]], "forceVisible": true, "objectId": "Bread|-01.40|+01.40|-03.24|BreadSliced_1"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 7, "planner_action": {"action": "GotoLocation", "location": "loc|1|3|1|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 8, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 9, "planner_action": {"action": "GotoLocation", "location": "loc|1|4|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "sidetable"]}, "high_idx": 10, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-5.60423232, -5.60423232, -12.96440316, -12.96440316, 5.589581, 5.589581]], "coordinateReceptacleObjectId": ["SideTable", [4.088, 4.088, 3.4844, 3.4844, 4.9732, 4.9732]], "forceVisible": true, "objectId": "Bread|-01.40|+01.40|-03.24|BreadSliced_1", "receptacleObjectId": "SideTable|+01.02|+01.24|+00.87"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 11, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Knife|-01.17|+01.35|-03.31"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [154, 117, 228, 125], "mask": [[34956, 15], [35254, 16], [35292, 22], [35554, 15], [35592, 32], [35854, 14], [35893, 34], [36155, 13], [36194, 35], [36456, 3], [36494, 32], [36794, 28], [37094, 21], [37394, 12]], "point": [192, 118]}}, "high_idx": 1}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-01.40|+01.40|-03.24"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [228, 97, 275, 148], "mask": [[29041, 8], [29338, 14], [29635, 19], [29933, 23], [30232, 25], [30531, 27], [30830, 30], [31129, 32], [31430, 32], [31730, 33], [32030, 34], [32329, 36], [32629, 37], [32929, 38], [33229, 39], [33529, 39], [33828, 41], [34128, 42], [34428, 43], [34728, 43], [35028, 44], [35328, 45], [35628, 45], [35928, 46], [36228, 46], [36529, 46], [36829, 46], [37130, 46], [37431, 45], [37731, 45], [38032, 44], [38332, 44], [38633, 43], [38933, 42], [39234, 41], [39534, 40], [39835, 39], [40135, 39], [40436, 38], [40737, 36], [41037, 36], [41338, 35], [41638, 35], [41939, 34], [42239, 33], [42540, 32], [42841, 31], [43141, 31], [43442, 29], [43744, 27], [44045, 25], [44350, 16]], "point": [251, 121]}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 3}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Knife|-01.17|+01.35|-03.31", "placeStationary": true, "receptacleObjectId": "CounterTop|-01.81|+01.36|+01.18"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 111, 129, 300], "mask": [[33106, 16], [33127, 3], [33410, 12], [33427, 2], [33715, 14], [34005, 23], [34292, 2], [34305, 22], [34579, 15], [34605, 22], [34866, 29], [34905, 21], [35156, 40], [35204, 21], [35454, 44], [35503, 22], [35751, 74], [36049, 76], [36346, 79], [36644, 81], [36944, 81], [37244, 81], [37536, 1], [37544, 81], [37834, 3], [37843, 82], [38187, 38], [38486, 38], [38785, 39], [39084, 40], [39382, 42], [39681, 42], [39980, 43], [40278, 44], [40577, 44], [40876, 44], [41174, 45], [41473, 45], [41772, 45], [42071, 45], [42369, 45], [42668, 45], [42967, 44], [43265, 45], [43564, 45], [43863, 45], [44161, 45], [44460, 45], [44759, 45], [45069, 35], [45375, 28], [45679, 23], [45981, 21], [46282, 19], [46582, 19], [46882, 18], [47181, 19], [47480, 19], [47779, 20], [48078, 20], [48377, 20], [48677, 20], [48976, 20], [49275, 20], [49574, 20], [49873, 20], [50172, 20], [50471, 20], [50770, 20], [51069, 20], [51369, 19], [51668, 19], [51967, 19], [52266, 19], [52565, 19], [52864, 19], [53163, 19], [53462, 20], [53761, 20], [54060, 20], [54359, 21], [54659, 20], [54958, 21], [55257, 21], [55556, 22], [55855, 23], [56154, 23], [56453, 24], [56752, 25], [57051, 25], [57351, 25], [57650, 25], [57949, 26], [58248, 27], [58547, 28], [58846, 28], [59145, 29], [59444, 30], [59743, 31], [60043, 30], [60342, 31], [60641, 32], [60940, 33], [61239, 34], [61538, 34], [61837, 35], [62136, 36], [62435, 37], [62734, 37], [63034, 37], [63333, 38], [63632, 38], [63931, 39], [64230, 40], [64529, 40], [64828, 41], [65127, 42], [65426, 42], [65726, 42], [66025, 43], [66324, 43], [66623, 44], [66922, 44], [67221, 45], [67520, 46], [67819, 46], [68118, 47], [68417, 48], [68717, 47], [69016, 48], [69315, 9], [69342, 22], [69614, 8], [69642, 21], [69913, 9], [69942, 21], [70212, 9], [70241, 21], [70511, 9], [70541, 21], [70810, 10], [70840, 22], [71109, 10], [71140, 21], [71408, 10], [71439, 22], [71708, 10], [71739, 21], [72007, 10], [72038, 22], [72305, 12], [72337, 22], [72604, 12], [72637, 22], [72903, 12], [72936, 23], [73202, 13], [73236, 22], [73501, 13], [73535, 23], [73800, 13], [73835, 22], [74100, 13], [74134, 23], [74400, 13], [74433, 23], [74700, 13], [74732, 24], [75000, 16], [75028, 28], [75300, 19], [75323, 32], [75600, 18], [75622, 33], [75900, 17], [75921, 33], [76200, 16], [76220, 34], [76500, 15], [76519, 24], [76800, 15], [76818, 14], [77100, 14], [77118, 8], [77400, 13], [77417, 4], [77700, 12], [77716, 2], [78000, 11], [78300, 10], [78314, 2], [78600, 10], [78613, 7], [78900, 9], [78912, 13], [79200, 8], [79212, 18], [79500, 7], [79511, 27], [79800, 6], [79810, 37], [80100, 5], [80109, 39], [80400, 5], [80408, 40], [80700, 4], [80707, 41], [81000, 3], [81006, 41], [81300, 2], [81306, 41], [81600, 1], [81605, 41], [81900, 1], [81904, 42], [82203, 42], [82502, 43], [82803, 42], [83102, 42], [83401, 43], [83701, 42], [84000, 43], [84300, 42], [84600, 42], [84900, 42], [85200, 41], [85500, 41], [85800, 40], [86100, 40], [86400, 39], [86700, 39], [87000, 38], [87300, 38], [87600, 38], [87900, 37], [88200, 37], [88500, 36], [88800, 36], [89100, 35], [89400, 35], [89700, 35]], "point": [64, 204]}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 5}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 5}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 5}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-01.40|+01.40|-03.24|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [232, 103, 258, 132], "mask": [[30842, 17], [31140, 4], [31151, 3], [31439, 4], [31738, 3], [32037, 3], [32336, 3], [32635, 3], [32935, 2], [33235, 1], [33534, 2], [33834, 2], [34135, 1], [34435, 1], [34735, 1], [35035, 1], [35334, 2], [35634, 2], [35934, 2], [36234, 2], [36534, 1], [36833, 2], [37133, 2], [37432, 2], [37732, 2], [38032, 2], [38332, 1], [38632, 1], [39533, 1]], "point": [235, 116]}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 7}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 7}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 7}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 168]], "point": [115, 125]}}, "high_idx": 8}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-01.40|+01.40|-03.24|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 72, 216, 245], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 214], [35100, 214], [35400, 214], [35700, 214], [36000, 214], [36300, 213], [36600, 213], [36900, 213], [37200, 213], [37500, 213], [37800, 213], [38100, 213], [38400, 213], [38700, 212], [39000, 212], [39300, 212], [39600, 212], [39900, 212], [40200, 212], [40500, 212], [40800, 212], [41100, 211], [41400, 211], [41700, 211], [42000, 211], [42300, 211], [42600, 211], [42900, 211], [43200, 210], [43500, 210], [43800, 210], [44100, 210], [44400, 210], [44700, 210], [45000, 210], [45300, 210], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54174, 31], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 34], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 23], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 12], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 4], [72000, 3], [72300, 2], [72600, 2], [72900, 1], [73200, 1]], "point": [108, 157]}}, "high_idx": 8}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 216, 245], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 214], [35100, 214], [35400, 214], [35700, 109], [35827, 87], [36000, 107], [36129, 85], [36300, 106], [36430, 83], [36600, 105], [36732, 81], [36900, 104], [37033, 80], [37200, 103], [37333, 80], [37500, 103], [37634, 79], [37800, 102], [37935, 78], [38100, 102], [38235, 78], [38400, 101], [38535, 78], [38700, 101], [38835, 77], [39000, 101], [39135, 77], [39300, 101], [39435, 77], [39600, 101], [39735, 77], [39900, 102], [40035, 77], [40200, 102], [40335, 77], [40500, 103], [40634, 78], [40800, 103], [40934, 78], [41100, 103], [41234, 77], [41400, 103], [41534, 77], [41700, 104], [41835, 76], [42000, 104], [42135, 76], [42300, 104], [42435, 76], [42600, 104], [42735, 76], [42900, 104], [43035, 76], [43200, 104], [43335, 75], [43500, 103], [43635, 75], [43800, 103], [43935, 75], [44100, 103], [44236, 74], [44400, 103], [44536, 74], [44700, 103], [44836, 74], [45000, 104], [45135, 75], [45300, 104], [45435, 75], [45600, 105], [45734, 75], [45900, 115], [46023, 86], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54174, 31], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 34], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 23], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 12], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 4], [72000, 3], [72300, 2], [72600, 2], [72900, 1], [73200, 1]], "point": [108, 157]}}, "high_idx": 8}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 168]], "point": [115, 125]}}, "high_idx": 8}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 168]], "point": [115, 125]}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 168]], "point": [115, 125]}}, "high_idx": 8}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-01.40|+01.40|-03.24|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [101, 120, 135, 154], "mask": [[35809, 18], [36107, 22], [36406, 24], [36705, 27], [37004, 29], [37303, 30], [37603, 31], [37902, 33], [38202, 33], [38501, 34], [38801, 34], [39101, 34], [39401, 34], [39701, 34], [40002, 33], [40302, 33], [40603, 31], [40903, 31], [41203, 31], [41503, 31], [41804, 31], [42104, 31], [42404, 31], [42704, 31], [43004, 31], [43304, 31], [43603, 32], [43903, 32], [44203, 33], [44503, 33], [44803, 33], [45104, 31], [45404, 31], [45705, 29], [46015, 8]], "point": [118, 136]}}, "high_idx": 8}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 216, 245], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 214], [35100, 214], [35400, 214], [35700, 214], [36000, 214], [36300, 213], [36600, 213], [36900, 213], [37200, 213], [37500, 213], [37800, 213], [38100, 213], [38400, 213], [38700, 212], [39000, 212], [39300, 212], [39600, 212], [39900, 212], [40200, 212], [40500, 212], [40800, 212], [41100, 211], [41400, 211], [41700, 211], [42000, 211], [42300, 211], [42600, 211], [42900, 211], [43200, 210], [43500, 210], [43800, 210], [44100, 210], [44400, 210], [44700, 210], [45000, 210], [45300, 210], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54174, 31], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 34], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 23], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 12], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 4], [72000, 3], [72300, 2], [72600, 2], [72900, 1], [73200, 1]], "point": [108, 157]}}, "high_idx": 8}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 9}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 9}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 9}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-01.40|+01.40|-03.24|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "SideTable|+01.02|+01.24|+00.87"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [40, 72, 299, 188], "mask": [[21383, 18], [21683, 18], [21982, 19], [22282, 5], [22582, 5], [22881, 6], [23181, 5], [23481, 5], [23780, 6], [24080, 5], [24380, 5], [24679, 6], [24979, 5], [25278, 6], [25578, 6], [25878, 5], [26177, 6], [26477, 6], [26777, 5], [26802, 1], [27076, 6], [27102, 1], [27376, 6], [27401, 2], [27675, 6], [27701, 3], [27975, 6], [28001, 3], [28183, 2], [28275, 6], [28301, 3], [28483, 2], [28574, 6], [28601, 3], [28783, 3], [28874, 6], [28901, 3], [29084, 3], [29174, 6], [29202, 2], [29385, 2], [29473, 6], [29503, 2], [29685, 3], [29773, 6], [29804, 1], [29986, 3], [30073, 6], [30104, 1], [30287, 2], [30372, 6], [30587, 3], [30672, 6], [30888, 3], [30971, 7], [31189, 2], [31271, 6], [31489, 3], [31571, 6], [31790, 3], [31870, 7], [32091, 2], [32170, 6], [32391, 3], [32470, 6], [32692, 3], [32769, 7], [32993, 2], [33069, 6], [33293, 3], [33368, 7], [33594, 3], [33668, 7], [33895, 2], [33968, 6], [34195, 3], [34267, 7], [34317, 20], [34431, 20], [34496, 3], [34567, 7], [34613, 29], [34727, 29], [34796, 3], [34867, 6], [34909, 36], [35024, 36], [35097, 3], [35166, 7], [35206, 42], [35321, 42], [35398, 2], [35466, 7], [35503, 47], [35619, 47], [35698, 2], [35766, 6], [35801, 51], [35918, 50], [35999, 1], [36065, 7], [36100, 54], [36216, 54], [36365, 7], [36398, 57], [36515, 57], [36664, 7], [36696, 61], [36814, 61], [36964, 7], [36994, 64], [37112, 65], [37264, 7], [37293, 66], [37412, 66], [37563, 7], [37592, 68], [37711, 68], [37863, 7], [37891, 70], [38011, 70], [38163, 7], [38190, 72], [38310, 72], [38462, 7], [38489, 74], [38610, 73], [38762, 7], [38788, 76], [38909, 76], [39061, 8], [39087, 77], [39209, 77], [39361, 7], [39386, 79], [39508, 79], [39661, 7], [39686, 79], [39808, 80], [39960, 8], [39985, 80], [40108, 81], [40260, 7], [40285, 81], [40408, 81], [40560, 7], [40584, 38], [40628, 38], [40708, 38], [40752, 38], [40859, 8], [40884, 36], [40930, 36], [41008, 36], [41054, 37], [41159, 7], [41183, 36], [41231, 36], [41308, 36], [41356, 36], [41459, 7], [41483, 35], [41532, 35], [41608, 36], [41657, 36], [41758, 8], [41782, 36], [41832, 35], [41908, 36], [41958, 35], [42058, 7], [42082, 36], [42132, 35], [42209, 35], [42258, 36], [42357, 8], [42382, 36], [42431, 36], [42509, 36], [42558, 36], [42657, 8], [42682, 37], [42730, 37], [42810, 37], [42858, 36], [42957, 7], [42982, 39], [43028, 39], [43110, 39], [43156, 39], [43256, 8], [43282, 84], [43411, 84], [43556, 8], [43582, 84], [43711, 84], [43856, 7], [43882, 84], [44011, 85], [44155, 8], [44182, 84], [44312, 84], [44455, 8], [44482, 84], [44612, 84], [44754, 8], [44782, 83], [44913, 83], [45054, 8], [45083, 82], [45214, 82], [45354, 8], [45383, 81], [45515, 81], [45653, 8], [45684, 79], [45816, 80], [45953, 8], [45984, 79], [46117, 79], [46253, 8], [46285, 77], [46418, 77], [46552, 8], [46585, 76], [46719, 76], [46852, 8], [46886, 75], [47020, 75], [47152, 8], [47186, 74], [47321, 74], [47451, 8], [47487, 72], [47622, 72], [47751, 8], [47788, 70], [47924, 69], [48050, 9], [48089, 67], [48225, 68], [48350, 8], [48390, 65], [48527, 65], [48650, 8], [48691, 63], [48829, 62], [48949, 9], [48993, 59], [49130, 60], [49249, 8], [49294, 57], [49432, 57], [49549, 8], [49595, 54], [49734, 54], [49848, 9], [49897, 50], [50037, 49], [50148, 8], [50200, 44], [50340, 44], [50447, 9], [50502, 40], [50642, 40], [50747, 9], [50805, 34], [50945, 35], [51047, 9], [51107, 29], [51248, 30], [51346, 9], [51415, 13], [51557, 13], [51646, 53], [51946, 103], [52245, 155], [52545, 206], [52844, 256], [53144, 256], [53444, 256], [53743, 257], [54043, 257], [54343, 257], [54642, 258], [54942, 258], [55242, 258], [55541, 259], [55841, 259], [56140, 260]], "point": [164, 131]}}, "high_idx": 10}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan3", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": 0.75, "y": 1.12401652, "z": 0.0}, "object_poses": [{"objectName": "Pan_9d168802", "position": {"x": -0.2394, "y": 1.3317, "z": -3.3019}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Potato_e8912d85", "position": {"x": -1.66056311, "y": 1.36275363, "z": -2.20134926}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_e8912d85", "position": {"x": -1.82652426, "y": 1.37289536, "z": -0.9118071}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a15664d1", "position": {"x": -1.68797278, "y": 0.4221356, "z": -1.2177}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": 0.824245334, "y": 0.3459003, "z": -2.26769447}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": -1.6457262, "y": 0.423119783, "z": -0.2988006}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.363688, "y": 1.33799994, "z": -1.50393641}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -2.00441241, "y": 1.33681107, "z": -1.25536227}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -1.0945816, "y": 1.32319188, "z": -2.9817524}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": -1.52276421, "y": 1.35692108, "z": -3.203495}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": -1.79031563, "y": 1.32130814, "z": 2.069365}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": 0.896088362, "y": 1.32130814, "z": -2.794527}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b13b6e3e", "position": {"x": -1.85165894, "y": 0.338814139, "z": -2.06300569}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b13b6e3e", "position": {"x": -1.88644969, "y": 0.338814139, "z": 1.00736094}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_c15f34b3", "position": {"x": -1.40105689, "y": 1.31800008, "z": 2.069365}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": -1.40105808, "y": 1.39739525, "z": -3.24110079}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": 0.349309921, "y": 1.39739525, "z": -3.12197924}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": -1.66056263, "y": 1.39912164, "z": 1.4197371}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": 0.5068307, "y": 1.39912164, "z": -2.794527}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": 0.965999663, "y": 1.3713994, "z": -1.53499949}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": 0.896088362, "y": 1.39739525, "z": -2.05370474}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_930b560d", "position": {"x": -2.04982066, "y": 1.3294332, "z": -3.24110079}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -1.16594541, "y": 1.35270047, "z": -3.31436586}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": -1.53081059, "y": 1.32130814, "z": -1.94141138}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_e8912d85", "position": {"x": 0.957809448, "y": 1.9858737, "z": 1.55330944}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -1.6575036, "y": 0.280376852, "z": 2.151774}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": -1.95454407, "y": 1.05159819, "z": -0.587838531}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Cup_f306e730", "position": {"x": 1.056315, "y": 1.75461769, "z": 2.23050737}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": -0.125, "y": 1.3324, "z": -2.973}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": -1.6575036, "y": 0.321009457, "z": 2.316678}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pan_9d168802", "position": {"x": -0.6309, "y": 1.3317, "z": -3.3019}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "SaltShaker_b13b6e3e", "position": {"x": -1.6140399, "y": 0.338814139, "z": 0.412196}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": -1.602987, "y": 0.3447429, "z": -1.9633193}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": -1.891887, "y": 1.40025389, "z": -1.07232559}, "rotation": {"x": 1.91888058, "y": 359.583954, "z": 355.7498}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": 0.6365833, "y": 1.33799994, "z": -3.164938}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_ddd73f57", "position": {"x": -1.634, "y": 0.3844704, "z": 2.23422623}, "rotation": {"x": 0.0, "y": 6.83018925e-06, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": 0.349309921, "y": 1.35692108, "z": -2.67849255}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": -1.68255043, "y": 0.344428062, "z": 1.11066759}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_c15f34b3", "position": {"x": -2.17957425, "y": 1.31800008, "z": 0.770108938}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a15664d1", "position": {"x": 1.05303526, "y": 0.344535828, "z": -1.75116086}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": 0.277946, "y": 1.32412946, "z": -3.12197924}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": 1.01704478, "y": 1.72400379, "z": 1.78130639}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": 0.900508642, "y": 0.339971423, "z": -2.06108117}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3424975414, "scene_num": 3}, "task_id": "trial_T20190907_050518_472219", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1DMXEJGJY02E1_3GS6S824STELCPL4II161Y26NV3NWR", "high_descs": ["Turn around and approach the stove.", "Pick up the knife to the right of the stove.", "Slice the loaf of bread on the counter.", "Turn around so your back is to the stove.", "Put the knife on the counter by the sink.", "Turn around to face the stove.", "Pick up a slice of bread.", "Turn around and go to the microwave.", "Heat the bread in the microwave and take it out", "Move slightly to the left.", "Put the slice of bread on the counter to the left of the microwave."], "task_desc": "Heat a slice of bread.", "votes": [1, 1]}, {"assignment_id": "A1RLO9LNUJIW5S_3RGU30DZTDPTON05ZI3RZTJYG7QMJX", "high_descs": ["Turn left and walk to the counter then turn left and walk to the stove.", "Pick up the yellow handled knife to the right of the stove.", "Cut the loaf of bread to the right of the sink into slices.", "Turn around.", "Put the knife on the counter to your left point first into the wood.", "Turn back around.", "Pick up a slice of bread.", "Turn around and walk until you're even with the microwave on your right then turn right and walk to it.", "Warm the bread in the microwave then take it back out and close the door.", "Move slightly to your left. ", "Put the bread on the counter in front and to the left of the microwave."], "task_desc": "Put a warm slice of bread on the counter.", "votes": [1, 1]}, {"assignment_id": "A2BLQ1GVEHJR8T_3VA45EW49Q4AT95DM9CYNXCHHR71OR", "high_descs": ["Go left to the counter, then left again to the stove.", "Pick up the knife that's pointed toward the loaf of bread to the right of the stove.", "Slice the bread with the knife.", "Face the opposite direction and go to the sink.", "Put the knife on the counter in front of the sink.", "Go back to the counter on the right of the stove.", "Get a slice of bread from the counter.", "Turn around and go to the microwave cart, ahead and to your right.", "Heat the slice of bread in the microwave.", "Go to your left, still facing the microwave cart.", "Put the slice of bread on the left front corner of the microwave cart."], "task_desc": "Put a heated slice of bread on the microwave cart.", "votes": [1, 1]}, {"assignment_id": "A17TKHT8FEVH0R_338JKRMM29GWVGWQEFA1EP8J98MAH5", "high_descs": ["Turn around and head to the counter right of the oven", "Grab the knife on the counter", "Cut the loaf of bread on the counter into slices", "Turn around and stop at the sink", "Put the knife in front of the sink", "Turn around and go to the counter", "Grab a slice of bread off the counter", "Turn around and go to the microwave", "Heat the bread in the microwave and then take it out", "Move slightly to the left", "Put the bread on the black table"], "task_desc": "Putting a slice of bread on the black table", "votes": [1, 1]}, {"assignment_id": "AFU00NU09CFXE_37TD41K0AKQ9E4749NIROY59IJHSCX", "high_descs": ["Turn and move to stand in front of the stove. ", "Pick up the sharp knife from the counter to the right of the stove. ", "Slice the loaf of bread on the counter to the right of the stove.", "Turn around so the counter is on your left side, while still carrying the knife.", "Place the knife on the counter in front of the spatula.", "Turn back around the move closer to the stove.", "Pick up a slice of bread from the counter.", "Carry the slice of bread to the microwave cart by the green door. ", "Place the bread inside the microwave, turn it on, and then remove once heated. ", "Move a little to the left so you're standing in front of the side of the microwave cart closest to the fridge while holding the bread.", "Place the slice of bread on the front left corner of the cart."], "task_desc": "Heat a piece of sliced bread to place on the microwave cart.", "votes": [1, 1]}, {"assignment_id": "A2RQMEPIHW5BOS_39ZSFO5CABDC1V5E8KUDQ30ST1DUJX", "high_descs": ["Turn left, walk to the counter, turn left, and walk to the stove.", "Pick up the knife.", "Slice the bread on the counter.", "Turn around and face the far wall.", "Put the knife on the counter.", "Turn around and face the stove.", "Pick up a slice of bread.", "Turn around, walk to the far wall, turn right, and walk to the microwave.", "Put the slice of bread in the microwave, heat it up, and take the slice of bread out of the microwave.", "Turn left and turn right.", "Put the bread in front of the microwave."], "task_desc": "Put hot bread in front of the microwave.", "votes": [1, 1]}]}}