{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000208.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000209.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000210.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000211.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000212.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000284.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000285.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000286.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000287.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000288.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000289.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000290.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000291.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000292.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000293.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000294.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000295.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000296.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000297.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000298.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000299.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000300.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000301.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000302.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000303.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000304.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000305.png", "low_idx": 50}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Potato", "parent_target": "Microwave", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|0|-4|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["potato"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Potato", [-0.25512892, -0.25512892, -6.8313546, -6.8313546, 3.298731328, 3.298731328]], "coordinateReceptacleObjectId": ["SinkBasin", [-0.25512844, -0.25512844, -6.85248328, -6.85248328, 3.0634928, 3.0634928]], "forceVisible": true, "objectId": "Potato|-00.06|+00.82|-01.71"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|2|0|1|30"}}, {"discrete_action": {"action": "PutObject", "args": ["potato", "microwave"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Potato", [-0.25512892, -0.25512892, -6.8313546, -6.8313546, 3.298731328, 3.298731328]], "coordinateReceptacleObjectId": ["Microwave", [5.688, 5.688, 0.092, 0.092, 4.6, 4.6]], "forceVisible": true, "objectId": "Potato|-00.06|+00.82|-01.71", "receptacleObjectId": "Microwave|+01.42|+01.15|+00.02"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-2|-4|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["potato"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Potato", [-2.210502624, -2.210502624, -7.15872764, -7.15872764, 3.8123852, 3.8123852]], "coordinateReceptacleObjectId": ["CounterTop", [4.66, 4.66, -2.6, -2.6, 3.7872, 3.7872]], "forceVisible": true, "objectId": "Potato|-00.55|+00.95|-01.79"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|2|0|1|30"}}, {"discrete_action": {"action": "PutObject", "args": ["potato", "microwave"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Potato", [-2.210502624, -2.210502624, -7.15872764, -7.15872764, 3.8123852, 3.8123852]], "coordinateReceptacleObjectId": ["Microwave", [5.688, 5.688, 0.092, 0.092, 4.6, 4.6]], "forceVisible": true, "objectId": "Potato|-00.55|+00.95|-01.79", "receptacleObjectId": "Microwave|+01.42|+01.15|+00.02"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Potato|-00.06|+00.82|-01.71"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [151, 147, 180, 168], "mask": [[43966, 1], [44261, 10], [44557, 21], [44856, 23], [45155, 25], [45454, 26], [45753, 27], [46052, 29], [46352, 29], [46651, 30], [46951, 29], [47251, 29], [47551, 29], [47852, 27], [48152, 27], [48452, 26], [48753, 24], [49053, 23], [49354, 21], [49655, 18], [49957, 14], [50260, 8]], "point": [165, 156]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Potato|-00.06|+00.82|-01.71", "placeStationary": true, "receptacleObjectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 24, 252, 209], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9622, 226], [9918, 233], [10214, 239], [10510, 243], [10806, 247], [11102, 251], [11400, 253], [11700, 252], [12000, 252], [12300, 252], [12600, 252], [12900, 252], [13200, 251], [13500, 251], [13800, 251], [14100, 251], [14400, 251], [14700, 251], [15000, 250], [15300, 250], [15600, 250], [15900, 250], [16200, 250], [16500, 249], [16800, 249], [17100, 249], [17400, 249], [17700, 249], [18000, 248], [18300, 248], [18600, 248], [18900, 248], [19200, 248], [19500, 248], [19800, 247], [20100, 247], [20400, 247], [20700, 247], [21000, 247], [21300, 246], [21600, 246], [21900, 246], [22200, 246], [22500, 246], [22800, 246], [23100, 245], [23400, 245], [23700, 245], [24000, 245], [24300, 245], [24600, 244], [24900, 244], [25200, 244], [25500, 244], [25800, 244], [26100, 244], [26400, 243], [26700, 154], [26859, 84], [27000, 153], [27160, 83], [27300, 152], [27461, 82], [27600, 151], [27762, 81], [27900, 151], [28062, 80], [28200, 150], [28362, 80], [28500, 150], [28663, 79], [28800, 150], [28963, 79], [29100, 149], [29263, 79], [29400, 149], [29563, 79], [29700, 149], [29863, 78], [30000, 149], [30163, 78], [30300, 149], [30463, 78], [30600, 150], [30763, 78], [30900, 150], [31063, 78], [31200, 150], [31362, 78], [31500, 151], [31662, 78], [31800, 152], [31961, 79], [32100, 153], [32260, 80], [32400, 155], [32557, 83], [32700, 240], [33000, 239], [33300, 239], [33600, 239], [33900, 239], [34200, 239], [34500, 238], [34800, 238], [35100, 238], [35400, 238], [35700, 238], [36000, 238], [36300, 237], [36600, 237], [36900, 237], [37200, 237], [37500, 237], [37800, 236], [38100, 236], [38400, 236], [38700, 236], [39000, 236], [39300, 236], [39600, 55], [39677, 6], [39798, 37], [39900, 54], [39977, 4], [40098, 37], [40200, 54], [40399, 36], [40500, 53], [40800, 52], [41100, 52], [41400, 51], [41700, 50], [42000, 49], [42300, 49], [42600, 48], [42900, 47], [43200, 47], [43500, 46], [43800, 45], [44100, 44], [44400, 44], [44700, 43], [45000, 42], [45300, 41], [45600, 41], [45900, 40], [46200, 39], [46500, 39], [46800, 38], [47100, 37], [47400, 36], [47700, 36], [48000, 35], [48300, 34], [48600, 34], [48900, 33], [49200, 32], [49500, 31], [49800, 31], [50100, 30], [50400, 29], [50700, 29], [51000, 28], [51300, 27], [51600, 26], [51900, 26], [52200, 25], [52500, 24], [52800, 24], [53100, 23], [53400, 22], [53700, 21], [54000, 21], [54300, 20], [54600, 19], [54900, 19], [55200, 18], [55500, 17], [55800, 16], [56100, 16], [56400, 15], [56700, 14], [57000, 14], [57300, 13], [57600, 12], [57900, 11], [58200, 11], [58500, 10], [58800, 9], [59100, 9], [59400, 8], [59700, 7], [60000, 6], [60300, 6], [60600, 5], [60900, 4], [61200, 4], [61500, 3], [61800, 2], [62100, 1], [62400, 1]], "point": [126, 115]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 24, 252, 209], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9622, 226], [9918, 233], [10214, 239], [10510, 243], [10806, 247], [11102, 251], [11400, 253], [11700, 252], [12000, 252], [12300, 252], [12600, 252], [12900, 252], [13200, 251], [13500, 251], [13800, 251], [14100, 251], [14400, 251], [14700, 251], [15000, 250], [15300, 250], [15600, 250], [15900, 250], [16200, 250], [16500, 249], [16800, 249], [17100, 249], [17400, 249], [17700, 249], [18000, 248], [18300, 248], [18600, 248], [18900, 248], [19200, 248], [19500, 248], [19800, 247], [20100, 247], [20400, 126], [20530, 117], [20700, 123], [20831, 116], [21000, 121], [21133, 114], [21300, 119], [21434, 112], [21600, 118], [21734, 112], [21900, 117], [22035, 111], [22200, 117], [22336, 110], [22500, 116], [22636, 110], [22800, 116], [22937, 109], [23100, 115], [23237, 108], [23400, 115], [23537, 108], [23700, 114], [23838, 107], [24000, 114], [24138, 107], [24300, 114], [24438, 107], [24600, 114], [24738, 106], [24900, 114], [25038, 106], [25200, 114], [25339, 105], [25500, 114], [25639, 105], [25800, 114], [25939, 105], [26100, 115], [26239, 105], [26400, 115], [26539, 104], [26700, 116], [26839, 15], [26859, 84], [27000, 116], [27139, 14], [27160, 83], [27300, 117], [27438, 14], [27461, 82], [27600, 117], [27738, 13], [27762, 81], [27900, 118], [28038, 13], [28062, 80], [28200, 119], [28338, 12], [28362, 80], [28500, 120], [28638, 12], [28663, 79], [28800, 120], [28938, 12], [28963, 79], [29100, 121], [29238, 11], [29263, 79], [29400, 122], [29538, 11], [29563, 79], [29700, 123], [29838, 11], [29863, 78], [30000, 124], [30138, 11], [30163, 78], [30300, 125], [30438, 11], [30463, 78], [30600, 127], [30737, 13], [30763, 78], [30900, 150], [31063, 78], [31200, 150], [31362, 78], [31500, 151], [31662, 78], [31800, 152], [31961, 79], [32100, 153], [32260, 80], [32400, 155], [32557, 83], [32700, 240], [33000, 239], [33300, 239], [33600, 239], [33900, 239], [34200, 239], [34500, 238], [34800, 238], [35100, 238], [35400, 238], [35700, 238], [36000, 238], [36300, 237], [36600, 237], [36900, 237], [37200, 237], [37500, 237], [37800, 236], [38100, 236], [38400, 236], [38700, 236], [39000, 236], [39300, 236], [39600, 55], [39677, 6], [39798, 37], [39900, 54], [39977, 4], [40098, 37], [40200, 54], [40399, 36], [40500, 53], [40800, 52], [41100, 52], [41400, 51], [41700, 50], [42000, 49], [42300, 49], [42600, 48], [42900, 47], [43200, 47], [43500, 46], [43800, 45], [44100, 44], [44400, 44], [44700, 43], [45000, 42], [45300, 41], [45600, 41], [45900, 40], [46200, 39], [46500, 39], [46800, 38], [47100, 37], [47400, 36], [47700, 36], [48000, 35], [48300, 34], [48600, 34], [48900, 33], [49200, 32], [49500, 31], [49800, 31], [50100, 30], [50400, 29], [50700, 29], [51000, 28], [51300, 27], [51600, 26], [51900, 26], [52200, 25], [52500, 24], [52800, 24], [53100, 23], [53400, 22], [53700, 21], [54000, 21], [54300, 20], [54600, 19], [54900, 19], [55200, 18], [55500, 17], [55800, 16], [56100, 16], [56400, 15], [56700, 14], [57000, 14], [57300, 13], [57600, 12], [57900, 11], [58200, 11], [58500, 10], [58800, 9], [59100, 9], [59400, 8], [59700, 7], [60000, 6], [60300, 6], [60600, 5], [60900, 4], [61200, 4], [61500, 3], [61800, 2], [62100, 1], [62400, 1]], "point": [126, 115]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Potato|-00.55|+00.95|-01.79"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [153, 107, 175, 134], "mask": [[31961, 6], [32259, 10], [32558, 12], [32857, 14], [33156, 16], [33456, 16], [33755, 18], [34055, 18], [34354, 20], [34654, 20], [34954, 21], [35253, 22], [35553, 22], [35853, 22], [36154, 21], [36454, 21], [36754, 21], [37055, 20], [37355, 20], [37656, 19], [37957, 18], [38257, 19], [38558, 18], [38859, 17], [39160, 15], [39461, 14], [39763, 11], [40065, 7]], "point": [164, 119]}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Potato|-00.55|+00.95|-01.79", "placeStationary": true, "receptacleObjectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 24, 252, 209], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9622, 226], [9918, 233], [10214, 239], [10510, 243], [10806, 247], [11102, 251], [11400, 253], [11700, 252], [12000, 252], [12300, 252], [12600, 252], [12900, 252], [13200, 251], [13500, 251], [13800, 251], [14100, 251], [14400, 251], [14700, 251], [15000, 250], [15300, 250], [15600, 250], [15900, 250], [16200, 250], [16500, 249], [16800, 249], [17100, 249], [17400, 249], [17700, 249], [18000, 248], [18300, 248], [18600, 248], [18900, 248], [19200, 248], [19500, 248], [19800, 247], [20100, 247], [20400, 126], [20530, 117], [20700, 123], [20831, 116], [21000, 121], [21133, 114], [21300, 119], [21434, 112], [21600, 118], [21734, 112], [21900, 117], [22035, 111], [22200, 117], [22336, 110], [22500, 116], [22636, 110], [22800, 116], [22937, 109], [23100, 115], [23237, 108], [23400, 115], [23537, 108], [23700, 114], [23838, 107], [24000, 114], [24138, 107], [24300, 114], [24438, 107], [24600, 114], [24738, 106], [24900, 114], [25038, 106], [25200, 114], [25339, 105], [25500, 114], [25639, 105], [25800, 114], [25939, 105], [26100, 115], [26239, 105], [26400, 115], [26539, 104], [26700, 116], [26839, 15], [26859, 84], [27000, 116], [27139, 14], [27160, 83], [27300, 117], [27438, 14], [27461, 82], [27600, 117], [27738, 13], [27762, 81], [27900, 118], [28038, 13], [28062, 80], [28200, 119], [28338, 12], [28362, 80], [28500, 120], [28638, 12], [28663, 79], [28800, 120], [28938, 12], [28963, 79], [29100, 121], [29238, 11], [29263, 79], [29400, 122], [29538, 11], [29563, 79], [29700, 123], [29838, 11], [29863, 78], [30000, 124], [30138, 11], [30163, 78], [30300, 125], [30438, 11], [30463, 78], [30600, 127], [30737, 13], [30763, 78], [30900, 150], [31063, 78], [31200, 150], [31362, 78], [31500, 151], [31662, 78], [31800, 152], [31961, 79], [32100, 153], [32260, 80], [32400, 155], [32557, 83], [32700, 240], [33000, 239], [33300, 239], [33600, 239], [33900, 239], [34200, 239], [34500, 238], [34800, 238], [35100, 238], [35400, 238], [35700, 238], [36000, 238], [36300, 237], [36600, 237], [36900, 237], [37200, 237], [37500, 237], [37800, 236], [38100, 236], [38400, 236], [38700, 236], [39000, 236], [39300, 236], [39600, 55], [39677, 6], [39798, 37], [39900, 54], [39977, 4], [40098, 37], [40200, 54], [40399, 36], [40500, 53], [40800, 52], [41100, 52], [41400, 51], [41700, 50], [42000, 49], [42300, 49], [42600, 48], [42900, 47], [43200, 47], [43500, 46], [43800, 45], [44100, 44], [44400, 44], [44700, 43], [45000, 42], [45300, 41], [45600, 41], [45900, 40], [46200, 39], [46500, 39], [46800, 38], [47100, 37], [47400, 36], [47700, 36], [48000, 35], [48300, 34], [48600, 34], [48900, 33], [49200, 32], [49500, 31], [49800, 31], [50100, 30], [50400, 29], [50700, 29], [51000, 28], [51300, 27], [51600, 26], [51900, 26], [52200, 25], [52500, 24], [52800, 24], [53100, 23], [53400, 22], [53700, 21], [54000, 21], [54300, 20], [54600, 19], [54900, 19], [55200, 18], [55500, 17], [55800, 16], [56100, 16], [56400, 15], [56700, 14], [57000, 14], [57300, 13], [57600, 12], [57900, 11], [58200, 11], [58500, 10], [58800, 9], [59100, 9], [59400, 8], [59700, 7], [60000, 6], [60300, 6], [60600, 5], [60900, 4], [61200, 4], [61500, 3], [61800, 2], [62100, 1], [62400, 1]], "point": [126, 115]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 24, 252, 209], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9622, 226], [9918, 233], [10214, 239], [10510, 243], [10806, 247], [11102, 251], [11400, 253], [11700, 252], [12000, 252], [12300, 252], [12600, 252], [12900, 252], [13200, 251], [13500, 251], [13800, 251], [14100, 251], [14400, 251], [14700, 251], [15000, 250], [15300, 250], [15600, 250], [15900, 250], [16200, 250], [16500, 249], [16800, 249], [17100, 249], [17400, 249], [17700, 249], [18000, 248], [18300, 248], [18600, 248], [18900, 248], [19200, 248], [19500, 248], [19800, 247], [20100, 247], [20400, 126], [20530, 117], [20700, 123], [20831, 116], [21000, 121], [21133, 114], [21300, 119], [21434, 112], [21600, 118], [21734, 112], [21900, 117], [22035, 111], [22200, 94], [22300, 17], [22336, 110], [22500, 92], [22602, 14], [22636, 110], [22800, 91], [22904, 12], [22937, 109], [23100, 90], [23205, 10], [23237, 108], [23400, 89], [23505, 10], [23537, 108], [23700, 88], [23806, 8], [23838, 107], [24000, 88], [24107, 7], [24138, 107], [24300, 88], [24408, 6], [24438, 107], [24600, 87], [24708, 6], [24738, 106], [24900, 87], [25008, 6], [25038, 106], [25200, 86], [25309, 5], [25339, 105], [25500, 86], [25609, 5], [25639, 105], [25800, 86], [25909, 5], [25939, 105], [26100, 86], [26209, 6], [26239, 105], [26400, 86], [26510, 5], [26539, 104], [26700, 86], [26810, 6], [26839, 15], [26859, 84], [27000, 87], [27110, 6], [27139, 14], [27160, 83], [27300, 87], [27410, 7], [27438, 14], [27461, 82], [27600, 87], [27709, 8], [27738, 13], [27762, 81], [27900, 88], [28009, 9], [28038, 13], [28062, 80], [28200, 89], [28309, 10], [28338, 12], [28362, 80], [28500, 89], [28608, 12], [28638, 12], [28663, 79], [28800, 90], [28908, 12], [28938, 12], [28963, 79], [29100, 91], [29207, 14], [29238, 11], [29263, 79], [29400, 92], [29507, 15], [29538, 11], [29563, 79], [29700, 93], [29806, 17], [29838, 11], [29863, 78], [30000, 95], [30104, 20], [30138, 11], [30163, 78], [30300, 97], [30402, 23], [30438, 11], [30463, 78], [30600, 127], [30737, 13], [30763, 78], [30900, 150], [31063, 78], [31200, 150], [31362, 78], [31500, 151], [31662, 78], [31800, 152], [31961, 79], [32100, 153], [32260, 80], [32400, 155], [32557, 83], [32700, 240], [33000, 239], [33300, 239], [33600, 239], [33900, 239], [34200, 239], [34500, 238], [34800, 238], [35100, 238], [35400, 238], [35700, 238], [36000, 238], [36300, 237], [36600, 237], [36900, 237], [37200, 237], [37500, 237], [37800, 236], [38100, 236], [38400, 236], [38700, 236], [39000, 236], [39300, 236], [39600, 55], [39677, 6], [39798, 37], [39900, 54], [39977, 4], [40098, 37], [40200, 54], [40399, 36], [40500, 53], [40800, 52], [41100, 52], [41400, 51], [41700, 50], [42000, 49], [42300, 49], [42600, 48], [42900, 47], [43200, 47], [43500, 46], [43800, 45], [44100, 44], [44400, 44], [44700, 43], [45000, 42], [45300, 41], [45600, 41], [45900, 40], [46200, 39], [46500, 39], [46800, 38], [47100, 37], [47400, 36], [47700, 36], [48000, 35], [48300, 34], [48600, 34], [48900, 33], [49200, 32], [49500, 31], [49800, 31], [50100, 30], [50400, 29], [50700, 29], [51000, 28], [51300, 27], [51600, 26], [51900, 26], [52200, 25], [52500, 24], [52800, 24], [53100, 23], [53400, 22], [53700, 21], [54000, 21], [54300, 20], [54600, 19], [54900, 19], [55200, 18], [55500, 17], [55800, 16], [56100, 16], [56400, 15], [56700, 14], [57000, 14], [57300, 13], [57600, 12], [57900, 11], [58200, 11], [58500, 10], [58800, 9], [59100, 9], [59400, 8], [59700, 7], [60000, 6], [60300, 6], [60600, 5], [60900, 4], [61200, 4], [61500, 3], [61800, 2], [62100, 1], [62400, 1]], "point": [126, 115]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan8", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -0.25, "y": 0.9009992, "z": 0.0}, "object_poses": [{"objectName": "Pan_c0048524", "position": {"x": -1.431, "y": 0.949799955, "z": -1.81}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_c0048524", "position": {"x": 1.34322, "y": 0.9109095, "z": 0.372564077}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_abf5c390", "position": {"x": -2.19359946, "y": 0.909904063, "z": -1.87949979}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_73e168e3", "position": {"x": -1.95722759, "y": 0.9131528, "z": 0.463499784}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_1cf505ed", "position": {"x": -0.552625656, "y": 0.9530963, "z": -1.78968191}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_238538e2", "position": {"x": -0.4018854, "y": 0.95566237, "z": -1.61925435}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_e75d4607", "position": {"x": 1.41487837, "y": 1.16324139, "z": -0.659025431}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_e75d4607", "position": {"x": -1.431, "y": 0.949799955, "z": -1.5257}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_b308514b", "position": {"x": -0.0374549329, "y": 1.6562916, "z": -1.91353726}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_7aaf3771", "position": {"x": 0.9161422, "y": 0.773711, "z": -1.013817}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Fork_7aaf3771", "position": {"x": -2.20158482, "y": 0.911048532, "z": 0.291}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_6f7c42a7", "position": {"x": -2.1035, "y": 0.7757431, "z": -1.50032341}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_1d30af71", "position": {"x": -0.7787359, "y": 0.9262998, "z": -1.87489557}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_1d30af71", "position": {"x": 1.0809052, "y": 0.788162351, "z": -1.23358309}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_e08d0eeb", "position": {"x": 0.14961265, "y": 0.9488791, "z": -1.65757918}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_bfcb0336", "position": {"x": -0.7033658, "y": 0.9117788, "z": -1.70446813}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_9b9c5535", "position": {"x": -0.67566514, "y": 1.65624368, "z": -1.839}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_af290bda", "position": {"x": -2.20158482, "y": 0.940200269, "z": 0.118500233}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_af290bda", "position": {"x": -1.73225582, "y": 0.940200269, "z": -1.87949979}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_c69c5340", "position": {"x": 0.285940379, "y": 0.919453442, "z": -1.75846994}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_c69c5340", "position": {"x": 1.15081286, "y": 0.115112662, "z": -1.18653619}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1de14ecd", "position": {"x": -1.88603711, "y": 0.9055, "z": -1.62075019}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1de14ecd", "position": {"x": 1.494119, "y": 1.1572001, "z": -1.34564734}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_a2768593", "position": {"x": 0.9731128, "y": 0.109924793, "z": -0.699439943}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_a2768593", "position": {"x": -1.88603711, "y": 0.9055, "z": -1.96574974}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_b66a32b5", "position": {"x": 1.476937, "y": 1.40215027, "z": 1.85099959}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Tomato_441a8bda", "position": {"x": 1.476939, "y": 1.407585, "z": 2.47599983}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Glassbottle_b308514b", "position": {"x": 0.9195629, "y": 0.115925908, "z": -1.18653619}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_af290bda", "position": {"x": 1.260095, "y": 0.9410003, "z": -0.309145331}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_9b9c5535", "position": {"x": 1.150191, "y": 0.118529677, "z": 0.37244302}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_7aaf3771", "position": {"x": -2.07940626, "y": 0.911048532, "z": 0.5497497}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_238538e2", "position": {"x": -0.3863028, "y": 1.69993734, "z": -1.839}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_1cf505ed", "position": {"x": -0.06378223, "y": 0.824682832, "z": -1.70783865}, "rotation": {"x": 1.40334191e-14, "y": 270.0, "z": 0.0}}, {"objectName": "Lettuce_0f08525d", "position": {"x": 0.526190639, "y": 0.952575564, "z": -1.56680572}, "rotation": {"x": 9.019211, "y": 143.625824, "z": 359.3296}}, {"objectName": "Kettle_e75d4607", "position": {"x": -1.0811, "y": 0.949799955, "z": -1.81}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_73e168e3", "position": {"x": 0.969732344, "y": 0.7776822, "z": 0.377106577}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_a2768593", "position": {"x": 1.33563781, "y": 1.1572001, "z": -1.68895817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_bfcb0336", "position": {"x": 1.47693741, "y": 1.36794627, "z": 1.97599983}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Egg_e08d0eeb", "position": {"x": 1.34429359, "y": 1.29478192, "z": -0.0212406684}, "rotation": {"x": 0.0, "y": 270.000153, "z": 0.0}}, {"objectName": "Spatula_1d30af71", "position": {"x": -2.32376337, "y": 0.925499856, "z": 0.291}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_a0ece05a", "position": {"x": -0.07234678, "y": 0.115160108, "z": -1.536113}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SprayBottle_abf5c390", "position": {"x": 0.8524788, "y": 0.910704, "z": -1.61925447}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_c0048524", "position": {"x": -1.07936192, "y": 0.94527024, "z": -1.52895641}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_c69c5340", "position": {"x": -1.43889976, "y": 1.85820985, "z": -1.83869994}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_316d55f0", "position": {"x": 1.41487837, "y": 1.16454041, "z": -1.00233626}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_7cca838f", "position": {"x": -2.32376337, "y": 0.9105421, "z": 0.463499784}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1de14ecd", "position": {"x": -2.07940626, "y": 0.9055, "z": 0.204750121}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_b7024f32", "position": {"x": 0.217776537, "y": 0.919114232, "z": -1.60713387}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bread_9b638b9f", "position": {"x": -1.95722759, "y": 0.988641143, "z": 0.204750121}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_b66a32b5", "position": {"x": 1.34153056, "y": 0.2492218, "z": 1.05101991}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_6f7c42a7", "position": {"x": 0.9037963, "y": 0.774564, "z": 0.323113024}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_49b86ad0", "position": {"x": 1.41487837, "y": 1.1572001, "z": -1.34564734}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 653939919, "scene_num": 8}, "task_id": "trial_T20190906_235202_410386", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A10AVWALIHR4UQ_3D4CH1LGEDA4Q1RA23EJXULYNOJG9K", "high_descs": ["Turn around and move toward the white dishwasher then turn right and move to the sink.", "Pick up the potato on top of the drain from the sink.", "Turn around and move toward the refrigerator then turn to the right and move to the white dishwasher and look up at the microwave above the dishwasher.", "Open the microwave and place the potato on the plate to the left of the egg and then close the microwave.", "Turn around and move toward the black stool then turn left and move to the counter between the sink and the range.", "Pick up the potato to the left of the spatula and to the rear of the ladle on the counter.", "Turn around and move toward the refrigerator then turn right and move toward the dishwasher and look up at the microwave above the dishwasher.", "Place the potato on the plate in the microwave to the left of the potato already there and close the microwave door."], "task_desc": "Place two potatoes in the microwave.", "votes": [1, 1]}, {"assignment_id": "A1CY7IOJ9YH136_3E337GFOLCPPF3V43DXOFVGISG8GNX", "high_descs": ["go to sink to the left", "pick up potato out of sink", "go to counter to the left with microwave", "open microwave, place potato on blue plate, close microwave", "go to counter to the right of the sink", "pick up potato above the ladle", "go to microwave to the left", "place potato in microwave to left of other potato"], "task_desc": "Place two potatoes in microwave", "votes": [1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3U5NZHP4LUJ3IK2S3YSY4KXE7LHHP4", "high_descs": ["Turn left and walk over to the kitchen sink.", "Pick up the potato that is in the kitchen sink.", "Turn around and begin walking across the room, then hang right and walk up to the microwave.", "Open the microwave and place the potato inside to the left of the egg.", "Turn around and begin walking across the room, then hang left and walk up to the kitchen counter to the right of the sink.", "Pick up the potato that is on the kitchen counter.", "Turn around and walk back to the microwave.", "Open the microwave and place the second potato inside to the left of the first potato."], "task_desc": "Place two potatoes into the microwave.", "votes": [1, 1]}]}}