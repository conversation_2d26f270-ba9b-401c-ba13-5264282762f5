
(define (problem plan_trial_T20190908_034605_540049)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON>haker - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Box_bar__minus_04_dot_67_bar__plus_00_dot_41_bar__plus_02_dot_34 - object
        Chair_bar__minus_04_dot_72_bar__plus_00_dot_03_bar__plus_04_dot_60 - object
        CreditCard_bar__minus_00_dot_68_bar__plus_00_dot_54_bar__plus_01_dot_85 - object
        CreditCard_bar__minus_02_dot_41_bar__plus_00_dot_45_bar__plus_02_dot_46 - object
        Curtains_bar__minus_02_dot_54_bar__plus_02_dot_60_bar__plus_00_dot_11 - object
        FloorLamp_bar__minus_04_dot_66_bar__plus_00_dot_00_bar__plus_00_dot_42 - object
        HousePlant_bar__minus_04_dot_82_bar__plus_00_dot_98_bar__plus_01_dot_57 - object
        KeyChain_bar__minus_00_dot_59_bar__plus_00_dot_53_bar__plus_03_dot_03 - object
        KeyChain_bar__minus_05_dot_01_bar__plus_00_dot_99_bar__plus_01_dot_74 - object
        Lamp_bar__plus_00_dot_02_bar__plus_00_dot_70_bar__plus_04_dot_68 - object
        Laptop_bar__minus_04_dot_87_bar__plus_00_dot_98_bar__plus_03_dot_44 - object
        LightSwitch_bar__minus_01_dot_02_bar__plus_01_dot_24_bar__plus_05_dot_00 - object
        Painting_bar__minus_00_dot_29_bar__plus_01_dot_82_bar__plus_00_dot_02 - object
        Painting_bar__minus_05_dot_12_bar__plus_01_dot_48_bar__plus_00_dot_66 - object
        Painting_bar__minus_05_dot_12_bar__plus_01_dot_48_bar__plus_04_dot_40 - object
        Pillow_bar__minus_02_dot_47_bar__plus_00_dot_60_bar__plus_00_dot_84 - object
        Pillow_bar__minus_03_dot_29_bar__plus_00_dot_62_bar__plus_00_dot_80 - object
        RemoteControl_bar__minus_00_dot_33_bar__plus_00_dot_47_bar__plus_03_dot_31 - object
        Statue_bar__minus_02_dot_66_bar__plus_00_dot_55_bar__plus_02_dot_36 - object
        Television_bar__minus_04_dot_88_bar__plus_01_dot_47_bar__plus_02_dot_43 - object
        Watch_bar__minus_04_dot_57_bar__plus_00_dot_89_bar__plus_02_dot_18 - object
        Watch_bar__minus_04_dot_66_bar__plus_00_dot_99_bar__plus_02_dot_88 - object
        WateringCan_bar__minus_00_dot_28_bar_00_dot_00_bar__plus_04_dot_86 - object
        Window_bar__minus_01_dot_72_bar__plus_01_dot_55_bar__plus_00_dot_01 - object
        Window_bar__minus_03_dot_25_bar__plus_01_dot_55_bar__plus_00_dot_01 - object
        ArmChair_bar__minus_00_dot_31_bar__minus_00_dot_03_bar__plus_01_dot_71 - receptacle
        ArmChair_bar__minus_00_dot_31_bar__minus_00_dot_03_bar__plus_03_dot_31 - receptacle
        Cabinet_bar__minus_04_dot_49_bar__plus_00_dot_50_bar__plus_01_dot_57 - receptacle
        Cabinet_bar__minus_04_dot_49_bar__plus_00_dot_50_bar__plus_02_dot_59 - receptacle
        Cabinet_bar__minus_04_dot_49_bar__plus_00_dot_50_bar__plus_02_dot_61 - receptacle
        Cabinet_bar__minus_04_dot_49_bar__plus_00_dot_50_bar__plus_03_dot_63 - receptacle
        CoffeeTable_bar__minus_02_dot_41_bar__plus_00_dot_00_bar__plus_02_dot_55 - receptacle
        Drawer_bar__minus_04_dot_58_bar__plus_00_dot_90_bar__plus_01_dot_82 - receptacle
        Drawer_bar__minus_04_dot_58_bar__plus_00_dot_90_bar__plus_02_dot_34 - receptacle
        Drawer_bar__minus_04_dot_58_bar__plus_00_dot_90_bar__plus_02_dot_86 - receptacle
        Drawer_bar__minus_04_dot_58_bar__plus_00_dot_90_bar__plus_03_dot_37 - receptacle
        Dresser_bar__minus_04_dot_83_bar__plus_00_dot_00_bar__plus_02_dot_60 - receptacle
        GarbageCan_bar__minus_04_dot_81_bar__plus_00_dot_00_bar__plus_01_dot_24 - receptacle
        SideTable_bar__minus_00_dot_04_bar_00_dot_00_bar__plus_04_dot_62 - receptacle
        Sofa_bar__minus_02_dot_47_bar_00_dot_00_bar__plus_00_dot_70 - receptacle
        loc_bar__minus_4_bar_17_bar_1_bar_60 - location
        loc_bar__minus_16_bar_13_bar_3_bar_45 - location
        loc_bar__minus_4_bar_6_bar_2_bar_0 - location
        loc_bar__minus_18_bar_16_bar_0_bar_60 - location
        loc_bar__minus_16_bar_3_bar_1_bar_0 - location
        loc_bar__minus_16_bar_10_bar_3_bar_60 - location
        loc_bar__minus_4_bar_13_bar_1_bar_60 - location
        loc_bar__minus_9_bar_7_bar_2_bar_60 - location
        loc_bar__minus_7_bar_6_bar_2_bar_0 - location
        loc_bar__minus_15_bar_6_bar_3_bar_60 - location
        loc_bar__minus_16_bar_7_bar_3_bar_45 - location
        loc_bar__minus_16_bar_3_bar_3_bar_60 - location
        loc_bar__minus_15_bar_7_bar_3_bar_60 - location
        loc_bar__minus_10_bar_6_bar_2_bar__minus_30 - location
        loc_bar__minus_16_bar_3_bar_3_bar_15 - location
        loc_bar__minus_6_bar_13_bar_1_bar_60 - location
        loc_bar__minus_15_bar_8_bar_3_bar_60 - location
        loc_bar__minus_16_bar_11_bar_3_bar_45 - location
        loc_bar__minus_16_bar_10_bar_3_bar_45 - location
        loc_bar__minus_2_bar_18_bar_0_bar_60 - location
        loc_bar__minus_9_bar_6_bar_0_bar_60 - location
        loc_bar__minus_15_bar_12_bar_3_bar_60 - location
        loc_bar__minus_18_bar_16_bar_3_bar_15 - location
        loc_bar__minus_15_bar_10_bar_3_bar_60 - location
        loc_bar__minus_4_bar_18_bar_0_bar_45 - location
        loc_bar__minus_6_bar_7_bar_1_bar_60 - location
        loc_bar__minus_8_bar_14_bar_0_bar_30 - location
        )
    

(:init


        (receptacleType GarbageCan_bar__minus_04_dot_81_bar__plus_00_dot_00_bar__plus_01_dot_24 GarbageCanType)
        (receptacleType Drawer_bar__minus_04_dot_58_bar__plus_00_dot_90_bar__plus_01_dot_82 DrawerType)
        (receptacleType Cabinet_bar__minus_04_dot_49_bar__plus_00_dot_50_bar__plus_01_dot_57 CabinetType)
        (receptacleType Sofa_bar__minus_02_dot_47_bar_00_dot_00_bar__plus_00_dot_70 SofaType)
        (receptacleType Drawer_bar__minus_04_dot_58_bar__plus_00_dot_90_bar__plus_02_dot_86 DrawerType)
        (receptacleType ArmChair_bar__minus_00_dot_31_bar__minus_00_dot_03_bar__plus_01_dot_71 ArmChairType)
        (receptacleType ArmChair_bar__minus_00_dot_31_bar__minus_00_dot_03_bar__plus_03_dot_31 ArmChairType)
        (receptacleType Cabinet_bar__minus_04_dot_49_bar__plus_00_dot_50_bar__plus_02_dot_59 CabinetType)
        (receptacleType Cabinet_bar__minus_04_dot_49_bar__plus_00_dot_50_bar__plus_03_dot_63 CabinetType)
        (receptacleType Drawer_bar__minus_04_dot_58_bar__plus_00_dot_90_bar__plus_02_dot_34 DrawerType)
        (receptacleType CoffeeTable_bar__minus_02_dot_41_bar__plus_00_dot_00_bar__plus_02_dot_55 CoffeeTableType)
        (receptacleType Drawer_bar__minus_04_dot_58_bar__plus_00_dot_90_bar__plus_03_dot_37 DrawerType)
        (receptacleType SideTable_bar__minus_00_dot_04_bar_00_dot_00_bar__plus_04_dot_62 SideTableType)
        (receptacleType Cabinet_bar__minus_04_dot_49_bar__plus_00_dot_50_bar__plus_02_dot_61 CabinetType)
        (receptacleType Dresser_bar__minus_04_dot_83_bar__plus_00_dot_00_bar__plus_02_dot_60 DresserType)
        (objectType Watch_bar__minus_04_dot_66_bar__plus_00_dot_99_bar__plus_02_dot_88 WatchType)
        (objectType FloorLamp_bar__minus_04_dot_66_bar__plus_00_dot_00_bar__plus_00_dot_42 FloorLampType)
        (objectType KeyChain_bar__minus_00_dot_59_bar__plus_00_dot_53_bar__plus_03_dot_03 KeyChainType)
        (objectType Painting_bar__minus_00_dot_29_bar__plus_01_dot_82_bar__plus_00_dot_02 PaintingType)
        (objectType KeyChain_bar__minus_05_dot_01_bar__plus_00_dot_99_bar__plus_01_dot_74 KeyChainType)
        (objectType Window_bar__minus_01_dot_72_bar__plus_01_dot_55_bar__plus_00_dot_01 WindowType)
        (objectType Window_bar__minus_03_dot_25_bar__plus_01_dot_55_bar__plus_00_dot_01 WindowType)
        (objectType Painting_bar__minus_05_dot_12_bar__plus_01_dot_48_bar__plus_00_dot_66 PaintingType)
        (objectType WateringCan_bar__minus_00_dot_28_bar_00_dot_00_bar__plus_04_dot_86 WateringCanType)
        (objectType Statue_bar__minus_02_dot_66_bar__plus_00_dot_55_bar__plus_02_dot_36 StatueType)
        (objectType Chair_bar__minus_04_dot_72_bar__plus_00_dot_03_bar__plus_04_dot_60 ChairType)
        (objectType HousePlant_bar__minus_04_dot_82_bar__plus_00_dot_98_bar__plus_01_dot_57 HousePlantType)
        (objectType Laptop_bar__minus_04_dot_87_bar__plus_00_dot_98_bar__plus_03_dot_44 LaptopType)
        (objectType Television_bar__minus_04_dot_88_bar__plus_01_dot_47_bar__plus_02_dot_43 TelevisionType)
        (objectType LightSwitch_bar__minus_01_dot_02_bar__plus_01_dot_24_bar__plus_05_dot_00 LightSwitchType)
        (objectType Pillow_bar__minus_03_dot_29_bar__plus_00_dot_62_bar__plus_00_dot_80 PillowType)
        (objectType RemoteControl_bar__minus_00_dot_33_bar__plus_00_dot_47_bar__plus_03_dot_31 RemoteControlType)
        (objectType Watch_bar__minus_04_dot_57_bar__plus_00_dot_89_bar__plus_02_dot_18 WatchType)
        (objectType Box_bar__minus_04_dot_67_bar__plus_00_dot_41_bar__plus_02_dot_34 BoxType)
        (objectType Curtains_bar__minus_02_dot_54_bar__plus_02_dot_60_bar__plus_00_dot_11 CurtainsType)
        (objectType Painting_bar__minus_05_dot_12_bar__plus_01_dot_48_bar__plus_04_dot_40 PaintingType)
        (objectType Pillow_bar__minus_02_dot_47_bar__plus_00_dot_60_bar__plus_00_dot_84 PillowType)
        (objectType CreditCard_bar__minus_00_dot_68_bar__plus_00_dot_54_bar__plus_01_dot_85 CreditCardType)
        (objectType CreditCard_bar__minus_02_dot_41_bar__plus_00_dot_45_bar__plus_02_dot_46 CreditCardType)
        (canContain DrawerType WatchType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain CabinetType BoxType)
        (canContain CabinetType WateringCanType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType CreditCardType)
        (canContain DrawerType WatchType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (canContain CabinetType BoxType)
        (canContain CabinetType WateringCanType)
        (canContain CabinetType BoxType)
        (canContain CabinetType WateringCanType)
        (canContain DrawerType WatchType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain CoffeeTableType WatchType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType StatueType)
        (canContain CoffeeTableType WateringCanType)
        (canContain DrawerType WatchType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain SideTableType WatchType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain SideTableType WateringCanType)
        (canContain CabinetType BoxType)
        (canContain CabinetType WateringCanType)
        (canContain DresserType WatchType)
        (canContain DresserType BoxType)
        (canContain DresserType KeyChainType)
        (canContain DresserType CreditCardType)
        (canContain DresserType LaptopType)
        (canContain DresserType RemoteControlType)
        (canContain DresserType StatueType)
        (canContain DresserType WateringCanType)
        (pickupable Watch_bar__minus_04_dot_66_bar__plus_00_dot_99_bar__plus_02_dot_88)
        (pickupable KeyChain_bar__minus_00_dot_59_bar__plus_00_dot_53_bar__plus_03_dot_03)
        (pickupable KeyChain_bar__minus_05_dot_01_bar__plus_00_dot_99_bar__plus_01_dot_74)
        (pickupable WateringCan_bar__minus_00_dot_28_bar_00_dot_00_bar__plus_04_dot_86)
        (pickupable Statue_bar__minus_02_dot_66_bar__plus_00_dot_55_bar__plus_02_dot_36)
        (pickupable Laptop_bar__minus_04_dot_87_bar__plus_00_dot_98_bar__plus_03_dot_44)
        (pickupable Pillow_bar__minus_03_dot_29_bar__plus_00_dot_62_bar__plus_00_dot_80)
        (pickupable RemoteControl_bar__minus_00_dot_33_bar__plus_00_dot_47_bar__plus_03_dot_31)
        (pickupable Watch_bar__minus_04_dot_57_bar__plus_00_dot_89_bar__plus_02_dot_18)
        (pickupable Box_bar__minus_04_dot_67_bar__plus_00_dot_41_bar__plus_02_dot_34)
        (pickupable Pillow_bar__minus_02_dot_47_bar__plus_00_dot_60_bar__plus_00_dot_84)
        (pickupable CreditCard_bar__minus_00_dot_68_bar__plus_00_dot_54_bar__plus_01_dot_85)
        (pickupable CreditCard_bar__minus_02_dot_41_bar__plus_00_dot_45_bar__plus_02_dot_46)
        (isReceptacleObject Box_bar__minus_04_dot_67_bar__plus_00_dot_41_bar__plus_02_dot_34)
        (openable Drawer_bar__minus_04_dot_58_bar__plus_00_dot_90_bar__plus_01_dot_82)
        (openable Cabinet_bar__minus_04_dot_49_bar__plus_00_dot_50_bar__plus_01_dot_57)
        (openable Drawer_bar__minus_04_dot_58_bar__plus_00_dot_90_bar__plus_02_dot_86)
        (openable Cabinet_bar__minus_04_dot_49_bar__plus_00_dot_50_bar__plus_02_dot_59)
        (openable Cabinet_bar__minus_04_dot_49_bar__plus_00_dot_50_bar__plus_03_dot_63)
        (openable Drawer_bar__minus_04_dot_58_bar__plus_00_dot_90_bar__plus_02_dot_34)
        (openable Drawer_bar__minus_04_dot_58_bar__plus_00_dot_90_bar__plus_03_dot_37)
        (openable Cabinet_bar__minus_04_dot_49_bar__plus_00_dot_50_bar__plus_02_dot_61)
        
        (atLocation agent1 loc_bar__minus_8_bar_14_bar_0_bar_30)
        
        
        
        
        
        
        
        (toggleable FloorLamp_bar__minus_04_dot_66_bar__plus_00_dot_00_bar__plus_00_dot_42)
        
        
        
        
        (inReceptacle Box_bar__minus_04_dot_67_bar__plus_00_dot_41_bar__plus_02_dot_34 Cabinet_bar__minus_04_dot_49_bar__plus_00_dot_50_bar__plus_02_dot_59)
        (inReceptacle Pillow_bar__minus_02_dot_47_bar__plus_00_dot_60_bar__plus_00_dot_84 Sofa_bar__minus_02_dot_47_bar_00_dot_00_bar__plus_00_dot_70)
        (inReceptacle Pillow_bar__minus_03_dot_29_bar__plus_00_dot_62_bar__plus_00_dot_80 Sofa_bar__minus_02_dot_47_bar_00_dot_00_bar__plus_00_dot_70)
        (inReceptacle Laptop_bar__minus_04_dot_87_bar__plus_00_dot_98_bar__plus_03_dot_44 Dresser_bar__minus_04_dot_83_bar__plus_00_dot_00_bar__plus_02_dot_60)
        (inReceptacle Watch_bar__minus_04_dot_66_bar__plus_00_dot_99_bar__plus_02_dot_88 Dresser_bar__minus_04_dot_83_bar__plus_00_dot_00_bar__plus_02_dot_60)
        (inReceptacle Television_bar__minus_04_dot_88_bar__plus_01_dot_47_bar__plus_02_dot_43 Dresser_bar__minus_04_dot_83_bar__plus_00_dot_00_bar__plus_02_dot_60)
        (inReceptacle KeyChain_bar__minus_05_dot_01_bar__plus_00_dot_99_bar__plus_01_dot_74 Dresser_bar__minus_04_dot_83_bar__plus_00_dot_00_bar__plus_02_dot_60)
        (inReceptacle HousePlant_bar__minus_04_dot_82_bar__plus_00_dot_98_bar__plus_01_dot_57 Dresser_bar__minus_04_dot_83_bar__plus_00_dot_00_bar__plus_02_dot_60)
        (inReceptacle Watch_bar__minus_04_dot_57_bar__plus_00_dot_89_bar__plus_02_dot_18 Drawer_bar__minus_04_dot_58_bar__plus_00_dot_90_bar__plus_02_dot_34)
        (inReceptacle CreditCard_bar__minus_00_dot_68_bar__plus_00_dot_54_bar__plus_01_dot_85 ArmChair_bar__minus_00_dot_31_bar__minus_00_dot_03_bar__plus_01_dot_71)
        (inReceptacle KeyChain_bar__minus_00_dot_59_bar__plus_00_dot_53_bar__plus_03_dot_03 ArmChair_bar__minus_00_dot_31_bar__minus_00_dot_03_bar__plus_03_dot_31)
        (inReceptacle Statue_bar__minus_02_dot_66_bar__plus_00_dot_55_bar__plus_02_dot_36 CoffeeTable_bar__minus_02_dot_41_bar__plus_00_dot_00_bar__plus_02_dot_55)
        (inReceptacle CreditCard_bar__minus_02_dot_41_bar__plus_00_dot_45_bar__plus_02_dot_46 CoffeeTable_bar__minus_02_dot_41_bar__plus_00_dot_00_bar__plus_02_dot_55)
        
        
        (receptacleAtLocation ArmChair_bar__minus_00_dot_31_bar__minus_00_dot_03_bar__plus_01_dot_71 loc_bar__minus_6_bar_7_bar_1_bar_60)
        (receptacleAtLocation ArmChair_bar__minus_00_dot_31_bar__minus_00_dot_03_bar__plus_03_dot_31 loc_bar__minus_6_bar_13_bar_1_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_04_dot_49_bar__plus_00_dot_50_bar__plus_01_dot_57 loc_bar__minus_15_bar_6_bar_3_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_04_dot_49_bar__plus_00_dot_50_bar__plus_02_dot_59 loc_bar__minus_15_bar_8_bar_3_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_04_dot_49_bar__plus_00_dot_50_bar__plus_02_dot_61 loc_bar__minus_15_bar_10_bar_3_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_04_dot_49_bar__plus_00_dot_50_bar__plus_03_dot_63 loc_bar__minus_15_bar_12_bar_3_bar_60)
        (receptacleAtLocation CoffeeTable_bar__minus_02_dot_41_bar__plus_00_dot_00_bar__plus_02_dot_55 loc_bar__minus_9_bar_6_bar_0_bar_60)
        (receptacleAtLocation Drawer_bar__minus_04_dot_58_bar__plus_00_dot_90_bar__plus_01_dot_82 loc_bar__minus_16_bar_7_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_04_dot_58_bar__plus_00_dot_90_bar__plus_02_dot_34 loc_bar__minus_16_bar_10_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_04_dot_58_bar__plus_00_dot_90_bar__plus_02_dot_86 loc_bar__minus_16_bar_11_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_04_dot_58_bar__plus_00_dot_90_bar__plus_03_dot_37 loc_bar__minus_16_bar_13_bar_3_bar_45)
        (receptacleAtLocation Dresser_bar__minus_04_dot_83_bar__plus_00_dot_00_bar__plus_02_dot_60 loc_bar__minus_16_bar_10_bar_3_bar_60)
        (receptacleAtLocation GarbageCan_bar__minus_04_dot_81_bar__plus_00_dot_00_bar__plus_01_dot_24 loc_bar__minus_15_bar_7_bar_3_bar_60)
        (receptacleAtLocation SideTable_bar__minus_00_dot_04_bar_00_dot_00_bar__plus_04_dot_62 loc_bar__minus_4_bar_17_bar_1_bar_60)
        (receptacleAtLocation Sofa_bar__minus_02_dot_47_bar_00_dot_00_bar__plus_00_dot_70 loc_bar__minus_9_bar_7_bar_2_bar_60)
        (objectAtLocation Watch_bar__minus_04_dot_57_bar__plus_00_dot_89_bar__plus_02_dot_18 loc_bar__minus_16_bar_10_bar_3_bar_45)
        (objectAtLocation Pillow_bar__minus_03_dot_29_bar__plus_00_dot_62_bar__plus_00_dot_80 loc_bar__minus_9_bar_7_bar_2_bar_60)
        (objectAtLocation KeyChain_bar__minus_00_dot_59_bar__plus_00_dot_53_bar__plus_03_dot_03 loc_bar__minus_6_bar_13_bar_1_bar_60)
        (objectAtLocation CreditCard_bar__minus_00_dot_68_bar__plus_00_dot_54_bar__plus_01_dot_85 loc_bar__minus_6_bar_7_bar_1_bar_60)
        (objectAtLocation Box_bar__minus_04_dot_67_bar__plus_00_dot_41_bar__plus_02_dot_34 loc_bar__minus_15_bar_8_bar_3_bar_60)
        (objectAtLocation Curtains_bar__minus_02_dot_54_bar__plus_02_dot_60_bar__plus_00_dot_11 loc_bar__minus_10_bar_6_bar_2_bar__minus_30)
        (objectAtLocation Chair_bar__minus_04_dot_72_bar__plus_00_dot_03_bar__plus_04_dot_60 loc_bar__minus_18_bar_16_bar_0_bar_60)
        (objectAtLocation FloorLamp_bar__minus_04_dot_66_bar__plus_00_dot_00_bar__plus_00_dot_42 loc_bar__minus_16_bar_3_bar_3_bar_60)
        (objectAtLocation Television_bar__minus_04_dot_88_bar__plus_01_dot_47_bar__plus_02_dot_43 loc_bar__minus_16_bar_10_bar_3_bar_60)
        (objectAtLocation WateringCan_bar__minus_00_dot_28_bar_00_dot_00_bar__plus_04_dot_86 loc_bar__minus_2_bar_18_bar_0_bar_60)
        (objectAtLocation CreditCard_bar__minus_02_dot_41_bar__plus_00_dot_45_bar__plus_02_dot_46 loc_bar__minus_9_bar_6_bar_0_bar_60)
        (objectAtLocation KeyChain_bar__minus_05_dot_01_bar__plus_00_dot_99_bar__plus_01_dot_74 loc_bar__minus_16_bar_10_bar_3_bar_60)
        (objectAtLocation Pillow_bar__minus_02_dot_47_bar__plus_00_dot_60_bar__plus_00_dot_84 loc_bar__minus_9_bar_7_bar_2_bar_60)
        (objectAtLocation Laptop_bar__minus_04_dot_87_bar__plus_00_dot_98_bar__plus_03_dot_44 loc_bar__minus_16_bar_10_bar_3_bar_60)
        (objectAtLocation Painting_bar__minus_00_dot_29_bar__plus_01_dot_82_bar__plus_00_dot_02 loc_bar__minus_4_bar_6_bar_2_bar_0)
        (objectAtLocation LightSwitch_bar__minus_01_dot_02_bar__plus_01_dot_24_bar__plus_05_dot_00 loc_bar__minus_4_bar_18_bar_0_bar_45)
        (objectAtLocation Painting_bar__minus_05_dot_12_bar__plus_01_dot_48_bar__plus_04_dot_40 loc_bar__minus_18_bar_16_bar_3_bar_15)
        (objectAtLocation Painting_bar__minus_05_dot_12_bar__plus_01_dot_48_bar__plus_00_dot_66 loc_bar__minus_16_bar_3_bar_3_bar_15)
        (objectAtLocation RemoteControl_bar__minus_00_dot_33_bar__plus_00_dot_47_bar__plus_03_dot_31 loc_bar__minus_4_bar_13_bar_1_bar_60)
        (objectAtLocation Watch_bar__minus_04_dot_66_bar__plus_00_dot_99_bar__plus_02_dot_88 loc_bar__minus_16_bar_10_bar_3_bar_60)
        (objectAtLocation Window_bar__minus_01_dot_72_bar__plus_01_dot_55_bar__plus_00_dot_01 loc_bar__minus_7_bar_6_bar_2_bar_0)
        (objectAtLocation Window_bar__minus_03_dot_25_bar__plus_01_dot_55_bar__plus_00_dot_01 loc_bar__minus_16_bar_3_bar_1_bar_0)
        (objectAtLocation HousePlant_bar__minus_04_dot_82_bar__plus_00_dot_98_bar__plus_01_dot_57 loc_bar__minus_16_bar_10_bar_3_bar_60)
        (objectAtLocation Statue_bar__minus_02_dot_66_bar__plus_00_dot_55_bar__plus_02_dot_36 loc_bar__minus_9_bar_6_bar_0_bar_60)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 PillowType)
                                    (receptacleType ?r ArmChairType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 PillowType)
                                            (receptacleType ?r ArmChairType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            