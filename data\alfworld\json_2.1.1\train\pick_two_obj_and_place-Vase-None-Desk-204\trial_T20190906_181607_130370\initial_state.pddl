
(define (problem plan_trial_T20190906_181607_130370)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Box_bar__minus_02_dot_01_bar__plus_00_dot_95_bar__minus_02_dot_46 - object
        CellPhone_bar__minus_02_dot_91_bar__plus_00_dot_39_bar__plus_05_dot_92 - object
        Chair_bar__minus_00_dot_37_bar__plus_00_dot_00_bar__plus_03_dot_60 - object
        Chair_bar__minus_00_dot_41_bar__plus_00_dot_00_bar__plus_04_dot_45 - object
        Chair_bar__minus_00_dot_64_bar__plus_00_dot_00_bar__plus_00_dot_99 - object
        Chair_bar__minus_01_dot_36_bar__plus_00_dot_02_bar__minus_02_dot_58 - object
        Chair_bar__minus_01_dot_38_bar__plus_00_dot_02_bar__minus_01_dot_59 - object
        Chair_bar__minus_02_dot_15_bar__plus_00_dot_02_bar__minus_01_dot_62 - object
        Chair_bar__minus_02_dot_26_bar__plus_00_dot_02_bar__minus_02_dot_64 - object
        CreditCard_bar__plus_00_dot_14_bar__plus_00_dot_74_bar__plus_01_dot_69 - object
        CreditCard_bar__minus_01_dot_09_bar__plus_00_dot_74_bar__plus_01_dot_20 - object
        FloorLamp_bar__plus_00_dot_39_bar__plus_00_dot_00_bar__plus_05_dot_16 - object
        HousePlant_bar__minus_03_dot_94_bar__plus_00_dot_00_bar__plus_05_dot_97 - object
        KeyChain_bar__minus_00_dot_14_bar__plus_00_dot_12_bar__plus_01_dot_42 - object
        KeyChain_bar__minus_00_dot_63_bar__plus_00_dot_69_bar__plus_05_dot_90 - object
        Lamp_bar__plus_00_dot_28_bar__plus_00_dot_73_bar__plus_01_dot_38 - object
        Laptop_bar__minus_02_dot_47_bar__plus_00_dot_65_bar__minus_02_dot_20 - object
        Laptop_bar__minus_03_dot_69_bar__plus_00_dot_39_bar__plus_03_dot_88 - object
        LightSwitch_bar__minus_02_dot_74_bar__plus_01_dot_50_bar__minus_00_dot_41 - object
        Painting_bar__minus_01_dot_76_bar__plus_01_dot_42_bar__minus_03_dot_60 - object
        Painting_bar__minus_03_dot_37_bar__plus_01_dot_74_bar__minus_00_dot_30 - object
        Pillow_bar__minus_01_dot_41_bar__plus_00_dot_45_bar__plus_05_dot_92 - object
        Pillow_bar__minus_03_dot_59_bar__plus_00_dot_45_bar__plus_03_dot_35 - object
        RemoteControl_bar__minus_00_dot_11_bar__plus_00_dot_74_bar__plus_01_dot_59 - object
        RemoteControl_bar__minus_02_dot_16_bar__plus_00_dot_39_bar__plus_06_dot_10 - object
        Statue_bar__minus_01_dot_95_bar__plus_00_dot_51_bar__plus_04_dot_12 - object
        Statue_bar__minus_02_dot_47_bar__plus_00_dot_67_bar__minus_02_dot_46 - object
        Statue_bar__minus_04_dot_22_bar__plus_01_dot_26_bar__plus_00_dot_40 - object
        Television_bar__minus_00_dot_24_bar__plus_01_dot_17_bar__plus_05_dot_82 - object
        Vase_bar__minus_01_dot_56_bar__plus_00_dot_66_bar__minus_02_dot_33 - object
        Vase_bar__minus_02_dot_11_bar__plus_00_dot_46_bar__plus_03_dot_77 - object
        Vase_bar__minus_04_dot_21_bar__plus_01_dot_66_bar__plus_00_dot_92 - object
        Vase_bar__minus_04_dot_23_bar__plus_00_dot_79_bar__plus_00_dot_38 - object
        WateringCan_bar__minus_03_dot_58_bar__plus_00_dot_00_bar__plus_05_dot_51 - object
        Window_bar__minus_00_dot_13_bar__plus_01_dot_70_bar__plus_06_dot_74 - object
        Window_bar__minus_01_dot_01_bar__plus_01_dot_70_bar__minus_00_dot_15 - object
        Window_bar__minus_02_dot_83_bar__plus_00_dot_47_bar__minus_00_dot_16 - object
        Window_bar__minus_02_dot_83_bar__plus_01_dot_70_bar__minus_00_dot_16 - object
        Window_bar__minus_03_dot_64_bar__plus_01_dot_70_bar__plus_06_dot_74 - object
        Window_bar__minus_04_dot_41_bar__plus_01_dot_70_bar__plus_01_dot_79 - object
        Window_bar__minus_04_dot_41_bar__plus_01_dot_70_bar__plus_02_dot_79 - object
        Window_bar__minus_04_dot_41_bar__plus_01_dot_70_bar__plus_05_dot_98 - object
        ArmChair_bar__minus_02_dot_63_bar_00_dot_00_bar__plus_02_dot_42 - receptacle
        CoffeeTable_bar__minus_01_dot_97_bar__plus_00_dot_00_bar__plus_04_dot_19 - receptacle
        Desk_bar__minus_00_dot_25_bar__plus_00_dot_00_bar__plus_01_dot_41 - receptacle
        DiningTable_bar__minus_01_dot_78_bar__plus_00_dot_01_bar__minus_02_dot_04 - receptacle
        Drawer_bar__plus_00_dot_41_bar__plus_00_dot_17_bar__plus_01_dot_24 - receptacle
        Drawer_bar__plus_00_dot_41_bar__plus_00_dot_37_bar__plus_01_dot_24 - receptacle
        Drawer_bar__plus_00_dot_41_bar__plus_00_dot_58_bar__plus_01_dot_24 - receptacle
        Drawer_bar_00_dot_00_bar__plus_00_dot_17_bar__plus_01_dot_24 - receptacle
        Drawer_bar_00_dot_00_bar__plus_00_dot_37_bar__plus_01_dot_24 - receptacle
        Drawer_bar_00_dot_00_bar__plus_00_dot_58_bar__plus_01_dot_24 - receptacle
        GarbageCan_bar__plus_00_dot_42_bar__plus_00_dot_00_bar__plus_02_dot_04 - receptacle
        Safe_bar__plus_00_dot_20_bar__plus_00_dot_00_bar__minus_00_dot_10 - receptacle
        Shelf_bar__minus_04_dot_25_bar__plus_01_dot_31_bar__plus_00_dot_77 - receptacle
        Shelf_bar__minus_04_dot_25_bar__plus_01_dot_69_bar__plus_00_dot_77 - receptacle
        Shelf_bar__minus_04_dot_26_bar__plus_00_dot_83_bar__plus_00_dot_77 - receptacle
        Sofa_bar__minus_02_dot_16_bar__plus_00_dot_00_bar__plus_06_dot_12 - receptacle
        Sofa_bar__minus_03_dot_81_bar__plus_00_dot_00_bar__plus_04_dot_15 - receptacle
        TVStand_bar__minus_00_dot_20_bar__minus_00_dot_01_bar__plus_05_dot_90 - receptacle
        loc_bar__minus_11_bar_16_bar_3_bar_60 - location
        loc_bar__minus_9_bar_20_bar_0_bar_60 - location
        loc_bar__minus_1_bar_12_bar_0_bar_60 - location
        loc_bar__minus_8_bar_12_bar_0_bar_60 - location
        loc_bar__minus_1_bar_20_bar_0_bar_0 - location
        loc_bar__minus_7_bar__minus_4_bar_2_bar_60 - location
        loc_bar__minus_11_bar_14_bar_2_bar_60 - location
        loc_bar__minus_15_bar_7_bar_3_bar_0 - location
        loc_bar__minus_10_bar__minus_5_bar_2_bar_60 - location
        loc_bar__minus_7_bar__minus_5_bar_1_bar_60 - location
        loc_bar__minus_3_bar_2_bar_0_bar_60 - location
        loc_bar__minus_5_bar_2_bar_2_bar_0 - location
        loc_bar__minus_11_bar__minus_4_bar_0_bar_15 - location
        loc_bar__minus_10_bar_1_bar_2_bar_60 - location
        loc_bar__minus_11_bar_4_bar_3_bar_0 - location
        loc_bar__minus_2_bar_10_bar_2_bar_60 - location
        loc_bar__minus_15_bar_10_bar_3_bar_0 - location
        loc_bar__minus_14_bar_1_bar_2_bar__minus_15 - location
        loc_bar__minus_4_bar_18_bar_1_bar_60 - location
        loc_bar_1_bar_18_bar_0_bar_60 - location
        loc_bar__minus_10_bar_1_bar_2_bar_0 - location
        loc_bar__minus_10_bar__minus_12_bar_0_bar_60 - location
        loc_bar_0_bar_11_bar_2_bar_60 - location
        loc_bar__minus_12_bar_21_bar_3_bar_60 - location
        loc_bar__minus_14_bar_3_bar_3_bar_45 - location
        loc_bar__minus_14_bar_4_bar_3_bar_30 - location
        loc_bar__minus_5_bar__minus_12_bar_0_bar_60 - location
        loc_bar__minus_12_bar_21_bar_3_bar_0 - location
        loc_bar__minus_2_bar_2_bar_1_bar_60 - location
        loc_bar_1_bar_18_bar_0_bar_45 - location
        loc_bar__minus_12_bar_21_bar_0_bar_0 - location
        loc_bar_0_bar_1_bar_0_bar_45 - location
        loc_bar__minus_3_bar_2_bar_1_bar_60 - location
        loc_bar__minus_7_bar__minus_12_bar_2_bar_15 - location
        loc_bar__minus_6_bar_3_bar_2_bar_30 - location
        )
    

(:init


        (receptacleType DiningTable_bar__minus_01_dot_78_bar__plus_00_dot_01_bar__minus_02_dot_04 DiningTableType)
        (receptacleType TVStand_bar__minus_00_dot_20_bar__minus_00_dot_01_bar__plus_05_dot_90 TVStandType)
        (receptacleType Sofa_bar__minus_03_dot_81_bar__plus_00_dot_00_bar__plus_04_dot_15 SofaType)
        (receptacleType Drawer_bar_00_dot_00_bar__plus_00_dot_58_bar__plus_01_dot_24 DrawerType)
        (receptacleType GarbageCan_bar__plus_00_dot_42_bar__plus_00_dot_00_bar__plus_02_dot_04 GarbageCanType)
        (receptacleType Shelf_bar__minus_04_dot_26_bar__plus_00_dot_83_bar__plus_00_dot_77 ShelfType)
        (receptacleType Desk_bar__minus_00_dot_25_bar__plus_00_dot_00_bar__plus_01_dot_41 DeskType)
        (receptacleType ArmChair_bar__minus_02_dot_63_bar_00_dot_00_bar__plus_02_dot_42 ArmChairType)
        (receptacleType CoffeeTable_bar__minus_01_dot_97_bar__plus_00_dot_00_bar__plus_04_dot_19 CoffeeTableType)
        (receptacleType Safe_bar__plus_00_dot_20_bar__plus_00_dot_00_bar__minus_00_dot_10 SafeType)
        (receptacleType Shelf_bar__minus_04_dot_25_bar__plus_01_dot_69_bar__plus_00_dot_77 ShelfType)
        (receptacleType Drawer_bar_00_dot_00_bar__plus_00_dot_17_bar__plus_01_dot_24 DrawerType)
        (receptacleType Drawer_bar_00_dot_00_bar__plus_00_dot_37_bar__plus_01_dot_24 DrawerType)
        (receptacleType Shelf_bar__minus_04_dot_25_bar__plus_01_dot_31_bar__plus_00_dot_77 ShelfType)
        (receptacleType Sofa_bar__minus_02_dot_16_bar__plus_00_dot_00_bar__plus_06_dot_12 SofaType)
        (receptacleType Drawer_bar__plus_00_dot_41_bar__plus_00_dot_37_bar__plus_01_dot_24 DrawerType)
        (receptacleType Drawer_bar__plus_00_dot_41_bar__plus_00_dot_58_bar__plus_01_dot_24 DrawerType)
        (receptacleType Drawer_bar__plus_00_dot_41_bar__plus_00_dot_17_bar__plus_01_dot_24 DrawerType)
        (objectType Statue_bar__minus_04_dot_22_bar__plus_01_dot_26_bar__plus_00_dot_40 StatueType)
        (objectType Vase_bar__minus_02_dot_11_bar__plus_00_dot_46_bar__plus_03_dot_77 VaseType)
        (objectType Vase_bar__minus_01_dot_56_bar__plus_00_dot_66_bar__minus_02_dot_33 VaseType)
        (objectType Window_bar__minus_01_dot_01_bar__plus_01_dot_70_bar__minus_00_dot_15 WindowType)
        (objectType Television_bar__minus_00_dot_24_bar__plus_01_dot_17_bar__plus_05_dot_82 TelevisionType)
        (objectType LightSwitch_bar__minus_02_dot_74_bar__plus_01_dot_50_bar__minus_00_dot_41 LightSwitchType)
        (objectType Statue_bar__minus_02_dot_47_bar__plus_00_dot_67_bar__minus_02_dot_46 StatueType)
        (objectType Chair_bar__minus_00_dot_37_bar__plus_00_dot_00_bar__plus_03_dot_60 ChairType)
        (objectType HousePlant_bar__minus_03_dot_94_bar__plus_00_dot_00_bar__plus_05_dot_97 HousePlantType)
        (objectType Window_bar__minus_03_dot_64_bar__plus_01_dot_70_bar__plus_06_dot_74 WindowType)
        (objectType RemoteControl_bar__minus_00_dot_11_bar__plus_00_dot_74_bar__plus_01_dot_59 RemoteControlType)
        (objectType Chair_bar__minus_01_dot_36_bar__plus_00_dot_02_bar__minus_02_dot_58 ChairType)
        (objectType Vase_bar__minus_04_dot_23_bar__plus_00_dot_79_bar__plus_00_dot_38 VaseType)
        (objectType WateringCan_bar__minus_03_dot_58_bar__plus_00_dot_00_bar__plus_05_dot_51 WateringCanType)
        (objectType Vase_bar__minus_04_dot_21_bar__plus_01_dot_66_bar__plus_00_dot_92 VaseType)
        (objectType Pillow_bar__minus_03_dot_59_bar__plus_00_dot_45_bar__plus_03_dot_35 PillowType)
        (objectType CreditCard_bar__minus_01_dot_09_bar__plus_00_dot_74_bar__plus_01_dot_20 CreditCardType)
        (objectType Window_bar__minus_04_dot_41_bar__plus_01_dot_70_bar__plus_02_dot_79 WindowType)
        (objectType Chair_bar__minus_00_dot_41_bar__plus_00_dot_00_bar__plus_04_dot_45 ChairType)
        (objectType Painting_bar__minus_03_dot_37_bar__plus_01_dot_74_bar__minus_00_dot_30 PaintingType)
        (objectType Window_bar__minus_00_dot_13_bar__plus_01_dot_70_bar__plus_06_dot_74 WindowType)
        (objectType Chair_bar__minus_02_dot_15_bar__plus_00_dot_02_bar__minus_01_dot_62 ChairType)
        (objectType FloorLamp_bar__plus_00_dot_39_bar__plus_00_dot_00_bar__plus_05_dot_16 FloorLampType)
        (objectType KeyChain_bar__minus_00_dot_14_bar__plus_00_dot_12_bar__plus_01_dot_42 KeyChainType)
        (objectType Window_bar__minus_02_dot_83_bar__plus_00_dot_47_bar__minus_00_dot_16 WindowType)
        (objectType Pillow_bar__minus_01_dot_41_bar__plus_00_dot_45_bar__plus_05_dot_92 PillowType)
        (objectType Chair_bar__minus_01_dot_38_bar__plus_00_dot_02_bar__minus_01_dot_59 ChairType)
        (objectType CellPhone_bar__minus_02_dot_91_bar__plus_00_dot_39_bar__plus_05_dot_92 CellPhoneType)
        (objectType Window_bar__minus_04_dot_41_bar__plus_01_dot_70_bar__plus_05_dot_98 WindowType)
        (objectType KeyChain_bar__minus_00_dot_63_bar__plus_00_dot_69_bar__plus_05_dot_90 KeyChainType)
        (objectType Laptop_bar__minus_02_dot_47_bar__plus_00_dot_65_bar__minus_02_dot_20 LaptopType)
        (objectType RemoteControl_bar__minus_02_dot_16_bar__plus_00_dot_39_bar__plus_06_dot_10 RemoteControlType)
        (objectType Window_bar__minus_04_dot_41_bar__plus_01_dot_70_bar__plus_01_dot_79 WindowType)
        (objectType Chair_bar__minus_02_dot_26_bar__plus_00_dot_02_bar__minus_02_dot_64 ChairType)
        (objectType CreditCard_bar__plus_00_dot_14_bar__plus_00_dot_74_bar__plus_01_dot_69 CreditCardType)
        (objectType Statue_bar__minus_01_dot_95_bar__plus_00_dot_51_bar__plus_04_dot_12 StatueType)
        (objectType Box_bar__minus_02_dot_01_bar__plus_00_dot_95_bar__minus_02_dot_46 BoxType)
        (objectType Laptop_bar__minus_03_dot_69_bar__plus_00_dot_39_bar__plus_03_dot_88 LaptopType)
        (objectType Chair_bar__minus_00_dot_64_bar__plus_00_dot_00_bar__plus_00_dot_99 ChairType)
        (objectType Painting_bar__minus_01_dot_76_bar__plus_01_dot_42_bar__minus_03_dot_60 PaintingType)
        (objectType Window_bar__minus_02_dot_83_bar__plus_01_dot_70_bar__minus_00_dot_16 WindowType)
        (canContain DiningTableType VaseType)
        (canContain DiningTableType BoxType)
        (canContain DiningTableType CellPhoneType)
        (canContain DiningTableType KeyChainType)
        (canContain DiningTableType CreditCardType)
        (canContain DiningTableType LaptopType)
        (canContain DiningTableType RemoteControlType)
        (canContain DiningTableType StatueType)
        (canContain DiningTableType WateringCanType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType CellPhoneType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType CreditCardType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType WateringCanType)
        (canContain DeskType VaseType)
        (canContain DeskType BoxType)
        (canContain DeskType CellPhoneType)
        (canContain DeskType KeyChainType)
        (canContain DeskType CreditCardType)
        (canContain DeskType LaptopType)
        (canContain DeskType RemoteControlType)
        (canContain DeskType StatueType)
        (canContain DeskType WateringCanType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType CellPhoneType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (canContain CoffeeTableType VaseType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType CellPhoneType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType StatueType)
        (canContain CoffeeTableType WateringCanType)
        (canContain SafeType CellPhoneType)
        (canContain SafeType KeyChainType)
        (canContain SafeType VaseType)
        (canContain SafeType CreditCardType)
        (canContain SafeType StatueType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType WateringCanType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType WateringCanType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType CellPhoneType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType CreditCardType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (pickupable Statue_bar__minus_04_dot_22_bar__plus_01_dot_26_bar__plus_00_dot_40)
        (pickupable Vase_bar__minus_02_dot_11_bar__plus_00_dot_46_bar__plus_03_dot_77)
        (pickupable Vase_bar__minus_01_dot_56_bar__plus_00_dot_66_bar__minus_02_dot_33)
        (pickupable Statue_bar__minus_02_dot_47_bar__plus_00_dot_67_bar__minus_02_dot_46)
        (pickupable RemoteControl_bar__minus_00_dot_11_bar__plus_00_dot_74_bar__plus_01_dot_59)
        (pickupable Vase_bar__minus_04_dot_23_bar__plus_00_dot_79_bar__plus_00_dot_38)
        (pickupable WateringCan_bar__minus_03_dot_58_bar__plus_00_dot_00_bar__plus_05_dot_51)
        (pickupable Vase_bar__minus_04_dot_21_bar__plus_01_dot_66_bar__plus_00_dot_92)
        (pickupable Pillow_bar__minus_03_dot_59_bar__plus_00_dot_45_bar__plus_03_dot_35)
        (pickupable CreditCard_bar__minus_01_dot_09_bar__plus_00_dot_74_bar__plus_01_dot_20)
        (pickupable KeyChain_bar__minus_00_dot_14_bar__plus_00_dot_12_bar__plus_01_dot_42)
        (pickupable Pillow_bar__minus_01_dot_41_bar__plus_00_dot_45_bar__plus_05_dot_92)
        (pickupable CellPhone_bar__minus_02_dot_91_bar__plus_00_dot_39_bar__plus_05_dot_92)
        (pickupable KeyChain_bar__minus_00_dot_63_bar__plus_00_dot_69_bar__plus_05_dot_90)
        (pickupable Laptop_bar__minus_02_dot_47_bar__plus_00_dot_65_bar__minus_02_dot_20)
        (pickupable RemoteControl_bar__minus_02_dot_16_bar__plus_00_dot_39_bar__plus_06_dot_10)
        (pickupable CreditCard_bar__plus_00_dot_14_bar__plus_00_dot_74_bar__plus_01_dot_69)
        (pickupable Statue_bar__minus_01_dot_95_bar__plus_00_dot_51_bar__plus_04_dot_12)
        (pickupable Box_bar__minus_02_dot_01_bar__plus_00_dot_95_bar__minus_02_dot_46)
        (pickupable Laptop_bar__minus_03_dot_69_bar__plus_00_dot_39_bar__plus_03_dot_88)
        (isReceptacleObject Box_bar__minus_02_dot_01_bar__plus_00_dot_95_bar__minus_02_dot_46)
        (openable Drawer_bar_00_dot_00_bar__plus_00_dot_58_bar__plus_01_dot_24)
        (openable Safe_bar__plus_00_dot_20_bar__plus_00_dot_00_bar__minus_00_dot_10)
        (openable Drawer_bar_00_dot_00_bar__plus_00_dot_17_bar__plus_01_dot_24)
        (openable Drawer_bar_00_dot_00_bar__plus_00_dot_37_bar__plus_01_dot_24)
        (openable Drawer_bar__plus_00_dot_41_bar__plus_00_dot_37_bar__plus_01_dot_24)
        (openable Drawer_bar__plus_00_dot_41_bar__plus_00_dot_58_bar__plus_01_dot_24)
        (openable Drawer_bar__plus_00_dot_41_bar__plus_00_dot_17_bar__plus_01_dot_24)
        
        (atLocation agent1 loc_bar__minus_6_bar_3_bar_2_bar_30)
        
        
        
        
        
        
        
        (toggleable FloorLamp_bar__plus_00_dot_39_bar__plus_00_dot_00_bar__plus_05_dot_16)
        
        
        
        
        (inReceptacle CellPhone_bar__minus_02_dot_91_bar__plus_00_dot_39_bar__plus_05_dot_92 Sofa_bar__minus_02_dot_16_bar__plus_00_dot_00_bar__plus_06_dot_12)
        (inReceptacle RemoteControl_bar__minus_02_dot_16_bar__plus_00_dot_39_bar__plus_06_dot_10 Sofa_bar__minus_02_dot_16_bar__plus_00_dot_00_bar__plus_06_dot_12)
        (inReceptacle Pillow_bar__minus_01_dot_41_bar__plus_00_dot_45_bar__plus_05_dot_92 Sofa_bar__minus_02_dot_16_bar__plus_00_dot_00_bar__plus_06_dot_12)
        (inReceptacle Statue_bar__minus_04_dot_22_bar__plus_01_dot_26_bar__plus_00_dot_40 Shelf_bar__minus_04_dot_25_bar__plus_01_dot_31_bar__plus_00_dot_77)
        (inReceptacle Vase_bar__minus_02_dot_11_bar__plus_00_dot_46_bar__plus_03_dot_77 CoffeeTable_bar__minus_01_dot_97_bar__plus_00_dot_00_bar__plus_04_dot_19)
        (inReceptacle Statue_bar__minus_01_dot_95_bar__plus_00_dot_51_bar__plus_04_dot_12 CoffeeTable_bar__minus_01_dot_97_bar__plus_00_dot_00_bar__plus_04_dot_19)
        (inReceptacle Statue_bar__minus_02_dot_47_bar__plus_00_dot_67_bar__minus_02_dot_46 DiningTable_bar__minus_01_dot_78_bar__plus_00_dot_01_bar__minus_02_dot_04)
        (inReceptacle Box_bar__minus_02_dot_01_bar__plus_00_dot_95_bar__minus_02_dot_46 DiningTable_bar__minus_01_dot_78_bar__plus_00_dot_01_bar__minus_02_dot_04)
        (inReceptacle Vase_bar__minus_01_dot_56_bar__plus_00_dot_66_bar__minus_02_dot_33 DiningTable_bar__minus_01_dot_78_bar__plus_00_dot_01_bar__minus_02_dot_04)
        (inReceptacle Laptop_bar__minus_02_dot_47_bar__plus_00_dot_65_bar__minus_02_dot_20 DiningTable_bar__minus_01_dot_78_bar__plus_00_dot_01_bar__minus_02_dot_04)
        (inReceptacle KeyChain_bar__minus_00_dot_14_bar__plus_00_dot_12_bar__plus_01_dot_42 Drawer_bar_00_dot_00_bar__plus_00_dot_17_bar__plus_01_dot_24)
        (inReceptacle Vase_bar__minus_04_dot_21_bar__plus_01_dot_66_bar__plus_00_dot_92 Shelf_bar__minus_04_dot_25_bar__plus_01_dot_69_bar__plus_00_dot_77)
        (inReceptacle CreditCard_bar__minus_01_dot_09_bar__plus_00_dot_74_bar__plus_01_dot_20 Desk_bar__minus_00_dot_25_bar__plus_00_dot_00_bar__plus_01_dot_41)
        (inReceptacle CreditCard_bar__plus_00_dot_14_bar__plus_00_dot_74_bar__plus_01_dot_69 Desk_bar__minus_00_dot_25_bar__plus_00_dot_00_bar__plus_01_dot_41)
        (inReceptacle RemoteControl_bar__minus_00_dot_11_bar__plus_00_dot_74_bar__plus_01_dot_59 Desk_bar__minus_00_dot_25_bar__plus_00_dot_00_bar__plus_01_dot_41)
        (inReceptacle KeyChain_bar__minus_00_dot_63_bar__plus_00_dot_69_bar__plus_05_dot_90 TVStand_bar__minus_00_dot_20_bar__minus_00_dot_01_bar__plus_05_dot_90)
        (inReceptacle Television_bar__minus_00_dot_24_bar__plus_01_dot_17_bar__plus_05_dot_82 TVStand_bar__minus_00_dot_20_bar__minus_00_dot_01_bar__plus_05_dot_90)
        (inReceptacle Laptop_bar__minus_03_dot_69_bar__plus_00_dot_39_bar__plus_03_dot_88 Sofa_bar__minus_03_dot_81_bar__plus_00_dot_00_bar__plus_04_dot_15)
        (inReceptacle Pillow_bar__minus_03_dot_59_bar__plus_00_dot_45_bar__plus_03_dot_35 Sofa_bar__minus_03_dot_81_bar__plus_00_dot_00_bar__plus_04_dot_15)
        (inReceptacle Vase_bar__minus_04_dot_23_bar__plus_00_dot_79_bar__plus_00_dot_38 Shelf_bar__minus_04_dot_26_bar__plus_00_dot_83_bar__plus_00_dot_77)
        
        
        (receptacleAtLocation ArmChair_bar__minus_02_dot_63_bar_00_dot_00_bar__plus_02_dot_42 loc_bar__minus_11_bar_14_bar_2_bar_60)
        (receptacleAtLocation CoffeeTable_bar__minus_01_dot_97_bar__plus_00_dot_00_bar__plus_04_dot_19 loc_bar__minus_8_bar_12_bar_0_bar_60)
        (receptacleAtLocation Desk_bar__minus_00_dot_25_bar__plus_00_dot_00_bar__plus_01_dot_41 loc_bar__minus_2_bar_10_bar_2_bar_60)
        (receptacleAtLocation DiningTable_bar__minus_01_dot_78_bar__plus_00_dot_01_bar__minus_02_dot_04 loc_bar__minus_7_bar__minus_4_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_41_bar__plus_00_dot_17_bar__plus_01_dot_24 loc_bar__minus_2_bar_2_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_41_bar__plus_00_dot_37_bar__plus_01_dot_24 loc_bar__minus_2_bar_2_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_41_bar__plus_00_dot_58_bar__plus_01_dot_24 loc_bar_0_bar_1_bar_0_bar_45)
        (receptacleAtLocation Drawer_bar_00_dot_00_bar__plus_00_dot_17_bar__plus_01_dot_24 loc_bar__minus_3_bar_2_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar_00_dot_00_bar__plus_00_dot_37_bar__plus_01_dot_24 loc_bar__minus_3_bar_2_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar_00_dot_00_bar__plus_00_dot_58_bar__plus_01_dot_24 loc_bar__minus_3_bar_2_bar_1_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_00_dot_42_bar__plus_00_dot_00_bar__plus_02_dot_04 loc_bar_0_bar_11_bar_2_bar_60)
        (receptacleAtLocation Safe_bar__plus_00_dot_20_bar__plus_00_dot_00_bar__minus_00_dot_10 loc_bar__minus_3_bar_2_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__minus_04_dot_25_bar__plus_01_dot_31_bar__plus_00_dot_77 loc_bar__minus_14_bar_4_bar_3_bar_30)
        (receptacleAtLocation Shelf_bar__minus_04_dot_25_bar__plus_01_dot_69_bar__plus_00_dot_77 loc_bar__minus_11_bar_4_bar_3_bar_0)
        (receptacleAtLocation Shelf_bar__minus_04_dot_26_bar__plus_00_dot_83_bar__plus_00_dot_77 loc_bar__minus_14_bar_3_bar_3_bar_45)
        (receptacleAtLocation Sofa_bar__minus_02_dot_16_bar__plus_00_dot_00_bar__plus_06_dot_12 loc_bar__minus_9_bar_20_bar_0_bar_60)
        (receptacleAtLocation Sofa_bar__minus_03_dot_81_bar__plus_00_dot_00_bar__plus_04_dot_15 loc_bar__minus_11_bar_16_bar_3_bar_60)
        (receptacleAtLocation TVStand_bar__minus_00_dot_20_bar__minus_00_dot_01_bar__plus_05_dot_90 loc_bar_1_bar_18_bar_0_bar_45)
        (objectAtLocation CreditCard_bar__minus_01_dot_09_bar__plus_00_dot_74_bar__plus_01_dot_20 loc_bar__minus_2_bar_10_bar_2_bar_60)
        (objectAtLocation RemoteControl_bar__minus_00_dot_11_bar__plus_00_dot_74_bar__plus_01_dot_59 loc_bar__minus_2_bar_10_bar_2_bar_60)
        (objectAtLocation Pillow_bar__minus_03_dot_59_bar__plus_00_dot_45_bar__plus_03_dot_35 loc_bar__minus_11_bar_16_bar_3_bar_60)
        (objectAtLocation Laptop_bar__minus_03_dot_69_bar__plus_00_dot_39_bar__plus_03_dot_88 loc_bar__minus_11_bar_16_bar_3_bar_60)
        (objectAtLocation KeyChain_bar__minus_00_dot_63_bar__plus_00_dot_69_bar__plus_05_dot_90 loc_bar_1_bar_18_bar_0_bar_45)
        (objectAtLocation Box_bar__minus_02_dot_01_bar__plus_00_dot_95_bar__minus_02_dot_46 loc_bar__minus_7_bar__minus_4_bar_2_bar_60)
        (objectAtLocation Chair_bar__minus_00_dot_41_bar__plus_00_dot_00_bar__plus_04_dot_45 loc_bar__minus_4_bar_18_bar_1_bar_60)
        (objectAtLocation Chair_bar__minus_01_dot_36_bar__plus_00_dot_02_bar__minus_02_dot_58 loc_bar__minus_5_bar__minus_12_bar_0_bar_60)
        (objectAtLocation Painting_bar__minus_03_dot_37_bar__plus_01_dot_74_bar__minus_00_dot_30 loc_bar__minus_14_bar_1_bar_2_bar__minus_15)
        (objectAtLocation Chair_bar__minus_00_dot_64_bar__plus_00_dot_00_bar__plus_00_dot_99 loc_bar__minus_3_bar_2_bar_0_bar_60)
        (objectAtLocation Painting_bar__minus_01_dot_76_bar__plus_01_dot_42_bar__minus_03_dot_60 loc_bar__minus_7_bar__minus_12_bar_2_bar_15)
        (objectAtLocation Chair_bar__minus_01_dot_38_bar__plus_00_dot_02_bar__minus_01_dot_59 loc_bar__minus_7_bar__minus_5_bar_1_bar_60)
        (objectAtLocation Chair_bar__minus_00_dot_37_bar__plus_00_dot_00_bar__plus_03_dot_60 loc_bar__minus_1_bar_12_bar_0_bar_60)
        (objectAtLocation Chair_bar__minus_02_dot_26_bar__plus_00_dot_02_bar__minus_02_dot_64 loc_bar__minus_10_bar__minus_12_bar_0_bar_60)
        (objectAtLocation Chair_bar__minus_02_dot_15_bar__plus_00_dot_02_bar__minus_01_dot_62 loc_bar__minus_10_bar__minus_5_bar_2_bar_60)
        (objectAtLocation FloorLamp_bar__plus_00_dot_39_bar__plus_00_dot_00_bar__plus_05_dot_16 loc_bar_1_bar_18_bar_0_bar_60)
        (objectAtLocation Vase_bar__minus_02_dot_11_bar__plus_00_dot_46_bar__plus_03_dot_77 loc_bar__minus_8_bar_12_bar_0_bar_60)
        (objectAtLocation CellPhone_bar__minus_02_dot_91_bar__plus_00_dot_39_bar__plus_05_dot_92 loc_bar__minus_9_bar_20_bar_0_bar_60)
        (objectAtLocation WateringCan_bar__minus_03_dot_58_bar__plus_00_dot_00_bar__plus_05_dot_51 loc_bar__minus_12_bar_21_bar_3_bar_60)
        (objectAtLocation KeyChain_bar__minus_00_dot_14_bar__plus_00_dot_12_bar__plus_01_dot_42 loc_bar__minus_3_bar_2_bar_1_bar_60)
        (objectAtLocation Vase_bar__minus_04_dot_21_bar__plus_01_dot_66_bar__plus_00_dot_92 loc_bar__minus_11_bar_4_bar_3_bar_0)
        (objectAtLocation Vase_bar__minus_01_dot_56_bar__plus_00_dot_66_bar__minus_02_dot_33 loc_bar__minus_7_bar__minus_4_bar_2_bar_60)
        (objectAtLocation Laptop_bar__minus_02_dot_47_bar__plus_00_dot_65_bar__minus_02_dot_20 loc_bar__minus_7_bar__minus_4_bar_2_bar_60)
        (objectAtLocation Window_bar__minus_02_dot_83_bar__plus_00_dot_47_bar__minus_00_dot_16 loc_bar__minus_10_bar_1_bar_2_bar_60)
        (objectAtLocation Window_bar__minus_04_dot_41_bar__plus_01_dot_70_bar__plus_01_dot_79 loc_bar__minus_15_bar_7_bar_3_bar_0)
        (objectAtLocation Window_bar__minus_03_dot_64_bar__plus_01_dot_70_bar__plus_06_dot_74 loc_bar__minus_12_bar_21_bar_0_bar_0)
        (objectAtLocation Window_bar__minus_04_dot_41_bar__plus_01_dot_70_bar__plus_02_dot_79 loc_bar__minus_15_bar_10_bar_3_bar_0)
        (objectAtLocation Window_bar__minus_02_dot_83_bar__plus_01_dot_70_bar__minus_00_dot_16 loc_bar__minus_10_bar_1_bar_2_bar_0)
        (objectAtLocation Window_bar__minus_00_dot_13_bar__plus_01_dot_70_bar__plus_06_dot_74 loc_bar__minus_1_bar_20_bar_0_bar_0)
        (objectAtLocation Window_bar__minus_01_dot_01_bar__plus_01_dot_70_bar__minus_00_dot_15 loc_bar__minus_5_bar_2_bar_2_bar_0)
        (objectAtLocation Window_bar__minus_04_dot_41_bar__plus_01_dot_70_bar__plus_05_dot_98 loc_bar__minus_12_bar_21_bar_3_bar_0)
        (objectAtLocation Pillow_bar__minus_01_dot_41_bar__plus_00_dot_45_bar__plus_05_dot_92 loc_bar__minus_9_bar_20_bar_0_bar_60)
        (objectAtLocation Television_bar__minus_00_dot_24_bar__plus_01_dot_17_bar__plus_05_dot_82 loc_bar_1_bar_18_bar_0_bar_45)
        (objectAtLocation RemoteControl_bar__minus_02_dot_16_bar__plus_00_dot_39_bar__plus_06_dot_10 loc_bar__minus_9_bar_20_bar_0_bar_60)
        (objectAtLocation HousePlant_bar__minus_03_dot_94_bar__plus_00_dot_00_bar__plus_05_dot_97 loc_bar__minus_12_bar_21_bar_3_bar_60)
        (objectAtLocation CreditCard_bar__plus_00_dot_14_bar__plus_00_dot_74_bar__plus_01_dot_69 loc_bar__minus_2_bar_10_bar_2_bar_60)
        (objectAtLocation Vase_bar__minus_04_dot_23_bar__plus_00_dot_79_bar__plus_00_dot_38 loc_bar__minus_14_bar_3_bar_3_bar_45)
        (objectAtLocation LightSwitch_bar__minus_02_dot_74_bar__plus_01_dot_50_bar__minus_00_dot_41 loc_bar__minus_11_bar__minus_4_bar_0_bar_15)
        (objectAtLocation Statue_bar__minus_04_dot_22_bar__plus_01_dot_26_bar__plus_00_dot_40 loc_bar__minus_14_bar_4_bar_3_bar_30)
        (objectAtLocation Statue_bar__minus_02_dot_47_bar__plus_00_dot_67_bar__minus_02_dot_46 loc_bar__minus_7_bar__minus_4_bar_2_bar_60)
        (objectAtLocation Statue_bar__minus_01_dot_95_bar__plus_00_dot_51_bar__plus_04_dot_12 loc_bar__minus_8_bar_12_bar_0_bar_60)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 VaseType)
                                    (receptacleType ?r DeskType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 VaseType)
                                            (receptacleType ?r DeskType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            