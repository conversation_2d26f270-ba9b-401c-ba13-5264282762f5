{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000267.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000268.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000269.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000270.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000271.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000272.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000273.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000274.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000275.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000276.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000277.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000278.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000279.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000280.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000281.png", "low_idx": 46}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON>g", "parent_target": "CoffeeMachine", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|4|9|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["mug"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON>g", [6.11599876, 6.11599876, 9.38240148, 9.38240148, 4.01194428, 4.01194428]], "coordinateReceptacleObjectId": ["CounterTop", [6.02, 6.02, 9.24, 9.24, 3.8936, 3.8936]], "forceVisible": true, "objectId": "Mug|+01.53|+01.00|+02.35"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|6|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["mug"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}}, {"discrete_action": {"action": "GotoLocation", "args": ["coffeemachine"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|4|10|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["mug", "coffeemachine"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON>g", [6.11599876, 6.11599876, 9.38240148, 9.38240148, 4.01194428, 4.01194428]], "coordinateReceptacleObjectId": ["CoffeeMachine", [6.768, 6.768, 9.816, 9.816, 3.706744196, 3.706744196]], "forceVisible": true, "objectId": "Mug|+01.53|+01.00|+02.35", "receptacleObjectId": "CoffeeMachine|+01.69|+00.93|+02.45"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Mug|+01.53|+01.00|+02.35"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [98, 125, 138, 170], "mask": [[37314, 4], [37609, 14], [37906, 20], [38204, 23], [38503, 25], [38802, 27], [39101, 29], [39400, 31], [39699, 32], [39999, 32], [40299, 32], [40599, 36], [40899, 38], [41198, 40], [41499, 32], [41534, 5], [41799, 32], [41836, 3], [42099, 32], [42136, 3], [42400, 31], [42435, 4], [42700, 31], [42735, 3], [43000, 31], [43035, 3], [43301, 30], [43334, 3], [43601, 30], [43634, 3], [43901, 30], [43933, 4], [44201, 30], [44233, 3], [44502, 29], [44532, 4], [44802, 29], [44832, 3], [45102, 33], [45402, 32], [45702, 32], [46003, 30], [46303, 29], [46603, 28], [46903, 28], [47204, 27], [47504, 27], [47804, 27], [48104, 27], [48404, 27], [48705, 26], [49005, 25], [49305, 25], [49606, 23], [49907, 21], [50208, 18], [50510, 15], [50812, 10]], "point": [118, 146]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|+01.53|+01.00|+02.35", "placeStationary": true, "receptacleObjectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[50, 211], [350, 211], [650, 211], [950, 210], [1250, 210], [1550, 210], [1850, 210], [2150, 210], [2450, 210], [2750, 210], [3050, 210], [3350, 210], [3650, 210], [3950, 209], [4250, 209], [4550, 209], [4800, 1], [4850, 209], [5100, 1], [5150, 209], [5400, 1], [5450, 209], [5700, 1], [5750, 209], [6000, 2], [6050, 209], [6300, 2], [6350, 209], [6600, 2], [6650, 209], [6900, 2], [6950, 208], [7200, 3], [7250, 208], [7500, 3], [7550, 208], [7800, 3], [7850, 208], [8100, 3], [8150, 208], [8400, 4], [8450, 208], [8700, 4], [8750, 208], [9000, 4], [9050, 208], [9300, 4], [9350, 208], [9600, 5], [9650, 208], [9900, 5], [9950, 207], [10200, 5], [10250, 207], [10500, 5], [10550, 207], [10800, 5], [10850, 207], [11100, 6], [11150, 207], [11400, 6], [11450, 207], [11700, 6], [11750, 207], [12000, 6], [12050, 207], [12300, 7], [12350, 207], [12600, 7], [12650, 207], [12900, 7], [12950, 206], [13200, 7], [13250, 206], [13500, 8], [13550, 206], [13800, 8], [13850, 206], [14100, 8], [14150, 206], [14400, 8], [14450, 206], [14700, 9], [14750, 206], [15000, 9], [15050, 206], [15300, 9], [15350, 206], [15600, 9], [15650, 206], [15900, 10], [15950, 205], [16200, 10], [16250, 205], [16500, 10], [16549, 206], [16800, 10], [16849, 206], [17100, 11], [17149, 206], [17400, 11], [17449, 206], [17700, 11], [17749, 206], [18000, 11], [18049, 206], [18300, 11], [18349, 206], [18600, 12], [18649, 206], [18900, 12], [18949, 205], [19200, 12], [19249, 205], [19500, 12], [19549, 205], [19800, 13], [19849, 205], [20100, 13], [20149, 205], [20400, 13], [20449, 205], [20700, 13], [20749, 205], [21000, 14], [21049, 205], [21300, 21], [21348, 206], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[50, 211], [350, 211], [650, 211], [950, 210], [1250, 210], [1550, 210], [1850, 210], [2150, 210], [2450, 210], [2750, 210], [3050, 210], [3350, 210], [3650, 210], [3950, 209], [4250, 209], [4550, 209], [4800, 1], [4850, 39], [4908, 151], [5100, 1], [5150, 33], [5215, 144], [5400, 1], [5450, 31], [5519, 140], [5700, 1], [5750, 30], [5822, 137], [6000, 2], [6050, 30], [6123, 136], [6300, 2], [6350, 30], [6424, 135], [6600, 2], [6650, 30], [6724, 135], [6900, 2], [6950, 30], [7028, 130], [7200, 3], [7250, 30], [7330, 128], [7500, 3], [7550, 31], [7631, 127], [7800, 3], [7850, 31], [7923, 4], [7932, 126], [8100, 3], [8150, 31], [8223, 5], [8233, 125], [8400, 4], [8450, 31], [8523, 6], [8533, 125], [8700, 4], [8750, 31], [8823, 7], [8833, 125], [9000, 4], [9050, 31], [9123, 7], [9134, 124], [9300, 4], [9350, 32], [9423, 7], [9434, 124], [9600, 5], [9650, 32], [9723, 6], [9733, 125], [9900, 5], [9950, 32], [10023, 6], [10033, 124], [10200, 5], [10250, 32], [10323, 6], [10333, 124], [10500, 5], [10550, 32], [10623, 6], [10633, 124], [10800, 5], [10850, 32], [10923, 5], [10933, 124], [11100, 6], [11150, 32], [11223, 5], [11232, 125], [11400, 6], [11450, 32], [11523, 5], [11532, 125], [11700, 6], [11750, 32], [11823, 4], [11832, 125], [12000, 6], [12050, 33], [12123, 4], [12131, 126], [12300, 7], [12350, 33], [12423, 4], [12431, 126], [12600, 7], [12650, 33], [12723, 4], [12731, 126], [12900, 7], [12950, 33], [13023, 3], [13030, 126], [13200, 7], [13250, 33], [13323, 3], [13330, 126], [13500, 8], [13550, 33], [13623, 3], [13630, 126], [13800, 8], [13850, 33], [13923, 2], [13930, 126], [14100, 8], [14150, 33], [14223, 2], [14229, 127], [14400, 8], [14450, 33], [14523, 2], [14529, 127], [14700, 9], [14750, 33], [14823, 1], [14829, 127], [15000, 9], [15050, 33], [15123, 1], [15128, 128], [15300, 9], [15350, 34], [15428, 128], [15600, 9], [15650, 34], [15728, 128], [15900, 10], [15950, 34], [16027, 128], [16200, 10], [16250, 34], [16327, 128], [16500, 10], [16549, 35], [16626, 129], [16800, 10], [16849, 35], [16926, 129], [17100, 11], [17149, 35], [17225, 130], [17400, 11], [17449, 35], [17525, 130], [17700, 11], [17749, 35], [17823, 132], [18000, 11], [18049, 35], [18123, 132], [18300, 11], [18349, 35], [18423, 132], [18600, 12], [18649, 35], [18723, 132], [18900, 12], [18949, 36], [19023, 131], [19200, 12], [19249, 36], [19323, 131], [19500, 12], [19549, 36], [19623, 131], [19800, 13], [19849, 36], [19923, 131], [20100, 13], [20149, 36], [20223, 131], [20400, 13], [20449, 36], [20523, 131], [20700, 13], [20749, 36], [20823, 131], [21000, 14], [21049, 36], [21123, 131], [21300, 21], [21348, 37], [21423, 131], [21600, 86], [21722, 132], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Mug|+01.53|+01.00|+02.35"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [80, 17, 133, 73], "mask": [[4889, 19], [5183, 32], [5481, 38], [5780, 42], [6080, 43], [6380, 44], [6680, 44], [6980, 48], [7280, 50], [7581, 50], [7881, 42], [7927, 5], [8181, 42], [8228, 5], [8481, 42], [8529, 4], [8781, 42], [8830, 3], [9081, 42], [9130, 4], [9382, 41], [9430, 4], [9682, 41], [9729, 4], [9982, 41], [10029, 4], [10282, 41], [10329, 4], [10582, 41], [10629, 4], [10882, 41], [10928, 5], [11182, 41], [11228, 4], [11482, 41], [11528, 4], [11782, 41], [11827, 5], [12083, 40], [12127, 4], [12383, 40], [12427, 4], [12683, 40], [12727, 4], [12983, 40], [13026, 4], [13283, 40], [13326, 4], [13583, 40], [13626, 4], [13883, 40], [13925, 5], [14183, 40], [14225, 4], [14483, 40], [14525, 4], [14783, 40], [14824, 5], [15083, 40], [15124, 4], [15384, 44], [15684, 44], [15984, 43], [16284, 43], [16584, 42], [16884, 42], [17184, 41], [17484, 41], [17784, 39], [18084, 39], [18384, 39], [18684, 39], [18985, 38], [19285, 38], [19585, 38], [19885, 38], [20185, 38], [20485, 38], [20785, 38], [21085, 38], [21385, 38], [21686, 36]], "point": [106, 44]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[50, 211], [350, 211], [650, 211], [950, 210], [1250, 210], [1550, 210], [1850, 210], [2150, 210], [2450, 210], [2750, 210], [3050, 210], [3350, 210], [3650, 210], [3950, 209], [4250, 209], [4550, 209], [4800, 1], [4850, 209], [5100, 1], [5150, 209], [5400, 1], [5450, 209], [5700, 1], [5750, 209], [6000, 2], [6050, 209], [6300, 2], [6350, 209], [6600, 2], [6650, 209], [6900, 2], [6950, 208], [7200, 3], [7250, 208], [7500, 3], [7550, 208], [7800, 3], [7850, 208], [8100, 3], [8150, 208], [8400, 4], [8450, 208], [8700, 4], [8750, 208], [9000, 4], [9050, 208], [9300, 4], [9350, 208], [9600, 5], [9650, 208], [9900, 5], [9950, 207], [10200, 5], [10250, 207], [10500, 5], [10550, 207], [10800, 5], [10850, 207], [11100, 6], [11150, 207], [11400, 6], [11450, 207], [11700, 6], [11750, 207], [12000, 6], [12050, 207], [12300, 7], [12350, 207], [12600, 7], [12650, 207], [12900, 7], [12950, 206], [13200, 7], [13250, 206], [13500, 8], [13550, 206], [13800, 8], [13850, 206], [14100, 8], [14150, 206], [14400, 8], [14450, 206], [14700, 9], [14750, 206], [15000, 9], [15050, 206], [15300, 9], [15350, 206], [15600, 9], [15650, 206], [15900, 10], [15950, 205], [16200, 10], [16250, 205], [16500, 10], [16549, 206], [16800, 10], [16849, 206], [17100, 11], [17149, 206], [17400, 11], [17449, 206], [17700, 11], [17749, 206], [18000, 11], [18049, 206], [18300, 11], [18349, 206], [18600, 12], [18649, 206], [18900, 12], [18949, 205], [19200, 12], [19249, 205], [19500, 12], [19549, 205], [19800, 13], [19849, 205], [20100, 13], [20149, 205], [20400, 13], [20449, 205], [20700, 13], [20749, 205], [21000, 14], [21049, 205], [21300, 21], [21348, 206], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|+01.53|+01.00|+02.35", "placeStationary": true, "receptacleObjectId": "CoffeeMachine|+01.69|+00.93|+02.45"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [92, 8, 271, 205], "mask": [[2200, 70], [2499, 72], [2799, 72], [3098, 73], [3398, 73], [3697, 138], [3997, 140], [4296, 141], [4596, 142], [4892, 147], [5192, 147], [5492, 148], [5792, 149], [6092, 150], [6392, 150], [6692, 151], [6992, 152], [7292, 152], [7592, 153], [7892, 154], [8192, 154], [8492, 155], [8792, 156], [9092, 157], [9392, 157], [9692, 158], [9992, 159], [10292, 159], [10592, 160], [10892, 161], [11192, 162], [11492, 162], [11792, 163], [12092, 164], [12392, 164], [12692, 165], [12992, 166], [13292, 166], [13592, 167], [13892, 168], [14192, 169], [14492, 169], [14792, 170], [15092, 171], [15392, 171], [15692, 172], [15992, 173], [16292, 174], [16592, 174], [16892, 175], [17192, 176], [17492, 176], [17792, 177], [18093, 177], [18393, 178], [18693, 178], [18993, 178], [19293, 178], [19593, 179], [19893, 179], [20193, 179], [20493, 179], [20793, 179], [21093, 178], [21393, 178], [21693, 178], [21993, 178], [22293, 177], [22593, 177], [22893, 177], [23193, 176], [23493, 176], [23793, 176], [24093, 175], [24393, 175], [24693, 174], [24993, 174], [25293, 174], [25593, 173], [25893, 173], [26194, 172], [26494, 171], [26794, 171], [27094, 171], [27394, 170], [27694, 170], [27994, 170], [28294, 169], [28594, 169], [28894, 168], [29194, 168], [29494, 168], [29794, 167], [30094, 166], [30394, 165], [30695, 163], [30995, 162], [31296, 159], [31595, 146], [31746, 4], [31895, 145], [32195, 145], [32495, 145], [32795, 145], [33095, 144], [33395, 144], [33696, 142], [33996, 140], [34296, 140], [34596, 140], [34896, 139], [35196, 139], [35496, 139], [35796, 138], [36097, 137], [36397, 137], [36697, 137], [36997, 137], [37297, 136], [37597, 136], [37897, 136], [38198, 135], [38498, 135], [38798, 134], [39098, 134], [39398, 134], [39698, 134], [39998, 134], [40299, 133], [40599, 133], [40898, 133], [41198, 133], [41498, 133], [41798, 133], [42098, 134], [42397, 135], [42697, 135], [42997, 136], [43297, 136], [43597, 136], [43896, 138], [44196, 138], [44496, 138], [44796, 139], [45096, 139], [45395, 140], [45695, 141], [45995, 141], [46295, 141], [46594, 143], [46894, 143], [47194, 143], [47494, 144], [47794, 144], [48093, 145], [48393, 146], [48693, 146], [48993, 146], [49294, 146], [49594, 146], [49894, 146], [50195, 146], [50496, 145], [50797, 144], [51097, 145], [51397, 145], [51697, 145], [51998, 145], [52300, 143], [52602, 141], [52903, 141], [53205, 139], [53507, 137], [53809, 136], [54110, 135], [54412, 133], [54714, 30], [54756, 90], [55016, 21], [55063, 83], [55317, 15], [55368, 78], [55619, 10], [55671, 76], [55921, 6], [55973, 74], [56224, 1], [56275, 72], [56577, 70], [56879, 69], [57180, 68], [57481, 67], [57781, 68], [58082, 67], [58383, 66], [58683, 67], [58983, 67], [59283, 67], [59583, 67], [59884, 65], [60193, 56], [60509, 39], [60835, 8], [61136, 6], [61436, 6]], "point": [181, 105]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan12", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -0.25, "y": 0.9009999, "z": -1.0}, "object_poses": [{"objectName": "Potato_22312ae0", "position": {"x": -0.8990397, "y": 0.598476648, "z": 0.2723028}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": -1.04122186, "y": 1.50067186, "z": 2.27596354}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": -1.12788, "y": 0.9278544, "z": 2.234908}, "rotation": {"x": 0.000153027053, "y": -4.79038754e-05, "z": -0.000163779492}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": -0.876905, "y": 0.9379421, "z": 1.15406442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_d40bfead", "position": {"x": -0.96003, "y": 0.938448548, "z": 0.921}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_d40bfead", "position": {"x": -0.7937801, "y": 0.938448548, "z": 0.765623748}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_733b0828", "position": {"x": 1.275738, "y": 0.761567056, "z": 1.05253744}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -1.109, "y": 0.9307289, "z": 1.8657}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": 1.38527226, "y": 1.07658637, "z": 0.376425028}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": 1.38253057, "y": 0.105463147, "z": 0.171664327}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": -0.827398658, "y": 0.7688297, "z": 1.05093741}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -1.209405, "y": 0.999118, "z": 1.15406442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": -1.209405, "y": 0.9342062, "z": 0.921}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": 1.52899969, "y": 1.00298607, "z": 2.34560037}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Lettuce_f3700d17", "position": {"x": -1.043155, "y": 1.02830076, "z": 1.0763762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_09f78d94", "position": {"x": -0.868187, "y": 0.08357638, "z": 0.9856334}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_20fd7fed", "position": {"x": -0.9233061, "y": 0.9907504, "z": 2.26470733}, "rotation": {"x": 0.0214600712, "y": 302.404938, "z": 359.9899}}, {"objectName": "Fork_d40bfead", "position": {"x": -1.04315484, "y": 0.938448548, "z": 0.8433119}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -1.12628, "y": 0.999118, "z": 0.765623748}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": -1.01830947, "y": 1.31560028, "z": 0.195651144}, "rotation": {"x": 0.0, "y": 269.999817, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -1.109, "y": 0.9307289, "z": 1.4704}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_733b0828", "position": {"x": -0.7236026, "y": 0.7618369, "z": 0.914999962}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": -0.723602653, "y": 0.7688297, "z": 2.24516249}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": -0.7236026, "y": 0.7431432, "z": 1.11890614}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": -0.877188742, "y": 1.36844981, "z": 0.348954171}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": 1.45963871, "y": 1.74306309, "z": 0.1684567}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": -1.12628, "y": 0.9379421, "z": 1.15406442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_22312ae0", "position": {"x": -0.877187848, "y": 1.35187662, "z": 0.0423489}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": 1.38017225, "y": 1.75122666, "z": -0.03568393}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": 1.40962565, "y": 0.9442617, "z": 1.67315912}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": 1.5506295, "y": 0.9798608, "z": 1.673159}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": 1.58757126, "y": 1.59505093, "z": 1.75620937}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Pan_9f7abbea", "position": {"x": 1.3781, "y": 1.73753989, "z": 0.3929}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": 1.4564538, "y": 0.8445722, "z": 1.3697021}, "rotation": {"x": 1.40334191e-14, "y": 180.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": -0.712255, "y": 0.9329, "z": 2.159162}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3513651649, "scene_num": 12}, "task_id": "trial_T20190907_034213_294818", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1GVTH5YS3WOK0_3I7DHKZYGQHFBET3OTVK3TGK14Q5FS", "high_descs": ["Turn around and walk towards the coffee maker on the left counter.", "Pick up the black cup on the coffee maker.", "Take a step to the right and find the microwave above the sink.", "Place the cup in the microwave, heat it up, and take it back out.", "Take a step towards the coffee maker to your left.", "Place the cup back on the coffee maker."], "task_desc": "Heat up the cup on the coffee maker.", "votes": [1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3SITXWYCNYQY0G1XPXSJCMGJF5IXBM", "high_descs": ["Turn around and begin walking across the room, then hang a right and walk over to the coffee machine on the counter.", "Remove the black coffee mug that is on the coffee machine.", "Turn right and walk over to the sink, then look up at the microwave.", "Open the microwave and put the coffee mug inside to the right of the glass, close the door and turn on the microwave, after a couple seconds open the door and remove the black mug, then close the door.", "Turn left and walk back over to the coffee machine on the counter.", "Put the heated coffee mug back on the coffee machine."], "task_desc": "Put a heated coffee mug on the coffee machine.", "votes": [1, 1]}, {"assignment_id": "A36DK84J5YJ942_3WLEIWSYHRY7QD1M10HM87PLX982HI", "high_descs": ["Move to the coffee pot to the left of the sink.", "Pick up the coffee mug from the pot.", "Move to the microwave to the right of you above the sink.", "Place the mug inside and heated before removing it and closing the door.", "Move to the coffee pot to the left of you.", "Place the mug on the coffee pot."], "task_desc": "Heat and replace the mug on the coffee pot.", "votes": [1, 1]}, {"assignment_id": "AFU00NU09CFXE_3JJVG1YBEEEPYXHNJR9OK3PZMEW5BO", "high_descs": ["Cross the room to stand in front of the coffee maker on the counter to the left of the sink. ", "Pick up the mug from the base of the coffee maker.", "Carry the mug to stand in front of the sink. ", "Put the mug in the microwave above the sink and then remove once heated. ", "Carry the mug back to the coffee maker.", "Place the mug back on the base of the coffee maker."], "task_desc": "Place a heated mug on the base of the coffee maker. ", "votes": [1, 1]}, {"assignment_id": "A24RGLS3BRZ60J_3X66WABAJZZIBZ2ULUD96HICOUA3GO", "high_descs": ["Turn around and go straight back. Turn right to find the coffee maker.", "Pick up the mug from the coffee maker.", "Take the mug and move a few steps to the right.", "Look up and place the mug in the microwave. Take the mug out and then shut the door.", "Take the mug a few steps to the left, back to the coffee maker.", "Place the mug on the coffee maker."], "task_desc": "Heat a mug and place it on the coffee maker.", "votes": [1, 1]}, {"assignment_id": "A4CHLWPHZIP7Y_3ZWFC4W1UXO4G1R1L08N6ULGTUZRFS", "high_descs": ["Turn around, go to the wall, turn right, and go to the coffee maker.", "Take the mug from the coffee maker.", "Turn right, go to the sink, turn left, and face the microwave above the sink.", "Open the microwave, put the mug in the microwave, close the microwave, run the microwave for two seconds, open the microwave, take the mug from the microwave, and close the microwave.", "Turn left, go to the coffee maker, and turn right.", "Put the mug on the coffee maker."], "task_desc": "Place a warmed mug on the coffee maker.", "votes": [1, 1]}]}}