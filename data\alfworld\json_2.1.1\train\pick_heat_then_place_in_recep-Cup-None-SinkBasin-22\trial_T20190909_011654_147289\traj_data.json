{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000245.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000246.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000247.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000248.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000249.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000254.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000255.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000256.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000258.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000259.png", "low_idx": 53}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "SinkBasin", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-6|-2|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-6.02024364, -6.02024364, -4.5577612, -4.5577612, 3.4014488, 3.4014488]], "coordinateReceptacleObjectId": ["SinkBasin", [-5.3792, -5.3792, -5.0, -5.0, 3.356, 3.356]], "forceVisible": true, "objectId": "Cup|-01.51|+00.85|-01.14"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|-2|2|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [1.132, 1.132, -5.3248, -5.3248, 3.6003664, 3.6003664]], "forceVisible": true, "objectId": "Microwave|+00.28|+00.90|-01.33"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-6|-2|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "sinkbasin"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-6.02024364, -6.02024364, -4.5577612, -4.5577612, 3.4014488, 3.4014488]], "coordinateReceptacleObjectId": ["SinkBasin", [-5.3792, -5.3792, -5.0, -5.0, 3.356, 3.356]], "forceVisible": true, "objectId": "Cup|-01.51|+00.85|-01.14", "receptacleObjectId": "Sink|-01.33|+00.92|-01.23|SinkBasin"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.51|+00.85|-01.14"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [138, 127, 164, 176], "mask": [[37947, 9], [38245, 13], [38542, 19], [38841, 21], [39140, 23], [39440, 23], [39739, 25], [40039, 25], [40339, 25], [40639, 26], [40938, 27], [41238, 27], [41538, 27], [41838, 27], [42139, 26], [42439, 25], [42739, 25], [43039, 25], [43339, 25], [43640, 23], [43940, 23], [44240, 23], [44541, 21], [44841, 21], [45142, 19], [45443, 17], [45744, 15], [46045, 13], [46347, 9], [46650, 3], [46950, 3], [47249, 5], [47545, 12], [47843, 16], [48142, 19], [48441, 20], [48740, 22], [49040, 23], [49340, 23], [49639, 24], [49939, 25], [50239, 24], [50540, 23], [50840, 23], [51141, 21], [51441, 20], [51742, 19], [52044, 15], [52346, 11], [52650, 3]], "point": [151, 150]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.51|+00.85|-01.14", "placeStationary": true, "receptacleObjectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 4, 252, 267], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15322, 229], [15620, 230], [15919, 231], [16219, 231], [16518, 232], [16817, 232], [17116, 233], [17415, 234], [17715, 234], [18014, 235], [18313, 236], [18612, 236], [18911, 237], [19211, 237], [19510, 237], [19809, 238], [20108, 239], [20407, 240], [20707, 239], [21006, 240], [21305, 241], [21604, 241], [21903, 242], [22203, 242], [22502, 243], [22801, 243], [23100, 244], [23400, 244], [23700, 243], [24000, 243], [24300, 243], [24600, 243], [24900, 242], [25200, 242], [25500, 242], [25800, 241], [26100, 241], [26400, 241], [26700, 241], [27000, 240], [27300, 240], [27600, 240], [27900, 240], [28200, 239], [28500, 239], [28800, 239], [29100, 238], [29400, 238], [29700, 238], [30000, 238], [30300, 237], [30600, 237], [30900, 237], [31200, 236], [31500, 236], [31800, 236], [32100, 236], [32400, 235], [32700, 235], [33000, 235], [33300, 234], [33600, 234], [33900, 234], [34200, 234], [34500, 233], [34800, 233], [35100, 233], [35400, 232], [35700, 232], [36000, 232], [36300, 232], [36600, 231], [36900, 231], [37200, 231], [37500, 230], [37800, 230], [38100, 230], [38400, 230], [38700, 229], [39000, 229], [39300, 229], [39600, 228], [39900, 228], [40200, 228], [40500, 228], [40800, 227], [41100, 227], [41400, 227], [41700, 226], [42000, 226], [42300, 225], [42600, 226], [42900, 225], [43200, 225], [43500, 225], [43800, 56], [43858, 166], [44100, 56], [44158, 166], [44400, 56], [44458, 166], [44700, 55], [44759, 165], [45000, 55], [45300, 54], [45600, 54], [45900, 54], [46200, 53], [46500, 53], [46800, 53], [47100, 52], [47400, 52], [47700, 52], [48000, 51], [48300, 51], [48600, 50], [48900, 50], [49200, 50], [49500, 49], [49800, 49], [50100, 49], [50400, 48], [50700, 48], [51000, 48], [51300, 47], [51600, 47], [51900, 46], [52200, 46], [52500, 46], [52800, 45], [53100, 45], [53400, 45], [53700, 44], [54000, 44], [54300, 43], [54600, 43], [54900, 43], [55200, 42], [55500, 42], [55800, 42], [56100, 41], [56400, 41], [56700, 41], [57000, 40], [57300, 40], [57600, 39], [57900, 39], [58200, 39], [58500, 38], [58800, 38], [59100, 38], [59400, 37], [59700, 37], [60000, 37], [60300, 36], [60600, 36], [60900, 35], [61200, 35], [61500, 35], [61800, 34], [62100, 34], [62400, 34], [62700, 33], [63000, 33], [63300, 33], [63600, 32], [63900, 32], [64200, 31], [64500, 31], [64800, 31], [65100, 30], [65400, 30], [65700, 30], [66000, 29], [66300, 29], [66600, 28], [66900, 28], [67200, 28], [67500, 27], [67800, 27], [68100, 27], [68400, 26], [68700, 26], [69000, 26], [69300, 25], [69600, 25], [69900, 24], [70200, 24], [70500, 24], [70800, 23], [71100, 23], [71400, 23], [71700, 22], [72000, 22], [72300, 22], [72600, 21], [72900, 21], [73200, 20], [73500, 20], [73800, 20], [74100, 19], [74400, 19], [74700, 19], [75000, 18], [75300, 18], [75600, 18], [75900, 17], [76200, 17], [76500, 16], [76800, 16], [77100, 16], [77400, 15], [77700, 17], [78000, 18], [78300, 19], [78600, 19], [78900, 14], [78915, 2], [79200, 13], [79500, 13], [79800, 12]], "point": [126, 134]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 4, 252, 267], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15322, 229], [15620, 230], [15919, 231], [16219, 231], [16518, 232], [16817, 232], [17116, 233], [17415, 234], [17715, 234], [18014, 235], [18313, 236], [18612, 236], [18911, 237], [19211, 237], [19510, 237], [19809, 133], [19963, 84], [20108, 133], [20264, 83], [20407, 133], [20565, 82], [20707, 133], [20865, 81], [21006, 133], [21165, 81], [21305, 134], [21466, 80], [21604, 135], [21766, 79], [21903, 136], [22066, 79], [22203, 136], [22366, 79], [22502, 137], [22666, 79], [22801, 138], [22966, 78], [23100, 139], [23266, 78], [23400, 139], [23565, 79], [23700, 140], [23865, 78], [24000, 140], [24165, 78], [24300, 140], [24465, 78], [24600, 140], [24764, 79], [24900, 141], [25064, 78], [25200, 141], [25364, 78], [25500, 141], [25663, 79], [25800, 142], [25963, 78], [26100, 142], [26262, 79], [26400, 143], [26562, 79], [26700, 143], [26861, 80], [27000, 144], [27160, 80], [27300, 145], [27459, 81], [27600, 147], [27758, 82], [27900, 149], [28056, 84], [28200, 151], [28353, 86], [28500, 151], [28653, 86], [28800, 151], [28953, 86], [29100, 151], [29253, 85], [29400, 151], [29553, 85], [29700, 151], [29853, 85], [30000, 151], [30153, 85], [30300, 151], [30453, 84], [30600, 149], [30756, 81], [30900, 145], [31059, 78], [31200, 143], [31361, 75], [31500, 142], [31662, 74], [31800, 141], [31963, 73], [32100, 141], [32264, 72], [32400, 140], [32564, 71], [32700, 140], [32865, 70], [33000, 140], [33164, 71], [33300, 140], [33464, 70], [33600, 141], [33764, 70], [33900, 142], [34063, 71], [34200, 143], [34362, 72], [34500, 145], [34660, 73], [34800, 147], [34957, 76], [35100, 233], [35400, 232], [35700, 232], [36000, 232], [36300, 232], [36600, 231], [36900, 231], [37200, 231], [37500, 230], [37800, 230], [38100, 230], [38400, 230], [38700, 229], [39000, 229], [39300, 229], [39600, 228], [39900, 228], [40200, 228], [40500, 228], [40800, 227], [41100, 227], [41400, 227], [41700, 226], [42000, 226], [42300, 225], [42600, 226], [42900, 225], [43200, 225], [43500, 225], [43800, 56], [43858, 166], [44100, 56], [44158, 166], [44400, 56], [44458, 166], [44700, 55], [44759, 165], [45000, 55], [45300, 54], [45600, 54], [45900, 54], [46200, 53], [46500, 53], [46800, 53], [47100, 52], [47400, 52], [47700, 52], [48000, 51], [48300, 51], [48600, 50], [48900, 50], [49200, 50], [49500, 49], [49800, 49], [50100, 49], [50400, 48], [50700, 48], [51000, 48], [51300, 47], [51600, 47], [51900, 46], [52200, 46], [52500, 46], [52800, 45], [53100, 45], [53400, 45], [53700, 44], [54000, 44], [54300, 43], [54600, 43], [54900, 43], [55200, 42], [55500, 42], [55800, 42], [56100, 41], [56400, 41], [56700, 41], [57000, 40], [57300, 40], [57600, 39], [57900, 39], [58200, 39], [58500, 38], [58800, 38], [59100, 38], [59400, 37], [59700, 37], [60000, 37], [60300, 36], [60600, 36], [60900, 35], [61200, 35], [61500, 35], [61800, 34], [62100, 34], [62400, 34], [62700, 33], [63000, 33], [63300, 33], [63600, 32], [63900, 32], [64200, 31], [64500, 31], [64800, 31], [65100, 30], [65400, 30], [65700, 30], [66000, 29], [66300, 29], [66600, 28], [66900, 28], [67200, 28], [67500, 27], [67800, 27], [68100, 27], [68400, 26], [68700, 26], [69000, 26], [69300, 25], [69600, 25], [69900, 24], [70200, 24], [70500, 24], [70800, 23], [71100, 23], [71400, 23], [71700, 22], [72000, 22], [72300, 22], [72600, 21], [72900, 21], [73200, 20], [73500, 20], [73800, 20], [74100, 19], [74400, 19], [74700, 19], [75000, 18], [75300, 18], [75600, 18], [75900, 17], [76200, 17], [76500, 16], [76800, 16], [77100, 16], [77400, 15], [77700, 17], [78000, 18], [78300, 19], [78600, 19], [78900, 14], [78915, 2], [79200, 13], [79500, 13], [79800, 12]], "point": [126, 134]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [1.132, 1.132, -5.3248, -5.3248, 3.6003664, 3.6003664]], "forceVisible": true, "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [1.132, 1.132, -5.3248, -5.3248, 3.6003664, 3.6003664]], "forceVisible": true, "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.51|+00.85|-01.14"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [139, 67, 165, 117], "mask": [[19942, 21], [20241, 23], [20540, 25], [20840, 25], [21139, 26], [21439, 27], [21739, 27], [22039, 27], [22339, 27], [22639, 27], [22939, 27], [23239, 27], [23539, 26], [23840, 25], [24140, 25], [24440, 25], [24740, 24], [25041, 23], [25341, 23], [25641, 22], [25942, 21], [26242, 20], [26543, 19], [26843, 18], [27144, 16], [27445, 14], [27747, 11], [28049, 7], [28351, 2], [28651, 2], [28951, 2], [29251, 2], [29551, 2], [29851, 2], [30151, 2], [30451, 2], [30749, 7], [31045, 14], [31343, 18], [31642, 20], [31941, 22], [32241, 23], [32540, 24], [32840, 25], [33140, 24], [33440, 24], [33741, 23], [34042, 21], [34343, 19], [34645, 15], [34947, 10]], "point": [152, 91]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 4, 252, 267], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15322, 229], [15620, 230], [15919, 231], [16219, 231], [16518, 232], [16817, 232], [17116, 233], [17415, 234], [17715, 234], [18014, 235], [18313, 236], [18612, 236], [18911, 237], [19211, 237], [19510, 237], [19809, 238], [20108, 239], [20407, 240], [20707, 239], [21006, 240], [21305, 241], [21604, 241], [21903, 242], [22203, 242], [22502, 243], [22801, 243], [23100, 244], [23400, 244], [23700, 243], [24000, 243], [24300, 243], [24600, 243], [24900, 242], [25200, 242], [25500, 242], [25800, 241], [26100, 241], [26400, 241], [26700, 241], [27000, 240], [27300, 240], [27600, 240], [27900, 240], [28200, 239], [28500, 239], [28800, 239], [29100, 238], [29400, 238], [29700, 238], [30000, 238], [30300, 237], [30600, 237], [30900, 237], [31200, 236], [31500, 236], [31800, 236], [32100, 236], [32400, 235], [32700, 235], [33000, 235], [33300, 234], [33600, 234], [33900, 234], [34200, 234], [34500, 233], [34800, 233], [35100, 233], [35400, 232], [35700, 232], [36000, 232], [36300, 232], [36600, 231], [36900, 231], [37200, 231], [37500, 230], [37800, 230], [38100, 230], [38400, 230], [38700, 229], [39000, 229], [39300, 229], [39600, 228], [39900, 228], [40200, 228], [40500, 228], [40800, 227], [41100, 227], [41400, 227], [41700, 226], [42000, 226], [42300, 225], [42600, 226], [42900, 225], [43200, 225], [43500, 225], [43800, 56], [43858, 166], [44100, 56], [44158, 166], [44400, 56], [44458, 166], [44700, 55], [44759, 165], [45000, 55], [45300, 54], [45600, 54], [45900, 54], [46200, 53], [46500, 53], [46800, 53], [47100, 52], [47400, 52], [47700, 52], [48000, 51], [48300, 51], [48600, 50], [48900, 50], [49200, 50], [49500, 49], [49800, 49], [50100, 49], [50400, 48], [50700, 48], [51000, 48], [51300, 47], [51600, 47], [51900, 46], [52200, 46], [52500, 46], [52800, 45], [53100, 45], [53400, 45], [53700, 44], [54000, 44], [54300, 43], [54600, 43], [54900, 43], [55200, 42], [55500, 42], [55800, 42], [56100, 41], [56400, 41], [56700, 41], [57000, 40], [57300, 40], [57600, 39], [57900, 39], [58200, 39], [58500, 38], [58800, 38], [59100, 38], [59400, 37], [59700, 37], [60000, 37], [60300, 36], [60600, 36], [60900, 35], [61200, 35], [61500, 35], [61800, 34], [62100, 34], [62400, 34], [62700, 33], [63000, 33], [63300, 33], [63600, 32], [63900, 32], [64200, 31], [64500, 31], [64800, 31], [65100, 30], [65400, 30], [65700, 30], [66000, 29], [66300, 29], [66600, 28], [66900, 28], [67200, 28], [67500, 27], [67800, 27], [68100, 27], [68400, 26], [68700, 26], [69000, 26], [69300, 25], [69600, 25], [69900, 24], [70200, 24], [70500, 24], [70800, 23], [71100, 23], [71400, 23], [71700, 22], [72000, 22], [72300, 22], [72600, 21], [72900, 21], [73200, 20], [73500, 20], [73800, 20], [74100, 19], [74400, 19], [74700, 19], [75000, 18], [75300, 18], [75600, 18], [75900, 17], [76200, 17], [76500, 16], [76800, 16], [77100, 16], [77400, 15], [77700, 17], [78000, 18], [78300, 19], [78600, 19], [78900, 14], [78915, 2], [79200, 13], [79500, 13], [79800, 12]], "point": [126, 134]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.51|+00.85|-01.14", "placeStationary": true, "receptacleObjectId": "Sink|-01.33|+00.92|-01.23|SinkBasin"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [7, 120, 197, 186], "mask": [[35752, 51], [35820, 51], [36046, 57], [36120, 57], [36343, 60], [36420, 59], [36640, 63], [36720, 62], [36938, 64], [37020, 64], [37237, 65], [37320, 65], [37535, 67], [37620, 66], [37834, 68], [37920, 67], [38132, 70], [38219, 70], [38431, 70], [38519, 70], [38730, 71], [38819, 71], [39029, 72], [39119, 71], [39329, 72], [39419, 14], [39443, 48], [39628, 72], [39719, 12], [39748, 43], [39927, 73], [40019, 11], [40063, 28], [40227, 73], [40319, 12], [40346, 7], [40378, 14], [40526, 74], [40618, 13], [40645, 15], [40680, 12], [40826, 75], [40918, 15], [40944, 21], [40979, 13], [41125, 76], [41218, 18], [41242, 33], [41277, 15], [41425, 77], [41517, 75], [41724, 168], [42024, 169], [42323, 170], [42623, 170], [42922, 171], [43222, 171], [43521, 172], [43821, 173], [44120, 174], [44420, 174], [44719, 175], [45018, 176], [45318, 101], [45420, 74], [45617, 178], [45917, 178], [46216, 179], [46516, 179], [46815, 180], [47115, 181], [47414, 182], [47714, 182], [48013, 183], [48313, 183], [48612, 184], [48912, 185], [49211, 186], [49511, 137], [49652, 45], [49810, 132], [49958, 39], [50110, 126], [50264, 33], [50409, 125], [50566, 31], [50709, 123], [50868, 30], [51009, 120], [51171, 27], [51308, 119], [51473, 24], [51608, 117], [51775, 22], [51908, 115], [52077, 20], [52208, 114], [52378, 19], [52507, 114], [52679, 18], [52807, 113], [52980, 17], [53107, 112], [53281, 16], [53407, 112], [53581, 15], [53707, 111], [53882, 14], [54008, 109], [54183, 12], [54308, 108], [54484, 10], [54609, 106], [54785, 9], [54909, 105], [55086, 7], [55209, 104], [55387, 5], [55510, 103], [55687, 4]], "point": [102, 152]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan22", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": 1.0, "y": 0.9009992, "z": 2.0}, "object_poses": [{"objectName": "Pan_304c8d90", "position": {"x": -2.59, "y": 0.9, "z": -1.147}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5ea35e17", "position": {"x": -0.598039, "y": 0.9335, "z": -1.119659}, "rotation": {"x": 0.0, "y": 47.53002, "z": 0.0}}, {"objectName": "Potato_5ea35e17", "position": {"x": -0.598039, "y": 0.9335, "z": -1.119659}, "rotation": {"x": 0.0, "y": 47.53002, "z": 0.0}}, {"objectName": "CreditCard_35b71b93", "position": {"x": 0.25451684, "y": 0.7431961, "z": -1.13832915}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_9171dd8d", "position": {"x": -1.33236611, "y": 0.8513145, "z": -1.3605597}, "rotation": {"x": 1.40334183e-14, "y": 3.208973e-43, "z": 2.89269767e-27}}, {"objectName": "DishSponge_9171dd8d", "position": {"x": -2.58522987, "y": 0.746153057, "z": -0.0271033123}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_1cf3b23c", "position": {"x": -2.6225, "y": 0.9287, "z": -0.7111}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_1cf3b23c", "position": {"x": -2.84925628, "y": 1.66631353, "z": -1.05923724}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_4b9198b3", "position": {"x": -1.50506079, "y": 0.85281, "z": -1.30527985}, "rotation": {"x": 1.40334183e-14, "y": 3.208973e-43, "z": 2.89269767e-27}}, {"objectName": "Cup_46773746", "position": {"x": -1.50506091, "y": 0.8503622, "z": -1.1394403}, "rotation": {"x": 1.40334183e-14, "y": 3.208973e-43, "z": 2.89269767e-27}}, {"objectName": "Cup_46773746", "position": {"x": -2.92097759, "y": 1.47254121, "z": 0.9327622}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_46773746", "position": {"x": -2.35459423, "y": 1.66115081, "z": -1.28460813}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ad96aa4f", "position": {"x": 0.231629014, "y": 1.113564, "z": 1.79067755}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Plate_ad96aa4f", "position": {"x": -1.44125962, "y": 1.66433537, "z": -1.32230663}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_e02c5e35", "position": {"x": -2.68010187, "y": 1.5090251, "z": 0.609712839}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Knife_1fa5d324", "position": {"x": -2.52118, "y": 0.9343805, "z": 0.1906048}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_f346af22", "position": {"x": 0.111418523, "y": 1.66372752, "z": -1.32230663}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_f346af22", "position": {"x": -2.78675556, "y": 0.03887308, "z": 1.39676857}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_b7a8ccec", "position": {"x": -2.06074429, "y": 1.66150928, "z": -1.36000526}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e2bf19ed", "position": {"x": 0.644392133, "y": 1.19090188, "z": 1.22969747}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Lettuce_e2bf19ed", "position": {"x": 0.765533566, "y": 0.990501761, "z": -1.28244877}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_f59054f9", "position": {"x": -2.773459, "y": 1.79136467, "z": -0.3051622}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_f59054f9", "position": {"x": -1.048614, "y": 1.65877759, "z": -1.39770389}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_4f98d325", "position": {"x": -2.60430479, "y": 0.9076062, "z": -0.0629096553}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_81a93477", "position": {"x": 0.548222065, "y": 1.16622007, "z": 1.47979164}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Apple_a99f93a6", "position": {"x": -2.7403214, "y": 0.430202544, "z": 0.93276304}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_a99f93a6", "position": {"x": -2.7283597, "y": 0.089363575, "z": 1.5369277}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_304c8d90", "position": {"x": -2.59, "y": 0.9, "z": -1.147}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a99f93a6", "position": {"x": -2.38871121, "y": 0.963046551, "z": -1.11555123}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_b9d165af", "position": {"x": -0.27649647, "y": 0.901713431, "z": -1.11815751}, "rotation": {"x": 0.154637411, "y": -0.00114239077, "z": 359.698547}}, {"objectName": "Kettle_1cf3b23c", "position": {"x": 0.798316, "y": 1.11274135, "z": 1.38362074}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "CreditCard_35b71b93", "position": {"x": -2.85368, "y": 0.908296, "z": 0.02159518}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_81a93477", "position": {"x": -2.740321, "y": 1.528507, "z": 0.852000237}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_ad96aa4f", "position": {"x": -0.7677574, "y": 0.9131639, "z": -1.03210282}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_f346af22", "position": {"x": -1.56990516, "y": 0.11854583, "z": -1.14270937}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_3b50a8bf", "position": {"x": -2.55216455, "y": 0.922, "z": 0.0104080439}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e2bf19ed", "position": {"x": -2.7403214, "y": 1.7524637, "z": 0.9327627}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_4f98d325", "position": {"x": -2.810885, "y": 1.79136467, "z": -0.8390826}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9ffe4e75", "position": {"x": 0.203787982, "y": 1.15205479, "z": 1.05698335}, "rotation": {"x": 359.98233, "y": 0.00772271538, "z": 0.233231813}}, {"objectName": "PepperShaker_f59054f9", "position": {"x": -0.0393949151, "y": 1.10800624, "z": 1.84464192}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "ButterKnife_dc49bd4c", "position": {"x": -2.22248077, "y": 0.9, "z": -0.997339845}, "rotation": {"x": 0.0, "y": 323.977722, "z": 0.0}}, {"objectName": "Pot_86f597c8", "position": {"x": -2.823, "y": 0.9287, "z": -0.3964}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5ea35e17", "position": {"x": -0.598039, "y": 0.9335, "z": -1.119659}, "rotation": {"x": 0.0, "y": 47.53002, "z": 0.0}}, {"objectName": "DishSponge_9171dd8d", "position": {"x": -2.499291, "y": 0.746153057, "z": 0.0998117}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_e02c5e35", "position": {"x": -0.115129411, "y": 1.14686334, "z": 1.53315783}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Spoon_4b9198b3", "position": {"x": -1.933472, "y": 0.912429333, "z": -1.36589742}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_1fa5d324", "position": {"x": -0.017285049, "y": 1.13478053, "z": 1.5863831}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Cup_46773746", "position": {"x": -2.848311, "y": 1.79373789, "z": -0.4124544}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f88244ed", "position": {"x": 0.372600049, "y": 1.65404749, "z": -1.35359979}, "rotation": {"x": 359.610046, "y": -3.68376641e-05, "z": 0.00738341268}}, {"objectName": "Bowl_b7a8ccec", "position": {"x": -2.68010235, "y": 0.818859637, "z": 0.852000356}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}], "object_toggles": [], "random_seed": 393926076, "scene_num": 22}, "task_id": "trial_T20190909_011654_147289", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "ALKQPW0O9C98N_34YB12FSQ1FRIAW2NK7532BKIFNGMY", "high_descs": ["Turn left and head towards the sink", "Pick up the wine glass from the sink", "Turn left and go to the microwave on the right", "Open the microwave door, put the glass in, close the door and turn on the microwave. Open the microwave door and take the glass out of the microwave.", "Turn right and go to the sink on the left", "Place the glass in the sink below the sponge"], "task_desc": "Put a heated wine glass in the sink", "votes": [1, 1]}, {"assignment_id": "A10AVWALIHR4UQ_36W0OB37HZ55HDQWMFFHW4JUCJIZHX", "high_descs": ["Move to the gray door in the corner of the room then turn left and move to the sink.", "Pick up the amber wine glass to the right of the drain from the sink.", "Turn to the left and move past the dishwasher then turn right and face the red microwave.", "Open the microwave, place the wine glass on the plate, microwave the glass to warm it, then open the microwave, pick up the wine glass from the microwave, and then close the microwave door.", "Turn right and move towards the range, then turn left and face the sink.", "Place the wine glass in front of the drain in the sink."], "task_desc": "Place a warmed wine glass in the sink.", "votes": [1, 1]}, {"assignment_id": "AJQGWGESKQT4Y_3EO896NRAZM5M7CN4NTKMTYS4FZTJF", "high_descs": ["Go forward and turn left to stand in front of the sink.", "Pick the wine glass up from the sink.", "Go left and turn to stand in front of the  microwave.", "Put the wine glass in the microwave and shut the door and then open the door and pick up the wine glass.", "Go to the right and turn to stand in front of the sink.", "Put the wine glass in the sink."], "task_desc": "Put a warmed glass in the sink.", "votes": [1, 1]}]}}