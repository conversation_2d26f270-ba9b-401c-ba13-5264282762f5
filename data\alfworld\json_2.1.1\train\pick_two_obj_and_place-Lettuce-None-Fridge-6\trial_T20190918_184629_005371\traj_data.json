{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000258.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000259.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000260.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000261.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000262.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000263.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000264.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000265.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000266.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000267.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000268.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000269.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000270.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000271.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000283.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000284.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000285.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000286.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000287.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000288.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000289.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000290.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000291.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000292.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000293.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000294.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000295.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000296.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000297.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000298.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000299.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000300.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000301.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000302.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000303.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000304.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000305.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000306.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000307.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000308.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000309.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000310.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000311.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000312.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000313.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000314.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000315.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000316.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000317.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000318.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000319.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000320.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000321.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000322.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000323.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000324.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000325.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000326.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000327.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000328.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000329.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000330.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000331.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000332.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000333.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000334.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000335.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000336.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000337.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000338.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000339.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000340.png", "low_idx": 60}, {"high_idx": 7, "image_name": "000000341.png", "low_idx": 60}, {"high_idx": 7, "image_name": "000000342.png", "low_idx": 60}, {"high_idx": 7, "image_name": "000000343.png", "low_idx": 60}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Lettuce", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-7|3|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["lettuce"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Lettuce", [-10.154736, -10.154736, 2.5251256, 2.5251256, 3.976215364, 3.976215364]], "coordinateReceptacleObjectId": ["CounterTop", [-5.94, -5.94, 5.276, 5.276, 3.7856, 3.7856]], "forceVisible": true, "objectId": "Lettuce|-02.54|+00.99|+00.63"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-7|-3|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["lettuce", "fridge"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Lettuce", [-10.154736, -10.154736, 2.5251256, 2.5251256, 3.976215364, 3.976215364]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-9.90399932, -9.90399932, -3.132, -3.132, 0.0, 0.0]], "forceVisible": true, "objectId": "Lettuce|-02.54|+00.99|+00.63", "receptacleObjectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-5|3|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["lettuce"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Lettuce", [-1.884718656, -1.884718656, 3.482644556, 3.482644556, 3.976215364, 3.976215364]], "coordinateReceptacleObjectId": ["CounterTop", [-1.44, -1.44, 4.376, 4.376, 3.7856, 3.7856]], "forceVisible": true, "objectId": "Lettuce|-00.47|+00.99|+00.87"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-7|-3|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["lettuce", "fridge"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Lettuce", [-1.884718656, -1.884718656, 3.482644556, 3.482644556, 3.976215364, 3.976215364]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-9.90399932, -9.90399932, -3.132, -3.132, 0.0, 0.0]], "forceVisible": true, "objectId": "Lettuce|-00.47|+00.99|+00.87", "receptacleObjectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Lettuce|-02.54|+00.99|+00.63"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [88, 89, 150, 125], "mask": [[26499, 19], [26797, 25], [27095, 30], [27393, 36], [27692, 40], [27991, 44], [28291, 46], [28590, 49], [28889, 52], [29189, 54], [29489, 56], [29788, 58], [30088, 60], [30388, 61], [30688, 62], [30988, 62], [31288, 62], [31588, 63], [31888, 63], [32188, 63], [32488, 62], [32788, 62], [33088, 62], [33388, 62], [33688, 60], [33989, 57], [34289, 55], [34590, 52], [34890, 50], [35191, 46], [35492, 43], [35793, 39], [36094, 35], [36396, 29], [36698, 23], [37000, 16], [37306, 2]], "point": [119, 106]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 241], "mask": [[0, 48599], [48600, 298], [48900, 299], [49200, 299], [49500, 299], [49800, 299], [50100, 299], [50400, 299], [50700, 298], [51000, 297], [51300, 296], [51600, 295], [51901, 293], [52202, 290], [52503, 288], [52805, 285], [53106, 282], [53407, 280], [53708, 278], [54009, 275], [54311, 272], [54612, 270], [54913, 267], [55214, 265], [55515, 262], [55816, 260], [56118, 257], [56419, 254], [56720, 125], [56854, 118], [57021, 122], [57156, 114], [57322, 119], [57458, 111], [57624, 115], [57760, 108], [57925, 113], [58061, 105], [58226, 111], [58362, 103], [58527, 110], [58662, 101], [58828, 108], [58963, 99], [59129, 106], [59264, 97], [59431, 103], [59565, 95], [59732, 101], [59866, 92], [60033, 100], [60167, 90], [60334, 98], [60468, 88], [60635, 96], [60769, 86], [60936, 95], [61069, 85], [61238, 92], [61370, 83], [61539, 90], [61671, 81], [61840, 88], [61972, 79], [62141, 87], [62273, 77], [62442, 85], [62574, 75], [62744, 82], [62875, 73], [63045, 80], [63176, 71], [63346, 78], [63476, 70], [63647, 76], [63777, 67], [63948, 75], [64078, 65], [64249, 73], [64379, 63], [64551, 70], [64680, 61], [64852, 68], [64981, 59], [65153, 67], [65282, 57], [65454, 65], [65582, 56], [65755, 63], [65883, 54], [66056, 61], [66184, 52], [66358, 58], [66484, 51], [66658, 58], [66785, 49], [66958, 57], [67086, 47], [67258, 56], [67386, 46], [67557, 56], [67687, 45], [67857, 56], [67988, 44], [68157, 56], [68288, 43], [68457, 55], [68589, 42], [68758, 54], [68889, 41], [69059, 53], [69190, 39], [69360, 51], [69491, 37], [69662, 49], [69791, 36], [69966, 45], [70092, 31], [70269, 41], [70392, 27], [70573, 37], [70693, 23], [70876, 34], [70993, 19], [71180, 29], [71294, 14], [71486, 23], [71594, 8], [71792, 17], [71895, 1], [72100, 8]], "point": [149, 120]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Lettuce|-02.54|+00.99|+00.63", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 161], [180, 279], [482, 275], [784, 272], [1085, 270], [1386, 269], [1687, 267], [1988, 266], [2288, 266], [2588, 265], [2888, 265], [3192, 261], [3493, 261], [3794, 260], [4095, 259], [4388, 3], [4395, 259], [4688, 4], [4695, 259], [4987, 5], [4995, 259], [5287, 5], [5295, 259], [5587, 5], [5595, 259], [5887, 5], [5895, 259], [6187, 5], [6195, 259], [6486, 5], [6494, 260], [6786, 5], [6794, 260], [7086, 4], [7094, 260], [7386, 4], [7393, 261], [7686, 4], [7693, 261], [7986, 3], [7992, 262], [8285, 4], [8292, 262], [8585, 3], [8591, 263], [8885, 3], [8891, 263], [9185, 2], [9190, 264], [9485, 1], [9490, 264], [9784, 1], [9789, 265], [10088, 266], [10388, 266], [10687, 267], [10986, 268], [11284, 270], [11583, 271], [11883, 272], [12183, 272], [12483, 272], [12783, 272], [13082, 273], [13382, 273], [13682, 274], [13981, 276], [14281, 277], [14580, 279], [14879, 282], [15177, 286], [15475, 23524], [39000, 298], [39300, 297], [39600, 296], [39900, 295], [40200, 295], [40500, 294], [40800, 293], [41100, 292], [41400, 291], [41700, 290], [42000, 289], [42300, 289], [42600, 288], [42900, 287], [43200, 286], [43500, 285], [43800, 284], [44100, 283], [44400, 282], [44700, 282], [45000, 281], [45300, 280], [45600, 279], [45900, 278], [46200, 277], [46500, 276], [46800, 275], [47100, 275], [47400, 274], [47700, 273], [48000, 272], [48300, 271], [48600, 270], [48900, 269], [49200, 268], [49500, 268], [49800, 267], [50100, 266], [50400, 265], [50700, 264], [51000, 263], [51300, 262], [51600, 261], [51900, 261], [52200, 260], [52500, 259], [52800, 258], [53100, 257], [53400, 256], [53700, 255], [54000, 254], [54300, 254], [54600, 253], [54900, 252], [55200, 251], [55500, 250], [55800, 249], [56100, 248], [56400, 247], [56700, 145], [56854, 93], [57000, 143], [57156, 90], [57300, 141], [57458, 87], [57600, 139], [57760, 84], [57900, 138], [58061, 82], [58200, 137], [58362, 80], [58500, 137], [58662, 79], [58800, 136], [58963, 77], [59100, 135], [59264, 76], [59400, 134], [59565, 74], [59700, 133], [59866, 72], [60000, 133], [60167, 70], [60300, 132], [60468, 68], [60600, 131], [60769, 66], [60900, 131], [61069, 65], [61200, 130], [61370, 63], [61500, 129], [61671, 62], [61800, 128], [61972, 60], [62100, 128], [62273, 58], [62400, 127], [62574, 56], [62700, 126], [62875, 54], [63000, 125], [63176, 53], [63300, 124], [63476, 53], [63600, 123], [63777, 52], [63900, 123], [64078, 51], [64200, 122], [64379, 50], [64500, 121], [64680, 49], [64800, 120], [64981, 48], [65100, 120], [65282, 48], [65400, 119], [65582, 48], [65700, 118], [65883, 47], [66000, 117], [66184, 46], [66300, 116], [66484, 46], [66600, 116], [66785, 45], [66900, 115], [67086, 45], [67200, 114], [67386, 45], [67500, 113], [67687, 44], [67800, 113], [67988, 43], [68100, 113], [68288, 43], [68400, 112], [68589, 42], [68700, 112], [68889, 41], [69000, 58], [69059, 53], [69190, 39], [69300, 57], [69360, 51], [69491, 37], [69600, 57], [69662, 49], [69791, 36], [69900, 57], [69966, 45], [70092, 31], [70200, 57], [70269, 41], [70392, 27], [70500, 57], [70573, 37], [70693, 23], [70800, 56], [70876, 34], [70993, 19], [71100, 56], [71180, 29], [71294, 14], [71400, 56], [71486, 23], [71594, 8], [71700, 56], [71792, 17], [71895, 1], [72000, 56], [72100, 8], [72300, 56], [72600, 55], [72900, 55], [73200, 55], [73500, 55], [73800, 55], [74100, 54], [74400, 54], [74700, 54], [75000, 54], [75300, 54], [75600, 54], [75900, 53], [76200, 53], [76500, 53], [76800, 53], [77100, 53], [77400, 52], [77700, 52], [78000, 52], [78300, 52], [78600, 52], [78900, 51], [79200, 51], [79500, 51], [79800, 51], [80100, 51], [80400, 51], [80700, 50], [81000, 50], [81300, 50], [81600, 50], [81900, 50], [82200, 49], [82500, 49], [82800, 49], [83100, 49], [83400, 49], [83700, 49], [84000, 48], [84300, 48], [84600, 48], [84900, 48], [85200, 48], [85500, 47], [85800, 47], [86100, 47], [86400, 47], [86700, 47], [87000, 47], [87300, 46], [87600, 46], [87900, 46], [88200, 46], [88500, 46], [88800, 45], [89100, 45], [89400, 45], [89700, 45]], "point": [149, 149]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 75], [126, 35], [180, 195], [426, 33], [482, 193], [725, 32], [784, 191], [1025, 31], [1085, 190], [1324, 31], [1386, 189], [1624, 31], [1687, 189], [1923, 31], [1988, 188], [2223, 31], [2288, 188], [2523, 31], [2588, 189], [2822, 31], [2888, 189], [3122, 31], [3192, 186], [3421, 32], [3493, 186], [3720, 34], [3794, 185], [4019, 35], [4095, 185], [4318, 36], [4388, 3], [4395, 186], [4617, 37], [4688, 4], [4695, 187], [4917, 37], [4987, 5], [4995, 188], [5216, 38], [5287, 5], [5295, 189], [5516, 38], [5587, 5], [5595, 189], [5814, 40], [5887, 5], [5895, 191], [6112, 42], [6187, 5], [6195, 193], [6410, 44], [6486, 5], [6494, 195], [6709, 45], [6786, 5], [6794, 197], [7007, 47], [7086, 4], [7094, 198], [7306, 48], [7386, 4], [7393, 200], [7605, 49], [7686, 4], [7693, 202], [7903, 51], [7986, 3], [7992, 262], [8285, 4], [8292, 262], [8585, 3], [8591, 263], [8885, 3], [8891, 263], [9185, 2], [9190, 264], [9485, 1], [9490, 264], [9784, 1], [9789, 265], [10088, 266], [10388, 266], [10687, 267], [10986, 268], [11284, 270], [11583, 271], [11883, 272], [12183, 272], [12483, 272], [12783, 272], [13082, 273], [13382, 273], [13682, 274], [13981, 276], [14281, 277], [14580, 279], [14879, 282], [15177, 286], [15475, 23524], [39000, 298], [39300, 297], [39600, 296], [39900, 295], [40200, 295], [40500, 294], [40800, 293], [41100, 292], [41400, 291], [41700, 290], [42000, 289], [42300, 289], [42600, 288], [42900, 287], [43200, 286], [43500, 285], [43800, 284], [44100, 283], [44400, 282], [44700, 282], [45000, 281], [45300, 280], [45600, 279], [45900, 278], [46200, 277], [46500, 276], [46800, 275], [47100, 275], [47400, 274], [47700, 273], [48000, 272], [48300, 271], [48600, 270], [48900, 269], [49200, 268], [49500, 268], [49800, 267], [50100, 266], [50400, 265], [50700, 264], [51000, 263], [51300, 262], [51600, 261], [51900, 261], [52200, 260], [52500, 259], [52800, 258], [53100, 257], [53400, 256], [53700, 255], [54000, 254], [54300, 254], [54600, 253], [54900, 252], [55200, 251], [55500, 250], [55800, 249], [56100, 248], [56400, 247], [56700, 247], [57000, 246], [57300, 245], [57600, 244], [57900, 243], [58200, 242], [58500, 241], [58800, 240], [59100, 240], [59400, 239], [59700, 238], [60000, 237], [60300, 236], [60600, 235], [60900, 234], [61200, 233], [61500, 233], [61800, 232], [62100, 231], [62400, 230], [62700, 229], [63000, 229], [63300, 229], [63600, 229], [63900, 229], [64200, 229], [64500, 229], [64800, 229], [65100, 230], [65400, 230], [65700, 230], [66000, 230], [66300, 230], [66600, 230], [66900, 231], [67200, 231], [67500, 231], [67800, 231], [68100, 231], [68400, 231], [68700, 230], [69000, 58], [69059, 170], [69300, 57], [69360, 168], [69600, 57], [69662, 165], [69900, 57], [69966, 157], [70200, 57], [70269, 150], [70500, 57], [70573, 143], [70800, 56], [70876, 136], [71100, 56], [71180, 128], [71400, 56], [71486, 116], [71700, 56], [71792, 104], [72000, 56], [72100, 89], [72300, 56], [72408, 72], [72600, 55], [72718, 52], [72900, 55], [73200, 55], [73500, 55], [73800, 55], [74100, 54], [74400, 54], [74700, 54], [75000, 54], [75300, 54], [75600, 54], [75900, 53], [76200, 53], [76500, 53], [76800, 53], [77100, 53], [77400, 52], [77700, 52], [78000, 52], [78300, 52], [78600, 52], [78900, 51], [79200, 51], [79500, 51], [79800, 51], [80100, 51], [80400, 51], [80700, 50], [81000, 50], [81300, 50], [81600, 50], [81900, 50], [82200, 49], [82500, 49], [82800, 49], [83100, 49], [83400, 49], [83700, 49], [84000, 48], [84300, 48], [84600, 48], [84900, 48], [85200, 48], [85500, 47], [85800, 47], [86100, 47], [86400, 47], [86700, 47], [87000, 47], [87300, 46], [87600, 46], [87900, 46], [88200, 46], [88500, 46], [88800, 45], [89100, 45], [89400, 45], [89700, 45]], "point": [149, 149]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Lettuce|-00.47|+00.99|+00.87"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [85, 96, 149, 138], "mask": [[28628, 6], [28920, 19], [29217, 23], [29513, 29], [29811, 32], [30108, 37], [30405, 41], [30703, 43], [31001, 46], [31299, 49], [31598, 50], [31896, 53], [32194, 55], [32492, 58], [32790, 60], [33088, 62], [33387, 63], [33686, 64], [33986, 64], [34285, 65], [34585, 65], [34885, 65], [35185, 65], [35485, 65], [35786, 64], [36086, 64], [36386, 64], [36687, 63], [36989, 61], [37292, 58], [37594, 55], [37896, 53], [38198, 51], [38500, 48], [38801, 46], [39103, 44], [39405, 41], [39709, 36], [40012, 32], [40315, 27], [40618, 22], [40921, 18], [41236, 1]], "point": [117, 116]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 241], "mask": [[0, 48599], [48600, 298], [48900, 299], [49200, 299], [49500, 299], [49800, 299], [50100, 299], [50400, 299], [50700, 298], [51000, 297], [51300, 296], [51600, 295], [51901, 293], [52202, 290], [52503, 288], [52805, 285], [53106, 282], [53407, 280], [53708, 278], [54009, 275], [54311, 272], [54612, 270], [54913, 267], [55214, 265], [55515, 262], [55816, 260], [56118, 257], [56419, 254], [56720, 125], [56854, 118], [57021, 122], [57156, 114], [57322, 119], [57458, 111], [57624, 115], [57760, 108], [57925, 113], [58061, 105], [58226, 111], [58362, 103], [58527, 110], [58662, 101], [58828, 108], [58963, 99], [59129, 106], [59264, 97], [59431, 103], [59565, 95], [59732, 101], [59866, 92], [60033, 100], [60167, 90], [60334, 98], [60468, 88], [60635, 96], [60769, 86], [60936, 95], [61069, 85], [61238, 92], [61370, 83], [61539, 90], [61671, 81], [61840, 88], [61972, 79], [62141, 87], [62273, 77], [62442, 85], [62574, 75], [62744, 82], [62875, 73], [63045, 80], [63176, 71], [63346, 78], [63476, 70], [63647, 76], [63777, 67], [63948, 75], [64078, 65], [64249, 73], [64379, 63], [64551, 70], [64680, 61], [64852, 68], [64981, 59], [65153, 67], [65282, 57], [65454, 65], [65582, 56], [65755, 63], [65883, 54], [66056, 61], [66184, 52], [66358, 58], [66484, 51], [66658, 58], [66785, 49], [66958, 57], [67086, 47], [67258, 56], [67386, 46], [67557, 56], [67687, 45], [67857, 56], [67988, 44], [68157, 56], [68288, 43], [68457, 55], [68589, 42], [68758, 54], [68889, 41], [69059, 53], [69190, 39], [69360, 51], [69491, 37], [69662, 49], [69791, 36], [69966, 45], [70092, 31], [70269, 41], [70392, 27], [70573, 37], [70693, 23], [70876, 34], [70993, 19], [71180, 29], [71294, 14], [71486, 23], [71594, 8], [71792, 17], [71895, 1], [72100, 8]], "point": [149, 120]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Lettuce|-00.47|+00.99|+00.87", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 75], [126, 35], [180, 195], [426, 33], [482, 193], [725, 32], [784, 191], [1025, 31], [1085, 190], [1324, 31], [1386, 189], [1624, 31], [1687, 189], [1923, 31], [1988, 188], [2223, 31], [2288, 188], [2523, 31], [2588, 189], [2822, 31], [2888, 189], [3122, 31], [3192, 186], [3421, 32], [3493, 186], [3720, 34], [3794, 185], [4019, 35], [4095, 185], [4318, 36], [4388, 3], [4395, 186], [4617, 37], [4688, 4], [4695, 187], [4917, 37], [4987, 5], [4995, 188], [5216, 38], [5287, 5], [5295, 189], [5516, 38], [5587, 5], [5595, 189], [5814, 40], [5887, 5], [5895, 191], [6112, 42], [6187, 5], [6195, 193], [6410, 44], [6486, 5], [6494, 195], [6709, 45], [6786, 5], [6794, 197], [7007, 47], [7086, 4], [7094, 198], [7306, 48], [7386, 4], [7393, 200], [7605, 49], [7686, 4], [7693, 202], [7903, 51], [7986, 3], [7992, 262], [8285, 4], [8292, 262], [8585, 3], [8591, 263], [8885, 3], [8891, 263], [9185, 2], [9190, 264], [9485, 1], [9490, 264], [9784, 1], [9789, 265], [10088, 266], [10388, 266], [10687, 267], [10986, 268], [11284, 270], [11583, 271], [11883, 272], [12183, 272], [12483, 272], [12783, 272], [13082, 273], [13382, 273], [13682, 274], [13981, 276], [14281, 277], [14580, 279], [14879, 282], [15177, 286], [15475, 23524], [39000, 298], [39300, 297], [39600, 296], [39900, 295], [40200, 295], [40500, 294], [40800, 293], [41100, 292], [41400, 291], [41700, 290], [42000, 289], [42300, 289], [42600, 288], [42900, 287], [43200, 286], [43500, 285], [43800, 284], [44100, 283], [44400, 282], [44700, 282], [45000, 281], [45300, 280], [45600, 279], [45900, 278], [46200, 277], [46500, 276], [46800, 275], [47100, 275], [47400, 274], [47700, 273], [48000, 272], [48300, 271], [48600, 270], [48900, 269], [49200, 268], [49500, 268], [49800, 267], [50100, 266], [50400, 265], [50700, 264], [51000, 263], [51300, 262], [51600, 261], [51900, 261], [52200, 260], [52500, 259], [52800, 258], [53100, 257], [53400, 256], [53700, 255], [54000, 254], [54300, 254], [54600, 253], [54900, 252], [55200, 251], [55500, 250], [55800, 249], [56100, 248], [56400, 247], [56700, 145], [56854, 93], [57000, 143], [57156, 90], [57300, 141], [57458, 87], [57600, 139], [57760, 84], [57900, 138], [58061, 82], [58200, 137], [58362, 80], [58500, 137], [58662, 79], [58800, 136], [58963, 77], [59100, 135], [59264, 76], [59400, 134], [59565, 74], [59700, 133], [59866, 72], [60000, 133], [60167, 70], [60300, 132], [60468, 68], [60600, 131], [60769, 66], [60900, 131], [61069, 65], [61200, 130], [61370, 63], [61500, 129], [61671, 62], [61800, 128], [61972, 60], [62100, 128], [62273, 58], [62400, 127], [62574, 56], [62700, 126], [62875, 54], [63000, 125], [63176, 53], [63300, 124], [63476, 53], [63600, 123], [63777, 52], [63900, 123], [64078, 51], [64200, 122], [64379, 50], [64500, 121], [64680, 49], [64800, 120], [64981, 48], [65100, 120], [65282, 48], [65400, 119], [65582, 48], [65700, 118], [65883, 47], [66000, 117], [66184, 46], [66300, 116], [66484, 46], [66600, 116], [66785, 45], [66900, 115], [67086, 45], [67200, 114], [67386, 45], [67500, 113], [67687, 44], [67800, 113], [67988, 43], [68100, 113], [68288, 43], [68400, 112], [68589, 42], [68700, 112], [68889, 41], [69000, 58], [69059, 53], [69190, 39], [69300, 57], [69360, 51], [69491, 37], [69600, 57], [69662, 49], [69791, 36], [69900, 57], [69966, 45], [70092, 31], [70200, 57], [70269, 41], [70392, 27], [70500, 57], [70573, 37], [70693, 23], [70800, 56], [70876, 34], [70993, 19], [71100, 56], [71180, 29], [71294, 14], [71400, 56], [71486, 23], [71594, 8], [71700, 56], [71792, 17], [71895, 1], [72000, 56], [72100, 8], [72300, 56], [72600, 55], [72900, 55], [73200, 55], [73500, 55], [73800, 55], [74100, 54], [74400, 54], [74700, 54], [75000, 54], [75300, 54], [75600, 54], [75900, 53], [76200, 53], [76500, 53], [76800, 53], [77100, 53], [77400, 52], [77700, 52], [78000, 52], [78300, 52], [78600, 52], [78900, 51], [79200, 51], [79500, 51], [79800, 51], [80100, 51], [80400, 51], [80700, 50], [81000, 50], [81300, 50], [81600, 50], [81900, 50], [82200, 49], [82500, 49], [82800, 49], [83100, 49], [83400, 49], [83700, 49], [84000, 48], [84300, 48], [84600, 48], [84900, 48], [85200, 48], [85500, 47], [85800, 47], [86100, 47], [86400, 47], [86700, 47], [87000, 47], [87300, 46], [87600, 46], [87900, 46], [88200, 46], [88500, 46], [88800, 45], [89100, 45], [89400, 45], [89700, 45]], "point": [149, 149]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 75], [126, 35], [180, 195], [426, 33], [482, 193], [725, 32], [784, 191], [1025, 31], [1085, 190], [1324, 31], [1386, 189], [1624, 31], [1687, 189], [1923, 31], [1988, 188], [2223, 31], [2288, 188], [2523, 31], [2588, 189], [2822, 31], [2888, 189], [3122, 31], [3192, 186], [3421, 32], [3493, 186], [3720, 34], [3794, 185], [4019, 35], [4095, 185], [4318, 36], [4388, 3], [4395, 186], [4617, 37], [4688, 4], [4695, 187], [4917, 37], [4987, 5], [4995, 188], [5216, 38], [5287, 5], [5295, 189], [5516, 38], [5587, 5], [5595, 189], [5814, 40], [5887, 5], [5895, 191], [6112, 42], [6187, 5], [6195, 193], [6410, 44], [6486, 5], [6494, 195], [6709, 45], [6786, 5], [6794, 197], [7007, 47], [7086, 4], [7094, 198], [7306, 48], [7386, 4], [7393, 200], [7605, 49], [7686, 4], [7693, 202], [7903, 51], [7986, 3], [7992, 262], [8285, 4], [8292, 262], [8585, 3], [8591, 263], [8885, 3], [8891, 263], [9185, 2], [9190, 264], [9485, 1], [9490, 264], [9784, 1], [9789, 265], [10088, 266], [10388, 266], [10687, 267], [10986, 268], [11284, 270], [11583, 271], [11883, 272], [12183, 272], [12483, 272], [12783, 272], [13082, 273], [13382, 273], [13682, 274], [13981, 276], [14281, 277], [14580, 279], [14879, 282], [15177, 286], [15475, 23524], [39000, 298], [39300, 297], [39600, 296], [39900, 295], [40200, 295], [40500, 294], [40800, 293], [41100, 292], [41400, 291], [41700, 290], [42000, 289], [42300, 289], [42600, 288], [42900, 287], [43200, 286], [43500, 285], [43800, 284], [44100, 283], [44400, 118], [44521, 161], [44700, 117], [44820, 3], [44825, 157], [45000, 117], [45120, 3], [45127, 154], [45300, 113], [45414, 3], [45420, 3], [45427, 153], [45600, 111], [45714, 2], [45720, 3], [45727, 152], [45900, 278], [46200, 277], [46500, 276], [46800, 275], [47100, 275], [47400, 108], [47535, 139], [47700, 108], [47835, 138], [48000, 108], [48136, 136], [48300, 108], [48436, 135], [48600, 107], [48736, 134], [48900, 107], [49037, 132], [49200, 107], [49337, 131], [49500, 107], [49637, 131], [49800, 106], [49937, 130], [50100, 106], [50237, 129], [50400, 106], [50537, 128], [50700, 106], [50837, 127], [51000, 106], [51137, 126], [51300, 106], [51436, 126], [51600, 106], [51736, 125], [51900, 106], [52035, 126], [52200, 107], [52334, 126], [52500, 107], [52634, 125], [52800, 108], [52933, 125], [53100, 109], [53232, 125], [53400, 109], [53529, 127], [53700, 114], [53827, 128], [54000, 254], [54300, 254], [54600, 253], [54900, 252], [55200, 251], [55500, 250], [55800, 249], [56100, 248], [56400, 247], [56700, 247], [57000, 246], [57300, 245], [57600, 244], [57900, 243], [58200, 242], [58500, 241], [58800, 240], [59100, 240], [59400, 239], [59700, 238], [60000, 237], [60300, 236], [60600, 235], [60900, 234], [61200, 233], [61500, 233], [61800, 232], [62100, 231], [62400, 230], [62700, 229], [63000, 229], [63300, 229], [63600, 229], [63900, 229], [64200, 229], [64500, 229], [64800, 229], [65100, 230], [65400, 230], [65700, 230], [66000, 230], [66300, 230], [66600, 230], [66900, 231], [67200, 231], [67500, 231], [67800, 231], [68100, 231], [68400, 231], [68700, 230], [69000, 58], [69059, 170], [69300, 57], [69360, 168], [69600, 57], [69662, 165], [69900, 57], [69966, 157], [70200, 57], [70269, 150], [70500, 57], [70573, 143], [70800, 56], [70876, 136], [71100, 56], [71180, 128], [71400, 56], [71486, 116], [71700, 56], [71792, 104], [72000, 56], [72100, 89], [72300, 56], [72408, 72], [72600, 55], [72718, 52], [72900, 55], [73200, 55], [73500, 55], [73800, 55], [74100, 54], [74400, 54], [74700, 54], [75000, 54], [75300, 54], [75600, 54], [75900, 53], [76200, 53], [76500, 53], [76800, 53], [77100, 53], [77400, 52], [77700, 52], [78000, 52], [78300, 52], [78600, 52], [78900, 51], [79200, 51], [79500, 51], [79800, 51], [80100, 51], [80400, 51], [80700, 50], [81000, 50], [81300, 50], [81600, 50], [81900, 50], [82200, 49], [82500, 49], [82800, 49], [83100, 49], [83400, 49], [83700, 49], [84000, 48], [84300, 48], [84600, 48], [84900, 48], [85200, 48], [85500, 47], [85800, 47], [86100, 47], [86400, 47], [86700, 47], [87000, 47], [87300, 46], [87600, 46], [87900, 46], [88200, 46], [88500, 46], [88800, 45], [89100, 45], [89400, 45], [89700, 45]], "point": [149, 149]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan6", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -1.75, "y": 0.9009992, "z": -1.5}, "object_poses": [{"objectName": "DishSponge_1f5d88fd", "position": {"x": -2.31330013, "y": 0.114542246, "z": 1.26794994}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f05a14b3", "position": {"x": -2.45561719, "y": 0.9109421, "z": 1.53077412}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_850ba9bc", "position": {"x": -2.37255049, "y": 0.911448538, "z": 1.2309432}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_0b8e1a97", "position": {"x": -2.37255049, "y": 0.9120294, "z": 0.331450462}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_0b8e1a97", "position": {"x": 1.31852353, "y": 0.9197595, "z": -1.20436275}, "rotation": {"x": 0.0, "y": 225.000031, "z": 0.0}}, {"objectName": "Spatula_3e5dadd0", "position": {"x": -2.538684, "y": 0.925899863, "z": 1.2309432}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_bc458cba", "position": {"x": 1.4123, "y": 0.9295, "z": -0.3359}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_2802642b", "position": {"x": 1.52019107, "y": 0.21585986, "z": 0.59640646}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_2802642b", "position": {"x": -2.37255049, "y": 0.94152844, "z": 0.03161955}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_b0ad5ef5", "position": {"x": -2.63846517, "y": 1.53581285, "z": 0.557547}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_7b0d90af", "position": {"x": -2.45561719, "y": 0.913369656, "z": 0.9311123}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_7b0d90af", "position": {"x": 1.73450017, "y": 1.6591959, "z": 0.01817143}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_b57df01c", "position": {"x": -2.289484, "y": 0.912865162, "z": 1.830605}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_b57df01c", "position": {"x": -2.564922, "y": 1.539577, "z": 2.55014372}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_988570b5", "position": {"x": -0.341296852, "y": 0.9080944, "z": 1.094}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_988570b5", "position": {"x": -2.564922, "y": 1.53480625, "z": 1.48311257}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_e9198ae5", "position": {"x": 1.772001, "y": 1.6587497, "z": -0.263052762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6f8b6e53", "position": {"x": -0.08153126, "y": 0.994053841, "z": 1.31733882}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6f8b6e53", "position": {"x": -0.471179664, "y": 0.994053841, "z": 0.870661139}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_57ff6e2f", "position": {"x": 1.68793631, "y": 0.246842712, "z": 0.706531167}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_57ff6e2f", "position": {"x": -2.55925035, "y": 0.6563896, "z": -0.514033556}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pan_adb25946", "position": {"x": 0.0, "y": 0.9, "z": 0.948}, "rotation": {"x": 0.0, "y": 29.9999466, "z": 0.0}}, {"objectName": "Knife_f4ae1c7a", "position": {"x": -2.34871125, "y": 0.799445331, "z": 0.8572111}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_57ff6e2f", "position": {"x": -2.55925035, "y": 0.6563896, "z": -1.05196643}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_988570b5", "position": {"x": -2.289484, "y": 0.9080944, "z": 1.2309432}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_850ba9bc", "position": {"x": -0.211414054, "y": 0.911448538, "z": 1.76401663}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_209e2ea9", "position": {"x": -0.6010624, "y": 0.996719658, "z": 1.094}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_7b0d90af", "position": {"x": -2.62175035, "y": 0.913369656, "z": 1.53077412}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_2802642b", "position": {"x": -2.43956757, "y": 1.33104134, "z": -0.6021706}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Kettle_0884e440", "position": {"x": 0.8544904, "y": 0.900000036, "z": -1.790359}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_b563bb4b", "position": {"x": -0.341296852, "y": 0.9472332, "z": 0.6473222}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_81e9b3dd", "position": {"x": -2.52156973, "y": 1.53300011, "z": 1.76832032}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6f8b6e53", "position": {"x": -2.538684, "y": 0.994053841, "z": 0.6312814}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_b0ad5ef5", "position": {"x": -2.20641732, "y": 0.9087127, "z": 0.6312814}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_b57df01c", "position": {"x": 1.42669582, "y": 0.9205953, "z": -1.09619033}, "rotation": {"x": 0.0, "y": 45.0000343, "z": 0.0}}, {"objectName": "Spatula_3e5dadd0", "position": {"x": -2.2879, "y": 0.7890956, "z": 1.00087774}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_40552007", "position": {"x": 0.7143881, "y": 1.01518524, "z": -1.42680693}, "rotation": {"x": 0.0, "y": 44.9999161, "z": 0.0}}, {"objectName": "ButterKnife_f05a14b3", "position": {"x": -0.341296852, "y": 0.9109421, "z": 1.31733882}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_e9198ae5", "position": {"x": -2.52156973, "y": 1.53300011, "z": 0.6297735}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_1f5d88fd", "position": {"x": -2.31830549, "y": 0.7729672, "z": 1.32221115}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_bc458cba", "position": {"x": 1.6916, "y": 0.9295, "z": 0.0642}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_0b8e1a97", "position": {"x": 1.512661, "y": 0.9122294, "z": 0.494945318}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9b863229", "position": {"x": -2.61399865, "y": 0.220853329, "z": -0.902157068}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_db7f1139", "position": {"x": -2.39333987, "y": 1.0842129, "z": -0.696573257}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}], "object_toggles": [], "random_seed": 600245325, "scene_num": 6}, "task_id": "trial_T20190918_184629_005371", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A3SFPSMFRSRTB3_33ISQZVXPS3T7NI8C1SR9T1RWMZCC3", "high_descs": ["Turn right, walk ahead to counter on left, face counter.", "Pick up lettuce from back of counter, behind cup.", "Turn right, go to fridge.  Turn right to face fridge.", "Open fridge, put lettuce on top shelf next to white cup.  Close fridge.", "Turn right, go ahead to counter area.  Turn right to face counter with lettuce and bread on it.", "Pick up lettuce slightly above and to the right of the bread.", "Turn around and go to fridge.  Turn right to face fridge.  ", "Open fridge, put lettuce on bottom shelf.  Close fridge."], "task_desc": "Move two heads of lettuce to a fridge.", "votes": [1, 1]}, {"assignment_id": "A1RLO9LNUJIW5S_33PPUNGG3BMALII1MC2U9M1Y57PRZS", "high_descs": ["Turn right and walk to the head of lettuce that's on the counter to your left and when you get to it turn left to face it.", "Pick up the head of lettuce.", "Turn left and walk to the fridge and turn right then take a small step backwards.", "Put the head of lettuce in the freezer then close the door.", "Turn right and walk to the head of lettuce on the counter to your right and when you get there turn right to face it.", "Pick up the head of lettuce that's to the right of the other head of lettuce.", "Walk back to the fridge and face it.", "Put this head of lettuce in the fridge and then close the door."], "task_desc": "Put two heads of lettuce in the fridge.", "votes": [1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3RXPCZQMQSSM1WOFYQZQIO3FQJAG15", "high_descs": ["Turn right and walk towards the wall, then turn left and walk up to the counter.", "Pick up the head of lettuce off of the counter.", "Turn around and take a step forward, then turn right and walk over to the fridge.", "Open the fridge and put the head of lettuce in the upper compartment of the fridge, then close the door.", "Turn right and walk over to the kitchen island.", "Pick up the head of lettuce that is closest to the loaf of bread on the kitchen island.", "Turn right and walk over to the fridge.", "Open the fridge and put the head of lettuce in the lower compartment of the fridge, then close the door."], "task_desc": "Move two heads of lettuce into the fridge.", "votes": [1, 1]}]}}