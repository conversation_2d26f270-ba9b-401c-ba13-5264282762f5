{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000281.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000282.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000283.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000284.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000285.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000286.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000287.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000288.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000289.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000290.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000291.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000292.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000293.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000294.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000295.png", "low_idx": 44}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "GarbageCan", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["egg"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|4|7|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [5.26249264, 5.26249264, 7.36129904, 7.36129904, 3.9194432, 3.9194432]], "forceVisible": true, "objectId": "Egg|+01.32|+00.98|+01.84"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|6|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|4|11|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "garbagecan"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [5.26249264, 5.26249264, 7.36129904, 7.36129904, 3.9194432, 3.9194432]], "coordinateReceptacleObjectId": ["GarbageCan", [5.80777024, 5.80777024, 11.65005396, 11.65005396, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|+01.32|+00.98|+01.84", "receptacleObjectId": "GarbageCan|+01.45|+00.00|+02.91"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+01.32|+00.98|+01.84"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [120, 155, 149, 173], "mask": [[46329, 10], [46627, 15], [46925, 19], [47223, 23], [47522, 25], [47821, 27], [48121, 28], [48420, 29], [48720, 29], [49020, 30], [49320, 29], [49620, 29], [49920, 29], [50221, 27], [50522, 25], [50823, 23], [51124, 20], [51425, 17], [51728, 12]], "point": [134, 163]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+01.32|+00.98|+01.84", "placeStationary": true, "receptacleObjectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 123], [10930, 127], [11100, 122], [11231, 126], [11400, 120], [11532, 125], [11700, 120], [11833, 124], [12000, 119], [12134, 123], [12300, 118], [12435, 122], [12600, 118], [12735, 122], [12900, 117], [13036, 120], [13200, 117], [13336, 120], [13500, 116], [13637, 119], [13800, 116], [13937, 119], [14100, 116], [14238, 118], [14400, 115], [14538, 118], [14700, 115], [14838, 118], [15000, 115], [15139, 117], [15300, 115], [15439, 117], [15600, 114], [15739, 117], [15900, 114], [16039, 116], [16200, 114], [16339, 116], [16500, 114], [16640, 115], [16800, 114], [16940, 115], [17100, 114], [17240, 115], [17400, 114], [17540, 115], [17700, 114], [17840, 115], [18000, 114], [18140, 115], [18300, 114], [18440, 115], [18600, 114], [18740, 115], [18900, 115], [19039, 115], [19200, 115], [19339, 115], [19500, 115], [19639, 115], [19800, 116], [19939, 115], [20100, 116], [20238, 116], [20400, 116], [20538, 116], [20700, 117], [20837, 117], [21000, 118], [21137, 117], [21300, 118], [21436, 118], [21600, 119], [21735, 119], [21900, 120], [22035, 118], [22200, 121], [22334, 119], [22500, 122], [22632, 121], [22800, 124], [22931, 122], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [139, 63]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+01.32|+00.98|+01.84"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [114, 37, 139, 77], "mask": [[10923, 7], [11222, 9], [11520, 12], [11820, 13], [12119, 15], [12418, 17], [12718, 17], [13017, 19], [13317, 19], [13616, 21], [13916, 21], [14216, 22], [14515, 23], [14815, 23], [15115, 24], [15415, 24], [15714, 25], [16014, 25], [16314, 25], [16614, 26], [16914, 26], [17214, 26], [17514, 26], [17814, 26], [18114, 26], [18414, 26], [18714, 26], [19015, 24], [19315, 24], [19615, 24], [19916, 23], [20216, 22], [20516, 22], [20817, 20], [21118, 19], [21418, 18], [21719, 16], [22020, 15], [22321, 13], [22622, 10], [22924, 7]], "point": [126, 56]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+01.32|+00.98|+01.84", "placeStationary": true, "receptacleObjectId": "GarbageCan|+01.45|+00.00|+02.91"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [64, 137, 150, 241], "mask": [[40886, 55], [41182, 62], [41479, 69], [41778, 70], [42077, 72], [42376, 74], [42675, 75], [42975, 76], [43275, 76], [43575, 76], [43874, 77], [44174, 77], [44474, 77], [44774, 77], [45074, 77], [45374, 77], [45673, 78], [45973, 78], [46273, 78], [46573, 78], [46873, 78], [47173, 78], [47472, 79], [47772, 79], [48072, 79], [48372, 79], [48672, 79], [48971, 80], [49271, 80], [49571, 80], [49871, 80], [50171, 80], [50471, 80], [50770, 81], [51070, 81], [51370, 81], [51670, 81], [51970, 81], [52270, 81], [52569, 82], [52869, 82], [53169, 82], [53469, 82], [53769, 82], [54069, 82], [54368, 83], [54668, 83], [54968, 83], [55268, 83], [55568, 83], [55868, 83], [56167, 84], [56467, 84], [56767, 84], [57067, 84], [57367, 84], [57667, 47], [57718, 33], [57966, 45], [58020, 31], [58266, 44], [58322, 29], [58566, 43], [58623, 28], [58866, 43], [58924, 27], [59166, 42], [59224, 27], [59466, 42], [59525, 26], [59765, 42], [59825, 26], [60065, 42], [60125, 26], [60365, 42], [60425, 26], [60665, 42], [60725, 26], [60965, 42], [61025, 26], [61265, 42], [61325, 26], [61564, 43], [61625, 26], [61864, 44], [61924, 27], [62164, 44], [62224, 27], [62464, 45], [62523, 28], [62764, 46], [62822, 8], [62835, 16], [63064, 48], [63120, 8], [63138, 13], [63365, 61], [63439, 11], [63665, 60], [63739, 11], [63965, 84], [64266, 82], [64566, 82], [64868, 80], [65170, 78], [65473, 74], [65775, 72], [66077, 69], [66379, 67], [66682, 63], [66985, 59], [67287, 57], [67589, 54], [67891, 52], [68193, 50], [68494, 48], [68795, 47], [69095, 47], [69396, 46], [69697, 45], [69998, 44], [70298, 44], [70599, 43], [70900, 41], [71200, 41], [71501, 39], [71801, 38], [72104, 33]], "point": [107, 188]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan12", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": 0.0, "y": 0.9009999, "z": -1.0}, "object_poses": [{"objectName": "Pan_9f7abbea", "position": {"x": -0.971269131, "y": 1.31882882, "z": 0.195651278}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Potato_22312ae0", "position": {"x": -0.876905, "y": 0.9705572, "z": 0.8433119}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": 1.66228318, "y": 1.94812226, "z": 1.83063245}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": -0.7937801, "y": 0.9379421, "z": 1.15406442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": -1.043155, "y": 0.9379421, "z": 0.9986881}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": -0.689003944, "y": 0.7479664, "z": 0.914999962}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": 1.275738, "y": 0.7479664, "z": 0.780662537}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_733b0828", "position": {"x": 1.76781189, "y": 0.9528998, "z": 1.45886016}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -1.109, "y": 0.9307289, "z": 1.8657}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": 1.31562316, "y": 0.9798608, "z": 1.84032476}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": -0.96003, "y": 0.9730633, "z": 1.0763762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": -1.0813, "y": 1.499687, "z": 2.61317515}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": 1.63003218, "y": 1.49968576, "z": 2.52613354}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": -0.7236026, "y": 0.7685599, "z": 0.779062569}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": -1.11600673, "y": 1.82511055, "z": -0.08169362}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": 1.39171886, "y": 0.07861984, "z": 2.532645}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": -1.043155, "y": 0.9342062, "z": 0.765623748}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -1.0753144, "y": 1.9462316, "z": 2.01923466}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -1.17972946, "y": 1.94781125, "z": 1.41137326}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": -0.99543643, "y": 1.94650507, "z": 1.36449826}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": -0.988836765, "y": 0.8101194, "z": 0.118999906}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": -1.209405, "y": 0.9871304, "z": 1.0763762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": 1.48997676, "y": 0.119435936, "z": 2.945116}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_f3700d17", "position": {"x": 1.45894945, "y": 1.01440716, "z": 1.67712712}, "rotation": {"x": 3.773014, "y": 58.90235, "z": 356.164642}}, {"objectName": "SoapBottle_09f78d94", "position": {"x": 1.66985226, "y": 0.9372058, "z": 0.8063456}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_20fd7fed", "position": {"x": -0.9230004, "y": 0.990678847, "z": 2.26549959}, "rotation": {"x": -6.68646e-05, "y": 302.470367, "z": 6.266839e-05}}, {"objectName": "Fork_d40bfead", "position": {"x": -1.20940506, "y": 0.938448548, "z": 0.921}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -0.9712682, "y": 1.56053746, "z": -0.110954016}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": 1.41990554, "y": 1.74313784, "z": -0.03568393}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -1.109, "y": 0.9307289, "z": 1.4704}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_733b0828", "position": {"x": 1.41413271, "y": 0.7618369, "z": 1.05253744}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": -0.689004064, "y": 0.7685599, "z": 2.44906855}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": -0.712255, "y": 0.9342062, "z": 2.159162}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": 1.39087415, "y": 0.119435996, "z": 2.84730816}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -0.791773558, "y": 0.08057672, "z": 1.05594587}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": -0.793779969, "y": 0.9379421, "z": 0.921}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_22312ae0", "position": {"x": 1.28540039, "y": 0.9705572, "z": 2.31}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": -0.804073453, "y": 0.0866650939, "z": 2.456895}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": -1.211005, "y": 0.937464237, "z": 2.3864}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": -0.876905, "y": 0.9730633, "z": 0.9986881}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": 1.67011023, "y": 1.499687, "z": 2.27798438}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_9f7abbea", "position": {"x": 1.3781, "y": 1.73753989, "z": 0.3929}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": -0.96002996, "y": 0.9390294, "z": 0.7656238}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": 1.52899969, "y": 1.00298607, "z": 2.34560037}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}], "object_toggles": [], "random_seed": 515011566, "scene_num": 12}, "task_id": "trial_T20190908_181854_326947", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1MKCTVKE7J0ZP_37C0GNLMHIUMHN1JYEZG8LB1FNMD65", "high_descs": ["Turn right and go forward, then turn right to face the sink.", "Pick up the egg at the sink.", "Look up above the sink, to the microwave.", "Open the microwave, put the egg inside and heat it, then take the egg out.", "Turn left and go to the trash bin at the end of the counter.", "Put the egg in the trash bin."], "task_desc": "Put a warm egg in the trash.", "votes": [1, 1, 1, 1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_3ND9UOO81NT39E4H2DA8PSIJCYGLWT", "high_descs": ["turn left and walk to the kitchen sink on the far end right side of the room", "grab an egg off of the kitchen sink there", "move to the right a bit and then face the microwave above the sink", "place the egg inside the sink to cook it, then take it back out once it is cooked", "move to the left side of the kitchen counter space, and face the end of the counter space there", "place the egg inside of the garbage bin on the ground in front of you"], "task_desc": "place a cooked egg inside the garbage bin", "votes": [1, 1, 1, 1, 1]}, {"assignment_id": "A31681CCEVDIH3_3W8CV64QJ5QQBM8JIHLIJGH1XOLH97", "high_descs": ["Turn right to walk to the sink.", "Pick up the egg from the sink.", "Turn upwards to face the microwave.", "Heat the egg in the microwave, removing it afterwards.", "Make a left to reach the trash can in the corner.", "Put the egg in the trash can."], "task_desc": "Microwave an egg to throw it away.", "votes": [1, 1]}]}}