{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000265.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000266.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000267.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000268.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000269.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000270.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000271.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000272.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000273.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000274.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000275.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000276.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000277.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000278.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000279.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000280.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000281.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000282.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000283.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000284.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000285.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000286.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000287.png", "low_idx": 54}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON>g", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|0|-9|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["mug"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON>g", [2.832319736, 2.832319736, -8.7635, -8.7635, 2.641310932, 2.641310932]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [2.72, 2.72, -7.832, -7.832, -0.1252121924, -0.1252121924]], "forceVisible": true, "objectId": "Mug|+00.71|+00.66|-02.19"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-8|-11|2|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["mug"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-8.052, -8.052, -14.756, -14.756, 2.761099816, 2.761099816]], "forceVisible": true, "objectId": "Microwave|-02.01|+00.69|-03.69"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-2|-2|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["mug", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON>g", [2.832319736, 2.832319736, -8.7635, -8.7635, 2.641310932, 2.641310932]], "coordinateReceptacleObjectId": ["Cabinet", [2.0289164, 2.0289164, 0.627202988, 0.627202988, 1.383646, 1.383646]], "forceVisible": true, "objectId": "Mug|+00.71|+00.66|-02.19", "receptacleObjectId": "Cabinet|+00.51|+00.35|+00.16"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|+00.68|-00.03|-01.96"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 219, 232], "mask": [[0, 220], [300, 219], [600, 219], [900, 219], [1200, 219], [1500, 219], [1800, 218], [2100, 218], [2400, 218], [2700, 218], [3000, 217], [3300, 217], [3600, 217], [3900, 217], [4200, 216], [4500, 216], [4800, 216], [5100, 216], [5400, 216], [5700, 215], [6000, 215], [6300, 215], [6600, 215], [6900, 214], [7200, 214], [7500, 214], [7800, 214], [8100, 213], [8400, 213], [8700, 213], [9000, 213], [9300, 213], [9600, 212], [9900, 212], [10200, 212], [10500, 212], [10800, 211], [11100, 211], [11400, 211], [11700, 211], [12000, 210], [12300, 210], [12600, 210], [12900, 210], [13200, 209], [13500, 209], [13800, 209], [14100, 209], [14400, 209], [14700, 208], [15000, 208], [15300, 208], [15600, 208], [15900, 207], [16200, 207], [16500, 207], [16800, 207], [17100, 206], [17400, 206], [17700, 206], [18000, 206], [18300, 206], [18600, 205], [18900, 205], [19200, 205], [19500, 205], [19800, 204], [20100, 204], [20400, 204], [20700, 204], [21000, 203], [21300, 203], [21600, 203], [21900, 203], [22200, 203], [22500, 202], [22800, 202], [23100, 202], [23400, 202], [23700, 201], [24000, 201], [24300, 201], [24600, 201], [24900, 200], [25200, 200], [25500, 201], [25800, 201], [26100, 201], [26400, 201], [26700, 201], [27000, 201], [27300, 201], [27600, 201], [27900, 201], [28200, 201], [28500, 201], [28800, 201], [29100, 201], [29400, 201], [29700, 200], [30000, 200], [30300, 200], [30600, 200], [30900, 199], [31200, 199], [31500, 199], [31800, 198], [32100, 197], [32400, 197], [32700, 196], [33000, 196], [33300, 195], [33600, 194], [33900, 194], [34200, 193], [34500, 193], [34800, 193], [35100, 192], [35400, 192], [35700, 192], [36000, 192], [36300, 192], [36600, 191], [36900, 191], [37200, 191], [37500, 191], [37800, 190], [38100, 190], [38400, 190], [38700, 190], [39000, 189], [39300, 189], [39600, 189], [39900, 189], [40200, 189], [40500, 188], [40800, 188], [41100, 188], [41400, 188], [41700, 187], [42000, 187], [42300, 187], [42600, 187], [42900, 186], [43200, 186], [43500, 186], [43800, 186], [44100, 185], [44400, 185], [44700, 185], [45000, 185], [45300, 185], [45600, 184], [45900, 184], [46200, 184], [46500, 184], [46800, 183], [47100, 183], [47400, 183], [47700, 183], [48000, 182], [48300, 182], [48600, 182], [48900, 182], [49200, 182], [49500, 181], [49800, 181], [50100, 181], [50400, 181], [50700, 180], [51000, 180], [51300, 180], [51600, 180], [51900, 179], [52200, 179], [52500, 179], [52800, 179], [53100, 179], [53400, 178], [53700, 178], [54000, 178], [54300, 178], [54600, 177], [54900, 177], [55200, 177], [55500, 177], [55800, 176], [56100, 176], [56400, 176], [56700, 176], [57000, 176], [57300, 175], [57600, 175], [57900, 175], [58200, 175], [58502, 172], [58803, 171], [59104, 170], [59406, 168], [59707, 166], [60009, 164], [60310, 163], [60612, 161], [60913, 159], [61214, 158], [61516, 156], [61817, 155], [62119, 153], [62420, 152], [62722, 150], [63023, 149], [63324, 148], [63626, 146], [63927, 145], [64229, 142], [64529, 142], [64828, 142], [65128, 142], [65428, 142], [65730, 140], [66031, 139], [66333, 136], [66635, 133], [66938, 127], [67241, 118], [67544, 111], [67848, 102], [68153, 92], [68458, 80], [68765, 66], [69072, 49], [69382, 18]], "point": [109, 115]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Mug|+00.71|+00.66|-02.19"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [125, 100, 145, 118], "mask": [[29825, 21], [30125, 21], [30425, 21], [30725, 21], [31025, 21], [31326, 20], [31626, 20], [31926, 20], [32226, 20], [32526, 20], [32827, 19], [33127, 19], [33427, 19], [33727, 18], [34028, 17], [34328, 16], [34629, 14], [34930, 12], [35232, 8]], "point": [135, 108]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|+00.68|-00.03|-01.96"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 209, 300], "mask": [[0, 210], [300, 209], [600, 209], [900, 209], [1200, 209], [1500, 209], [1800, 208], [2100, 208], [2400, 208], [2700, 208], [3000, 208], [3300, 207], [3600, 207], [3900, 207], [4200, 207], [4500, 207], [4800, 206], [5100, 206], [5400, 206], [5700, 206], [6000, 206], [6300, 205], [6600, 205], [6900, 205], [7200, 205], [7500, 205], [7800, 204], [8100, 204], [8400, 204], [8700, 204], [9000, 204], [9300, 203], [9600, 203], [9900, 203], [10200, 203], [10500, 203], [10800, 202], [11100, 202], [11400, 202], [11700, 202], [12000, 202], [12300, 201], [12600, 201], [12900, 201], [13200, 201], [13500, 201], [13800, 200], [14100, 200], [14400, 200], [14700, 200], [15000, 200], [15300, 199], [15600, 199], [15900, 199], [16200, 199], [16500, 199], [16800, 198], [17100, 198], [17400, 198], [17700, 198], [18000, 198], [18300, 197], [18600, 197], [18900, 197], [19200, 197], [19500, 197], [19800, 196], [20100, 196], [20400, 196], [20700, 196], [21000, 196], [21300, 195], [21600, 195], [21900, 195], [22200, 195], [22500, 195], [22800, 194], [23100, 194], [23400, 194], [23700, 194], [24000, 194], [24300, 193], [24600, 193], [24900, 193], [25200, 193], [25500, 193], [25800, 193], [26100, 192], [26400, 192], [26700, 192], [27000, 192], [27300, 192], [27600, 191], [27900, 191], [28200, 191], [28500, 191], [28800, 191], [29100, 190], [29400, 190], [29700, 190], [30000, 190], [30300, 190], [30600, 189], [30900, 189], [31200, 189], [31500, 189], [31800, 189], [32100, 188], [32400, 188], [32700, 188], [33000, 188], [33300, 188], [33600, 187], [33900, 187], [34200, 187], [34500, 187], [34800, 187], [35100, 186], [35400, 186], [35700, 186], [36000, 186], [36300, 186], [36600, 185], [36900, 185], [37200, 185], [37500, 185], [37800, 185], [38100, 184], [38400, 184], [38700, 184], [39000, 184], [39300, 184], [39600, 183], [39900, 183], [40200, 183], [40500, 183], [40800, 183], [41100, 182], [41400, 182], [41700, 182], [42000, 182], [42300, 182], [42600, 181], [42900, 181], [43200, 181], [43500, 181], [43800, 181], [44100, 180], [44400, 180], [44700, 180], [45000, 180], [45300, 180], [45600, 179], [45900, 179], [46200, 179], [46500, 179], [46800, 179], [47100, 178], [47400, 178], [47700, 178], [48000, 178], [48300, 178], [48600, 177], [48900, 177], [49200, 177], [49500, 177], [49800, 177], [50100, 176], [50400, 176], [50700, 176], [51000, 176], [51300, 176], [51600, 175], [51900, 175], [52200, 175], [52500, 175], [52800, 175], [53100, 2], [53103, 171], [53400, 3], [53404, 170], [53700, 174], [54000, 174], [54300, 174], [54600, 8], [54609, 164], [54900, 9], [54910, 163], [55200, 173], [55500, 173], [55800, 173], [56100, 14], [56115, 157], [56400, 15], [56416, 156], [56700, 172], [57000, 172], [57300, 172], [57600, 20], [57621, 150], [57900, 21], [57922, 149], [58200, 171], [58500, 171], [58800, 171], [59100, 26], [59127, 143], [59400, 27], [59428, 142], [59700, 170], [60000, 170], [60300, 170], [60600, 170], [60900, 170], [61200, 170], [61500, 170], [61800, 170], [62100, 170], [62400, 146], [62554, 16], [62700, 140], [62860, 10], [63000, 137], [63163, 7], [63300, 135], [63465, 5], [63600, 132], [63768, 2], [63900, 130], [64200, 129], [64500, 128], [64800, 126], [65100, 125], [65400, 124], [65700, 28], [65730, 93], [66000, 27], [66031, 91], [66300, 27], [66333, 88], [66600, 27], [66635, 85], [66900, 27], [66938, 81], [67200, 26], [67241, 78], [67500, 26], [67544, 74], [67800, 26], [67848, 69], [68100, 26], [68153, 63], [68400, 25], [68458, 58], [68700, 25], [68765, 50], [69000, 25], [69072, 43], [69300, 25], [69382, 18], [69600, 24], [69900, 24], [70200, 24], [70500, 24], [70800, 24], [71100, 23], [71400, 23], [71700, 23], [72000, 23], [72300, 22], [72600, 22], [72900, 22], [73200, 22], [73500, 21], [73800, 21], [74100, 21], [74400, 21], [74700, 20], [75000, 20], [75300, 20], [75600, 20], [75900, 19], [76200, 19], [76500, 19], [76800, 19], [77100, 18], [77400, 18], [77700, 18], [78000, 18], [78300, 17], [78600, 17], [78900, 17], [79200, 17], [79500, 16], [79800, 16], [80100, 16], [80400, 16], [80700, 15], [81000, 15], [81300, 15], [81600, 15], [81900, 14], [82200, 14], [82500, 14], [82800, 14], [83100, 13], [83400, 13], [83700, 13], [84000, 13], [84300, 13], [84600, 12], [84900, 12], [85200, 12], [85500, 12], [85800, 11], [86100, 11], [86400, 11], [86700, 11], [87000, 10], [87300, 10], [87600, 10], [87900, 10], [88200, 9], [88500, 9], [88800, 9], [89100, 9], [89400, 8], [89700, 8]], "point": [104, 149]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|+00.71|+00.66|-02.19", "placeStationary": true, "receptacleObjectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 42, 242, 253], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25861, 181], [26160, 181], [26459, 182], [26759, 182], [27059, 182], [27358, 182], [27658, 182], [27957, 183], [28257, 182], [28556, 183], [28854, 185], [29154, 185], [29453, 185], [29752, 186], [30052, 186], [30351, 187], [30651, 187], [30950, 188], [31250, 188], [31550, 188], [31849, 189], [32149, 189], [32448, 190], [32748, 191], [33047, 192], [33347, 192], [33646, 193], [33946, 192], [34245, 193], [34545, 193], [34844, 194], [35144, 194], [35443, 194], [35743, 194], [36043, 194], [36342, 195], [36642, 194], [36941, 195], [37241, 195], [37540, 195], [37840, 195], [38139, 196], [38439, 195], [38738, 196], [39038, 196], [39337, 197], [39637, 196], [39936, 197], [40236, 197], [40536, 196], [40835, 197], [41135, 197], [41434, 197], [41734, 197], [42033, 198], [42333, 198], [42632, 198], [42932, 198], [43231, 199], [43531, 198], [43830, 199], [44130, 199], [44429, 199], [44729, 199], [45029, 199], [45328, 200], [45628, 199], [45927, 200], [46227, 195], [46526, 197], [46826, 196], [47125, 197], [47425, 197], [47724, 60], [47785, 136], [48024, 60], [48085, 136], [48323, 61], [48385, 136], [48623, 61], [48922, 61], [49222, 61], [49522, 61], [49821, 62], [50121, 61], [50420, 62], [50720, 62], [51019, 63], [51319, 62], [51618, 63], [51918, 63], [52217, 64], [52517, 63], [52816, 64], [53116, 64], [53415, 65], [53715, 64], [54015, 64], [54314, 65], [54614, 65], [54913, 66], [55213, 65], [55512, 66], [55812, 66], [56111, 67], [56411, 66], [56710, 67], [57000, 3], [57010, 67], [57300, 3], [57309, 68], [57600, 3], [57609, 67], [57900, 4], [57908, 68], [58200, 76], [58500, 76], [58800, 75], [59100, 75], [59400, 75], [59700, 75], [60000, 74], [60300, 74], [60600, 74], [60901, 73], [61203, 70], [61503, 70], [61802, 71], [62102, 71], [62400, 1], [62402, 70], [62700, 72], [63000, 72], [63300, 72], [63600, 71], [63900, 71], [64200, 71], [64500, 71], [64800, 70], [65100, 70], [65400, 70], [65700, 70], [66000, 69], [66300, 69], [66600, 69], [66900, 69], [67200, 69], [67500, 68], [67800, 68], [68101, 67], [68402, 66], [68703, 64], [69004, 63], [69304, 63], [69605, 62], [69906, 60], [70207, 59], [70508, 58], [70809, 57], [71109, 56], [71410, 55], [71711, 54], [72012, 53], [72313, 51], [72613, 51], [72914, 50], [73215, 49], [73517, 46], [73819, 44], [74123, 42], [74427, 8], [74436, 30], [74730, 3], [74739, 28], [75043, 19], [75063, 3], [75345, 17], [75646, 16]], "point": [121, 146]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 42, 242, 253], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25861, 181], [26160, 181], [26459, 182], [26759, 182], [27059, 182], [27358, 182], [27658, 182], [27957, 183], [28257, 182], [28556, 183], [28854, 185], [29154, 185], [29453, 185], [29752, 186], [30052, 186], [30351, 187], [30651, 187], [30950, 95], [31056, 82], [31250, 93], [31358, 80], [31550, 92], [31659, 79], [31849, 92], [31960, 78], [32149, 91], [32261, 77], [32448, 92], [32561, 77], [32748, 92], [32863, 76], [33047, 93], [33165, 74], [33347, 93], [33461, 1], [33465, 74], [33646, 94], [33761, 3], [33766, 73], [33946, 94], [34061, 3], [34066, 72], [34245, 95], [34361, 3], [34366, 72], [34545, 95], [34661, 3], [34666, 72], [34844, 96], [34960, 3], [34965, 73], [35144, 97], [35260, 3], [35265, 73], [35443, 98], [35560, 3], [35565, 72], [35743, 98], [35860, 2], [35864, 73], [36043, 98], [36160, 2], [36164, 73], [36342, 99], [36460, 1], [36463, 74], [36642, 99], [36762, 74], [36941, 100], [37061, 75], [37241, 100], [37360, 76], [37540, 101], [37660, 75], [37840, 101], [37960, 75], [38139, 102], [38259, 76], [38439, 103], [38559, 75], [38738, 104], [38859, 75], [39038, 105], [39158, 76], [39337, 107], [39457, 77], [39637, 109], [39755, 78], [39936, 114], [40051, 82], [40236, 197], [40536, 196], [40835, 197], [41135, 197], [41434, 197], [41734, 197], [42033, 198], [42333, 198], [42632, 198], [42932, 198], [43231, 199], [43531, 198], [43830, 199], [44130, 199], [44429, 199], [44729, 199], [45029, 199], [45328, 200], [45628, 199], [45927, 200], [46227, 195], [46526, 197], [46826, 196], [47125, 197], [47425, 197], [47724, 60], [47785, 136], [48024, 60], [48085, 136], [48323, 61], [48385, 136], [48623, 61], [48922, 61], [49222, 61], [49522, 61], [49821, 62], [50121, 61], [50420, 62], [50720, 62], [51019, 63], [51319, 62], [51618, 63], [51918, 63], [52217, 64], [52517, 63], [52816, 64], [53116, 64], [53415, 65], [53715, 64], [54015, 64], [54314, 65], [54614, 65], [54913, 66], [55213, 65], [55512, 66], [55812, 66], [56111, 67], [56411, 66], [56710, 67], [57000, 3], [57010, 67], [57300, 3], [57309, 68], [57600, 3], [57609, 67], [57900, 4], [57908, 68], [58200, 76], [58500, 76], [58800, 75], [59100, 75], [59400, 75], [59700, 75], [60000, 74], [60300, 74], [60600, 74], [60901, 73], [61203, 70], [61503, 70], [61802, 71], [62102, 71], [62400, 1], [62402, 70], [62700, 72], [63000, 72], [63300, 72], [63600, 71], [63900, 71], [64200, 71], [64500, 71], [64800, 70], [65100, 70], [65400, 70], [65700, 70], [66000, 69], [66300, 69], [66600, 69], [66900, 69], [67200, 69], [67500, 68], [67800, 68], [68101, 67], [68402, 66], [68703, 64], [69004, 63], [69304, 63], [69605, 62], [69906, 60], [70207, 59], [70508, 58], [70809, 57], [71109, 56], [71410, 55], [71711, 54], [72012, 53], [72313, 51], [72613, 51], [72914, 50], [73215, 49], [73517, 46], [73819, 44], [74123, 42], [74427, 8], [74436, 30], [74730, 3], [74739, 28], [75043, 19], [75063, 3], [75345, 17], [75646, 16]], "point": [121, 146]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-8.052, -8.052, -14.756, -14.756, 2.761099816, 2.761099816]], "forceVisible": true, "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-8.052, -8.052, -14.756, -14.756, 2.761099816, 2.761099816]], "forceVisible": true, "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Mug|+00.71|+00.66|-02.19"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [140, 104, 165, 134], "mask": [[31045, 11], [31343, 15], [31642, 17], [31941, 19], [32240, 21], [32540, 21], [32840, 23], [33140, 25], [33440, 21], [33462, 3], [33740, 21], [33764, 2], [34040, 21], [34064, 2], [34340, 21], [34364, 2], [34640, 21], [34664, 2], [34940, 20], [34963, 2], [35241, 19], [35263, 2], [35541, 19], [35563, 2], [35841, 19], [35862, 2], [36141, 19], [36162, 2], [36441, 19], [36461, 2], [36741, 21], [37041, 20], [37341, 19], [37641, 19], [37941, 19], [38241, 18], [38542, 17], [38842, 17], [39143, 15], [39444, 13], [39746, 9], [40050, 1]], "point": [152, 118]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 42, 242, 253], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25861, 181], [26160, 181], [26459, 182], [26759, 182], [27059, 182], [27358, 182], [27658, 182], [27957, 183], [28257, 182], [28556, 183], [28854, 185], [29154, 185], [29453, 185], [29752, 186], [30052, 186], [30351, 187], [30651, 187], [30950, 188], [31250, 188], [31550, 188], [31849, 189], [32149, 189], [32448, 190], [32748, 191], [33047, 192], [33347, 192], [33646, 193], [33946, 192], [34245, 193], [34545, 193], [34844, 194], [35144, 194], [35443, 194], [35743, 194], [36043, 194], [36342, 195], [36642, 194], [36941, 195], [37241, 195], [37540, 195], [37840, 195], [38139, 196], [38439, 195], [38738, 196], [39038, 196], [39337, 197], [39637, 196], [39936, 197], [40236, 197], [40536, 196], [40835, 197], [41135, 197], [41434, 197], [41734, 197], [42033, 198], [42333, 198], [42632, 198], [42932, 198], [43231, 199], [43531, 198], [43830, 199], [44130, 199], [44429, 199], [44729, 199], [45029, 199], [45328, 200], [45628, 199], [45927, 200], [46227, 195], [46526, 197], [46826, 196], [47125, 197], [47425, 197], [47724, 60], [47785, 136], [48024, 60], [48085, 136], [48323, 61], [48385, 136], [48623, 61], [48922, 61], [49222, 61], [49522, 61], [49821, 62], [50121, 61], [50420, 62], [50720, 62], [51019, 63], [51319, 62], [51618, 63], [51918, 63], [52217, 64], [52517, 63], [52816, 64], [53116, 64], [53415, 65], [53715, 64], [54015, 64], [54314, 65], [54614, 65], [54913, 66], [55213, 65], [55512, 66], [55812, 66], [56111, 67], [56411, 66], [56710, 67], [57000, 3], [57010, 67], [57300, 3], [57309, 68], [57600, 3], [57609, 67], [57900, 4], [57908, 68], [58200, 76], [58500, 76], [58800, 75], [59100, 75], [59400, 75], [59700, 75], [60000, 74], [60300, 74], [60600, 74], [60901, 73], [61203, 70], [61503, 70], [61802, 71], [62102, 71], [62400, 1], [62402, 70], [62700, 72], [63000, 72], [63300, 72], [63600, 71], [63900, 71], [64200, 71], [64500, 71], [64800, 70], [65100, 70], [65400, 70], [65700, 70], [66000, 69], [66300, 69], [66600, 69], [66900, 69], [67200, 69], [67500, 68], [67800, 68], [68101, 67], [68402, 66], [68703, 64], [69004, 63], [69304, 63], [69605, 62], [69906, 60], [70207, 59], [70508, 58], [70809, 57], [71109, 56], [71410, 55], [71711, 54], [72012, 53], [72313, 51], [72613, 51], [72914, 50], [73215, 49], [73517, 46], [73819, 44], [74123, 42], [74427, 8], [74436, 30], [74730, 3], [74739, 28], [75043, 19], [75063, 3], [75345, 17], [75646, 16]], "point": [121, 146]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|+00.51|+00.35|+00.16"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [19, 126, 135, 207], "mask": [[37519, 113], [37819, 113], [38119, 113], [38420, 112], [38720, 112], [39021, 111], [39321, 111], [39622, 110], [39922, 110], [40223, 109], [40523, 109], [40823, 109], [41124, 108], [41424, 108], [41725, 108], [42025, 108], [42326, 107], [42626, 107], [42927, 106], [43227, 106], [43528, 105], [43828, 105], [44129, 104], [44429, 104], [44729, 104], [45030, 103], [45330, 103], [45631, 102], [45931, 102], [46232, 102], [46532, 102], [46833, 101], [47133, 101], [47434, 100], [47734, 100], [48035, 99], [48335, 99], [48635, 99], [48936, 98], [49236, 98], [49537, 97], [49837, 97], [50138, 96], [50438, 96], [50739, 95], [51039, 96], [51340, 95], [51640, 95], [51941, 94], [52241, 94], [52541, 94], [52842, 93], [53142, 93], [53443, 92], [53743, 92], [54044, 91], [54344, 91], [54645, 90], [54945, 90], [55246, 89], [55546, 90], [55847, 89], [56147, 89], [56448, 88], [56748, 88], [57048, 88], [57349, 87], [57649, 87], [57950, 86], [58250, 86], [58551, 85], [58851, 82], [59152, 79], [59452, 77], [59753, 75], [60053, 73], [60354, 71], [60654, 70], [60954, 69], [61255, 67], [61555, 66], [61887, 33]], "point": [77, 165]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|+00.71|+00.66|-02.19", "placeStationary": true, "receptacleObjectId": "Cabinet|+00.51|+00.35|+00.16"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 126, 132, 294], "mask": [[37518, 2], [37817, 3], [37823, 104], [38117, 4], [38122, 105], [38416, 5], [38423, 104], [38716, 6], [38723, 104], [39015, 7], [39024, 104], [39315, 8], [39324, 104], [39614, 9], [39624, 104], [39913, 10], [39925, 103], [40213, 11], [40225, 103], [40512, 12], [40526, 102], [40812, 13], [40826, 102], [41111, 14], [41127, 101], [41411, 15], [41427, 101], [41710, 16], [41728, 100], [42010, 17], [42028, 100], [42309, 18], [42329, 99], [42608, 20], [42629, 99], [42908, 20], [42929, 100], [43207, 21], [43230, 99], [43507, 22], [43530, 99], [43806, 23], [43831, 98], [44106, 24], [44131, 98], [44405, 25], [44432, 97], [44704, 27], [44732, 97], [45004, 27], [45033, 96], [45303, 29], [45333, 96], [45603, 29], [45633, 96], [45902, 31], [45934, 95], [46202, 31], [46234, 95], [46501, 33], [46535, 95], [46801, 33], [46835, 95], [47100, 34], [47136, 94], [47400, 35], [47436, 94], [47700, 35], [47737, 93], [48000, 36], [48037, 93], [48300, 36], [48338, 92], [48600, 37], [48638, 50], [48691, 39], [48900, 37], [48938, 50], [48992, 38], [49200, 38], [49239, 49], [49292, 38], [49500, 38], [49539, 49], [49593, 37], [49800, 39], [49840, 48], [49893, 37], [50100, 39], [50140, 48], [50194, 37], [50400, 39], [50441, 47], [50494, 37], [50700, 40], [50741, 47], [50794, 37], [51000, 40], [51042, 46], [51095, 36], [51300, 41], [51342, 46], [51395, 36], [51600, 41], [51643, 45], [51695, 36], [51900, 42], [51943, 46], [51995, 36], [52200, 42], [52243, 46], [52294, 37], [52500, 43], [52544, 87], [52800, 43], [52844, 87], [53100, 44], [53145, 86], [53400, 44], [53445, 86], [53700, 45], [53746, 85], [54000, 45], [54046, 86], [54300, 45], [54347, 85], [54600, 46], [54647, 85], [54900, 46], [54948, 84], [55200, 47], [55248, 84], [55500, 47], [55548, 84], [55800, 48], [55849, 83], [56100, 48], [56149, 83], [56400, 49], [56450, 82], [56700, 49], [56750, 82], [57000, 50], [57051, 81], [57300, 50], [57351, 81], [57600, 50], [57652, 81], [57900, 51], [57952, 81], [58200, 51], [58253, 80], [58500, 52], [58553, 80], [58800, 52], [58853, 80], [59100, 53], [59154, 77], [59400, 53], [59454, 75], [59700, 54], [59755, 73], [60000, 54], [60055, 71], [60300, 55], [60356, 69], [60600, 55], [60656, 68], [60900, 55], [60957, 66], [61200, 56], [61257, 65], [61500, 56], [61800, 56], [62100, 56], [62400, 55], [62700, 55], [63000, 55], [63300, 54], [63600, 54], [63900, 54], [64200, 54], [64500, 53], [64800, 53], [65100, 53], [65400, 52], [65700, 52], [66000, 52], [66300, 51], [66600, 51], [66900, 51], [67200, 51], [67500, 50], [67800, 50], [68100, 50], [68400, 49], [68700, 49], [69000, 49], [69300, 49], [69600, 48], [69900, 48], [70200, 48], [70500, 47], [70800, 47], [71100, 47], [71400, 46], [71700, 46], [72000, 46], [72300, 46], [72600, 45], [72900, 45], [73200, 45], [73500, 44], [73800, 44], [74100, 44], [74400, 43], [74700, 43], [75000, 43], [75300, 43], [75600, 42], [75900, 42], [76200, 42], [76500, 41], [76800, 42], [77100, 42], [77400, 41], [77700, 41], [78000, 41], [78300, 41], [78600, 40], [78900, 40], [79200, 40], [79500, 39], [79800, 39], [80100, 39], [80401, 37], [80702, 36], [81003, 35], [81304, 34], [81605, 32], [81906, 31], [82207, 30], [82508, 28], [82809, 27], [83110, 26], [83411, 24], [83712, 23], [84013, 22], [84315, 20], [84616, 18], [84917, 17], [85218, 16], [85519, 14], [85820, 13], [86121, 12], [86422, 11], [86723, 9], [87024, 8], [87325, 6], [87626, 4], [87927, 3]], "point": [66, 204]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|+00.51|+00.35|+00.16"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 126, 133, 294], "mask": [[37518, 2], [37817, 3], [37823, 104], [38117, 4], [38122, 105], [38416, 5], [38423, 104], [38716, 6], [38723, 104], [39015, 7], [39024, 104], [39315, 8], [39324, 104], [39614, 9], [39624, 104], [39913, 10], [39925, 103], [40213, 11], [40225, 103], [40512, 12], [40526, 102], [40812, 13], [40826, 102], [41111, 14], [41127, 101], [41411, 15], [41427, 101], [41710, 16], [41728, 100], [42010, 17], [42028, 100], [42309, 18], [42329, 99], [42608, 20], [42629, 99], [42908, 20], [42929, 100], [43207, 21], [43230, 99], [43507, 22], [43530, 99], [43806, 23], [43831, 98], [44106, 24], [44131, 98], [44405, 25], [44432, 97], [44704, 27], [44732, 97], [45004, 27], [45033, 96], [45303, 29], [45333, 96], [45603, 29], [45633, 96], [45902, 31], [45934, 95], [46202, 31], [46234, 95], [46501, 33], [46535, 95], [46801, 33], [46835, 95], [47100, 34], [47136, 94], [47400, 35], [47436, 94], [47700, 35], [47737, 93], [48000, 36], [48037, 93], [48300, 36], [48338, 92], [48600, 37], [48638, 50], [48691, 39], [48900, 37], [48938, 50], [48992, 38], [49200, 38], [49239, 49], [49292, 38], [49500, 38], [49539, 49], [49593, 37], [49800, 39], [49840, 48], [49893, 37], [50100, 39], [50140, 48], [50194, 37], [50400, 39], [50441, 47], [50494, 37], [50700, 40], [50741, 47], [50794, 37], [51000, 40], [51042, 46], [51095, 36], [51300, 41], [51342, 46], [51395, 36], [51600, 41], [51643, 45], [51695, 36], [51900, 42], [51943, 46], [51995, 36], [52200, 42], [52243, 46], [52294, 37], [52500, 43], [52544, 87], [52800, 43], [52844, 87], [53100, 44], [53145, 75], [53228, 3], [53400, 44], [53445, 74], [53529, 2], [53700, 45], [53746, 72], [53829, 2], [54000, 45], [54046, 71], [54130, 2], [54300, 45], [54347, 70], [54430, 2], [54600, 46], [54647, 70], [54730, 2], [54900, 46], [54948, 69], [55030, 2], [55200, 47], [55248, 69], [55330, 2], [55500, 47], [55548, 70], [55630, 2], [55800, 48], [55849, 69], [55930, 2], [56100, 48], [56149, 69], [56230, 2], [56400, 49], [56450, 68], [56530, 2], [56700, 49], [56750, 69], [56830, 2], [57000, 50], [57051, 68], [57130, 2], [57300, 50], [57351, 68], [57430, 2], [57600, 50], [57652, 67], [57729, 4], [57900, 51], [57952, 68], [58028, 5], [58200, 51], [58253, 69], [58326, 7], [58500, 52], [58553, 80], [58800, 52], [58853, 80], [59100, 53], [59154, 79], [59400, 53], [59454, 79], [59700, 54], [59755, 78], [60000, 54], [60055, 78], [60300, 55], [60356, 77], [60600, 55], [60656, 77], [60900, 55], [60957, 76], [61200, 56], [61257, 77], [61500, 56], [61800, 56], [62100, 56], [62400, 55], [62700, 55], [63000, 55], [63300, 54], [63600, 54], [63900, 54], [64200, 54], [64500, 53], [64800, 53], [65100, 53], [65400, 52], [65700, 52], [66000, 52], [66300, 51], [66600, 51], [66900, 51], [67200, 51], [67500, 50], [67800, 50], [68100, 50], [68400, 49], [68700, 49], [69000, 49], [69300, 49], [69600, 48], [69900, 48], [70200, 48], [70500, 47], [70800, 47], [71100, 47], [71400, 46], [71700, 46], [72000, 46], [72300, 46], [72600, 45], [72900, 45], [73200, 45], [73500, 44], [73800, 44], [74100, 44], [74400, 43], [74700, 43], [75000, 43], [75300, 43], [75600, 42], [75900, 42], [76200, 42], [76500, 41], [76800, 42], [77100, 42], [77400, 41], [77700, 41], [78000, 41], [78300, 41], [78600, 40], [78900, 40], [79200, 40], [79500, 39], [79800, 39], [80100, 39], [80401, 37], [80702, 36], [81003, 35], [81304, 34], [81605, 32], [81906, 31], [82207, 30], [82508, 28], [82809, 27], [83110, 26], [83411, 24], [83712, 23], [84013, 22], [84315, 20], [84616, 18], [84917, 17], [85218, 16], [85519, 14], [85820, 13], [86121, 12], [86422, 11], [86723, 9], [87024, 8], [87325, 6], [87626, 4], [87927, 3]], "point": [66, 204]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan21", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -1.5, "y": 0.869696259, "z": -2.25}, "object_poses": [{"objectName": "Potato_027e0a30", "position": {"x": 1.02592409, "y": 0.9166531, "z": -0.460193217}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_027e0a30", "position": {"x": 0.877196848, "y": 0.916653156, "z": -0.314791471}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_0ad5c562", "position": {"x": 0.574625134, "y": 0.7605971, "z": -0.188901335}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_34f12e87", "position": {"x": 0.7573, "y": 0.7731047, "z": -0.6255269}, "rotation": {"x": 1.40334191e-14, "y": 4.829673e-06, "z": 5.914644e-22}}, {"objectName": "Pot_cde1951c", "position": {"x": -2.097, "y": 0.9306, "z": 1.049}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_5a2cfc73", "position": {"x": 0.708081245, "y": 1.42301631, "z": -1.725125}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SoapBottle_d7803c74", "position": {"x": -0.2527333, "y": 0.8999009, "z": 0.7581701}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "SoapBottle_d7803c74", "position": {"x": 0.901886463, "y": 0.6646764, "z": 0.4481}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_737b77b7", "position": {"x": 0.9493945, "y": 1.46226478, "z": 0.411470354}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_737b77b7", "position": {"x": 0.1490829, "y": 0.899054945, "z": 1.01547456}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "PepperShaker_87875872", "position": {"x": 0.601545, "y": 0.6617025, "z": 0.6306019}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_886fcaf3", "position": {"x": 0.787663, "y": 0.0120247006, "z": -0.05397676}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_886fcaf3", "position": {"x": 0.601545, "y": 0.661702454, "z": 0.326432019}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_288ff1e3", "position": {"x": 0.708079934, "y": 0.660327733, "z": -2.190875}, "rotation": {"x": 0.0, "y": 0.000169038554, "z": 0.0}}, {"objectName": "Apple_093f2b24", "position": {"x": -1.29754388, "y": 0.10573864, "z": -3.57439852}, "rotation": {"x": 0.0, "y": 269.999878, "z": 0.0}}, {"objectName": "Pot_cde1951c", "position": {"x": -1.552, "y": 0.9306, "z": 0.785}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_2b33f66e", "position": {"x": -2.02399969, "y": 0.69248, "z": -1.44700074}, "rotation": {"x": 1.06787948e-05, "y": -0.000256137952, "z": -1.99874139e-05}}, {"objectName": "Fork_34f12e87", "position": {"x": 0.799450934, "y": 0.7731047, "z": -0.6805734}, "rotation": {"x": 1.40334191e-14, "y": 4.829673e-06, "z": 5.914644e-22}}, {"objectName": "SoapBottle_d7803c74", "position": {"x": -1.97052181, "y": 0.7010695, "z": -1.19986916}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_093f2b24", "position": {"x": 0.8416018, "y": 0.826046169, "z": -0.8457127}, "rotation": {"x": 7.58303337e-22, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Pan_63693e0a", "position": {"x": 0.7401087, "y": -0.007886887, "z": -0.596487045}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_c79ff911", "position": {"x": 0.8244615, "y": 1.41123283, "z": -2.19087529}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Spatula_8142ce7b", "position": {"x": -0.2527308, "y": 0.9156206, "z": 1.18701458}, "rotation": {"x": 0.0, "y": 180.00032, "z": 0.0}}, {"objectName": "Potato_027e0a30", "position": {"x": 0.8034234, "y": 0.916653156, "z": 0.09879146}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_cb0de209", "position": {"x": -0.25273174, "y": 0.899250448, "z": 1.0154767}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "SaltShaker_886fcaf3", "position": {"x": 0.9619548, "y": 0.660906136, "z": 0.265598059}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_87875872", "position": {"x": 0.901886463, "y": 0.6609062, "z": 0.630601943}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_0ad5c562", "position": {"x": 0.781749845, "y": 0.6654384, "z": 0.5697679}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_11e4138d", "position": {"x": 0.9509703, "y": 0.8831642, "z": -0.176930517}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_42314d78", "position": {"x": -0.873921752, "y": 0.931037068, "z": 0.978991032}, "rotation": {"x": 0.0298037343, "y": 24.2316284, "z": 359.895}}, {"objectName": "Lettuce_0ad3089c", "position": {"x": -1.08401322, "y": 0.9732391, "z": 0.955370843}, "rotation": {"x": 41.4521332, "y": 1.68723977, "z": 2.95340466}}, {"objectName": "Tomato_5a2cfc73", "position": {"x": 0.7151491, "y": 0.8209449, "z": -0.7906662}, "rotation": {"x": 7.58303337e-22, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Spoon_93a81026", "position": {"x": 0.7204354, "y": 0.7616844, "z": 0.00290554762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_33d4fba0", "position": {"x": 0.8662456, "y": 0.7829485, "z": -0.252836943}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_288ff1e3", "position": {"x": 0.9038171, "y": 1.4588306, "z": -0.02267167}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_737b77b7", "position": {"x": -2.03849554, "y": 0.6917894, "z": -1.00353038}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 538477639, "scene_num": 21}, "task_id": "trial_T20190909_063844_086301", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2A028LRDJB7ZB_3CN4LGXD50FB0BSWBMTFO81O1O14Y6", "high_descs": ["Turn right walk towards the fridge", "Open the fridge and pick up the mug then close the fridge", "Turn right then head to the microwave", "Open the microwave heat up the mug then take it out and close the microwave", "Turn left then head to the cabinet below the sink", "Open the left side cabinet then put in the mug then close it"], "task_desc": "Put the heated mug in the sink cabinet ", "votes": [1, 1]}, {"assignment_id": "A3PPLDHC3CG0YN_3J4Q2Z4UT1UE0BKUUII46EHDM8RWQP", "high_descs": ["Turn right and walk to the fridge. ", "Open the door, pick up the mug from the top shelf, close the door. ", "Turn around and walk to the microwave on the left. ", "Open the microwave door, put the mug inside, heat the mug, open the door, take the mug out, close the door. ", "Walk over to the counter to the left of the sink and look below at the cabinet. ", "Open the cabinet door, put the mug inside of the cabinet, close the door. "], "task_desc": "To heat a mug and put it in the cabinet. ", "votes": [1, 1]}, {"assignment_id": "AM2KK02JXXW48_3JNQLM5FT7DHXY6AOTL11L2U8V52LK", "high_descs": ["Turn right, go to the fridge.", "Take the mug out of the fridge. ", "Turn around, bring the mug to the microwave on the table in the left corner.", "Heat the mug in the mike.", "Take the heated mug from the microwave, turn around, go to the sink on the right.", "Put the heated mug in the left cabinet under the sink. "], "task_desc": "Put a heated mug in the left cabinet under the sink. ", "votes": [1, 1]}]}}