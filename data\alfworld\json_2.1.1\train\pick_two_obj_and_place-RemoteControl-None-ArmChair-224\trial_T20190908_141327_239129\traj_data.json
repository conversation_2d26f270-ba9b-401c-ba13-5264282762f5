{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000103.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000104.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000105.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000243.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000244.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000245.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000246.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000247.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000283.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000284.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000285.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000286.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000287.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000288.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000289.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000290.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000291.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000292.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000293.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000294.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000295.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000296.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000297.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000298.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000299.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000300.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000301.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000302.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000303.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000304.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000305.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000306.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000307.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000308.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000309.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000310.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000311.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000312.png", "low_idx": 64}, {"high_idx": 7, "image_name": "000000313.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000314.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000315.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000316.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000317.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000318.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000319.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000320.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000321.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000322.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000323.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000324.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000325.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000326.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000327.png", "low_idx": 65}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "RemoteControl", "parent_target": "ArmChair", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["dresser"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-3|9|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["remotecontrol"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["RemoteControl", [-0.455876024, -0.455876024, 8.75566196, 8.75566196, 3.629771472, 3.629771472]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON>", [-0.0928, -0.0928, 6.8896, 6.8896, 0.0436, 0.0436]], "forceVisible": true, "objectId": "RemoteControl|-00.11|+00.91|+02.19"}}, {"discrete_action": {"action": "GotoLocation", "args": ["armchair"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-9|-3|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["remotecontrol", "armchair"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["RemoteControl", [-0.455876024, -0.455876024, 8.75566196, 8.75566196, 3.629771472, 3.629771472]], "coordinateReceptacleObjectId": ["ArmChair", [-11.44, -11.44, -8.148, -8.148, 0.064, 0.064]], "forceVisible": true, "objectId": "RemoteControl|-00.11|+00.91|+02.19", "receptacleObjectId": "ArmChair|-02.86|+00.02|-02.04"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-13|-4|0|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["remotecontrol"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["RemoteControl", [-13.352476, -13.352476, -2.4896984, -2.4896984, 2.3649364, 2.3649364]], "coordinateReceptacleObjectId": ["SideTable", [-13.2092, -13.2092, -1.96, -1.96, 0.064, 0.064]], "forceVisible": true, "objectId": "RemoteControl|-03.34|+00.59|-00.62"}}, {"discrete_action": {"action": "GotoLocation", "args": ["armchair"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-9|-3|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["remotecontrol", "armchair"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["RemoteControl", [-13.352476, -13.352476, -2.4896984, -2.4896984, 2.3649364, 2.3649364]], "coordinateReceptacleObjectId": ["ArmChair", [-11.44, -11.44, -8.148, -8.148, 0.064, 0.064]], "forceVisible": true, "objectId": "RemoteControl|-03.34|+00.59|-00.62", "receptacleObjectId": "ArmChair|-02.86|+00.02|-02.04"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "RemoteControl|-00.11|+00.91|+02.19"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [141, 151, 202, 167], "mask": [[45146, 8], [45444, 16], [45743, 54], [46042, 58], [46342, 59], [46642, 60], [46941, 61], [47241, 62], [47541, 62], [47841, 62], [48141, 62], [48441, 62], [48741, 61], [49042, 60], [49343, 57], [49644, 22], [49676, 22], [49946, 9]], "point": [171, 158]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "RemoteControl|-00.11|+00.91|+02.19", "placeStationary": true, "receptacleObjectId": "ArmChair|-02.86|+00.02|-02.04"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [166, 74, 299, 229], "mask": [[22123, 8], [22422, 12], [22721, 17], [23020, 22], [23319, 27], [23619, 32], [23918, 38], [24218, 42], [24517, 47], [24817, 51], [25116, 56], [25416, 61], [25716, 64], [26015, 68], [26315, 72], [26615, 76], [26908, 5], [26915, 79], [27207, 91], [27506, 94], [27806, 94], [28105, 95], [28404, 96], [28703, 97], [29003, 97], [29302, 98], [29601, 99], [29900, 100], [30200, 100], [30499, 101], [30798, 102], [31097, 103], [31397, 103], [31696, 104], [31995, 105], [32295, 105], [32594, 106], [32893, 107], [33193, 107], [33492, 108], [33791, 109], [34091, 109], [34390, 110], [34689, 111], [34989, 111], [35288, 112], [35588, 112], [35887, 113], [36187, 113], [36486, 114], [36786, 114], [37085, 115], [37385, 115], [37684, 116], [37984, 116], [38283, 117], [38583, 117], [38882, 118], [39182, 118], [39482, 118], [39781, 119], [40081, 119], [40381, 119], [40680, 120], [40980, 120], [41279, 121], [41579, 121], [41879, 121], [42178, 122], [42478, 122], [42777, 123], [43076, 124], [43375, 125], [43675, 125], [43974, 126], [44273, 127], [44573, 127], [44872, 128], [45171, 129], [45471, 129], [45770, 130], [46070, 130], [46369, 131], [46669, 131], [46969, 131], [47268, 132], [47568, 132], [47868, 132], [48168, 132], [48467, 133], [48767, 133], [49067, 133], [49367, 133], [49667, 133], [49967, 133], [50267, 132], [50567, 132], [50867, 132], [51166, 132], [51466, 132], [51766, 131], [52066, 131], [52366, 131], [52666, 130], [52966, 130], [53266, 129], [53566, 129], [53866, 129], [54166, 128], [54466, 128], [54766, 127], [55067, 126], [55368, 125], [55671, 122], [55973, 119], [56275, 117], [56578, 114], [56880, 112], [57182, 109], [57484, 107], [57787, 104], [58089, 102], [58391, 99], [58693, 97], [58996, 94], [59298, 92], [59600, 89], [59902, 87], [60205, 84], [60507, 81], [60809, 79], [61111, 77], [61414, 73], [61716, 70], [62018, 68], [62321, 64], [62623, 62], [62925, 59], [63227, 57], [63530, 53], [63832, 51], [64134, 48], [64436, 45], [64739, 42], [65041, 39], [65343, 36], [65645, 34], [65948, 30], [66250, 27], [66552, 25], [66854, 22], [67157, 18], [67459, 16], [67761, 13], [68063, 10], [68366, 7], [68668, 3]], "point": [232, 150]}}, "high_idx": 3}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "RemoteControl|-03.34|+00.59|-00.62"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [120, 169, 136, 221], "mask": [[50525, 9], [50823, 12], [51122, 14], [51421, 16], [51721, 16], [52021, 16], [52321, 16], [52621, 16], [52921, 16], [53221, 16], [53521, 16], [53821, 16], [54121, 15], [54422, 14], [54722, 14], [55022, 14], [55322, 13], [55622, 13], [55922, 13], [56222, 13], [56522, 13], [56822, 13], [57122, 13], [57422, 13], [57722, 13], [58022, 13], [58322, 13], [58621, 14], [58921, 14], [59221, 14], [59521, 14], [59821, 14], [60121, 14], [60421, 14], [60721, 14], [61021, 14], [61320, 15], [61620, 15], [61920, 15], [62220, 15], [62520, 15], [62820, 15], [63120, 15], [63420, 15], [63720, 15], [64020, 15], [64320, 14], [64621, 13], [64921, 13], [65222, 11], [65522, 10], [65823, 8], [66125, 4]], "point": [128, 194]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "RemoteControl|-03.34|+00.59|-00.62", "placeStationary": true, "receptacleObjectId": "ArmChair|-02.86|+00.02|-02.04"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [166, 74, 299, 229], "mask": [[22123, 8], [22422, 12], [22721, 17], [23020, 22], [23319, 27], [23619, 32], [23918, 38], [24218, 42], [24517, 47], [24817, 51], [25116, 56], [25416, 61], [25716, 64], [26015, 68], [26315, 72], [26615, 76], [26908, 5], [26915, 79], [27207, 91], [27506, 94], [27806, 94], [28105, 95], [28404, 96], [28703, 97], [29003, 97], [29302, 98], [29601, 99], [29900, 100], [30200, 100], [30499, 101], [30798, 102], [31097, 103], [31397, 103], [31696, 104], [31995, 105], [32295, 105], [32594, 106], [32893, 107], [33193, 107], [33492, 108], [33791, 109], [34091, 109], [34390, 110], [34689, 111], [34989, 111], [35288, 112], [35588, 112], [35887, 113], [36187, 113], [36486, 114], [36786, 114], [37085, 115], [37385, 115], [37684, 116], [37984, 116], [38283, 117], [38583, 117], [38882, 118], [39182, 118], [39482, 118], [39781, 119], [40081, 119], [40381, 119], [40680, 120], [40980, 120], [41279, 121], [41579, 121], [41879, 121], [42178, 122], [42478, 122], [42777, 123], [43076, 124], [43375, 125], [43675, 125], [43974, 126], [44273, 127], [44573, 127], [44872, 128], [45171, 129], [45471, 129], [45770, 130], [46070, 130], [46369, 131], [46669, 131], [46969, 19], [46992, 108], [47268, 19], [47295, 105], [47568, 18], [47597, 103], [47868, 18], [47899, 101], [48168, 18], [48201, 99], [48467, 19], [48504, 96], [48767, 20], [48807, 93], [49067, 21], [49110, 90], [49367, 23], [49414, 86], [49667, 26], [49717, 83], [49967, 29], [50019, 81], [50267, 31], [50319, 80], [50567, 34], [50620, 79], [50867, 36], [50920, 79], [51166, 39], [51220, 78], [51466, 41], [51519, 79], [51766, 43], [51819, 78], [52066, 45], [52118, 79], [52366, 47], [52416, 81], [52666, 130], [52966, 130], [53266, 129], [53566, 129], [53866, 129], [54166, 128], [54466, 128], [54766, 127], [55067, 126], [55368, 125], [55671, 122], [55973, 119], [56275, 117], [56578, 114], [56880, 112], [57182, 109], [57484, 107], [57787, 104], [58089, 102], [58391, 99], [58693, 97], [58996, 94], [59298, 92], [59600, 89], [59902, 87], [60205, 84], [60507, 81], [60809, 79], [61111, 77], [61414, 73], [61716, 70], [62018, 68], [62321, 64], [62623, 62], [62925, 59], [63227, 57], [63530, 53], [63832, 51], [64134, 48], [64436, 45], [64739, 42], [65041, 39], [65343, 36], [65645, 34], [65948, 30], [66250, 27], [66552, 25], [66854, 22], [67157, 18], [67459, 16], [67761, 13], [68063, 10], [68366, 7], [68668, 3]], "point": [232, 150]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan224", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 1.0, "y": 0.916797757, "z": 1.0}, "object_poses": [{"objectName": "CellPhone_dad3503f", "position": {"x": -3.0318377, "y": 0.564095259, "z": 1.18768871}, "rotation": {"x": 0.0, "y": 270.0008, "z": 0.0}}, {"objectName": "CellPhone_dad3503f", "position": {"x": 3.121273, "y": 0.5924486, "z": 0.4974249}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "CreditCard_5dc34411", "position": {"x": -0.244691774, "y": 0.6634426, "z": 1.30467308}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "RemoteControl_b0bc5ae3", "position": {"x": -0.113969006, "y": 0.907442868, "z": 2.18891549}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "RemoteControl_b0bc5ae3", "position": {"x": -3.338119, "y": 0.5912341, "z": -0.6224246}, "rotation": {"x": 0.0, "y": 6.659434e-05, "z": 0.0}}, {"objectName": "RemoteControl_b0bc5ae3", "position": {"x": -0.295507044, "y": 0.907442868, "z": 1.94603217}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Box_ec78f573", "position": {"x": -2.88580942, "y": 0.754012942, "z": 1.60031164}, "rotation": {"x": 0.0, "y": 270.0008, "z": 0.0}}, {"objectName": "Pillow_fe37edd9", "position": {"x": -2.8127923, "y": 0.6234801, "z": 2.012936}, "rotation": {"x": 0.0, "y": 270.0008, "z": 0.0}}, {"objectName": "Book_4e0dbe45", "position": {"x": 0.116, "y": 0.902580142, "z": 1.073}, "rotation": {"x": 0.0, "y": 29.9999466, "z": 0.0}}, {"objectName": "Box_ec78f573", "position": {"x": 3.16004467, "y": 0.7824192, "z": 0.166363046}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Statue_7e494874", "position": {"x": -0.204738, "y": 0.91209656, "z": 1.70314872}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_5dc34411", "position": {"x": -0.1951531, "y": 0.6625638, "z": 0.830187}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_c532ec98", "position": {"x": 0.067569, "y": 0.9075801, "z": 2.431799}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WateringCan_cb2be04d", "position": {"x": 2.89399958, "y": 0.014939107, "z": -1.91600132}, "rotation": {"x": 0.0007926384, "y": 328.832336, "z": 0.000867469469}}, {"objectName": "Newspaper_1e8f1c02", "position": {"x": -0.170000017, "y": 0.5463814, "z": -2.34227681}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_4c89f135", "position": {"x": -2.16899252, "y": 1.19524324, "z": -2.1836822}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_dad3503f", "position": {"x": -0.244691774, "y": 0.412094, "z": 0.936001658}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "RemoteControl_b0bc5ae3", "position": {"x": -0.145614475, "y": 0.413350761, "z": 2.4194622}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_eef72b70", "position": {"x": -1.04643679, "y": 1.19524324, "z": -2.15178347}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pillow_fe37edd9", "position": {"x": -2.893, "y": 0.616, "z": 0.818}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "KeyChain_ec44dd4c", "position": {"x": -0.2795056, "y": 0.5400471, "z": -2.139723}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3316827942, "scene_num": 224}, "task_id": "trial_T20190908_141327_239129", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "AHBWX3WYMAB0E_3Y9N9SS8L12NK4E1FC1I1IZH5U53D6", "high_descs": ["Turn right and go around the corner to the TV stand on your right.", "Pick up the remote control that's closest to the laptop.", "Turn right and head across the room to the brown chair on the right of the fireplace.", "Put the remote control parallel to the front edge on the left side of the seat cushion of the brown chair.", "Turn right and face the small black stand.", "Pick up the remote control from the top of the stand.", "Turn left and move back to the brown chair on the right of the fireplace.", "Put the second remote on the seat cushion of the chair perpendicular to the first remote."], "task_desc": "Move two remote controls to the brown chair on the right of the fireplace.", "votes": [1, 1]}, {"assignment_id": "A24RGLS3BRZ60J_3ZAZR5XV049E05FG0N1GLDMEZOJCZ1", "high_descs": ["Turn right and then right again to move into the next room. Then turn right and face the dresser on the right side.", "Pick up the remote from the left side of the dresser.", "Carry the remote and turn around. Locate the chair across the room.", "Place the remote on the chair cushion.", "Turn to the right and face the small table to the right.", "Pick up the remote from the small table.", "Carry the remote and turn to the right, then turn right to face the chair.", "Place the remote on the chair cushion with the other remote."], "task_desc": "Move two remotes to a chair.", "votes": [1, 1]}, {"assignment_id": "A2KAGFQU28JY43_3ZQIG0FLQH765JGBGHCEC1GL3TLVWJ", "high_descs": ["Turn around and go through the entry way to the living room, turn to your right, and go to the end of the dresser the TV is on, to your right. ", "Pick up the closest to the laptop, on the dresser. ", "Turn around and walk across the room to the couch, then turn left before the couch and go across the room again, to the chair to the right of the fireplace. ", "Place the remote on the chair to the right of the fireplace. ", "Turn to the small, black table against the wall, to your right. ", "Pick up the remote from the small black table. ", "Turn around and go back to the chair to the right of the fireplace. ", "Place the remote on the chair, to the left of the other remote. "], "task_desc": "Put two remotes on the living room chair. ", "votes": [1, 1]}]}}