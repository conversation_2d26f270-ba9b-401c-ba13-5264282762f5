
(define (problem plan_trial_T20190909_001550_668945)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Candle_bar__minus_01_dot_46_bar__plus_00_dot_77_bar__plus_02_dot_78 - object
        Cloth_bar__minus_01_dot_49_bar__plus_00_dot_24_bar__plus_02_dot_27 - object
        Faucet_bar__plus_00_dot_85_bar__plus_01_dot_80_bar__plus_01_dot_11 - object
        Faucet_bar__minus_01_dot_93_bar__plus_00_dot_72_bar__plus_02_dot_29 - object
        Faucet_bar__minus_01_dot_93_bar__plus_00_dot_72_bar__plus_03_dot_37 - object
        HandTowel_bar__minus_01_dot_51_bar__plus_01_dot_46_bar__plus_03_dot_96 - object
        LightSwitch_bar__plus_01_dot_25_bar__plus_01_dot_27_bar__plus_03_dot_64 - object
        Mirror_bar__minus_01_dot_92_bar__plus_01_dot_41_bar__plus_02_dot_76 - object
        Plunger_bar__minus_01_dot_69_bar__plus_00_dot_00_bar__plus_00_dot_33 - object
        ScrubBrush_bar__minus_01_dot_85_bar__plus_00_dot_00_bar__plus_00_dot_14 - object
        ShowerDoor_bar__plus_00_dot_57_bar__plus_01_dot_02_bar__plus_01_dot_12 - object
        Sink_bar__minus_01_dot_70_bar__plus_00_dot_62_bar__plus_02_dot_28 - object
        Sink_bar__minus_01_dot_70_bar__plus_00_dot_62_bar__plus_03_dot_37 - object
        SoapBar_bar__minus_01_dot_70_bar__plus_00_dot_64_bar__plus_03_dot_32 - object
        SoapBar_bar__minus_01_dot_80_bar__plus_01_dot_05_bar__plus_00_dot_49 - object
        SoapBar_bar__minus_01_dot_89_bar__plus_00_dot_73_bar__plus_02_dot_73 - object
        SoapBottle_bar__minus_01_dot_87_bar__plus_01_dot_05_bar__plus_00_dot_68 - object
        SprayBottle_bar__minus_01_dot_37_bar__plus_00_dot_73_bar__plus_02_dot_93 - object
        SprayBottle_bar__minus_01_dot_54_bar__plus_00_dot_73_bar__plus_02_dot_88 - object
        ToiletPaper_bar__minus_01_dot_55_bar__plus_00_dot_62_bar__plus_00_dot_05 - object
        ToiletPaper_bar__minus_01_dot_61_bar__plus_00_dot_24_bar__plus_03_dot_33 - object
        ToiletPaper_bar__minus_01_dot_87_bar__plus_00_dot_72_bar__plus_01_dot_86 - object
        Towel_bar__plus_00_dot_87_bar__plus_01_dot_17_bar__plus_02_dot_36 - object
        Window_bar__minus_01_dot_04_bar__plus_01_dot_40_bar__plus_00_dot_02 - object
        Window_bar__minus_01_dot_99_bar__plus_01_dot_40_bar__plus_00_dot_71 - object
        CounterTop_bar__minus_01_dot_61_bar__plus_00_dot_70_bar__plus_02_dot_83 - receptacle
        Drawer_bar__minus_01_dot_34_bar__plus_00_dot_30_bar__plus_02_dot_13 - receptacle
        Drawer_bar__minus_01_dot_34_bar__plus_00_dot_30_bar__plus_03_dot_33 - receptacle
        GarbageCan_bar__minus_01_dot_74_bar__plus_00_dot_00_bar__plus_01_dot_34 - receptacle
        HandTowelHolder_bar__minus_01_dot_51_bar__plus_01_dot_56_bar__plus_04_dot_00 - receptacle
        HandTowelHolder_bar__minus_02_dot_00_bar__plus_01_dot_69_bar__plus_01_dot_32 - receptacle
        Sink_bar__minus_01_dot_70_bar__plus_00_dot_62_bar__plus_02_dot_28_bar_SinkBasin - receptacle
        Sink_bar__minus_01_dot_70_bar__plus_00_dot_62_bar__plus_03_dot_37_bar_SinkBasin - receptacle
        ToiletPaperHanger_bar__minus_01_dot_50_bar__plus_00_dot_72_bar__plus_00_dot_00 - receptacle
        Toilet_bar__minus_01_dot_55_bar__plus_00_dot_00_bar__plus_00_dot_69 - receptacle
        TowelHolder_bar__plus_00_dot_87_bar__plus_01_dot_16_bar__plus_02_dot_25 - receptacle
        loc_bar_3_bar_14_bar_1_bar_30 - location
        loc_bar__minus_3_bar_3_bar_2_bar_45 - location
        loc_bar_0_bar_12_bar_3_bar_45 - location
        loc_bar__minus_4_bar_9_bar_3_bar_60 - location
        loc_bar__minus_3_bar_9_bar_3_bar_45 - location
        loc_bar__minus_5_bar_5_bar_2_bar_60 - location
        loc_bar_0_bar_10_bar_3_bar_45 - location
        loc_bar__minus_4_bar_7_bar_3_bar_45 - location
        loc_bar__minus_4_bar_11_bar_3_bar_60 - location
        loc_bar_0_bar_5_bar_1_bar_45 - location
        loc_bar__minus_4_bar_13_bar_3_bar_60 - location
        loc_bar__minus_4_bar_11_bar_3_bar_15 - location
        loc_bar__minus_5_bar_5_bar_3_bar_0 - location
        loc_bar__minus_3_bar_3_bar_3_bar_60 - location
        loc_bar__minus_4_bar_12_bar_3_bar_60 - location
        loc_bar__minus_4_bar_14_bar_3_bar_15 - location
        loc_bar__minus_3_bar_3_bar_2_bar_15 - location
        loc_bar_3_bar_11_bar_2_bar_45 - location
        loc_bar__minus_3_bar_3_bar_3_bar_15 - location
        loc_bar__minus_2_bar_2_bar_1_bar_30 - location
        )
    

(:init


        (receptacleType CounterTop_bar__minus_01_dot_61_bar__plus_00_dot_70_bar__plus_02_dot_83 CounterTopType)
        (receptacleType HandTowelHolder_bar__minus_02_dot_00_bar__plus_01_dot_69_bar__plus_01_dot_32 HandTowelHolderType)
        (receptacleType GarbageCan_bar__minus_01_dot_74_bar__plus_00_dot_00_bar__plus_01_dot_34 GarbageCanType)
        (receptacleType Sink_bar__minus_01_dot_70_bar__plus_00_dot_62_bar__plus_03_dot_37_bar_SinkBasin SinkBasinType)
        (receptacleType HandTowelHolder_bar__minus_01_dot_51_bar__plus_01_dot_56_bar__plus_04_dot_00 HandTowelHolderType)
        (receptacleType Drawer_bar__minus_01_dot_34_bar__plus_00_dot_30_bar__plus_03_dot_33 DrawerType)
        (receptacleType Sink_bar__minus_01_dot_70_bar__plus_00_dot_62_bar__plus_02_dot_28_bar_SinkBasin SinkBasinType)
        (receptacleType ToiletPaperHanger_bar__minus_01_dot_50_bar__plus_00_dot_72_bar__plus_00_dot_00 ToiletPaperHangerType)
        (receptacleType Toilet_bar__minus_01_dot_55_bar__plus_00_dot_00_bar__plus_00_dot_69 ToiletType)
        (receptacleType TowelHolder_bar__plus_00_dot_87_bar__plus_01_dot_16_bar__plus_02_dot_25 TowelHolderType)
        (receptacleType Drawer_bar__minus_01_dot_34_bar__plus_00_dot_30_bar__plus_02_dot_13 DrawerType)
        (objectType SoapBar_bar__minus_01_dot_80_bar__plus_01_dot_05_bar__plus_00_dot_49 SoapBarType)
        (objectType Sink_bar__minus_01_dot_70_bar__plus_00_dot_62_bar__plus_03_dot_37 SinkType)
        (objectType LightSwitch_bar__plus_01_dot_25_bar__plus_01_dot_27_bar__plus_03_dot_64 LightSwitchType)
        (objectType Plunger_bar__minus_01_dot_69_bar__plus_00_dot_00_bar__plus_00_dot_33 PlungerType)
        (objectType SoapBottle_bar__minus_01_dot_87_bar__plus_01_dot_05_bar__plus_00_dot_68 SoapBottleType)
        (objectType Window_bar__minus_01_dot_04_bar__plus_01_dot_40_bar__plus_00_dot_02 WindowType)
        (objectType Window_bar__minus_01_dot_99_bar__plus_01_dot_40_bar__plus_00_dot_71 WindowType)
        (objectType Mirror_bar__minus_01_dot_92_bar__plus_01_dot_41_bar__plus_02_dot_76 MirrorType)
        (objectType ScrubBrush_bar__minus_01_dot_85_bar__plus_00_dot_00_bar__plus_00_dot_14 ScrubBrushType)
        (objectType ToiletPaper_bar__minus_01_dot_87_bar__plus_00_dot_72_bar__plus_01_dot_86 ToiletPaperType)
        (objectType Cloth_bar__minus_01_dot_49_bar__plus_00_dot_24_bar__plus_02_dot_27 ClothType)
        (objectType SoapBar_bar__minus_01_dot_70_bar__plus_00_dot_64_bar__plus_03_dot_32 SoapBarType)
        (objectType SoapBar_bar__minus_01_dot_89_bar__plus_00_dot_73_bar__plus_02_dot_73 SoapBarType)
        (objectType SprayBottle_bar__minus_01_dot_54_bar__plus_00_dot_73_bar__plus_02_dot_88 SprayBottleType)
        (objectType Sink_bar__minus_01_dot_70_bar__plus_00_dot_62_bar__plus_02_dot_28 SinkType)
        (objectType ToiletPaper_bar__minus_01_dot_61_bar__plus_00_dot_24_bar__plus_03_dot_33 ToiletPaperType)
        (objectType SprayBottle_bar__minus_01_dot_37_bar__plus_00_dot_73_bar__plus_02_dot_93 SprayBottleType)
        (objectType Towel_bar__plus_00_dot_87_bar__plus_01_dot_17_bar__plus_02_dot_36 TowelType)
        (objectType ToiletPaper_bar__minus_01_dot_55_bar__plus_00_dot_62_bar__plus_00_dot_05 ToiletPaperType)
        (objectType Candle_bar__minus_01_dot_46_bar__plus_00_dot_77_bar__plus_02_dot_78 CandleType)
        (objectType ShowerDoor_bar__plus_00_dot_57_bar__plus_01_dot_02_bar__plus_01_dot_12 ShowerDoorType)
        (objectType HandTowel_bar__minus_01_dot_51_bar__plus_01_dot_46_bar__plus_03_dot_96 HandTowelType)
        (canContain CounterTopType CandleType)
        (canContain CounterTopType SoapBarType)
        (canContain CounterTopType SprayBottleType)
        (canContain CounterTopType ToiletPaperType)
        (canContain CounterTopType SoapBottleType)
        (canContain CounterTopType ClothType)
        (canContain CounterTopType HandTowelType)
        (canContain HandTowelHolderType HandTowelType)
        (canContain GarbageCanType SoapBarType)
        (canContain GarbageCanType SprayBottleType)
        (canContain GarbageCanType ToiletPaperType)
        (canContain GarbageCanType SoapBottleType)
        (canContain GarbageCanType ClothType)
        (canContain GarbageCanType HandTowelType)
        (canContain SinkBasinType SoapBarType)
        (canContain SinkBasinType ClothType)
        (canContain SinkBasinType HandTowelType)
        (canContain HandTowelHolderType HandTowelType)
        (canContain DrawerType CandleType)
        (canContain DrawerType SoapBarType)
        (canContain DrawerType SprayBottleType)
        (canContain DrawerType ToiletPaperType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ClothType)
        (canContain DrawerType HandTowelType)
        (canContain SinkBasinType SoapBarType)
        (canContain SinkBasinType ClothType)
        (canContain SinkBasinType HandTowelType)
        (canContain ToiletPaperHangerType ToiletPaperType)
        (canContain ToiletType SoapBottleType)
        (canContain ToiletType HandTowelType)
        (canContain ToiletType ToiletPaperType)
        (canContain ToiletType ClothType)
        (canContain ToiletType CandleType)
        (canContain ToiletType SoapBarType)
        (canContain ToiletType SprayBottleType)
        (canContain TowelHolderType TowelType)
        (canContain DrawerType CandleType)
        (canContain DrawerType SoapBarType)
        (canContain DrawerType SprayBottleType)
        (canContain DrawerType ToiletPaperType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ClothType)
        (canContain DrawerType HandTowelType)
        (pickupable SoapBar_bar__minus_01_dot_80_bar__plus_01_dot_05_bar__plus_00_dot_49)
        (pickupable Plunger_bar__minus_01_dot_69_bar__plus_00_dot_00_bar__plus_00_dot_33)
        (pickupable SoapBottle_bar__minus_01_dot_87_bar__plus_01_dot_05_bar__plus_00_dot_68)
        (pickupable ScrubBrush_bar__minus_01_dot_85_bar__plus_00_dot_00_bar__plus_00_dot_14)
        (pickupable ToiletPaper_bar__minus_01_dot_87_bar__plus_00_dot_72_bar__plus_01_dot_86)
        (pickupable Cloth_bar__minus_01_dot_49_bar__plus_00_dot_24_bar__plus_02_dot_27)
        (pickupable SoapBar_bar__minus_01_dot_70_bar__plus_00_dot_64_bar__plus_03_dot_32)
        (pickupable SoapBar_bar__minus_01_dot_89_bar__plus_00_dot_73_bar__plus_02_dot_73)
        (pickupable SprayBottle_bar__minus_01_dot_54_bar__plus_00_dot_73_bar__plus_02_dot_88)
        (pickupable ToiletPaper_bar__minus_01_dot_61_bar__plus_00_dot_24_bar__plus_03_dot_33)
        (pickupable SprayBottle_bar__minus_01_dot_37_bar__plus_00_dot_73_bar__plus_02_dot_93)
        (pickupable Towel_bar__plus_00_dot_87_bar__plus_01_dot_17_bar__plus_02_dot_36)
        (pickupable ToiletPaper_bar__minus_01_dot_55_bar__plus_00_dot_62_bar__plus_00_dot_05)
        (pickupable Candle_bar__minus_01_dot_46_bar__plus_00_dot_77_bar__plus_02_dot_78)
        (pickupable HandTowel_bar__minus_01_dot_51_bar__plus_01_dot_46_bar__plus_03_dot_96)
        
        (openable Drawer_bar__minus_01_dot_34_bar__plus_00_dot_30_bar__plus_03_dot_33)
        (openable Drawer_bar__minus_01_dot_34_bar__plus_00_dot_30_bar__plus_02_dot_13)
        
        (atLocation agent1 loc_bar__minus_2_bar_2_bar_1_bar_30)
        
        (cleanable SoapBar_bar__minus_01_dot_80_bar__plus_01_dot_05_bar__plus_00_dot_49)
        (cleanable Cloth_bar__minus_01_dot_49_bar__plus_00_dot_24_bar__plus_02_dot_27)
        (cleanable SoapBar_bar__minus_01_dot_70_bar__plus_00_dot_64_bar__plus_03_dot_32)
        (cleanable SoapBar_bar__minus_01_dot_89_bar__plus_00_dot_73_bar__plus_02_dot_73)
        
        
        
        
        
        
        
        
        
        
        (inReceptacle ToiletPaper_bar__minus_01_dot_61_bar__plus_00_dot_24_bar__plus_03_dot_33 Drawer_bar__minus_01_dot_34_bar__plus_00_dot_30_bar__plus_03_dot_33)
        (inReceptacle SoapBar_bar__minus_01_dot_89_bar__plus_00_dot_73_bar__plus_02_dot_73 CounterTop_bar__minus_01_dot_61_bar__plus_00_dot_70_bar__plus_02_dot_83)
        (inReceptacle Candle_bar__minus_01_dot_46_bar__plus_00_dot_77_bar__plus_02_dot_78 CounterTop_bar__minus_01_dot_61_bar__plus_00_dot_70_bar__plus_02_dot_83)
        (inReceptacle SprayBottle_bar__minus_01_dot_37_bar__plus_00_dot_73_bar__plus_02_dot_93 CounterTop_bar__minus_01_dot_61_bar__plus_00_dot_70_bar__plus_02_dot_83)
        (inReceptacle SprayBottle_bar__minus_01_dot_54_bar__plus_00_dot_73_bar__plus_02_dot_88 CounterTop_bar__minus_01_dot_61_bar__plus_00_dot_70_bar__plus_02_dot_83)
        (inReceptacle Cloth_bar__minus_01_dot_49_bar__plus_00_dot_24_bar__plus_02_dot_27 Drawer_bar__minus_01_dot_34_bar__plus_00_dot_30_bar__plus_02_dot_13)
        (inReceptacle Towel_bar__plus_00_dot_87_bar__plus_01_dot_17_bar__plus_02_dot_36 TowelHolder_bar__plus_00_dot_87_bar__plus_01_dot_16_bar__plus_02_dot_25)
        (inReceptacle HandTowel_bar__minus_01_dot_51_bar__plus_01_dot_46_bar__plus_03_dot_96 HandTowelHolder_bar__minus_01_dot_51_bar__plus_01_dot_56_bar__plus_04_dot_00)
        (inReceptacle ToiletPaper_bar__minus_01_dot_55_bar__plus_00_dot_62_bar__plus_00_dot_05 ToiletPaperHanger_bar__minus_01_dot_50_bar__plus_00_dot_72_bar__plus_00_dot_00)
        (inReceptacle SoapBar_bar__minus_01_dot_70_bar__plus_00_dot_64_bar__plus_03_dot_32 Sink_bar__minus_01_dot_70_bar__plus_00_dot_62_bar__plus_03_dot_37_bar_SinkBasin)
        (inReceptacle SoapBottle_bar__minus_01_dot_87_bar__plus_01_dot_05_bar__plus_00_dot_68 Toilet_bar__minus_01_dot_55_bar__plus_00_dot_00_bar__plus_00_dot_69)
        (inReceptacle SoapBar_bar__minus_01_dot_80_bar__plus_01_dot_05_bar__plus_00_dot_49 Toilet_bar__minus_01_dot_55_bar__plus_00_dot_00_bar__plus_00_dot_69)
        
        
        (receptacleAtLocation CounterTop_bar__minus_01_dot_61_bar__plus_00_dot_70_bar__plus_02_dot_83 loc_bar__minus_3_bar_9_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_01_dot_34_bar__plus_00_dot_30_bar__plus_02_dot_13 loc_bar_0_bar_10_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_01_dot_34_bar__plus_00_dot_30_bar__plus_03_dot_33 loc_bar_0_bar_12_bar_3_bar_45)
        (receptacleAtLocation GarbageCan_bar__minus_01_dot_74_bar__plus_00_dot_00_bar__plus_01_dot_34 loc_bar__minus_3_bar_3_bar_3_bar_60)
        (receptacleAtLocation HandTowelHolder_bar__minus_01_dot_51_bar__plus_01_dot_56_bar__plus_04_dot_00 loc_bar__minus_4_bar_14_bar_3_bar_15)
        (receptacleAtLocation HandTowelHolder_bar__minus_02_dot_00_bar__plus_01_dot_69_bar__plus_01_dot_32 loc_bar__minus_5_bar_5_bar_3_bar_0)
        (receptacleAtLocation Sink_bar__minus_01_dot_70_bar__plus_00_dot_62_bar__plus_02_dot_28_bar_SinkBasin loc_bar__minus_4_bar_11_bar_3_bar_60)
        (receptacleAtLocation Sink_bar__minus_01_dot_70_bar__plus_00_dot_62_bar__plus_03_dot_37_bar_SinkBasin loc_bar__minus_4_bar_12_bar_3_bar_60)
        (receptacleAtLocation ToiletPaperHanger_bar__minus_01_dot_50_bar__plus_00_dot_72_bar__plus_00_dot_00 loc_bar__minus_3_bar_3_bar_2_bar_45)
        (receptacleAtLocation Toilet_bar__minus_01_dot_55_bar__plus_00_dot_00_bar__plus_00_dot_69 loc_bar__minus_5_bar_5_bar_2_bar_60)
        (receptacleAtLocation TowelHolder_bar__plus_00_dot_87_bar__plus_01_dot_16_bar__plus_02_dot_25 loc_bar_3_bar_11_bar_2_bar_45)
        (objectAtLocation SprayBottle_bar__minus_01_dot_54_bar__plus_00_dot_73_bar__plus_02_dot_88 loc_bar__minus_3_bar_9_bar_3_bar_45)
        (objectAtLocation ToiletPaper_bar__minus_01_dot_55_bar__plus_00_dot_62_bar__plus_00_dot_05 loc_bar__minus_3_bar_3_bar_2_bar_45)
        (objectAtLocation SoapBar_bar__minus_01_dot_80_bar__plus_01_dot_05_bar__plus_00_dot_49 loc_bar__minus_5_bar_5_bar_2_bar_60)
        (objectAtLocation SoapBar_bar__minus_01_dot_70_bar__plus_00_dot_64_bar__plus_03_dot_32 loc_bar__minus_4_bar_12_bar_3_bar_60)
        (objectAtLocation Sink_bar__minus_01_dot_70_bar__plus_00_dot_62_bar__plus_03_dot_37 loc_bar__minus_4_bar_13_bar_3_bar_60)
        (objectAtLocation ShowerDoor_bar__plus_00_dot_57_bar__plus_01_dot_02_bar__plus_01_dot_12 loc_bar_0_bar_5_bar_1_bar_45)
        (objectAtLocation Mirror_bar__minus_01_dot_92_bar__plus_01_dot_41_bar__plus_02_dot_76 loc_bar__minus_4_bar_11_bar_3_bar_15)
        (objectAtLocation Sink_bar__minus_01_dot_70_bar__plus_00_dot_62_bar__plus_02_dot_28 loc_bar__minus_4_bar_9_bar_3_bar_60)
        (objectAtLocation Candle_bar__minus_01_dot_46_bar__plus_00_dot_77_bar__plus_02_dot_78 loc_bar__minus_3_bar_9_bar_3_bar_45)
        (objectAtLocation HandTowel_bar__minus_01_dot_51_bar__plus_01_dot_46_bar__plus_03_dot_96 loc_bar__minus_4_bar_14_bar_3_bar_15)
        (objectAtLocation Plunger_bar__minus_01_dot_69_bar__plus_00_dot_00_bar__plus_00_dot_33 loc_bar__minus_3_bar_3_bar_3_bar_60)
        (objectAtLocation Towel_bar__plus_00_dot_87_bar__plus_01_dot_17_bar__plus_02_dot_36 loc_bar_3_bar_11_bar_2_bar_45)
        (objectAtLocation SoapBottle_bar__minus_01_dot_87_bar__plus_01_dot_05_bar__plus_00_dot_68 loc_bar__minus_5_bar_5_bar_2_bar_60)
        (objectAtLocation SoapBar_bar__minus_01_dot_89_bar__plus_00_dot_73_bar__plus_02_dot_73 loc_bar__minus_3_bar_9_bar_3_bar_45)
        (objectAtLocation ToiletPaper_bar__minus_01_dot_61_bar__plus_00_dot_24_bar__plus_03_dot_33 loc_bar_0_bar_12_bar_3_bar_45)
        (objectAtLocation Cloth_bar__minus_01_dot_49_bar__plus_00_dot_24_bar__plus_02_dot_27 loc_bar_0_bar_10_bar_3_bar_45)
        (objectAtLocation ToiletPaper_bar__minus_01_dot_87_bar__plus_00_dot_72_bar__plus_01_dot_86 loc_bar__minus_4_bar_7_bar_3_bar_45)
        (objectAtLocation Window_bar__minus_01_dot_04_bar__plus_01_dot_40_bar__plus_00_dot_02 loc_bar__minus_3_bar_3_bar_2_bar_15)
        (objectAtLocation Window_bar__minus_01_dot_99_bar__plus_01_dot_40_bar__plus_00_dot_71 loc_bar__minus_3_bar_3_bar_3_bar_15)
        (objectAtLocation LightSwitch_bar__plus_01_dot_25_bar__plus_01_dot_27_bar__plus_03_dot_64 loc_bar_3_bar_14_bar_1_bar_30)
        (objectAtLocation ScrubBrush_bar__minus_01_dot_85_bar__plus_00_dot_00_bar__plus_00_dot_14 loc_bar__minus_3_bar_3_bar_3_bar_60)
        (objectAtLocation SprayBottle_bar__minus_01_dot_37_bar__plus_00_dot_73_bar__plus_02_dot_93 loc_bar__minus_3_bar_9_bar_3_bar_45)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 SprayBottleType)
                                    (receptacleType ?r GarbageCanType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 SprayBottleType)
                                            (receptacleType ?r GarbageCanType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            