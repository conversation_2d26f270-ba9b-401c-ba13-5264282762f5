
(define (problem plan_trial_T20190907_012608_530399)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Apple_bar__plus_01_dot_84_bar__plus_01_dot_07_bar__minus_00_dot_68 - object
        Apple_bar__minus_00_dot_79_bar__plus_01_dot_45_bar__minus_01_dot_63 - object
        Bowl_bar__plus_01_dot_81_bar__plus_01_dot_64_bar__plus_00_dot_38 - object
        Bread_bar__plus_01_dot_62_bar__plus_00_dot_98_bar__plus_00_dot_76 - object
        ButterKnife_bar__plus_00_dot_30_bar__plus_00_dot_90_bar__minus_01_dot_30 - object
        ButterKnife_bar__plus_00_dot_55_bar__plus_00_dot_91_bar__minus_01_dot_35 - object
        ButterKnife_bar__plus_01_dot_84_bar__plus_00_dot_91_bar__plus_00_dot_52 - object
        Chair_bar__plus_00_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_72 - object
        Chair_bar__plus_01_dot_09_bar__plus_00_dot_00_bar__plus_01_dot_74 - object
        Cup_bar__plus_01_dot_77_bar__plus_01_dot_01_bar__minus_00_dot_89 - object
        Cup_bar__plus_01_dot_88_bar__plus_01_dot_63_bar__minus_00_dot_68 - object
        DishSponge_bar__plus_01_dot_77_bar__plus_00_dot_07_bar__plus_01_dot_54 - object
        Egg_bar__minus_00_dot_89_bar__plus_00_dot_50_bar__minus_01_dot_67 - object
        Faucet_bar__plus_01_dot_11_bar__plus_01_dot_14_bar__minus_01_dot_76 - object
        Fork_bar__plus_00_dot_99_bar__plus_00_dot_91_bar__plus_01_dot_00 - object
        Fork_bar__plus_01_dot_25_bar__plus_00_dot_91_bar__plus_00_dot_83 - object
        Fork_bar__plus_01_dot_66_bar__plus_00_dot_91_bar__minus_01_dot_63 - object
        Knife_bar__plus_00_dot_46_bar__plus_00_dot_94_bar__minus_01_dot_26 - object
        Knife_bar__plus_01_dot_76_bar__plus_00_dot_94_bar__plus_01_dot_09 - object
        Ladle_bar__plus_01_dot_17_bar__plus_00_dot_80_bar__minus_01_dot_60 - object
        Lettuce_bar__plus_01_dot_80_bar__plus_01_dot_01_bar__minus_01_dot_55 - object
        LightSwitch_bar__plus_02_dot_00_bar__plus_01_dot_26_bar__plus_03_dot_09 - object
        Mug_bar__plus_01_dot_71_bar__plus_01_dot_01_bar__minus_00_dot_68 - object
        Pan_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32 - object
        Pan_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32 - object
        PepperShaker_bar__plus_00_dot_26_bar__plus_00_dot_91_bar__minus_01_dot_26 - object
        PepperShaker_bar__plus_00_dot_48_bar__plus_00_dot_91_bar__plus_01_dot_27 - object
        PepperShaker_bar__plus_00_dot_84_bar__plus_00_dot_91_bar__minus_01_dot_23 - object
        Plate_bar__plus_01_dot_81_bar__plus_01_dot_63_bar__minus_00_dot_90 - object
        Plate_bar__minus_00_dot_74_bar__plus_01_dot_09_bar__minus_01_dot_59 - object
        Plate_bar__minus_00_dot_79_bar__plus_00_dot_46_bar__minus_01_dot_48 - object
        Potato_bar__plus_01_dot_03_bar__plus_00_dot_80_bar__minus_01_dot_71 - object
        Pot_bar__plus_01_dot_53_bar__plus_00_dot_91_bar__plus_00_dot_06 - object
        SaltShaker_bar__plus_01_dot_45_bar__plus_00_dot_91_bar__plus_00_dot_36 - object
        SaltShaker_bar__plus_01_dot_94_bar__plus_00_dot_91_bar__minus_01_dot_37 - object
        Sink_bar__plus_01_dot_10_bar__plus_00_dot_91_bar__minus_01_dot_60 - object
        SoapBottle_bar__plus_00_dot_99_bar__plus_00_dot_91_bar__plus_00_dot_92 - object
        SoapBottle_bar__plus_01_dot_76_bar__plus_00_dot_91_bar__plus_01_dot_27 - object
        SoapBottle_bar__plus_01_dot_88_bar__plus_01_dot_65_bar__minus_00_dot_50 - object
        Spatula_bar__plus_01_dot_50_bar__plus_00_dot_93_bar__plus_01_dot_18 - object
        Spatula_bar__plus_01_dot_87_bar__plus_00_dot_93_bar__minus_01_dot_72 - object
        Spoon_bar__plus_00_dot_89_bar__plus_00_dot_76_bar__minus_01_dot_65 - object
        StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__plus_00_dot_09 - object
        StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__minus_00_dot_05 - object
        StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__minus_00_dot_18 - object
        StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__minus_00_dot_32 - object
        Tomato_bar__plus_01_dot_76_bar__plus_00_dot_97_bar__plus_00_dot_57 - object
        Tomato_bar__plus_01_dot_92_bar__plus_00_dot_97_bar__plus_00_dot_42 - object
        Window_bar__plus_00_dot_78_bar__plus_01_dot_74_bar__minus_01_dot_89 - object
        Window_bar__minus_00_dot_37_bar__plus_01_dot_59_bar__plus_03_dot_35 - object
        Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__plus_00_dot_67 - receptacle
        Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_00_dot_61 - receptacle
        Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_01_dot_21 - receptacle
        Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_01_dot_89 - receptacle
        Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_06_bar__plus_00_dot_19 - receptacle
        Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_06_bar__minus_00_dot_59 - receptacle
        Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_30_bar__plus_00_dot_69 - receptacle
        Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_30_bar__plus_01_dot_31 - receptacle
        CoffeeMachine_bar__plus_00_dot_50_bar__plus_00_dot_90_bar__minus_01_dot_60 - receptacle
        CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02 - receptacle
        CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52 - receptacle
        Fridge_bar__minus_00_dot_53_bar__plus_00_dot_00_bar__minus_01_dot_55 - receptacle
        GarbageCan_bar__plus_01_dot_77_bar__plus_00_dot_00_bar__plus_01_dot_69 - receptacle
        Microwave_bar__plus_01_dot_75_bar__plus_00_dot_90_bar__minus_00_dot_84 - receptacle
        Sink_bar__plus_01_dot_10_bar__plus_00_dot_91_bar__minus_01_dot_60_bar_SinkBasin - receptacle
        StoveBurner_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32 - receptacle
        StoveBurner_bar__plus_01_dot_55_bar__plus_00_dot_91_bar__plus_00_dot_08 - receptacle
        StoveBurner_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__plus_00_dot_08 - receptacle
        StoveBurner_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32 - receptacle
        Toaster_bar__plus_00_dot_13_bar__plus_00_dot_90_bar__minus_01_dot_59 - receptacle
        loc_bar_4_bar__minus_3_bar_1_bar__minus_30 - location
        loc_bar_4_bar_9_bar_2_bar_60 - location
        loc_bar_4_bar__minus_3_bar_1_bar_45 - location
        loc_bar_4_bar_0_bar_1_bar_45 - location
        loc_bar_6_bar_9_bar_2_bar_60 - location
        loc_bar_2_bar_0_bar_1_bar__minus_15 - location
        loc_bar_1_bar__minus_3_bar_2_bar_45 - location
        loc_bar_4_bar_1_bar_0_bar_45 - location
        loc_bar_6_bar_9_bar_2_bar__minus_30 - location
        loc_bar_4_bar__minus_3_bar_2_bar__minus_15 - location
        loc_bar_4_bar_0_bar_1_bar__minus_30 - location
        loc_bar_0_bar_7_bar_1_bar_60 - location
        loc_bar_3_bar__minus_3_bar_1_bar__minus_30 - location
        loc_bar_4_bar_0_bar_1_bar_60 - location
        loc_bar_4_bar__minus_3_bar_2_bar_45 - location
        loc_bar__minus_1_bar_11_bar_0_bar_0 - location
        loc_bar_4_bar__minus_1_bar_1_bar_60 - location
        loc_bar__minus_2_bar__minus_2_bar_2_bar_60 - location
        loc_bar_4_bar_1_bar_1_bar__minus_30 - location
        loc_bar_3_bar__minus_3_bar_2_bar_0 - location
        loc_bar_6_bar_11_bar_1_bar_45 - location
        loc_bar_4_bar__minus_1_bar_1_bar_45 - location
        loc_bar_2_bar__minus_3_bar_2_bar_45 - location
        loc_bar__minus_2_bar_5_bar_1_bar_30 - location
        )
    

(:init


        (receptacleType StoveBurner_bar__plus_01_dot_55_bar__plus_00_dot_91_bar__plus_00_dot_08 StoveBurnerType)
        (receptacleType Fridge_bar__minus_00_dot_53_bar__plus_00_dot_00_bar__minus_01_dot_55 FridgeType)
        (receptacleType GarbageCan_bar__plus_01_dot_77_bar__plus_00_dot_00_bar__plus_01_dot_69 GarbageCanType)
        (receptacleType CoffeeMachine_bar__plus_00_dot_50_bar__plus_00_dot_90_bar__minus_01_dot_60 CoffeeMachineType)
        (receptacleType Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_30_bar__plus_00_dot_69 CabinetType)
        (receptacleType StoveBurner_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32 StoveBurnerType)
        (receptacleType Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_30_bar__plus_01_dot_31 CabinetType)
        (receptacleType Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_06_bar__plus_00_dot_19 CabinetType)
        (receptacleType Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_01_dot_21 CabinetType)
        (receptacleType Sink_bar__plus_01_dot_10_bar__plus_00_dot_91_bar__minus_01_dot_60_bar_SinkBasin SinkBasinType)
        (receptacleType Microwave_bar__plus_01_dot_75_bar__plus_00_dot_90_bar__minus_00_dot_84 MicrowaveType)
        (receptacleType CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02 CounterTopType)
        (receptacleType Toaster_bar__plus_00_dot_13_bar__plus_00_dot_90_bar__minus_01_dot_59 ToasterType)
        (receptacleType Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_06_bar__minus_00_dot_59 CabinetType)
        (receptacleType StoveBurner_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__plus_00_dot_08 StoveBurnerType)
        (receptacleType Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__plus_00_dot_67 CabinetType)
        (receptacleType Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_00_dot_61 CabinetType)
        (receptacleType CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52 CounterTopType)
        (receptacleType StoveBurner_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32 StoveBurnerType)
        (receptacleType Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_01_dot_89 CabinetType)
        (objectType PepperShaker_bar__plus_00_dot_84_bar__plus_00_dot_91_bar__minus_01_dot_23 PepperShakerType)
        (objectType Bowl_bar__plus_01_dot_81_bar__plus_01_dot_64_bar__plus_00_dot_38 BowlType)
        (objectType Egg_bar__minus_00_dot_89_bar__plus_00_dot_50_bar__minus_01_dot_67 EggType)
        (objectType Chair_bar__plus_00_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_72 ChairType)
        (objectType Plate_bar__minus_00_dot_74_bar__plus_01_dot_09_bar__minus_01_dot_59 PlateType)
        (objectType LightSwitch_bar__plus_02_dot_00_bar__plus_01_dot_26_bar__plus_03_dot_09 LightSwitchType)
        (objectType Spatula_bar__plus_01_dot_87_bar__plus_00_dot_93_bar__minus_01_dot_72 SpatulaType)
        (objectType ButterKnife_bar__plus_00_dot_55_bar__plus_00_dot_91_bar__minus_01_dot_35 ButterKnifeType)
        (objectType Cup_bar__plus_01_dot_88_bar__plus_01_dot_63_bar__minus_00_dot_68 CupType)
        (objectType Spatula_bar__plus_01_dot_50_bar__plus_00_dot_93_bar__plus_01_dot_18 SpatulaType)
        (objectType Sink_bar__plus_01_dot_10_bar__plus_00_dot_91_bar__minus_01_dot_60 SinkType)
        (objectType DishSponge_bar__plus_01_dot_77_bar__plus_00_dot_07_bar__plus_01_dot_54 DishSpongeType)
        (objectType Pot_bar__plus_01_dot_53_bar__plus_00_dot_91_bar__plus_00_dot_06 PotType)
        (objectType SoapBottle_bar__plus_00_dot_99_bar__plus_00_dot_91_bar__plus_00_dot_92 SoapBottleType)
        (objectType Mug_bar__plus_01_dot_71_bar__plus_01_dot_01_bar__minus_00_dot_68 MugType)
        (objectType Cup_bar__plus_01_dot_77_bar__plus_01_dot_01_bar__minus_00_dot_89 CupType)
        (objectType Pan_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32 PanType)
        (objectType StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__plus_00_dot_09 StoveKnobType)
        (objectType Apple_bar__minus_00_dot_79_bar__plus_01_dot_45_bar__minus_01_dot_63 AppleType)
        (objectType ButterKnife_bar__plus_01_dot_84_bar__plus_00_dot_91_bar__plus_00_dot_52 ButterKnifeType)
        (objectType StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__minus_00_dot_05 StoveKnobType)
        (objectType Window_bar__minus_00_dot_37_bar__plus_01_dot_59_bar__plus_03_dot_35 WindowType)
        (objectType ButterKnife_bar__plus_00_dot_30_bar__plus_00_dot_90_bar__minus_01_dot_30 ButterKnifeType)
        (objectType PepperShaker_bar__plus_00_dot_26_bar__plus_00_dot_91_bar__minus_01_dot_26 PepperShakerType)
        (objectType StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__minus_00_dot_18 StoveKnobType)
        (objectType PepperShaker_bar__plus_00_dot_48_bar__plus_00_dot_91_bar__plus_01_dot_27 PepperShakerType)
        (objectType Fork_bar__plus_01_dot_25_bar__plus_00_dot_91_bar__plus_00_dot_83 ForkType)
        (objectType Window_bar__plus_00_dot_78_bar__plus_01_dot_74_bar__minus_01_dot_89 WindowType)
        (objectType Plate_bar__plus_01_dot_81_bar__plus_01_dot_63_bar__minus_00_dot_90 PlateType)
        (objectType Chair_bar__plus_01_dot_09_bar__plus_00_dot_00_bar__plus_01_dot_74 ChairType)
        (objectType Tomato_bar__plus_01_dot_92_bar__plus_00_dot_97_bar__plus_00_dot_42 TomatoType)
        (objectType Fork_bar__plus_00_dot_99_bar__plus_00_dot_91_bar__plus_01_dot_00 ForkType)
        (objectType Potato_bar__plus_01_dot_03_bar__plus_00_dot_80_bar__minus_01_dot_71 PotatoType)
        (objectType Tomato_bar__plus_01_dot_76_bar__plus_00_dot_97_bar__plus_00_dot_57 TomatoType)
        (objectType Lettuce_bar__plus_01_dot_80_bar__plus_01_dot_01_bar__minus_01_dot_55 LettuceType)
        (objectType Bread_bar__plus_01_dot_62_bar__plus_00_dot_98_bar__plus_00_dot_76 BreadType)
        (objectType StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__minus_00_dot_32 StoveKnobType)
        (objectType SoapBottle_bar__plus_01_dot_88_bar__plus_01_dot_65_bar__minus_00_dot_50 SoapBottleType)
        (objectType SaltShaker_bar__plus_01_dot_45_bar__plus_00_dot_91_bar__plus_00_dot_36 SaltShakerType)
        (objectType Spoon_bar__plus_00_dot_89_bar__plus_00_dot_76_bar__minus_01_dot_65 SpoonType)
        (objectType Ladle_bar__plus_01_dot_17_bar__plus_00_dot_80_bar__minus_01_dot_60 LadleType)
        (objectType Knife_bar__plus_01_dot_76_bar__plus_00_dot_94_bar__plus_01_dot_09 KnifeType)
        (objectType SaltShaker_bar__plus_01_dot_94_bar__plus_00_dot_91_bar__minus_01_dot_37 SaltShakerType)
        (objectType Fork_bar__plus_01_dot_66_bar__plus_00_dot_91_bar__minus_01_dot_63 ForkType)
        (objectType SoapBottle_bar__plus_01_dot_76_bar__plus_00_dot_91_bar__plus_01_dot_27 SoapBottleType)
        (objectType Knife_bar__plus_00_dot_46_bar__plus_00_dot_94_bar__minus_01_dot_26 KnifeType)
        (objectType Pan_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32 PanType)
        (objectType Plate_bar__minus_00_dot_79_bar__plus_00_dot_46_bar__minus_01_dot_48 PlateType)
        (objectType Apple_bar__plus_01_dot_84_bar__plus_01_dot_07_bar__minus_00_dot_68 AppleType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain FridgeType BreadType)
        (canContain FridgeType BowlType)
        (canContain FridgeType PotType)
        (canContain FridgeType MugType)
        (canContain FridgeType EggType)
        (canContain FridgeType LettuceType)
        (canContain FridgeType PotatoType)
        (canContain FridgeType CupType)
        (canContain FridgeType PlateType)
        (canContain FridgeType TomatoType)
        (canContain FridgeType AppleType)
        (canContain FridgeType PanType)
        (canContain GarbageCanType BreadType)
        (canContain GarbageCanType DishSpongeType)
        (canContain GarbageCanType EggType)
        (canContain GarbageCanType SoapBottleType)
        (canContain GarbageCanType LettuceType)
        (canContain GarbageCanType PotatoType)
        (canContain GarbageCanType TomatoType)
        (canContain GarbageCanType AppleType)
        (canContain CoffeeMachineType MugType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain SinkBasinType DishSpongeType)
        (canContain SinkBasinType BowlType)
        (canContain SinkBasinType PotType)
        (canContain SinkBasinType MugType)
        (canContain SinkBasinType EggType)
        (canContain SinkBasinType LadleType)
        (canContain SinkBasinType ForkType)
        (canContain SinkBasinType SpoonType)
        (canContain SinkBasinType LettuceType)
        (canContain SinkBasinType PotatoType)
        (canContain SinkBasinType ButterKnifeType)
        (canContain SinkBasinType CupType)
        (canContain SinkBasinType PlateType)
        (canContain SinkBasinType TomatoType)
        (canContain SinkBasinType KnifeType)
        (canContain SinkBasinType AppleType)
        (canContain SinkBasinType PanType)
        (canContain SinkBasinType SpatulaType)
        (canContain MicrowaveType MugType)
        (canContain MicrowaveType PotatoType)
        (canContain MicrowaveType EggType)
        (canContain MicrowaveType AppleType)
        (canContain MicrowaveType TomatoType)
        (canContain MicrowaveType BreadType)
        (canContain MicrowaveType CupType)
        (canContain MicrowaveType PlateType)
        (canContain MicrowaveType BowlType)
        (canContain CounterTopType SaltShakerType)
        (canContain CounterTopType BreadType)
        (canContain CounterTopType DishSpongeType)
        (canContain CounterTopType BowlType)
        (canContain CounterTopType PotType)
        (canContain CounterTopType MugType)
        (canContain CounterTopType EggType)
        (canContain CounterTopType LadleType)
        (canContain CounterTopType ForkType)
        (canContain CounterTopType SpoonType)
        (canContain CounterTopType SoapBottleType)
        (canContain CounterTopType LettuceType)
        (canContain CounterTopType PotatoType)
        (canContain CounterTopType ButterKnifeType)
        (canContain CounterTopType CupType)
        (canContain CounterTopType PlateType)
        (canContain CounterTopType PepperShakerType)
        (canContain CounterTopType TomatoType)
        (canContain CounterTopType KnifeType)
        (canContain CounterTopType AppleType)
        (canContain CounterTopType PanType)
        (canContain CounterTopType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CounterTopType SaltShakerType)
        (canContain CounterTopType BreadType)
        (canContain CounterTopType DishSpongeType)
        (canContain CounterTopType BowlType)
        (canContain CounterTopType PotType)
        (canContain CounterTopType MugType)
        (canContain CounterTopType EggType)
        (canContain CounterTopType LadleType)
        (canContain CounterTopType ForkType)
        (canContain CounterTopType SpoonType)
        (canContain CounterTopType SoapBottleType)
        (canContain CounterTopType LettuceType)
        (canContain CounterTopType PotatoType)
        (canContain CounterTopType ButterKnifeType)
        (canContain CounterTopType CupType)
        (canContain CounterTopType PlateType)
        (canContain CounterTopType PepperShakerType)
        (canContain CounterTopType TomatoType)
        (canContain CounterTopType KnifeType)
        (canContain CounterTopType AppleType)
        (canContain CounterTopType PanType)
        (canContain CounterTopType SpatulaType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (pickupable PepperShaker_bar__plus_00_dot_84_bar__plus_00_dot_91_bar__minus_01_dot_23)
        (pickupable Bowl_bar__plus_01_dot_81_bar__plus_01_dot_64_bar__plus_00_dot_38)
        (pickupable Egg_bar__minus_00_dot_89_bar__plus_00_dot_50_bar__minus_01_dot_67)
        (pickupable Plate_bar__minus_00_dot_74_bar__plus_01_dot_09_bar__minus_01_dot_59)
        (pickupable Spatula_bar__plus_01_dot_87_bar__plus_00_dot_93_bar__minus_01_dot_72)
        (pickupable ButterKnife_bar__plus_00_dot_55_bar__plus_00_dot_91_bar__minus_01_dot_35)
        (pickupable Cup_bar__plus_01_dot_88_bar__plus_01_dot_63_bar__minus_00_dot_68)
        (pickupable Spatula_bar__plus_01_dot_50_bar__plus_00_dot_93_bar__plus_01_dot_18)
        (pickupable DishSponge_bar__plus_01_dot_77_bar__plus_00_dot_07_bar__plus_01_dot_54)
        (pickupable Pot_bar__plus_01_dot_53_bar__plus_00_dot_91_bar__plus_00_dot_06)
        (pickupable SoapBottle_bar__plus_00_dot_99_bar__plus_00_dot_91_bar__plus_00_dot_92)
        (pickupable Mug_bar__plus_01_dot_71_bar__plus_01_dot_01_bar__minus_00_dot_68)
        (pickupable Cup_bar__plus_01_dot_77_bar__plus_01_dot_01_bar__minus_00_dot_89)
        (pickupable Pan_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32)
        (pickupable Apple_bar__minus_00_dot_79_bar__plus_01_dot_45_bar__minus_01_dot_63)
        (pickupable ButterKnife_bar__plus_01_dot_84_bar__plus_00_dot_91_bar__plus_00_dot_52)
        (pickupable ButterKnife_bar__plus_00_dot_30_bar__plus_00_dot_90_bar__minus_01_dot_30)
        (pickupable PepperShaker_bar__plus_00_dot_26_bar__plus_00_dot_91_bar__minus_01_dot_26)
        (pickupable PepperShaker_bar__plus_00_dot_48_bar__plus_00_dot_91_bar__plus_01_dot_27)
        (pickupable Fork_bar__plus_01_dot_25_bar__plus_00_dot_91_bar__plus_00_dot_83)
        (pickupable Plate_bar__plus_01_dot_81_bar__plus_01_dot_63_bar__minus_00_dot_90)
        (pickupable Tomato_bar__plus_01_dot_92_bar__plus_00_dot_97_bar__plus_00_dot_42)
        (pickupable Fork_bar__plus_00_dot_99_bar__plus_00_dot_91_bar__plus_01_dot_00)
        (pickupable Potato_bar__plus_01_dot_03_bar__plus_00_dot_80_bar__minus_01_dot_71)
        (pickupable Tomato_bar__plus_01_dot_76_bar__plus_00_dot_97_bar__plus_00_dot_57)
        (pickupable Lettuce_bar__plus_01_dot_80_bar__plus_01_dot_01_bar__minus_01_dot_55)
        (pickupable Bread_bar__plus_01_dot_62_bar__plus_00_dot_98_bar__plus_00_dot_76)
        (pickupable SoapBottle_bar__plus_01_dot_88_bar__plus_01_dot_65_bar__minus_00_dot_50)
        (pickupable SaltShaker_bar__plus_01_dot_45_bar__plus_00_dot_91_bar__plus_00_dot_36)
        (pickupable Spoon_bar__plus_00_dot_89_bar__plus_00_dot_76_bar__minus_01_dot_65)
        (pickupable Ladle_bar__plus_01_dot_17_bar__plus_00_dot_80_bar__minus_01_dot_60)
        (pickupable Knife_bar__plus_01_dot_76_bar__plus_00_dot_94_bar__plus_01_dot_09)
        (pickupable SaltShaker_bar__plus_01_dot_94_bar__plus_00_dot_91_bar__minus_01_dot_37)
        (pickupable Fork_bar__plus_01_dot_66_bar__plus_00_dot_91_bar__minus_01_dot_63)
        (pickupable SoapBottle_bar__plus_01_dot_76_bar__plus_00_dot_91_bar__plus_01_dot_27)
        (pickupable Knife_bar__plus_00_dot_46_bar__plus_00_dot_94_bar__minus_01_dot_26)
        (pickupable Pan_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32)
        (pickupable Plate_bar__minus_00_dot_79_bar__plus_00_dot_46_bar__minus_01_dot_48)
        (pickupable Apple_bar__plus_01_dot_84_bar__plus_01_dot_07_bar__minus_00_dot_68)
        (isReceptacleObject Bowl_bar__plus_01_dot_81_bar__plus_01_dot_64_bar__plus_00_dot_38)
        (isReceptacleObject Plate_bar__minus_00_dot_74_bar__plus_01_dot_09_bar__minus_01_dot_59)
        (isReceptacleObject Cup_bar__plus_01_dot_88_bar__plus_01_dot_63_bar__minus_00_dot_68)
        (isReceptacleObject Pot_bar__plus_01_dot_53_bar__plus_00_dot_91_bar__plus_00_dot_06)
        (isReceptacleObject Mug_bar__plus_01_dot_71_bar__plus_01_dot_01_bar__minus_00_dot_68)
        (isReceptacleObject Cup_bar__plus_01_dot_77_bar__plus_01_dot_01_bar__minus_00_dot_89)
        (isReceptacleObject Pan_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32)
        (isReceptacleObject Plate_bar__plus_01_dot_81_bar__plus_01_dot_63_bar__minus_00_dot_90)
        (isReceptacleObject Pan_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32)
        (isReceptacleObject Plate_bar__minus_00_dot_79_bar__plus_00_dot_46_bar__minus_01_dot_48)
        (openable Fridge_bar__minus_00_dot_53_bar__plus_00_dot_00_bar__minus_01_dot_55)
        (openable Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_06_bar__plus_00_dot_19)
        (openable Microwave_bar__plus_01_dot_75_bar__plus_00_dot_90_bar__minus_00_dot_84)
        (openable Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_06_bar__minus_00_dot_59)
        (openable Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__plus_00_dot_67)
        (openable Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_00_dot_61)
        
        (atLocation agent1 loc_bar__minus_2_bar_5_bar_1_bar_30)
        
        (cleanable Bowl_bar__plus_01_dot_81_bar__plus_01_dot_64_bar__plus_00_dot_38)
        (cleanable Egg_bar__minus_00_dot_89_bar__plus_00_dot_50_bar__minus_01_dot_67)
        (cleanable Plate_bar__minus_00_dot_74_bar__plus_01_dot_09_bar__minus_01_dot_59)
        (cleanable Spatula_bar__plus_01_dot_87_bar__plus_00_dot_93_bar__minus_01_dot_72)
        (cleanable ButterKnife_bar__plus_00_dot_55_bar__plus_00_dot_91_bar__minus_01_dot_35)
        (cleanable Cup_bar__plus_01_dot_88_bar__plus_01_dot_63_bar__minus_00_dot_68)
        (cleanable Spatula_bar__plus_01_dot_50_bar__plus_00_dot_93_bar__plus_01_dot_18)
        (cleanable DishSponge_bar__plus_01_dot_77_bar__plus_00_dot_07_bar__plus_01_dot_54)
        (cleanable Pot_bar__plus_01_dot_53_bar__plus_00_dot_91_bar__plus_00_dot_06)
        (cleanable Mug_bar__plus_01_dot_71_bar__plus_01_dot_01_bar__minus_00_dot_68)
        (cleanable Cup_bar__plus_01_dot_77_bar__plus_01_dot_01_bar__minus_00_dot_89)
        (cleanable Pan_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32)
        (cleanable Apple_bar__minus_00_dot_79_bar__plus_01_dot_45_bar__minus_01_dot_63)
        (cleanable ButterKnife_bar__plus_01_dot_84_bar__plus_00_dot_91_bar__plus_00_dot_52)
        (cleanable ButterKnife_bar__plus_00_dot_30_bar__plus_00_dot_90_bar__minus_01_dot_30)
        (cleanable Fork_bar__plus_01_dot_25_bar__plus_00_dot_91_bar__plus_00_dot_83)
        (cleanable Plate_bar__plus_01_dot_81_bar__plus_01_dot_63_bar__minus_00_dot_90)
        (cleanable Tomato_bar__plus_01_dot_92_bar__plus_00_dot_97_bar__plus_00_dot_42)
        (cleanable Fork_bar__plus_00_dot_99_bar__plus_00_dot_91_bar__plus_01_dot_00)
        (cleanable Potato_bar__plus_01_dot_03_bar__plus_00_dot_80_bar__minus_01_dot_71)
        (cleanable Tomato_bar__plus_01_dot_76_bar__plus_00_dot_97_bar__plus_00_dot_57)
        (cleanable Lettuce_bar__plus_01_dot_80_bar__plus_01_dot_01_bar__minus_01_dot_55)
        (cleanable Spoon_bar__plus_00_dot_89_bar__plus_00_dot_76_bar__minus_01_dot_65)
        (cleanable Ladle_bar__plus_01_dot_17_bar__plus_00_dot_80_bar__minus_01_dot_60)
        (cleanable Knife_bar__plus_01_dot_76_bar__plus_00_dot_94_bar__plus_01_dot_09)
        (cleanable Fork_bar__plus_01_dot_66_bar__plus_00_dot_91_bar__minus_01_dot_63)
        (cleanable Knife_bar__plus_00_dot_46_bar__plus_00_dot_94_bar__minus_01_dot_26)
        (cleanable Pan_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32)
        (cleanable Plate_bar__minus_00_dot_79_bar__plus_00_dot_46_bar__minus_01_dot_48)
        (cleanable Apple_bar__plus_01_dot_84_bar__plus_01_dot_07_bar__minus_00_dot_68)
        
        (heatable Egg_bar__minus_00_dot_89_bar__plus_00_dot_50_bar__minus_01_dot_67)
        (heatable Plate_bar__minus_00_dot_74_bar__plus_01_dot_09_bar__minus_01_dot_59)
        (heatable Cup_bar__plus_01_dot_88_bar__plus_01_dot_63_bar__minus_00_dot_68)
        (heatable Mug_bar__plus_01_dot_71_bar__plus_01_dot_01_bar__minus_00_dot_68)
        (heatable Cup_bar__plus_01_dot_77_bar__plus_01_dot_01_bar__minus_00_dot_89)
        (heatable Apple_bar__minus_00_dot_79_bar__plus_01_dot_45_bar__minus_01_dot_63)
        (heatable Plate_bar__plus_01_dot_81_bar__plus_01_dot_63_bar__minus_00_dot_90)
        (heatable Tomato_bar__plus_01_dot_92_bar__plus_00_dot_97_bar__plus_00_dot_42)
        (heatable Potato_bar__plus_01_dot_03_bar__plus_00_dot_80_bar__minus_01_dot_71)
        (heatable Tomato_bar__plus_01_dot_76_bar__plus_00_dot_97_bar__plus_00_dot_57)
        (heatable Bread_bar__plus_01_dot_62_bar__plus_00_dot_98_bar__plus_00_dot_76)
        (heatable Plate_bar__minus_00_dot_79_bar__plus_00_dot_46_bar__minus_01_dot_48)
        (heatable Apple_bar__plus_01_dot_84_bar__plus_01_dot_07_bar__minus_00_dot_68)
        (coolable Bowl_bar__plus_01_dot_81_bar__plus_01_dot_64_bar__plus_00_dot_38)
        (coolable Egg_bar__minus_00_dot_89_bar__plus_00_dot_50_bar__minus_01_dot_67)
        (coolable Plate_bar__minus_00_dot_74_bar__plus_01_dot_09_bar__minus_01_dot_59)
        (coolable Cup_bar__plus_01_dot_88_bar__plus_01_dot_63_bar__minus_00_dot_68)
        (coolable Pot_bar__plus_01_dot_53_bar__plus_00_dot_91_bar__plus_00_dot_06)
        (coolable Mug_bar__plus_01_dot_71_bar__plus_01_dot_01_bar__minus_00_dot_68)
        (coolable Cup_bar__plus_01_dot_77_bar__plus_01_dot_01_bar__minus_00_dot_89)
        (coolable Pan_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32)
        (coolable Apple_bar__minus_00_dot_79_bar__plus_01_dot_45_bar__minus_01_dot_63)
        (coolable Plate_bar__plus_01_dot_81_bar__plus_01_dot_63_bar__minus_00_dot_90)
        (coolable Tomato_bar__plus_01_dot_92_bar__plus_00_dot_97_bar__plus_00_dot_42)
        (coolable Potato_bar__plus_01_dot_03_bar__plus_00_dot_80_bar__minus_01_dot_71)
        (coolable Tomato_bar__plus_01_dot_76_bar__plus_00_dot_97_bar__plus_00_dot_57)
        (coolable Lettuce_bar__plus_01_dot_80_bar__plus_01_dot_01_bar__minus_01_dot_55)
        (coolable Bread_bar__plus_01_dot_62_bar__plus_00_dot_98_bar__plus_00_dot_76)
        (coolable Pan_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32)
        (coolable Plate_bar__minus_00_dot_79_bar__plus_00_dot_46_bar__minus_01_dot_48)
        (coolable Apple_bar__plus_01_dot_84_bar__plus_01_dot_07_bar__minus_00_dot_68)
        
        
        
        
        
        (sliceable Egg_bar__minus_00_dot_89_bar__plus_00_dot_50_bar__minus_01_dot_67)
        (sliceable Apple_bar__minus_00_dot_79_bar__plus_01_dot_45_bar__minus_01_dot_63)
        (sliceable Tomato_bar__plus_01_dot_92_bar__plus_00_dot_97_bar__plus_00_dot_42)
        (sliceable Potato_bar__plus_01_dot_03_bar__plus_00_dot_80_bar__minus_01_dot_71)
        (sliceable Tomato_bar__plus_01_dot_76_bar__plus_00_dot_97_bar__plus_00_dot_57)
        (sliceable Lettuce_bar__plus_01_dot_80_bar__plus_01_dot_01_bar__minus_01_dot_55)
        (sliceable Bread_bar__plus_01_dot_62_bar__plus_00_dot_98_bar__plus_00_dot_76)
        (sliceable Apple_bar__plus_01_dot_84_bar__plus_01_dot_07_bar__minus_00_dot_68)
        
        (inReceptacle Bread_bar__plus_01_dot_62_bar__plus_00_dot_98_bar__plus_00_dot_76 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle PepperShaker_bar__plus_00_dot_48_bar__plus_00_dot_91_bar__plus_01_dot_27 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle SaltShaker_bar__plus_01_dot_45_bar__plus_00_dot_91_bar__plus_00_dot_36 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle Fork_bar__plus_01_dot_25_bar__plus_00_dot_91_bar__plus_00_dot_83 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle ButterKnife_bar__plus_01_dot_84_bar__plus_00_dot_91_bar__plus_00_dot_52 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle Fork_bar__plus_00_dot_99_bar__plus_00_dot_91_bar__plus_01_dot_00 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle Knife_bar__plus_01_dot_76_bar__plus_00_dot_94_bar__plus_01_dot_09 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle Spatula_bar__plus_01_dot_50_bar__plus_00_dot_93_bar__plus_01_dot_18 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle SoapBottle_bar__plus_01_dot_76_bar__plus_00_dot_91_bar__plus_01_dot_27 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle SoapBottle_bar__plus_00_dot_99_bar__plus_00_dot_91_bar__plus_00_dot_92 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle Tomato_bar__plus_01_dot_92_bar__plus_00_dot_97_bar__plus_00_dot_42 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle Tomato_bar__plus_01_dot_76_bar__plus_00_dot_97_bar__plus_00_dot_57 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02)
        (inReceptacle Pan_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32 StoveBurner_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32)
        (inReceptacle Pan_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32 StoveBurner_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32)
        (inReceptacle Lettuce_bar__plus_01_dot_80_bar__plus_01_dot_01_bar__minus_01_dot_55 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52)
        (inReceptacle PepperShaker_bar__plus_00_dot_26_bar__plus_00_dot_91_bar__minus_01_dot_26 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52)
        (inReceptacle PepperShaker_bar__plus_00_dot_84_bar__plus_00_dot_91_bar__minus_01_dot_23 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52)
        (inReceptacle Spatula_bar__plus_01_dot_87_bar__plus_00_dot_93_bar__minus_01_dot_72 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52)
        (inReceptacle ButterKnife_bar__plus_00_dot_55_bar__plus_00_dot_91_bar__minus_01_dot_35 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52)
        (inReceptacle SaltShaker_bar__plus_01_dot_94_bar__plus_00_dot_91_bar__minus_01_dot_37 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52)
        (inReceptacle Fork_bar__plus_01_dot_66_bar__plus_00_dot_91_bar__minus_01_dot_63 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52)
        (inReceptacle Knife_bar__plus_00_dot_46_bar__plus_00_dot_94_bar__minus_01_dot_26 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52)
        (inReceptacle ButterKnife_bar__plus_00_dot_30_bar__plus_00_dot_90_bar__minus_01_dot_30 CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52)
        (inReceptacle Pot_bar__plus_01_dot_53_bar__plus_00_dot_91_bar__plus_00_dot_06 StoveBurner_bar__plus_01_dot_55_bar__plus_00_dot_91_bar__plus_00_dot_08)
        (inReceptacle Bowl_bar__plus_01_dot_81_bar__plus_01_dot_64_bar__plus_00_dot_38 Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__plus_00_dot_67)
        (inReceptacle SoapBottle_bar__plus_01_dot_88_bar__plus_01_dot_65_bar__minus_00_dot_50 Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_06_bar__minus_00_dot_59)
        (inReceptacle Plate_bar__plus_01_dot_81_bar__plus_01_dot_63_bar__minus_00_dot_90 Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_00_dot_61)
        (inReceptacle Cup_bar__plus_01_dot_88_bar__plus_01_dot_63_bar__minus_00_dot_68 Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_00_dot_61)
        (inReceptacle DishSponge_bar__plus_01_dot_77_bar__plus_00_dot_07_bar__plus_01_dot_54 GarbageCan_bar__plus_01_dot_77_bar__plus_00_dot_00_bar__plus_01_dot_69)
        (inReceptacle Potato_bar__plus_01_dot_03_bar__plus_00_dot_80_bar__minus_01_dot_71 Sink_bar__plus_01_dot_10_bar__plus_00_dot_91_bar__minus_01_dot_60_bar_SinkBasin)
        (inReceptacle Spoon_bar__plus_00_dot_89_bar__plus_00_dot_76_bar__minus_01_dot_65 Sink_bar__plus_01_dot_10_bar__plus_00_dot_91_bar__minus_01_dot_60_bar_SinkBasin)
        (inReceptacle Ladle_bar__plus_01_dot_17_bar__plus_00_dot_80_bar__minus_01_dot_60 Sink_bar__plus_01_dot_10_bar__plus_00_dot_91_bar__minus_01_dot_60_bar_SinkBasin)
        (inReceptacle Mug_bar__plus_01_dot_71_bar__plus_01_dot_01_bar__minus_00_dot_68 Microwave_bar__plus_01_dot_75_bar__plus_00_dot_90_bar__minus_00_dot_84)
        (inReceptacle Cup_bar__plus_01_dot_77_bar__plus_01_dot_01_bar__minus_00_dot_89 Microwave_bar__plus_01_dot_75_bar__plus_00_dot_90_bar__minus_00_dot_84)
        (inReceptacle Apple_bar__plus_01_dot_84_bar__plus_01_dot_07_bar__minus_00_dot_68 Microwave_bar__plus_01_dot_75_bar__plus_00_dot_90_bar__minus_00_dot_84)
        (inReceptacle Egg_bar__minus_00_dot_89_bar__plus_00_dot_50_bar__minus_01_dot_67 Fridge_bar__minus_00_dot_53_bar__plus_00_dot_00_bar__minus_01_dot_55)
        (inReceptacle Plate_bar__minus_00_dot_74_bar__plus_01_dot_09_bar__minus_01_dot_59 Fridge_bar__minus_00_dot_53_bar__plus_00_dot_00_bar__minus_01_dot_55)
        (inReceptacle Plate_bar__minus_00_dot_79_bar__plus_00_dot_46_bar__minus_01_dot_48 Fridge_bar__minus_00_dot_53_bar__plus_00_dot_00_bar__minus_01_dot_55)
        (inReceptacle Apple_bar__minus_00_dot_79_bar__plus_01_dot_45_bar__minus_01_dot_63 Fridge_bar__minus_00_dot_53_bar__plus_00_dot_00_bar__minus_01_dot_55)
        
        
        (receptacleAtLocation Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__plus_00_dot_67 loc_bar_4_bar_1_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_00_dot_61 loc_bar_3_bar__minus_3_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_01_dot_21 loc_bar_4_bar__minus_3_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_05_bar__minus_01_dot_89 loc_bar_4_bar__minus_3_bar_2_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_06_bar__plus_00_dot_19 loc_bar_4_bar_0_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_06_bar__minus_00_dot_59 loc_bar_2_bar_0_bar_1_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_30_bar__plus_00_dot_69 loc_bar_4_bar_1_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_66_bar__plus_02_dot_30_bar__plus_01_dot_31 loc_bar_6_bar_9_bar_2_bar__minus_30)
        (receptacleAtLocation CoffeeMachine_bar__plus_00_dot_50_bar__plus_00_dot_90_bar__minus_01_dot_60 loc_bar_2_bar__minus_3_bar_2_bar_45)
        (receptacleAtLocation CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__plus_01_dot_02 loc_bar_4_bar_1_bar_0_bar_45)
        (receptacleAtLocation CounterTop_bar__plus_00_dot_99_bar__plus_00_dot_95_bar__minus_01_dot_52 loc_bar_2_bar__minus_3_bar_2_bar_45)
        (receptacleAtLocation Fridge_bar__minus_00_dot_53_bar__plus_00_dot_00_bar__minus_01_dot_55 loc_bar__minus_2_bar__minus_2_bar_2_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_01_dot_77_bar__plus_00_dot_00_bar__plus_01_dot_69 loc_bar_6_bar_9_bar_2_bar_60)
        (receptacleAtLocation Microwave_bar__plus_01_dot_75_bar__plus_00_dot_90_bar__minus_00_dot_84 loc_bar_4_bar__minus_3_bar_1_bar_45)
        (receptacleAtLocation Sink_bar__plus_01_dot_10_bar__plus_00_dot_91_bar__minus_01_dot_60_bar_SinkBasin loc_bar_4_bar__minus_3_bar_2_bar_45)
        (receptacleAtLocation StoveBurner_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32 loc_bar_4_bar__minus_1_bar_1_bar_60)
        (receptacleAtLocation StoveBurner_bar__plus_01_dot_55_bar__plus_00_dot_91_bar__plus_00_dot_08 loc_bar_4_bar_0_bar_1_bar_60)
        (receptacleAtLocation StoveBurner_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__plus_00_dot_08 loc_bar_4_bar_0_bar_1_bar_45)
        (receptacleAtLocation StoveBurner_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32 loc_bar_4_bar__minus_1_bar_1_bar_45)
        (receptacleAtLocation Toaster_bar__plus_00_dot_13_bar__plus_00_dot_90_bar__minus_01_dot_59 loc_bar_1_bar__minus_3_bar_2_bar_45)
        (objectAtLocation PepperShaker_bar__plus_00_dot_26_bar__plus_00_dot_91_bar__minus_01_dot_26 loc_bar_2_bar__minus_3_bar_2_bar_45)
        (objectAtLocation ButterKnife_bar__plus_00_dot_30_bar__plus_00_dot_90_bar__minus_01_dot_30 loc_bar_2_bar__minus_3_bar_2_bar_45)
        (objectAtLocation Knife_bar__plus_01_dot_76_bar__plus_00_dot_94_bar__plus_01_dot_09 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation Spatula_bar__plus_01_dot_87_bar__plus_00_dot_93_bar__minus_01_dot_72 loc_bar_2_bar__minus_3_bar_2_bar_45)
        (objectAtLocation Tomato_bar__plus_01_dot_92_bar__plus_00_dot_97_bar__plus_00_dot_42 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation Pan_bar__plus_01_dot_81_bar__plus_00_dot_91_bar__minus_00_dot_32 loc_bar_4_bar__minus_1_bar_1_bar_45)
        (objectAtLocation SaltShaker_bar__plus_01_dot_94_bar__plus_00_dot_91_bar__minus_01_dot_37 loc_bar_2_bar__minus_3_bar_2_bar_45)
        (objectAtLocation Apple_bar__minus_00_dot_79_bar__plus_01_dot_45_bar__minus_01_dot_63 loc_bar__minus_2_bar__minus_2_bar_2_bar_60)
        (objectAtLocation Fork_bar__plus_01_dot_66_bar__plus_00_dot_91_bar__minus_01_dot_63 loc_bar_2_bar__minus_3_bar_2_bar_45)
        (objectAtLocation Cup_bar__plus_01_dot_77_bar__plus_01_dot_01_bar__minus_00_dot_89 loc_bar_4_bar__minus_3_bar_1_bar_45)
        (objectAtLocation SoapBottle_bar__plus_01_dot_76_bar__plus_00_dot_91_bar__plus_01_dot_27 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation Plate_bar__plus_01_dot_81_bar__plus_01_dot_63_bar__minus_00_dot_90 loc_bar_3_bar__minus_3_bar_1_bar__minus_30)
        (objectAtLocation PepperShaker_bar__plus_00_dot_48_bar__plus_00_dot_91_bar__plus_01_dot_27 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation SoapBottle_bar__plus_01_dot_88_bar__plus_01_dot_65_bar__minus_00_dot_50 loc_bar_2_bar_0_bar_1_bar__minus_15)
        (objectAtLocation Plate_bar__minus_00_dot_74_bar__plus_01_dot_09_bar__minus_01_dot_59 loc_bar__minus_2_bar__minus_2_bar_2_bar_60)
        (objectAtLocation Fork_bar__plus_01_dot_25_bar__plus_00_dot_91_bar__plus_00_dot_83 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation ButterKnife_bar__plus_01_dot_84_bar__plus_00_dot_91_bar__plus_00_dot_52 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation Chair_bar__plus_00_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_72 loc_bar_0_bar_7_bar_1_bar_60)
        (objectAtLocation Chair_bar__plus_01_dot_09_bar__plus_00_dot_00_bar__plus_01_dot_74 loc_bar_4_bar_9_bar_2_bar_60)
        (objectAtLocation Sink_bar__plus_01_dot_10_bar__plus_00_dot_91_bar__minus_01_dot_60 loc_bar_4_bar__minus_3_bar_2_bar_45)
        (objectAtLocation Plate_bar__minus_00_dot_79_bar__plus_00_dot_46_bar__minus_01_dot_48 loc_bar__minus_2_bar__minus_2_bar_2_bar_60)
        (objectAtLocation SoapBottle_bar__plus_00_dot_99_bar__plus_00_dot_91_bar__plus_00_dot_92 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation Cup_bar__plus_01_dot_88_bar__plus_01_dot_63_bar__minus_00_dot_68 loc_bar_3_bar__minus_3_bar_1_bar__minus_30)
        (objectAtLocation Egg_bar__minus_00_dot_89_bar__plus_00_dot_50_bar__minus_01_dot_67 loc_bar__minus_2_bar__minus_2_bar_2_bar_60)
        (objectAtLocation Fork_bar__plus_00_dot_99_bar__plus_00_dot_91_bar__plus_01_dot_00 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation Ladle_bar__plus_01_dot_17_bar__plus_00_dot_80_bar__minus_01_dot_60 loc_bar_4_bar__minus_3_bar_2_bar_45)
        (objectAtLocation Apple_bar__plus_01_dot_84_bar__plus_01_dot_07_bar__minus_00_dot_68 loc_bar_4_bar__minus_3_bar_1_bar_45)
        (objectAtLocation SaltShaker_bar__plus_01_dot_45_bar__plus_00_dot_91_bar__plus_00_dot_36 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation Pan_bar__plus_01_dot_54_bar__plus_00_dot_91_bar__minus_00_dot_32 loc_bar_4_bar__minus_1_bar_1_bar_60)
        (objectAtLocation Tomato_bar__plus_01_dot_76_bar__plus_00_dot_97_bar__plus_00_dot_57 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation Window_bar__minus_00_dot_37_bar__plus_01_dot_59_bar__plus_03_dot_35 loc_bar__minus_1_bar_11_bar_0_bar_0)
        (objectAtLocation Window_bar__plus_00_dot_78_bar__plus_01_dot_74_bar__minus_01_dot_89 loc_bar_3_bar__minus_3_bar_2_bar_0)
        (objectAtLocation Spatula_bar__plus_01_dot_50_bar__plus_00_dot_93_bar__plus_01_dot_18 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation Potato_bar__plus_01_dot_03_bar__plus_00_dot_80_bar__minus_01_dot_71 loc_bar_4_bar__minus_3_bar_2_bar_45)
        (objectAtLocation Knife_bar__plus_00_dot_46_bar__plus_00_dot_94_bar__minus_01_dot_26 loc_bar_2_bar__minus_3_bar_2_bar_45)
        (objectAtLocation ButterKnife_bar__plus_00_dot_55_bar__plus_00_dot_91_bar__minus_01_dot_35 loc_bar_2_bar__minus_3_bar_2_bar_45)
        (objectAtLocation StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__plus_00_dot_09 loc_bar_4_bar_0_bar_1_bar_60)
        (objectAtLocation StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__minus_00_dot_18 loc_bar_4_bar__minus_1_bar_1_bar_60)
        (objectAtLocation StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__minus_00_dot_32 loc_bar_4_bar__minus_1_bar_1_bar_60)
        (objectAtLocation StoveKnob_bar__plus_01_dot_36_bar__plus_00_dot_82_bar__minus_00_dot_05 loc_bar_4_bar_0_bar_1_bar_60)
        (objectAtLocation Bread_bar__plus_01_dot_62_bar__plus_00_dot_98_bar__plus_00_dot_76 loc_bar_4_bar_1_bar_0_bar_45)
        (objectAtLocation Lettuce_bar__plus_01_dot_80_bar__plus_01_dot_01_bar__minus_01_dot_55 loc_bar_2_bar__minus_3_bar_2_bar_45)
        (objectAtLocation PepperShaker_bar__plus_00_dot_84_bar__plus_00_dot_91_bar__minus_01_dot_23 loc_bar_2_bar__minus_3_bar_2_bar_45)
        (objectAtLocation Pot_bar__plus_01_dot_53_bar__plus_00_dot_91_bar__plus_00_dot_06 loc_bar_4_bar_0_bar_1_bar_60)
        (objectAtLocation LightSwitch_bar__plus_02_dot_00_bar__plus_01_dot_26_bar__plus_03_dot_09 loc_bar_6_bar_11_bar_1_bar_45)
        (objectAtLocation DishSponge_bar__plus_01_dot_77_bar__plus_00_dot_07_bar__plus_01_dot_54 loc_bar_6_bar_9_bar_2_bar_60)
        (objectAtLocation Spoon_bar__plus_00_dot_89_bar__plus_00_dot_76_bar__minus_01_dot_65 loc_bar_4_bar__minus_3_bar_2_bar_45)
        (objectAtLocation Bowl_bar__plus_01_dot_81_bar__plus_01_dot_64_bar__plus_00_dot_38 loc_bar_4_bar_1_bar_1_bar__minus_30)
        (objectAtLocation Mug_bar__plus_01_dot_71_bar__plus_01_dot_01_bar__minus_00_dot_68 loc_bar_4_bar__minus_3_bar_1_bar_45)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 PanType)
                                    (receptacleType ?r CounterTopType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 PanType)
                                            (receptacleType ?r CounterTopType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            