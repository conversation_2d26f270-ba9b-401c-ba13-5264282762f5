{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000243.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000244.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000245.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000246.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000247.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000248.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000249.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000254.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000255.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000256.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000258.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000259.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000260.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000261.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000262.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000263.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000264.png", "low_idx": 40}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-5|4|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-5.15259696, -5.15259696, 1.7152, 1.7152, 3.8470384, 3.8470384]], "coordinateReceptacleObjectId": ["SinkBasin", [-5.5576, -5.5576, 1.7152, 1.7152, 3.684, 3.684]], "forceVisible": true, "objectId": "Egg|-01.29|+00.96|+00.43"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-6|4|1|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-1.468, -1.468, 1.72, 1.72, 4.4524064, 4.4524064]], "forceVisible": true, "objectId": "Microwave|-00.37|+01.11|+00.43"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-10|11|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-5.15259696, -5.15259696, 1.7152, 1.7152, 3.8470384, 3.8470384]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-14.076, -14.076, 10.868, 10.868, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|-01.29|+00.96|+00.43", "receptacleObjectId": "<PERSON><PERSON>|-03.52|+00.00|+02.72"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.29|+00.96|+00.43"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [154, 153, 169, 170], "mask": [[45759, 5], [46058, 8], [46357, 10], [46656, 12], [46955, 13], [47255, 14], [47554, 15], [47854, 16], [48154, 16], [48454, 16], [48754, 16], [49054, 15], [49354, 15], [49655, 14], [49955, 13], [50256, 11], [50557, 9], [50859, 6]], "point": [161, 160]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.37|+01.11|+00.43"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [223, 27, 299, 142], "mask": [[8042, 58], [8331, 69], [8631, 69], [8931, 69], [9231, 69], [9531, 69], [9831, 69], [10130, 70], [10430, 70], [10730, 70], [11030, 70], [11330, 70], [11630, 70], [11930, 70], [12230, 70], [12529, 71], [12829, 71], [13129, 71], [13429, 71], [13729, 71], [14029, 71], [14329, 71], [14628, 72], [14928, 72], [15228, 72], [15528, 72], [15828, 72], [16128, 72], [16428, 72], [16727, 73], [17027, 73], [17327, 73], [17627, 73], [17927, 73], [18227, 73], [18527, 73], [18826, 74], [19126, 74], [19426, 74], [19726, 74], [20026, 74], [20326, 74], [20626, 74], [20925, 75], [21225, 75], [21525, 75], [21825, 75], [22125, 75], [22425, 75], [22725, 75], [23024, 76], [23324, 76], [23624, 76], [23924, 76], [24224, 76], [24524, 76], [24824, 76], [25123, 77], [25423, 77], [25723, 77], [26023, 77], [26323, 77], [26624, 76], [26925, 75], [27225, 75], [27526, 74], [27827, 73], [28128, 72], [28429, 71], [28730, 70], [29030, 70], [29331, 69], [29632, 68], [29933, 67], [30234, 66], [30535, 65], [30835, 65], [31136, 64], [31437, 63], [31738, 62], [32039, 61], [32340, 60], [32641, 59], [32941, 59], [33242, 58], [33543, 57], [33844, 56], [34145, 55], [34446, 54], [34746, 54], [35047, 53], [35348, 51], [35649, 46], [35950, 43], [36251, 41], [36551, 41], [36852, 40], [37153, 38], [37454, 37], [37755, 36], [38056, 35], [38356, 34], [38657, 33], [38958, 32], [39259, 30], [39560, 29], [39861, 28], [40161, 27], [40462, 26], [40763, 25], [41075, 13], [41376, 11], [41683, 4], [41983, 4], [42283, 3], [42583, 3]], "point": [261, 83]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.29|+00.96|+00.43", "placeStationary": true, "receptacleObjectId": "Microwave|-00.37|+01.11|+00.43"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [146, 27, 299, 142], "mask": [[7946, 95], [8042, 58], [8246, 154], [8546, 154], [8846, 154], [9146, 154], [9446, 154], [9746, 154], [10046, 154], [10346, 154], [10646, 154], [10946, 154], [11246, 154], [11546, 154], [11846, 154], [12146, 154], [12446, 154], [12746, 154], [13046, 154], [13346, 154], [13646, 154], [13946, 154], [14246, 154], [14546, 154], [14846, 154], [15146, 154], [15446, 154], [15746, 154], [16046, 154], [16346, 154], [16646, 154], [16946, 154], [17246, 154], [17546, 154], [17846, 154], [18146, 154], [18446, 154], [18746, 154], [19046, 154], [19346, 154], [19646, 154], [19946, 154], [20246, 154], [20546, 154], [20846, 154], [21146, 154], [21446, 154], [21746, 154], [22046, 154], [22346, 154], [22646, 154], [22946, 154], [23246, 154], [23546, 154], [23846, 154], [24146, 154], [24446, 154], [24746, 154], [25046, 154], [25346, 154], [25646, 154], [25946, 154], [26246, 154], [26631, 69], [26932, 68], [27233, 67], [27534, 66], [27835, 65], [28136, 64], [28437, 63], [28738, 62], [29039, 61], [29339, 61], [29640, 60], [29941, 59], [30242, 58], [30543, 57], [30844, 56], [31145, 55], [31446, 54], [31747, 53], [32048, 52], [32349, 51], [32650, 50], [32950, 50], [33251, 49], [33552, 48], [33853, 47], [34154, 46], [34454, 46], [34754, 46], [35053, 47], [35353, 46], [35653, 42], [35953, 40], [36253, 39], [36552, 40], [36852, 40], [37153, 38], [37454, 37], [37755, 36], [38056, 35], [38356, 34], [38657, 33], [38958, 32], [39259, 30], [39560, 29], [39861, 28], [40161, 27], [40462, 26], [40763, 25], [41075, 13], [41376, 11], [41683, 4], [41983, 4], [42283, 3], [42583, 3]], "point": [222, 83]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.37|+01.11|+00.43"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [146, 27, 299, 142], "mask": [[7946, 95], [8042, 58], [8246, 154], [8546, 154], [8846, 154], [9146, 154], [9446, 154], [9746, 154], [10046, 154], [10346, 154], [10646, 154], [10946, 154], [11246, 154], [11546, 154], [11846, 154], [12146, 154], [12446, 154], [12746, 154], [13046, 154], [13346, 154], [13646, 154], [13946, 154], [14246, 154], [14546, 154], [14846, 154], [15146, 154], [15446, 154], [15746, 154], [16046, 154], [16346, 154], [16646, 154], [16946, 154], [17246, 154], [17546, 154], [17846, 154], [18146, 154], [18446, 154], [18746, 154], [19046, 154], [19346, 154], [19646, 154], [19946, 154], [20246, 154], [20546, 154], [20846, 154], [21146, 106], [21257, 43], [21446, 104], [21558, 42], [21746, 104], [21858, 42], [22046, 103], [22159, 41], [22346, 102], [22459, 41], [22646, 102], [22759, 41], [22946, 102], [23059, 41], [23246, 101], [23359, 41], [23546, 101], [23658, 42], [23846, 101], [23958, 42], [24146, 102], [24258, 42], [24446, 102], [24557, 43], [24746, 103], [24856, 44], [25046, 104], [25155, 45], [25346, 154], [25646, 154], [25946, 154], [26246, 154], [26631, 69], [26932, 68], [27233, 67], [27534, 66], [27835, 65], [28136, 64], [28437, 63], [28738, 62], [29039, 61], [29339, 61], [29640, 60], [29941, 59], [30242, 58], [30543, 57], [30844, 56], [31145, 55], [31446, 54], [31747, 53], [32048, 52], [32349, 51], [32650, 50], [32950, 50], [33251, 49], [33552, 48], [33853, 47], [34154, 46], [34454, 46], [34754, 46], [35053, 47], [35353, 46], [35653, 42], [35953, 40], [36253, 39], [36552, 40], [36852, 40], [37153, 38], [37454, 37], [37755, 36], [38056, 35], [38356, 34], [38657, 33], [38958, 32], [39259, 30], [39560, 29], [39861, 28], [40161, 27], [40462, 26], [40763, 25], [41075, 13], [41376, 11], [41683, 4], [41983, 4], [42283, 3], [42583, 3]], "point": [222, 83]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-1.468, -1.468, 1.72, 1.72, 4.4524064, 4.4524064]], "forceVisible": true, "objectId": "Microwave|-00.37|+01.11|+00.43"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [223, 27, 299, 142], "mask": [[8042, 58], [8331, 69], [8631, 69], [8931, 69], [9231, 69], [9531, 69], [9831, 69], [10130, 70], [10430, 70], [10730, 70], [11030, 70], [11330, 70], [11630, 70], [11930, 70], [12230, 70], [12529, 71], [12829, 71], [13129, 71], [13429, 71], [13729, 71], [14029, 71], [14329, 71], [14628, 72], [14928, 72], [15228, 72], [15528, 72], [15828, 72], [16128, 72], [16428, 72], [16727, 73], [17027, 73], [17327, 73], [17627, 73], [17927, 73], [18227, 73], [18527, 73], [18826, 74], [19126, 74], [19426, 74], [19726, 74], [20026, 74], [20326, 74], [20626, 74], [20925, 75], [21225, 75], [21525, 75], [21825, 75], [22125, 75], [22425, 75], [22725, 75], [23024, 76], [23324, 76], [23624, 76], [23924, 76], [24224, 76], [24524, 76], [24824, 76], [25123, 77], [25423, 77], [25723, 77], [26023, 77], [26323, 77], [26624, 76], [26925, 75], [27225, 75], [27526, 74], [27827, 73], [28128, 72], [28429, 71], [28730, 70], [29030, 70], [29331, 69], [29632, 68], [29933, 67], [30234, 66], [30535, 65], [30835, 65], [31136, 64], [31437, 63], [31738, 62], [32039, 61], [32340, 60], [32641, 59], [32941, 59], [33242, 58], [33543, 57], [33844, 56], [34145, 55], [34446, 54], [34746, 54], [35047, 53], [35348, 51], [35649, 46], [35950, 43], [36251, 41], [36551, 41], [36852, 40], [37153, 38], [37454, 37], [37755, 36], [38056, 35], [38356, 34], [38657, 33], [38958, 32], [39259, 30], [39560, 29], [39861, 28], [40161, 27], [40462, 26], [40763, 25], [41075, 13], [41376, 11], [41683, 4], [41983, 4], [42283, 3], [42583, 3]], "point": [261, 83]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-1.468, -1.468, 1.72, 1.72, 4.4524064, 4.4524064]], "forceVisible": true, "objectId": "Microwave|-00.37|+01.11|+00.43"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [223, 27, 299, 142], "mask": [[8042, 58], [8331, 69], [8631, 69], [8931, 69], [9231, 69], [9531, 69], [9831, 69], [10130, 70], [10430, 70], [10730, 70], [11030, 70], [11330, 70], [11630, 70], [11930, 70], [12230, 70], [12529, 71], [12829, 71], [13129, 71], [13429, 71], [13729, 71], [14029, 71], [14329, 71], [14628, 72], [14928, 72], [15228, 72], [15528, 72], [15828, 72], [16128, 72], [16428, 72], [16727, 73], [17027, 73], [17327, 73], [17627, 73], [17927, 73], [18227, 73], [18527, 73], [18826, 74], [19126, 74], [19426, 74], [19726, 74], [20026, 74], [20326, 74], [20626, 74], [20925, 75], [21225, 75], [21525, 75], [21825, 75], [22125, 75], [22425, 75], [22725, 75], [23024, 76], [23324, 76], [23624, 76], [23924, 76], [24224, 76], [24524, 76], [24824, 76], [25123, 77], [25423, 77], [25723, 77], [26023, 77], [26323, 77], [26624, 76], [26925, 75], [27225, 75], [27526, 74], [27827, 73], [28128, 72], [28429, 71], [28730, 70], [29030, 70], [29331, 69], [29632, 68], [29933, 67], [30234, 66], [30535, 65], [30835, 65], [31136, 64], [31437, 63], [31738, 62], [32039, 61], [32340, 60], [32641, 59], [32941, 59], [33242, 58], [33543, 57], [33844, 56], [34145, 55], [34446, 54], [34746, 54], [35047, 53], [35348, 51], [35649, 46], [35950, 43], [36251, 41], [36551, 41], [36852, 40], [37153, 38], [37454, 37], [37755, 36], [38056, 35], [38356, 34], [38657, 33], [38958, 32], [39259, 30], [39560, 29], [39861, 28], [40161, 27], [40462, 26], [40763, 25], [41075, 13], [41376, 11], [41683, 4], [41983, 4], [42283, 3], [42583, 3]], "point": [261, 83]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.37|+01.11|+00.43"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [223, 27, 299, 142], "mask": [[8042, 58], [8331, 69], [8631, 69], [8931, 69], [9231, 69], [9531, 69], [9831, 69], [10130, 70], [10430, 70], [10730, 70], [11030, 70], [11330, 70], [11630, 70], [11930, 70], [12230, 70], [12529, 71], [12829, 71], [13129, 71], [13429, 71], [13729, 71], [14029, 71], [14329, 71], [14628, 72], [14928, 72], [15228, 72], [15528, 72], [15828, 72], [16128, 72], [16428, 72], [16727, 73], [17027, 73], [17327, 73], [17627, 73], [17927, 73], [18227, 73], [18527, 73], [18826, 74], [19126, 74], [19426, 74], [19726, 74], [20026, 74], [20326, 74], [20626, 74], [20925, 75], [21225, 75], [21525, 75], [21825, 75], [22125, 75], [22425, 75], [22725, 75], [23024, 76], [23324, 76], [23624, 76], [23924, 76], [24224, 76], [24524, 76], [24824, 76], [25123, 77], [25423, 77], [25723, 77], [26023, 77], [26323, 77], [26624, 76], [26925, 75], [27225, 75], [27526, 74], [27827, 73], [28128, 72], [28429, 71], [28730, 70], [29030, 70], [29331, 69], [29632, 68], [29933, 67], [30234, 66], [30535, 65], [30835, 65], [31136, 64], [31437, 63], [31738, 62], [32039, 61], [32340, 60], [32641, 59], [32941, 59], [33242, 58], [33543, 57], [33844, 56], [34145, 55], [34446, 54], [34746, 54], [35047, 53], [35348, 51], [35649, 46], [35950, 43], [36251, 41], [36551, 41], [36852, 40], [37153, 38], [37454, 37], [37755, 36], [38056, 35], [38356, 34], [38657, 33], [38958, 32], [39259, 30], [39560, 29], [39861, 28], [40161, 27], [40462, 26], [40763, 25], [41075, 13], [41376, 11], [41683, 4], [41983, 4], [42283, 3], [42583, 3]], "point": [261, 83]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.29|+00.96|+00.43"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [247, 71, 258, 84], "mask": [[21252, 5], [21550, 8], [21850, 8], [22149, 10], [22448, 11], [22748, 11], [23048, 11], [23347, 12], [23647, 11], [23947, 11], [24248, 10], [24548, 9], [24849, 7], [25150, 5]], "point": [252, 76]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.37|+01.11|+00.43"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [146, 27, 299, 142], "mask": [[7946, 95], [8042, 58], [8246, 154], [8546, 154], [8846, 154], [9146, 154], [9446, 154], [9746, 154], [10046, 154], [10346, 154], [10646, 154], [10946, 154], [11246, 154], [11546, 154], [11846, 154], [12146, 154], [12446, 154], [12746, 154], [13046, 154], [13346, 154], [13646, 154], [13946, 154], [14246, 154], [14546, 154], [14846, 154], [15146, 154], [15446, 154], [15746, 154], [16046, 154], [16346, 154], [16646, 154], [16946, 154], [17246, 154], [17546, 154], [17846, 154], [18146, 154], [18446, 154], [18746, 154], [19046, 154], [19346, 154], [19646, 154], [19946, 154], [20246, 154], [20546, 154], [20846, 154], [21146, 154], [21446, 154], [21746, 154], [22046, 154], [22346, 154], [22646, 154], [22946, 154], [23246, 154], [23546, 154], [23846, 154], [24146, 154], [24446, 154], [24746, 154], [25046, 154], [25346, 154], [25646, 154], [25946, 154], [26246, 154], [26631, 69], [26932, 68], [27233, 67], [27534, 66], [27835, 65], [28136, 64], [28437, 63], [28738, 62], [29039, 61], [29339, 61], [29640, 60], [29941, 59], [30242, 58], [30543, 57], [30844, 56], [31145, 55], [31446, 54], [31747, 53], [32048, 52], [32349, 51], [32650, 50], [32950, 50], [33251, 49], [33552, 48], [33853, 47], [34154, 46], [34454, 46], [34754, 46], [35053, 47], [35353, 46], [35653, 42], [35953, 40], [36253, 39], [36552, 40], [36852, 40], [37153, 38], [37454, 37], [37755, 36], [38056, 35], [38356, 34], [38657, 33], [38958, 32], [39259, 30], [39560, 29], [39861, 28], [40161, 27], [40462, 26], [40763, 25], [41075, 13], [41376, 11], [41683, 4], [41983, 4], [42283, 3], [42583, 3]], "point": [222, 83]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-03.52|+00.00|+02.72"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 200], "mask": [[0, 39000], [39001, 299], [39302, 298], [39603, 297], [39904, 296], [40204, 296], [40505, 295], [40806, 293], [41107, 291], [41408, 289], [41709, 288], [42010, 286], [42311, 284], [42611, 283], [42912, 281], [43213, 279], [43514, 277], [43815, 275], [44116, 273], [44417, 271], [44718, 269], [45019, 268], [45319, 267], [45620, 265], [45921, 263], [46222, 261], [46523, 259], [46824, 257], [47125, 255], [47426, 253], [47726, 252], [48027, 250], [48328, 248], [48629, 247], [48930, 245], [49231, 243], [49532, 241], [49833, 239], [50133, 238], [50434, 236], [50735, 234], [51036, 232], [51337, 230], [51638, 228], [51939, 227], [52240, 225], [52540, 224], [52841, 222], [53142, 220], [53443, 218], [53744, 216], [54045, 214], [54346, 212], [54647, 210], [54948, 208], [55248, 207], [55549, 206], [55850, 204], [56151, 202], [56452, 200], [56753, 198], [57054, 196], [57355, 194], [57655, 193], [57956, 191], [58257, 189], [58558, 187], [58859, 186], [59165, 173], [59465, 173], [59825, 4], [59835, 4]], "point": [149, 99]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.29|+00.96|+00.43", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-03.52|+00.00|+02.72"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 191], [199, 288], [502, 283], [805, 279], [1106, 277], [1407, 276], [1708, 274], [2009, 273], [2309, 273], [2609, 273], [2909, 273], [3209, 273], [3509, 273], [3809, 273], [4108, 20], [4136, 246], [4408, 9], [4441, 240], [4708, 7], [4746, 235], [5008, 7], [5052, 229], [5308, 6], [5354, 227], [5607, 6], [5657, 224], [5907, 6], [5960, 83], [6047, 134], [6207, 6], [6263, 79], [6349, 132], [6507, 6], [6565, 73], [6649, 132], [6807, 6], [6867, 68], [6951, 130], [7106, 8], [7168, 64], [7253, 127], [7406, 8], [7470, 61], [7554, 126], [7706, 8], [7771, 58], [7855, 125], [8006, 9], [8072, 56], [8155, 125], [8306, 10], [8372, 55], [8456, 124], [8605, 11], [8673, 54], [8756, 124], [8905, 12], [8974, 52], [9056, 124], [9205, 12], [9274, 52], [9357, 123], [9505, 13], [9575, 51], [9657, 123], [9805, 13], [9876, 50], [9957, 123], [10104, 15], [10176, 50], [10258, 123], [10404, 16], [10476, 50], [10558, 123], [10704, 16], [10776, 50], [10858, 124], [11003, 18], [11076, 51], [11159, 124], [11302, 20], [11376, 51], [11459, 126], [11600, 22], [11676, 52], [11759, 129], [11898, 25], [11976, 52], [12060, 164], [12276, 53], [12360, 165], [12575, 54], [12660, 165], [12875, 55], [12961, 165], [13174, 56], [13261, 166], [13474, 57], [13561, 167], [13774, 57], [13862, 166], [14073, 59], [14162, 167], [14373, 59], [14463, 167], [14672, 61], [14763, 168], [14972, 61], [15063, 169], [15271, 63], [15364, 168], [15570, 263], [15870, 265], [16169, 267], [16468, 269], [16767, 271], [17066, 273], [17366, 275], [17665, 277], [17963, 281], [18262, 284], [18560, 290], [18858, 8081], [26949, 286], [27252, 280], [27553, 278], [27854, 276], [28155, 274], [28456, 272], [28756, 272], [29056, 272], [29356, 272], [29655, 274], [29954, 277], [30253, 280], [30552, 283], [30850, 289], [31148, 23692], [54841, 299], [55141, 299], [55441, 299], [55741, 299], [56041, 299], [56342, 299], [56642, 299], [56942, 299], [57242, 299], [57542, 299], [57842, 300], [58143, 296], [58443, 296], [58743, 295], [59043, 121], [59165, 173], [59343, 121], [59465, 173], [59644, 120], [59944, 120], [60244, 120], [60544, 120], [60844, 119], [61145, 118], [61445, 118], [61745, 118], [62045, 118], [62345, 117], [62645, 117], [62946, 116], [63246, 116], [63546, 116], [63846, 116], [64146, 115], [64447, 114], [64747, 114], [65047, 114], [65347, 114], [65647, 114], [65948, 112], [66248, 112], [66548, 112], [66848, 112], [67148, 112], [67448, 112], [67749, 110], [68049, 110], [68349, 110], [68649, 110], [68949, 110], [69250, 109], [69550, 108], [69850, 108], [70150, 108], [70450, 108], [70751, 107], [71051, 106], [71351, 106], [71651, 106], [71951, 106], [72251, 106], [72552, 105], [72852, 104], [73152, 104], [73452, 102], [73752, 48], [73801, 50], [74053, 47], [74104, 47], [74353, 47], [74407, 44], [74653, 47], [74710, 41], [74953, 47], [75012, 38], [75253, 47], [75315, 35], [75554, 46], [75618, 32], [75854, 46], [75921, 29], [76154, 46], [76224, 26], [76454, 46], [76526, 24], [76754, 46], [76829, 19], [77054, 46], [77355, 45], [77655, 45], [77955, 45], [78255, 45], [78555, 45], [78856, 44], [79156, 44], [79456, 44], [79756, 44], [80056, 44], [80357, 43], [80657, 43], [80957, 43], [81257, 43], [81557, 43], [81857, 43], [82158, 42], [82458, 42], [82758, 42], [83058, 42], [83358, 42], [83659, 41], [83959, 41], [84259, 41], [84559, 41], [84859, 41], [85160, 40], [85460, 40], [85760, 40], [86060, 40], [86360, 40], [86660, 40], [86961, 39], [87261, 39], [87561, 39], [87861, 39], [88161, 39], [88462, 38], [88762, 38], [89062, 38], [89362, 38], [89662, 38], [89963, 37]], "point": [149, 149]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-03.52|+00.00|+02.72"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 191], [199, 288], [502, 283], [805, 279], [1106, 277], [1407, 276], [1708, 274], [2009, 273], [2309, 273], [2609, 273], [2909, 273], [3209, 273], [3509, 273], [3809, 273], [4108, 20], [4136, 246], [4408, 9], [4441, 240], [4708, 7], [4746, 235], [5008, 7], [5052, 229], [5308, 6], [5354, 227], [5607, 6], [5657, 224], [5907, 6], [5960, 83], [6047, 134], [6207, 6], [6263, 79], [6349, 132], [6507, 6], [6565, 73], [6649, 132], [6807, 6], [6867, 68], [6951, 130], [7106, 8], [7168, 64], [7253, 127], [7406, 8], [7470, 61], [7554, 126], [7706, 8], [7771, 58], [7855, 125], [8006, 9], [8072, 56], [8155, 125], [8306, 10], [8372, 55], [8456, 124], [8605, 11], [8673, 54], [8756, 124], [8905, 12], [8974, 52], [9056, 124], [9205, 12], [9274, 52], [9357, 123], [9505, 13], [9575, 51], [9657, 123], [9805, 13], [9876, 50], [9957, 123], [10104, 15], [10176, 50], [10258, 123], [10404, 16], [10476, 50], [10558, 123], [10704, 16], [10776, 50], [10858, 124], [11003, 18], [11076, 51], [11159, 124], [11302, 20], [11376, 51], [11459, 126], [11600, 22], [11676, 52], [11759, 129], [11898, 25], [11976, 52], [12060, 164], [12276, 53], [12360, 165], [12575, 54], [12660, 165], [12875, 55], [12961, 165], [13174, 56], [13261, 166], [13474, 57], [13561, 167], [13774, 57], [13862, 166], [14073, 59], [14162, 167], [14373, 59], [14463, 167], [14672, 61], [14763, 168], [14972, 61], [15063, 169], [15271, 63], [15364, 168], [15570, 263], [15870, 265], [16169, 267], [16468, 269], [16767, 271], [17066, 273], [17366, 275], [17665, 277], [17963, 281], [18262, 284], [18560, 290], [18858, 8081], [26949, 286], [27252, 280], [27553, 202], [27759, 72], [27854, 199], [28061, 69], [28155, 197], [28362, 67], [28456, 196], [28662, 66], [28756, 195], [28963, 65], [29056, 195], [29263, 65], [29356, 195], [29563, 65], [29655, 196], [29863, 66], [29954, 197], [30163, 68], [30253, 198], [30463, 70], [30552, 199], [30763, 72], [30850, 202], [31062, 77], [31148, 204], [31361, 293], [31660, 296], [31958, 22882], [54841, 299], [55141, 299], [55441, 299], [55741, 299], [56041, 299], [56342, 299], [56642, 299], [56942, 299], [57242, 299], [57542, 299], [57842, 300], [58143, 296], [58443, 296], [58743, 295], [59043, 121], [59165, 173], [59343, 121], [59465, 173], [59644, 120], [59944, 120], [60244, 120], [60544, 120], [60844, 119], [61145, 118], [61445, 118], [61745, 118], [62045, 118], [62345, 117], [62645, 117], [62946, 116], [63246, 116], [63546, 116], [63846, 116], [64146, 115], [64447, 114], [64747, 114], [65047, 114], [65347, 114], [65647, 114], [65948, 112], [66248, 112], [66548, 112], [66848, 112], [67148, 112], [67448, 112], [67749, 110], [68049, 110], [68349, 110], [68649, 110], [68949, 110], [69250, 109], [69550, 108], [69850, 108], [70150, 108], [70450, 108], [70751, 107], [71051, 106], [71351, 106], [71651, 106], [71951, 106], [72251, 106], [72552, 105], [72852, 104], [73152, 104], [73452, 102], [73752, 48], [73801, 50], [74053, 47], [74104, 47], [74353, 47], [74407, 44], [74653, 47], [74710, 41], [74953, 47], [75012, 38], [75253, 47], [75315, 35], [75554, 46], [75618, 32], [75854, 46], [75921, 29], [76154, 46], [76224, 26], [76454, 46], [76526, 24], [76754, 46], [76829, 19], [77054, 46], [77355, 45], [77655, 45], [77955, 45], [78255, 45], [78555, 45], [78856, 44], [79156, 44], [79456, 44], [79756, 44], [80056, 44], [80357, 43], [80657, 43], [80957, 43], [81257, 43], [81557, 43], [81857, 43], [82158, 42], [82458, 42], [82758, 42], [83058, 42], [83358, 42], [83659, 41], [83959, 41], [84259, 41], [84559, 41], [84859, 41], [85160, 40], [85460, 40], [85760, 40], [86060, 40], [86360, 40], [86660, 40], [86961, 39], [87261, 39], [87561, 39], [87861, 39], [88161, 39], [88462, 38], [88762, 38], [89062, 38], [89362, 38], [89662, 38], [89963, 37]], "point": [149, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan4", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -1.75, "y": 0.9009999, "z": 2.0}, "object_poses": [{"objectName": "Pan_447fa38c", "position": {"x": -3.37359357, "y": 1.28636956, "z": 3.061901}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_4679e4a5", "position": {"x": -0.415176868, "y": 1.25903976, "z": 0.443056762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_f4537974", "position": {"x": -0.785364747, "y": 1.086315, "z": 1.9207176}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_130c30e2", "position": {"x": -1.11113727, "y": 0.931120455, "z": 0.4288}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8393cb4d", "position": {"x": -0.662086, "y": 1.04718959, "z": 3.04208541}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_1f60da4f", "position": {"x": -0.8837076, "y": 1.12544858, "z": 0.296814859}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_9dba57b8", "position": {"x": -0.785364747, "y": 1.04827678, "z": 2.14499116}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_0271e4d1", "position": {"x": -0.7158578, "y": 1.1399, "z": 0.296814859}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_7b2f7cfb", "position": {"x": -0.9396575, "y": 1.15510356, "z": 0.387548745}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_7b1a480e", "position": {"x": -1.64217317, "y": 0.9295632, "z": 0.636592}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_7b1a480e", "position": {"x": -1.4651612, "y": 0.9295632, "z": 0.221008}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ca5fe19d", "position": {"x": -1.19579625, "y": 0.0479601622, "z": 0.49469927}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ca5fe19d", "position": {"x": -3.37359381, "y": 1.55897939, "z": 2.84919739}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_58fe0a5f", "position": {"x": -1.995582, "y": 0.488586754, "z": 0.5459646}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_58fe0a5f", "position": {"x": -1.995582, "y": 0.09629749, "z": 0.613230169}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_e61894bc", "position": {"x": -0.5388072, "y": 1.04345369, "z": 2.817812}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_e61894bc", "position": {"x": -1.640668, "y": 0.0456396937, "z": 0.325359136}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_ff353859", "position": {"x": -0.5388072, "y": 1.043238, "z": 3.26635885}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_ff353859", "position": {"x": -3.29100013, "y": 0.9178577, "z": 2.37879539}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_f6bdaee7", "position": {"x": -3.59356833, "y": 0.179869235, "z": 1.97881007}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_f6bdaee7", "position": {"x": -2.51405215, "y": 1.19227993, "z": 0.284664035}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_1f60da4f", "position": {"x": -1.55366719, "y": 0.932104647, "z": 0.290272}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_58fe0a5f", "position": {"x": -0.8837076, "y": 1.12543952, "z": 0.569016457}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_ab8a6692", "position": {"x": -3.475, "y": 1.1332, "z": 0.2271}, "rotation": {"x": 0.0, "y": 90.00039, "z": 0.0}}, {"objectName": "Ladle_f4537974", "position": {"x": -0.5388072, "y": 1.08740556, "z": 1.9207176}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_4679e4a5", "position": {"x": -3.229594, "y": 0.758546, "z": 3.13280368}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Plate_ca5fe19d", "position": {"x": -0.908643544, "y": 1.04566681, "z": 2.817812}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_d5b8c410", "position": {"x": -2.63972664, "y": 1.21164966, "z": 0.473335981}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_7c6826ef", "position": {"x": -3.33989358, "y": 0.969437838, "z": 3.081294}, "rotation": {"x": 0.150243849, "y": 228.2653, "z": 312.447327}}, {"objectName": "Spatula_0271e4d1", "position": {"x": -1.37665522, "y": 0.946556032, "z": 0.567328}, "rotation": {"x": 0.0, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Pan_447fa38c", "position": {"x": -0.266, "y": 1.038, "z": 2.561}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7b2f7cfb", "position": {"x": -1.28814924, "y": 0.9617596, "z": 0.4288}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_7b1a480e", "position": {"x": -0.7158578, "y": 1.12290716, "z": 0.115347147}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_e61894bc", "position": {"x": -0.785364747, "y": 1.04345369, "z": 3.04208541}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_e6e9017d", "position": {"x": -1.19964325, "y": 0.9639934, "z": 0.290272}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Bowl_9496ee5e", "position": {"x": -0.785364747, "y": 1.04583609, "z": 2.59353828}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_4cc43937", "position": {"x": -3.742703, "y": 1.12120628, "z": 0.284664035}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8393cb4d", "position": {"x": -2.262703, "y": 1.12494218, "z": 0.6620079}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_f6bdaee7", "position": {"x": -2.01135373, "y": 1.19227993, "z": 0.567671955}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_1473e57e", "position": {"x": -3.63921952, "y": 1.60308588, "z": 2.84919667}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_130c30e2", "position": {"x": -1.19964325, "y": 0.931120455, "z": 0.567328}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_9dba57b8", "position": {"x": -2.4979, "y": 0.488305867, "z": 0.579597354}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_ff353859", "position": {"x": -3.44, "y": 0.906860232, "z": 2.92009878}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}], "object_toggles": [], "random_seed": 315781679, "scene_num": 4}, "task_id": "trial_T20190908_220828_121076", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1RLO9LNUJIW5S_3IX2EGZR7EAS6SZV0YOPMEXAGUWJRM", "high_descs": ["Turn around and walk to the sink then take a step to your left.", "Pick up the egg that's in front of you to the left of the spatula.", "Turn left and position yourself so you can use the microwave.", "Cook the egg in microwave then take it back out and close the door.", "Turn left and walk to the wall then turn left and walk to the fridge.", "Open the fridge and put the egg in there before closing  the door."], "task_desc": "Put a microwaved egg in the fridge.", "votes": [1, 1, 1, 1, 1]}, {"assignment_id": "A1MKCTVKE7J0ZP_3PW9OPU9PTB47R26VJ3IH6TGAL812D", "high_descs": ["Turn right and go to the table, then turn right to go to the sink.", "Pick up the egg from inside of the sink.", "Turn toward the microwave at your left.", "Put the egg inside of the microwave, heat it up, then take it out.", "Turn left, go to the wall, then turn left to go to the fridge.", "Open the fridge, put the egg inside, and close the fridge."], "task_desc": "Put a warm egg inside of the fridge.", "votes": [1, 1, 1, 1, 1]}, {"assignment_id": "A1ELPYAFO7MANS_3AAPLD8UCF89V1HXGNQZAGIDRVOHT7", "high_descs": ["Turn around and walk to the sink.", "Pick up the egg in the sink.", "Look left.", "Heat the egg in the microwave.", "Turn around and veer right to the fridge.", "Place the egg in the fridge to the left of the potato, near the opposite side of the shelf."], "task_desc": "Place a heated egg in a fridge.", "votes": [1, 1]}]}}