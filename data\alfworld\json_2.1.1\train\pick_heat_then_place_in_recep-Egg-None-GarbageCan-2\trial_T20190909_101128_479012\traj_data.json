{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000297.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000298.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000299.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000300.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000301.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000302.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000303.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000304.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000305.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000306.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000307.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000308.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000309.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000310.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000311.png", "low_idx": 61}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "GarbageCan", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-3|4|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-0.85454172, -0.85454172, 3.8808032, 3.8808032, 3.929890156, 3.929890156]], "coordinateReceptacleObjectId": ["CounterTop", [-0.06, -0.06, 2.024, 2.024, 3.8444, 3.8444]], "forceVisible": true, "objectId": "Egg|-00.21|+00.98|+00.97"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|-3|1|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [7.732, 7.732, -3.068, -3.068, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.93|+00.90|-00.77"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-5|5|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "garbagecan"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-0.85454172, -0.85454172, 3.8808032, 3.8808032, 3.929890156, 3.929890156]], "coordinateReceptacleObjectId": ["GarbageCan", [-7.184, -7.184, 5.32, 5.32, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|-00.21|+00.98|+00.97", "receptacleObjectId": "GarbageCan|-01.80|+00.00|+01.33"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.21|+00.98|+00.97"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [146, 150, 172, 181], "mask": [[44860, 1], [45155, 9], [45453, 13], [45752, 15], [46051, 17], [46350, 19], [46649, 21], [46949, 22], [47248, 23], [47547, 25], [47847, 25], [48147, 26], [48446, 27], [48746, 27], [49046, 27], [49346, 27], [49646, 27], [49946, 27], [50246, 27], [50546, 27], [50846, 27], [51146, 27], [51446, 27], [51747, 25], [52047, 25], [52348, 23], [52648, 23], [52949, 21], [53250, 19], [53551, 17], [53853, 13], [54155, 9]], "point": [159, 164]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [55, 9, 255, 124], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11160, 190], [11460, 191], [11758, 195], [12057, 197], [12356, 199], [12656, 199], [12955, 201], [13255, 201], [13555, 201], [13855, 201], [14155, 201], [14455, 200], [14756, 199], [15056, 199], [15356, 199], [15656, 198], [15957, 197], [16257, 197], [16557, 196], [16858, 195], [17158, 195], [17458, 195], [17758, 194], [18059, 193], [18359, 193], [18659, 192], [18959, 192], [19260, 191], [19560, 190], [19860, 190], [20160, 190], [20461, 189], [20761, 188], [21061, 188], [21361, 188], [21662, 186], [21962, 186], [22262, 186], [22563, 185], [22863, 184], [23163, 184], [23463, 184], [23764, 182], [24064, 182], [24364, 182], [24664, 181], [24965, 180], [25265, 180], [25565, 180], [25865, 179], [26166, 178], [26466, 178], [26766, 177], [27066, 177], [27367, 176], [27667, 176], [27967, 175], [28267, 175], [28568, 174], [28868, 173], [29168, 173], [29469, 172], [29769, 172], [30069, 171], [30369, 171], [30670, 170], [30970, 169], [31270, 169], [31570, 169], [31871, 167], [32171, 167], [32471, 167], [32771, 167], [33072, 165], [33372, 165], [33672, 165], [33972, 164], [34273, 163], [34573, 163], [34873, 163], [35174, 161], [35474, 161], [35774, 161], [36074, 160], [36375, 159], [36675, 159], [36976, 156]], "point": [155, 65]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.21|+00.98|+00.97", "placeStationary": true, "receptacleObjectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 9, 255, 228], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11149, 201], [11448, 203], [11747, 206], [12046, 208], [12345, 210], [12645, 210], [12944, 212], [13243, 213], [13543, 213], [13842, 214], [14141, 215], [14441, 214], [14740, 215], [15039, 216], [15339, 216], [15638, 216], [15937, 217], [16237, 217], [16536, 217], [16835, 218], [17134, 219], [17434, 219], [17733, 219], [18032, 220], [18332, 220], [18631, 220], [18930, 221], [19230, 221], [19529, 221], [19828, 222], [20128, 222], [20427, 223], [20726, 223], [21025, 224], [21325, 224], [21624, 224], [21923, 225], [22223, 225], [22522, 226], [22821, 226], [23121, 226], [23420, 227], [23719, 227], [24019, 227], [24318, 228], [24617, 228], [24916, 229], [25216, 229], [25515, 230], [25814, 230], [26114, 230], [26413, 231], [26712, 231], [27012, 231], [27311, 232], [27610, 233], [27910, 232], [28209, 233], [28508, 234], [28807, 234], [29107, 234], [29406, 235], [29705, 236], [30005, 235], [30304, 236], [30603, 237], [30903, 236], [31202, 237], [31501, 238], [31801, 237], [32100, 238], [32400, 238], [32700, 238], [33000, 237], [33300, 237], [33600, 237], [33900, 236], [34200, 236], [34500, 236], [34800, 236], [35100, 235], [35400, 235], [35700, 79], [35902, 33], [36000, 79], [36202, 32], [36300, 78], [36502, 32], [36600, 78], [36802, 32], [36900, 78], [37103, 29], [37200, 77], [37500, 77], [37800, 77], [38100, 77], [38400, 76], [38700, 76], [39000, 76], [39300, 75], [39600, 75], [39900, 75], [40200, 74], [40500, 74], [40800, 74], [41100, 73], [41400, 73], [41700, 73], [42000, 73], [42300, 72], [42600, 72], [42900, 72], [43200, 71], [43500, 71], [43800, 71], [44100, 70], [44400, 70], [44700, 70], [45000, 69], [45300, 69], [45600, 69], [45900, 68], [46200, 68], [46500, 68], [46800, 68], [47100, 67], [47400, 67], [47700, 67], [48000, 66], [48300, 66], [48600, 66], [48900, 65], [49200, 65], [49500, 65], [49800, 64], [50100, 64], [50400, 64], [50700, 64], [51000, 63], [51300, 63], [51600, 63], [51900, 62], [52200, 62], [52500, 62], [52800, 61], [53100, 61], [53400, 61], [53700, 60], [54000, 60], [54301, 59], [54601, 59], [54902, 57], [55203, 56], [55503, 56], [55804, 54], [56105, 53], [56405, 53], [56706, 51], [57007, 50], [57307, 50], [57608, 48], [57909, 47], [58209, 47], [58510, 45], [58811, 44], [59111, 44], [59412, 43], [59713, 41], [60013, 41], [60314, 40], [60615, 38], [60915, 38], [61216, 37], [61517, 35], [61817, 35], [62118, 34], [62418, 33], [62719, 32], [63020, 31], [63320, 31], [63621, 29], [63922, 28], [64222, 28], [64522, 27], [64821, 28], [65122, 27], [65423, 25], [65723, 26], [66024, 27], [66325, 26], [66625, 27], [66926, 21], [66948, 3], [67227, 20], [67527, 19], [67828, 18], [68129, 17]], "point": [127, 117]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 9, 255, 228], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11149, 201], [11448, 203], [11747, 206], [12046, 208], [12345, 210], [12645, 210], [12944, 212], [13243, 213], [13543, 213], [13842, 214], [14141, 215], [14441, 214], [14740, 215], [15039, 216], [15339, 216], [15638, 216], [15937, 217], [16237, 217], [16536, 217], [16835, 218], [17134, 219], [17434, 219], [17733, 219], [18032, 220], [18332, 220], [18631, 220], [18930, 221], [19230, 221], [19529, 120], [19655, 95], [19828, 119], [19957, 93], [20128, 117], [20258, 92], [20427, 118], [20559, 91], [20726, 118], [20860, 89], [21025, 118], [21160, 89], [21325, 118], [21461, 88], [21624, 118], [21761, 87], [21923, 119], [22062, 86], [22223, 118], [22362, 86], [22522, 119], [22662, 86], [22821, 120], [22963, 84], [23121, 120], [23263, 84], [23420, 121], [23563, 84], [23719, 122], [23863, 83], [24019, 121], [24163, 83], [24318, 122], [24463, 83], [24617, 124], [24763, 82], [24916, 125], [25063, 82], [25216, 125], [25363, 82], [25515, 126], [25662, 83], [25814, 128], [25962, 82], [26114, 128], [26261, 83], [26413, 130], [26561, 83], [26712, 131], [26860, 83], [27012, 132], [27159, 84], [27311, 134], [27458, 85], [27610, 137], [27757, 86], [27910, 139], [28054, 88], [28209, 233], [28508, 234], [28807, 234], [29107, 234], [29406, 235], [29705, 236], [30005, 235], [30304, 236], [30603, 237], [30903, 236], [31202, 237], [31501, 238], [31801, 237], [32100, 238], [32400, 238], [32700, 238], [33000, 237], [33300, 237], [33600, 237], [33900, 236], [34200, 236], [34500, 236], [34800, 236], [35100, 235], [35400, 235], [35700, 79], [35902, 33], [36000, 79], [36202, 32], [36300, 78], [36502, 32], [36600, 78], [36802, 32], [36900, 78], [37103, 29], [37200, 77], [37500, 77], [37800, 77], [38100, 77], [38400, 76], [38700, 76], [39000, 76], [39300, 75], [39600, 75], [39900, 75], [40200, 74], [40500, 74], [40800, 74], [41100, 73], [41400, 73], [41700, 73], [42000, 73], [42300, 72], [42600, 72], [42900, 72], [43200, 71], [43500, 71], [43800, 71], [44100, 70], [44400, 70], [44700, 70], [45000, 69], [45300, 69], [45600, 69], [45900, 68], [46200, 68], [46500, 68], [46800, 68], [47100, 67], [47400, 67], [47700, 67], [48000, 66], [48300, 66], [48600, 66], [48900, 65], [49200, 65], [49500, 65], [49800, 64], [50100, 64], [50400, 64], [50700, 64], [51000, 63], [51300, 63], [51600, 63], [51900, 62], [52200, 62], [52500, 62], [52800, 61], [53100, 61], [53400, 61], [53700, 60], [54000, 60], [54301, 59], [54601, 59], [54902, 57], [55203, 56], [55503, 56], [55804, 54], [56105, 53], [56405, 53], [56706, 51], [57007, 50], [57307, 50], [57608, 48], [57909, 47], [58209, 47], [58510, 45], [58811, 44], [59111, 44], [59412, 43], [59713, 41], [60013, 41], [60314, 40], [60615, 38], [60915, 38], [61216, 37], [61517, 35], [61817, 35], [62118, 34], [62418, 33], [62719, 32], [63020, 31], [63320, 31], [63621, 29], [63922, 28], [64222, 28], [64522, 27], [64821, 28], [65122, 27], [65423, 25], [65723, 26], [66024, 27], [66325, 26], [66625, 27], [66926, 21], [66948, 3], [67227, 20], [67527, 19], [67828, 18], [68129, 17]], "point": [127, 117]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [7.732, 7.732, -3.068, -3.068, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [55, 9, 255, 124], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11160, 190], [11460, 191], [11758, 195], [12057, 197], [12356, 199], [12656, 199], [12955, 201], [13255, 201], [13555, 201], [13855, 201], [14155, 201], [14455, 200], [14756, 199], [15056, 199], [15356, 199], [15656, 198], [15957, 197], [16257, 197], [16557, 196], [16858, 195], [17158, 195], [17458, 195], [17758, 194], [18059, 193], [18359, 193], [18659, 192], [18959, 192], [19260, 191], [19560, 190], [19860, 190], [20160, 190], [20461, 189], [20761, 188], [21061, 188], [21361, 188], [21662, 186], [21962, 186], [22262, 186], [22563, 185], [22863, 184], [23163, 184], [23463, 184], [23764, 182], [24064, 182], [24364, 182], [24664, 181], [24965, 180], [25265, 180], [25565, 180], [25865, 179], [26166, 178], [26466, 178], [26766, 177], [27066, 177], [27367, 176], [27667, 176], [27967, 175], [28267, 175], [28568, 174], [28868, 173], [29168, 173], [29469, 172], [29769, 172], [30069, 171], [30369, 171], [30670, 170], [30970, 169], [31270, 169], [31570, 169], [31871, 167], [32171, 167], [32471, 167], [32771, 167], [33072, 165], [33372, 165], [33672, 165], [33972, 164], [34273, 163], [34573, 163], [34873, 163], [35174, 161], [35474, 161], [35774, 161], [36074, 160], [36375, 159], [36675, 159], [36976, 156]], "point": [155, 65]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [7.732, 7.732, -3.068, -3.068, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [55, 9, 255, 124], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11160, 190], [11460, 191], [11758, 195], [12057, 197], [12356, 199], [12656, 199], [12955, 201], [13255, 201], [13555, 201], [13855, 201], [14155, 201], [14455, 200], [14756, 199], [15056, 199], [15356, 199], [15656, 198], [15957, 197], [16257, 197], [16557, 196], [16858, 195], [17158, 195], [17458, 195], [17758, 194], [18059, 193], [18359, 193], [18659, 192], [18959, 192], [19260, 191], [19560, 190], [19860, 190], [20160, 190], [20461, 189], [20761, 188], [21061, 188], [21361, 188], [21662, 186], [21962, 186], [22262, 186], [22563, 185], [22863, 184], [23163, 184], [23463, 184], [23764, 182], [24064, 182], [24364, 182], [24664, 181], [24965, 180], [25265, 180], [25565, 180], [25865, 179], [26166, 178], [26466, 178], [26766, 177], [27066, 177], [27367, 176], [27667, 176], [27967, 175], [28267, 175], [28568, 174], [28868, 173], [29168, 173], [29469, 172], [29769, 172], [30069, 171], [30369, 171], [30670, 170], [30970, 169], [31270, 169], [31570, 169], [31871, 167], [32171, 167], [32471, 167], [32771, 167], [33072, 165], [33372, 165], [33672, 165], [33972, 164], [34273, 163], [34573, 163], [34873, 163], [35174, 161], [35474, 161], [35774, 161], [36074, 160], [36375, 159], [36675, 159], [36976, 156]], "point": [155, 65]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [55, 9, 255, 124], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11160, 190], [11460, 191], [11758, 195], [12057, 197], [12356, 199], [12656, 199], [12955, 201], [13255, 201], [13555, 201], [13855, 201], [14155, 201], [14455, 200], [14756, 199], [15056, 199], [15356, 199], [15656, 198], [15957, 197], [16257, 197], [16557, 196], [16858, 195], [17158, 195], [17458, 195], [17758, 194], [18059, 193], [18359, 193], [18659, 192], [18959, 192], [19260, 191], [19560, 190], [19860, 190], [20160, 190], [20461, 189], [20761, 188], [21061, 188], [21361, 188], [21662, 186], [21962, 186], [22262, 186], [22563, 185], [22863, 184], [23163, 184], [23463, 184], [23764, 182], [24064, 182], [24364, 182], [24664, 181], [24965, 180], [25265, 180], [25565, 180], [25865, 179], [26166, 178], [26466, 178], [26766, 177], [27066, 177], [27367, 176], [27667, 176], [27967, 175], [28267, 175], [28568, 174], [28868, 173], [29168, 173], [29469, 172], [29769, 172], [30069, 171], [30369, 171], [30670, 170], [30970, 169], [31270, 169], [31570, 169], [31871, 167], [32171, 167], [32471, 167], [32771, 167], [33072, 165], [33372, 165], [33672, 165], [33972, 164], [34273, 163], [34573, 163], [34873, 163], [35174, 161], [35474, 161], [35774, 161], [36074, 160], [36375, 159], [36675, 159], [36976, 156]], "point": [155, 65]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.21|+00.98|+00.97"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [140, 66, 162, 94], "mask": [[19649, 6], [19947, 10], [20245, 13], [20545, 14], [20844, 16], [21143, 17], [21443, 18], [21742, 19], [22042, 20], [22341, 21], [22641, 21], [22941, 22], [23241, 22], [23541, 22], [23841, 22], [24140, 23], [24440, 23], [24741, 22], [25041, 22], [25341, 22], [25641, 21], [25942, 20], [26242, 19], [26543, 18], [26843, 17], [27144, 15], [27445, 13], [27747, 10], [28049, 5]], "point": [151, 79]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 9, 255, 228], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11149, 201], [11448, 203], [11747, 206], [12046, 208], [12345, 210], [12645, 210], [12944, 212], [13243, 213], [13543, 213], [13842, 214], [14141, 215], [14441, 214], [14740, 215], [15039, 216], [15339, 216], [15638, 216], [15937, 217], [16237, 217], [16536, 217], [16835, 218], [17134, 219], [17434, 219], [17733, 219], [18032, 220], [18332, 220], [18631, 220], [18930, 221], [19230, 221], [19529, 221], [19828, 222], [20128, 222], [20427, 223], [20726, 223], [21025, 224], [21325, 224], [21624, 224], [21923, 225], [22223, 225], [22522, 226], [22821, 226], [23121, 226], [23420, 227], [23719, 227], [24019, 227], [24318, 228], [24617, 228], [24916, 229], [25216, 229], [25515, 230], [25814, 230], [26114, 230], [26413, 231], [26712, 231], [27012, 231], [27311, 232], [27610, 233], [27910, 232], [28209, 233], [28508, 234], [28807, 234], [29107, 234], [29406, 235], [29705, 236], [30005, 235], [30304, 236], [30603, 237], [30903, 236], [31202, 237], [31501, 238], [31801, 237], [32100, 238], [32400, 238], [32700, 238], [33000, 237], [33300, 237], [33600, 237], [33900, 236], [34200, 236], [34500, 236], [34800, 236], [35100, 235], [35400, 235], [35700, 79], [35902, 33], [36000, 79], [36202, 32], [36300, 78], [36502, 32], [36600, 78], [36802, 32], [36900, 78], [37103, 29], [37200, 77], [37500, 77], [37800, 77], [38100, 77], [38400, 76], [38700, 76], [39000, 76], [39300, 75], [39600, 75], [39900, 75], [40200, 74], [40500, 74], [40800, 74], [41100, 73], [41400, 73], [41700, 73], [42000, 73], [42300, 72], [42600, 72], [42900, 72], [43200, 71], [43500, 71], [43800, 71], [44100, 70], [44400, 70], [44700, 70], [45000, 69], [45300, 69], [45600, 69], [45900, 68], [46200, 68], [46500, 68], [46800, 68], [47100, 67], [47400, 67], [47700, 67], [48000, 66], [48300, 66], [48600, 66], [48900, 65], [49200, 65], [49500, 65], [49800, 64], [50100, 64], [50400, 64], [50700, 64], [51000, 63], [51300, 63], [51600, 63], [51900, 62], [52200, 62], [52500, 62], [52800, 61], [53100, 61], [53400, 61], [53700, 60], [54000, 60], [54301, 59], [54601, 59], [54902, 57], [55203, 56], [55503, 56], [55804, 54], [56105, 53], [56405, 53], [56706, 51], [57007, 50], [57307, 50], [57608, 48], [57909, 47], [58209, 47], [58510, 45], [58811, 44], [59111, 44], [59412, 43], [59713, 41], [60013, 41], [60314, 40], [60615, 38], [60915, 38], [61216, 37], [61517, 35], [61817, 35], [62118, 34], [62418, 33], [62719, 32], [63020, 31], [63320, 31], [63621, 29], [63922, 28], [64222, 28], [64522, 27], [64821, 28], [65122, 27], [65423, 25], [65723, 26], [66024, 27], [66325, 26], [66625, 27], [66926, 21], [66948, 3], [67227, 20], [67527, 19], [67828, 18], [68129, 17]], "point": [127, 117]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.21|+00.98|+00.97", "placeStationary": true, "receptacleObjectId": "GarbageCan|-01.80|+00.00|+01.33"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [96, 129, 239, 217], "mask": [[38516, 100], [38814, 104], [39112, 108], [39410, 112], [39708, 116], [40007, 118], [40306, 120], [40606, 120], [40905, 122], [41204, 124], [41503, 126], [41803, 127], [42102, 128], [42402, 128], [42702, 129], [43002, 129], [43301, 130], [43601, 131], [43901, 131], [44201, 131], [44501, 131], [44801, 131], [45100, 133], [45400, 133], [45700, 133], [46000, 133], [46300, 133], [46600, 134], [46900, 134], [47200, 134], [47500, 134], [47799, 135], [48099, 136], [48399, 136], [48699, 136], [48999, 136], [49299, 136], [49599, 136], [49899, 137], [50198, 138], [50498, 138], [50798, 138], [51098, 138], [51398, 139], [51698, 139], [51998, 139], [52298, 139], [52597, 140], [52897, 141], [53197, 141], [53497, 141], [53797, 141], [54097, 141], [54397, 142], [54697, 142], [54997, 142], [55296, 143], [55596, 143], [55896, 144], [56196, 144], [56496, 65], [56565, 75], [56796, 64], [56866, 74], [57096, 63], [57167, 73], [57396, 63], [57467, 73], [57696, 63], [57768, 72], [57996, 62], [58068, 72], [58296, 62], [58369, 71], [58597, 60], [58669, 71], [58897, 142], [59198, 141], [59499, 139], [59799, 138], [60100, 137], [60400, 136], [60701, 135], [61002, 133], [61303, 131], [61605, 127], [61907, 123], [62208, 121], [62510, 117], [62812, 113], [63116, 105], [63429, 72], [63732, 66], [64034, 61], [64336, 56], [64639, 49], [64943, 41]], "point": [167, 172]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan2", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -0.25, "y": 0.9009992, "z": 2.75}, "object_poses": [{"objectName": "CellPhone_6cb12d41", "position": {"x": 0.750144839, "y": 0.6694276, "z": -1.3015}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_ec6bb035", "position": {"x": 1.986738, "y": 0.9558624, "z": 1.10763264}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_ec6bb035", "position": {"x": 1.65876126, "y": 0.6269737, "z": -0.0927972943}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}, {"objectName": "Kettle_233c7df0", "position": {"x": -0.21363543, "y": 0.9266413, "z": 0.506000042}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_233c7df0", "position": {"x": 2.02593255, "y": 0.9125413, "z": -0.131973028}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_4f8c70c0", "position": {"x": -0.21363543, "y": 0.9256421, "z": -0.190301061}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_4f8c70c0", "position": {"x": 1.6587615, "y": 0.121546052, "z": 0.454233527}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_de267f63", "position": {"x": 1.86236525, "y": 0.240419865, "z": 1.35055}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pot_1bc23cfd", "position": {"x": 1.9753, "y": 0.938299954, "z": 0.8192}, "rotation": {"x": 0.0, "y": 180.000214, "z": 0.0}}, {"objectName": "Egg_7f5e18ec", "position": {"x": -0.21363543, "y": 0.982472539, "z": 0.9702008}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7f5e18ec", "position": {"x": -1.78940713, "y": 1.08623946, "z": -0.175190687}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_7f5e18ec", "position": {"x": 0.321481615, "y": 0.982472539, "z": 0.273899674}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d852c2bd", "position": {"x": 0.000411391258, "y": 0.92430824, "z": 1.20230114}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d852c2bd", "position": {"x": 0.19417879, "y": 0.111495435, "z": -1.34579122}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_e0496240", "position": {"x": 1.76426947, "y": 0.492066115, "z": 0.0477723442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_448791c6", "position": {"x": 0.170128584, "y": 0.815738261, "z": -1.49077988}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_448791c6", "position": {"x": -1.74964678, "y": 0.8944802, "z": 0.2956789}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_586649ce", "position": {"x": 2.073358, "y": 0.907806158, "z": 1.33350134}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_48e00ad2", "position": {"x": -1.75051856, "y": 1.75657606, "z": -0.175191209}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_48e00ad2", "position": {"x": -1.80318046, "y": 1.54979384, "z": 0.0131571312}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_b045e42b", "position": {"x": -1.59253287, "y": 1.46421421, "z": -0.175190747}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_487a7950", "position": {"x": -1.72138691, "y": 0.6164668, "z": 0.20150508}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_7ce82db0", "position": {"x": 1.924396, "y": 1.66291428, "z": -1.2800504}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_4e364eea", "position": {"x": -1.64519465, "y": 1.46734476, "z": 0.295678854}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_1bc23cfd", "position": {"x": 0.214458212, "y": 0.929685533, "z": -0.190301061}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_c80d57de", "position": {"x": 1.713, "y": 0.1174392, "z": 0.116144642}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Ladle_ec6bb035", "position": {"x": 1.90011775, "y": 0.9558624, "z": 1.40879107}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_233c7df0", "position": {"x": -1.15179658, "y": 0.1145072, "z": -1.29661942}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7f5e18ec", "position": {"x": 0.214458212, "y": 0.982472539, "z": 0.506000042}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d852c2bd", "position": {"x": -0.106612019, "y": 0.92430824, "z": 0.0417993069}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_6d10ab8d", "position": {"x": 1.765, "y": 0.938299954, "z": 0.3846}, "rotation": {"x": 0.0, "y": 180.000214, "z": 0.0}}, {"objectName": "Lettuce_aa2e533f", "position": {"x": 0.1074348, "y": 1.00230384, "z": 0.9702008}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_de267f63", "position": {"x": 0.1074348, "y": 0.940599859, "z": 1.20230114}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Apple_487a7950", "position": {"x": -0.0889133438, "y": 0.8137205, "z": -1.4355}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_e0496240", "position": {"x": -0.106612019, "y": 0.9475927, "z": 0.7381004}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_586649ce", "position": {"x": -0.106612019, "y": 0.9219062, "z": 0.273899674}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_14add4df", "position": {"x": 1.34167874, "y": 1.67594481, "z": -1.65244007}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_6cb12d41", "position": {"x": 1.66173053, "y": 0.2243176, "z": 0.5310277}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_4f8c70c0", "position": {"x": 1.66173053, "y": 0.11693275, "z": -0.08897236}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_48e00ad2", "position": {"x": -1.25613821, "y": 0.9920796, "z": -1.70706248}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_448791c6", "position": {"x": -1.721387, "y": 0.61835146, "z": -0.2693641}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_5e728867", "position": {"x": -1.796, "y": 0.06506693, "z": 1.32999992}, "rotation": {"x": 0.0, "y": 269.999939, "z": 0.0}}, {"objectName": "DishSponge_e1c57c26", "position": {"x": -0.1752606, "y": 0.7602068, "z": -1.32494032}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_bfeb74cb", "position": {"x": -1.46217752, "y": 0.912629366, "z": -1.61003125}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_b045e42b", "position": {"x": 1.3554858, "y": 0.9554, "z": -1.36239815}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_253fa632", "position": {"x": 0.321481615, "y": 0.9240406, "z": 0.7381004}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3541604522, "scene_num": 2}, "task_id": "trial_T20190909_101128_479012", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A28Q5S6WK94WUN_33FOTY3KEPCH59U8SCSOTJ22MOJC1J", "high_descs": ["Go to the kitchen island ", "pick up the egg sitting on the island", "take the egg to the microwave", "put the egg in and microwave it and then remove it", "take the egg and go to the trash can", "throw the egg in the trash "], "task_desc": "Heat up the egg and throw it away", "votes": [1, 1]}, {"assignment_id": "A20FCMWP43CVIU_3KMS4QQVK5HQEM04ROO764J4S7CKF6", "high_descs": ["walk to face table", "pick up egg from table", "walk to face microwave", "heat and remove egg from microwave", "walk to grey trash bin", "put egg inside trash bin"], "task_desc": "put heated egg inside trash bin", "votes": [1, 1]}, {"assignment_id": "AM2KK02JXXW48_3P4RDNWND8XFDTWCABUG8MWJTA0JIT", "high_descs": ["Turn right, move to the right side of the center island, turn left to face the egg on the island.", "Pick up the egg on the left side of the island. ", "Turn right, bring the egg around the island to the left, to the microwave on the counter.", "Heat the egg in the microwave. ", "Take the heated egg back around the center island, to the trash can on the right side of the fridge.", "Put the heated egg in the trash can. "], "task_desc": "Put a heated egg in the trash can. ", "votes": [1, 1]}]}}