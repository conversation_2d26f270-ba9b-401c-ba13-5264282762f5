{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 28}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 28}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 29}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 29}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 30}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 30}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 31}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 31}, {"high_idx": 0, "image_name": "000000091.png", "low_idx": 31}, {"high_idx": 0, "image_name": "000000092.png", "low_idx": 31}, {"high_idx": 0, "image_name": "000000093.png", "low_idx": 31}, {"high_idx": 0, "image_name": "000000094.png", "low_idx": 31}, {"high_idx": 0, "image_name": "000000095.png", "low_idx": 31}, {"high_idx": 0, "image_name": "000000096.png", "low_idx": 31}, {"high_idx": 0, "image_name": "000000097.png", "low_idx": 31}, {"high_idx": 0, "image_name": "000000098.png", "low_idx": 31}, {"high_idx": 0, "image_name": "000000099.png", "low_idx": 31}, {"high_idx": 0, "image_name": "000000100.png", "low_idx": 32}, {"high_idx": 0, "image_name": "000000101.png", "low_idx": 32}, {"high_idx": 0, "image_name": "000000102.png", "low_idx": 33}, {"high_idx": 0, "image_name": "000000103.png", "low_idx": 33}, {"high_idx": 0, "image_name": "000000104.png", "low_idx": 34}, {"high_idx": 0, "image_name": "000000105.png", "low_idx": 34}, {"high_idx": 0, "image_name": "000000106.png", "low_idx": 35}, {"high_idx": 0, "image_name": "000000107.png", "low_idx": 35}, {"high_idx": 0, "image_name": "000000108.png", "low_idx": 36}, {"high_idx": 0, "image_name": "000000109.png", "low_idx": 36}, {"high_idx": 0, "image_name": "000000110.png", "low_idx": 37}, {"high_idx": 0, "image_name": "000000111.png", "low_idx": 37}, {"high_idx": 0, "image_name": "000000112.png", "low_idx": 38}, {"high_idx": 0, "image_name": "000000113.png", "low_idx": 38}, {"high_idx": 0, "image_name": "000000114.png", "low_idx": 39}, {"high_idx": 0, "image_name": "000000115.png", "low_idx": 39}, {"high_idx": 0, "image_name": "000000116.png", "low_idx": 39}, {"high_idx": 0, "image_name": "000000117.png", "low_idx": 39}, {"high_idx": 0, "image_name": "000000118.png", "low_idx": 39}, {"high_idx": 0, "image_name": "000000119.png", "low_idx": 39}, {"high_idx": 0, "image_name": "000000120.png", "low_idx": 39}, {"high_idx": 0, "image_name": "000000121.png", "low_idx": 39}, {"high_idx": 0, "image_name": "000000122.png", "low_idx": 39}, {"high_idx": 0, "image_name": "000000123.png", "low_idx": 39}, {"high_idx": 0, "image_name": "000000124.png", "low_idx": 39}, {"high_idx": 1, "image_name": "000000125.png", "low_idx": 40}, {"high_idx": 1, "image_name": "000000126.png", "low_idx": 40}, {"high_idx": 1, "image_name": "000000127.png", "low_idx": 40}, {"high_idx": 1, "image_name": "000000128.png", "low_idx": 40}, {"high_idx": 1, "image_name": "000000129.png", "low_idx": 41}, {"high_idx": 1, "image_name": "000000130.png", "low_idx": 41}, {"high_idx": 1, "image_name": "000000131.png", "low_idx": 41}, {"high_idx": 1, "image_name": "000000132.png", "low_idx": 41}, {"high_idx": 1, "image_name": "000000133.png", "low_idx": 41}, {"high_idx": 1, "image_name": "000000134.png", "low_idx": 41}, {"high_idx": 1, "image_name": "000000135.png", "low_idx": 41}, {"high_idx": 1, "image_name": "000000136.png", "low_idx": 41}, {"high_idx": 1, "image_name": "000000137.png", "low_idx": 41}, {"high_idx": 1, "image_name": "000000138.png", "low_idx": 41}, {"high_idx": 1, "image_name": "000000139.png", "low_idx": 41}, {"high_idx": 1, "image_name": "000000140.png", "low_idx": 41}, {"high_idx": 1, "image_name": "000000141.png", "low_idx": 41}, {"high_idx": 1, "image_name": "000000142.png", "low_idx": 41}, {"high_idx": 1, "image_name": "000000143.png", "low_idx": 41}, {"high_idx": 1, "image_name": "000000144.png", "low_idx": 42}, {"high_idx": 1, "image_name": "000000145.png", "low_idx": 42}, {"high_idx": 1, "image_name": "000000146.png", "low_idx": 42}, {"high_idx": 1, "image_name": "000000147.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000186.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000187.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000188.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000189.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000190.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000191.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000192.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000193.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000194.png", "low_idx": 48}, {"high_idx": 2, "image_name": "000000195.png", "low_idx": 48}, {"high_idx": 2, "image_name": "000000196.png", "low_idx": 48}, {"high_idx": 2, "image_name": "000000197.png", "low_idx": 48}, {"high_idx": 2, "image_name": "000000198.png", "low_idx": 48}, {"high_idx": 2, "image_name": "000000199.png", "low_idx": 48}, {"high_idx": 2, "image_name": "000000200.png", "low_idx": 48}, {"high_idx": 2, "image_name": "000000201.png", "low_idx": 48}, {"high_idx": 2, "image_name": "000000202.png", "low_idx": 48}, {"high_idx": 2, "image_name": "000000203.png", "low_idx": 48}, {"high_idx": 2, "image_name": "000000204.png", "low_idx": 48}, {"high_idx": 2, "image_name": "000000205.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000206.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000207.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000208.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000209.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000210.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000211.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000212.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000213.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000214.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000215.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000245.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000246.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000247.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000248.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000249.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000250.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000251.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000252.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000253.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000254.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000255.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000256.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000257.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000258.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000259.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000260.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000261.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000262.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000263.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000264.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000265.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000266.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000267.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000268.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000269.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000270.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000271.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000272.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000273.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000274.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000275.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000276.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000277.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000278.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000279.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000280.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000281.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000282.png", "low_idx": 57}, {"high_idx": 3, "image_name": "000000283.png", "low_idx": 57}, {"high_idx": 3, "image_name": "000000284.png", "low_idx": 57}, {"high_idx": 3, "image_name": "000000285.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000320.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000321.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000322.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000323.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000324.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000325.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000326.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000327.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000328.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000329.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000330.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000331.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000332.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000333.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000334.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000335.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000336.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000337.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000338.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000339.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000340.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000341.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000342.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000343.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000344.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000345.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000346.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000347.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000348.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000349.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000350.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000351.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000352.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000353.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000354.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000355.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000356.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000357.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000358.png", "low_idx": 67}, {"high_idx": 4, "image_name": "000000359.png", "low_idx": 67}, {"high_idx": 4, "image_name": "000000360.png", "low_idx": 68}, {"high_idx": 4, "image_name": "000000361.png", "low_idx": 68}, {"high_idx": 4, "image_name": "000000362.png", "low_idx": 69}, {"high_idx": 4, "image_name": "000000363.png", "low_idx": 69}, {"high_idx": 4, "image_name": "000000364.png", "low_idx": 70}, {"high_idx": 4, "image_name": "000000365.png", "low_idx": 70}, {"high_idx": 4, "image_name": "000000366.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000367.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000368.png", "low_idx": 72}, {"high_idx": 4, "image_name": "000000369.png", "low_idx": 72}, {"high_idx": 4, "image_name": "000000370.png", "low_idx": 72}, {"high_idx": 4, "image_name": "000000371.png", "low_idx": 72}, {"high_idx": 4, "image_name": "000000372.png", "low_idx": 72}, {"high_idx": 4, "image_name": "000000373.png", "low_idx": 72}, {"high_idx": 4, "image_name": "000000374.png", "low_idx": 72}, {"high_idx": 4, "image_name": "000000375.png", "low_idx": 72}, {"high_idx": 4, "image_name": "000000376.png", "low_idx": 72}, {"high_idx": 4, "image_name": "000000377.png", "low_idx": 72}, {"high_idx": 4, "image_name": "000000378.png", "low_idx": 72}, {"high_idx": 4, "image_name": "000000379.png", "low_idx": 73}, {"high_idx": 4, "image_name": "000000380.png", "low_idx": 73}, {"high_idx": 4, "image_name": "000000381.png", "low_idx": 74}, {"high_idx": 4, "image_name": "000000382.png", "low_idx": 74}, {"high_idx": 4, "image_name": "000000383.png", "low_idx": 75}, {"high_idx": 4, "image_name": "000000384.png", "low_idx": 75}, {"high_idx": 4, "image_name": "000000385.png", "low_idx": 76}, {"high_idx": 4, "image_name": "000000386.png", "low_idx": 76}, {"high_idx": 4, "image_name": "000000387.png", "low_idx": 77}, {"high_idx": 4, "image_name": "000000388.png", "low_idx": 77}, {"high_idx": 4, "image_name": "000000389.png", "low_idx": 77}, {"high_idx": 4, "image_name": "000000390.png", "low_idx": 77}, {"high_idx": 4, "image_name": "000000391.png", "low_idx": 77}, {"high_idx": 4, "image_name": "000000392.png", "low_idx": 77}, {"high_idx": 4, "image_name": "000000393.png", "low_idx": 77}, {"high_idx": 4, "image_name": "000000394.png", "low_idx": 77}, {"high_idx": 4, "image_name": "000000395.png", "low_idx": 77}, {"high_idx": 4, "image_name": "000000396.png", "low_idx": 77}, {"high_idx": 4, "image_name": "000000397.png", "low_idx": 77}, {"high_idx": 5, "image_name": "000000398.png", "low_idx": 78}, {"high_idx": 5, "image_name": "000000399.png", "low_idx": 78}, {"high_idx": 5, "image_name": "000000400.png", "low_idx": 78}, {"high_idx": 5, "image_name": "000000401.png", "low_idx": 78}, {"high_idx": 5, "image_name": "000000402.png", "low_idx": 78}, {"high_idx": 5, "image_name": "000000403.png", "low_idx": 78}, {"high_idx": 5, "image_name": "000000404.png", "low_idx": 78}, {"high_idx": 5, "image_name": "000000405.png", "low_idx": 78}, {"high_idx": 5, "image_name": "000000406.png", "low_idx": 78}, {"high_idx": 5, "image_name": "000000407.png", "low_idx": 78}, {"high_idx": 5, "image_name": "000000408.png", "low_idx": 78}, {"high_idx": 5, "image_name": "000000409.png", "low_idx": 78}, {"high_idx": 5, "image_name": "000000410.png", "low_idx": 78}, {"high_idx": 5, "image_name": "000000411.png", "low_idx": 78}, {"high_idx": 5, "image_name": "000000412.png", "low_idx": 78}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|6|-3|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [6.721688, 6.721688, -5.94865368, -5.94865368, 0.45253682, 0.45253682]], "coordinateReceptacleObjectId": ["Cabinet", [7.1000018, 7.1000018, -4.97099972, -4.97099972, 1.490000128, 1.490000128]], "forceVisible": true, "objectId": "Cup|+01.68|+00.11|-01.49"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|5|-3|2|0"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [4.6016, 4.6016, -6.45, -6.45, 6.584, 6.584]], "forceVisible": true, "objectId": "Microwave|+01.15|+01.65|-01.61"}}, {"discrete_action": {"action": "GotoLocation", "args": ["shelf"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|12|2|0|30"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "shelf"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [6.721688, 6.721688, -5.94865368, -5.94865368, 0.45253682, 0.45253682]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [14.75689508, 14.75689508, 6.56870556, 6.56870556, 3.4798656, 3.4798656]], "forceVisible": true, "objectId": "Cup|+01.68|+00.11|-01.49", "receptacleObjectId": "<PERSON><PERSON>|+03.69|+00.87|+01.64"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|+01.78|+00.37|-01.24"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [82, 157, 145, 203], "mask": [[46883, 61], [47183, 61], [47483, 61], [47782, 62], [48083, 61], [48383, 61], [48684, 60], [48984, 60], [49285, 59], [49585, 59], [49886, 58], [50186, 58], [50487, 57], [50787, 57], [51088, 57], [51388, 57], [51689, 56], [51989, 56], [52289, 56], [52590, 55], [52890, 55], [53191, 54], [53491, 54], [53792, 53], [54092, 53], [54393, 52], [54693, 52], [54994, 51], [55294, 51], [55595, 50], [55895, 50], [56196, 49], [56496, 49], [56797, 48], [57097, 48], [57398, 47], [57698, 47], [57999, 46], [58299, 47], [58600, 46], [58900, 46], [59201, 45], [59501, 45], [59802, 44], [60102, 44], [60403, 43], [60703, 43]], "point": [113, 179]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+01.68|+00.11|-01.49"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [110, 157, 127, 169], "mask": [[46910, 18], [47211, 17], [47511, 17], [47812, 15], [48112, 15], [48413, 14], [48714, 12], [49014, 12], [49315, 11], [49616, 10], [49917, 9], [50218, 7], [50519, 4]], "point": [118, 162]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|+01.78|+00.37|-01.24"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [74, 157, 144, 247], "mask": [[46883, 61], [47184, 60], [47484, 60], [47785, 59], [48083, 61], [48383, 61], [48682, 62], [48982, 62], [49282, 62], [49582, 62], [49882, 62], [50182, 62], [50481, 63], [50781, 63], [51081, 64], [51381, 64], [51681, 64], [51981, 64], [52281, 64], [52580, 65], [52880, 65], [53180, 65], [53480, 65], [53780, 65], [54080, 65], [54380, 65], [54679, 66], [54979, 66], [55279, 66], [55579, 66], [55879, 66], [56179, 66], [56479, 66], [56778, 67], [57078, 67], [57378, 67], [57678, 67], [57978, 65], [58278, 59], [58578, 56], [58877, 55], [59177, 52], [59477, 50], [59777, 48], [60077, 28], [60377, 28], [60677, 29], [60976, 30], [61276, 30], [61576, 30], [61876, 30], [62176, 30], [62476, 29], [62775, 30], [63075, 30], [63375, 30], [63675, 30], [63975, 30], [64275, 30], [64575, 30], [64874, 31], [65174, 31], [65474, 31], [65774, 30], [66074, 30], [66374, 30], [66674, 30], [66974, 30], [67275, 28], [67577, 26], [67878, 24], [68179, 23], [68480, 21], [68781, 20], [69082, 19], [69383, 18], [69684, 16], [69985, 15], [70286, 14], [70587, 13], [70888, 12], [71189, 10], [71490, 9], [71791, 8], [72092, 7], [72393, 5], [72694, 4], [72995, 3], [73296, 2], [73597, 2], [73898, 1]], "point": [109, 199]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.15|+01.65|-01.61"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [76, 1, 297, 124], "mask": [[76, 222], [376, 222], [676, 222], [976, 222], [1276, 222], [1576, 222], [1876, 222], [2176, 222], [2476, 222], [2776, 222], [3076, 222], [3376, 222], [3676, 222], [3976, 222], [4276, 222], [4576, 222], [4876, 222], [5176, 222], [5476, 222], [5776, 222], [6076, 222], [6376, 222], [6676, 222], [6976, 222], [7276, 222], [7576, 222], [7876, 222], [8176, 222], [8476, 222], [8776, 222], [9076, 222], [9376, 222], [9676, 222], [9976, 222], [10276, 222], [10576, 222], [10876, 222], [11176, 222], [11476, 222], [11776, 222], [12076, 222], [12376, 222], [12676, 222], [12976, 222], [13276, 222], [13576, 222], [13876, 222], [14176, 222], [14476, 222], [14776, 222], [15076, 222], [15376, 222], [15676, 222], [15976, 222], [16276, 222], [16576, 222], [16876, 222], [17176, 222], [17476, 222], [17776, 222], [18076, 222], [18376, 222], [18676, 222], [18976, 222], [19276, 222], [19576, 222], [19876, 222], [20176, 222], [20476, 222], [20776, 222], [21076, 222], [21376, 222], [21676, 222], [21976, 222], [22276, 222], [22576, 222], [22876, 222], [23176, 222], [23476, 222], [23776, 222], [24076, 222], [24376, 222], [24676, 222], [24976, 222], [25276, 222], [25576, 222], [25876, 222], [26176, 222], [26476, 222], [26776, 222], [27076, 222], [27376, 222], [27676, 222], [27976, 222], [28276, 222], [28576, 222], [28876, 222], [29176, 222], [29476, 222], [29776, 222], [30076, 222], [30376, 222], [30676, 222], [30976, 222], [31276, 222], [31576, 222], [31876, 222], [32176, 222], [32477, 219], [32778, 215], [33080, 208], [33381, 209], [33681, 209], [33981, 209], [34281, 209], [34582, 205], [34884, 199], [35186, 193], [35488, 187], [35790, 183], [36092, 181], [36394, 169], [36564, 9], [36696, 163], [36864, 9], [36998, 157], [37164, 9]], "point": [186, 61]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+01.68|+00.11|-01.49", "placeStationary": true, "receptacleObjectId": "Microwave|+01.15|+01.65|-01.61"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 297, 124], "mask": [[0, 81], [252, 46], [300, 81], [551, 47], [600, 81], [851, 47], [900, 82], [1150, 48], [1200, 82], [1449, 49], [1500, 298], [1800, 298], [2100, 298], [2400, 298], [2700, 298], [3000, 298], [3300, 298], [3600, 298], [3900, 298], [4200, 298], [4500, 298], [4800, 298], [5100, 298], [5400, 298], [5700, 298], [6000, 298], [6300, 298], [6600, 298], [6900, 298], [7200, 298], [7500, 298], [7800, 298], [8100, 298], [8400, 298], [8700, 298], [9000, 298], [9300, 298], [9600, 298], [9900, 298], [10200, 298], [10500, 298], [10800, 298], [11100, 298], [11400, 298], [11700, 298], [12000, 298], [12300, 298], [12600, 298], [12900, 298], [13200, 298], [13500, 298], [13800, 298], [14100, 298], [14400, 298], [14700, 298], [15000, 298], [15300, 298], [15600, 298], [15900, 298], [16200, 298], [16500, 298], [16800, 298], [17100, 298], [17400, 298], [17700, 298], [18000, 298], [18300, 298], [18600, 298], [18900, 298], [19200, 298], [19500, 298], [19800, 298], [20100, 298], [20400, 298], [20700, 298], [21000, 298], [21300, 298], [21600, 298], [21900, 298], [22200, 298], [22500, 298], [22800, 298], [23100, 298], [23401, 297], [23703, 295], [24005, 293], [24307, 291], [24609, 289], [24912, 286], [25214, 284], [25516, 282], [25818, 280], [26120, 278], [26422, 276], [26724, 274], [27026, 272], [27328, 270], [27630, 268], [27932, 266], [28234, 264], [28536, 262], [28839, 259], [29141, 257], [29443, 255], [29745, 253], [30047, 251], [30349, 249], [30651, 247], [30953, 245], [31255, 243], [31557, 241], [31859, 239], [32161, 237], [32464, 232], [32766, 227], [33068, 220], [33381, 209], [33681, 209], [33981, 209], [34281, 209], [34582, 205], [34884, 199], [35186, 193], [35488, 187], [35790, 183], [36092, 181], [36394, 169], [36564, 9], [36696, 163], [36864, 9], [36998, 157], [37164, 9]], "point": [148, 61]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.15|+01.65|-01.61"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 297, 124], "mask": [[0, 81], [252, 46], [300, 81], [551, 47], [600, 81], [851, 47], [900, 82], [1150, 48], [1200, 82], [1449, 49], [1500, 298], [1800, 298], [2100, 298], [2400, 298], [2700, 298], [3000, 298], [3300, 298], [3600, 298], [3900, 298], [4200, 298], [4500, 298], [4800, 298], [5100, 298], [5400, 298], [5700, 298], [6000, 298], [6300, 298], [6600, 298], [6900, 298], [7200, 298], [7500, 298], [7800, 298], [8100, 298], [8400, 298], [8700, 298], [9000, 298], [9300, 298], [9600, 298], [9900, 298], [10200, 298], [10500, 298], [10800, 298], [11100, 298], [11400, 298], [11700, 298], [12000, 298], [12300, 141], [12450, 148], [12600, 136], [12755, 143], [12900, 133], [13058, 140], [13200, 131], [13360, 138], [13500, 130], [13661, 137], [13800, 129], [13962, 136], [14100, 129], [14262, 136], [14400, 129], [14563, 135], [14700, 129], [14862, 136], [15000, 129], [15162, 136], [15300, 129], [15462, 136], [15600, 129], [15762, 136], [15900, 129], [16062, 136], [16200, 129], [16362, 136], [16500, 130], [16662, 136], [16800, 130], [16962, 136], [17100, 130], [17262, 136], [17400, 130], [17562, 136], [17700, 130], [17861, 137], [18000, 130], [18161, 137], [18300, 131], [18461, 137], [18600, 131], [18761, 137], [18900, 131], [19061, 137], [19200, 131], [19360, 138], [19500, 131], [19660, 138], [19800, 132], [19960, 138], [20100, 132], [20260, 138], [20400, 132], [20560, 138], [20700, 132], [20859, 139], [21000, 133], [21159, 139], [21300, 133], [21459, 139], [21600, 133], [21758, 140], [21900, 133], [22058, 140], [22200, 134], [22358, 140], [22500, 134], [22658, 140], [22800, 134], [22957, 141], [23100, 134], [23257, 141], [23401, 134], [23557, 141], [23703, 132], [23857, 141], [24005, 130], [24157, 141], [24307, 128], [24456, 142], [24609, 126], [24756, 142], [24912, 123], [25056, 142], [25214, 122], [25356, 142], [25516, 120], [25656, 142], [25818, 118], [25956, 142], [26120, 116], [26256, 142], [26422, 114], [26556, 142], [26724, 112], [26856, 142], [27026, 110], [27155, 143], [27328, 109], [27455, 143], [27630, 109], [27753, 145], [27932, 266], [28234, 264], [28536, 262], [28839, 259], [29141, 257], [29443, 255], [29745, 253], [30047, 251], [30349, 249], [30651, 247], [30953, 245], [31255, 243], [31557, 241], [31859, 239], [32161, 237], [32464, 232], [32766, 227], [33068, 220], [33381, 209], [33681, 209], [33981, 209], [34281, 209], [34582, 205], [34884, 199], [35186, 193], [35488, 187], [35790, 183], [36092, 181], [36394, 169], [36564, 9], [36696, 163], [36864, 9], [36998, 157], [37164, 9]], "point": [160, 64]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [4.6016, 4.6016, -6.45, -6.45, 6.584, 6.584]], "forceVisible": true, "objectId": "Microwave|+01.15|+01.65|-01.61"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [76, 1, 297, 124], "mask": [[76, 222], [376, 222], [676, 222], [976, 222], [1276, 222], [1576, 222], [1876, 222], [2176, 222], [2476, 222], [2776, 222], [3076, 222], [3376, 222], [3676, 222], [3976, 222], [4276, 222], [4576, 222], [4876, 222], [5176, 222], [5476, 222], [5776, 222], [6076, 222], [6376, 222], [6676, 222], [6976, 222], [7276, 222], [7576, 222], [7876, 222], [8176, 222], [8476, 222], [8776, 222], [9076, 222], [9376, 222], [9676, 222], [9976, 222], [10276, 222], [10576, 222], [10876, 222], [11176, 222], [11476, 222], [11776, 222], [12076, 222], [12376, 222], [12676, 222], [12976, 222], [13276, 222], [13576, 222], [13876, 222], [14176, 222], [14476, 222], [14776, 222], [15076, 222], [15376, 222], [15676, 222], [15976, 222], [16276, 222], [16576, 222], [16876, 222], [17176, 222], [17476, 222], [17776, 222], [18076, 222], [18376, 222], [18676, 222], [18976, 222], [19276, 222], [19576, 222], [19876, 222], [20176, 222], [20476, 222], [20776, 222], [21076, 222], [21376, 222], [21676, 222], [21976, 222], [22276, 222], [22576, 222], [22876, 222], [23176, 222], [23476, 222], [23776, 222], [24076, 222], [24376, 222], [24676, 222], [24976, 222], [25276, 222], [25576, 222], [25876, 222], [26176, 222], [26476, 222], [26776, 222], [27076, 222], [27376, 222], [27676, 222], [27976, 222], [28276, 222], [28576, 222], [28876, 222], [29176, 222], [29476, 222], [29776, 222], [30076, 222], [30376, 222], [30676, 222], [30976, 222], [31276, 222], [31576, 222], [31876, 222], [32176, 222], [32477, 219], [32778, 215], [33080, 208], [33381, 209], [33681, 209], [33981, 209], [34281, 209], [34582, 205], [34884, 199], [35186, 193], [35488, 187], [35790, 183], [36092, 181], [36394, 169], [36564, 9], [36696, 163], [36864, 9], [36998, 157], [37164, 9]], "point": [186, 61]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [4.6016, 4.6016, -6.45, -6.45, 6.584, 6.584]], "forceVisible": true, "objectId": "Microwave|+01.15|+01.65|-01.61"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [76, 1, 297, 124], "mask": [[76, 222], [376, 222], [676, 222], [976, 222], [1276, 222], [1576, 222], [1876, 222], [2176, 222], [2476, 222], [2776, 222], [3076, 222], [3376, 222], [3676, 222], [3976, 222], [4276, 222], [4576, 222], [4876, 222], [5176, 222], [5476, 222], [5776, 222], [6076, 222], [6376, 222], [6676, 222], [6976, 222], [7276, 222], [7576, 222], [7876, 222], [8176, 222], [8476, 222], [8776, 222], [9076, 222], [9376, 222], [9676, 222], [9976, 222], [10276, 222], [10576, 222], [10876, 222], [11176, 222], [11476, 222], [11776, 222], [12076, 222], [12376, 222], [12676, 222], [12976, 222], [13276, 222], [13576, 222], [13876, 222], [14176, 222], [14476, 222], [14776, 222], [15076, 222], [15376, 222], [15676, 222], [15976, 222], [16276, 222], [16576, 222], [16876, 222], [17176, 222], [17476, 222], [17776, 222], [18076, 222], [18376, 222], [18676, 222], [18976, 222], [19276, 222], [19576, 222], [19876, 222], [20176, 222], [20476, 222], [20776, 222], [21076, 222], [21376, 222], [21676, 222], [21976, 222], [22276, 222], [22576, 222], [22876, 222], [23176, 222], [23476, 222], [23776, 222], [24076, 222], [24376, 222], [24676, 222], [24976, 222], [25276, 222], [25576, 222], [25876, 222], [26176, 222], [26476, 222], [26776, 222], [27076, 222], [27376, 222], [27676, 222], [27976, 222], [28276, 222], [28576, 222], [28876, 222], [29176, 222], [29476, 222], [29776, 222], [30076, 222], [30376, 222], [30676, 222], [30976, 222], [31276, 222], [31576, 222], [31876, 222], [32176, 222], [32477, 219], [32778, 215], [33080, 208], [33381, 209], [33681, 209], [33981, 209], [34281, 209], [34582, 205], [34884, 199], [35186, 193], [35488, 187], [35790, 183], [36092, 181], [36394, 169], [36564, 9], [36696, 163], [36864, 9], [36998, 157], [37164, 9]], "point": [186, 61]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.15|+01.65|-01.61"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [76, 1, 297, 124], "mask": [[76, 222], [376, 222], [676, 222], [976, 222], [1276, 222], [1576, 222], [1876, 222], [2176, 222], [2476, 222], [2776, 222], [3076, 222], [3376, 222], [3676, 222], [3976, 222], [4276, 222], [4576, 222], [4876, 222], [5176, 222], [5476, 222], [5776, 222], [6076, 222], [6376, 222], [6676, 222], [6976, 222], [7276, 222], [7576, 222], [7876, 222], [8176, 222], [8476, 222], [8776, 222], [9076, 222], [9376, 222], [9676, 222], [9976, 222], [10276, 222], [10576, 222], [10876, 222], [11176, 222], [11476, 222], [11776, 222], [12076, 222], [12376, 222], [12676, 222], [12976, 222], [13276, 222], [13576, 222], [13876, 222], [14176, 222], [14476, 222], [14776, 222], [15076, 222], [15376, 222], [15676, 222], [15976, 222], [16276, 222], [16576, 222], [16876, 222], [17176, 222], [17476, 222], [17776, 222], [18076, 222], [18376, 222], [18676, 222], [18976, 222], [19276, 222], [19576, 222], [19876, 222], [20176, 222], [20476, 222], [20776, 222], [21076, 222], [21376, 222], [21676, 222], [21976, 222], [22276, 222], [22576, 222], [22876, 222], [23176, 222], [23476, 222], [23776, 222], [24076, 222], [24376, 222], [24676, 222], [24976, 222], [25276, 222], [25576, 222], [25876, 222], [26176, 222], [26476, 222], [26776, 222], [27076, 222], [27376, 222], [27676, 222], [27976, 222], [28276, 222], [28576, 222], [28876, 222], [29176, 222], [29476, 222], [29776, 222], [30076, 222], [30376, 222], [30676, 222], [30976, 222], [31276, 222], [31576, 222], [31876, 222], [32176, 222], [32477, 219], [32778, 215], [33080, 208], [33381, 209], [33681, 209], [33981, 209], [34281, 209], [34582, 205], [34884, 199], [35186, 193], [35488, 187], [35790, 183], [36092, 181], [36394, 169], [36564, 9], [36696, 163], [36864, 9], [36998, 157], [37164, 9]], "point": [186, 61]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+01.68|+00.11|-01.49"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [129, 42, 162, 93], "mask": [[12441, 9], [12736, 19], [13033, 25], [13331, 29], [13630, 31], [13929, 33], [14229, 33], [14529, 34], [14829, 33], [15129, 33], [15429, 33], [15729, 33], [16029, 33], [16329, 33], [16630, 32], [16930, 32], [17230, 32], [17530, 32], [17830, 31], [18130, 31], [18431, 30], [18731, 30], [19031, 30], [19331, 29], [19631, 29], [19932, 28], [20232, 28], [20532, 28], [20832, 27], [21133, 26], [21433, 26], [21733, 25], [22033, 25], [22334, 24], [22634, 24], [22934, 23], [23234, 23], [23535, 22], [23835, 22], [24135, 22], [24435, 21], [24735, 21], [25035, 21], [25336, 20], [25636, 20], [25936, 20], [26236, 20], [26536, 20], [26836, 20], [27136, 19], [27437, 18], [27739, 14]], "point": [145, 66]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.15|+01.65|-01.61"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 297, 124], "mask": [[0, 81], [252, 46], [300, 81], [551, 47], [600, 81], [851, 47], [900, 82], [1150, 48], [1200, 82], [1449, 49], [1500, 298], [1800, 298], [2100, 298], [2400, 298], [2700, 298], [3000, 298], [3300, 298], [3600, 298], [3900, 298], [4200, 298], [4500, 298], [4800, 298], [5100, 298], [5400, 298], [5700, 298], [6000, 298], [6300, 298], [6600, 298], [6900, 298], [7200, 298], [7500, 298], [7800, 298], [8100, 298], [8400, 298], [8700, 298], [9000, 298], [9300, 298], [9600, 298], [9900, 298], [10200, 298], [10500, 298], [10800, 298], [11100, 298], [11400, 298], [11700, 298], [12000, 298], [12300, 298], [12600, 298], [12900, 298], [13200, 298], [13500, 298], [13800, 298], [14100, 298], [14400, 298], [14700, 298], [15000, 298], [15300, 298], [15600, 298], [15900, 298], [16200, 298], [16500, 298], [16800, 298], [17100, 298], [17400, 298], [17700, 298], [18000, 298], [18300, 298], [18600, 298], [18900, 298], [19200, 298], [19500, 298], [19800, 298], [20100, 298], [20400, 298], [20700, 298], [21000, 298], [21300, 298], [21600, 298], [21900, 298], [22200, 298], [22500, 298], [22800, 298], [23100, 298], [23401, 297], [23703, 295], [24005, 293], [24307, 291], [24609, 289], [24912, 286], [25214, 284], [25516, 282], [25818, 280], [26120, 278], [26422, 276], [26724, 274], [27026, 272], [27328, 270], [27630, 268], [27932, 266], [28234, 264], [28536, 262], [28839, 259], [29141, 257], [29443, 255], [29745, 253], [30047, 251], [30349, 249], [30651, 247], [30953, 245], [31255, 243], [31557, 241], [31859, 239], [32161, 237], [32464, 232], [32766, 227], [33068, 220], [33381, 209], [33681, 209], [33981, 209], [34281, 209], [34582, 205], [34884, 199], [35186, 193], [35488, 187], [35790, 183], [36092, 181], [36394, 169], [36564, 9], [36696, 163], [36864, 9], [36998, 157], [37164, 9]], "point": [148, 61]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+01.68|+00.11|-01.49", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|+03.69|+00.87|+01.64"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [223, 166, 299, 269], "mask": [[49727, 2], [49733, 3], [49741, 19], [50025, 5], [50034, 2], [50042, 18], [50323, 7], [50335, 2], [50343, 18], [50623, 7], [50636, 2], [50645, 17], [50923, 8], [50937, 1], [50948, 14], [51224, 7], [51238, 1], [51524, 4], [51539, 1], [51825, 4], [52125, 4], [52426, 4], [52726, 5], [53026, 6], [53327, 4], [53627, 5], [53928, 5], [54228, 6], [54529, 6], [54829, 7], [55129, 9], [55430, 9], [55730, 11], [56031, 12], [56331, 14], [56632, 15], [56932, 16], [57232, 16], [57533, 14], [57833, 13], [58134, 12], [58434, 11], [58735, 10], [59035, 9], [59335, 9], [59636, 8], [59936, 8], [60237, 6], [60537, 6], [60838, 5], [61138, 5], [61438, 5], [61739, 4], [62039, 4], [62340, 4], [62640, 4], [62941, 3], [63241, 3], [63542, 3], [63842, 3], [64142, 4], [64443, 3], [64743, 4], [65044, 4], [65344, 4], [65645, 4], [65945, 5], [66245, 6], [66294, 2], [66546, 6], [66594, 3], [66846, 7], [66893, 4], [67147, 8], [67192, 6], [67447, 9], [67491, 8], [67748, 10], [67790, 9], [68048, 12], [68088, 12], [68348, 14], [68386, 14], [68649, 16], [68684, 16], [68949, 21], [68980, 20], [69250, 50], [69550, 49], [69851, 48], [70151, 47], [70451, 47], [70752, 45], [71052, 44], [71353, 43], [71653, 42], [71954, 40], [72254, 40], [72554, 39], [72855, 38], [73155, 37], [73456, 35], [73756, 35], [74057, 33], [74357, 33], [74658, 31], [74958, 30], [75258, 30], [75559, 28], [75599, 1], [75859, 28], [75898, 2], [76160, 26], [76198, 2], [76460, 25], [76497, 3], [76761, 24], [76796, 4], [77061, 23], [77096, 4], [77361, 22], [77395, 5], [77662, 21], [77694, 6], [77962, 20], [77994, 6], [78263, 19], [78293, 7], [78563, 18], [78592, 8], [78864, 16], [78892, 8], [79166, 14], [79191, 9], [79468, 11], [79491, 9], [79770, 9], [79790, 10], [80072, 6], [80089, 11], [80374, 3], [80389, 11], [80676, 1], [80688, 12]], "point": [257, 225]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan7", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -3.5, "y": 0.9009992, "z": -1.0}, "object_poses": [{"objectName": "Book_7abfb00f", "position": {"x": 1.65124238, "y": 0.9068576, "z": -1.34649062}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Book_7abfb00f", "position": {"x": -1.88839841, "y": 1.65425754, "z": -1.7109437}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_628dcab4", "position": {"x": -2.5922792, "y": 0.8786325, "z": 3.39376974}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Kettle_ef41be6a", "position": {"x": 0.9741, "y": 0.9576474, "z": -1.6882}, "rotation": {"x": 0.0, "y": 180.000061, "z": 0.0}}, {"objectName": "WineBottle_471b4432", "position": {"x": -1.62763071, "y": 0.9130287, "z": -1.59173536}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_471b4432", "position": {"x": -2.32146621, "y": 0.8795191, "z": 2.99919486}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_759492dd", "position": {"x": -1.96116972, "y": 0.9270999, "z": -1.2658236}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_759492dd", "position": {"x": -1.6493, "y": 0.7327641, "z": -0.930900037}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_fcc17e74", "position": {"x": 1.3286, "y": 0.9576474, "z": -1.6882}, "rotation": {"x": 0.0, "y": 180.000061, "z": 0.0}}, {"objectName": "Pot_fcc17e74", "position": {"x": 0.079062, "y": 1.5745033, "z": 2.10055065}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Fork_6f28e6b4", "position": {"x": -1.74855781, "y": 0.7203189, "z": -0.930900037}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_6f28e6b4", "position": {"x": -2.148058, "y": 0.719712734, "z": -1.51025}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_b16915eb", "position": {"x": -2.24849272, "y": 0.8759226, "z": 2.916141}, "rotation": {"x": 0.0, "y": 135.000092, "z": 0.0}}, {"objectName": "Egg_bdc82745", "position": {"x": 0.0790612549, "y": 0.414300382, "z": 1.9672153}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Egg_bdc82745", "position": {"x": -0.263125837, "y": 0.828299165, "z": 2.02334881}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Knife_f7b250a5", "position": {"x": -1.96116972, "y": 0.9340927, "z": 0.363735378}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_30c4ced6", "position": {"x": -2.15259933, "y": 0.879034, "z": 3.073422}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_30c4ced6", "position": {"x": -3.16580033, "y": 0.879034, "z": 3.14764929}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_d317e149", "position": {"x": -1.62763071, "y": 0.9594331, "z": -0.9399118}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_d317e149", "position": {"x": -2.43651819, "y": 0.9259225, "z": 3.728477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_9c57f915", "position": {"x": -2.80765414, "y": 0.876712143, "z": 2.88414288}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_9c57f915", "position": {"x": -2.51074529, "y": 0.87291497, "z": 2.88414288}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_ba969788", "position": {"x": -1.84999013, "y": 0.9084062, "z": 0.363735378}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_403e6980", "position": {"x": 0.5712522, "y": 0.9955075, "z": -1.369268}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_403e6980", "position": {"x": -2.65919971, "y": 0.958200753, "z": 3.55961}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e46f4ce8", "position": {"x": -1.84999013, "y": 0.977197945, "z": -1.2658236}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_e4d9d6fe", "position": {"x": -1.96116972, "y": 0.9071, "z": -0.9399118}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_23f7a2f7", "position": {"x": 0.30718714, "y": 0.858028769, "z": 2.1375432}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Book_7abfb00f", "position": {"x": -1.94, "y": 0.900999963, "z": -1.693}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_6f28e6b4", "position": {"x": -2.48730588, "y": 0.8753418, "z": 3.288797}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Tomato_d317e149", "position": {"x": -0.367531717, "y": 0.755091369, "z": -1.57347214}, "rotation": {"x": 1.40334191e-14, "y": 270.0, "z": 0.0}}, {"objectName": "Egg_bdc82745", "position": {"x": 0.307186425, "y": 0.414300382, "z": 1.996246}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Kettle_ef41be6a", "position": {"x": -1.33146334, "y": 1.6585412, "z": -1.74787152}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_2fddf4c6", "position": {"x": 3.671401, "y": 0.168322966, "z": 1.84175575}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_da95a06a", "position": {"x": 1.680422, "y": 0.113134205, "z": -1.48716342}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_471b4432", "position": {"x": -0.8448454, "y": 0.193631619, "z": 2.174791}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_82e11e5b", "position": {"x": -2.088548, "y": 0.123987138, "z": -1.4337585}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_759492dd", "position": {"x": -2.99693346, "y": 0.8935903, "z": 2.99919486}, "rotation": {"x": 0.0, "y": 4.829673e-06, "z": 0.0}}, {"objectName": "Potato_92b0ae81", "position": {"x": 0.07906306, "y": 1.60891652, "z": 2.2792387}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Pot_fcc17e74", "position": {"x": 3.63793588, "y": 0.555264354, "z": 1.553224}, "rotation": {"x": 359.961, "y": 270.000061, "z": 0.03900441}}, {"objectName": "Knife_f7b250a5", "position": {"x": -2.07234931, "y": 0.9340927, "z": -1.59173536}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_ba969788", "position": {"x": -1.6864779, "y": 0.115884662, "z": -1.14136827}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_32949c32", "position": {"x": 0.716121554, "y": 0.112981141, "z": -1.37086713}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e46f4ce8", "position": {"x": -2.24849176, "y": 0.9398912, "z": 3.52761078}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "ButterKnife_628dcab4", "position": {"x": 0.06952658, "y": 0.930619836, "z": -1.64601612}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bread_403e6980", "position": {"x": -3.1658, "y": 0.958200753, "z": 3.37033081}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Vase_b78f3af0", "position": {"x": 0.207431242, "y": 0.115550816, "z": -1.379001}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_bd179bb7", "position": {"x": 0.5914999, "y": 0.11623919, "z": -1.40825}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_23f7a2f7", "position": {"x": -2.07234931, "y": 0.9672848, "z": -1.2658236}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_aa05aa65", "position": {"x": -0.0349998921, "y": 0.802453458, "z": 2.194644}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Spoon_b16915eb", "position": {"x": 0.289695024, "y": 0.9297715, "z": -1.6460166}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_30c4ced6", "position": {"x": -1.37882578, "y": 0.912543535, "z": -1.47111571}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_e4d9d6fe", "position": {"x": -1.112, "y": 0.9581, "z": -1.4795}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Statue_c0a5928e", "position": {"x": 3.676045, "y": 0.1683198, "z": 1.410345}, "rotation": {"x": 0.0, "y": 334.521, "z": 0.0}}, {"objectName": "Statue_bf0c7315", "position": {"x": 3.668767, "y": 0.5405253, "z": 1.800497}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_9c57f915", "position": {"x": -2.07234931, "y": 0.910221756, "z": -0.614}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 920202949, "scene_num": 7}, "task_id": "trial_T20190906_211134_297102", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1MKCTVKE7J0ZP_3OJSZ2ATDVD2WLEX6AX8F1332NL75H", "high_descs": ["Turn right, then turn right at the counter, then go straight, then go right toward the stove.", "Take the cup from the cabinet, to the left of the stove.", "Turn right and turn left to go toward the stove.", "Warm the cup in the microwave, above the stove.", "Turn left, then turn left again, then turn right, then turn left to reach the shelf.", "Place the cup on the top shelf. "], "task_desc": "Put a warm cup on the shelf. ", "votes": [1, 1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_3UNH76FOCVMJJCV4LI4U9GYMSFUMYT", "high_descs": ["head over to the kitchen counter to the left of the stove", "open up the cupboard below and grab a cup", "bring the cup over to the microwave", "put the cup inside the microwave and cook the cup for a bit, then take it out", "bring the cup over to the brown shelves in the corner of the room", "place the cup on the top brown shelf"], "task_desc": "put a hot cup on top of the brown shelf in the corner of the kitchen", "votes": [1, 1, 1]}, {"assignment_id": "AJNQ2PBD07FKE_3HVVDCPGTH9NW7LOUF56VUTEMPPTY4", "high_descs": ["Turn right, go forward, at counter turn right again, after the refrigerator turn right again until facing the stove. ", "Open the cupboard to left of stove and take out a bowl.", "Put the bowl in the microwave.", "Take the bowl out of the microwave.", "Turn left and then right until you see a shelf on your right.", "Put the bowl on the shelf."], "task_desc": "Moving a bowl to the shelf.", "votes": [1, 0, 1]}]}}