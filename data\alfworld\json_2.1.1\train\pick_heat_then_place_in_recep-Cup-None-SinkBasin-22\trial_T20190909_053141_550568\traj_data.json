{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000030.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000031.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000032.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000033.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000034.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000043.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000044.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000045.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000046.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000047.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000077.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000078.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000079.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000080.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000081.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000082.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000083.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000084.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000085.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000088.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000089.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000090.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000091.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000092.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000093.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000094.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000187.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000188.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000189.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000190.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000191.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000192.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000193.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000194.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000195.png", "low_idx": 30}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "SinkBasin", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-2|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-4.47874116, -4.47874116, -4.9645624, -4.9645624, 3.3667964, 3.3667964]], "coordinateReceptacleObjectId": ["SinkBasin", [-5.3792, -5.3792, -5.0, -5.0, 3.356, 3.356]], "forceVisible": true, "objectId": "Cup|-01.12|+00.84|-01.24"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|-2|2|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [1.132, 1.132, -5.3248, -5.3248, 3.6003664, 3.6003664]], "forceVisible": true, "objectId": "Microwave|+00.28|+00.90|-01.33"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-6|-2|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "sinkbasin"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-4.47874116, -4.47874116, -4.9645624, -4.9645624, 3.3667964, 3.3667964]], "coordinateReceptacleObjectId": ["SinkBasin", [-5.3792, -5.3792, -5.0, -5.0, 3.356, 3.356]], "forceVisible": true, "objectId": "Cup|-01.12|+00.84|-01.24", "receptacleObjectId": "Sink|-01.33|+00.92|-01.23|SinkBasin"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.12|+00.84|-01.24"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [169, 111, 194, 157], "mask": [[33179, 7], [33476, 13], [33774, 17], [34073, 20], [34372, 22], [34672, 22], [34972, 23], [35272, 23], [35571, 24], [35871, 24], [36171, 24], [36471, 24], [36771, 24], [37071, 24], [37371, 24], [37671, 23], [37971, 23], [38271, 23], [38572, 22], [38872, 21], [39172, 21], [39473, 19], [39773, 18], [40074, 16], [40375, 14], [40676, 12], [40978, 9], [41280, 2], [41580, 2], [41880, 2], [42180, 2], [42474, 10], [42772, 14], [43071, 17], [43370, 19], [43669, 21], [43969, 21], [44269, 22], [44569, 22], [44869, 22], [45169, 22], [45470, 21], [45770, 20], [46071, 18], [46373, 15], [46675, 11], [46978, 5]], "point": [181, 133]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.12|+00.84|-01.24", "placeStationary": true, "receptacleObjectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 4, 252, 267], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15322, 229], [15620, 230], [15919, 231], [16219, 231], [16518, 232], [16817, 232], [17116, 233], [17415, 234], [17715, 234], [18014, 235], [18313, 236], [18612, 236], [18911, 237], [19211, 237], [19510, 237], [19809, 238], [20108, 239], [20407, 240], [20707, 239], [21006, 240], [21305, 241], [21604, 241], [21903, 242], [22203, 242], [22502, 243], [22801, 243], [23100, 244], [23400, 244], [23700, 243], [24000, 243], [24300, 243], [24600, 243], [24900, 118], [25023, 119], [25200, 114], [25328, 114], [25500, 112], [25630, 112], [25800, 111], [25931, 110], [26100, 111], [26232, 109], [26400, 111], [26532, 109], [26700, 111], [26833, 108], [27000, 111], [27133, 107], [27300, 111], [27433, 107], [27600, 111], [27733, 107], [27900, 111], [28034, 106], [28200, 111], [28334, 105], [28500, 112], [28633, 106], [28800, 112], [28933, 106], [29100, 112], [29233, 105], [29400, 112], [29532, 19], [29554, 84], [29700, 113], [29831, 18], [29856, 82], [30000, 113], [30130, 18], [30157, 81], [30300, 113], [30429, 18], [30458, 79], [30600, 114], [30728, 18], [30758, 79], [30900, 115], [31027, 19], [31059, 78], [31200, 115], [31326, 19], [31359, 77], [31500, 116], [31624, 21], [31660, 76], [31800, 117], [31922, 23], [31960, 76], [32100, 145], [32260, 76], [32400, 145], [32560, 75], [32700, 145], [32860, 75], [33000, 145], [33160, 75], [33300, 145], [33459, 75], [33600, 146], [33759, 75], [33900, 146], [34059, 75], [34200, 147], [34358, 76], [34500, 148], [34657, 76], [34800, 149], [34956, 77], [35100, 233], [35400, 232], [35700, 232], [36000, 232], [36300, 232], [36600, 231], [36900, 231], [37200, 231], [37500, 230], [37800, 230], [38100, 230], [38400, 230], [38700, 229], [39000, 229], [39300, 229], [39600, 228], [39900, 228], [40200, 228], [40500, 228], [40800, 227], [41100, 227], [41400, 227], [41700, 226], [42000, 226], [42300, 225], [42600, 226], [42900, 225], [43200, 225], [43500, 225], [43800, 56], [43858, 166], [44100, 56], [44158, 166], [44400, 56], [44458, 166], [44700, 55], [44759, 165], [45000, 55], [45300, 54], [45600, 54], [45900, 54], [46200, 53], [46500, 53], [46800, 53], [47100, 52], [47400, 52], [47700, 52], [48000, 51], [48300, 51], [48600, 50], [48900, 50], [49200, 50], [49500, 49], [49800, 49], [50100, 49], [50400, 48], [50700, 48], [51000, 48], [51300, 47], [51600, 47], [51900, 46], [52200, 46], [52500, 46], [52800, 45], [53100, 45], [53400, 45], [53700, 44], [54000, 44], [54300, 43], [54600, 43], [54900, 43], [55200, 42], [55500, 42], [55800, 42], [56100, 41], [56400, 41], [56700, 41], [57000, 40], [57300, 40], [57600, 39], [57900, 39], [58200, 39], [58500, 38], [58800, 38], [59100, 38], [59400, 37], [59700, 37], [60000, 37], [60300, 36], [60600, 36], [60900, 35], [61200, 35], [61500, 35], [61800, 34], [62100, 34], [62400, 34], [62700, 33], [63000, 33], [63300, 33], [63600, 32], [63900, 32], [64200, 31], [64500, 31], [64800, 31], [65100, 30], [65400, 30], [65700, 30], [66000, 29], [66300, 29], [66600, 28], [66900, 28], [67200, 28], [67500, 27], [67800, 27], [68100, 27], [68400, 26], [68700, 26], [69000, 26], [69300, 25], [69600, 25], [69900, 24], [70200, 24], [70500, 24], [70800, 23], [71100, 23], [71400, 23], [71700, 22], [72000, 22], [72300, 22], [72600, 21], [72900, 21], [73200, 20], [73500, 20], [73800, 20], [74100, 19], [74400, 19], [74700, 19], [75000, 18], [75300, 18], [75600, 18], [75900, 17], [76200, 17], [76500, 16], [76800, 16], [77100, 16], [77400, 15], [77700, 17], [78000, 18], [78300, 19], [78600, 19], [78900, 14], [78915, 2], [79200, 13], [79500, 13], [79800, 12]], "point": [126, 134]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 4, 252, 267], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15322, 229], [15620, 230], [15919, 231], [16219, 231], [16518, 232], [16817, 232], [17116, 233], [17415, 234], [17715, 234], [18014, 235], [18313, 236], [18612, 236], [18911, 237], [19211, 237], [19510, 237], [19809, 132], [19964, 83], [20108, 133], [20263, 84], [20407, 134], [20563, 84], [20707, 135], [20863, 83], [21006, 136], [21163, 83], [21305, 137], [21462, 84], [21604, 139], [21762, 83], [21903, 140], [22061, 84], [22203, 141], [22361, 84], [22502, 142], [22660, 85], [22801, 145], [22959, 85], [23100, 147], [23257, 87], [23400, 148], [23556, 88], [23700, 151], [23853, 90], [24000, 151], [24153, 90], [24300, 151], [24453, 90], [24600, 151], [24753, 90], [24900, 118], [25023, 28], [25053, 89], [25200, 114], [25328, 23], [25353, 89], [25500, 112], [25630, 21], [25653, 89], [25800, 111], [25931, 20], [25953, 88], [26100, 111], [26232, 19], [26253, 88], [26400, 111], [26532, 16], [26556, 85], [26700, 111], [26833, 12], [26859, 82], [27000, 111], [27133, 10], [27161, 79], [27300, 111], [27433, 9], [27462, 78], [27600, 111], [27733, 8], [27763, 77], [27900, 111], [28034, 7], [28063, 77], [28200, 111], [28334, 7], [28364, 75], [28500, 112], [28633, 8], [28663, 76], [28800, 112], [28933, 8], [28963, 76], [29100, 112], [29233, 9], [29262, 76], [29400, 112], [29532, 11], [29561, 77], [29700, 113], [29831, 14], [29860, 78], [30000, 113], [30130, 17], [30157, 81], [30300, 113], [30429, 18], [30458, 79], [30600, 114], [30728, 18], [30758, 79], [30900, 115], [31027, 19], [31059, 78], [31200, 115], [31326, 19], [31359, 77], [31500, 116], [31624, 21], [31660, 76], [31800, 117], [31922, 23], [31960, 76], [32100, 145], [32260, 76], [32400, 145], [32560, 75], [32700, 145], [32860, 75], [33000, 145], [33160, 75], [33300, 145], [33459, 75], [33600, 146], [33759, 75], [33900, 146], [34059, 75], [34200, 147], [34358, 76], [34500, 148], [34657, 76], [34800, 149], [34956, 77], [35100, 233], [35400, 232], [35700, 232], [36000, 232], [36300, 232], [36600, 231], [36900, 231], [37200, 231], [37500, 230], [37800, 230], [38100, 230], [38400, 230], [38700, 229], [39000, 229], [39300, 229], [39600, 228], [39900, 228], [40200, 228], [40500, 228], [40800, 227], [41100, 227], [41400, 227], [41700, 226], [42000, 226], [42300, 225], [42600, 226], [42900, 225], [43200, 225], [43500, 225], [43800, 56], [43858, 166], [44100, 56], [44158, 166], [44400, 56], [44458, 166], [44700, 55], [44759, 165], [45000, 55], [45300, 54], [45600, 54], [45900, 54], [46200, 53], [46500, 53], [46800, 53], [47100, 52], [47400, 52], [47700, 52], [48000, 51], [48300, 51], [48600, 50], [48900, 50], [49200, 50], [49500, 49], [49800, 49], [50100, 49], [50400, 48], [50700, 48], [51000, 48], [51300, 47], [51600, 47], [51900, 46], [52200, 46], [52500, 46], [52800, 45], [53100, 45], [53400, 45], [53700, 44], [54000, 44], [54300, 43], [54600, 43], [54900, 43], [55200, 42], [55500, 42], [55800, 42], [56100, 41], [56400, 41], [56700, 41], [57000, 40], [57300, 40], [57600, 39], [57900, 39], [58200, 39], [58500, 38], [58800, 38], [59100, 38], [59400, 37], [59700, 37], [60000, 37], [60300, 36], [60600, 36], [60900, 35], [61200, 35], [61500, 35], [61800, 34], [62100, 34], [62400, 34], [62700, 33], [63000, 33], [63300, 33], [63600, 32], [63900, 32], [64200, 31], [64500, 31], [64800, 31], [65100, 30], [65400, 30], [65700, 30], [66000, 29], [66300, 29], [66600, 28], [66900, 28], [67200, 28], [67500, 27], [67800, 27], [68100, 27], [68400, 26], [68700, 26], [69000, 26], [69300, 25], [69600, 25], [69900, 24], [70200, 24], [70500, 24], [70800, 23], [71100, 23], [71400, 23], [71700, 22], [72000, 22], [72300, 22], [72600, 21], [72900, 21], [73200, 20], [73500, 20], [73800, 20], [74100, 19], [74400, 19], [74700, 19], [75000, 18], [75300, 18], [75600, 18], [75900, 17], [76200, 17], [76500, 16], [76800, 16], [77100, 16], [77400, 15], [77700, 17], [78000, 18], [78300, 19], [78600, 19], [78900, 14], [78915, 2], [79200, 13], [79500, 13], [79800, 12]], "point": [126, 134]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [1.132, 1.132, -5.3248, -5.3248, 3.6003664, 3.6003664]], "forceVisible": true, "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [1.132, 1.132, -5.3248, -5.3248, 3.6003664, 3.6003664]], "forceVisible": true, "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.12|+00.84|-01.24"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [141, 67, 163, 101], "mask": [[19941, 23], [20241, 22], [20541, 22], [20842, 21], [21142, 21], [21442, 20], [21743, 19], [22043, 18], [22344, 17], [22644, 16], [22946, 13], [23247, 10], [23548, 8], [23851, 2], [24151, 2], [24451, 2], [24751, 2], [25051, 2], [25351, 2], [25651, 2], [25951, 2], [26251, 2], [26548, 8], [26845, 14], [27143, 18], [27442, 20], [27741, 22], [28041, 22], [28341, 23], [28641, 22], [28941, 22], [29242, 20], [29543, 8], [29554, 7], [29845, 4], [29856, 4], [30147, 1]], "point": [152, 83]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 4, 252, 267], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15322, 229], [15620, 230], [15919, 231], [16219, 231], [16518, 232], [16817, 232], [17116, 233], [17415, 234], [17715, 234], [18014, 235], [18313, 236], [18612, 236], [18911, 237], [19211, 237], [19510, 237], [19809, 238], [20108, 239], [20407, 240], [20707, 239], [21006, 240], [21305, 241], [21604, 241], [21903, 242], [22203, 242], [22502, 243], [22801, 243], [23100, 244], [23400, 244], [23700, 243], [24000, 243], [24300, 243], [24600, 243], [24900, 118], [25023, 119], [25200, 114], [25328, 114], [25500, 112], [25630, 112], [25800, 111], [25931, 110], [26100, 111], [26232, 109], [26400, 111], [26532, 109], [26700, 111], [26833, 108], [27000, 111], [27133, 107], [27300, 111], [27433, 107], [27600, 111], [27733, 107], [27900, 111], [28034, 106], [28200, 111], [28334, 105], [28500, 112], [28633, 106], [28800, 112], [28933, 106], [29100, 112], [29233, 105], [29400, 112], [29532, 19], [29554, 84], [29700, 113], [29831, 18], [29856, 82], [30000, 113], [30130, 18], [30157, 81], [30300, 113], [30429, 18], [30458, 79], [30600, 114], [30728, 18], [30758, 79], [30900, 115], [31027, 19], [31059, 78], [31200, 115], [31326, 19], [31359, 77], [31500, 116], [31624, 21], [31660, 76], [31800, 117], [31922, 23], [31960, 76], [32100, 145], [32260, 76], [32400, 145], [32560, 75], [32700, 145], [32860, 75], [33000, 145], [33160, 75], [33300, 145], [33459, 75], [33600, 146], [33759, 75], [33900, 146], [34059, 75], [34200, 147], [34358, 76], [34500, 148], [34657, 76], [34800, 149], [34956, 77], [35100, 233], [35400, 232], [35700, 232], [36000, 232], [36300, 232], [36600, 231], [36900, 231], [37200, 231], [37500, 230], [37800, 230], [38100, 230], [38400, 230], [38700, 229], [39000, 229], [39300, 229], [39600, 228], [39900, 228], [40200, 228], [40500, 228], [40800, 227], [41100, 227], [41400, 227], [41700, 226], [42000, 226], [42300, 225], [42600, 226], [42900, 225], [43200, 225], [43500, 225], [43800, 56], [43858, 166], [44100, 56], [44158, 166], [44400, 56], [44458, 166], [44700, 55], [44759, 165], [45000, 55], [45300, 54], [45600, 54], [45900, 54], [46200, 53], [46500, 53], [46800, 53], [47100, 52], [47400, 52], [47700, 52], [48000, 51], [48300, 51], [48600, 50], [48900, 50], [49200, 50], [49500, 49], [49800, 49], [50100, 49], [50400, 48], [50700, 48], [51000, 48], [51300, 47], [51600, 47], [51900, 46], [52200, 46], [52500, 46], [52800, 45], [53100, 45], [53400, 45], [53700, 44], [54000, 44], [54300, 43], [54600, 43], [54900, 43], [55200, 42], [55500, 42], [55800, 42], [56100, 41], [56400, 41], [56700, 41], [57000, 40], [57300, 40], [57600, 39], [57900, 39], [58200, 39], [58500, 38], [58800, 38], [59100, 38], [59400, 37], [59700, 37], [60000, 37], [60300, 36], [60600, 36], [60900, 35], [61200, 35], [61500, 35], [61800, 34], [62100, 34], [62400, 34], [62700, 33], [63000, 33], [63300, 33], [63600, 32], [63900, 32], [64200, 31], [64500, 31], [64800, 31], [65100, 30], [65400, 30], [65700, 30], [66000, 29], [66300, 29], [66600, 28], [66900, 28], [67200, 28], [67500, 27], [67800, 27], [68100, 27], [68400, 26], [68700, 26], [69000, 26], [69300, 25], [69600, 25], [69900, 24], [70200, 24], [70500, 24], [70800, 23], [71100, 23], [71400, 23], [71700, 22], [72000, 22], [72300, 22], [72600, 21], [72900, 21], [73200, 20], [73500, 20], [73800, 20], [74100, 19], [74400, 19], [74700, 19], [75000, 18], [75300, 18], [75600, 18], [75900, 17], [76200, 17], [76500, 16], [76800, 16], [77100, 16], [77400, 15], [77700, 17], [78000, 18], [78300, 19], [78600, 19], [78900, 14], [78915, 2], [79200, 13], [79500, 13], [79800, 12]], "point": [126, 134]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.12|+00.84|-01.24", "placeStationary": true, "receptacleObjectId": "Sink|-01.33|+00.92|-01.23|SinkBasin"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [7, 120, 197, 186], "mask": [[35752, 58], [35811, 60], [36046, 64], [36111, 66], [36343, 67], [36411, 68], [36640, 69], [36711, 71], [36938, 71], [37011, 73], [37237, 72], [37311, 74], [37535, 74], [37611, 75], [37834, 75], [37910, 77], [38132, 77], [38210, 79], [38431, 77], [38510, 79], [38730, 78], [38810, 80], [39029, 79], [39110, 80], [39329, 79], [39410, 81], [39628, 80], [39710, 81], [39927, 81], [40010, 81], [40227, 81], [40310, 82], [40526, 81], [40610, 82], [40826, 81], [40910, 82], [41125, 82], [41209, 83], [41425, 82], [41509, 83], [41724, 83], [41809, 83], [42024, 83], [42109, 84], [42323, 83], [42409, 84], [42623, 83], [42709, 84], [42922, 84], [43009, 84], [43222, 84], [43309, 84], [43521, 85], [43609, 84], [43821, 85], [43908, 86], [44120, 86], [44208, 86], [44420, 85], [44508, 86], [44719, 86], [44808, 86], [45018, 86], [45108, 86], [45318, 86], [45408, 11], [45420, 74], [45617, 65], [45683, 21], [45708, 87], [45917, 63], [45984, 20], [46007, 88], [46216, 63], [46286, 18], [46307, 88], [46516, 61], [46587, 17], [46607, 88], [46815, 61], [46889, 14], [46907, 88], [47115, 59], [47190, 13], [47207, 89], [47414, 59], [47491, 12], [47507, 89], [47714, 57], [47793, 10], [47806, 90], [48013, 57], [48094, 9], [48106, 90], [48313, 55], [48394, 9], [48406, 90], [48612, 55], [48694, 9], [48706, 90], [48912, 53], [48995, 7], [49006, 91], [49211, 53], [49295, 7], [49306, 91], [49511, 51], [49594, 8], [49606, 42], [49652, 45], [49810, 51], [49893, 9], [49905, 37], [49958, 39], [50110, 49], [50191, 11], [50205, 31], [50264, 33], [50409, 49], [50490, 12], [50505, 29], [50566, 31], [50709, 47], [50789, 13], [50805, 27], [50868, 30], [51009, 46], [51087, 14], [51105, 24], [51171, 27], [51308, 45], [51386, 15], [51405, 22], [51473, 24], [51608, 44], [51685, 16], [51705, 20], [51775, 22], [51908, 44], [51983, 18], [52005, 18], [52077, 20], [52208, 45], [52282, 19], [52304, 18], [52378, 19], [52507, 46], [52580, 21], [52604, 17], [52679, 18], [52807, 46], [52879, 22], [52904, 16], [52980, 17], [53107, 47], [53178, 23], [53204, 15], [53281, 16], [53407, 48], [53476, 25], [53504, 15], [53581, 15], [53707, 49], [53775, 27], [53803, 15], [53882, 14], [54008, 50], [54074, 43], [54183, 12], [54308, 51], [54372, 44], [54484, 10], [54609, 51], [54671, 44], [54785, 9], [54909, 52], [54969, 45], [55086, 7], [55209, 53], [55268, 45], [55387, 5], [55510, 54], [55566, 47], [55687, 4]], "point": [102, 152]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan22", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -0.25, "y": 0.9009992, "z": -0.5}, "object_poses": [{"objectName": "Pan_304c8d90", "position": {"x": -2.59, "y": 0.9, "z": -1.147}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_304c8d90", "position": {"x": -2.59, "y": 0.9, "z": -1.147}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5ea35e17", "position": {"x": -0.598039, "y": 0.9335, "z": -1.119659}, "rotation": {"x": 0.0, "y": 47.53002, "z": 0.0}}, {"objectName": "Potato_5ea35e17", "position": {"x": -0.598039, "y": 0.9335, "z": -1.119659}, "rotation": {"x": 0.0, "y": 47.53002, "z": 0.0}}, {"objectName": "ButterKnife_dc49bd4c", "position": {"x": 0.12517786, "y": 1.11174214, "z": 1.59498954}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Kettle_1cf3b23c", "position": {"x": -1.98216009, "y": 0.116907895, "z": -1.01926947}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_86f597c8", "position": {"x": -2.6225, "y": 0.9287, "z": -0.3964}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_86f597c8", "position": {"x": -2.80054, "y": 0.379703343, "z": 0.852}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spoon_4b9198b3", "position": {"x": -2.62819934, "y": 0.7477182, "z": 0.226726711}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_b9d165af", "position": {"x": 0.253938317, "y": 1.11224866, "z": 1.76836848}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Plate_ad96aa4f", "position": {"x": -2.848311, "y": 1.79692233, "z": -0.7317904}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_e02c5e35", "position": {"x": -2.7283597, "y": 0.07278025, "z": 1.57196748}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_e02c5e35", "position": {"x": -2.770555, "y": 0.9464633, "z": -0.0629096553}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_1fa5d324", "position": {"x": -2.58522987, "y": 0.7699078, "z": 0.226726711}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_9ffe4e75", "position": {"x": 0.920218468, "y": 0.9607489, "z": -0.9486538}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_b7a8ccec", "position": {"x": 0.842876, "y": 0.9103378, "z": -1.36589742}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a99f93a6", "position": {"x": 1.24075019, "y": 1.16344666, "z": 0.787261367}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Pan_304c8d90", "position": {"x": -2.59, "y": 0.9, "z": -1.147}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a99f93a6", "position": {"x": 0.294268668, "y": 1.05720615, "z": -1.38111067}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_b9d165af", "position": {"x": -1.94883788, "y": 0.747497737, "z": -1.094794}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Kettle_1cf3b23c", "position": {"x": -2.81003428, "y": 1.66581166, "z": 0.03346309}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_35b71b93", "position": {"x": 0.997390866, "y": 0.9, "z": -0.982281566}, "rotation": {"x": 0.0, "y": 334.941345, "z": 0.0}}, {"objectName": "Bread_81a93477", "position": {"x": -0.401600122, "y": 0.9483048, "z": -1.29149938}, "rotation": {"x": -6.95604249e-05, "y": 5.9807604e-05, "z": -6.607245e-05}}, {"objectName": "Plate_ad96aa4f", "position": {"x": -2.08521843, "y": 0.9131638, "z": -1.28244877}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_f346af22", "position": {"x": -0.748, "y": 0.8999999, "z": -1.291}, "rotation": {"x": 0.0, "y": 315.000031, "z": 0.0}}, {"objectName": "Spatula_3b50a8bf", "position": {"x": -2.55216455, "y": 0.922, "z": 0.0104080439}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e2bf19ed", "position": {"x": -2.6608, "y": 0.8744, "z": 0.9831}, "rotation": {"x": 31.0465031, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_4f98d325", "position": {"x": -2.695, "y": 0.9, "z": -0.132}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9ffe4e75", "position": {"x": -2.52118, "y": 0.960749, "z": 0.359614462}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_f59054f9", "position": {"x": -2.830618, "y": 0.9, "z": -0.112962961}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_dc49bd4c", "position": {"x": 0.9906562, "y": 1.11174214, "z": 0.883432269}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Pot_86f597c8", "position": {"x": -2.740321, "y": 1.47510922, "z": 0.7712377}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Potato_5ea35e17", "position": {"x": -0.598039, "y": 0.9335, "z": -1.119659}, "rotation": {"x": 0.0, "y": 47.53002, "z": 0.0}}, {"objectName": "DishSponge_9171dd8d", "position": {"x": -1.221, "y": 0.8416841, "z": -1.1264}, "rotation": {"x": 0.0, "y": 41.1644974, "z": 0.0}}, {"objectName": "Egg_e02c5e35", "position": {"x": 0.241928414, "y": 1.04062283, "z": -1.2486105}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_4b9198b3", "position": {"x": 0.240374386, "y": 1.11282945, "z": 1.17194486}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Knife_1fa5d324", "position": {"x": -1.33236611, "y": 0.8726366, "z": -1.19472015}, "rotation": {"x": 0.0, "y": 270.0, "z": -1.40334183e-14}}, {"objectName": "Cup_46773746", "position": {"x": -1.11968529, "y": 0.8416991, "z": -1.2411406}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f88244ed", "position": {"x": 1.02924943, "y": 1.145, "z": 0.209302634}, "rotation": {"x": 0.0, "y": 228.424088, "z": 0.0}}, {"objectName": "Bowl_b7a8ccec", "position": {"x": -2.80053973, "y": 0.818859637, "z": 0.7712375}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}], "object_toggles": [], "random_seed": 4129240179, "scene_num": 22}, "task_id": "trial_T20190909_053141_550568", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AJQGWGESKQT4Y_392CY0QWG4IPABDR747C2K70B1XI40", "high_descs": ["Turn to the left to face the sink.", "Pick the wine glass up from the sink.", "Go left and turn to the right to face the microwave.", "Put the wine glass in the microwave and shut the door and then open the door and pick up the wine glass.", "Go right and then turn to the left to face the sink.", "Put the wine glass in the sink."], "task_desc": "Put a heated wine glass in the sink.", "votes": [1, 1]}, {"assignment_id": "A20FCMWP43CVIU_3S4AW7T80E9R0CLXS74PU0ZSS5UL4K", "high_descs": ["walk to face sink", "pick up golden goblet from sink", "walk to face microwave", "heat goblet inside microwave, pick up goblet from microwave", "walk to face sink", "place goblet inside sink basin"], "task_desc": "put heated goblet into sink", "votes": [1, 1]}, {"assignment_id": "A1RLO9LNUJIW5S_3WQ3B2KGEB7YV422ECWTIZWK4SAB1G", "high_descs": ["Take a step forward then turn left.", "Pick up the orange wine glass that's in front of you in the sink.", "Turn left and walk to the microwave then turn right to face it when you get there.", "Warm the wine glass in the microwave then take it back out and close the door.", "Turn right and walk back to the sink then turn left to face it.", "Put the glass back in the sink."], "task_desc": "Put a warm wine glass in the sink.", "votes": [1, 1]}]}}