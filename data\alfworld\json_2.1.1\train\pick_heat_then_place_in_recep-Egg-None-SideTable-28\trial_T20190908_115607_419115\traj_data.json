{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000268.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000269.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000270.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000271.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000272.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000273.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000274.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000275.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000276.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000277.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000278.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000279.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000280.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000281.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000282.png", "low_idx": 42}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "SideTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-6|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-0.630764068, -0.630764068, -5.977293, -5.977293, 3.9294672, 3.9294672]], "coordinateReceptacleObjectId": ["CounterTop", [-1.308, -1.308, -5.802, -5.802, 3.9072, 3.9072]], "forceVisible": true, "objectId": "Egg|-00.16|+00.98|-01.49"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-8|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-15|-5|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "sidetable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-0.630764068, -0.630764068, -5.977293, -5.977293, 3.9294672, 3.9294672]], "coordinateReceptacleObjectId": ["SideTable", [-16.284, -16.284, -1.236, -1.236, 2.904, 2.904]], "forceVisible": true, "objectId": "Egg|-00.16|+00.98|-01.49", "receptacleObjectId": "SideTable|-04.07|+00.73|-00.31"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.16|+00.98|-01.49"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [140, 97, 156, 117], "mask": [[28945, 7], [29244, 9], [29543, 11], [29842, 13], [30142, 13], [30441, 15], [30741, 15], [31041, 15], [31340, 17], [31640, 17], [31940, 17], [32240, 17], [32540, 17], [32840, 17], [33141, 15], [33441, 15], [33742, 14], [34042, 13], [34343, 11], [34644, 9], [34946, 5]], "point": [148, 106]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.16|+00.98|-01.49", "placeStationary": true, "receptacleObjectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 11510], [11511, 299], [11811, 288], [12120, 278], [12421, 276], [12722, 274], [13023, 272], [13323, 272], [13624, 271], [13924, 270], [14225, 269], [14525, 268], [14826, 267], [15126, 267], [15426, 267], [15727, 265], [16027, 265], [16327, 265], [16627, 265], [16927, 265], [17227, 265], [17527, 265], [17827, 265], [18127, 266], [18427, 266], [18727, 266], [19027, 267], [19326, 268], [19626, 269], [19926, 269], [20226, 269], [20525, 271], [20825, 271], [21125, 272], [21424, 274], [21724, 274], [22023, 276], [22323, 277], [22622, 278], [22922, 279], [23221, 281], [23520, 283], [23820, 284], [24119, 296], [24417, 8326], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [149, 62]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 11510], [11511, 299], [11811, 288], [12120, 278], [12421, 276], [12722, 274], [13023, 272], [13323, 272], [13624, 271], [13924, 21], [13948, 246], [14225, 18], [14250, 244], [14525, 16], [14552, 241], [14826, 14], [14853, 240], [15126, 13], [15154, 239], [15426, 12], [15455, 238], [15727, 11], [15755, 237], [16027, 10], [16056, 236], [16327, 10], [16356, 236], [16627, 9], [16657, 235], [16927, 9], [16957, 235], [17227, 9], [17258, 234], [17527, 8], [17558, 234], [17827, 8], [17858, 234], [18127, 8], [18158, 235], [18427, 8], [18459, 234], [18727, 7], [18759, 234], [19027, 7], [19059, 235], [19326, 8], [19359, 235], [19626, 8], [19659, 236], [19926, 8], [19959, 236], [20226, 8], [20259, 236], [20525, 9], [20559, 237], [20825, 9], [20859, 237], [21125, 10], [21158, 239], [21424, 11], [21458, 240], [21724, 11], [21758, 240], [22023, 13], [22057, 242], [22323, 13], [22357, 243], [22622, 15], [22656, 244], [22922, 15], [22956, 245], [23221, 17], [23255, 247], [23520, 19], [23554, 249], [23820, 20], [23853, 251], [24119, 22], [24152, 263], [24417, 26], [24450, 8293], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [158, 60]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.16|+00.98|-01.49"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [134, 47, 158, 82], "mask": [[13945, 3], [14243, 7], [14541, 11], [14840, 13], [15139, 15], [15438, 17], [15738, 17], [16037, 19], [16337, 19], [16636, 21], [16936, 21], [17236, 22], [17535, 23], [17835, 23], [18135, 23], [18435, 24], [18734, 25], [19034, 25], [19334, 25], [19634, 25], [19934, 25], [20234, 25], [20534, 25], [20834, 25], [21135, 23], [21435, 23], [21735, 23], [22036, 21], [22336, 21], [22637, 19], [22937, 19], [23238, 17], [23539, 15], [23840, 13], [24141, 11], [24443, 7]], "point": [146, 63]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 11510], [11511, 299], [11811, 288], [12120, 278], [12421, 276], [12722, 274], [13023, 272], [13323, 272], [13624, 271], [13924, 270], [14225, 269], [14525, 268], [14826, 267], [15126, 267], [15426, 267], [15727, 265], [16027, 265], [16327, 265], [16627, 265], [16927, 265], [17227, 265], [17527, 265], [17827, 265], [18127, 266], [18427, 266], [18727, 266], [19027, 267], [19326, 268], [19626, 269], [19926, 269], [20226, 269], [20525, 271], [20825, 271], [21125, 272], [21424, 274], [21724, 274], [22023, 276], [22323, 277], [22622, 278], [22922, 279], [23221, 281], [23520, 283], [23820, 284], [24119, 296], [24417, 8326], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [149, 62]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.16|+00.98|-01.49", "placeStationary": true, "receptacleObjectId": "SideTable|-04.07|+00.73|-00.31"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [21, 115, 140, 251], "mask": [[34251, 87], [34551, 87], [34851, 33], [34888, 50], [35150, 33], [35189, 49], [35450, 33], [35489, 49], [35749, 34], [35789, 49], [36049, 34], [36090, 48], [36349, 34], [36390, 48], [36648, 35], [36691, 47], [36948, 35], [36991, 47], [37247, 36], [37291, 47], [37547, 36], [37592, 46], [37846, 37], [37892, 46], [38146, 37], [38192, 46], [38446, 37], [38493, 45], [38745, 38], [38793, 45], [39045, 37], [39093, 45], [39345, 25], [39394, 44], [39645, 10], [39674, 7], [39694, 44], [39945, 9], [39969, 13], [39994, 44], [40244, 11], [40262, 21], [40294, 5], [40302, 36], [40544, 40], [40594, 4], [40602, 36], [40844, 41], [40893, 5], [40902, 36], [41144, 43], [41190, 8], [41202, 37], [41446, 52], [41502, 37], [41755, 43], [41801, 38], [42055, 42], [42101, 38], [42355, 42], [42401, 38], [42656, 41], [42700, 39], [42956, 41], [43000, 39], [43256, 41], [43299, 40], [43557, 40], [43599, 40], [43857, 40], [43898, 41], [44157, 40], [44198, 41], [44458, 38], [44498, 41], [44758, 14], [44782, 14], [44798, 41], [45058, 12], [45084, 12], [45097, 42], [45359, 10], [45385, 10], [45398, 41], [45659, 9], [45686, 8], [45698, 41], [45960, 7], [45987, 6], [45999, 40], [46260, 7], [46288, 4], [46300, 39], [46560, 6], [46589, 3], [46600, 39], [46833, 1], [46861, 5], [46889, 2], [46900, 39], [47133, 1], [47161, 5], [47190, 1], [47200, 39], [47432, 3], [47461, 5], [47490, 1], [47500, 39], [47732, 3], [47762, 4], [47790, 1], [47799, 40], [48031, 5], [48062, 3], [48090, 2], [48098, 42], [48331, 5], [48362, 3], [48390, 3], [48396, 44], [48631, 6], [48663, 3], [48690, 50], [48930, 7], [48963, 3], [48989, 51], [49230, 8], [49263, 4], [49289, 51], [49529, 9], [49563, 4], [49589, 51], [49829, 9], [49863, 4], [49888, 52], [50128, 11], [50162, 6], [50188, 52], [50428, 11], [50462, 7], [50487, 53], [50728, 12], [50762, 7], [50787, 53], [51027, 13], [51061, 10], [51086, 54], [51327, 14], [51361, 11], [51385, 55], [51626, 15], [51661, 12], [51684, 56], [51926, 16], [51960, 15], [51983, 57], [52225, 17], [52260, 80], [52525, 18], [52560, 80], [52824, 116], [53124, 116], [53424, 116], [53723, 117], [54022, 118], [54322, 118], [54621, 119], [54922, 118], [55223, 118], [55523, 11], [55546, 95], [55824, 11], [55846, 95], [56124, 11], [56145, 96], [56425, 11], [56445, 96], [56725, 11], [56745, 92], [57026, 11], [57044, 93], [57326, 11], [57344, 93], [57627, 11], [57644, 93], [57928, 10], [57943, 94], [58228, 11], [58243, 94], [58529, 10], [58542, 95], [58829, 11], [58842, 95], [59130, 10], [59142, 95], [59430, 107], [59731, 106], [60032, 105], [60332, 105], [60633, 104], [60933, 104], [61234, 103], [61534, 102], [61835, 101], [62136, 9], [62158, 78], [62436, 10], [62457, 79], [62737, 9], [62757, 79], [63037, 10], [63057, 79], [63338, 9], [63356, 80], [63638, 10], [63656, 80], [63939, 10], [63956, 80], [64240, 9], [64256, 80], [64540, 10], [64555, 81], [64841, 9], [64855, 82], [65141, 10], [65155, 82], [65442, 9], [65454, 83], [65742, 10], [65754, 83], [66043, 9], [66054, 83], [66343, 10], [66354, 83], [66644, 93], [66945, 92], [67245, 92], [67546, 91], [67846, 91], [68147, 90], [68447, 90], [68748, 89], [69049, 8], [69131, 7], [69349, 9], [69431, 7], [69650, 8], [69731, 7], [69950, 9], [70032, 6], [70251, 8], [70332, 6], [70551, 9], [70632, 5], [70852, 8], [70932, 4], [71153, 8], [71232, 3], [71453, 8], [71532, 3], [71754, 8], [71832, 2], [72054, 8], [72132, 1], [72355, 8], [72432, 1], [72655, 8], [72956, 8], [73256, 9], [73557, 8], [73858, 8], [74158, 8], [74459, 7], [74759, 7], [75060, 5]], "point": [80, 182]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan28", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -1.25, "y": 0.900998235, "z": -2.25}, "object_poses": [{"objectName": "Potato_5e3edff7", "position": {"x": -3.20301747, "y": 0.79206425, "z": -3.11814}, "rotation": {"x": 0.0, "y": 244.125916, "z": 0.0}}, {"objectName": "DishSponge_7e2c9e01", "position": {"x": -3.409666, "y": 0.7596526, "z": -2.90250444}, "rotation": {"x": 0.0, "y": 199.125946, "z": 0.0}}, {"objectName": "ButterKnife_e676ac70", "position": {"x": -3.56536078, "y": 0.894662857, "z": -0.290749818}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Spoon_7dcfa4cd", "position": {"x": -4.002282, "y": 0.6916294, "z": -0.369833976}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_7dcfa4cd", "position": {"x": -4.122419, "y": 0.6924257, "z": -0.24816598}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_1d69618b", "position": {"x": -1.24855721, "y": 0.9572999, "z": -3.59545565}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_571582a1", "position": {"x": -0.236659765, "y": 0.9823668, "z": -1.31903028}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_571582a1", "position": {"x": -2.45298, "y": 0.140726745, "z": -3.57128429}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_571582a1", "position": {"x": -0.157691017, "y": 0.9823668, "z": -1.49432325}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_41873c33", "position": {"x": -3.163546, "y": 0.895324, "z": -0.290752053}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Plate_ba5a8c1e", "position": {"x": -0.175364524, "y": 1.95197284, "z": -1.00262713}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_0407be4c", "position": {"x": -0.473566, "y": 0.9394891, "z": -1.58196974}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_0407be4c", "position": {"x": -0.572748244, "y": 1.50193381, "z": -3.69230819}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Tomato_b1760e7a", "position": {"x": -0.321307868, "y": 1.53947651, "z": -0.2970663}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Bread_4d72833d", "position": {"x": -3.78087044, "y": 0.84152, "z": -3.59140062}, "rotation": {"x": 0.0, "y": 199.125946, "z": 0.0}}, {"objectName": "Bread_4d72833d", "position": {"x": -2.56082439, "y": 0.9760524, "z": -0.37652427}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Lettuce_ec37c659", "position": {"x": -3.82274556, "y": 0.851072848, "z": -2.90460825}, "rotation": {"x": 0.0, "y": 334.125916, "z": 0.0}}, {"objectName": "Lettuce_ec37c659", "position": {"x": -3.5653615, "y": 0.98560524, "z": -0.3765187}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "SaltShaker_6f13b4f9", "position": {"x": -0.136440963, "y": 1.93873167, "z": -0.766370654}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_8ae9f292", "position": {"x": -0.509394348, "y": 0.747274, "z": -2.632294}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_8ae9f292", "position": {"x": -4.06235027, "y": 0.6868062, "z": -0.248166}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f3c7368a", "position": {"x": -3.36445236, "y": 0.8896208, "z": -0.119213134}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Mug_f3c7368a", "position": {"x": -0.245574251, "y": 1.48371041, "z": -0.9949341}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Apple_4eadea0d", "position": {"x": -0.293651551, "y": 1.34801114, "z": -0.878622055}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Apple_4eadea0d", "position": {"x": -0.2012378, "y": 1.63686419, "z": -1.88466358}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SoapBottle_0407be4c", "position": {"x": -4.182487, "y": 0.689485431, "z": -0.491501868}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_c7418f98", "position": {"x": -4.080143, "y": 0.761066, "z": -3.2276957}, "rotation": {"x": 0.0, "y": 109.125885, "z": 0.0}}, {"objectName": "Fork_938fe393", "position": {"x": -0.473584652, "y": 0.9428485, "z": -2.61117625}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_41873c33", "position": {"x": -0.5525347, "y": 0.9420032, "z": -1.40667677}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_b1760e7a", "position": {"x": -0.252379239, "y": 1.33736539, "z": -0.646000266}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Pan_d1250561", "position": {"x": -0.2221, "y": 0.961999953, "z": -1.8457}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Plate_ba5a8c1e", "position": {"x": -0.212670773, "y": 1.51676917, "z": -3.05265713}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_4d72833d", "position": {"x": -2.56082582, "y": 0.9760524, "z": -0.6338309}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Spatula_1d69618b", "position": {"x": -3.455305, "y": 0.775088251, "z": -3.31207824}, "rotation": {"x": 0.0, "y": 199.12587, "z": 0.0}}, {"objectName": "Knife_1b546504", "position": {"x": -0.236659765, "y": 0.966918349, "z": -1.4505}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_6f13b4f9", "position": {"x": -2.76173043, "y": 0.890926957, "z": -0.1192165}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "PepperShaker_8ae9f292", "position": {"x": -0.440981239, "y": 0.7482216, "z": -2.84604383}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_e676ac70", "position": {"x": -3.36445332, "y": 0.894662857, "z": -0.290750921}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Apple_4eadea0d", "position": {"x": -4.06235027, "y": 0.7519119, "z": -0.491501927}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_7e2c9e01", "position": {"x": -0.5416963, "y": 0.08172554, "z": -1.48973584}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5e3edff7", "position": {"x": -3.163547, "y": 0.926596642, "z": -0.46228978}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Egg_571582a1", "position": {"x": -0.185307026, "y": 0.9833667, "z": -3.012313}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Spoon_7dcfa4cd", "position": {"x": -0.5778075, "y": 0.7520972, "z": -2.632294}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Lettuce_ec37c659", "position": {"x": -1.47139764, "y": 1.03328443, "z": -3.67361975}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f3c7368a", "position": {"x": -3.77244115, "y": 0.7550884, "z": -3.75784922}, "rotation": {"x": 0.0, "y": 199.125946, "z": 0.0}}, {"objectName": "Bowl_1f51c27f", "position": {"x": -0.490136683, "y": 0.8117749, "z": -3.26063681}, "rotation": {"x": 9.923132e-15, "y": 315.000031, "z": -9.92312e-15}}], "object_toggles": [], "random_seed": 1376299023, "scene_num": 28}, "task_id": "trial_T20190908_115607_419115", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1ELPYAFO7MANS_30OG32W0SX2ZGELBTQWHGEF8AK6NE2", "high_descs": ["Turn left and walk to the left side of the sink.", "Pick up the egg on the counter, the one to the right of the other egg.", "Take a step right and look up at the microwave.", "Heat the egg in the microwave.", "Turn around and go straight, veering right to the square black table in the corner of the room.", "Place the egg on the table in the front right corner."], "task_desc": "Place a heated egg on a table.", "votes": [1, 1]}, {"assignment_id": "AJQGWGESKQT4Y_3BWI6RSP7J0ADTTODJFSKHTIA5CE7T", "high_descs": ["Turn around and go to the right to stand in front of the counter to the left of the stove.", "Pick the egg on the right side up from the counter.", "Move to the right and face the microwave.", "Put the egg in the microwave and shut the door and then open the door and take the egg out and shut the door again.", "Turn around and cross the room and go to the right to stand in front of the small black table next to the white table.", "Put the egg on the table."], "task_desc": "Put a heated egg on a table.", "votes": [1, 1]}, {"assignment_id": "A3SFPSMFRSRTB3_3HYA4D452UAVX6WE2II368XABQAF21", "high_descs": ["Turn around, go straight until counter between stove and refrigerator is to the right.  Turn right to face counter.", "Remove right egg from counter, behind pan.", "Turn right, go to stove, turn left to face stove.", "Open microwave, put egg in microwave to right of apple, close microwave.  Wait four seconds, open microwave, remove egg, close microwave.", "Turn around, proceed to wall.  Turn right, proceed to table.", "Place egg on lower right corner of table, to the right of the apple."], "task_desc": "Heat microwave, move to counter.", "votes": [1, 1]}]}}